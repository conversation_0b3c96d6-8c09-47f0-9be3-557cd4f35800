import React,{Component} from 'react'
import {findDOMNode} from 'react-dom'
import PropTypes from 'prop-types'
import addEventListener from 'rc-util/lib/Dom/addEventListener'
import {canvasStore} from '../../../redux/CanvasStore';
import {paintOnCanvas,tgsActionCreator} from '../CanvasRedux';
import {assetManager} from '../../AssetManager';
import Slider from '@src/userComponentV6.0/Slider/Slider';
import {getFontNameList,imgHost} from '../../IPSConfig'

import {emitter} from '../../Emitter';
import WebFont from 'webfontloader';
import MoreColorsPanelPro from '../../MoreColorsPanel/MoreColorsPanelPro';
import {PanelDoBlock} from '../PanelDoBlock';
import {matchesSelector,convertLanguage} from '../../Function';
import MoreColorsPreinstall from '../../MoreColorsPanel/MoreColorsPreinstall'
import {MoreColorsPanel,DarkMoreColorsPanel} from '../../MoreColorsPanel/MoreColorsPanel';
import TableEditDataModal from "../assetControl/ToolTableComs/TableEditDataModal";
import {attributes} from '../data';
import classNames from 'classnames';
import {AssetLogic} from "@v7_logic/AssetLogic";
import {klona} from 'klona';
import {storeAdapter} from '@v7_logic_core/StoreAdapter';

import {TgsFontSelector,TgsRequestManager} from "../../../../../ips_component_publish/tgs-component/src/index";
import {connect} from 'react-redux';
import {getProps} from '../../IPSConfig';
import {TGS_SET_FONT_LIST,TGS_SET_BRAND_USERTEAMINFO,TGS_SET_BRAND_TEAMINFO} from '../../../action/actionType';
import {DownloadPopup} from "../../InfoBar";
import SliderItem from "../../Slider/SliderItem";
import {klona as cloneDeep} from "klona";
import equal from 'fast-deep-equal';
import {AssetHelper} from '@v7_logic/AssetHelper';
import {TgsManageHelper} from '@v7_logic/StoreLogic';
import {CanvasPaintedLogic} from '@v7_logic/CanvasPaintedLogic'
import {AssetUpdateEffect} from '@v7_render/AssetUpdateEffect'
import {SetAssetAnimation} from '@v7_render/SetAssetAnimation'
import {RightPanel} from '@v7_logic/RightPanel';
import {GroupAssetEditBox} from '@v7_render/AssetToolPanel/Group';
import {NewTypesettingBlock} from './TableTextBlock'
import { TransformSettingBlock } from '@v7_render/AssetToolPanel/components/TransformSettingBlock';
const preventDefault = (e) => {
    e.preventDefault();
    e.nativeEvent?.preventDefault();
}

/**
 * 编辑文本
 */
class NewTextEditBlock extends Component {

    constructor(props) {
        super(props);

        let newFontList = {
            font247: 1,
            font258: 1,
            font259: 1,
            font260: 1,
            font261: 1,
            font262: 1,
            font263: 1,
            font264: 1,
            font266: 1,
            font267: 1,
            font268: 1,
            // font269: 1,
            font270: 1,
            font271: 1,
            font272: 1,
            // font273: 1,
            font274: 1,
            // font275: 1,
            font276: 1,
            // font277: 1,
            // font280: 1,
        }
        const useList = localStorage.getItem('USELIST') || ''
        useList.split(',').forEach(key => newFontList[key] && (newFontList[key] = 0))
        console.log("表单工具栏组件props==========》")
        console.log(props)
        this.state = {
            yuyanType: 'zn',
            brandFontList: [],
        };
        this.commonDichroicList = [
            [
                {r: 0,g: 0,b: 0,a: 1},
                {r: 68,g: 68,b: 68,a: 1},
            ],
            [
                {r: 0,g: 0,b: 0,a: 1},
                {r: 255,g: 255,b: 255,a: 1},
            ],
            [
                {r: 191,g: 191,b: 191,a: 1},
                {r: 255,g: 255,b: 255,a: 1},
            ],
            [
                {r: 255,g: 0,b: 60,a: 1},
                {r: 255,g: 92,b: 151,a: 1},
            ],
            [
                {r: 255,g: 0,b: 60,a: 1},
                {r: 255,g: 204,b: 198,a: 1},
            ],
            [
                {r: 255,g: 0,b: 60,a: 1},
                {r: 20,g: 40,b: 196,a: 1},
            ],
            [
                {r: 251,g: 152,b: 23,a: 1},
                {r: 255,g: 204,b: 128,a: 1},
            ],
            [
                {r: 251,g: 152,b: 23,a: 1},
                {r: 255,g: 248,b: 225,a: 1},
            ],
            [
                {r: 251,g: 152,b: 23,a: 1},
                {r: 70,g: 34,b: 221,a: 1},
            ],
            [
                {r: 255,g: 224,b: 0,a: 1},
                {r: 251,g: 191,b: 46,a: 1},
            ],
            [
                {r: 255,g: 224,b: 0,a: 1},
                {r: 255,g: 253,b: 231,a: 1},
            ],
            [
                {r: 255,g: 224,b: 0,a: 1},
                {r: 0,g: 0,b: 0,a: 1},
            ],
            [
                {r: 109,g: 196,b: 20,a: 1},
                {r: 242,g: 245,b: 185,a: 1},
            ],
            [
                {r: 109,g: 196,b: 20,a: 1},
                {r: 219,g: 255,b: 189,a: 1},
            ],
            [
                {r: 109,g: 196,b: 20,a: 1},
                {r: 73,g: 34,b: 196,a: 1},
            ],
            [
                {r: 0,g: 121,b: 107,a: 1},
                {r: 165,g: 214,b: 167,a: 1},
            ],
            [
                {r: 0,g: 121,b: 107,a: 1},
                {r: 225,g: 242,b: 241,a: 1},
            ],
            [
                {r: 0,g: 121,b: 107,a: 1},
                {r: 3,g: 36,b: 38,a: 1},
            ],
            [
                {r: 54,g: 200,b: 248,a: 1},
                {r: 25,g: 118,b: 210,a: 1},
            ],
            [
                {r: 54,g: 200,b: 248,a: 1},
                {r: 227,g: 242,b: 253,a: 1},
            ],
            [
                {r: 54,g: 200,b: 248,a: 1},
                {r: 255,g: 182,b: 206,a: 1},
            ],
            [
                {r: 60,g: 94,b: 243,a: 1},
                {r: 27,g: 2,b: 104,a: 1},
            ],
            [
                {r: 60,g: 94,b: 243,a: 1},
                {r: 255,g: 255,b: 255,a: 1},
            ],
            [
                {r: 60,g: 94,b: 243,a: 1},
                {r: 31,g: 19,b: 71,a: 1},
            ],
            [
                {r: 140,g: 32,b: 186,a: 1},
                {r: 255,g: 149,b: 202,a: 1},
            ],
            [
                {r: 140,g: 32,b: 186,a: 1},
                {r: 214,g: 189,b: 255,a: 1},
            ],
            [
                {r: 140,g: 32,b: 186,a: 1},
                {r: 255,g: 224,b: 0,a: 1},
            ],
            [
                {r: 21,g: 46,b: 163,a: 1},
                {r: 54,g: 200,b: 248,a: 1},
            ],
            [
                {r: 21,g: 46,b: 163,a: 1},
                {r: 227,g: 242,b: 253,a: 1},
            ],
            [
                {r: 21,g: 46,b: 163,a: 1},
                {r: 255,g: 221,b: 125,a: 1},
            ],
            [
                {r: 247,g: 225,b: 186,a: 1},
                {r: 199,g: 50,b: 41,a: 1},
            ],
            [
                {r: 247,g: 225,b: 186,a: 1},
                {r: 255,g: 211,b: 159,a: 1},
            ],
            [
                {r: 247,g: 225,b: 186,a: 1},
                {r: 0,g: 0,b: 0,a: 1},
            ],
            [
                {r: 62,g: 39,b: 35,a: 1},
                {r: 121,g: 85,b: 72,a: 1},
            ],
            [
                {r: 62,g: 39,b: 35,a: 1},
                {r: 239,g: 235,b: 233,a: 1},
            ],
            [
                {r: 62,g: 39,b: 35,a: 1},
                {r: 255,g: 197,b: 93,a: 1},
            ],
            [
                {r: 79,g: 84,b: 65,a: 1},
                {r: 226,g: 232,b: 221,a: 1},
            ],
            [
                {r: 129,g: 60,b: 243,a: 1},
                {r: 255,g: 92,b: 151,a: 1},
            ],
            [
                {r: 255,g: 0,b: 60,a: 1},
                {r: 0,g: 0,b: 0,a: 1},
            ],
            [
                {r: 255,g: 179,b: 197,a: 1},
                {r: 255,g: 126,b: 169,a: 1},
            ],
            [
                {r: 255,g: 179,b: 197,a: 1},
                {r: 255,g: 231,b: 192,a: 1},
            ],
            [
                {r: 255,g: 179,b: 197,a: 1},
                {r: 205,g: 170,b: 242,a: 1},
            ]
        ];
        this.fontNameList = getFontNameList();
        this.delFontNameList = [
            'font0',
            'font1',
            'font2',
            'font3',
        ];
        this.commonColorList = [
            {
                r: 0,g: 0,b: 0,a: 1,
            },{
                r: 50,g: 50,b: 50,a: 1,
            },{
                r: 98,g: 98,b: 98,a: 1,
            },{
                r: 191,g: 191,b: 191,a: 1,
            },{
                r: 255,g: 255,b: 255,a: 1,
            },{
                r: 255,g: 54,b: 58,a: 1,
            },{
                r: 255,g: 240,b: 53,a: 1,
            },{
                r: 45,g: 191,b: 255,a: 1,
            },{
                r: 103,g: 234,b: 84,a: 1,
            },{
                r: 250,g: 142,b: 192,a: 1,
            },{
                r: 30,g: 35,b: 130,a: 1,
            },{
                r: 88,g: 33,b: 128,a: 1,
            },{
                r: 202,g: 0,b: 0,a: 1,
            },{
                r: 155,g: 72,b: 22,a: 1,
            },
        ];

        this.commercialFontNameList = [
            {
                cateName: '黑体',
                fontList: [
                    'font4',
                    'font130',
                    'font169',
                    'font129',
                    'font11',
                    'font254',
                    'font216',
                    'font258',
                    'font243',
                    'font237',
                    'font232',
                    'font271',
                    'font236',
                    'font235',
                    'font222',
                    'font225',
                    'font282',
                    'font283',
                    'font285',
                    'font288',
                    'font297',
                    'font298',
                    'font299',


                ]
            },
            {
                cateName: '宋体',
                fontList: [
                    'font165',
                    'font167',
                    'font166',
                    'font164',
                    'font252',
                    'font217',
                    'font295',
                    'font301',
                    'font302',
                    'font303',
                    'font304',
                    'font305',
                ]
            },
            {
                cateName: '圆体',
                fontList: [
                    'font296',
                    'font300',

                ]
            },
            {
                cateName: '手写体',
                fontList: [
                    'font58',
                    'font153',
                    'font158',
                    'font155',
                    'font156',
                    'font157',
                    'font154',
                    'font218',
                ]
            },
            {
                cateName: '卡通可爱字体',
                fontList: [
                    'font276',
                    'font272',
                    'font213',
                    'font249',
                    'font206',
                    'font255',
                    'font268',
                    'font248',
                    'font251',
                    'font220',
                    'font211',
                    'font226',
                    'font281',
                    'font286',
                    'font289',
                    'font291',
                    'font292',
                    'font293',
                ]
            },
            {
                cateName: '毛笔书法',
                fontList: [
                    'font256',
                    'font212',
                    'font261',
                    'font245',
                    // 'font228',
                    'font207',
                    'font224',
                    // 'font253',
                    'font233',
                    'font208',
                    'font270',
                    'font234',
                    'font246',
                    'font274',
                    // 'font280',
                    'font210',
                    'font209',
                    'font215',
                    // 'font273',
                    'font231',
                    'font307'
                ]
            },
            {
                cateName: '楷体',
                fontList: [
                    'font260',
                    'font202',
                    // 'font227',
                    'font219',
                    // 'font223',
                ]
            },
            {
                cateName: '硬笔书法',
                fontList: [
                    // 'font277',
                    'font259',
                    'font263',
                    'font205',
                    'font266',
                    'font257',
                    'font240',
                    'font250',
                    'font267',
                    'font264',
                    // 'font221',
                    'font230',
                    'font204',
                    'font229',
                    'font239',
                    'font242',
                    'font238',
                    'font214',
                    'font247',
                    'font306'
                ]
            },
            {
                cateName: '英文字体',
                fontList: [
                    'font101',
                    'font102',
                    'font103',
                    'font104',
                    'font105',
                    'font106',
                    // 'font107',
                    'font108',
                    'font109',
                    // 'font110',
                    'font111',
                    // 'font112',
                    'font113',
                    'font114',
                    // 'font115',
                    'font116',
                    'font117',
                    // 'font118',
                    'font119',
                    'font120',
                    // 'font121',
                    'font122',
                    // 'font161',
                    // 'font162',
                ]
            },
            {
                cateName: 'vip',
                fontList: [
                    'font308',
                    'font309',
                    'font310',
                    'font312',
                    'font313',
                    'font314',
                    // 'font107',
                    'font315',
                    'font316',
                    // 'font110',
                    'font317',
                    // 'font112',
                    'font318',
                    'font319',
                    // 'font115',
                    'font320',
                    'font321',
                    // 'font118',
                    'font322',
                    'font323',
                    // 'font121',
                    'font324',
                    // 'font161',
                    // 'font162',
                ]
            },
            {
                cateName: '创意字体',
                fontList: [
                    'font284',
                    'font287',
                    'font290',
                    'font294'
                ]
            },
            {
                cateName: "字体VVIP专属",
                fontList: [
                    "font333",
                    "font334",
                    "font335",
                    "font336",
                    "font337",
                    "font338",
                    "font339",
                    "font340",
                    "font341",
                    "font342",
                    "font343",
                    "font344",
                    "font345",
                    "font346",
                    "font347",
                    "font348",
                    "font349",
                    "font350",
                    "font351",
                    "font352",
                    "font353",
                    "font354",
                    "font355",
                    "font356",
                    "font357",
                    "font358",
                    "font359",
                    "font360",
                    "font361",
                    "font362",
                    "font363",
                    "font364",
                    "font365",
                    "font366",
                    "font367",
                    "font368",
                    "font369",
                    "font370",
                    "font371",
                    "font372",
                    "font373",
                    "font374",
                    "font375",
                    "font376",
                    "font377",
                    "font378",
                    "font379",
                    "font380",
                    "font381",
                    "font382",
                    "font383",
                    "font384",
                    "font385",
                    "font386",
                    "font387",
                    "font388",
                    "font389",
                    "font390",
                    "font391",
                    "font392",
                    "font393",
                    "font394",
                    "font395",
                    "font396",
                    "font397",
                    "font398",
                    "font399",
                    "font400",
                    "font401",
                    "font402",
                    "font403",
                    "font404",
                    "font405",
                    "font406",
                    "font407",
                    "font408",
                    "font409",
                    "font410",
                    "font411",
                    "font412",
                    "font413",
                    "font414",
                    "font415",
                    "font416",
                    "font417",
                    "font418",
                    "font419",
                    "font420",
                    "font421",
                    "font422",
                    "font423",
                    "font424",
                    "font425",
                    "font426",
                    "font427",
                    "font428",
                    "font429",
                    "font430",
                    "font431",
                    "font432",
                ]
            }
        ];
        this.brandExclusive = [
            "font319",
            "font320",
            "font321",
            "font322",
            "font323",
            "font324",
            "font325",
            "font326",
            "font327",
            "font328",
            "font329",
            "font330",
            "font331",
            "font332",
        ];
        this.commercialFontNameListSingle = [];
        this.commercialFontNameList.map((item) => {
            this.commercialFontNameListSingle = this.commercialFontNameListSingle.concat(item['fontList']);
        })

        this.nonCommercialFontNameList = [
            // 'font0',
            // 'font1',
            // 'font2',
            // 'font3',
            // 'font5',
            // 'font6',
            // 'font7',
            // 'font8',
            // 'font15',
            // 'font16',
            // 'font18',
            // 'font19',
            'font20',
            'font21',
            'font22',
            'font23',
        ];

        this.newNoCopyrightList = [
            'font135',
            'font136',
            'font144',
        ];

        this.fontNameValueList = {};

        let th = this;

        this.state = {
            fontFamilyShow: false,
            colorShow: false,
            fontSizeShow: false,
            show_copyright_tip: false,
            isColorMorePanel: false,
            isBorderColorMore: false,
            isBgColorMore: false,
            isTypeBtnMoreList: false,
            favTextList: [],
            textList: [],
            isQuickEditingText: false,
            cateTab: 'commercial',
            scrollbarHeight: 500,
            specificWordSelectListShow: false,
            showVariableColorParaIndex: -1,
            newFontList,
            newTgsFontList: [], //组件方式字体列表
            // recently_font: [], //最近使用字体

        };

        const {tgsManageFontSelectorStore = {}} = this.props;
        let {font_list = [],brandInfo = {}} = tgsManageFontSelectorStore
        TgsRequestManager.getMyTeamBrand().then(data => {
            data.json().then(res => {
                if(res.stat == 1 && res.msg.length > 0) {
                    this.setState({
                        teamInfo: res.msg,
                        isTeam: true,
                        userTeamInfo: res.msg[0].list[0],
                    },() => {
                        // this.getTeamBrandFont()
                    });
                    TgsManageHelper.tgsSetBrandTeamInfo({
                        brandDatas: res.msg,
                    });
                    // canvasStore.dispatch(tgsActionCreator(TGS_SET_BRAND_TEAMINFO, { teamInfo: res.msg }));
                    // canvasStore.dispatch(tgsActionCreator(TGS_SET_BRAND_USERTEAMINFO, { userTeamInfo: res.msg[0].list[0] }));
                    TgsManageHelper.tgsSetBrandUserTeamInfo({
                        userTeamInfo: res.msg[0].list[0],
                    });
                    this.getTeamBrandFont(res.msg[0].list[0])
                }
            })
        })

        if(!font_list || font_list.length == 0) {
            TgsRequestManager.getFontsShowInfomation().then(data => {
                data.json().then(res => {
                    if(res.stat == 1) {
                        const {user} = canvasStore.getState().onCanvasPainted
                        // user.vip_font
                        // 0：只能使用免费字体
                        // 1：免费字体 + 10款vip字体 '字体VIP专属'
                        // 2：免费字体 + 100款vip字体 '字体VVIP专属'
                        // 12：免费字体 + 10款vip字体 + 100款vip字体
                        // '你已解锁的字体'
                        // '升级字体VIP解锁以下全部字体'

                        const list = res.msg
                        // if(user.vipFont !== 0 && typeof(user.vipFont) != "undefined") {
                        list.forEach((item,index) => {
                            item.index = index
                            // if(user.vipFont === 1) {
                            //     if(item.type === '字体VIP专属'){
                            //         item.index = 91
                            //         item.type = '你已解锁的字体'
                            //     }
                            //     if(item.type === '字体VVIP专属'){
                            //         item.index = 92
                            //         item.type = '升级字体VIP解锁以下全部字体'
                            //         item.needVip = true
                            //     }
                            // }

                            // if(user.vipFont === 2) {
                            //     if(item.type === '字体VIP专属'){
                            //         item.index = 91
                            //         item.type = '你已解锁的字体'
                            //     }

                            //     if(item.type === '字体VVIP专属'){
                            //         item.index = 92
                            //         item.type = '升级字体VIP解锁以下全部字体'
                            //         item.needVip = true
                            //     }
                            // }

                            // if(user.vipFont ===  3) {
                            //     if(item.type === '字体VIP专属'){
                            //         item.index = 91
                            //         item.type = '你已解锁的字体'
                            //     }
                            //     if(item.type === '字体VVIP专属'){
                            //         item.index = 92
                            //         item.type = ''
                            //     }
                            // }

                            // if(user.vipFont ===  4) {
                            //     if(item.type === '字体SVIP专属'){
                            //         item.index = 91
                            //         item.type = '你已解锁的字体'
                            //     }
                            //     if(item.type === '字体非SVIP专属'){
                            //         item.index = 92
                            //         item.type = '升级字体VIP解锁以下全部字体'
                            //         item.needVip = true
                            //     }
                            // }

                            if(item.type === "未解锁VIP字体") {
                                item.index = 92
                                item.type = user.vipFont === 0 ? '升级字体VIP解锁以下全部字体' : '未解锁VIP字体';
                            }
                            if(item.type === "已解锁VIP字体") {
                                item.index = 91
                                item.type = user.vipFont === 3 ? '你已解锁的字体' : `你已解锁以下${item.list.length}款字体VIP`;
                            }


                        })
                        list.push({type: "剩余解锁次数",have_count: res.have_count})
                        list.sort((a,b) => a.index - b.index)
                        // } else {
                        //     list.forEach((item,index) => {
                        //         if( item.type === '字体VIP专属'){
                        //             item.index = 91
                        //             item.type = '升级字体VIP解锁以下全部字体';
                        //             item.needVip = true
                        //         }

                        //         if( item.type === '字体VVIP专属'){
                        //             item.index = 92
                        //             item.type = '';
                        //             item.needVip = true
                        //         }
                        //     })

                        // }

                        this.setState({newTgsFontList: list})
                        TgsManageHelper.tgsSetFontList({
                            font_list: res.msg,
                        });
                        // canvasStore.dispatch(tgsActionCreator(TGS_SET_FONT_LIST, { fontList: list }));
                    }
                })
            })
        }

        Object.keys(this.fontNameList).map(function(key) {
            Object.assign(th.fontNameValueList,{[th.fontNameList[key]]: key})
        });

        // this.sliderDownFontSizeEvent = this.sliderDownEvent.bind(this, 'fontSize');
        this.fontSizeInputChangeEvent = this.fontSizeInputChangeEvent.bind(this);
        this.fontFamilyShowEvent = this.fontFamilyShowEvent.bind(this)
        this.colorShowEvent = this.colorShowEvent.bind(this)

        // this.fontSizeMin = 12;
        // this.fontSizeMax = 500;
        // this.fontSizeWidth = 111;
        // this.fontOpacityMin = 0;
        // this.fontOpacityMax = 100;
        // this.fontOpacityWidth = 111;

        // this.componentDidMount();

        // this.updateTextList();
        // this.updateFavText();

        // this.updateTextListEmitter();
        // this.updateFavTextEmitter();
        this.getSpecificWordList();

        this.specificSizeBar = 130;
        this.fontOpacityMin = 0;
        this.fontOpacityMax = 100;
        this.fontOpacityWidth = 130;

        this.sliderDownFontOpacityEvent = this.sliderDownEvent.bind(this,'opacity');
        // this.getRecentlyUsedFont();
    }

    // /**
    //  * 获取最近使用列表
    //  */
    // getRecentlyUsedFont = () => {
    //     const {user} = canvasStore.getState().onCanvasPainted                  
    //     TgsRequestManager.getRecentlyUsedFont(user.userId).then(data => {
    //         data.json().then(res => {
    //             if(res.stat == 1){
    //                 this.setState({
    //                     recently_font: res.data
    //                 })
    //                 let zn,en,vip;
    //                 res.data.forEach(v => {
    //                     if(v.type == '黑体'){
    //                        zn = v.list.length
    //                     }else if(v.type == '英文字体'){
    //                         en = v.list.length
    //                     }else if(v.type == '字体VIP专属'){
    //                         vip=v.list.length
    //                     }
    //                 })
    //                 assetManager.setPv_new(4542, {additional: {s0: "ue",zn: zn,en: en,vip: vip} });
    //             }
    //         })
    //     })
    // }

    /**
     * 获取特效字列表
     */
    getSpecificWordList() {
        let {specificWordList} = canvasStore.getState().onCanvasPainted;

        if(specificWordList.length > 0) {

        } else {
            assetManager.getSpecificWordList().then(data => {
                data.json().then(resultData => {
                    if(resultData.stat == 1) {
                        CanvasPaintedLogic.updateSpecificWordList(
                            {
                                specificWordList: resultData.msg
                            }
                        )
                        // canvasStore.dispatch(paintOnCanvas('UPDATE_SPECIFICWORDLIST', {specificWordList: resultData.msg}));
                    }
                });
            });
        }
    }

    updateTextListEmitter() {
        let th = this;

        this.quickEditingUpdateTextListEmitter = emitter.addListener("quickEditingUpdateTextList",() => {
            th.updateTextList();
        });
    }

    updateTextList() {
        let th = this;

        assetManager.getHistoryRecordText().then((data) => {
            data.json().then((resultData) => {
                if(resultData.stat == 1) {
                    th.setState({
                        textList: resultData.msg
                    });
                }
            });
        });
    }

    updateFavTextEmitter() {
        let th = this;

        this.quickEditingUpdateFavTextEmitter = emitter.addListener("quickEditingUpdateFavText",() => {
            th.updateFavText();
        });
    }

    updateFavText() {
        let th = this;

        assetManager.getTextFav(1).then((data) => {
            data.json().then((resultData) => {
                if(resultData.stat == 1) {
                    th.setState({
                        favTextList: resultData.msg
                    });
                    // let tempObject = {};
                    // resultData.msg.map((item) => {
                    //     Object.assign(tempObject, {
                    //         [item.name]: item
                    //     });
                    // });
                    // th.setState({
                    //     favTextList: tempObject
                    // });
                }
            });
        });
    }

    /**
     * 滑动条点击事件
     */
    /*    sliderDownEvent(sliderType, e) {
            var toolPanel = canvasStore.getState().onCanvasPainted.toolPanel

            this.fontSize = toolPanel.asset.attribute.fontSize > this.fontSizeMax ? this.fontSizeMax : toolPanel.asset.attribute.fontSize;
            this.opacity = toolPanel.asset.attribute.opacity;

            this.clientX = e.clientX;
            this.clientY = e.clientY;
            this.sliderType = sliderType;

            if (this.sliderMoveListtener) {
                this.sliderMoveListtener.remove();
            }

            if (this.sliderUpListtener) {
                this.sliderUpListtener.remove();
            }

            var th = this;
            this.sliderMoveListtener = addEventListener(window, 'mousemove', function (e) {
                th.sliderMoveEvent(e);
            });
            this.sliderUpListtener = addEventListener(window, 'mouseup', function (e) {
                th.sliderUpEvent(e);
            });
        }*/

    /**
     * 滑动条释放事件
     */
    /*    sliderUpEvent(e) {
            this.sliderMoveListtener.remove();
            this.sliderUpListtener.remove();
        }*/

    /**
     * 滑动条移动事件
     */
    /*    sliderMoveEvent(e) {
            var props = {};
            switch (this.sliderType) {
                case 'fontSize':
                    var fontSize = this.fontSizeFormat(this.fontSize + (e.clientX - this.clientX) * this.fontSizeMax / this.fontOpacityWidth)
                    Object.assign(props, {fontSize: fontSize});
                    canvasStore.dispatch(paintOnCanvas('UPDATE_FONTSIZE', {fontSize: this.fontSizeMin + (this.fontSizeMax - this.fontSizeMin) * fontSize / this.fontSizeMax}));
                    break;
                case 'opacity':
                    var opacity = this.opacityFormat(this.opacity + (e.clientX - this.clientX) * this.fontOpacityMax / this.fontOpacityWidth)

                    Object.assign(props, {opacity: opacity});
                    canvasStore.dispatch(paintOnCanvas('UPDATE_OPACITY', {opacity: this.fontOpacityMin + (this.fontOpacityMax - this.fontOpacityMin) * opacity / this.fontOpacityMax}));
                    break;
            }

            this.setState(Object.assign({}, props));
        }*/

    /** ---------------------------------------font-size edit-------------------------------------------- */

    /**文字大小（输入框）*/
    fontSizeInputChangeEvent(e) {
        const {choosedTdKeys = []} = this.props;
        var fontSize;
        fontSize = this.fontSizeFormat(parseInt(e.target.value));
        if(choosedTdKeys.length > 0) {

            AssetLogic.updateAssetTableText(
                {
                    updateType: "fontSize",
                    value: fontSize,
                    tbKeys: choosedTdKeys,
                    fun_name: "UPDATE_TABLETEXT_FONTSIZE",
                }
            )

            // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLETEXT_FONTSIZE', {fontSize: fontSize , tbKeys : choosedTdKeys}));
        } else {
            AssetLogic.updateAssetTableCell(
                {
                    updateType: 'fontSize',
                    value: fontSize,
                }
            )
            // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLE_ALLCELLS_ATTRIBUTE', {updateType: 'fontSize' , value : fontSize}));
        }
        this.setState({fontSizeShow: false})
    }

    /**font-size点击（选择框）*/
    fontSizeClickEvent = (fontSize,e) => {
        this.setState({
            initalFontsize: fontSize
        })
        assetManager.setPv_new(2321,{
            additional: {
                s0: fontSize
            }
        });
        this.stopPropagation(e);
        const {choosedTdKeys = []} = this.props;
        if(choosedTdKeys.length > 0) {

            AssetLogic.updateAssetTableText(
                {
                    updateType: "fontSize",
                    value: fontSize,
                    tbKeys: choosedTdKeys,
                    fun_name: "UPDATE_TABLETEXT_FONTSIZE",
                }
            )
        } else {
            AssetLogic.updateAssetTableCell(
                {
                    updateType: 'fontSize',
                    value: fontSize,
                }
            )
        }
        // canvasStore.dispatch(paintOnCanvas('UPDATE_FONTSIZE', {fontSize: fontSize}));

        this.setState({fontSizeShow: false})
    };

    /** font-size选择框显示（选择框）*/
    fontSizeShowEvent = (e) => {
        if(this.fontSizeClickListtener) {
            this.fontSizeClickListtener.remove();
        }

        let {fontSizeShow} = this.state;
        this.setState({fontSizeShow: !fontSizeShow});
        this.setState({
            isColorMorePanel: false,
            specificWordSelectListShow: false,
            showVariableColorParaIndex: -1
        });
        RightPanel.updateRightDialogFlag({rightDialog: !fontSizeShow});

        let th = this;
        this.fontSizeClickListtener = addEventListener(window,'click',function(e) {
            th.setState({
                fontSizeShow: false,
                isColorMorePanel: false,
            });
            RightPanel.updateRightDialogFlag({rightDialog: false});
            th.fontSizeClickListtener.remove()
        });
        e.stopPropagation();
        e.nativeEvent.stopPropagation();


    };

    /** font-size Array*/
    getFontSizeAllCharacters = () => {
        /* const fontSizeBase = [12, 14, 18, 24, 30, 36, 48, 60, 72, 84, 96, 108, 120];

        function addFontSize(arr) {
            let nextItem = arr[arr.length - 1];

            if (120 <= nextItem < 200) {
                nextItem += 12;
            } else if (200 <= nextItem < 300) {
                nextItem += 20;
            } else if (300 <= nextItem < 400) {
                nextItem += 30;
            } else if (400 <= nextItem < 500) {
                nextItem += 50;
            }

            if (nextItem < 500) {
                fontSizeBase.push(nextItem);
                addFontSize(fontSizeBase);
            }
        }

        addFontSize(fontSizeBase); */

        const fontSizeBase = [12,14,16,18,20,24,28,32,36,40,44,48,54,60,66,72,80,88,96,100,150,200,250];
        const content = fontSizeBase.map((item,index) => {
            return (
                <li
                    key={index}
                    onClick={this.fontSizeClickEvent.bind(this,item)}
                    onMouseEnter={this.fontlistItemHover.bind(this,item)}
                >{item}
                </li>
            )
        });

        return content;

    };

    /**
     *字体下拉列表hover
     *
     * @memberof AssetsColorBlock
     */
    fontlistItemHover = (fontSize,e) => {
        const {choosedTdKeys = []} = this.props;
        const firstTdKeyArr = choosedTdKeys[0] ? choosedTdKeys[0].split('_') : [0,0];
        const {initalFontsize = null} = this.state;
        let {toolPanel,specificWordList,isDesigner} = canvasStore.getState().onCanvasPainted;
        if(!initalFontsize) {
            let nowAsset;
            if(toolPanel.asset.meta && toolPanel.asset.meta.type == 'group' && toolPanel.asset.groupTextEditAsset && toolPanel.asset.groupTextEditAsset.meta.type == 'text') {
                nowAsset = toolPanel.asset.groupTextEditAsset;
            } else {
                nowAsset = toolPanel.asset;
            }
            let fontSizeInput = parseInt(nowAsset.attribute.text[firstTdKeyArr[0]][firstTdKeyArr[1]].fontSize)
            this.setState({
                initalFontsize: fontSizeInput
            },() => {
                if(choosedTdKeys.length > 0) {

                    AssetLogic.updateAssetTableText(
                        {
                            updateType: "fontSize",
                            value: fontSize,
                            tbKeys: choosedTdKeys,
                            fun_name: "UPDATE_TABLETEXT_FONTSIZE"
                        }
                    )
                    // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLETEXT_FONTSIZE', {fontSize: fontSize , tbKeys : choosedTdKeys}));
                } else {
                    // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLE_ALLCELLS_ATTRIBUTE', {
                    //     updateType: 'fontSize',
                    //     value : fontSize
                    // }));

                    AssetLogic.updateAssetTableCell(
                        {
                            updateType: 'fontSize',
                            value: fontSize,
                        }
                    )
                }
            })
        } else {
            if(choosedTdKeys.length > 0) {
                AssetLogic.updateAssetTableText(
                    {
                        updateType: "fontSize",
                        value: fontSize,
                        tbKeys: choosedTdKeys,
                        fun_name: "UPDATE_TABLETEXT_FONTSIZE"
                    }
                )
                // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLETEXT_FONTSIZE', {fontSize: fontSize , tbKeys : choosedTdKeys}));
            } else {
                AssetLogic.updateAssetTableCell(
                    {
                        updateType: 'fontSize',
                        value: fontSize,
                    }
                )
                // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLE_ALLCELLS_ATTRIBUTE', {
                //     updateType: 'fontSize',
                //     value : fontSize
                // }));
            }
        }
    }

    /**
     *字体下拉列表区域鼠标移出
     *
     * @memberof AssetsColorBlock
     */
    fontListAreaMouseLeave = (e) => {
        const {choosedTdKeys = []} = this.props;
        const {initalFontsize = null} = this.state;
        // let toolPanel = canvasStore.getState().onCanvasPainted.toolPanel;
        if(initalFontsize) {
            if(choosedTdKeys.length > 0) {
                AssetLogic.updateAssetTableText(
                    {
                        updateType: "fontSize",
                        value: initalFontsize,
                        tbKeys: choosedTdKeys,
                        fun_name: "UPDATE_TABLETEXT_FONTSIZE"
                    }
                )
                // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLETEXT_FONTSIZE', {fontSize: initalFontsize , tbKeys : choosedTdKeys}));
            } else {
                AssetLogic.updateAssetTableCell(
                    {
                        updateType: 'fontSize',
                        value: initalFontsize,
                    }
                )
                // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLE_ALLCELLS_ATTRIBUTE', {
                //     updateType: 'fontSize',
                //     value : initalFontsize
                // }));
            }
            this.setState({
                initalFontsize: null
            })
        }
    }
    /** ---------------------------------------font-size edit-------------------------------------------- */


    /**
     * 字体大小（滑动范围设置）
     */
    fontSizeFormat(fontSize) {
        fontSize = fontSize > 0 ? fontSize : 0
        // fontSize = fontSize > this.fontSizeMax ? this.fontSizeMax : fontSize;

        return fontSize;
    }


    /**
     * 设置字体
     */
    fontFamilyEvent(fontFamilyKey,e) {
        const {choosedTdKeys = []} = this.props;
        let fontFamily = fontFamilyKey,
            {toolPanel} = canvasStore.getState().onCanvasPainted
        const {newFontList} = this.state
        if(newFontList[fontFamilyKey]) {
            this.setState({
                newFontList: {...this.state.newFontList,[fontFamilyKey]: 0}
            },() => {
                localStorage.setItem('USELIST',Object.keys(this.state.newFontList).filter(key => this.state.newFontList[key] === 0).join(','))
            })
        }
        if(Boolean(this.state.newFontList[fontFamilyKey])) {
            if(!localStorage.getItem('NEWFONTTIP')) {
                localStorage.setItem('NEWFONTTIP',true)
                window.NEWFONTTIP = true
            }
        }
        this.setState({
            fontFamilyShow: false
        })
        this.fontFamilyShowFlag = 2

        let nowAsset;
        if(toolPanel.asset.meta && toolPanel.asset.meta.type == 'group' && toolPanel.asset.groupTextEditAsset && toolPanel.asset.groupTextEditAsset.meta.type == 'text') {
            nowAsset = toolPanel.asset.groupTextEditAsset;
        } else {
            nowAsset = toolPanel.asset;
        }

        fontFamily = this.fontNameList[fontFamilyKey]

        // canvasStore.dispatch(paintOnCanvas('UPDATE_FONTFAMILY', {fontFamily: fontFamily}));


        let className = 'assetEditLoad' + nowAsset.meta.className,
            className2 = 'assetTextArea' + nowAsset.meta.className,
            className2Dom = document.getElementsByClassName(className2)[0]

        // document.getElementsByClassName(className)[0].style.display = 'inline-block'
        WebFont.load({
            custom: {
                families: [fontFamilyKey]
            },
            timeout: 60000,
            loading: () => {
                // className2Dom.childNodes[0].style.opacity = 0
                // document.getElementsByClassName(className)[0].style.display = 'inline-block'
                console.log('loading',fontFamily)
            },
            active: () => {
                if(choosedTdKeys.length > 0) {
                    AssetLogic.updateAssetTableText(
                        {
                            updateType: "fontFamily",
                            value: fontFamily,
                            tbKeys: choosedTdKeys,
                            fun_name: "UPDATE_TABLE_FONTFAMILY"
                        }
                    )
                    // console.log("22222")
                    // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLE_FONTFAMILY', {fontFamily: fontFamily , tbKeys : choosedTdKeys}));
                } else {
                    AssetLogic.updateAssetTableCell(
                        {
                            updateType: 'fontFamily',
                            value: fontFamily,
                        }
                    )
                    // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLE_ALLCELLS_ATTRIBUTE', {updateType: 'fontFamily' , value : fontFamily}));
                }
                // className2Dom.childNodes[0].style.opacity = 100
                // document.getElementsByClassName(className)[0].style.display = 'none';
                // emitter.emit('TextEditorUpdateTime');
                console.log('active',fontFamily)
            },
            inactive: () => {
                // canvasStore.dispatch(paintOnCanvas('UPDATE_FONTFAMILY', {fontFamily: fontFamily}));
                // className2Dom.childNodes[0].style.opacity = 100
                // document.getElementsByClassName(className)[0].style.display = 'none';
                // emitter.emit('TextEditorUpdateTime');
                console.log('inactive',fontFamily)
            }
        });

        assetManager.setPv_new(3232,{
            additional: {
                fontFamily: fontFamily
            }
        });
        e && e.stopPropagation && e.stopPropagation();
        e && e.nativeEvent && e.nativeEvent.stopImmediatePropagation();
    }

    /**
     * 字体下拉框
     */
    fontFamilyShowEvent(e) {
        e.stopPropagation()
        assetManager.setPv_new(2320,{
            additional: {
                s0: this.fontFamilyShowFlag == 2 ? 'open' : 'close'
            }
        })
        if(this.specificWordSelectListShowFlag == 2) {
            this.fontFamilyShowFlag = 2
            if(this.state.fontFamilyShow == true) {
                this.fontFamilyShowFlag = 1
            }
        }
        if(this.specificWordSelectListShowFlag == 1 && this.state.fontFamilyShow == false && this.fontFamilyShowFlag == 1) {
            this.fontFamilyShowFlag = 2
        }
        // 增加显示本模板字体
        let {work,pageInfo} = canvasStore.getState().onCanvasPainted,
            page = work.pages[pageInfo.pageNow];
        let currNewfontFamily = [];
        for(const item of page.assets) {
            if(item.meta.type == 'text' && currNewfontFamily.indexOf(this.fontNameValueList[item.attribute.fontFamily]) == -1 && this.commercialFontNameListSingle.indexOf(this.fontNameValueList[item.attribute.fontFamily]) > -1) { // 只处理文字类型
                currNewfontFamily.push(this.fontNameValueList[item.attribute.fontFamily])
            }
        }
        let currFontNameList = {
            cateName: '本模板字体',
            fontList: currNewfontFamily
        }
        if(currNewfontFamily.length > 0) {
            if(this.commercialFontNameList[0].cateName !== '本模板字体') {
                this.commercialFontNameList.unshift(currFontNameList)
            } else {
                this.commercialFontNameList[0].fontList = currNewfontFamily; ` `
            }
        }
        if(this.fontFamilyShowFlag == 1) {
            return
        }
        var th = this
        this.fontFamilyShowFlag = 2
        this.setState({
            fontFamilyShow: !this.state.fontFamilyShow,
            // fontFamilyShow: true,
            cateTab: 'commercial',
        })
        if(this.fontFamilyShowClickListtener) {
            this.fontFamilyShowClickListtener.remove()
        }
        this.fontFamilyShowClickListtener = addEventListener(window,'click',function(e) {
            // if (th.fontFamilyShowFlag == 2) {
            //     th.fontFamilyShowFlag = 1
            //     return
            // }

            th.fontFamilyShowClickListtener.remove()

            var toolPanel = canvasStore.getState().onCanvasPainted.toolPanel

            if(toolPanel.asset == undefined || toolPanel.asset == "") {
                return false
            }

            th.setState({
                fontFamilyShow: false,
                isColorMorePanel: false
            })
            th.fontFamilyShowFlag = 2

        });

    }

    showVipFontBox(font) {

        // let { user } = canvasStore.getState().onCanvasPainted;

        // if(!(user.userId > 0)){
        //     emitter.emit('LoginPanelShow');
        //     return
        // }
        const {tgsManageFontSelectorStore = {}} = this.props;
        let {font_list = []} = tgsManageFontSelectorStore
        let have_count = 0
        font_list.forEach(v => {
            if(v.type == '剩余解锁次数') {
                have_count = v.have_count
            }
        });

        let windowInfo = {
            windowContent: <DownloadPopup isShowCloseInternal={true} isDownloadZero={true} key={new Date().getTime()} className="DownloadPopup" showFlag={0} font={font} have_count={have_count} />,
            popupWidth: 460,
            popupHeight: 320,
            style: {
                borderRadius: '4px',
                backgroundColor: '#FFFFFF'
            },
            popupTitleBarStyle: {
                width: 0,
                height: 0,
                display: 'none',
            }
            // popupTitleBarStyle：{
            //     display:none
            // }
        };
        setTimeout(() => {
            // 受限判断是否还有解锁个数，如果有弹出解锁框，如果没有弹出受限框
            if(have_count > 0) {
                windowInfo.popupWidth = 420;
                windowInfo.popupHeight = 244;
                windowInfo.style.boxSizing = 'border-box';
                emitter.emit('popupWindow',windowInfo);
                emitter.emit('DownloadPopupUS',44)
                assetManager.setPv_new(4716,{additional: {}});
            } else {
                emitter.emit('popupWindow',windowInfo);
                emitter.emit('DownloadPopupUS',41)
                assetManager.setPv_new(3953,{additional: {}});
            }
        },0)


    }

    /**
     * 特效字下拉框
     */
    specificWordSelectListShowEvent(e) {
        if(this.fontFamilyShowFlag == 2) {
            this.specificWordSelectListShowFlag = 2
            if(this.state.specificWordSelectListShow == true) {
                this.specificWordSelectListShowFlag = 1
            }
        }
        if(this.specificWordSelectListShowFlag == 1) {
            return
        }
        var th = this

        this.specificWordSelectListShowFlag = 2
        this.setState({
            // specificWordSelectListShow: !this.state.specificWordSelectListShow,
            specificWordSelectListShow: true,
            // fontFamilyShow: false,
            cateTab: 'commercial',
        })
        if(this.specificWordSelectListShowClickListtener) {
            this.specificWordSelectListShowClickListtener.remove()
        }

        this.specificWordSelectListShowClickListtener = addEventListener(window,'click',function(e) {
            if(th.specificWordSelectListShowFlag == 2) {
                th.specificWordSelectListShowFlag = 1
                return
            }

            th.specificWordSelectListShowClickListtener.remove()

            var toolPanel = canvasStore.getState().onCanvasPainted.toolPanel

            if(toolPanel.asset == undefined || toolPanel.asset == "") {
                return false
            }

            th.setState({
                specificWordSelectListShow: false,
                isColorMorePanel: false
            })
            th.specificWordSelectListShowFlag = 2

        });
    }

    /**
     * 设置颜色
     */
    borderColorEvent1(color,type) {
        this.colorEvent(color,type,'border');
        assetManager.setPv_new(1041,{
            additional: {
                type: 'border'
            }
        });
    }
    /**
    * 设置颜色--点击，只触发一次
    */
    borderColorEvent2(color,type) {
        this.colorEvent(color,type,'border');
    }
    /**
     * 设置颜色
     */
    bgColorEvent1(color,type) {
        this.colorEvent(color,type,'bg');
        assetManager.setPv_new(1041,{
            additional: {
                type: 'bg'
            }
        });
    }
    /**
    * 设置颜色--点击，只触发一次
    */
    bgColorEvent2(color,type) {
        this.colorEvent(color,type,'bg');
    }
    /**
     * 设置颜色
     */
    fontColorEvent1(color,type) {
        this.colorEvent(color,type,'font');
        assetManager.setPv_new(2360,{
            additional: {
                s1: 'font',
                s0: 'pre'
            }
        });
    }
    /**
    * 设置颜色--点击，只触发一次
    */
    fontColorEvent2(color,type) {
        this.colorEvent(color,type,'font');
        assetManager.setPv_new(2360,{
            additional: {
                s1: 'font',
                s0: 'panel'
            }
        });
    }
    /**
    * 设置颜色
    */
    colorEvent(color,type,colorType) {
        // if (type == 'click') {
        //     canvasStore.dispatch(paintOnCanvas('UPDATE_COLOR', {color: color.color}))
        //     canvasStore.dispatch(paintOnCanvas('UPDATE_COLOR_END', {color: color.color}))
        // } else {
        //     canvasStore.dispatch(paintOnCanvas('UPDATE_COLOR', {color: color.color}))
        // }
        const {choosedTdKeys = []} = this.props;

        if(choosedTdKeys.length > 0) {
            if(colorType == 'border') {
                AssetLogic.updateTableBorderColor(
                    {
                        color: color.color,tbKeys: choosedTdKeys
                    }
                )
                // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLEBORDER_COLOR', {color: color.color , tbKeys : choosedTdKeys}))
            } else if(colorType == 'bg') {
                AssetLogic.updateBgColor(
                    {
                        color: color.color,tbKeys: choosedTdKeys
                    }
                )
                // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLEBG_COLOR', {color: color.color , tbKeys : choosedTdKeys}))
            } else {
                AssetLogic.updateTableTextColor(
                    {
                        color: color.color,tbKeys: choosedTdKeys
                    }
                )
                // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLETEXT_COLOR', {color: color.color , tbKeys : choosedTdKeys}))
            }
        } else {
            // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLE_ALLCELLS_ATTRIBUTE', {
            //     updateType: colorType == 'border' ? 'borderColor' : colorType == 'bg'? 'backgroundColor' : 'fontColor',
            //     value : color.color
            // }));

            AssetLogic.updateAssetTableCell(
                {
                    updateType: colorType == 'border' ? 'borderColor' : colorType == 'bg' ? 'backgroundColor' : 'fontColor',
                    value: color.color,
                }
            )
        }
    }


    multChoiceColorPanelChangeEvent(changeIndex,e,type) {
        if(type == 'click') {
            // 保存修改状态
            AssetLogic.updateTextSpecificWordColorMathNewEnd({index: changeIndex,color: e.color})
        } else {
            AssetLogic.updateTextSpecificWordColorMathNew(
                {
                    index: changeIndex,changeColor: e.color
                }
            )
            // canvasStore.dispatch(paintOnCanvas('UPDATE_TEXT_SPECIFIC_WORD_COLORMATCH_NEW', {index: changeIndex, color: e.color}));
        }
        /*特效字配色更新（新）*/
        // e.stopPropagation();
        // e.nativeEvent.stopPropagation();
    }

    //UPDATE_TEXT_SPECIFIC_WORD_COLORMATCH

    /**
     * 特效字配色修改
     * @param colorMatchId
     * @param e
     */
    specificWordColorBlockItemClickEvent(colorMatchId,e) {
        const {toolPanel} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        AssetLogic.updateTextSpecificWordColorMatch({assetIndex: toolPanel.asset_index,effectColorMatch: colorMatchId})
        // canvasStore.dispatch(paintOnCanvas('UPDATE_TEXT_SPECIFIC_WORD_COLORMATCH', {effectColorMatch: colorMatchId}));

        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    /**
     * 颜色防止冒泡
     */
    colorClickEvent(e) {
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
    }

    /**
     * 防止冒泡
     */
    stopPropagation(e) {
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
    }

    noCopyrightMouseOver(key,noCopyrightList,e) {
        let show_copyright_tip = false;
        if(noCopyrightList.indexOf(key) >= 0) {
            show_copyright_tip = true;
        }
        this.setState({
            show_copyright_tip: show_copyright_tip
        })
    }

    noCopyrightMouseOut(e) {
        this.setState({
            show_copyright_tip: false
        })
    }

    /**
     * 颜色弹窗
     */
    colorShowEvent(e) {
        if(this.colorShowFlag == 1) {
            return
        }
        var th = this
        // this.colorShowFlag = 2
        this.setState({
            colorShow: true
        })

        if(this.colorShowClickListtener) {
            this.colorShowClickListtener.remove();
        }
        this.colorShowClickListtener = addEventListener(window,'click',function(e) {
            // if( th.colorShowFlag == 2 ){
            //     th.colorShowFlag = 1
            //     return
            // }
            th.colorShowClickListtener.remove()

            var toolPanel = canvasStore.getState().onCanvasPainted.toolPanel

            if(toolPanel.asset == undefined || toolPanel.asset == "") {
                return false
            }

            th.setState({
                colorShow: false,
                isColorMorePanel: false,
            })
            // th.colorShowFlag = 2

        });
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    choiceColorClickEvent(type = '',e) {
        if(this.choiceColorClickListtener) {
            this.choiceColorClickListtener.remove();
        }
        let moreColorObj = {
            isColorMorePanel: !this.state.isColorMorePanel,
            isBorderColorMore: false,
            isBgColorMore: false
        }
        if(type == 'border') {
            moreColorObj = {
                isBorderColorMore: !this.state.isBorderColorMore,
                isColorMorePanel: false,
                isBgColorMore: false
            };
        } else if(type == 'bg') {
            moreColorObj = {
                isBgColorMore: !this.state.isBgColorMore,
                isBorderColorMore: false,
                isColorMorePanel: false,
            };
        }
        this.setState({
            // isColorMorePanel: !this.state.isColorMorePanel,
            specificWordSelectListShow: false,
            showVariableColorParaIndex: -1,
            ...moreColorObj
        });
        let th = this;
        this.choiceColorClickListtener = addEventListener(window,'click',function(e) {
            if(matchesSelector(e.target,'.colorsArea .choiceColor,.commonSelect .moreColorIcon,.commonSelect .moreColorIconTest,.moreColorIcon .icon-yanse,.color-tip')) {
                return
            }
            th.setState({
                isColorMorePanel: false,
                isBorderColorMore: false,
                isBgColorMore: false,
                colorShow: false,
            });
            th.choiceColorClickListtener.remove()
        });
        //
        // e.stopPropagation();
        // e.nativeEvent.stopPropagation();
    }

    /**
     * 向左旋转90度
     */
    degrees90Left(e) {
        AssetLogic.degrees90Left({})
        // canvasStore.dispatch(paintOnCanvas('DEGREES_90_LEFT'))
    }

    /**
     * 向右旋转90度
     */
    degrees90Right(e) {
        AssetLogic.degrees90Right({})
        // canvasStore.dispatch(paintOnCanvas('DEGREES_90_RIGHT'))
    }

    inputFocus() {
        this.updateTextList();
        this.updateFavText();
        this.setState({
            isQuickEditingText: true
        });
    }

    inputBlur() {
        this.setState({
            isQuickEditingText: false
        });
    }

    /**
     * 文字下拉菜单选项卡切换（点击事件）
     */
    cateTabBtnClickEvent(cateTab,e) {
        this.setState({
            cateTab: cateTab
        });

        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }
    /**
 * 文字下拉菜单选项卡切换（点击事件）
 */
    cateTabBtnClickyuyan(yuyanType,e) {
        this.fontFamilyShowFlag = 2;
        this.setState({
            yuyanType: yuyanType,
            fontFamilyShow: true,
        });
        if(yuyanType == 'zn') {
            assetManager.setPv_new(1004,{additional: {}});
        } else {
            assetManager.setPv_new(1005,{additional: {}});
        }
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    /**
     * 特效字自定义颜色（点击事件）
     */
    choiceColorBtnClickEvent(showIndex,e) {
        let {showVariableColorParaIndex} = this.state;
        this.setState({
            isColorMorePanel: false,
            showVariableColorParaIndex: showVariableColorParaIndex == showIndex ? -1 : showIndex
        });

        if(this.choiceColorBtnClickListener) {
            this.choiceColorBtnClickListener.remove();
        }
        let th = this;
        this.choiceColorBtnClickListener = addEventListener(window,'click',(e) => {
            th.choiceColorBtnClickListener.remove();
            th.setState({
                showVariableColorParaIndex: -1
            });
        });

        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    specificWordItemClickEvent(effectID,e) {
        let effect = '';
        if(effectID > 0) {
            effect = effectID + '@0'
        }
        this.setState({
            specificWordSelectListShow: false,
        })
        this.specificWordSelectListShowFlag = 2;

        const {toolPanel} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        AssetLogic.updateAssetSpecificWordEffect(
            {
                assetIndex: toolPanel.asset_index,
                effect: effect,
            }
        )
        assetManager.setPv_new(184,{
            additional: {
                specificWordId: effectID,
                origin: 'tool'
            }
        });

        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    colorBlockItemDichroicClickEvent(colors,e) {

        // canvasStore.dispatch(paintOnCanvas('UPDATE_TEXT_SPECIFIC_WORD_COLORMATCH_DICHROIC', {colors: colors}))
        AssetLogic.updateTextSpecificWordColorMathDichroic(
            {
                colors
            }
        )
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    specificSizeMousedownEvent(e) {
        this.clientX = e.clientX;
        this.clientY = e.clientY;

        let {toolPanel,specificWordList} = canvasStore.getState().onCanvasPainted;

        let nowAsset;
        if(toolPanel.asset.meta && toolPanel.asset.meta.type == 'group' && toolPanel.asset.groupTextEditAsset && toolPanel.asset.groupTextEditAsset.meta.type == 'text') {
            nowAsset = toolPanel.asset.groupTextEditAsset;
        } else {
            nowAsset = toolPanel.asset;
        }
        let tempSizePara = nowAsset.attribute.effectVariant.variableSizePara[0];
        let tempSizeMax = tempSizePara.range.max,
            tempSizeMin = tempSizePara.range.min,
            tempSizeOffset = tempSizeMax - tempSizeMin,
            tempSizeNow = nowAsset.attribute.effectVariant.layers[tempSizePara.sizes[0].index][tempSizePara.sizes[0].key];
        let tempObject = {};
        Object.assign(tempObject,{
            sizeMax: tempSizeMax,
            sizeMin: tempSizeMin,
            sizeOffset: tempSizeOffset,
            sizeNow: tempSizeNow,
            barProp: (tempSizeNow - tempSizeMin) / tempSizeOffset
        });
        this.specificSizeParas = JSON.parse(JSON.stringify(tempObject));

        if(this.specificSizeMousedown) {
            this.specificSizeMousedown.remove();
        }
        if(this.specificSizeMouseUp) {
            this.specificSizeMouseUp.remove();
        }

        let th = this;
        this.specificSizeMousedown = addEventListener(window,'mousemove',(e) => {
            th.specificSizeMove(e);
        });
        this.specificSizeMouseUp = addEventListener(window,'mouseup',(e) => {
            th.specificSizeMousedown.remove();
            th.specificSizeMouseUp.remove();
        });
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    specificSizeMove(e) {
        let offsetX = e.clientX - this.clientX,
            offsetY = e.clientY - this.clientY,
            barProp = this.specificSizeParas.barProp + (offsetX / this.specificSizeBar);

        AssetLogic.updateTextSpecificSizes(
            {
                proportion: barProp
            }
        )
        // canvasStore.dispatch(paintOnCanvas('UPDATE_TEXT_SPECIFIC_SIZES', {proportion: barProp}));
    }

    /**
     * 滑动条点击事件
     */
    sliderDownEvent(sliderType,e) {
        let toolPanel = canvasStore.getState().onCanvasPainted.toolPanel;
        let nowAsset;
        if(toolPanel.asset.meta && toolPanel.asset.meta.type == 'group' && toolPanel.asset.groupTextEditAsset && toolPanel.asset.groupTextEditAsset.meta.type == 'text') {
            nowAsset = toolPanel.asset.groupTextEditAsset;
        } else {
            nowAsset = toolPanel.asset;
        }

        this.letterSpacing = nowAsset.attribute.letterSpacing;
        this.lineHeight = nowAsset.attribute.lineHeight;
        this.opacity = nowAsset.attribute.opacity;


        this.clientX = e.clientX;
        this.clientY = e.clientY;
        this.sliderType = sliderType;
        let th = this;
        this.sliderMoveListtener = addEventListener(window,'mousemove',function(e) {
            th.sliderMoveEvent(e);
        });
        this.sliderUpListtener = addEventListener(window,'mouseup',function(e) {
            th.sliderUpEvent(e);
        });

    }

    /**
     * 滑动条释放事件
     */
    sliderUpEvent(e) {
        this.sliderMoveListtener.remove();
        this.sliderUpListtener.remove();
    }

    /**
     * 滑动条移动事件
     */
    sliderMoveEvent(e) {
        const {choosedTdKeys = []} = this.props;
        let props = {};
        let toolPanel = canvasStore.getState().onCanvasPainted.toolPanel;
        switch(this.sliderType) {
            /*行间距*/
            case 'lineHeight': {
                let lineHeight = this.lineHeight + (e.clientX - this.clientX) * this.lineHeightMax / this.lineHeightWidth
                lineHeight = lineHeight > this.lineHeightMin ? lineHeight : this.lineHeightMin
                lineHeight = lineHeight < this.lineHeightMax ? lineHeight : this.lineHeightMax
                if(choosedTdKeys.length > 0) {
                    AssetLogic.updateAssetTableText(
                        {
                            updateType: "lineHeight",
                            value: this.lineHeightMin + (this.lineHeightMax - this.lineHeightMin) * lineHeight / this.lineHeightMax,
                            tbKeys: choosedTdKeys,
                            fun_name: "UPDATE_TABLETEXT_LINEHEIGHT"
                        }
                    )
                    // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLETEXT_LINEHEIGHT', {tbKeys:choosedTdKeys,lineHeight: this.lineHeightMin + (this.lineHeightMax - this.lineHeightMin) * lineHeight / this.lineHeightMax}))
                } else {
                    // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLE_ALLCELLS_ATTRIBUTE', {
                    //     updateType: 'lineHeight',
                    //     value : this.lineHeightMin + (this.lineHeightMax - this.lineHeightMin) * lineHeight / this.lineHeightMax
                    // }));

                    AssetLogic.updateAssetTableCell(
                        {
                            updateType: "lineHeight",
                            value: this.lineHeightMin + (this.lineHeightMax - this.lineHeightMin) * lineHeight / this.lineHeightMax,
                        }
                    )
                }

                break
            }
            /*字间距*/
            case 'letterSpacing': {
                let letterSpacing = this.letterSpacing + (e.clientX - this.clientX) * this.letterSpacingMax / this.letterSpacingWidth
                letterSpacing = letterSpacing > this.letterSpacingMin ? letterSpacing : this.letterSpacingMin
                letterSpacing = letterSpacing < this.letterSpacingMax ? letterSpacing : this.letterSpacingMax
                if(choosedTdKeys.length > 0) {
                    AssetLogic.updateAssetTableText(
                        {
                            letterSpacing: this.letterSpacingMin + (this.letterSpacingMax - this.letterSpacingMin) * letterSpacing / this.letterSpacingMax,
                            tbKeys: choosedTdKeys,
                            fun_name: "UPDATE_TABLETEXT_LETTERSPACING"
                        }
                    )
                    // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLETEXT_LETTERSPACING', {tbKeys:choosedTdKeys,letterSpacing: this.letterSpacingMin + (this.letterSpacingMax - this.letterSpacingMin) * letterSpacing / this.letterSpacingMax}))
                } else {
                    AssetLogic.updateAssetTableCell(
                        {
                            updateType: 'letterSpacing',
                            value: this.letterSpacingMin + (this.letterSpacingMax - this.letterSpacingMin) * letterSpacing / this.letterSpacingMax,
                        }
                    )
                }
                break
            }
            /*透明度*/
            case 'opacity':
                var opacity = this.opacityFormat(this.opacity + (e.clientX - this.clientX) * this.fontOpacityMax / this.fontOpacityWidth)

                Object.assign(props,{opacity: opacity});

                if(choosedTdKeys.length > 0) {
                    AssetLogic.updateTableTextOpacity(
                        {
                            tbKeys: choosedTdKeys,opacity: this.fontOpacityMin + (this.fontOpacityMax - this.fontOpacityMin) * opacity / this.fontOpacityMax
                        }
                    )
                    // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLETEXT_OPACITY', {tbKeys:choosedTdKeys,opacity: this.fontOpacityMin + (this.fontOpacityMax - this.fontOpacityMin) * opacity / this.fontOpacityMax}));
                } else {
                    // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLE_ALLCELLS_ATTRIBUTE', {
                    //     updateType: 'opacity',
                    //     value : this.fontOpacityMin + (this.fontOpacityMax - this.fontOpacityMin) * opacity / this.fontOpacityMax
                    // }));
                    AssetLogic.updateAssetTableCell(
                        {
                            updateType: 'opacity',
                            value: this.fontOpacityMin + (this.fontOpacityMax - this.fontOpacityMin) * opacity / this.fontOpacityMax,
                        }
                    )
                }
                break;

            default:
                break
        }
        this.setState(Object.assign({},props));
    }

    dropDownBoxClickEvent(value,e) {
        AssetLogic.quickEditingUpdateText({text: [value]});
        // canvasStore.dispatch(paintOnCanvas('QUICK_EDITING_UPDATE_TEXT', {text: [value]}))

        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    stopPropagation(e) {
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    windowResize(th,innerHeight) {
        let scrollbarHeight = innerHeight,
            rightLayoutDom = document.getElementsByClassName('floatToolPanel')

        // if (rightLayoutDom) {
        //     scrollbarHeight = rightLayoutDom[0].offsetHeight - 300
        // }

        th.setState({
            scrollbarHeight: scrollbarHeight
        })
    }

    componentDidMount() {
        let {canvas} = canvasStore.getState().onCanvasPainted;

        if(canvas.width == 2480 && canvas.height == 3366) {
            this.fontSizeMax = 1200;
        }
        this.windowResize(this,document.body.clientHeight - 200);
    }

    /*    componentDidUpdate(){
            let {toolPanel} = canvasStore.getState().onCanvasPainted;
            let nowAsset;
            if( toolPanel.asset.meta.type == 'group' && toolPanel.asset.groupTextEditAsset && toolPanel.asset.groupTextEditAsset.meta.type == 'text' ){
                nowAsset = toolPanel.asset.groupTextEditAsset;
            }else{
                nowAsset = toolPanel.asset;
            }
    
            if(!this.newText){
                this.newText = document.querySelector(`.asset.${nowAsset.meta.className}`).innerText.replace(/\s+/g,'');
            }
    
            let nowFontClass = this.fontNameValueList[nowAsset.attribute.fontFamily];
            if(this.newNoCopyrightList.indexOf(nowFontClass) >= 0){
                nowAsset.attribute.text= convertLanguage('繁',nowAsset.attribute.text.join('\n')).split('\n')
            }else{
                nowAsset.attribute.text= convertLanguage('简',nowAsset.attribute.text.join('\n')).split('\n')
            }
    
            if(this.newText != nowAsset.attribute.text.join('').replace(/\s+/g,'')){
                this.newText = nowAsset.attribute.text.join('').replace(/\s+/g,'');
                canvasStore.dispatch(paintOnCanvas('UPDATE_TEXT', {text: nowAsset.attribute.text}));
            }
        }*/
    /**
     * 透明度（滑动范围设置）
     */
    opacityFormat(opacity) {
        opacity = opacity > 0 ? opacity : 0
        opacity = opacity > this.fontOpacityMax ? this.fontOpacityMax : opacity;

        return opacity;
    }

    /**
     * 文字大小修改按钮
     * @param {} e 
     */
    fontSizeBtnMouseDownEvent(type,e) {
        e.preventDefault();
        e.nativeEvent?.preventDefault();
        const {choosedTdKeys = []} = this.props;
        const firstTdKeyArr = choosedTdKeys[0] ? choosedTdKeys[0].split('_') : [0,0];

        let {toolPanel} = canvasStore.getState().onCanvasPainted,
            nowAsset;
        if(toolPanel.asset.meta && toolPanel.asset.meta.type == 'group' && toolPanel.asset.groupTextEditAsset && toolPanel.asset.groupTextEditAsset.meta.type == 'text') {
            nowAsset = toolPanel.asset.groupTextEditAsset;
        } else {
            nowAsset = toolPanel.asset;
        }
        this.timeNow = new Date().getTime();
        this.fontSize = nowAsset.attribute.text[firstTdKeyArr[0]][firstTdKeyArr[1]].fontSize;
        let timeoutNum = 100;

        switch(type) {
            case "plus": {
                ++this.fontSize;
                this.fontSize = this.fontSize > 1000 ? 1000 : this.fontSize;
                this.forceUpdate()
                if(choosedTdKeys.length > 0) {
                    AssetLogic.updateAssetTableText(
                        {
                            updateType: "fontSize",
                            value: this.fontSize,
                            tbKeys: choosedTdKeys,
                            fun_name: "UPDATE_TABLETEXT_FONTSIZE"
                        }
                    )
                    // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLETEXT_FONTSIZE', {fontSize: this.fontSize , tbKeys : choosedTdKeys}));
                } else {
                    AssetLogic.updateAssetTableCell(
                        {
                            updateType: 'fontSize',
                            value: this.fontSize,
                        }
                    )
                    // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLE_ALLCELLS_ATTRIBUTE', {
                    //     updateType: 'fontSize',
                    //     value : this.fontSize
                    // }));
                }
                this.fontSizeTimeout = setInterval(() => {
                    ++this.fontSize;
                    this.fontSize = this.fontSize > 1000 ? 1000 : this.fontSize;
                    if(choosedTdKeys.length > 0) {
                        AssetLogic.updateAssetTableText(
                            {
                                updateType: "fontSize",
                                value: this.fontSize,
                                tbKeys: choosedTdKeys,
                                fun_name: "UPDATE_TABLETEXT_FONTSIZE"
                            }
                        )
                        // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLETEXT_FONTSIZE', {fontSize: this.fontSize, autoSaveFlag: 2 , tbKeys : choosedTdKeys}));
                    } else {
                        AssetLogic.updateAssetTableCell(
                            {
                                updateType: 'fontSize',
                                value: this.fontSize,
                            }
                        )
                        // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLE_ALLCELLS_ATTRIBUTE', {
                        //     updateType: 'fontSize',
                        //     value : this.fontSize
                        // }));
                    }

                },timeoutNum)
                assetManager.setPv_new(3233,{
                    additional: {
                        s0: "plus"
                    }
                })
                break;
            }
            case "less": {
                --this.fontSize;
                this.fontSize = this.fontSize < 12 ? 12 : this.fontSize;
                this.forceUpdate()
                if(choosedTdKeys.length > 0) {
                    AssetLogic.updateAssetTableText(
                        {
                            updateType: "fontSize",
                            value: this.fontSize,
                            tbKeys: choosedTdKeys,
                            fun_name: "UPDATE_TABLETEXT_FONTSIZE"
                        }
                    )
                    // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLETEXT_FONTSIZE', {fontSize: this.fontSize , tbKeys : choosedTdKeys}));
                } else {
                    AssetLogic.updateAssetTableCell(
                        {
                            updateType: 'fontSize',
                            value: this.fontSize,
                        }
                    )
                    // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLE_ALLCELLS_ATTRIBUTE', {
                    //     updateType: 'fontSize',
                    //     value : this.fontSize
                    // }));
                }
                this.fontSizeTimeout = setInterval(() => {
                    --this.fontSize;
                    this.fontSize = this.fontSize < 12 ? 12 : this.fontSize;
                    if(choosedTdKeys.length > 0) {
                        AssetLogic.updateAssetTableText(
                            {
                                updateType: "fontSize",
                                value: this.fontSize,
                                tbKeys: choosedTdKeys,
                                fun_name: "UPDATE_TABLETEXT_FONTSIZE"
                            }
                        )
                        // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLETEXT_FONTSIZE', {fontSize: this.fontSize, autoSaveFlag: 2 , tbKeys : choosedTdKeys}));
                    } else {
                        // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLE_ALLCELLS_ATTRIBUTE', {
                        //     updateType: 'fontSize',
                        //     value : this.fontSize
                        // }));
                        AssetLogic.updateAssetTableCell(
                            {
                                updateType: 'fontSize',
                                value: this.fontSize,
                            }
                        )

                    }
                },timeoutNum)
                assetManager.setPv_new(3233,{
                    additional: {
                        s0: "less"
                    }
                })
                break;
            }
        }

        let th = this;
        this.fontSizeBtnListtener = addEventListener(window,'mouseup',function(e) {
            th.fontSizeBtnListtener.remove();
            clearInterval(th.fontSizeTimeout);
        });



        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }
    /**
     * 获取当前模板使用字体
     * @returns 
    */
    getuseFontFamily = (font_list) => {
        if (font_list.length > 0) { 
            let { work } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas
            });
    
            let array = []
            let useFamily = []
            let font_listArr = []
            let usefont_list = []
            let newArr = []
            work.pages.forEach((item) => {
                array.push(...item.assets)
            })
            array.forEach(item => {
                if (item.meta.type === 'text') {
                    useFamily.push(item.attribute.fontFamily)
                }
            })
            font_list.forEach((item) => {
                if (item.list) {
                    font_listArr.push(...item.list)
                }
            })
            useFamily.forEach(item => {
                const arr = font_listArr.find(jitem => jitem.font === item)
                usefont_list.push(arr)
            })
            if (!Array.isArray(usefont_list)) {
                console.log('type error!')
                return
            }
            usefont_list.forEach((val)=>{
                if(newArr.indexOf(val) == -1){
                     newArr.push(val)
                 }
            })
            return newArr
        }
   
    }
    /**
     * 文字大小修改（滚轮）
     * @param {} e 
     */
    fontSizeBtnMouseScrollEvent(e) {
        const {choosedTdKeys = []} = this.props;
        const firstTdKeyArr = choosedTdKeys[0] ? choosedTdKeys[0].split('_') : [0,0];

        let {toolPanel} = canvasStore.getState().onCanvasPainted,
            nowAsset;
        if(toolPanel.asset.meta && toolPanel.asset.meta.type == 'group' && toolPanel.asset.groupTextEditAsset && toolPanel.asset.groupTextEditAsset.meta.type == 'text') {
            nowAsset = toolPanel.asset.groupTextEditAsset;
        } else {
            nowAsset = toolPanel.asset;
        }
        this.timeNow = new Date().getTime();
        this.fontSize = nowAsset.attribute.text[firstTdKeyArr[0]][firstTdKeyArr[1]].fontSize;
        let timeoutNum = 100;

        if(e.nativeEvent.deltaY < 0) {
            ++this.fontSize;
            this.fontSize = this.fontSize > 1000 ? 1000 : this.fontSize;
            this.forceUpdate()
            if(choosedTdKeys.length > 0) {
                AssetLogic.updateAssetTableText(
                    {
                        updateType: "fontSize",
                        value: this.fontSize,
                        tbKeys: choosedTdKeys,
                        fun_name: "UPDATE_TABLETEXT_FONTSIZE"
                    }
                )
                // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLETEXT_FONTSIZE', {fontSize: this.fontSize , tbKeys : choosedTdKeys}));
            } else {
                // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLE_ALLCELLS_ATTRIBUTE', {
                //     updateType: 'fontSize',
                //     value : this.fontSize
                // }));


                AssetLogic.updateAssetTableCell(
                    {
                        updateType: 'fontSize',
                        value: this.fontSize,
                    }
                )
            }
        } else if(e.nativeEvent.deltaY > 0) {
            --this.fontSize;
            this.fontSize = this.fontSize < 12 ? 12 : this.fontSize;
            this.forceUpdate()
            if(choosedTdKeys.length > 0) {

                AssetLogic.updateAssetTableText(
                    {
                        updateType: "fontSize",
                        value: this.fontSize,
                        tbKeys: choosedTdKeys,
                        fun_name: "UPDATE_TABLETEXT_FONTSIZE"
                    }
                )
                // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLETEXT_FONTSIZE', {fontSize: this.fontSize , tbKeys : choosedTdKeys}));
            } else {
                // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLE_ALLCELLS_ATTRIBUTE', {
                //     updateType: 'fontSize',
                //     value : this.fontSize
                // }));

                AssetLogic.updateAssetTableCell(
                    {
                        updateType: 'fontSize',
                        value: this.fontSize,
                    }
                )
            }
        }

        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    componentWillUnmount() {
        if(this.fontSizeTimeout) {
            clearTimeout(this.fontSizeTimeout);
        }

        this.floatFuncListener && this.floatFuncListener.remove();
    }

    getTeamBrandFont(userTeamInfo = '') {
        // let { userTeamInfo } = this.state
        if(userTeamInfo == '') return;
        assetManager.getTeamBrandFont(userTeamInfo.team_id,userTeamInfo.id).then(data => {
            data.json().then(resultData => {
                if(resultData.stat == 1 && resultData.msg.length >= 0) {
                    let list = resultData.msg
                    list.map(v => {
                        v.is_open = true
                    })
                    this.setState({
                        brandFontList: resultData.msg
                    })
                }
            });
        });
        /* Tgs_request({
            url:ipsApi(`/apiv2/get-my-team-brand-font?team_id=${userTeamInfo.team_id}&brand_id=${userTeamInfo.id}`)
        }).then(data=>{
            data.json().then(res=>{
                if(res.stat == 1 && res.msg.length > 0){
                    // this.setState({
                    //     teamInfo: res.msg,
                    //     isTeam: true,
                    //     userTeamInfo: res.msg[0].list[0],
                    // },()=>{
                    //     // this.getTeamBrandFont()
                    // });
                    // canvasStore.dispatch(tgsActionCreator(TGS_SET_BRAND_TEAMINFO, { teamInfo: res.msg }));
                    // canvasStore.dispatch(tgsActionCreator(TGS_SET_BRAND_USERTEAMINFO, { userTeamInfo: res.msg[0].list[0] }));
                    // this.getTeamBrandFont(res.msg[0].list[0])
                }
            })
        }) */
    }

    changeTeamType(item,index,e) {
        this.setState({
            userTeamInfo: item,
            chooseTeamIndex: index,
            showChooseTeam: false,
        },() => {
            // this.getTeamBrandFont()
        })
        TgsManageHelper.tgsSetBrandUserTeamInfo({
            userTeamInfo: item
        });
        this.getTeamBrandFont(item)

        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    // 判断右侧面板弹是否有窗
    changeRightPanelDialog(flag) {
        RightPanel.updateRightDialogFlag({rightDialog: flag})
    }

    /**
    * 文字选择框（选择事件）
    * @param font 
    */
    tgsFontSelectorChangeEvent = (font,options,brand = false,e) => {
        this.fontFamilyEvent(font.font_id,brand,e);
    }

    render() {
        const {choosedTdKeys = []} = this.props;
        const firstTdKeyArr = choosedTdKeys[0] ? choosedTdKeys[0].split('_') : [0,0];
        const {newTgsFontList,userTeamInfo,brandFontList,teamInfo,} = this.state;
        let url_props = getProps();
        let {info} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage
        }); // canvasStore.getState().infoManageRedux;
        let {toolPanel,specificWordList,user,isDesigner} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas
        });
        const {tgsManageFontSelectorStore: {font_list = [],brandInfo = {}}} = this.props;
        let useFontFamily = this.getuseFontFamily(font_list);
        let nowAsset;
        if(toolPanel.asset.meta && toolPanel.asset.meta.type == 'group' && toolPanel.asset.groupTextEditAsset && toolPanel.asset.groupTextEditAsset.meta.type == 'text') {
            nowAsset = toolPanel.asset.groupTextEditAsset;
        } else {
            nowAsset = toolPanel.asset;
        }

        // fix js error
        if(nowAsset.attribute.text === undefined) {
            return null;
        }
        if(!nowAsset.attribute.text[firstTdKeyArr[0]][firstTdKeyArr[1]]) {
            return null;
        }
        let fontSizeInput = parseInt(nowAsset.attribute.text[firstTdKeyArr[0]][firstTdKeyArr[1]].fontSize),
            fontSize = ((fontSizeInput > this.fontSizeMax ? this.fontSizeMax : fontSizeInput) - this.fontSizeMin) * this.fontSizeWidth / (this.fontSizeMax - this.fontSizeMin),
            // opacityInput = parseInt(nowAsset.attribute.opacity),
            // opacity = (opacityInput - this.fontOpacityMin) * this.fontOpacityWidth / (this.fontOpacityMax - this.fontOpacityMin),
            nowFontClass = this.fontNameValueList[nowAsset.attribute.text[firstTdKeyArr[0]][firstTdKeyArr[1]].fontFamily];
        if(this.commercialFontNameListSingle.indexOf(nowFontClass) == -1 && this.brandExclusive.indexOf(nowFontClass) == -1) {
            nowFontClass = "";
        }

        let {favTextList,textList,isQuickEditingText,cateTab,scrollbarHeight,showVariableColorParaIndex} = this.state,
            favTextListRepeat = {};

        let th = this,
            {fontFamilyShow,fontSizeShow,colorShow,show_copyright_tip,isColorMorePanel,isBorderColorMore,isBgColorMore,specificWordSelectListShow} = this.state;

        let myScrollbar = {
            width: '180px',
            height: '250px',
            top: '36px',
            background: '#fff',
            boxShadow: '0px 0px 10px rgba(200, 200, 200, 0.6)',
            borderRadius: '5px',
        }
        let curColor = nowAsset.attribute.text[firstTdKeyArr[0]][firstTdKeyArr[1]].color
        let choiceColor = {
            background: 'rgba(' + nowAsset.attribute.text[firstTdKeyArr[0]][firstTdKeyArr[1]].color.r + ', ' +
                nowAsset.attribute.text[firstTdKeyArr[0]][firstTdKeyArr[1]].color.g + ', ' +
                nowAsset.attribute.text[firstTdKeyArr[0]][firstTdKeyArr[1]].color.b + ', ' +
                nowAsset.attribute.text[firstTdKeyArr[0]][firstTdKeyArr[1]].color.a + ')'
        }



        if(nowAsset.attribute.cell[firstTdKeyArr[0]][firstTdKeyArr[1]] && nowAsset.attribute.cell[firstTdKeyArr[0]][firstTdKeyArr[1]].lineColor) {
            let choiceBorderColor = {

                background: `rgba(${nowAsset.attribute.cell[firstTdKeyArr[0]][firstTdKeyArr[1]].lineColor[0].r},
                    ${nowAsset.attribute.cell[firstTdKeyArr[0]][firstTdKeyArr[1]].lineColor[0].g},
                    ${nowAsset.attribute.cell[firstTdKeyArr[0]][firstTdKeyArr[1]].lineColor[0].b},
                    ${nowAsset.attribute.cell[firstTdKeyArr[0]][firstTdKeyArr[1]].lineColor[0].a})`
            },
                choiceBgColor = {
                    background: `rgba(${nowAsset.attribute.cell[firstTdKeyArr[0]][firstTdKeyArr[1]].background.color.r},
                    ${nowAsset.attribute.cell[firstTdKeyArr[0]][firstTdKeyArr[1]].background.color.g},
                    ${nowAsset.attribute.cell[firstTdKeyArr[0]][firstTdKeyArr[1]].background.color.b},
                    ${nowAsset.attribute.cell[firstTdKeyArr[0]][firstTdKeyArr[1]].background.color.a})`
                };
        }

        myScrollbar = fontFamilyShow ? myScrollbar : {width: 0}

        if(nowAsset.attribute.effect && nowAsset.attribute.effectVariant && nowAsset.attribute.effectVariant.name && nowAsset.attribute.effectVariant.variantColors.length > 0) {
            let tempColor = nowAsset.attribute.effectVariant.variantColors[nowAsset.attribute.effect.split('@')[1]]
            choiceColor = {
                background: 'rgba(' + tempColor.r + ', ' + tempColor.g + ', ' + tempColor.b + ', ' + tempColor.a + ')'
            }
        }

        fontSizeInput = fontSizeInput > 0 ? fontSizeInput : ''
        // opacityInput = opacityInput > 0 ? opacityInput : 0
        // opacity = opacity > 0 ? opacity : 0
        let noCopyrightList = [
            'font0',
            'font1',
            'font2',
            'font3',
            'font5',
            'font6',
            'font7',
            'font8',
            'font15',
            'font16',
            'font18',
            'font19',
            'font20',
            'font21',
            'font22',
            'font23',
        ];
        let noDesinerShowList = [];
        if(isDesigner) {
            noDesinerShowList = [
                'font14',
                'font160',
            ]
        }
        let show_copyright_tip_class = show_copyright_tip ? 'show' : '';

        let myScrollbar2 = {
            width: 'inherit',
            overflow: 'hidden',
            maxHeight: '320px',
        };
        let cateFontListMyScrollbar = {
            width: 'inherit',
            overflowX: "hidden",
            overflowY: "auto",
            height: scrollbarHeight - 80,
        },
            specificWordSelectList = {
                width: 'inherit',
                overflow: 'hidden',
                height: scrollbarHeight - 90,
            };
        let newFontSelectListStyle = {
            height: scrollbarHeight - 285 + 'px',
            width: "220px",
            top: '40px'
        }

        let selectBtnImgUrl = '//js.tuguaishou.com/specificWordPreview/-4-small.png?t=155543';
        if(nowAsset.attribute.effect) {
            selectBtnImgUrl = '//js.tuguaishou.com/specificWordPreview/' + nowAsset.attribute.effect.split('@')[0] + '-big.png?t=155543';
        }

        let fontSizeAllCharacters = this.getFontSizeAllCharacters();

        /*特效字特效大小 START*/
        let specificSize = 0,
            isSizeFlag = false;
        if(nowAsset.attribute.effectVariant && nowAsset.attribute.effectVariant.variableSizePara && nowAsset.attribute.effectVariant.variableSizePara.length > 0) {
            let tempSizePara = nowAsset.attribute.effectVariant.variableSizePara[0];
            let tempSizeMax = tempSizePara.range.max,
                tempSizeMin = tempSizePara.range.min,
                tempSizeOffset = tempSizeMax - tempSizeMin,
                tempSizeNow = nowAsset.attribute.effectVariant.layers[tempSizePara.sizes[0].index][tempSizePara.sizes[0].key];
            specificSize = Math.abs((tempSizeNow - tempSizeMin) / tempSizeOffset) * this.specificSizeBar;

            isSizeFlag = true;
        }
        /*特效字特效大小 END*/

        let {opacity} = nowAsset.attribute;

        opacity = (opacity - this.fontOpacityMin) * this.fontOpacityWidth / (this.fontOpacityMax - this.fontOpacityMin);
        opacity = opacity > 0 ? parseInt(opacity) : 0;

        let yuyanType = this.state.yuyanType || 'zn';

        
        return (
            <div className="textEditBlock tableEditBlock tableFontFamily">
                {/* <div className="item" style={{ width: '176px' }}> */}
                <div className="item" >
                    <div >
                        <TgsFontSelector
                            // getRecentlyUsedFont={this.getRecentlyUsedFont}
                            // recently_font={recently_font}
                            useFontFamily={useFontFamily && useFontFamily.length > 0 ? useFontFamily : []}
                            font_list={font_list.length > 0 ? font_list : newTgsFontList}
                            font_now={this.fontNameList[nowFontClass]}
                            onChange={this.tgsFontSelectorChangeEvent}
                            showVipFontBox={this.showVipFontBox.bind(this)}
                            user={user}
                            isDesigner={isDesigner}
                            brandInfo={{
                                brandDatas: teamInfo,
                                brandFontList: brandFontList,
                                userTeamInfo
                            }}
                            onRightPanelDialog={this.changeRightPanelDialog}
                            // brandOnchange = {(brand)=>{
                            // }}
                            brandOnchange={this.changeTeamType.bind(this)}
                            propsStyle={{
                                fontList: newFontSelectListStyle,
                            }}
                            templ_info={{
                                id: url_props['upicId'],
                                templ_id: info && parseInt(info.last_templ_id) > 0 ? parseInt(info.last_templ_id) : parseInt(url_props['picId'])
                            }}
                        ></TgsFontSelector>
                    </div>
                    <div style={{display:'flex',alignItems:'center',marginTop:'10px'}}>
                        <div className="commonSelect fontSizeSelect"
                            onClick={this.fontSizeShowEvent}
                            onWheel={this.stopPropagation.bind(this)}>
                            <div className="fontSizeBtnArea" onClick={this.stopPropagation.bind(this)}>
                                <div className="plusBtn" onMouseDown={this.fontSizeBtnMouseDownEvent.bind(this,'plus')} style={{transform: "rotateX(180deg)"}} >
                                    <i style={{fontSize: '10px'}} className="iconfont icon-jiahao"></i>
                                </div>
                                <div className="lessBtn" onMouseDown={this.fontSizeBtnMouseDownEvent.bind(this,'less')} >
                                    <i style={{fontSize: '10px'}} className="iconfont icon-jianhao"></i>
                                </div>
                            </div>
                            <input type="text" className="fontSizeSelectInput" value={fontSizeInput} onWheel={this.fontSizeBtnMouseScrollEvent.bind(this)}
                                onChange={this.fontSizeInputChangeEvent}
                                onFocus={(e) => {
                                    let dom = e.target;
                                    dom.select && dom.select();
                                    console.log(dom)
                                }}
                            // onClick={this.stopPropagation.bind(this)}
                            />
                            {
                                fontSizeShow && <ul className="fontsizeList" style={{height: scrollbarHeight}} onMouseLeave={this.fontListAreaMouseLeave.bind(this)}>
                                    {fontSizeAllCharacters}
                                </ul>
                            }
                        </div>
                        <div
                            className="commonSelect colorsArea"
                        >
                            <div className="choice-color-wrap"   onClick={this.choiceColorClickEvent.bind(this, 'fontColor')}>
                                <div
                                    className="choiceColor"
                                    style={choiceColor}
                                    ref="color"
                                ></div>
                                <div className="color-tip tip">文字色</div>
                            </div>
                            <div className="colorPanel" ref="colorSelect">
                                {isColorMorePanel  && (
                                    <MoreColorsPanel
                                        color={curColor}
                                        onChange={this.fontColorEvent2.bind(this)} 
                                        panelStyle={{
                                            position: 'fixed',
                                        }}
                                    />
                                )}
                            </div>
                        </div>
                    </div>
                   
                    {false && <div className="fontSelect commonSelect" ref="fontFamily" onClick={this.fontFamilyShowEvent}
                        onWheel={this.stopPropagation.bind(this)}>
                        <div className={"fontSelectButton " + nowFontClass}></div>
                        <i className="iconfont icon-xiangshang-copy-copy"></i>
                        {fontFamilyShow &&
                            <div className="newFontSelectList" style={newFontSelectListStyle}>
                                <div className="cateTabBtnArea">
                                    <div className={cateTab === 'commercial' ? "cateTabBtn active" : "cateTabBtn"}
                                        onClick={this.cateTabBtnClickEvent.bind(this,'commercial')}>字体均可商用
                                    </div>
                                </div>
                                <div className="cateTabBtnYyan">
                                    <div className={yuyanType === 'zn' ? "cateTabyyan active" : "cateTabyyan"}
                                        onClick={this.cateTabBtnClickyuyan.bind(this,'zn')}>中文
                                    </div>
                                    <div className={yuyanType === 'en' ? "cateTabyyan active" : "cateTabyyan"}
                                        onClick={this.cateTabBtnClickyuyan.bind(this,'en')}>英文
                                    </div>
                                </div>
                                <div className="cateFontListArea">
                                    <div className="cateFontList" onClick={this.stopPropagation.bind(this)}>
                                        <div style={cateFontListMyScrollbar} ref="fontFamilySelect" className={`scroll_content_auto font_list_scroll_container`}
                                            key={"fontFamilySelect-" + cateTab + "-" + yuyanType}>
                                            {cateTab === 'commercial' && this.commercialFontNameList.map((item) => {
                                                if(yuyanType === 'zn' && item.cateName !== '英文字体') {
                                                    return (
                                                        <div key={item.cateName}>
                                                            <div className="cateTitle">
                                                                {item.cateName}
                                                            </div>
                                                            {
                                                                item.fontList.map((subItem,subIndex) => {
                                                                    if(noDesinerShowList.indexOf(subItem) >= 0) {
                                                                        return;
                                                                    }
                                                                    let cateFontItemClassName = '';
                                                                    if(this.newNoCopyrightList.indexOf(subItem) >= 0) {
                                                                        cateFontItemClassName = 'needTips';
                                                                    }
                                                                    let chooseBut = {},extraClass = '';
                                                                    if(nowFontClass == subItem) {//item.cateName == '本模板字体' && 
                                                                        chooseBut = {background: '#f2f2f2'}
                                                                        extraClass = ' current_select_fontClass'
                                                                    }
                                                                    return (
                                                                        <div className={"cateFontItem " + cateFontItemClassName + `${extraClass}`}
                                                                            key={subIndex}
                                                                            onClick={this.fontFamilyEvent.bind(this,subItem)}
                                                                            style={chooseBut} >
                                                                            <div className={"cateFontImg " + subItem}></div>
                                                                            <div className="floatTipArea">此为繁体字体，仅适用于繁体字和部分简体字
                                                                            </div>
                                                                            {Boolean(this.state.newFontList[subItem]) && <span className='newCorner'>NEW</span>}
                                                                        </div>
                                                                    );
                                                                })
                                                            }
                                                        </div>
                                                    );
                                                }
                                                if(yuyanType === 'en' && item.cateName === '英文字体') {
                                                    return (
                                                        <div key={item.cateName}>
                                                            <div className="cateTitle">
                                                                {item.cateName}
                                                            </div>
                                                            {
                                                                item.fontList.map((subItem,subIndex) => {
                                                                    if(noDesinerShowList.indexOf(subItem) >= 0) {
                                                                        return;
                                                                    }
                                                                    let cateFontItemClassName = '';
                                                                    if(this.newNoCopyrightList.indexOf(subItem) >= 0) {
                                                                        cateFontItemClassName = 'needTips';
                                                                    }
                                                                    let chooseBut = {};
                                                                    if(item.cateName == '本模板字体' && nowFontClass == subItem) {
                                                                        chooseBut = {background: '#f2f2f2'}
                                                                    }
                                                                    return (
                                                                        <div className={"cateFontItem " + cateFontItemClassName}
                                                                            key={subIndex}
                                                                            onClick={this.fontFamilyEvent.bind(this,subItem)}
                                                                            style={chooseBut} >
                                                                            <div className={"cateFontImg " + subItem}></div>
                                                                            <div className="floatTipArea">此为繁体字体，仅适用于繁体字和部分简体字
                                                                            </div>
                                                                        </div>
                                                                    );
                                                                })
                                                            }
                                                        </div>
                                                    );
                                                }

                                            })}
                                            {/* {cateTab === 'nonCommercial' && <div>
                                            <div className="newCopyright_tip ">
                                                <i className="iconfont icon-tishi"></i>
                                                <p>若该字体用于商业用途，请于字体公司购买版权</p>
                                            </div>
                                            {this.nonCommercialFontNameList.map((item) => {
                                                if (this.delFontNameList.indexOf(item) >= 0) {
                                                    return;
                                                }

                                                return (
                                                    <div className="cateFontItem" key={item}
                                                         onClick={this.fontFamilyEvent.bind(this, item)}>
                                                        <div className={"cateFontImg " + item}></div>
                                                    </div>
                                                );
                                            })}
                                        </div>} */}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                    }
                </div>
                {/* <div className="item" style={{width:"176px"}}> */}
                {/* <div className="item">
                    <div className="commonSelect fontSizeSelect"
                        onClick={this.fontSizeShowEvent}
                        onWheel={this.stopPropagation.bind(this)}>
                        <div className="fontSizeBtnArea" onClick={this.stopPropagation.bind(this)}>
                            <div className="plusBtn" onMouseDown={this.fontSizeBtnMouseDownEvent.bind(this,'plus')} style={{transform: "rotateX(180deg)"}} >
                                <i style={{fontSize: '10px'}} className="iconfont icon-jiahao"></i>
                            </div>
                            <div className="lessBtn" onMouseDown={this.fontSizeBtnMouseDownEvent.bind(this,'less')} >
                                <i style={{fontSize: '10px'}} className="iconfont icon-jianhao"></i>
                            </div>
                        </div>
                        <input type="text" className="fontSizeSelectInput" value={fontSizeInput} onWheel={this.fontSizeBtnMouseScrollEvent.bind(this)}
                            onChange={this.fontSizeInputChangeEvent}
                        // onClick={this.stopPropagation.bind(this)}
                        />
                        {
                            fontSizeShow && <ul className="fontsizeList" style={{height: scrollbarHeight}} onMouseLeave={this.fontListAreaMouseLeave.bind(this)}>
                                {fontSizeAllCharacters}
                            </ul>
                        }
                    </div>
                </div> */}
                <div className="item"> 
                    {/* <div className="commonSelect colorsArea" onClick={this.choiceColorClickEvent.bind(this)}>
                        <MoreColorsPreinstall dropdownStyle={{ top: '154px', right: '25px' }} 
                            btnStyle={{ position: 'relative', float: 'right' }} 
                            onCallback={this.fontColorEvent1.bind(this)} 
                            closeColorSelect = {()=>this.setState({isColorMorePanel : false})}
                        />

                        <div className="choiceColor"
                            style={choiceColor}
                            ref="color">
                        </div>

                    </div>
                    <div className="colorPanel" ref="colorSelect">
                        {isColorMorePanel && <MoreColorsPanel color={nowAsset.attribute.text[firstTdKeyArr[0]][firstTdKeyArr[1]].color}
                            onChange={this.fontColorEvent2.bind(this)} 
                            style={{top:12}}
                        />}
                    </div> */}
             
                </div>
            </div>
        );
    }
}
const mapStateToProps = (state) => {
    return {
        tgsManageFontSelectorStore: state.tgsManageStore.tgs_font_selector
    };
}
NewTextEditBlock = connect(mapStateToProps,null)(NewTextEditBlock);



/**
 * 表格边框
 */
class TableBorderStylePanel extends Component {
    constructor(props) {
        super(props);
        this.state = {
            borderColorShow: false,
            borderTypeShow: false,
            borderDegreeShow: false,
            borderColor: {},
            borderStyle: '',
            borderWeight: '',
            choosedTdKeys: [],
            selectType: '',
            isColorMore: false,
            isStyelMore: false,
            isWeightMore: false,
            disableBtns: [],
        }
    }

    componentWillMount() {
        const {toolPanel,newAsset,choosedTdKeys} = this.publicFunction();
        const {cell} = newAsset.attribute;
        let selectType,isColorMore,isStyelMore,isWeightMore,borderColor,borderStyle,borderWeight;
        let selectColor = [];
        let selectStyle = [];
        let selectWeight = [];
        const rowNum = cell.length;
        const colNum = cell[0].length;
        //全选
        if(choosedTdKeys.length === 0) {
            selectType = 'all';
            cell.forEach(item => {
                item.forEach(it => {
                    it.lineStyle.forEach(i => {
                        if(i !== 'hidden') {
                            selectStyle.push(i);
                        }
                    })
                    it.lineWidth.forEach(i => {
                        selectWeight.push(i)
                    })
                    it.lineColor.forEach(i => {
                        selectColor.push(i)
                    })
                })
            })
            if(colNum === 1 && rowNum === 1) {
                this.setState({disableBtns: [3,4,5]});
            } else if(colNum === 1 && rowNum > 1) {
                this.setState({disableBtns: [3,4]});
            } else if(colNum > 1 && rowNum === 1) {
                this.setState({disableBtns: [3,5]});
            } else {
                this.setState({disableBtns: []});
            }

        } else {
            //选中多个
            choosedTdKeys.map(v => {
                let keysArr = v.split('_');
                const row = Number.parseInt(keysArr[0]);
                const col = Number.parseInt(keysArr[1]);
                cell[row][col].lineStyle.forEach(i => {
                    if(i !== 'hidden') {
                        selectStyle.push(i);
                    }
                });
                cell[row][col].lineWidth.forEach(i => {
                    selectWeight.push(i);
                });
                cell[row][col].lineColor.forEach(i => {
                    selectColor.push(i);
                });
            })
            let rowArr = [];
            let colArr = [];
            choosedTdKeys.forEach(v => {
                let keysArr = v.split('_');
                const row = Number.parseInt(keysArr[0]);
                const col = Number.parseInt(keysArr[1]);
                rowArr.push(row);
                colArr.push(col);
            })
            const rowMax = Math.max.apply(null,rowArr);
            const rowMin = Math.min.apply(null,rowArr);
            const colMax = Math.max.apply(null,colArr);
            const colMin = Math.min.apply(null,colArr);
            if(rowMax === rowMin && colMax === colMin) {
                selectType = 'one';
                this.setState({disableBtns: [3,4,5]});
            } else if(rowMax === rowMin && colMax !== colMin) {
                this.setState({disableBtns: [3,5]});
                selectType = 'oneRow';
            } else if(rowMax !== rowMin && colMax === colMin) {
                this.setState({disableBtns: [3,4]});
                selectType = 'oneCol';
            } else if(rowMax !== rowMin && colMax !== colMin) {
                selectType = 'moreRowCol';
            }
        }
        isColorMore = !selectColor.every(item => equal(item,selectColor[0]));
        isStyelMore = !selectStyle.every(item => equal(item,selectStyle[0]));
        isWeightMore = !selectWeight.every(item => equal(item,selectWeight[0]));
        if(!isColorMore) {
            borderColor = cell[0][0].lineColor[0];
        }
        if(!isStyelMore) {
            borderStyle = selectStyle.length === 0 ? 'none' : selectStyle[0];
        }
        if(!isWeightMore) {
            borderWeight = cell[0][0].lineWidth[0];;
        }
        this.setState({choosedTdKeys,toolPanel,newAsset,selectType,isColorMore,isStyelMore,isWeightMore,borderColor,borderStyle,borderWeight})
    }

    publicFunction() {
        const {toolPanel,work,pageInfo} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if(typeof toolPanel.asset_index != 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work,pageInfo,{index: toolPanel.asset_index});
        const newAsset = cloneDeep(asset);
        const {choosedTdKeys = []} = asset;
        return {toolPanel,newAsset,choosedTdKeys};
    }

    colorClickEvent(e) {
        this.setState({borderColorShow: !this.state.borderColorShow})
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
    }
    typeClickEvent(e) {
        this.setState({borderTypeShow: !this.state.borderTypeShow})
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
    }
    degreeClickEvent(e) {
        this.setState({borderDegreeShow: true})
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
    }

    scopeClickEvent(key) {
        const {toolPanel,newAsset,choosedTdKeys} = this.publicFunction();
        if(!newAsset || !newAsset.attribute || !newAsset.attribute.cell) return;
        const {cell} = newAsset.attribute;
        const rowNum = cell.length;
        const colNum = cell[0].length;
        const allSolid = () => {
            for(let i = 0; i < cell.length; i++) {
                for(let s = 0; s < cell[i].length; s++) {
                    for(let t = 0; t < cell[i][s].lineStyle.length; t++) {
                        newAsset.attribute.cell[i][s].lineStyle[t] = 'solid';
                        newAsset.attribute.cell[i][s].lineWidth[t] = 1;
                    }
                }
            }
        }
        const allHidden = () => {
            for(let i = 0; i < cell.length; i++) {
                for(let s = 0; s < cell[i].length; s++) {
                    for(let t = 0; t < cell[i][s].lineStyle.length; t++) {
                        newAsset.attribute.cell[i][s].lineStyle[t] = 'hidden';
                    }
                }
            }
        }
        const isAllBorderShow = () => {
            let isAllShow = true;
            for(let i = 0; i < cell.length; i++) {
                for(let s = 0; s < cell[i].length; s++) {
                    for(let t = 0; t < cell[i][s].lineStyle.length; t++) {
                        if(newAsset.attribute.cell[i][s] && newAsset.attribute.cell[i][s].lineStyle[t] == 'hidden') {
                            isAllShow = false;
                            return isAllShow;
                        }
                    }
                }
            }
            return isAllShow;
        }
        // 是否选中整个表格
        if(this.state.selectType !== 'all') {
            if(choosedTdKeys.length > 0) {
                let rowArr = [];
                let colArr = [];
                choosedTdKeys.forEach(v => {
                    let keysArr = v.split('_');
                    const row = Number.parseInt(keysArr[0]);
                    const col = Number.parseInt(keysArr[1]);
                    rowArr.push(row);
                    colArr.push(col);
                })
                const rowMax = Math.max.apply(null,rowArr);
                const rowMin = Math.min.apply(null,rowArr);
                const colMax = Math.max.apply(null,colArr);
                const colMin = Math.min.apply(null,colArr);
                choosedTdKeys.map(v => {
                    let keysArr = v.split('_');
                    const row = Number.parseInt(keysArr[0]);
                    const col = Number.parseInt(keysArr[1]);
                    switch(key) {
                        case 1:
                            for(let i = 0; i < cell[row][col].lineStyle.length; i++) {
                                newAsset.attribute.cell[row][col].lineStyle[i] = 'hidden';
                            }
                            if(cell?.[row - 1]?.[col]?.lineStyle[2]) { // 上方单元格的 bottom
                                newAsset.attribute.cell[row - 1][col].lineStyle[2] = 'hidden';
                            }
                            if(cell?.[row + 1]?.[col]?.lineStyle[0]) { // 下方单元格的 top
                                newAsset.attribute.cell[row + 1][col].lineStyle[0] = 'hidden';
                            }
                            if(cell?.[row]?.[col - 1]?.lineStyle[1]) { // 左方单元格的 right
                                newAsset.attribute.cell[row][col - 1].lineStyle[1] = 'hidden';
                            }
                            if(cell?.[row]?.[col + 1]?.lineStyle[3]) { // 右方单元格的 left
                                newAsset.attribute.cell[row][col + 1].lineStyle[3] = 'hidden';
                            }
                            break;
                        case 2:
                            for(let i = 0; i < cell[row][col].lineStyle.length; i++) {
                                newAsset.attribute.cell[row][col].lineStyle[i] = 'solid';
                            }
                            if(cell?.[row - 1]?.[col]?.lineStyle[2]) { // 上方单元格的 bottom
                                newAsset.attribute.cell[row - 1][col].lineStyle[2] = 'solid';
                            }
                            if(cell?.[row + 1]?.[col]?.lineStyle[0]) { // 下方单元格的 top
                                newAsset.attribute.cell[row + 1][col].lineStyle[0] = 'solid';
                            }
                            if(cell?.[row]?.[col - 1]?.lineStyle[1]) { // 左方单元格的 right
                                newAsset.attribute.cell[row][col - 1].lineStyle[1] = 'solid';
                            }
                            if(cell?.[row]?.[col + 1]?.lineStyle[3]) { // 右方单元格的 left
                                newAsset.attribute.cell[row][col + 1].lineStyle[3] = 'solid';
                            }
                            break;
                        case 3:
                            for(let r = 0; r < rowMax - rowMin + 1; r++) {
                                for(let c = 0; c < colMax - colMin; c++) {
                                    newAsset.attribute.cell[rowMin + r][colMin + c].lineStyle[1] = 'solid';
                                    newAsset.attribute.cell[rowMin + r][colMin + c + 1].lineStyle[3] = 'solid';
                                }
                            }
                            for(let r = 0; r < rowMax - rowMin; r++) {
                                for(let c = 0; c < colMax - colMin + 1; c++) {
                                    newAsset.attribute.cell[rowMin + r][colMin + c].lineStyle[2] = 'solid';
                                    newAsset.attribute.cell[rowMin + r + 1][colMin + c].lineStyle[0] = 'solid';
                                }
                            }
                            break;
                        case 4:
                            for(let r = 0; r < rowMax - rowMin + 1; r++) {
                                for(let c = 0; c < colMax - colMin; c++) {
                                    newAsset.attribute.cell[rowMin + r][colMin + c].lineStyle[1] = 'solid';
                                    newAsset.attribute.cell[rowMin + r][colMin + c + 1].lineStyle[3] = 'solid';
                                }
                            }
                            break;
                        case 5:
                            for(let r = 0; r < rowMax - rowMin; r++) {
                                for(let c = 0; c < colMax - colMin + 1; c++) {
                                    newAsset.attribute.cell[rowMin + r][colMin + c].lineStyle[2] = 'solid';
                                    newAsset.attribute.cell[rowMin + r + 1][colMin + c].lineStyle[0] = 'solid';
                                }
                            }
                            break;
                        case 6:
                            // 上边线
                            if(cell?.[rowMin - 1]?.[col]?.lineStyle[2]) {
                                newAsset.attribute.cell[rowMin - 1][col].lineStyle[2] = 'solid';
                            }
                            if(cell?.[rowMin]?.[col]?.lineStyle[0]) {
                                newAsset.attribute.cell[rowMin][col].lineStyle[0] = 'solid';
                            }
                            // 下边线
                            if(cell?.[rowMax + 1]?.[col]?.lineStyle[0]) {
                                newAsset.attribute.cell[rowMax + 1][col].lineStyle[0] = 'solid';
                            }
                            if(cell?.[rowMax]?.[col]?.lineStyle[2]) {
                                newAsset.attribute.cell[rowMax][col].lineStyle[2] = 'solid';
                            }
                            // 左边线
                            if(cell?.[row]?.[colMin - 1]?.lineStyle[1]) {
                                newAsset.attribute.cell[row][colMin - 1].lineStyle[1] = 'solid';
                            }
                            if(cell?.[row]?.[colMin]?.lineStyle[3]) {
                                newAsset.attribute.cell[row][colMin].lineStyle[3] = 'solid';
                            }
                            // 右边线
                            if(cell?.[row]?.[colMax + 1]?.lineStyle[3]) {
                                newAsset.attribute.cell[row][colMax + 1].lineStyle[3] = 'solid';
                            }
                            if(cell?.[row]?.[colMax]?.lineStyle[1]) {
                                newAsset.attribute.cell[row][colMax].lineStyle[1] = 'solid';
                            }
                            break;
                        case 7:
                            // 左边线
                            if(cell?.[row]?.[colMin - 1]?.lineStyle[1]) {
                                newAsset.attribute.cell[row][colMin - 1].lineStyle[1] = 'solid';
                            }
                            if(cell?.[row]?.[colMin]?.lineStyle[3]) {
                                newAsset.attribute.cell[row][colMin].lineStyle[3] = 'solid';
                            }
                            break;
                        case 8:
                            // 右边线
                            if(cell?.[row]?.[colMax + 1]?.lineStyle[3]) {
                                newAsset.attribute.cell[row][colMax + 1].lineStyle[3] = 'solid';
                            }
                            if(cell?.[row]?.[colMax]?.lineStyle[1]) {
                                newAsset.attribute.cell[row][colMax].lineStyle[1] = 'solid';
                            }
                            break;
                        case 9:
                            // 上边线
                            if(cell?.[rowMin - 1]?.[col]?.lineStyle[2]) {
                                newAsset.attribute.cell[rowMin - 1][col].lineStyle[2] = 'solid';
                            }
                            if(cell?.[rowMin]?.[col]?.lineStyle[0]) {
                                newAsset.attribute.cell[rowMin][col].lineStyle[0] = 'solid';
                            }
                            break;
                        case 10:
                            // 下边线
                            if(cell?.[rowMax + 1]?.[col]?.lineStyle[0]) {
                                newAsset.attribute.cell[rowMax + 1][col].lineStyle[0] = 'solid';
                            }
                            if(cell?.[rowMax]?.[col]?.lineStyle[2]) {
                                newAsset.attribute.cell[rowMax][col].lineStyle[2] = 'solid';
                            }
                            break;
                    }
                });
            }
        } else {
            // 如果表格全部边框线展示，则隐藏所有边框线
            if(key !== 1 && key !== 2 && isAllBorderShow()) {
                allHidden();
            }
            switch(key) {
                case 1:
                    allHidden();
                    break;
                case 2:
                    allSolid();
                    break;
                case 3:
                    for(let r = 0; r < rowNum - 1; r++) {
                        for(let c = 0; c < colNum; c++) {
                            newAsset.attribute.cell[r][c].lineStyle[2] = 'solid';
                            newAsset.attribute.cell[r + 1][c].lineStyle[0] = 'solid';

                            newAsset.attribute.cell[r][c].lineWidth[2] = 1;
                            newAsset.attribute.cell[r + 1][c].lineWidth[0] = 1;
                        }
                    }
                    for(let r = 0; r < rowNum; r++) {
                        for(let c = 0; c < colNum - 1; c++) {
                            newAsset.attribute.cell[r][c].lineStyle[1] = 'solid';
                            newAsset.attribute.cell[r][c + 1].lineStyle[3] = 'solid';

                            newAsset.attribute.cell[r][c].lineWidth[1] = 1;
                            newAsset.attribute.cell[r][c + 1].lineWidth[3] = 1;
                        }
                    }
                    break;
                case 4:
                    for(let r = 0; r < rowNum; r++) {
                        for(let c = 0; c < colNum - 1; c++) {
                            newAsset.attribute.cell[r][c].lineStyle[1] = 'solid';
                            newAsset.attribute.cell[r][c + 1].lineStyle[3] = 'solid';

                            newAsset.attribute.cell[r][c].lineWidth[1] = 1;
                            newAsset.attribute.cell[r][c + 1].lineWidth[3] = 1;
                        }
                    }
                    break;
                case 5:
                    for(let r = 0; r < rowNum - 1; r++) {
                        for(let c = 0; c < colNum; c++) {
                            newAsset.attribute.cell[r][c].lineStyle[2] = 'solid';
                            newAsset.attribute.cell[r + 1][c].lineStyle[0] = 'solid';

                            newAsset.attribute.cell[r][c].lineWidth[2] = 1;
                            newAsset.attribute.cell[r + 1][c].lineWidth[0] = 1;
                        }
                    }
                    break;
                case 6:
                    for(let i = 0; i < rowNum; i++) {
                        newAsset.attribute.cell[i][0].lineStyle[3] = 'solid';
                        newAsset.attribute.cell[i][colNum - 1].lineStyle[1] = 'solid';

                        newAsset.attribute.cell[i][0].lineWidth[3] = 1;
                        newAsset.attribute.cell[i][colNum - 1].lineWidth[1] = 1;
                    }
                    for(let i = 0; i < colNum; i++) {
                        newAsset.attribute.cell[0][i].lineStyle[0] = 'solid';
                        newAsset.attribute.cell[rowNum - 1][i].lineStyle[2] = 'solid';

                        newAsset.attribute.cell[0][i].lineWidth[0] = 1;
                        newAsset.attribute.cell[rowNum - 1][i].lineWidth[2] = 1;
                    }
                    break;
                case 7:
                    for(let i = 0; i < rowNum; i++) {
                        newAsset.attribute.cell[i][0].lineStyle[3] = 'solid';

                        newAsset.attribute.cell[i][0].lineWidth[3] = 1;
                    }
                    break;
                case 8:
                    for(let i = 0; i < rowNum; i++) {
                        newAsset.attribute.cell[i][colNum - 1].lineStyle[1] = 'solid';

                        newAsset.attribute.cell[i][colNum - 1].lineWidth[1] = 1;
                    }
                    break;
                case 9:
                    for(let i = 0; i < colNum; i++) {
                        newAsset.attribute.cell[0][i].lineStyle[0] = 'solid';

                        newAsset.attribute.cell[0][i].lineWidth[0] = 1;
                    }
                    break;
                case 10:
                    for(let i = 0; i < colNum; i++) {
                        newAsset.attribute.cell[rowNum - 1][i].lineStyle[2] = 'solid';

                        newAsset.attribute.cell[rowNum - 1][i].lineWidth[2] = 1;
                    }
                    break;
            }
        }

        newAsset.meta.rt_select_key = key;

        assetManager.setPv_new(7132,{
            additional: {
                i0: key,
            }
        });
        AssetLogic.updateAssetTableStyle(
            {
                asset: newAsset,
                assetIndex: toolPanel.asset_index,
            }
        )
        canvasStore.dispatch(paintOnCanvas('UPDATE_TABLE_STYLE',Object.assign(newAsset,{autoSaveFlag: 2})));
    }

    // 线条类型修改
    selectTypeClickEvent(type,key) {
        this.setState({borderTypeShow: false,borderStyle: type,isStyelMore: false});
        const {toolPanel,newAsset,choosedTdKeys} = this.publicFunction();
        const changeType = (self) => {
            if(self === 'hidden') {
                return 'hidden';
            } else {
                return type;
            }
        }

        if(choosedTdKeys.length > 0) {
            choosedTdKeys.map(v => {
                let keysArr = v.split('_');
                const row = Number.parseInt(keysArr[0]);
                const col = Number.parseInt(keysArr[1]);
                for(let i = 0; i < newAsset.attribute.cell[row][col].lineStyle.length; i++) {
                    newAsset.attribute.cell[row][col].lineStyle[i] = changeType(newAsset.attribute.cell[row][col].lineStyle[i]);
                }
                if(newAsset.attribute.cell?.[row - 1]?.[col]?.lineStyle[2]) {
                    // 上方单元格的 bottom
                    newAsset.attribute.cell[row - 1][col].lineStyle[2] = changeType(newAsset.attribute.cell[row - 1][col].lineStyle[2]);
                }
                if(newAsset.attribute.cell?.[row + 1]?.[col]?.lineStyle[0]) {
                    // 下方单元格的 top
                    newAsset.attribute.cell[row + 1][col].lineStyle[0] = changeType(newAsset.attribute.cell[row + 1][col].lineStyle[0]);
                }
                if(newAsset.attribute.cell?.[row]?.[col - 1]?.lineStyle[1]) {
                    // 左方单元格的 right
                    newAsset.attribute.cell[row][col - 1].lineStyle[1] = changeType(newAsset.attribute.cell[row][col - 1].lineStyle[1]);
                }
                if(newAsset.attribute.cell?.[row]?.[col + 1]?.lineStyle[3]) {
                    // 右方单元格的 left
                    newAsset.attribute.cell[row][col + 1].lineStyle[3] = changeType(newAsset.attribute.cell[row][col + 1].lineStyle[3]);
                }
            });

        } else {
            if(newAsset && newAsset.attribute && newAsset.attribute.cell) {
                for(let i = 0; i < newAsset.attribute.cell.length; i++) {
                    for(let s = 0; s < newAsset.attribute.cell[i].length; s++) {
                        for(let t = 0; t < newAsset.attribute.cell[i][s].lineStyle.length; t++) {
                            newAsset.attribute.cell[i][s].lineStyle[t] = changeType(newAsset.attribute.cell[i][s].lineStyle[t]);
                        }
                    }
                }
            }
        }
        assetManager.setPv_new(7130,{
            additional: {
                i0: key,
            }
        });
        AssetLogic.updateAssetTableStyle(
            {
                asset: newAsset,
                assetIndex: toolPanel.asset_index,
            }
        )
        canvasStore.dispatch(paintOnCanvas('UPDATE_TABLE_STYLE',Object.assign(newAsset,{autoSaveFlag: 2})));
    }

    // 颜色修改
    colorChangeEvent(color) {
        this.setState({
            borderColor: color.color,
            isColorMore: false,
        });
        const {toolPanel,newAsset,choosedTdKeys} = this.publicFunction();
        // 是否选择整个表格
        if(choosedTdKeys.length > 0) {
            choosedTdKeys.map(v => {
                let keysArr = v.split('_');
                const row = Number.parseInt(keysArr[0]);
                const col = Number.parseInt(keysArr[1]);
                for(let i = 0; i < newAsset.attribute.cell[row][col].lineColor.length; i++) {
                    newAsset.attribute.cell[row][col].lineColor[i] = color.color;
                }
                if(newAsset.attribute.cell?.[row - 1]?.[col]?.lineColor[2]) {
                    // 上方单元格的 bottom
                    newAsset.attribute.cell[row - 1][col].lineColor[2] = color.color;
                }
                if(newAsset.attribute.cell?.[row + 1]?.[col]?.lineColor[0]) {
                    // 下方单元格的 top
                    newAsset.attribute.cell[row + 1][col].lineColor[0] = color.color;
                }
                if(newAsset.attribute.cell?.[row]?.[col - 1]?.lineColor[1]) {
                    // 左方单元格的 right
                    newAsset.attribute.cell[row][col - 1].lineColor[1] = color.color;
                }
                if(newAsset.attribute.cell?.[row]?.[col + 1]?.lineColor[3]) {
                    // 右方单元格的 left
                    newAsset.attribute.cell[row][col + 1].lineColor[3] = color.color;
                }
            });

        } else {
            if(newAsset && newAsset.attribute && newAsset.attribute.cell) {
                for(let i = 0; i < newAsset.attribute.cell.length; i++) {
                    for(let s = 0; s < newAsset.attribute.cell[i].length; s++) {
                        for(let t = 0; t < newAsset.attribute.cell[i][s].lineColor.length; t++) {
                            newAsset.attribute.cell[i][s].lineColor[t].r = color.color.r;
                            newAsset.attribute.cell[i][s].lineColor[t].g = color.color.g;
                            newAsset.attribute.cell[i][s].lineColor[t].b = color.color.b;
                            newAsset.attribute.cell[i][s].lineColor[t].a = color.color.a;
                        }
                    }
                }
            }
        }
        assetManager.setPv_new(7129);
        AssetLogic.updateAssetTableStyle(
            {
                asset: newAsset,
                assetIndex: toolPanel.asset_index,
            }
        )
        canvasStore.dispatch(paintOnCanvas('UPDATE_TABLE_STYLE',Object.assign(newAsset,{autoSaveFlag: 2})));
    }

    inputOnBlur() {
        this.setState({borderDegreeShow: false})
    }
    inputOnChange(e) {
        const num = Number(e.target.value);
        if(num > 0 && num < 100) {
            this.setState({borderWeight: num});
        }
        this.changeBorderWeight(num);
    }
    changeBorderWeight(num) {
        const {toolPanel,newAsset,choosedTdKeys} = this.publicFunction();
        // 是否选择整个表格
        if(choosedTdKeys.length > 0) {
            choosedTdKeys.map(v => {
                let keysArr = v.split('_');
                const row = Number.parseInt(keysArr[0]);
                const col = Number.parseInt(keysArr[1]);
                for(let i = 0; i < newAsset.attribute.cell[row][col].lineWidth.length; i++) {
                    newAsset.attribute.cell[row][col].lineWidth[i] = num;
                }
                if(newAsset.attribute.cell?.[row - 1]?.[col]?.lineWidth[2]) {
                    // 上方单元格的 bottom
                    newAsset.attribute.cell[row - 1][col].lineWidth[2] = num;
                }
                if(newAsset.attribute.cell?.[row + 1]?.[col]?.lineWidth[0]) {
                    // 下方单元格的 top
                    newAsset.attribute.cell[row + 1][col].lineWidth[0] = num;
                }
                if(newAsset.attribute.cell?.[row]?.[col - 1]?.lineWidth[1]) {
                    // 左方单元格的 right
                    newAsset.attribute.cell[row][col - 1].lineWidth[1] = num;
                }
                if(newAsset.attribute.cell?.[row]?.[col + 1]?.lineWidth[3]) {
                    // 右方单元格的 left
                    newAsset.attribute.cell[row][col + 1].lineWidth[3] = num;
                }
            });
        } else {
            if(newAsset && newAsset.attribute && newAsset.attribute.cell) {
                for(let i = 0; i < newAsset.attribute.cell.length; i++) {
                    for(let s = 0; s < newAsset.attribute.cell[i].length; s++) {
                        for(let t = 0; t < newAsset.attribute.cell[i][s].lineWidth.length; t++) {
                            newAsset.attribute.cell[i][s].lineWidth[t] = num;
                        }
                    }
                }
            }
        }
        // assetManager.setPv_new(7131,{
        //     additional:{
        //         i0:key,
        //     }
        // });
        AssetLogic.updateAssetTableStyle(
            {
                asset: newAsset,
                assetIndex: toolPanel.asset_index,
            }
        )
        canvasStore.dispatch(paintOnCanvas('UPDATE_TABLE_STYLE',Object.assign(newAsset,{autoSaveFlag: 2})));
    }

    stopPropagation(e) {
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
    }

    render() {
        let {borderColorShow,borderTypeShow,borderDegreeShow,selectType,borderColor,borderStyle,borderWeight,isColorMore,isStyelMore,isWeightMore,disableBtns} = this.state;
        const scopeData = [
            {key: 1,img: 'wubiankuang1'},
            {key: 2,img: 'biankuang'},
            {key: 3,img: 'neibiankuang'},
            {key: 4,img: 'shuxian'},
            {key: 5,img: 'hengxian'},
            {key: 6,img: 'waibiankuang'},
            {key: 7,img: 'zuobiankuang'},
            {key: 8,img: 'youbiankuang'},
            {key: 9,img: 'shangbiankuang'},
            {key: 10,img: 'xiabiankuang'},
        ];
        const typeData = [
            {key: 1,type: 'solid'},
            {key: 2,type: 'dashed'},
            {key: 3,type: 'dotted'},
            {key: 4,type: 'none'},
        ]

        const style = isColorMore ? {background: 'linear-gradient(349deg, #FF0000 0%, #FF00B8 11%, #BD00FF 24%, #000AFF 35%, #00F0FF 49%, #00FF0A 61%, #FFE600 75%, #FF5C00 89%, #FF0000 100%)',} : {background: `rgba(${borderColor.r}, ${borderColor.g}, ${borderColor.b}, ${borderColor.a})`,};
        const {newAsset} = this.publicFunction();
        const selectKey = newAsset.meta?.rt_select_key || 0;
        return (
            <div
                className="tableBorderStylePanel"
                onClick={() => {
                    this.setState({borderColorShow: false,borderTypeShow: false,borderDegreeShow: false})
                }}
                onMouseDown={this.stopPropagation}
                style={this.props.style ? this.props.style : {}}
            >
                <div className='typeTitle'>边框</div>
                <div className='scopeBox'>
                    {scopeData.map((item,index) => {
                        // 不是选中全部禁用部分按钮
                        const disableDiv = disableBtns.includes(item.key);
                        const active = selectKey === item.key;
                        return (
                            <div
                                class={`${disableDiv ? 'disableDiv' : active ? 'active' : ''}`}
                                key={item.key}
                                onClick={disableDiv ? this.stopPropagation : this.scopeClickEvent.bind(this,item.key)}
                                onMouseDown={this.stopPropagation}
                            >
                                <img src={`https://s.tuguaishou.com/image/iconfont/${item.img}.png`} />
                            </div>
                        )
                    })}
                </div>
                <div className='typeTitle'>颜色</div>
                <div className='colorBox'>
                    <div
                        className='color'
                        onClick={this.colorClickEvent.bind(this)}
                        onMouseDown={this.stopPropagation}
                        style={style}
                    />
                    {borderColorShow && <MoreColorsPanel
                        color={{r: 1,g: 1,b: 1,a: 1}}
                        onChange={this.colorChangeEvent.bind(this)}
                        style={{
                            left: 3,
                            top: 186,
                            zIndex: 10,
                        }}
                    />}
                </div>
                <div className='typeTitle'>样式</div>
                <div className='typeDegreeBox'>
                    <div
                        className='type'
                        onClick={this.typeClickEvent.bind(this)}
                        onMouseDown={this.stopPropagation}
                    >
                        {isStyelMore ? <div className='text'>多个值</div> : borderStyle === 'none' ?
                            <div className='text'>无边框</div>
                            : <div className='line' style={{borderBottomStyle: borderStyle}}></div>
                        }
                        <i className="iconfont icon-xiala"></i>
                        {borderTypeShow && <div className='typeSelects'>
                            {isStyelMore &&
                                <div
                                    className='typeItem'
                                    onClick={() => {
                                        this.setState({borderTypeShow: false});
                                        this.stopPropagation();
                                    }}
                                >
                                    <div className='text'>多个值</div>
                                    <i className="iconfont icon-gou1"></i>
                                </div>
                            }
                            {typeData.map((item) => {
                                return (
                                    <div
                                        key={item.key}
                                        className='typeItem'
                                        onClick={this.selectTypeClickEvent.bind(this,item.type,item.key)}
                                    >
                                        {item.type === 'none' ?
                                            <div className='text'>无边框</div>
                                            : <div className='line' style={{borderBottomStyle: item.type}}></div>
                                        }
                                        {item.type === borderStyle && <i className="iconfont icon-gou1"></i>}
                                    </div>
                                )
                            })}
                        </div>}
                    </div>
                    {/* <div
                        className='degree'
                        onClick={this.degreeClickEvent.bind(this)}
                        onMouseDown={this.stopPropagation}
                    >
                        <i className="iconfont icon-xianduan"></i>
                        {borderDegreeShow ? 
                            <input type="number" min='1' max='10'
                                onBlur={this.inputOnBlur.bind(this)}
                                onChange={this.inputOnChange.bind(this)}
                                value={borderWeight}
                                placeholder='--'
                            />
                            :
                            <span>{borderWeight? borderWeight: '--'}</span>
                        }
                    </div> */}
                </div>
            </div>
        );
    }
}

class ToolTablePanel extends Component {

    // 节流函数实现
    throttle = (func, delay) => {
        let timeoutId;
        let lastExecTime = 0;
        return (...args) => {
            const currentTime = Date.now();
            
            if (currentTime - lastExecTime > delay) {
                func.apply(this, args);
                lastExecTime = currentTime;
            } else {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    func.apply(this, args);
                    lastExecTime = Date.now();
                }, delay - (currentTime - lastExecTime));
            }
        };
    };

    constructor(props) {
        super(props);
        let {toolPanel} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        this.showTableEditEmitter();
        this.state = {
            selectedTableId: 3,
            isShowPreviewTable: false,
            isShowPresetColor: false,
            selectedPreset: 1,
            selectedPresetColor: 1,
            selectedColor: [],
            colorTable2: [],
            colorTable1: [],
            transparencyValue: 0, // 0-100 透明度比例，0表示完全透明，100表示完全不透明
            selectedTableStyle: {
                "width": "76px",
                "height": "58px",
                "backgroundSize": "100%",
                "backgroundRepeat": "no-repeat",
                "backgroundPosition": "center"
            },
            tableData: [],
            presetColorTableList: [
                {
                    id: 3,
                    "color_table": [{color_table: [{r: 96,g: 103,b: 112,a: 1,id: 1},{r: 141,g: 151,b: 164,a: 1,id: 2},{r: 214,g: 218,b: 222,a: 1,id: 3}],id: 1},
                    {color_table: [{r: 95,g: 149,b: 145,a: 1,id: 1},{r: 128,g: 165,b: 162,a: 1,id: 2},{r: 168,g: 193,b: 186,a: 1,id: 3}],id: 2},
                    {color_table: [{r: 93,g: 88,b: 86,a: 1,id: 1},{r: 132,g: 117,b: 111,a: 1,id: 2},{r: 164,g: 163,b: 162,a: 1,id: 3}],id: 3},
                    {color_table: [{r: 95,g: 103,b: 149,a: 1,id: 1},{r: 112,g: 116,b: 150,a: 1,id: 2},{r: 144,g: 147,b: 170,a: 1,id: 3}],id: 4},
                    {color_table: [{r: 149,g: 108,b: 95,a: 1,id: 1},{r: 171,g: 150,b: 142,a: 1,id: 2},{r: 199,g: 192,b: 182,a: 1,id: 3}],id: 5},
                    ]
                },
                {
                    id: 4,
                    "color_table": [{color_table: [{r: 96,g: 103,b: 112,a: 1,id: 1},{r: 141,g: 151,b: 164,a: 1,id: 2},{r: 214,g: 218,b: 222,a: 1,id: 3}],id: 1},
                    {color_table: [{r: 95,g: 149,b: 145,a: 1,id: 1},{r: 128,g: 165,b: 162,a: 1,id: 2},{r: 168,g: 193,b: 186,a: 1,id: 3}],id: 2},
                    {color_table: [{r: 93,g: 88,b: 86,a: 1,id: 1},{r: 132,g: 117,b: 111,a: 1,id: 2},{r: 164,g: 163,b: 162,a: 1,id: 3}],id: 3},
                    {color_table: [{r: 95,g: 103,b: 149,a: 1,id: 1},{r: 112,g: 116,b: 150,a: 1,id: 2},{r: 144,g: 147,b: 170,a: 1,id: 3}],id: 4},
                    {color_table: [{r: 149,g: 108,b: 95,a: 1,id: 1},{r: 171,g: 150,b: 142,a: 1,id: 2},{r: 199,g: 192,b: 182,a: 1,id: 3}],id: 5},
                    ]
                },
                {
                    id: 5,
                    "color_table": [{color_table: [{r: 96,g: 103,b: 112,a: 1,id: 1},{r: 141,g: 151,b: 164,a: 1,id: 2},{r: 214,g: 218,b: 222,a: 1,id: 3}],id: 1},
                    {color_table: [{r: 95,g: 149,b: 145,a: 1,id: 1},{r: 128,g: 165,b: 162,a: 1,id: 2},{r: 168,g: 193,b: 186,a: 1,id: 3}],id: 2},
                    {color_table: [{r: 93,g: 88,b: 86,a: 1,id: 1},{r: 132,g: 117,b: 111,a: 1,id: 2},{r: 164,g: 163,b: 162,a: 1,id: 3}],id: 3},
                    {color_table: [{r: 95,g: 103,b: 149,a: 1,id: 1},{r: 112,g: 116,b: 150,a: 1,id: 2},{r: 144,g: 147,b: 170,a: 1,id: 3}],id: 4},
                    {color_table: [{r: 149,g: 108,b: 95,a: 1,id: 1},{r: 171,g: 150,b: 142,a: 1,id: 2},{r: 199,g: 192,b: 182,a: 1,id: 3}],id: 5},
                    ]
                },
                {
                    id: 6,
                    "color_table": [{color_table: [{r: 96,g: 103,b: 112,a: 1,id: 1},{r: 141,g: 151,b: 164,a: 1,id: 2},{r: 214,g: 218,b: 222,a: 1,id: 3}],id: 1},
                    {color_table: [{r: 95,g: 149,b: 145,a: 1,id: 1},{r: 128,g: 165,b: 162,a: 1,id: 2},{r: 168,g: 193,b: 186,a: 1,id: 3}],id: 2},
                    {color_table: [{r: 93,g: 88,b: 86,a: 1,id: 1},{r: 132,g: 117,b: 111,a: 1,id: 2},{r: 164,g: 163,b: 162,a: 1,id: 3}],id: 3},
                    {color_table: [{r: 95,g: 103,b: 149,a: 1,id: 1},{r: 112,g: 116,b: 150,a: 1,id: 2},{r: 144,g: 147,b: 170,a: 1,id: 3}],id: 4},
                    {color_table: [{r: 149,g: 108,b: 95,a: 1,id: 1},{r: 171,g: 150,b: 142,a: 1,id: 2},{r: 199,g: 192,b: 182,a: 1,id: 3}],id: 5},
                    ]
                },
                {
                    id: 7,
                    "color_table": [{color_table: [{r: 96,g: 103,b: 112,a: 1,id: 1},{r: 141,g: 151,b: 164,a: 1,id: 2},{r: 214,g: 218,b: 222,a: 1,id: 3}],id: 1},
                    {color_table: [{r: 95,g: 149,b: 145,a: 1,id: 1},{r: 128,g: 165,b: 162,a: 1,id: 2},{r: 168,g: 193,b: 186,a: 1,id: 3}],id: 2},
                    {color_table: [{r: 93,g: 88,b: 86,a: 1,id: 1},{r: 132,g: 117,b: 111,a: 1,id: 2},{r: 164,g: 163,b: 162,a: 1,id: 3}],id: 3},
                    {color_table: [{r: 95,g: 103,b: 149,a: 1,id: 1},{r: 112,g: 116,b: 150,a: 1,id: 2},{r: 144,g: 147,b: 170,a: 1,id: 3}],id: 4},
                    {color_table: [{r: 149,g: 108,b: 95,a: 1,id: 1},{r: 171,g: 150,b: 142,a: 1,id: 2},{r: 199,g: 192,b: 182,a: 1,id: 3}],id: 5},
                    ]
                },
                {
                    id: 8,
                    "color_table": [{color_table: [{r: 96,g: 103,b: 112,a: 1,id: 1},{r: 141,g: 151,b: 164,a: 1,id: 2},{r: 214,g: 218,b: 222,a: 1,id: 3}],id: 1},
                    {color_table: [{r: 95,g: 149,b: 145,a: 1,id: 1},{r: 128,g: 165,b: 162,a: 1,id: 2},{r: 168,g: 193,b: 186,a: 1,id: 3}],id: 2},
                    {color_table: [{r: 93,g: 88,b: 86,a: 1,id: 1},{r: 132,g: 117,b: 111,a: 1,id: 2},{r: 164,g: 163,b: 162,a: 1,id: 3}],id: 3},
                    {color_table: [{r: 95,g: 103,b: 149,a: 1,id: 1},{r: 112,g: 116,b: 150,a: 1,id: 2},{r: 144,g: 147,b: 170,a: 1,id: 3}],id: 4},
                    {color_table: [{r: 149,g: 108,b: 95,a: 1,id: 1},{r: 171,g: 150,b: 142,a: 1,id: 2},{r: 199,g: 192,b: 182,a: 1,id: 3}],id: 5},
                    ]
                },
                {
                    id: 9,
                    "color_table": [{color_table: [{r: 96,g: 103,b: 112,a: 1,id: 1},{r: 141,g: 151,b: 164,a: 1,id: 2},{r: 214,g: 218,b: 222,a: 1,id: 3}],id: 1},
                    {color_table: [{r: 95,g: 149,b: 145,a: 1,id: 1},{r: 128,g: 165,b: 162,a: 1,id: 2},{r: 168,g: 193,b: 186,a: 1,id: 3}],id: 2},
                    {color_table: [{r: 93,g: 88,b: 86,a: 1,id: 1},{r: 132,g: 117,b: 111,a: 1,id: 2},{r: 164,g: 163,b: 162,a: 1,id: 3}],id: 3},
                    {color_table: [{r: 95,g: 103,b: 149,a: 1,id: 1},{r: 112,g: 116,b: 150,a: 1,id: 2},{r: 144,g: 147,b: 170,a: 1,id: 3}],id: 4},
                    {color_table: [{r: 149,g: 108,b: 95,a: 1,id: 1},{r: 171,g: 150,b: 142,a: 1,id: 2},{r: 199,g: 192,b: 182,a: 1,id: 3}],id: 5},
                    ]
                },
            ],
            isEffectFollow: toolPanel.asset.attribute?.ks && toolPanel.asset.attribute?.ks?.i?.kw?.ks?.c?.length > 0 ? true : false
        }
        
        // 创建节流版本的透明度控制方法
        this.throttledTransparentControl = this.throttle(this.transparentControlClickEventOriginal, 150);
    }


    componentDidMount() {
        const {toolPanel: {asset}} = canvasStore.getState().onCanvasPainted;
        const {presetColorTableList,selectedPreset,selectedTableStyle,tableData} = this.state;


        let tableId = asset.attribute.resId;

        if(!tableId) {
            tableId = 3;
        }

        this.setState({
            selectedTableId: tableId
        })

        let newColorTable2 = [];
        let newColorTable1 = [];

        for(let t = 0; t < presetColorTableList.length; t++) {
            if(presetColorTableList[t].id == tableId) {
                newColorTable2 = presetColorTableList[t].color_table;
                for(let i = 0; i < newColorTable2.length; i++) {
                    if(newColorTable2[i].id == selectedPreset) {
                        newColorTable1 = newColorTable2[i].color_table;
                    }
                }
            }
        }
        let transparencyValue = 0; // 初始透明度值
        if(asset && asset.attribute && asset.attribute.cell && asset.attribute.text) {
            // 计算整体透明度（背景、文字、边框的平均值）
            let totalAlpha = 0;
            let elementCount = 0;
            
            // 计算背景透明度
            for(let i = 0; i < asset.attribute.cell.length; i++) {
                for(let s = 0; s < asset.attribute.cell[i].length; s++) {
                    totalAlpha += asset.attribute.cell[i][s].background.color.a;
                    elementCount++;
                    
                    // 计算边框透明度（取第一条边框作为代表）
                    if (asset.attribute.cell[i][s].lineColor && asset.attribute.cell[i][s].lineColor.length > 0) {
                        totalAlpha += asset.attribute.cell[i][s].lineColor[0].a;
                        elementCount++;
                    }
                }
            }
            
            // 计算文字透明度
            for(let i = 0; i < asset.attribute.text.length; i++) {
                for(let s = 0; s < asset.attribute.text[i].length; s++) {
                    if (asset.attribute.text[i][s].color) {
                        totalAlpha += asset.attribute.text[i][s].color.a;
                        elementCount++;
                    }
                }
            }
            
            if(elementCount > 0) {
                // 将平均alpha值 (0-1) 转换为透明度百分比 (0-100)
                const averageAlpha = totalAlpha / elementCount;
                transparencyValue = Math.round(averageAlpha * 100);
            }
        }


        const th = this;
        assetManager.getPPTTables().then(data => {
            data.json().then(resultData => {
                if(resultData.stat == 1) {
                    let newSelectedTableStyle = Object.assign({},selectedTableStyle);
                    for(let i = 0; i < resultData.data.length; i++) {
                        if(resultData.data[i].id == tableId) {
                            newSelectedTableStyle.backgroundImage = `url(${imgHost}${resultData.data[i].preview})`;
                        }
                    }

                    th.setState({
                        tableData: resultData.data,
                        selectedTableStyle: newSelectedTableStyle
                    });
                }
            });
        });

        this.setState({transparencyValue,selectedTableId: tableId,colorTable1: newColorTable1,colorTable2: newColorTable2});

        this.floatFuncListener = emitter.addListener("openFuncFloat", (type, item) => {
            if (type === "table-color") {
                this.setState({
                    isShowPresetColor: !this.state.isShowPresetColor,
                    showColorSetIndex: this.state.showColorSetIndex === 2 ? -1 : 2
                });

                RightPanel.updateRightDialogFlag({rightDialog: !this.state.isShowPresetColor});

                if(this.addPresetColorEventListenerListener) {
                    this.addPresetColorEventListenerListener.remove();
                }
        
                let th = this;
                this.addPresetColorEventListenerListener = addEventListener(window,'click',() => {
                    th.setState({
                        isShowPresetColor: false,
                    });
                    RightPanel.updateRightDialogFlag({rightDialog: false});
                    emitter.emit("floatMenu.hideFloat", true);
                    th.addPresetColorEventListenerListener.remove();
                });
            }
        });
    }


    onSelectPreviewTableClickEvent(v) {
        const {presetColorTableList,selectedPreset,selectedTableStyle,tableData,transparencyValue} = this.state;
        const tableId = Number(v.id);

        assetManager.setPv_new(3214,{
            additional: {
                s0: tableId
            }
        })

        let th = this;

        assetManager.getPPTTableDetail(tableId).then(data => {
            data.json().then(resultData => {
                if(resultData.stat != 1) {
                    alert('服务器错误，请联系客服：1994432176')
                    return false
                }
                const item = resultData.data;

                let replacedAttribute = item.attribute;

                // const {toolPanel:{asset} }= canvasStore.getState().onCanvasPainted;
                const {toolPanel,work,pageInfo} = storeAdapter.getStore({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                });

                if(typeof toolPanel.asset_index != 'number' || toolPanel.asset_index < 0) {
                    return;
                }

                const asset = AssetHelper.find(work,pageInfo,{index: toolPanel.asset_index});
                const newAsset = cloneDeep(asset);

                newAsset.transform = item.transform;

                // 启用本地配置
                const attributesAsset = JSON.parse(JSON.stringify(attributes));
                for(let i = 0; i < attributesAsset.length; i++) {
                    if(attributesAsset[i].id == tableId) {
                        replacedAttribute = attributesAsset[i].attribute;
                        break;
                    }
                }
                const lastCellRow = replacedAttribute.cell[replacedAttribute.cell.length - 1];
                const lastCellCol = replacedAttribute.cell.map(v => v[v.length - 1]);
                const lastCellStyle = replacedAttribute.cell[replacedAttribute.cell.length - 1][replacedAttribute.cell[replacedAttribute.cell.length - 1].length - 1];
                const cell = JSON.parse(JSON.stringify(replacedAttribute.cell));
                let firstRowCell = cell && cell[0][0];
                let secondRowCell = cell && cell[1][0];

                let arrCell = [];
                if(tableId == 3 || tableId == 8) {
                    arrCell = JSON.parse(JSON.stringify(newAsset.attribute.cell));
                    for(let i = 0; i < arrCell.length; i++) {
                        for(let t = 0; t < arrCell[i].length; t++) {
                            // 特殊处理表格id 为3的和8的
                            if(i % 2 == 0) {
                                arrCell[i][t].background = firstRowCell.background;
                                arrCell[i][t].lineColor = firstRowCell.lineColor;
                                arrCell[i][t].lineStyle = firstRowCell.lineStyle;
                                arrCell[i][t].lineWidth = firstRowCell.lineWidth;
                            } else {
                                arrCell[i][t].background = secondRowCell.background;
                                arrCell[i][t].lineColor = secondRowCell.lineColor;
                                arrCell[i][t].lineStyle = secondRowCell.lineStyle;
                                arrCell[i][t].lineWidth = secondRowCell.lineWidth;
                            }
                        }
                    }
                } else {
                    arrCell = JSON.parse(JSON.stringify(newAsset.attribute.cell));
                    for(let i = 0; i < arrCell.length; i++) {
                        for(let t = 0; t < arrCell[i].length; t++) {
                            if(cell[i] && cell[i][t]) {
                                arrCell[i][t].background = cell[i][t].background;
                                arrCell[i][t].lineColor = cell[i][t].lineColor;
                                arrCell[i][t].lineStyle = cell[i][t].lineStyle;
                                arrCell[i][t].lineWidth = cell[i][t].lineWidth;
                            } else {
                                if (tableId === 4 || tableId === 7) {
                                    let cellstyle = null;
                                    let v = 0
                                    if(!cell[i]) {
                                        v = t;
                                        cellstyle = lastCellRow[v]
                                    } else if(!cell[i][t]) {
                                        v = i;
                                        cellstyle = lastCellCol[v]
                                    }
                                    if(!cellstyle) {
                                        cellstyle = lastCellStyle
                                    }
                                    console.log(i,t,cellstyle)
                                    arrCell[i][t].background = cellstyle.background;
                                    arrCell[i][t].lineColor = cellstyle.lineColor;
                                    arrCell[i][t].lineStyle = cellstyle.lineStyle;
                                    arrCell[i][t].lineWidth = cellstyle.lineWidth;
                                } else {
                                    arrCell[i][t].background = lastCellStyle.background;
                                    arrCell[i][t].lineColor = lastCellStyle.lineColor;
                                    arrCell[i][t].lineStyle = lastCellStyle.lineStyle;
                                    arrCell[i][t].lineWidth = lastCellStyle.lineWidth;
                                }
                            }
                        }
                    }
                }

                newAsset.attribute.width = asset && asset.attribute && asset.attribute.width;
                newAsset.attribute.height = asset && asset.attribute && asset.attribute.height;
                newAsset.transform.posX = asset && asset.transform && asset.transform.posX;
                newAsset.transform.posY = asset && asset.transform && asset.transform.posY;
                newAsset.attribute.text = asset && asset.transform && asset.attribute.text;
                newAsset.attribute.cell = arrCell;
                newAsset.attribute.resId = tableId;

                let newTransparencyValue = transparencyValue;
                if(newAsset && newAsset.attribute && newAsset.attribute.cell && newAsset.attribute.text) {
                    // 计算新表格样式的整体透明度
                    let totalAlpha = 0;
                    let elementCount = 0;
                    
                    // 计算背景透明度
                    for(let i = 0; i < newAsset.attribute.cell.length; i++) {
                        for(let s = 0; s < newAsset.attribute.cell[i].length; s++) {
                            totalAlpha += newAsset.attribute.cell[i][s].background.color.a;
                            elementCount++;
                            
                            // 计算边框透明度（取第一条边框作为代表）
                            if (newAsset.attribute.cell[i][s].lineColor && newAsset.attribute.cell[i][s].lineColor.length > 0) {
                                totalAlpha += newAsset.attribute.cell[i][s].lineColor[0].a;
                                elementCount++;
                            }
                        }
                    }
                    
                    // 计算文字透明度
                    for(let i = 0; i < newAsset.attribute.text.length; i++) {
                        for(let s = 0; s < newAsset.attribute.text[i].length; s++) {
                            if (newAsset.attribute.text[i][s].color) {
                                totalAlpha += newAsset.attribute.text[i][s].color.a;
                                elementCount++;
                            }
                        }
                    }
                    
                    if(elementCount > 0) {
                        // 将平均alpha值 (0-1) 转换为透明度百分比 (0-100)
                        const averageAlpha = totalAlpha / elementCount;
                        newTransparencyValue = Math.round(averageAlpha * 100);
                    }
                }

                th.setState({
                    transparencyValue: newTransparencyValue
                })

                AssetLogic.updateAssetTableStyle(
                    {
                        asset: newAsset,
                        assetIndex: toolPanel.asset_index,
                    }
                )

                // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLE_STYLE', Object.assign(newAsset)));
            });
        });


        let newColorTable2 = [];
        let newColorTable1 = [];

        for(let t = 0; t < presetColorTableList.length; t++) {
            if(presetColorTableList[t].id === tableId) {
                newColorTable2 = presetColorTableList[t].color_table;
                for(let i = 0; i < newColorTable2.length; i++) {
                    if(newColorTable2[i].id === selectedPreset) {
                        newColorTable1 = newColorTable2[i].color_table;
                    }
                }
            }
        }

        let newSelectedTableStyle = Object.assign({},selectedTableStyle);
        for(let i = 0; i < tableData.length; i++) {
            if(tableData[i].id == tableId) {
                newSelectedTableStyle.backgroundImage = `url(${imgHost}${tableData[i].preview})`;
                break;
            }
        }
        this.setState({selectedTableStyle: newSelectedTableStyle,colorTable1: newColorTable1,colorTable2: newColorTable2,selectedTableId: tableId});
    }


    listItemClickEvent(asset,index,e) {

        const assetData = {
            id: asset.id,
            width: asset.width,
            height: asset.height,
            meta: {},
            attribute: {},
            transform: {}
        }

        emitter.emit("ListAddTable",assetData);

        this.dropDownBoxShowListener && this.dropDownBoxShowListener.remove()
        this.setState({
            dropDownBoxShow: false
        })


    }



    onPreviewTableShowClickEvent(e) {
        this.setState({
            isShowPreviewTable: !this.state.isShowPreviewTable
        })
        RightPanel.updateRightDialogFlag({rightDialog: !this.state.isShowPreviewTable})
        assetManager.setPv_new(3213,{
            additional: {}
        })

        if(this.addEventListenerListener) {
            this.addEventListenerListener.remove();
        }

        let th = this;
        this.addEventListenerListener = addEventListener(window,'click',() => {
            th.setState({
                isShowPreviewTable: false,
            });
            RightPanel.updateRightDialogFlag({rightDialog: false});

            th.addEventListenerListener.remove();
        });

        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    colorBtnItemBlockClickEvent(index,id,e) {
        this.setState({
            showColorSetIndex: index === this.state.showColorSetIndex ? -1 : index,
            selectedPresetColor: id
        });

        if(this.state.showColorSetIndex === index) {
            RightPanel.updateRightDialogFlag({rightDialog: false});
        } else {
            RightPanel.updateRightDialogFlag({rightDialog: true});
        }
        assetManager.setPv_new(3217,{
            additional: {
                s0: id
            }
        })


        let th = this;
        if(this.choiceColorBtnClickListener) {
            this.choiceColorBtnClickListener.remove();
        }

        this.choiceColorBtnClickListener = addEventListener(window,'mousedown',(e) => {
            // if (this.refs.colorSets && this.refs.colorSets.contains(e.target)) {
            //     return;
            // }
            th.choiceColorBtnClickListener.remove();
            th.setState({
                showColorSetIndex: -1
            });
            RightPanel.updateRightDialogFlag({rightDialog: false});
        });
    }


    /**
    * 颜色修改
    */
    colorEvent2(index,color) {
        console.log('colorEvent2', index, color);
        
        const {colorTable1} = this.state;
        const newColorTable1 = Object.assign([],colorTable1);
        color.opacityBg = false
        if(color.color.a == 0) {
            color.opacityBg = true
        }
        newColorTable1[index].r = color.color.r;
        newColorTable1[index].g = color.color.g;
        newColorTable1[index].b = color.color.b;
        newColorTable1[index].a = color.color.a;
        this.setState({
            colorTable1: newColorTable1
        });
        const {toolPanel,work,pageInfo} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if(typeof toolPanel.asset_index != 'number' || toolPanel.asset_index < 0) {
            return;
        }

        const asset = AssetHelper.find(work,pageInfo,{index: toolPanel.asset_index});
        const newAsset = cloneDeep(asset);
        const {choosedTdKeys = []} = asset;

        if(choosedTdKeys.length > 0) {
            if(index == 0) {
                choosedTdKeys.map(v => {
                    let keysArr = v.split('_');
                    newAsset.attribute.text[keysArr[0]][keysArr[1]].color = color.color;
                });
            } else if(index == 1) {
                choosedTdKeys.map(v => {
                    let keysArr = v.split('_');
                    const row = Number.parseInt(keysArr[0]);
                    const col = Number.parseInt(keysArr[1]);
                    for(let i = 0; i < newAsset.attribute.cell[row][col].lineColor.length; i++) {
                        newAsset.attribute.cell[row][col].lineColor[i] = color.color;
                    }
                    if(newAsset.attribute.cell?.[row - 1]?.[col]?.lineColor[2]) {
                        // 上方单元格的 bottom
                        newAsset.attribute.cell[row - 1][col].lineColor[2] = color.color;
                    }
                    if(newAsset.attribute.cell?.[row + 1]?.[col]?.lineColor[0]) {
                        // 下方单元格的 top
                        newAsset.attribute.cell[row + 1][col].lineColor[0] = color.color;
                    }
                    if(newAsset.attribute.cell?.[row]?.[col - 1]?.lineColor[1]) {
                        // 左方单元格的 right
                        newAsset.attribute.cell[row][col - 1].lineColor[1] = color.color;
                    }
                    if(newAsset.attribute.cell?.[row]?.[col + 1]?.lineColor[3]) {
                        // 右方单元格的 left
                        newAsset.attribute.cell[row][col + 1].lineColor[3] = color.color;
                    }
                });
            } else {
                choosedTdKeys.map(v => {
                    let keysArr = v.split('_');
                    newAsset.attribute.cell[keysArr[0]][keysArr[1]].background = color;
                });
            }

        } else {
            if(index === 0 && newAsset && newAsset.attribute && newAsset.attribute.text) {
                for(let i = 0; i < newAsset.attribute.text.length; i++) {
                    for(let s = 0; s < newAsset.attribute.text[i].length; s++) {
                        newAsset.attribute.text[i][s].color = color.color;
                    }
                }
            }


            if(index === 1 && newAsset && newAsset.attribute && newAsset.attribute.cell) {
                for(let i = 0; i < newAsset.attribute.cell.length; i++) {
                    for(let s = 0; s < newAsset.attribute.cell[i].length; s++) {
                        for(let t = 0; t < newAsset.attribute.cell[i][s].lineColor.length; t++) {
                            newAsset.attribute.cell[i][s].lineColor[t].r = color.color.r;
                            newAsset.attribute.cell[i][s].lineColor[t].g = color.color.g;
                            newAsset.attribute.cell[i][s].lineColor[t].b = color.color.b;
                            newAsset.attribute.cell[i][s].lineColor[t].a = color.color.a;
                        }
                    }
                }
            }

            if(index === 2 && newAsset && newAsset.attribute && newAsset.attribute.cell) {
                for(let i = 0; i < newAsset.attribute.cell.length; i++) {
                    for(let s = 0; s < newAsset.attribute.cell[i].length; s++) {
                        newAsset.attribute.cell[i][s].background.color = color.color;
                        newAsset.attribute.cell[i][s].background.opacityBg = color.opacityBg;
                    }
                }
            }
        }
        AssetLogic.updateAssetTableStyle(
            {
                asset: newAsset,
                assetIndex: toolPanel.asset_index,
            }
        )

        // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLE_STYLE', Object.assign(newAsset, {autoSaveFlag: 2})));
    }


    onColorPanelClickEvent(e) {
        this.setState({
            isShowPresetColor: !this.state.isShowPresetColor,
            showColorSetIndex: -1
        })
        RightPanel.updateRightDialogFlag({rightDialog: !this.state.isShowPresetColor});

        assetManager.setPv_new(3215,{
            additional: {}
        })

        if(this.addPresetColorEventListenerListener) {
            this.addPresetColorEventListenerListener.remove();
        }

        let th = this;
        this.addPresetColorEventListenerListener = addEventListener(window,'click',() => {
            th.setState({
                isShowPresetColor: false,
            });
            RightPanel.updateRightDialogFlag({rightDialog: false});
            th.addPresetColorEventListenerListener.remove();
        });

        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }



    showTableEditEmitter() {
        this.showTableEditEmitter = emitter.addListener("showTableEdit",(e) => {
            // let {toolPanel} = canvasStore.getState().onCanvasPainted;
            // const text = toolPanel.asset.attribute.text;
            // let arr1=[];
            // for(let i=0;i<text.length;i++){
            //     let arr2 = [];
            //     let childText = JSON.parse(JSON.stringify(text[i]));
            //     childText = Object.values(childText)
            //     for(let t=0;t<childText.length;t++){
            //         arr2.push(childText[t].content[0]);
            //     }
            //     arr1.push(arr2);
            // }

            // const tableAsset = {
            //     attribute:{
            //         userData:arr1
            //     }
            // }

            let windowInfo = {
                windowContent: <TableEditDataModal />,
                popupWidth: 816,
                popupHeight: 520,
                style: {
                    padding: 0,
                    borderRadius: '4px',
                    left: "50%",
                    top: "50%",
                    marginLeft: "-554px",
                    marginTop: "-274px",
                },
                popupTitleBarStyle: {
                    height: 0
                },
                popupBodyStyle: {
                    padding: 0
                }
            };

            emitter.emit('popupWindow',windowInfo);
        })
    }


    onClickEditTableClickEvent() {
        assetManager.setPv_new(3219,{
            additional: {
                s0: "outer"
            }
        })
        emitter.emit("showTableEdit")
        // let {toolPanel} = canvasStore.getState().onCanvasPainted;
        // const text = toolPanel.asset.attribute.text;

        // let arr1=[];
        // for(let i=0;i<text.length;i++){
        //     let arr2 = [];
        //     for(let t=0;t<text[i].length;t++){
        //         arr2.push(text[i][t].content[0]);
        //     }
        //     arr1.push(arr2);
        // }

        // const tableAsset = {
        //     attribute:{
        //         userData:arr1
        //     }
        // }

        // let windowInfo = {
        //     windowContent: <TableEditDataModal tableAsset={tableAsset}/>,
        //     popupWidth: 816,
        //     popupHeight: 520,
        //     style: {
        //         padding: 0,
        //         borderRadius: '4px',
        //         left: "50%",
        //         top: "50%",
        //         marginLeft: "-554px",
        //         marginTop: "-274px",
        //     },
        //     popupTitleBarStyle: {
        //         height: 0
        //     },
        //     popupBodyStyle: {
        //         padding: 0
        //     }
        // };

        // emitter.emit('popupWindow', windowInfo);

    }


    onSelectPresetColorClickEvent(id) {
        const th = this;

        assetManager.setPv_new(3216,{
            additional: {
                s0: id
            }
        })
        this.addEventListenerListener = addEventListener(window,'click',() => {
            th.setState({
                isShowPresetColor: false,
            });

            th.addEventListenerListener.remove();
        });

        const {toolPanel,work,pageInfo} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if(typeof toolPanel.asset_index != 'number' || toolPanel.asset_index < 0) {
            return;
        }

        const asset = AssetHelper.find(work,pageInfo,{index: toolPanel.asset_index});

        let newColorTable2 = [];
        let newColorTable1 = [];
        const {presetColorTableList,selectedTableId} = this.state;
        // const {toolPanel:{asset} }= canvasStore.getState().onCanvasPainted;
        const newAsset = cloneDeep(asset);

        for(let t = 0; t < presetColorTableList.length; t++) {
            if(presetColorTableList[t].id == selectedTableId) {
                newColorTable2 = presetColorTableList[t].color_table;
                for(let i = 0; i < newColorTable2.length; i++) {
                    if(newColorTable2[i].id === id) {
                        newColorTable1 = newColorTable2[i].color_table;
                    }
                }
            }
        }

        for(let i = 0; i < newAsset.attribute.text.length; i++) {
            for(let s = 0; s < newAsset.attribute.text[i].length; s++) {
                newAsset.attribute.text[i][s].color = newColorTable1[0];
            }
        }

        for(let i = 0; i < newAsset.attribute.cell.length; i++) {
            for(let s = 0; s < newAsset.attribute.cell[i].length; s++) {
                for(let t = 0; t < newAsset.attribute.cell[i][s].lineColor.length; t++) {
                    newAsset.attribute.cell[i][s].lineColor[t].r = newColorTable1[1].r;
                    newAsset.attribute.cell[i][s].lineColor[t].g = newColorTable1[1].g;
                    newAsset.attribute.cell[i][s].lineColor[t].b = newColorTable1[1].b;
                    newAsset.attribute.cell[i][s].lineColor[t].a = newColorTable1[1].a;
                }
            }
        }

        for(let i = 0; i < newAsset.attribute.cell.length; i++) {
            for(let s = 0; s < newAsset.attribute.cell[i].length; s++) {
                newAsset.attribute.cell[i][s].background.color = newColorTable1[2];
            }
        }

        AssetLogic.updateAssetTableStyle(
            {
                asset: newAsset,
                assetIndex: toolPanel.asset_index,
            }
        )

        // canvasStore.dispatch(paintOnCanvas('UPDATE_TABLE_STYLE', Object.assign(newAsset, {autoSaveFlag: 2})));

        this.setState({colorTable1: newColorTable1,colorTable2: newColorTable2,selectedPreset: id});

    }

    transparentControlClickEventOriginal(value) {
        // value 是从 Slider 传入的 0-100 的数值
        const transparencyValue = typeof value === 'number' ? value : (typeof value === 'object' && value.target ? parseInt(value.target.value) : 0);

        const {toolPanel,work,pageInfo} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if(typeof toolPanel.asset_index != 'number' || toolPanel.asset_index < 0) {
            return;
        }

        const asset = AssetHelper.find(work,pageInfo,{index: toolPanel.asset_index});

        const newAsset = cloneDeep(asset);
        let cloneCell = JSON.parse(JSON.stringify(newAsset.attribute.cell));
        let cloneText = JSON.parse(JSON.stringify(newAsset.attribute.text));

        if(newAsset && newAsset.attribute && newAsset.attribute.cell) {

            cloneCell = Object.values(cloneCell);
            cloneText = Object.values(cloneText);

            cloneCell = cloneCell.map((item) => {
                return Object.values(item);
            })
            cloneText = cloneText.map((item) => {
                return Object.values(item);
            })
            
            // 将 0-100 的透明度值转换为 alpha 值 (0-1)
            const alpha = transparencyValue / 100;
            
            // 设置背景透明度
            for(let i = 0; i < cloneCell.length; i++) {
                for(let s = 0; s < cloneCell[i].length; s++) {
                    cloneCell[i][s].background.color.a = alpha;
                    cloneCell[i][s].background.opacityBg = alpha === 0; // 完全透明时设置为true
                    
                    // 设置边框透明度
                    if (cloneCell[i][s].lineColor) {
                        for(let t = 0; t < cloneCell[i][s].lineColor.length; t++) {
                            cloneCell[i][s].lineColor[t].a = alpha;
                        }
                    }
                }
            }
            
            // 设置文字透明度
            for(let i = 0; i < cloneText.length; i++) {
                for(let s = 0; s < cloneText[i].length; s++) {
                    if (cloneText[i][s].color) {
                        cloneText[i][s].color.a = alpha;
                    }
                }
            }

            newAsset.attribute.cell = cloneCell;
            newAsset.attribute.text = cloneText;
        }

        assetManager.setPv_new(3218,{
            additional: {}
        })
        AssetLogic.updateAssetTableStyle(
            {
                asset: newAsset,
                assetIndex: toolPanel.asset_index,
            }
        )

        // 阻止事件传播（如果是事件对象）
        if(typeof value === 'object' && value.stopPropagation) {
            value.stopPropagation();
            value.nativeEvent && value.nativeEvent.stopPropagation();
        }
    }

    // 对外暴露的透明度控制方法（带节流）
    transparentControlClickEvent = (value) => {
        // 立即更新UI状态，保证滑块响应流畅
        const transparencyValue = typeof value === 'number' ? value : (typeof value === 'object' && value.target ? parseInt(value.target.value) : 0);
        this.setState({
            transparencyValue
        });
        
        // 节流处理实际的业务逻辑（API调用和样式更新）
        this.throttledTransparentControl(value);
    }




    render() {
        /*编辑器判断*/
        let {isDesigner,toolPanel} = canvasStore.getState().onCanvasPainted;
        const {asset} = toolPanel;
        const {choosedTdKeys = []} = asset;
        const {fontCount} = this.props;
        const {showColorSetIndex,selectedTableId,isShowPresetColor,isShowPreviewTable,colorTable1,colorTable2,selectedTableStyle,transparencyValue,tableData,isEffectFollow} = this.state;
        const isInnerGroup = toolPanel.asset?.meta.group
        return (
            <div className="textEditorBox tableEditorBox" >
                {/* {
                    !isDesigner&&(
                      <div className="company_use" style={{position:'absolute'}}>
                          <i className="iconfont icon-shang" style={{marginRight:"6px"}}></i>
                          <span>商用会员含{fontCount}套正版字体商用授权</span>
                      </div>
                    )
                } */}
                {isInnerGroup && <GroupAssetEditBox style={{marginBottom: '20px',paddingBottom: '15px',borderBottom: '1px solid #E9E8E8'}} />}
                <div className="tableDescStyle" style={{paddingTop: '10px'}}>表格样式</div>
                <div className="previewTable" onClick={this.onPreviewTableShowClickEvent.bind(this)} style={{marginTop: '30px'}}>
                    <div className="currentTableStyle">
                        <div style={selectedTableStyle}></div>
                    </div>
                    <div className="icon"><i onClick={this.onPreviewTableShowClickEvent.bind(this)} className="iconfont icon-xiala"></i></div>
                </div>

                {
                    isShowPreviewTable && (<div className="previewTableList">
                        <div className="previewTableContent">
                            {tableData.map((v,i,arr) => {
                                let style = {
                                    backgroundImage: `url(${imgHost}${v.preview})`,
                                    width: "76px",
                                    height: "58px",
                                    backgroundSize: "100%",
                                    backgroundRepeat: "no-repeat",
                                    backgroundPosition: "center"
                                }
                                // 第九个表格暂时先不展示
                                if(v.id == 9) {
                                    return null
                                }
                                return (
                                    <div className={selectedTableId == v.id ? "list-item selectedPreviewTable" : "list-item"} onClick={this.onSelectPreviewTableClickEvent.bind(this,v)}>
                                        <div style={style}></div>
                                    </div>
                                )
                            })}
                        </div>
                    </div>)
                }

                <div className="toolColorPanel">
                    <div className="typesettingBlock" style={{width: '100%'}} ref="colorSets">
                        <div className="typeBtnBan" style={{paddingLeft: "0px",paddingRight: "0px",margin: '12px 0',zIndex: 1}}>
                            {colorTable1.map((item,index) => {
                                const MoreColorsPanelStyle = {
                                    left: index == 0 ? 0 : '-60px',
                                    top: 50,
                                    zIndex: 9,
                                }
                                if(index == 0) {
                                    return (<>
                                        <div
                                            aria-label="字体色"
                                            className={"typeBtnBef typeBtn typeBtnBefb"}
                                            onClick={this.colorBtnItemBlockClickEvent.bind(this,index,item.id)}
                                            onMouseDown={preventDefault}
                                        >
                                            {showColorSetIndex == index &&
                                                <MoreColorsPanel
                                                    color={item}
                                                    onChange={this.colorEvent2.bind(this,index)}
                                                    style={MoreColorsPanelStyle}
                                                />
                                            }
                                            <i className="iconfont icon-wenzi2"></i>
                                        </div>
                                        <div className="typeBtnBanpipe"></div>
                                    </>)
                                } else if(index == 2) {
                                    return (<>
                                        <div
                                            aria-label="填充色"
                                            className={"typeBtnBef typeBtn typeBtnBefb"}
                                            onClick={this.colorBtnItemBlockClickEvent.bind(this,index,item.id)}
                                            onMouseDown={preventDefault}
                                        >
                                            {showColorSetIndex == index &&
                                                <MoreColorsPanel
                                                    color={item}
                                                    onChange={this.colorEvent2.bind(this,index)}
                                                    style={MoreColorsPanelStyle}
                                                />
                                            }
                                            <i className="iconfont icon-tianchong"></i>
                                            {/* <i className="iconfont icon-biankuang"></i> */}

                                        </div>
                                        <div className="typeBtnBanpipe"></div>
                                    </>)
                                }
                            })}
                            {colorTable1[1] &&
                                <div
                                    aria-label="边框"
                                    className={"typeBtnBef typeBtn typeBtnBefb"}
                                    onClick={this.colorBtnItemBlockClickEvent.bind(this,1,colorTable1[1]?.id)}
                                    onMouseDown={preventDefault}
                                >
                                    <i className="iconfont icon-biankuang1"></i>
                                </div>}
                            <div className="typeBtnBanpipe"></div>
                            <div
                                aria-label="颜色预设"
                                className={"typeBtnBef typeBtn typeBtnBefb"}
                                onClick={this.onColorPanelClickEvent.bind(this)}
                            // onMouseDown={preventDefault}
                            >
                                <i className="iconfont icon-yanse1"></i>
                            </div>

                        </div>
                        {
                                isShowPresetColor && <div className="presetColorContainer">
                                    <p>颜色预设</p>
                                    <div className='presetColorContent'>
                                        {
                                            colorTable2.map((item,index) => {
                                                return (
                                                    <div className="presetColorItem" onClick={this.onSelectPresetColorClickEvent.bind(this,item.id)}>
                                                        {
                                                            item.color_table.map((subItem,subIndex) => {

                                                                let style = {
                                                                    background: `rgba(${subItem.r}, ${subItem.g}, ${subItem.b}, ${subItem.a})`
                                                                };

                                                                return (
                                                                    <div className={item.color_table.length >= 3 ? "shemeItemBlockItem" : "shemeItemBlockItemDouble"} style={style}></div>
                                                                );
                                                            })
                                                        }

                                                    </div>
                                                )
                                            })
                                        }

                                    </div>
                                </div>
                            }
                    </div>
                    <div className="fontContainer" onMouseDown={preventDefault}>
                        <NewTextEditBlock choosedTdKeys={choosedTdKeys} />
                        <NewTypesettingBlock choosedTdKeys={choosedTdKeys} />
                    </div>

                </div>
                {showColorSetIndex == 1 &&
                    <TableBorderStylePanel
                        color={colorTable1[1]}
                        onChange={this.colorEvent2.bind(this,1)}
                        style={{
                            position: 'absolute',
                            left: '-50px',
                            top: '170px',
                            zIndex: 99,
                        }}
                    />
                }

                {/* <div className="tableEditContainer">
                    <p>表格选项</p>
                    <div className="tableEdit" onClick={this.onClickEditTableClickEvent.bind(this)}>编辑表格数据</div>
                </div> */}

                <div className="tableTransparent">
                    <div className="desc">表格透明</div>
                    {/* <span onClick={this.transparentControlClickEvent.bind(this)} className={classNames('transparentControlSwitchC',{'active': isTransparent})}><i className={isTransparent ? "transparentControlSwitchOff" : "transparentControlSwitchOn"}></i></span> */}
                    <div className="sliderContainer">
                        <Slider 
                            value={transparencyValue}
                            onChange={this.transparentControlClickEvent.bind(this)}
                            min={0}
                            max={100}
                            step={1}
                            sliderWidth={150}
                        />
                    </div>
                </div>
                {/* <div className="textEditorBox-left">
                    <TextEditBlock choosedTdKeys = {choosedTdKeys} />
                    <TypesettingBlock choosedTdKeys = {choosedTdKeys} />
                </div> */}
                <div className="borderline" style={{width: '220px ',border: '1px solid #f7f7f7',marginLeft: '20px'}}></div>
                {/* <SetAssetAnimation marginLeft={'20px'} />
                <AssetUpdateEffect isActive={isEffectFollow} marginLeft={'20px'} /> */}
                <TransformSettingBlock />
                <PanelDoBlock />
            </div>
        );
    }
}



export {ToolTablePanel, TableBorderStylePanel};
