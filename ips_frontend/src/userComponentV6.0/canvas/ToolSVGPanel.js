import React,{Component} from 'react'
import PropTypes from 'prop-types'
import addEventListener from 'rc-util/lib/Dom/addEventListener'
import {SketchPicker} from 'react-color';

import {canvasStore} from '../../redux/CanvasStore';
import {paintOnCanvas} from '../canvas/CanvasRedux';
import {MoreColorsPanel} from '../MoreColorsPanel/MoreColorsPanel';
import {assetManager} from '../AssetManager';
import MoreColorsPreinstall from '../MoreColorsPanel/MoreColorsPreinstall'
import {matchesSelector} from '../Function';
import {PanelDoBlock} from './PanelDoBlock';
import SliderItem from '../Slider/SliderItem';
import {storeAdapter} from '@v7_logic_core/StoreAdapter';
import {FlowEditBlock} from '@v7_render/AssetSvgStroke'
import {SetAssetAnimation} from '@v7_render/SetAssetAnimation'

import {ToolDividingLine} from '../Function';

import {emitter} from '../Emitter'
import {AssetLogic,UpdateAsset} from '@v7_logic/AssetLogic';
import {GroupAssetEditBox} from '@v7_render/AssetToolPanel/Group';
import classNames from 'classnames';
import {TransformSettingBlock} from '@v7_render/AssetToolPanel/components/TransformSettingBlock';
import {isSameColor} from '../../userComponentV6.0/MoreColorsPanel/components'
import {hexToRgb} from '@v7_utils/color'
import {klona as cloneDeep} from "klona";
import {AssetDecoration} from '@v7_render/AssetDecoration';
/**
 * 元素属性
 */
class ElementAttrBlock extends Component {
    constructor(props) {
        super(props)

        this.state = {
            otherRadio: '',
            otherRadioChecked: ''
        }


        this.propNameChangeEvent = this.propNameChangeEvent.bind(this)
        this.otherRadioChangeEvent = this.otherRadioChangeEvent.bind(this)
        this.clickOtherRadioInputEvent = this.clickOtherRadioInputEvent.bind(this)
    }


    propNameChangeEvent(e) {
        AssetLogic.updateTextValueHint(
            {
                name: e.target.value
            }
        )
        // canvasStore.dispatch(paintOnCanvas('UPDATE_VALUEHINT', {name: e.target.value}))
        this.setState({
            otherRadioChecked: ''
        })
    }


    otherRadioChangeEvent(e) {
        AssetLogic.updateTextValueHint(
            {
                name: e.target.value
            }
        )
        // canvasStore.dispatch(paintOnCanvas('UPDATE_VALUEHINT', {name: e.target.value}))
        this.setState({
            otherRadio: e.target.value
        })
    }


    clickOtherRadioInputEvent(e) {
        AssetLogic.updateTextValueHint(
            {
                name: e.target.value
            }
        )
        // canvasStore.dispatch(paintOnCanvas('UPDATE_VALUEHINT', {name: e.target.value}))
        this.setState({
            otherRadioChecked: 'checked'
        })
    }


    render() {
        let th = this
        let propVlues = [
            '二维码',
            'Logo'
        ]
        let {otherRadio,otherRadioChecked} = this.state,
            toolPanel = canvasStore.getState().onCanvasPainted.toolPanel,
            defaultShow = 'checked'

        if(otherRadio == '' && toolPanel.asset.meta && !(propVlues.indexOf(toolPanel.asset.meta.name) >= 0)) {
            otherRadio = toolPanel.asset.meta.name
        }

        return (
            <div className="elementAttrBlock">
                <div className="blockTitle">该元素属性</div>
                <div className="radioArea">
                    {propVlues.map(function(name) {
                        var checked = ''
                        if(toolPanel.asset.meta && (toolPanel.asset.meta.name == name)) {
                            checked = 'checked'
                            defaultShow = ''
                        }
                        return <label className="item" key={name}><input type="radio" name="propName" value={name} onChange={th.propNameChangeEvent} checked={checked} />{name}</label>
                    })}
                    <label className="item"><input type="radio" ref="otherRadio" name="propName" onClick={this.clickOtherRadioInputEvent} value={otherRadio} checked={defaultShow} />其他<input type="text" value={otherRadio} className="otherRadio" onChange={this.otherRadioChangeEvent} onClick={this.clickOtherRadioInputEvent} /></label>
                </div>
            </div>
        );
    }
}

/*
 *形状编辑
 * */
class SVGEditBlock extends Component {
    constructor(props) {
        super(props)

        this.state = {
            colorsShow: '',
            isFlipMoreList: false,
            isColorMorePanel: false,
        };

        this.commonColorList = [
            {
                r: 0,g: 0,b: 0,a: 1,
            },{
                r: 50,g: 50,b: 50,a: 1,
            },{
                r: 98,g: 98,b: 98,a: 1,
            },{
                r: 191,g: 191,b: 191,a: 1,
            },{
                r: 255,g: 255,b: 255,a: 1,
            },{
                r: 255,g: 54,b: 58,a: 1,
            },{
                r: 255,g: 240,b: 53,a: 1,
            },{
                r: 45,g: 191,b: 255,a: 1,
            },{
                r: 103,g: 234,b: 84,a: 1,
            },{
                r: 250,g: 142,b: 192,a: 1,
            },{
                r: 30,g: 35,b: 130,a: 1,
            },{
                r: 88,g: 33,b: 128,a: 1,
            },{
                r: 202,g: 0,b: 0,a: 1,
            },{
                r: 155,g: 72,b: 22,a: 1,
            },
        ];

        this.sliderDownFontOpacityEvent = this.sliderDownEvent.bind(this,'opacity');
        this.fontOpacityChangeEvent = this.fontOpacityChangeEvent.bind(this);
        this.horizontalFlipEvent = this.horizontalFlipEvent.bind(this);
        this.verticalFlipEvent = this.verticalFlipEvent.bind(this);
        this.degrees90Left = this.degrees90Left.bind(this);
        this.degrees90Right = this.degrees90Right.bind(this);

        this.opacityMin = 0;
        this.opacityMax = 100;
        this.opacityWidth = 111;
    }

    componentDidMount() {
        this.floatFuncListener = emitter.addListener("openFuncFloat",(type,item) => {
            if(type === "svg-color") {
                this.setState({
                    isColorMorePanel: !this.state.isColorMorePanel,
                });
            }
        });
    }

    componentWillUnmount() {
        this.floatFuncListener?.remove();
    }

    /**
     * 滑动条点击事件
     */
    sliderDownEvent(sliderType,e) {
        let toolPanel = canvasStore.getState().onCanvasPainted.toolPanel

        this.opacity = toolPanel.asset.attribute.opacity;

        this.clientX = e.clientX;
        this.clientY = e.clientY;
        this.sliderType = sliderType;
        let th = this;
        this.sliderMoveListtener = addEventListener(window,'mousemove',function(e) {
            th.sliderMoveEvent(e);
        });
        this.sliderUpListtener = addEventListener(window,'mouseup',function(e) {
            th.sliderUpEvent(e);
        });
    }

    /**
     * 滑动条释放事件
     */
    sliderUpEvent(e) {
        this.sliderMoveListtener.remove();
        this.sliderUpListtener.remove();
    }


    /**
     * 滑动条移动事件
     */
    sliderMoveEvent(e) {
        var props = {};

        switch(this.sliderType) {
            case 'opacity':
                var opacity = this.opacityFormat(this.opacity + (e.clientX - this.clientX) * this.opacityMax / this.opacityWidth)

                Object.assign(props,{opacity: opacity});
                // canvasStore.dispatch(paintOnCanvas('UPDATE_OPACITY', {opacity: this.opacityMin + (this.opacityMax - this.opacityMin) * opacity / this.opacityMax}));
                const {toolPanel} = storeAdapter.getStore({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                });
                AssetLogic.updateAssetImage(
                    {
                        assetIndex: toolPanel.asset_index,
                        // className:toolPanel.asset.meta.className,
                        key: "opacity",
                        attribute: "attribute",
                        value: this.opacityMin + (this.opacityMax - this.opacityMin) * opacity / this.opacityMax,
                        fun_name: "UPDATE_OPACITY"
                    }
                )
                break;
        }

        this.setState(Object.assign({

        },props));
    }


    /**
     * 透明度（滑动范围设置）
     */
    opacityFormat(opacity) {
        opacity = opacity > this.opacityMin ? opacity : this.opacityMin
        opacity = opacity > this.opacityMax ? this.opacityMax : opacity;

        return opacity;
    }


    /**
     * 文字透明度（输入框）
     */
    fontOpacityChangeEvent(e) {
        var opacity;
        opacity = parseInt(e.target.value) > this.opacityMax ? this.opacityMax : parseInt(e.target.value);

        const {toolPanel} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        AssetLogic.updateAssetImage(
            {
                assetIndex: toolPanel.asset_index,
                // className:toolPanel.asset.meta.className,
                key: "opacity",
                attribute: "attribute",
                value: opacity,
                fun_name: "UPDATE_OPACITY"
            }
        )

        // canvasStore.dispatch(paintOnCanvas('UPDATE_OPACITY', {opacity: opacity}));
    }

    /**
     * 水平翻转
     */
    horizontalFlipEvent(e) {
        const {toolPanel} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        AssetLogic.updateAssetImage(
            {
                assetIndex: toolPanel.asset_index,
                attribute: "transform",
                key: "horizontalFlip",
                value: !toolPanel.asset.transform.horizontalFlip,
                fun_name: "HORIZONTAL_FLIP"
            }
        )
        // canvasStore.dispatch(paintOnCanvas('HORIZONTAL_FLIP'))
    }

    /**
     * 垂直翻转
     */
    verticalFlipEvent(e) {
        const {toolPanel} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        AssetLogic.updateAssetImage(
            {
                assetIndex: toolPanel.asset_index,
                attribute: "transform",
                key: "verticalFlip",
                value: !toolPanel.asset.transform.verticalFlip,
                // className:toolPanel.asset.meta.className,
                fun_name: "VERTICAL_FLIP"
            }
        )
        // canvasStore.dispatch(paintOnCanvas('VERTICAL_FLIP'))
    }

    /**
     * 向左旋转90度
     */
    degrees90Left(e) {
        AssetLogic.degrees90Left({})
        // canvasStore.dispatch(paintOnCanvas('DEGREES_90_LEFT'))
    }

    /**
     * 向右旋转90度
     */
    degrees90Right(e) {
        AssetLogic.degrees90Right({})
        // canvasStore.dispatch(paintOnCanvas('DEGREES_90_RIGHT'))
    }

    /**
     * 设置颜色// 取色按钮
     */
    colorEvent1(kColor,color,type) {
        this.colorEvent(kColor,color,type)
    }
    /**
    * 设置颜色// 取色板
    */
    colorEvent2(kColor,color,type) {
        this.colorEvent(kColor,color,type)
    }
    /**
     * 设置颜色
     */
    colorEvent(kColor,color,type) {
        let tempColor = {
            r: color.color.r,
            g: color.color.g,
            b: color.color.b,
            a: color.color.a,
        }
        if(type == 'click') {
            AssetLogic.updateAssetSvgColorsInfo(
                {
                    color: tempColor,kColor: kColor
                }
            )
            // canvasStore.dispatch(paintOnCanvas('UPDATE_SVG_COLORS_INFO_END', {color: tempColor, kColor: kColor}));
        } else {

            AssetLogic.updateAssetSvgColorsInfo(
                {
                    color: tempColor,kColor: kColor
                }
            )
            // canvasStore.dispatch(paintOnCanvas('UPDATE_SVG_COLORS_INFO', {color: tempColor, kColor: kColor}));
        }
        this.setState({});
        // this.forceUpdate();
    }

    /**
     * 替换全部颜色
     */
    replaceAllSVGColorEvent(assets,lastColor,color) {
        // 记录操作，方便undo
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'UPDATE_SVG_COLORS_INFO',
            params: [{
                type: 'UPDATE_SVG_COLORS_INFO',
                params: {}
            }],
        });
        const targetsAssets = [];
        for(const asset of assets) {
            const colors = cloneDeep(asset.attribute.colors);
            // 与lastColor相同的colors的key才是kColor
            const kColor = Object.keys(colors).find(key => {
                if(typeof colors[key] === 'string') {
                    return isSameColor(lastColor,hexToRgb(key))
                } else {
                    return isSameColor(lastColor,colors[key])
                }
            });

            if(!kColor) continue;

            Object.assign(colors,{
                [kColor]: color,
            });

            targetsAssets.push({
                index: asset.meta.rt_page_assets_index,
                className: asset.meta.className,
                changes: {
                    attribute: {
                        colors,
                    }
                }
            })
        }
        // 批量更新
        UpdateAsset.updateAssets('UPDATE_SVG_COLORS_INFO',targetsAssets,void 0,void 0,true);
        this.setState({});
    }

    // 检查其他相同颜色的元素
    checkLastColorInCavans(color) {
        if(!color) return []

        const {work} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        })
        const res = []

        // 遍历所有page，寻找指定color的SVG对象，包括在组中的，并返回
        for(let index = 0; index < work.pages.length; index++) {
            const assetsSvg = work.pages[index].assets.filter(item => item.meta.type === 'SVG' && this.isEqualColor(item,color));
            res.push(...assetsSvg)
        }

        return res
    }

    // 判断asset中是含指定color
    isEqualColor(asset,color) {
        let colorsArr = [];
        for(let item in asset.attribute.colors) {
            colorsArr.push([item,asset.attribute.colors[item]]);
        }
        for(let index = 0; index < colorsArr.length; index++) {
            const item = colorsArr[index];
            if(index > 0) {
                return;
            }
            let moreColorsPanelColor = item[0];
            if(item[1] != '') {
                moreColorsPanelColor = item[1];
            }
            return isSameColor(color,typeof moreColorsPanelColor === 'string' ? hexToRgb(moreColorsPanelColor) : moreColorsPanelColor)
        }
    }

    choiceColorClickEvent(e) {
        if(this.choiceColorClickListtener) {
            this.choiceColorClickListtener.remove();
        }
        this.setState({
            isColorMorePanel: !this.state.isColorMorePanel,
        });
        let th = this;
        this.choiceColorClickListtener = addEventListener(window,'click',function(e) {
            if(matchesSelector(e.target,'.SVGEditBlock .choiceColor')) {
                return
            }
            th.setState({
                isColorMorePanel: false,
                colorsShow: false,
            });
            th.choiceColorClickListtener.remove()
        });
        //
        // e.stopPropagation();
        // e.nativeEvent.stopPropagation();
    }

    /**
     * 颜色防止冒泡
     */
    colorClickEvent(e) {
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
    }

    colorBlockItemClickEvent(color,kColor,e) {
        AssetLogic.updateAssetSvgColorsInfo(
            {
                color: color.rgb,kColor: kColor
            }
        )
        // canvasStore.dispatch(paintOnCanvas('UPDATE_SVG_COLORS_INFO', {color: color.rgb, kColor: kColor}));

        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    /**
     * SVG色块（点击事件）
     * @param kColor
     */
    colorsItemClick(kColor,e) {
        this.setState({
            colorsShow: kColor
        });

        if(this.colorsItemClickListtener) {
            this.colorsItemClickListtener.remove();
        }
        let th = this;
        this.colorsItemClickListtener = addEventListener(window,'click',function(e) {
            th.colorsItemClickListtener.remove()

            th.setState({
                colorsShow: ''
            });
        });

        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
    }

    /**
     * 颜色弹窗
     */
    colorShowEvent(kColor,e) {
        // if( this.colorShowFlag == 1 ){
        //     return
        // }
        let th = this
        // this.colorShowFlag = 2
        this.setState({
            colorsShow: kColor
        })

        if(this.colorShowClickListtener) {
            this.colorShowClickListtener.remove();
        }
        this.colorShowClickListtener = addEventListener(window,'click',function(e) {
            // if( th.colorShowFlag == 2 ){
            //     th.colorShowFlag = 1
            //     return
            // }
            th.colorShowClickListtener.remove()

            var toolPanel = canvasStore.getState().onCanvasPainted.toolPanel

            if(toolPanel.asset == undefined || toolPanel.asset == "") {
                return false
            }

            th.setState({
                colorsShow: false,
                isColorMorePanel: false,
            })
            // th.colorShowFlag = 2

        });
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    typeBtnMoreClickEvent(e) {
        this.setState({
            isFlipMoreList: true
        });

        if(this.typeBtnMoreClickListtener) {
            this.typeBtnMoreClickListtener.remove();
        }
        let th = this;
        this.typeBtnMoreClickListtener = addEventListener(window,'click',function(e) {
            th.setState({
                isFlipMoreList: false
            });
            th.typeBtnMoreClickListtener.remove()
        });

        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    stopPropagation(e) {
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    render() {
        let toolPanel = canvasStore.getState().onCanvasPainted.toolPanel,
            horizontalFlipClass = '',
            verticalFlipClass = ''
        let {colorsShow,isFlipMoreList,isColorMorePanel} = this.state,
            typeBtnMoreClickEvent = this.typeBtnMoreClickEvent.bind(this);
        if(isFlipMoreList) {
            typeBtnMoreClickEvent = "";
        }

        let opacityInput = parseInt(toolPanel.asset.attribute.opacity),
            opacity = (opacityInput - this.opacityMin) * this.opacityWidth / (this.opacityMax - this.opacityMin)

        if(toolPanel.asset.transform.horizontalFlip) {
            horizontalFlipClass = 'active'
        }

        if(toolPanel.asset.transform.verticalFlip) {
            verticalFlipClass = 'active'
        }

        opacity = opacity > 0 ? opacity : 0

        let colorsArr = [];
        for(let item in toolPanel.asset.attribute.colors) {
            colorsArr.push([item,toolPanel.asset.attribute.colors[item]]);
        }
        return (
            <div className="SVGEditBlock">
                <div className="item">
                    {colorsArr.map((item,index) => {
                        if(index > 0) {
                            return;
                        }
                        let color = item[0],
                            moreColorsPanelColor = color;
                        if(item[1] != '') {
                            color = "rgba(" + item[1].r + "," + item[1].g + "," + item[1].b + "," + item[1].a + ")";
                            moreColorsPanelColor = item[1];
                        }
                        return (
                            <div key={"colorsArr_" + index}>
                                <div className="commonSelect" onClick={this.choiceColorClickEvent.bind(this)}>
                                    <MoreColorsPreinstall dropdownStyle={{top: '43px',right: '-1px'}}
                                        btnStyle={{position: 'relative',float: 'right'}} onCallback={this.colorEvent1.bind(this,item[0])}
                                        closeColorSelect={() => this.setState({isColorMorePanel: false})}
                                    />
                                    <div className="choiceColor"
                                        style={{background: color}}
                                        ref="color">
                                    </div>
                                    <div className="colorPanel" ref="colorSelect">
                                        {isColorMorePanel &&
                                            <MoreColorsPanel color={moreColorsPanelColor}
                                                onChange={this.colorEvent2.bind(this,item[0])}
                                                style={{
                                                    left: '-10px',
                                                    top: '-20px',
                                                    zIndex: 20,
                                                }}
                                                onClose={() => this.setState({isColorMorePanel: false})}
                                                couldReplaceAllColor={true}
                                                onReplaceColorFunc={this.replaceAllSVGColorEvent.bind(this)}
                                                checkLastColorInCavans={this.checkLastColorInCavans.bind(this)}
                                                setPvReplaceAllColorHover={8321}
                                                setPvReplaceAllColorClick={8322}
                                            />}
                                    </div>
                                </div>
                            </div>
                        );
                    })}

                </div>


                {/*<div className="blockTitle">编辑线条形状</div>*/}
                {/*<div className="item">*/}
                {/*<p>透明度</p>*/}
                {/*<br />*/}
                {/*<div className="secondLine">*/}
                {/*<div className="slideBar">*/}
                {/*<div className="slideBarButtonBar" style={{width: opacity + 'px'}}></div>*/}
                {/*<div className="slideBarButton" style={{left: opacity+'px'}} onMouseDown={this.sliderDownFontOpacityEvent}></div>*/}
                {/*<div className="slideBarMask"></div>*/}
                {/*</div>*/}
                {/*<input type="text" value={opacityInput} onChange={this.fontOpacityChangeEvent} />*/}
                {/*/!*<i style={{position: 'absolute', right: '9px', top: '-2', color: '#666', fontSize: '16px'}}>%</i>*!/*/}
                {/*</div>*/}
                {/*</div>*/}
                {/*/!*<div className="item mb10">*!/*/}
                {/*/!*<p>旋转</p>*!/*/}
                {/*/!*<div className="typeBtnArea">*!/*/}
                {/*/!*<div className="typeBtn" onClick={this.degrees90Left}>*!/*/}
                {/*/!*<i className="item iconfont iconfont icon-rotate" ></i>*!/*/}
                {/*/!*</div>*!/*/}
                {/*/!*<div className="typeBtn" onClick={this.degrees90Right}>*!/*/}
                {/*/!*<i className="item iconfont icon-youxuanzhuan"></i>*!/*/}
                {/*/!*</div>*!/*/}
                {/*/!*</div>*!/*/}
                {/*/!*</div>*!/*/}
                {/*<div className="item" style={{display: 'inline-block', width: '70px'}}>*/}
                {/*<p>颜色</p>*/}
                {/*<div className="colorsArea">*/}
                {/*{*/}
                {/*colorsArr.map((item) => {*/}
                {/*let color = item[0];*/}
                {/*if( item[1] != '' ){*/}
                {/*color = "rgba(" + item[1].r + "," + item[1].g + "," + item[1].b + "," + item[1].a + ")";*/}
                {/*}*/}

                {/*return (*/}
                {/*<div className="colorsItem" onClick={this.choiceColorClickEvent.bind(this)} style={{background: color}}>*/}
                {/*{isColorMorePanel && <div className="colorMorePanel" onClick={this.colorClickEvent.bind(this)}>*/}
                {/*<div className="colorMorePanelTitle">常见颜色</div>*/}
                {/*<div className="colorBlockList">*/}
                {/*{*/}
                {/*this.commonColorList.map((item2) => {*/}

                {/*return (*/}
                {/*<div className="colorBlockItem" style={{background: 'RGBA('+item2.r+', '+item2.g+', '+item2.b+', '+item2.a+')'}} onClick={this.colorBlockItemClickEvent.bind(this, {rgb: {r: item2.r, g: item2.g, b: item2.b, a: item2.a}}, item[0])}></div>*/}
                {/*);*/}
                {/*})*/}
                {/*}*/}
                {/*<div className="colorBlockItem colorBlockMore" onClick={this.colorShowEvent.bind(this, item[0])}></div>*/}
                {/*</div>*/}
                {/*</div>}*/}
                {/*<div className="colorPanel">*/}
                {/*{colorsShow == item[0] && <SketchPicker color={color} disableAlpha={false} onChange={this.colorEvent.bind(this, item[0])} />}*/}
                {/*</div>*/}
                {/*</div>*/}
                {/*);*/}
                {/*})*/}
                {/*}*/}
                {/*</div>*/}
                {/*</div>*/}
                {/*<div className="item" style={{display: 'inline-block', width: '100px', marginLeft: '25px'}}>*/}
                {/*<p>翻转</p>*/}
                {/*<div className="typeBtnList">*/}
                {/*<div className={"typeBtn " + horizontalFlipClass} onClick={this.horizontalFlipEvent}>*/}
                {/*<i className="iconfont icon-zuoyoufanzhuan"></i>*/}
                {/*</div>*/}
                {/*<div className="typeBtnMore" onClick={typeBtnMoreClickEvent}>*/}
                {/*<i className="iconfont icon-xiangshang-copy-copy"></i>*/}
                {/*</div>*/}
                {/*{isFlipMoreList && <div className="typeBtnMoreList">*/}
                {/*<div className={"typeBtnMoreItem " + horizontalFlipClass} onClick={this.horizontalFlipEvent}>*/}
                {/*<i className="iconfont icon-zuoyoufanzhuan"></i>*/}
                {/*</div>*/}
                {/*<div className={"typeBtnMoreItem " + verticalFlipClass} onClick={this.verticalFlipEvent}>*/}
                {/*<i className="iconfont icon-shangxiafanzhuan"></i>*/}
                {/*</div>*/}
                {/*</div>}*/}
                {/*</div>*/}
                {/*/!*<div className="typeBtnArea">*!/*/}
                {/*/!*<div className={"typeBtn " + horizontalFlipClass} onClick={this.horizontalFlipEvent}>*!/*/}
                {/*/!*<i className="item iconfont icon-zuoyoufanzhuan"></i>*!/*/}
                {/*/!*</div>*!/*/}
                {/*/!*<div className={"typeBtn " + verticalFlipClass} onClick={this.verticalFlipEvent}>*!/*/}
                {/*/!*<i className="item iconfont icon-shangxiafanzhuan"></i>*!/*/}
                {/*/!*</div>*!/*/}
                {/*/!*</div>*!/*/}
                {/*</div>*/}
            </div>
        );
    }
}

class TypesettingBlock extends Component {

    constructor(props) {
        super(props)

        this.state = {
            toolPanel: props.toolPanel,
            lineHeightShow: false,
            letterSpacingShow: false,
        }

        this.sliderDownFontOpacityEvent = this.sliderDownEvent.bind(this,'opacity');
        this.fontOpacityChangeEvent = this.fontOpacityChangeEvent.bind(this);

        this.stopPropagationEvent = this.stopPropagationEvent.bind(this)

        this.fontOpacityMin = 0;
        this.fontOpacityMax = 100;
        this.fontOpacityWidth = 100;
    }

    /**
     * 滑动条点击事件
     */
    sliderDownEvent(sliderType,e) {
        var toolPanel = canvasStore.getState().onCanvasPainted.toolPanel;

        this.letterSpacing = toolPanel.asset.attribute.letterSpacing;
        this.lineHeight = toolPanel.asset.attribute.lineHeight;
        this.opacity = toolPanel.asset.attribute.opacity;


        this.clientX = e.clientX;
        this.clientY = e.clientY;
        this.sliderType = sliderType;
        var th = this;
        this.sliderMoveListtener = addEventListener(window,'mousemove',function(e) {
            th.sliderMoveEvent(e);
        });
        this.sliderUpListtener = addEventListener(window,'mouseup',function(e) {
            th.sliderUpEvent(e);
        });

    }

    /**
     * 滑动条释放事件
     */
    sliderUpEvent(e) {
        this.sliderMoveListtener.remove();
        this.sliderUpListtener.remove();
    }

    /**
     * 滑动条移动事件
     */
    sliderMoveEvent(e) {
        let props = {};
        switch(this.sliderType) {
            /*透明度*/
            case 'opacity':
                var opacity = this.opacityFormat(this.opacity + (e.clientX - this.clientX) * this.fontOpacityMax / this.fontOpacityWidth)

                Object.assign(props,{opacity: opacity});
                // canvasStore.dispatch(paintOnCanvas('UPDATE_OPACITY', {opacity: this.fontOpacityMin + (this.fontOpacityMax - this.fontOpacityMin) * opacity / this.fontOpacityMax}));
                const {toolPanel} = storeAdapter.getStore({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                });
                AssetLogic.updateAssetImage(
                    {
                        assetIndex: toolPanel.asset_index,
                        // className:toolPanel.asset.meta.className,
                        key: "opacity",
                        attribute: "attribute",
                        value: this.fontOpacityMin + (this.fontOpacityMax - this.fontOpacityMin) * opacity / this.fontOpacityMax,
                        fun_name: "UPDATE_OPACITY"
                    }
                )
                break;

            default:
                break
        }
        this.setState(Object.assign({},props));
    }

    /**
     * 文字透明度
     */
    fontOpacityChangeEvent(value,unrecordable) {
        var opacity;
        opacity = parseInt(value) > this.fontOpacityMax ? this.fontOpacityMax : parseInt(value);
        // canvasStore.dispatch(paintOnCanvas('UPDATE_OPACITY', {opacity: opacity}));
        const {toolPanel} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        UpdateAsset.updateAssets(
            "UPDATE_ASSETS",
            [{
                index: toolPanel.asset_index,
                changes: {
                    attribute: {
                        opacity
                    }
                }
            }],
            undefined,
            undefined,
            unrecordable
        )
    }

    /**
     * 透明度（滑动范围设置）
     */
    opacityFormat(opacity) {
        opacity = opacity > 0 ? opacity : 0
        opacity = opacity > this.fontOpacityMax ? this.fontOpacityMax : opacity;

        return opacity;
    }

    /**
     * 阻止冒泡
     */
    stopPropagationEvent(e) {
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
    }

    /**
     * 水平翻转
     */
    horizontalFlipEvent(e) {
        const {toolPanel} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        AssetLogic.updateAssetImage(
            {
                assetIndex: toolPanel.asset_index,
                attribute: "transform",
                key: "horizontalFlip",
                value: !toolPanel.asset.transform.horizontalFlip,
                fun_name: "HORIZONTAL_FLIP"
            }
        )
        // canvasStore.dispatch(paintOnCanvas('HORIZONTAL_FLIP'));

        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    /**
     * 垂直翻转
     */
    verticalFlipEvent(e) {
        const {toolPanel} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        AssetLogic.updateAssetImage(
            {
                assetIndex: toolPanel.asset_index,
                attribute: "transform",
                key: "verticalFlip",
                value: !toolPanel.asset.transform.verticalFlip,
                // className:toolPanel.asset.meta.className,
                fun_name: "VERTICAL_FLIP"
            }
        )
        // canvasStore.dispatch(paintOnCanvas('VERTICAL_FLIP'));

        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    render() {
        let toolPanel = canvasStore.getState().onCanvasPainted.toolPanel;

        let {opacity} = toolPanel.asset.attribute;

        opacity = (opacity - this.fontOpacityMin) * this.fontOpacityWidth / (this.fontOpacityMax - this.fontOpacityMin);
        opacity = opacity > 0 ? parseInt(opacity) : 0;

        let horizontalFlipClass = '',
            verticalFlipClass = '';

        if(toolPanel.asset.transform.horizontalFlip) {
            horizontalFlipClass = 'active'
        }

        if(toolPanel.asset.transform.verticalFlip) {
            verticalFlipClass = 'active'
        }

        return (
            <div className="typesettingBlock">
                {/*版式*/}
                <div className="typeBtnWrap">
                    <div className={"typeBtnLg " + horizontalFlipClass}
                        onClick={this.horizontalFlipEvent.bind(this)}>
                        <i className="iconfont icon-zuoyoufanzhuan"></i>
                        横向翻转
                    </div>
                    <div className={"typeBtnLg " + verticalFlipClass}
                        onClick={this.verticalFlipEvent.bind(this)}>
                        <i className="iconfont icon-shangxiafanzhuan"></i>
                        竖向翻转
                    </div>
                    <div className={"typeBtnLg-slider"}>
                        <p>不透明</p>
                        <SliderItem
                            value={opacity}
                            range={[0,100]}
                            sliderWidth={120}
                            onChange={(value,isStop,unrecordable) => this.fontOpacityChangeEvent(value,unrecordable)}
                            showInput={true}
                            height={42}
                            adjust
                        />
                    </div>

                </div>

            </div>
        );
    }
}

class ToolSVGPanel extends Component {
    constructor(props) {
        super(props)
        let {toolPanel} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        this.state = {
            isEffectFollow: toolPanel.asset.attribute?.ks && toolPanel.asset.attribute?.ks?.i?.kw?.ks?.c?.length > 0 ? true : false
        }
    }
    render() {
        /*编辑器判断*/
        let {isDesigner,toolPanel,isDocDesigner} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const {isEffectFollow} = this.state;
        const isInnerGroup = toolPanel.asset?.meta.group
        const isFlow = toolPanel.asset?.meta?.type === 'flow' || toolPanel.asset?.meta?.type === 'SVG'  ;
        return (
            <div className={classNames("toolSVGPanel")}>
                {isDocDesigner && <AssetDecoration />}
                {isInnerGroup && <GroupAssetEditBox style={{marginBottom: '20px',paddingBottom: '15px',borderBottom: '1px solid #E9E8E8'}} />}
                {/*{isDesigner && <ElementAttrBlock/>}*/}
                {/*{isDesigner && <ToolDividingLine/>}*/}
                <FlowEditBlock /> 
                {/*<ToolDividingLine/>*/}
                <TypesettingBlock />
                {/* <SetAssetAnimation marginLeft={'20px'} />
                <AssetUpdateEffect isActive={isEffectFollow} marginLeft={'20px'} /> */}
                <TransformSettingBlock />
                <PanelDoBlock />
            </div>
        )
    }
}

export {ToolSVGPanel}