import React, { Component, PureComponent } from 'react';

import { CanvasContent } from '@v7_render/Canvas';

import { Nav } from './Nav';
// import { ToolContent } from './toolV6.0/Tool';
import { DesigerToolContent } from './Designer/toolV6.0/Tool';
import { connect } from 'react-redux';
// import { CanvasContent } from './canvas/Canvas';
import PropTypes from 'prop-types';
import { emitter } from './Emitter';
import { FloatToolPanel } from './toolV6.0/FloatToolPanel';
import { InfoBar } from './InfoBar';
import { assetManager } from './AssetManager';
import { getProps } from './IPSConfig';
// import { canvasStore } from '../redux/CanvasStore';
import { LoginPanel } from './LoginPanel/LoginPanel';

// import { paintOnCanvas,infoManage } from './canvas/CanvasRedux';
import addEventListener from 'rc-util/lib/Dom/addEventListener';
import { matchesSelector } from './Function';
// import { SideProsAndConsArea } from './SidePagingArea/SideProsAndConsArea';
import OnlineRetailerMainDrawing from './toolV6.0/OnlineRetailer/MainDrawing';
import OnlineDetailComs from './toolV6.0/OnlineRetailer/onlineDetail/OnlineDetail';
import { storeDecorator } from '@v7_logic/StoreHOC';
import { HotKeyHelper, InfoManageHelper } from '@v7_logic/StoreLogic';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { HeadNav } from '@v7_render/HeadNav';
import { CanvasPaintedLogic } from '@v7_logic/CanvasPaintedLogic';

import { ToolContent } from '@v7_render/ToolContent';
import { ErrorBoundary } from '@v7_render/ErrorBoundary';
import { NavigatorTemplate } from './toolV6.0/NavigatorTemplate/NavigatorTemplate';
import { TgsPv } from '../../../ips_component_publish/tgs-component/src/tgs-pv';
import { TemplResExceptionPv } from '@v7_logic/TemplResExceptionPv';
import { storeHOC } from '@v7_logic/StoreHOC';
import { PageAnimation } from '@v7_render/Animation';
import { env } from '@editorConfig/env';
import { CustomizeMenu } from '@v7_render/CustomizeMenu';
import { TemplateCanvasLogic } from '@v7_logic/TemplateCanvasLogic';
import { IPSConfig } from '@v7_utils/IPSConfig';
import { isUeAndEcommerce } from '@v7_utils/webSource'
import { ClickOutside, PopoverBox } from "@v7_render/Ui";
import { Message } from "@v7_render/Ui";
import { isCutout } from './canvas/ToolModuleAssetCutPanel';
import AiAgent from '@v7_render/AiAgent'
import { CanvasLoadingAnimate } from '@v7_render/LoadingAnimate';
import PolyFill from '@v7_utils/polyfill';
import { AITextToolPanel } from '@v7_render/AssetToolPanel/Text/AlTextToolPanel';
import { ETool } from '@v7_logic/Enum';

class InfoLayout extends Component {
    render() {
        return (
            <div className="infoLayout">
                <InfoBar></InfoBar>
            </div>
        );
    }
}

class CanvasLayout extends Component {
    constructor(props) {
        super(props);
        this.state = {
            isLogin: true,
        };
        this.hideLoginAlertClickEvent = this.hideLoginAlertClickEvent.bind(this);
        this.showLoginClickEvent = this.showLoginClickEvent.bind(this);
        this.canvasLayoutRef = React.createRef();
        this.showEmitter();
        this.getIsLoginData();
        this.recordBeginTime();
        // 阻止 ctrl + 滚轮 放大縮小
        window.addEventListener('wheel', function (event) {
            if (event.ctrlKey === true) {
                event.preventDefault();
            }
        }, { passive: false });
    }



    /**
     * 判断是否发送请求的方式
     */
    getIsLoginData() {
        this.isCloseLogin = emitter.addListener('isCloseLoginBtn', (data) => {
            this.isCloseLoginStatus = data;
        });
        this.isLoginSuccess = emitter.addListener('isCloseLoginSuccess', (success) => {
            this.isLoginStatus = success;
        });
    }

    /**
     * 记录进入页面时的时间戳
     */
    recordBeginTime() {
        let now1 = new Date();
        let startTime = now1.getTime();
        let setLoadSlowFlag = true;
        TemplResExceptionPv.setBeginTime({
            beginPageTime: startTime,
            setLoadSlowFlag: setLoadSlowFlag,
        });
    }

    /**
     * 发送请求的方式
     */
    listener = (e) => {
        !this.isCloseLoginStatus && !this.isLoginStatus && assetManager.setPv_new(4103);
        // e.preventDefault();
        // e.returnValue = '确定关闭浏览器？';
    };

    /**
     * 挂载浏览器发送埋点的钩子
     */
    componentDidMount() {
        window.addEventListener('beforeunload', this.listener);
    }

    /**
     * 卸载浏览器发送埋点的钩子
     */
    componentWillUnmount() {
        window.removeEventListener('beforeunload', this.listener);
    }

    showEmitter() {
        let th = this;
        let urlProps = getProps();
        this.showEmitter = emitter.addListener('LoginAlertTip', (status) => {
            if (urlProps.upicId > 0 && !status) {
                // 强制弹出登录
                emitter.emit('LoginPanelShow');
                emitter.emit('LoginPanelShowType');
            }
            th.setState({ isLogin: status });
        });
        this.updateCanvasLayoutEmitter = emitter.addListener('updateCanvasLayoutEmitter', () => {
            this.setState({});
        });
    }

    showLoginClickEvent(e) {
        assetManager.setPv_new(4572, {
            additional: {
                s0: 'loginBtn'
            }
        })
        assetManager.setPv_new(4655)
        emitter.emit('LoginPanelShow', 'loginBtn');
    }

    hideLoginAlertClickEvent(e) {
        assetManager.setPv_new(6849)
        this.setState({ isLogin: true });
    }

    stopPropagation(e) {
        e.stopPropagation();
    }

    render() {
        const { isLogin } = this.state;
        const { toolPanelWidth, resourcePanelWidth, diffWidth, afterDocLoad } = this.props
        let { rt_isMutiSizeTemplate, isDesigner, rt_is_online_detail_page, canvas} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        let diffW = diffWidth;
        let style = rt_isMutiSizeTemplate && isDesigner ? { left: 136 } : {};
        const isLoading = this.props.canvasLoadingStatus;
        if (rt_is_online_detail_page) {
            style = { left: 176 };
        } else if (!canvas.floorCutting) {
            style = { right: 0 };
        } else if (canvas.floorCutting) {
            style = { left: 258 };
        }else{
            style = {};
        }
        if (resourcePanelWidth >= 0 && canvas.floorCutting) {
            if (!style.left || style.left && style.left < resourcePanelWidth) {
                style.left = resourcePanelWidth;
            }
        }
        return (
            <div className="canvasLayout" style={style} ref={this.canvasLayoutRef}>
                {
                    env.editor === 'shentu' &&
                    <div className="preventClick"
                        onClick={this.stopPropagation.bind(this)}
                        onMouseDown={this.stopPropagation.bind(this)}
                    ></div>
                }
                {!isLogin && (
                    <div className="loginAlert">
                        {/* <span style={{ marginLeft: 60 + 'px' }}>登录后，将为您实时保存设计作品{' '}</span> */}
                        登录即可下载无水印大图{' '}
                        <span
                            className="btn"
                            style={{ background: '#ff4555', color: '#fff', width: '100px' }}
                            onClick={this.showLoginClickEvent}
                            onMouseEnter={() => { assetManager.setPv_new(4618) }}
                        >
                            立即登录
                        </span>
                        <span className="closeBtn" onClick={this.hideLoginAlertClickEvent}>
                            &times;
                        </span>
                    </div>
                )}
                {/* {work.pages.length > 1 && isDesigner && <SideProsAndConsArea />} */}
                <CanvasContent toolPanelWidth={toolPanelWidth} resourcePanelWidth={resourcePanelWidth} diffWidth={diffW} afterDocLoad={afterDocLoad} />
                
                {isLoading && <CanvasLoadingAnimate loadingText='加载中...'></CanvasLoadingAnimate>}
            </div>
        );
    }
}

class NavLayout extends Component {
    constructor(props) {
        super(props);
        this.state = {
            sideHelpBox: false,
            helpProblem: '',
            isTeam: false,
            showTemplate: false,
            isHitTip: false
        };
        this.updateStateEmitter();
        // this.navHoverCustomIsShow();
        this.stopPropagationEvent = this.stopPropagationEvent.bind(this);

        assetManager.getTeamName().then((data) => {
            data.json().then((resultData) => {
                if (resultData.stat == 1 && resultData.msg.length > 0) {
                    this.setState({
                        isTeam: true,
                    });
                }
            });
        });
    }

    updateStateEmitter() {
        let th = this;
        this.updateStateEmitter = emitter.addListener('NavLayoutupdateState', () => {
            th.setState({});
        });
    }

    /**
     * 阻止冒泡
     */
    stopPropagationEvent(e) {
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
    }

    /*
     *   是否显示我要帮助的悬浮框
     */
    navHoverCustomIsShowClickEvent(e) {
        if (this.state.sideHelpBox == false) {
            assetManager.setPv_new(806);
        }
        if (this.sideOtherPlaceClick) {
            this.sideOtherPlaceClick.remove();
        }
        let _this = this;
        this.setState({
            sideHelpBox: !this.state.sideHelpBox,
        });
        this.sideOtherPlaceClick = addEventListener(window, 'click', function (e) {
            _this.setState({
                sideHelpBox: false,
            });
            _this.sideOtherPlaceClick.remove();
        });
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
    }

    /*
     *   问题的回答图标向上
     */
    showItemAnswerClickEvent(item, id, e) {
        this.setState({
            helpProblem: item,
        });
        assetManager.setPv_new(id);
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
    }

    /*
     *   点击跳转在线客服
     */
    toCustomPageClickEvent() {
        window.open(
            'https://818ps.com/apiv2/udesk',
            '_blank',
            'height=544, width=644,toolbar=no,scrollbars=no,menubar=no,status=no',
        );
        assetManager.setPv_new(816);
    }

    /*
     *   问题的回答图标向下
     */
    hideItemAnswerClickEvent(item, e) {
        if (item == this.state.helpProblem) {
            this.setState({
                helpProblem: '',
            });
        }
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
    }

    /**
     * 意见反馈按钮（点击事件）
     */
    opinionShowBtnClickEvent(e) {
        emitter.emit('opinionShowBtnClickEvent');
    }

    /**
     * 文字无法更改?（点击事件）
     */
    opinion2ShowBtnClickEvent(e) {
        emitter.emit('opinion2ShowBtnClickEvent');
    }

    componentWillUnmount() {
        this.updateStateEmitter.remove();
        this.emitterOpenTemplate.remove();
        this.popupHitTip.remove();
    }

    componentDidMount() {
        this.emitterOpenTemplate = emitter.addListener('emitterOpenTemplate', (type = false) => {
            let navigatorContainerDom = document.getElementsByClassName('navigatorContainer')[0];
            navigatorContainerDom && (navigatorContainerDom.style.left = '72px');
            this.props.setPanel(false, true);
        })
        if (env.editor !== 'ecommerce' && env.editor !== 'ecommerceteam') {
            this.popupHitTip = emitter.addListener('popupHitTip', (isHitTip) => {
                this.setState({
                    isHitTip
                })
            });
        }
        sessionStorage.setItem('downloadTimer', Date.now());
    }

    /**
     * 删除数组元素
     */
    removeArray(arr, val) {
        for (let i = 0; i < arr.length; i++) {
            if (arr[i] == val) {
                arr.splice(i, 1);
                break;
            }
        }
    }

    /**
     * 处理url参数
     */
    urlParametric(url) {
        let obj = {},
            params = url.substr(1);
        let parr = params.split('&');
        for (let i of parr) {
            let arr = i.split('=');
            obj[arr[0]] = arr[1];
        }
        return obj;
    }

    showRightUploadFile = (activeMenuClassName) => {
        // restore ActivedMenuId
        emitter.emit('changeActivedMenuId', undefined)

        assetManager.setPv_new(207, {
            additional: {}
        });
        emitter.emit('onNavChanged', 'uploadFile');
        emitter.emit('rightLayoutHideClick', '', 'uploadFile', undefined);
    }

    uploadFileMouseEnter = (e) => {
        assetManager.setPv_new(5702)
        e.buttons === 0 && this.props.onItemEnter('uploadFile', '-2', '底部上传');
    }

    knowClick = (e) => {
        emitter.emit('popupHitTip', false)
        localStorage.setItem('navTipPopup', 'true')
        // this.setState({
        //     isHitTip: false
        // })
        assetManager.setPv_new(6708)
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
    }

    render() {
        const userType = this.props.userType;
        const currentNav = this.props.currentNav;
        let urlParames = window.location.search ? this.urlParametric(window.location.search) : {}
        let { sideHelpBox, helpProblem, isTeam, iconStyle, isHitTip } = this.state;
        let templates = { "navType": "template", "navText": "模板", "isActive": currentNav === "template" };
        let navList = [
            templates,
            // {"navType":"template","navText":"模板","isActive":currentNav==="template"},
            // {"navType": "text", "navText": "文字", "isActive": currentNav === "text"},
            { navType: 'specificWord', navText: '特效字', isActive: currentNav === 'specificWord' },
            { navType: 'specificWord3D', navText: '3D文字', isActive: currentNav === 'specificWord3D' },
            { navType: 'sourceMaterial', navText: '素材', isActive: currentNav === 'sourceMaterial' },
            // { "navType": "SVG", "navText": "线条形状", "isActive": currentNav === "SVG" },
            // { "navType": "png", "navText": "贴纸", "isActive": currentNav === "png" },
            // {"navType": "groupWord", "navText": "文案排版", "isActive": currentNav === "groupWord"},
            // {"navType": "artWord", "navText": "艺术字", "isActive": currentNav === "artWord"},
            // {"navType": "png", "navText": "PNG元素", "isActive": currentNav === "png"},
            // {"navType": "background", "navText": "背景", "isActive": currentNav === "background"},
            // {"navType": "pic", "navText": "图片", "isActive": currentNav === "pic"},
            // {"navType": "material", "navText": "素材", "isActive": currentNav === "material"},
            // {"navType":"element","navText":"元素", "isActive":currentNav==="element" },
            // {"navType":"SVG","navText":"图形","isActive":currentNav==="SVG"},
            // {"navType":"favorites","navText":"收藏","isActive":currentNav==="favorites"},
            // { "navType": "emoji", "navText": "表情包", "isActive": currentNav === "emoji" },
            { navType: 'pic', navText: '照片', isActive: currentNav === 'pic' },

            { navType: 'tableChart', navText: '表格图表', isActive: currentNav === 'tableChart' },
            // { "navType": "table", "navText": "表格", "isActive": currentNav === "table" },
            // { "navType": "chartType", "navText": "图表", "isActive": currentNav === "chartType" },
            { navType: 'gallery', navText: '背景', isActive: currentNav === 'gallery' },
            { navType: 'animationEffect', navText: '页面动效', isActive: currentNav === 'animationEffect' },
            { navType: 'videoe', navText: '视频素材', isActive: currentNav === 'videoe' },
            { navType: 'music', navText: '音乐素材', isActive: currentNav === 'music' },
            { navType: 'copywriting', navText: '文案大全', isActive: currentNav === 'copywriting' },
            // { navType: 'uploadFile', navText: '上传', isActive: currentNav === 'uploadFile' },
            // { "navType": "teamWork", "navText": "团队", "isActive": currentNav === "teamWork" },
            // { "navType": "tucaoAsset", "navText": "参赛素材", "isActive": currentNav === "tucaoAsset" },
            // {"navType":"QRcode","navText":"二维码","isActive":currentNav==="QRcode"},
        ];

        const { info } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage,
        });
        let showTemplate;
        showTemplate =
            info &&
            info.class_id &&
            info.class_id.some((v) => {
                return v === '1060' || v === '1059' || v === '1122';
            });
        if (isTeam) {
            navList.push({ navType: 'teamWork', navText: '团队', isActive: currentNav === 'teamWork' });
        }
        /*编辑器判断*/
        let { isDesigner, GroupWordUser, user, isEB } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (isDesigner) {
            navList = [
                { navType: 'sourceMaterial', navText: '素材', isActive: currentNav === 'sourceMaterial' },
                { navType: 'specificWord', navText: '特效字', isActive: currentNav === 'specificWord' },
                { navType: "pic", navText: "图片", isActive: currentNav === "pic" },
                { navType: 'gallery', navText: '背景', isActive: currentNav === 'gallery' },
                { navType: "emojiPage", navText: "Emoji", isActive: currentNav === "emojiPage" },
                { navType: 'tableChart', navText: '表格图表', isActive: currentNav === 'tableChart' },
                { navType: 'groupWord', navText: '文案排版', isActive: currentNav === 'groupWord' },
                { navType: "wordartMaterial", navText: "艺术字", isActive: currentNav === "wordartMaterial" },
                { navType: 'specificWord3D', navText: '3D文字', isActive: currentNav === 'specificWord3D' },
                // { navType: 'copywriting', navText: '文案大全', isActive: currentNav === 'copywriting' },
                // { navType: 'animationEffect', navText: '页面动效', isActive: currentNav === 'animationEffect' },
                // { "navType": "teamWork", "navText": "团队", "isActive": currentNav === "teamWork" },
                // {"navType": "designer", "navText": "设计师", "isActive": currentNav === "designer"},
            ];
            // if( user.userId == 881894 || user.userId == 862711 || user.userId == 24259 || !user.DESIGNER_EDITOR_ASSET_USER ){
            if (user.DESIGNER_EDITOR_ASSET_USER) {
                // if( user.userId == 3296381 || user.userId == 30963 || user.userId == 2615443 || user.userId == 2626047 ){
                navList.push({ navType: 'designer', navText: '设计师', isActive: currentNav === 'designer' });
            }
        } else if (GroupWordUser) {
            // navList = [
            //     {"navType": "template", "navText": "模板", "isActive": currentNav === "template"},
            //     {"navType": "text", "navText": "文字", "isActive": currentNav === "text"},
            //     {"navType": "background", "navText": "背景", "isActive": currentNav === "background"},
            //     {"navType": "material", "navText": "素材", "isActive": currentNav === "material"},
            //     // {"navType":"element","navText":"元素", "isActive":currentNav==="element" },
            //     // {"navType":"SVG","navText":"图形","isActive":currentNav==="SVG"},
            //     // {"navType":"favorites","navText":"收藏","isActive":currentNav==="favorites"},
            //     {"navType": "designer", "navText": "设计师", "isActive": currentNav === "designer"},
            // ];
            navList = [
                // {"navType": "template", "navText": "模板", "isActive": currentNav === "template"},
                { navType: 'specificWord', navText: '特效字', isActive: currentNav === 'specificWord' },
                { navType: 'specificWord3D', navText: '3D文字', isActive: currentNav === 'specificWord3D' },
                { navType: 'SVG', navText: '线条形状', isActive: currentNav === 'SVG' },
                { navType: 'groupWord', navText: '文案排版', isActive: currentNav === 'groupWord' },
                // { "navType": "artWord", "navText": "艺术字", "isActive": currentNav === "artWord" },
                { navType: 'png', navText: 'PNG元素', isActive: currentNav === 'png' },
                // {"navType": "background", "navText": "背景", "isActive": currentNav === "background"},
                { navType: 'pic', navText: '图片', isActive: currentNav === 'pic' },
                { navType: 'designer', navText: '设计师', isActive: currentNav === 'designer' },
            ];
        }

        /* 电商编辑器面板 START */
        if (isEB) {
            navList = [
                { navType: 'template', navText: '模板', isActive: currentNav === 'template' },
                { navType: 'sourceMaterial', navText: '素材', isActive: currentNav === 'sourceMaterial' },
                { navType: 'specificWord', navText: '特效字', isActive: currentNav === 'specificWord' },
                { navType: 'pic', navText: '照片', isActive: currentNav === 'pic' },
                { navType: 'gallery', navText: '背景', isActive: currentNav === 'gallery' },
                { navType: 'uploadFile', navText: '上传', isActive: currentNav === 'uploadFile' },
                { navType: 'toolTik', navText: '工具', isActive: currentNav === 'toolTik' },
            ];
        }
        if (urlParames.origin && urlParames.origin != 'customize_index' && urlParames.origin != 'fixed_blank_index') {
            this.removeArray(navList, templates)
        }
        if (!isDesigner && (env.editor === 'ecommerce' || env.editor === 'ecommerceteam')) {
            navList = env.navList?.(currentNav);
        }

        /* 电商编辑器面板 END */

        /*新功能提示START*/
        let arr,
            reg = new RegExp('(^| )' + 'navTextNew' + '=([^;]*)(;|$)'),
            guideFlag = true;
        if ((arr = document.cookie.match(reg))) {
            if (arr[2] == 1) {
                guideFlag = false;
            }
        } else {
            let exp = new Date();
            exp.setTime(exp.getTime() + 12 * 30 * 24 * 60 * 60 * 1000);
            document.cookie = 'navTextNew=1;expires=' + exp.toGMTString();
        }
        /*新功能提示END*/

        let { picId } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }),
            { operationRecord } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.operationRecordRedux,
            }),
            urlProps = getProps();

        let newProblems = [
            {
                problem: '如何下载图片？',
                pvId: 812,
                answer: [{ part: '点击界面右上角【无水印下载】' }],
            },
            {
                problem: '如何抠图？',
                pvId: 814,
                answer: [
                    { part: '第一步：点击【实用工具】——【怪兽抠图工具】' },
                    { part: '第二步：上传要抠的图片' },
                    { part: '第三步：在保留区域画蓝色线，去除区域画红色线' },
                    { part: '第四步：等待自动抠图并下载' },
                ],
            },
            {
                problem: '文字无法选中编辑怎么办？',
                pvId: 811,
                answer: [
                    { part: '第一步：右键选择文字图层' },
                    { part: '第二步：移动文字图层' },
                    { part: '第三步：双击修改文字' },
                    { part: '第四步：把文字移回原处' },
                ],
            },
            {
                problem: '如何修改文字？',
                pvId: 807,
                answer: [
                    { part: '第一步：双击文字即可修改文字' },
                    { part: '第二步：右侧可以还选择字体，颜色，调整字间距等' },
                ],
            },
            {
                problem: '怎么让上传的图片底色融入背景？',
                pvId: 813,
                answer: [
                    { part: '第一步：在图怪兽使用抠图工具一键抠图' },
                    { part: '第二步：将抠好的图片上传到图片模板，即可融入背景' },
                ],
            },
            {
                problem: '如何修改图片尺寸？',
                pvId: 808,
                answer: [{ part: '第一步：右侧编辑尺寸' }, { part: '第二步：选择尺寸或自定义尺寸' }],
            },
            {
                problem: '如何上传图片？',
                pvId: 810,
                answer: [
                    { part: '第一步：点击左侧【上传】' },
                    { part: '第二步：上传图片，可上传电脑本地图片或手机里的图片' },
                ],
            },
            {
                problem: '如何替换图片？',
                pvId: 809,
                answer: [{ part: '第一步：双击图片' }, { part: '第二步：上传本地图片即可替换图片' }],
            },
            {
                problem: '好的模板如何收藏和查看？',
                pvId: 815,
                answer: [
                    { part: '第一步：点击首页模板列表的左上角【❤】即可收藏' },
                    { part: '第二步：点击右上角【头像】可查看收藏的模板' },
                ],
            },
        ];


        const { k1, k2 } = getProps(); // 物料下的对联不显示导航

        const { menu: { menuKey } = {} } = storeAdapter.getStore({ store_name: storeAdapter.store_names.appStore })

        const activeMenuClassName = this.props.isOpen && menuKey === 'uploadFile' ? 'active' : ''
        const hoverUploadFile = this.props.isHover && menuKey === 'uploadFile' ? 'hover' : ''

        return (
            <div className="navLayout" style={ this.props.isOpen ? { background: '#fff' } : undefined } onMouseLeave={this.props.onNavLayoutLeave}>
                {env?.customizeMenu && !isDesigner ? <CustomizeMenu isTeam={isTeam} onItemEnter={this.props.onItemEnter} isHover={this.props.isHover}></CustomizeMenu> :
                    <Nav userType={userType} guideFlag={guideFlag} navList={navList} />
                }
                {!isDesigner &&
                    <div className={`navCustomService ${this.props.isOpen ? 'open' : ''}`}>
                        <div
                            className={`uploadBtn ${activeMenuClassName} ${hoverUploadFile}`}
                            onMouseEnter={this.uploadFileMouseEnter.bind(this)}
                            onClick={this.showRightUploadFile.bind(this, activeMenuClassName)}
                        >
                            <i className="iconfont icon-bianjiqi-xin-zuocedaohangshangchuanmoren"></i>
                            <i className="iconfont icon-bianjiqi-xin-zuocedaohangshangchuanxuanting"></i>
                            <div className="uploadFont">上传</div>
                        </div>
                        {/* <div className="navHelpIcon" onClick={this.navHoverCustomIsShowClickEvent.bind(this)}>
                        <a
                            className="navTextHelp"
                            target="_blank"
                            href="https://818ps.com/apiv2/udesk"
                            onClick={this.toCustomPageClickEvent.bind(this)}
                        >
                            联系客服
                        </a>
                    </div> */}
                        {false && sideHelpBox && (
                            <div className="navHoverCustom" onClick={this.stopPropagationEvent.bind(this)}>
                                <div className="navHoverHelpCemter">
                                    <div className="helpCenterHeader">帮助中心</div>
                                    {newProblems.map((item, index) => {
                                        return (
                                            <div className="helpCenterPart" key={index}>
                                                <div
                                                    className="helpCenterProblem"
                                                    onClick={
                                                        item.problem != helpProblem || helpProblem == ''
                                                            ? this.showItemAnswerClickEvent.bind(
                                                                this,
                                                                item.problem,
                                                                item.pvId,
                                                            )
                                                            : this.hideItemAnswerClickEvent.bind(this, item.problem)
                                                    }
                                                >
                                                    {item.problem}
                                                    {item.problem != helpProblem || helpProblem == '' ? (
                                                        <i className="iconfont icon-xiala-copy"></i>
                                                    ) : (
                                                        <i className="iconfont icon-xiangshang-copy"></i>
                                                    )}
                                                </div>
                                                <div className="helpCenterAnswer">
                                                    {item.problem == helpProblem &&
                                                        item.answer.map((newItem, newIndex) => {
                                                            return (
                                                                <div className="helpAnswer" key={newIndex}>
                                                                    {newItem.part}
                                                                </div>
                                                            );
                                                        })}
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                                <div className="customOnline">
                                    <a
                                        className="customOnlineButton"
                                        target="_blank"
                                        href="https://818ps.com/apiv2/udesk"
                                        onClick={this.toCustomPageClickEvent.bind(this)}
                                    >
                                        <i className="icon iconfont icon-kefu1"></i>
                                        在线客服
                                    </a>
                                </div>
                            </div>
                        )}
                    </div>}
            </div>
        );
    }
}

class ToolLayout extends PureComponent {

    hideStyle = {
        top: 0,
        bottom: 0,
        borderRight: '1px solid #E9E8E8',
        height: 'calc(100vh - 57px)',
        display: 'none',
        animation: `toolLayoutHide 150ms ease`,
        borderRadius: '0px 16px 16px 0px',
        zIndex: 10,
    }

    hoverHideStyle = {
        top: 0,
        bottom: 50,
        borderRadius: 16,
        boxShadow: '11px 0px 10px 0px rgba(0, 0, 0, 0.10), 0px -6px 10px 0px rgba(0, 0, 0, 0.05), 0px 6px 10px 0px rgba(0, 0, 0, 0.10)',
        height: 'calc(100vh - 70px - 57px)',
        display: 'none',
        animation: `toolLayoutHide 150ms ease`,
        zIndex: 10,
    }

    hoverStyle = {
        top: 0,
        bottom: 50,
        borderRadius: 16,
        boxShadow: '11px 0px 10px 0px rgba(0, 0, 0, 0.10), 0px -6px 10px 0px rgba(0, 0, 0, 0.05), 0px 6px 10px 0px rgba(0, 0, 0, 0.10)',
        height: 'calc(100vh - 70px - 57px)',
        zIndex: 30,
        animation: `toolLayoutShow 150ms ease`,
    }

    openStyle = {
        top: 0,
        bottom: 0,
        borderRight: '1px solid #E9E8E8',
        height: 'calc(100vh - 57px)',
        // zIndex: 30,
        animation: `toolLayoutShow 150ms ease`,
        borderRadius: '0px 16px 16px 0px',
    }

    oldIsOpen = false;
    oldIsHover = false;

    render() {
        const {isDesigner, currentNav, isHover, isOpen } = this.props
        let style
        if (isOpen) {
            style = this.openStyle
        } else if (isHover) {
            style = this.hoverStyle
        } else {
            style = this.oldIsOpen ? this.hideStyle : this.hoverHideStyle
        }
        if(currentNav === ETool.AI_CHAT) {
            style = {
                ...style,
                overflow: 'inherit',
            }
        }
        this.oldIsOpen = isOpen;
        this.oldIsHover = isHover;

        return (
            <div className={`toolLayout`} style={style}>
                {!isDesigner && <ToolContent currentNav={currentNav} />}
                {isDesigner && <DesigerToolContent currentNav={currentNav} />}
            </div>
        );
    }
}


class RightLayout extends PureComponent {
    isHidingResourcePanel = false;

    hoverStyle = {
        width: 72,
    }

    openStyle = {
        width: 72 + 360,
        boxShadow: '0px 0px 4.8px 0px rgba(0, 0, 0, 0.15)',
        borderRadius: '0px 16px 16px 0px',
    }

    keepHover = false;
    keepHoverEmitter = null;

    hoverTimer = null;

    constructor(props) {
        super(props);

        const tempProps = getProps()

        this.state = {
            hideBtnI: 'icon-you-copy',
            hideBtnStatus: {
                isVisible: false,
                text: '',
            },
            rightLayoutStyle: {
            },
            isDropdownBox: false,
            prevNavType: '',
            isShowResourcePanel: false,
            isShowNavigatorTemplate: false,
            isHover: tempProps.menu ? false : false,
            isOpen: false,
        };

        this.hideClickEvent = this.hideClickEvent.bind(this);

        this.hideClickEmitter();

        this.keepHoverEmitter = emitter.addListener('keepHover', (status) => {
            this.keepHover = status;
        })
    }

    hideClickEmitter() {
        let th = this;

        if (this.rightLayoutHideClickEmitter) {
            this.rightLayoutHideClickEmitter.remove();
        }
        this.rightLayoutHideClickEmitter = emitter.addListener(
            'rightLayoutHideClick',
            (status = '', origin = '', clickOrigin, endShrink) => {
                if (this.hoverTimer) {
                    clearTimeout(this.hoverTimer);
                }
                // status 目前接受参数 hide | open | hover , open表示面板打开
                // origin 目前接受参数 initial(仅didMount触发)
                // clickOrigin 目前只有collection 只有点击收藏自动弹出
                const { isShowResourcePanel } = this.state;
                // 防止右侧动效菜单栏点击 左侧菜单栏显示
                if (endShrink === 'endShrink' && !isShowResourcePanel) {
                    return
                }
                if (typeof endShrink === 'undefined' && !isShowResourcePanel) {
                    if (origin === 'animationEffect') {
                        const pageAniDom = document.getElementById('right-page-Animation')
                        if (pageAniDom.style.right === '260px') {
                            pageAniDom.style.right = 0
                        }
                    }
                }
                if (status === 'hover') {
                    this.keepHover = true
                }
                let open = true
                if (status === 'hide' || isShowResourcePanel && this.state.prevNavType === origin && status !== 'open' && this.state.isOpen) {
                    this.setPanel(false, false, origin, clickOrigin, status)
                    open = false
                } else {
                    this.setPanel(true, false, origin, clickOrigin, status)
                    open = true
                }
                // 新增查看当前菜单的展开还是关闭逻辑
                emitter.emit('changeMenuItemLayout', { currentNav: origin, open });
                if (origin !== '') {
                    this.setState({ prevNavType: origin });
                }
                emitter.emit('UserUploadFilePhUpdateIsShow', false);
                emitter.emit('materialAssetDidMount');
            },
        );
    }

    hideClickEvent() {
        const { isShowResourcePanel } = this.state;
        if (isShowResourcePanel) {
            this.setPanel(false, false, 'hideBtn')
            emitter.emit('UserUploadFilePhUpdateIsShow', false);
            // 取消菜单选中状态
            emitter.emit('changeActivedMenuId', undefined)
            // restore current menu
            storeAdapter.dispatch({
                fun_name: 'updateApp',
                store_name: storeAdapter.store_names.appStore,
                params: [{
                    type: 'updateAppMenu',
                    params: {
                        menu: {
                            menuKey: undefined
                        }
                    }
                }]
            })
        } else {
            // 默认展开模版panel
            emitter.emit('changeActivedMenuId', '17')
            emitter.emit('onNavChanged', 'template');
            emitter.emit('rightLayoutHideClick', '', 'template', undefined);
            // this.setPanel(true, false)
            assetManager.setPv_new(6807);
        }
    }

    handleHideBtnHoverEvent = (bool) => {
        this.setState({
            hideBtnStatus: {
                isVisible: bool,
                text: this.state.isShowResourcePanel ? '关闭侧边栏' : '开启侧边栏',
            },
        });
    };

    showResourcePanel(origin, clickOrigin, status) {
        if (!this.state.isShowResourcePanel) {
            origin !== 'initial' &&
                assetManager.setPv_new(158, {
                    additional: { isActive: 2, sl: clickOrigin ? clickOrigin : undefined },
                });
        }
        const canvasBackToTop = document.getElementsByClassName('backToTop');
        const canvasDropdownBox = document.getElementsByClassName('dropdownBox');
        const { rightLayoutStyle } = this.state;
        let width = status === 'hover' ? this.hoverStyle.width : this.openStyle.width;

        this.setState({
            isShowResourcePanel: true,
            isShowNavigatorTemplate: false,
            isOpen: status !== 'hover' ? true : false,
            isHover: status === 'hover' ? true : false,
            rightLayoutStyle: {
                ...rightLayoutStyle,
                width,
            }
        })
        canvasDropdownBox[0] ? (canvasDropdownBox[0].style.display = 'block') : null;
        canvasBackToTop[0] ? (canvasBackToTop[0].style.display = 'block') : null;
        return width;
    }

    hideResourcePanel(origin, clickOrigin) {
        if (!this.isHidingResourcePanel && this.state.isShowResourcePanel) {
            // 处理存在的单次收起菜单 多次调用的情况
            if (origin !== 'initial') {
                assetManager.setPv_new(4028);
            } else {
                assetManager.setPv_new(158, {
                    additional: { isActive: 1, sl: clickOrigin ? clickOrigin : undefined },
                });
            }
        }
        this.isHidingResourcePanel = true;
        const navLayoutDom = document.getElementsByClassName('navLayout');
        const canvasBackToTop = document.getElementsByClassName('backToTop');
        const canvasDropdownBox = document.getElementsByClassName('dropdownBox');
        const { rightLayoutStyle } = this.state;
        // const width = navLayoutDom[0].offsetWidth + 'px';
        const width = this.hoverStyle.width;

        this.setState({
            isShowResourcePanel: false,
            isOpen: false,
            isHover: origin === 'hideBtn' ? false : true,
            rightLayoutStyle: {
                ...rightLayoutStyle,
                width,
            }
        }, () => {
            this.isHidingResourcePanel = false
        })
        canvasDropdownBox[0] ? (canvasDropdownBox[0].style.display = 'none') : null;
        canvasBackToTop[0] ? (canvasBackToTop[0].style.display = 'none') : null;
        return width;
    }

    setPanel = (isShowResourcePanel, isShowNavigatorTemplate, origin, clickOrigin, status) => {
        let resoureWidth = 0;
        if (typeof isShowResourcePanel === 'boolean'
            && typeof isShowNavigatorTemplate === 'boolean'
        ) {
            const width = isShowResourcePanel ? this.showResourcePanel(origin, clickOrigin, status) : this.hideResourcePanel(origin, clickOrigin)
            resoureWidth = Number.parseInt(width)
            this.props.setResourcePanelWidth(Number.parseInt(width))
        }
        if (typeof isShowResourcePanel === 'boolean'
            && typeof isShowNavigatorTemplate === 'boolean'
            && !isShowResourcePanel
        ) {
              this.setState({isShowNavigatorTemplate})
            const templateWidth = isShowNavigatorTemplate ? 168 + 72 : 72;
              if (templateWidth!==resoureWidth) {
                this.props.setResourcePanelWidth(isShowNavigatorTemplate ? 168 + 72 : 72)
            }
        }
    }

    onNavItemEnter = (navType, menuId, menuName) => {
        if (!this.state.isOpen) {
            clearTimeout(this.hoverTimer);
            this.hoverTimer = setTimeout(() => {
                if (!this.state.isHover) {
                    this.setState({
                        isHover: true,
                    })
                }
                if (menuId) {
                    assetManager.setPv_new(9028, {
                        additional: { s0: menuId, s1: menuName },
                    })
                }
                emitter.emit('onNavChanged', navType);
            }, 300);
        }
        this.keepHover = false;
    }

    onMenuLayoutLeave = () => {
        if (this.state.isHover && !this.keepHover) {
            this.setState({
                isHover: false,
            })
        }
        if (this.hoverTimer) {
            clearTimeout(this.hoverTimer);
        }
    }

    onNavLayoutLeave = () => {
        if (this.hoverTimer) {
            clearTimeout(this.hoverTimer);
        }
    }

    onClickOutside = (e) => {
        if (this.keepHover && this.state.isHover && !this.state.isOpen) {
            this.setState({
                isHover: false,
            })
            this.keepHover = false;
        }
    }

    componentWillUnmount() {
        this.rightLayoutHideClickEmitter.remove();
    }

    render() {
        let { hideBtnStatus, isDropdownBox, isShowResourcePanel, isHover, isOpen } = this.state;

        const rightLayoutStyle = isOpen ? this.openStyle : this.hoverStyle;

        return (
            <ClickOutside onClickOutside={this.onClickOutside}>
            <div className="rightLayout" ref="rightLayout" style={rightLayoutStyle} onMouseLeave={this.onMenuLayoutLeave}>
                {isOpen && <div
                    className="hideBtn"
                    ref="hideBtn"
                    onClick={this.hideClickEvent}
                    onMouseEnter={() => {
                        this.handleHideBtnHoverEvent(true);
                    }}
                    onMouseLeave={() => {
                        this.handleHideBtnHoverEvent(false);
                    }}
                >
                    <i
                        className={'iconfont icon-xiangyou'}
                        style={{ color: '#202020', transform: !isShowResourcePanel ? '' : 'rotate(180deg)' }}
                    ></i>
                    <div className="popover">{hideBtnStatus.text}</div>
                </div>}

                <NavLayout
                    key="NavLayout"
                    currentNav={this.props.currentNav}
                    userType="designer"
                    setResourcePanelWidth={this.props.setResourcePanelWidth}
                    setPanel={this.setPanel}
                    onItemEnter={this.onNavItemEnter}
                    isOpen={isOpen}
                    isHover={isHover}
                    onNavLayoutLeave={this.onNavLayoutLeave}
                />
                <ToolLayout
                    key="ToolLayout"
                    currentNav={this.props.currentNav}
                    isDesigner={this.props.isDesigner}
                    isHover={isHover}
                    isOpen={isOpen}
                />

                <OnlineRetailerMainDrawing />
            </div>
            </ClickOutside>
        );
    }
}
const mapStateRightLayoutProps = ((state) => {
    return {
        toolPanel: state.onCanvasPainted.toolPanel,
        work: state.onCanvasPainted.work,
    };
})

storeHOC(mapStateRightLayoutProps, RightLayout)

/**
 * 消息弹出框
 */
class PromptBox extends Component {
    constructor(props) {
        super(props);
        this.onCloseButtonClicked = this.onCloseButtonClicked.bind(this);
        this.state = {
            isShow: this.props.isShow,
            windowContent: '',
            // popupWidth: 300,
            // popupHeight: 70,
            isEnter: false,
            promptBoxContentStyle: {}
        };
        this.init();
    }

    init() {
        this.PromptBoxEmitter = emitter.addListener('PromptBox', (windowInfo) => {
            if (typeof windowInfo.style == 'undefined') {
                Object.assign(windowInfo, {
                    style: {},
                });
            }
            this.setState({
                isShow: true,
                windowContent: windowInfo.windowContent,
                popupWidth: windowInfo.popupWidth,
                popupHeight: windowInfo.popupHeight,
                style: windowInfo.style,
                promptBoxContentStyle: windowInfo.promptBoxContentStyle

            });
        });
        this.PromptBoxCloseEmitter = emitter.addListener('PromptBoxClose', () => {
            this.onCloseButtonClicked();
        });
    }

    componentWillUnmount() {
        // emitter.removeListener(this.popupWindow);
        this.PromptBoxEmitter.remove();
        this.PromptBoxCloseEmitter.remove();
    }

    /**
     * 鼠标在提示框上一直显示
     * @param e
     */
    promptBoxMouseEnter(e) {
        this.setState({
            isEnter: true,
        });
    }

    /**
     * 鼠标离开提示框关闭
     * @param e
     */
    promptBoxMouseLeave(e) {
        this.setState({
            isEnter: false,
        });
    }

    onCloseButtonClicked() {
        this.setState({ isShow: false });
    }

    componentDidUpdate() {
        let { isShow, isEnter } = this.state;
        if (isEnter) {
            isShow = true;
        }

        if (isShow) {
            this.isCloseFlag = 1;
        }

        if (this.isCloseFlag && !isShow) {
            this.isCloseFlag = 0;
            if (this.closeTimeout) {
                clearTimeout(this.closeTimeout);
            }
            this.closeTimeout = setTimeout(() => {
                this.setState({});
                // this.forceUpdate();
            }, 500);
        }
    }

    render() {
        let { isShow, isEnter, popupHeight, popupWidth, style } = this.state,
            promptBoxStyle = {
                ...style,
                top: '0px',
            };

        if (isEnter) {
            isShow = true;
        }
        // const windowStyle = {
        //     width: this.state.popupWidth,
        //     height: this.state.popupHeight,
        //     left: (window.innerWidth-this.state.popupWidth)/2,
        //     top: this.state.popupHeight > 0 ? (window.innerHeight-this.state.popupHeight)/3 : 100
        // };
        // const maskStyle = {
        //     width: window.innerWidth,
        //     height: window.innerHeight
        // }

        if (!this.isCloseFlag && !isShow) {
            Object.assign(promptBoxStyle, {
                top: '-1000000px',
            });
        }
        // let displayStyle = {display: "none"};
        // if(this.state.isShow===true) {
        //     displayStyle = {display: "block"};
        // }
        // if(this.state.isShow===false) {
        //     displayStyle = {display: "none"};
        // }
        //
        // Object.assign(windowStyle, this.state.style)

        return (
            <div
                className={isShow ? 'promptBox active' : 'promptBox'}
                ref=""
                style={promptBoxStyle}
                onMouseEnter={this.promptBoxMouseEnter.bind(this)}
                onMouseLeave={this.promptBoxMouseLeave.bind(this)}
            >
                <div className="promptBoxContent" style={Object.assign({
                    width: popupWidth,
                    height: popupHeight
                }, this.state.promptBoxContentStyle)}>{this.state.windowContent}</div>
            </div>
        );
    }
}

/*弹出窗popup*/
class Popup extends Component {

    // 事件触发回调方法
    eventCallBack = {};

    constructor(props) {
        super(props);
        // this.onCloseButtonClicked = this.onCloseButtonClicked.bind(this);
        this.state = {
            isShow: this.props.isShow,
            windowContent: '',
            popupWidth: 640,
            popupHeight: 480,
            popupTitleBarStyle: {},
            popupBodyStyle: {},
            windowStyleU: {},
            isLimitModal: false,
            isDownLoadSuccess: false,
            isUnVip: false,
            popCloseBtn: true,
            closePopupPvNew: undefined,
            popWrapperClassName: '',
            clickMask: false
        };

        this.updateWindowStyleEmitter();
        this.updateWindowClassEmitter();
        this.closePopupBtnEmitter();
    }

    /**
     * 设置时间监听
     * @param {*} eventName 
     * @param {*} handler 
     * @returns 
     */
    addListener(eventName, handler) {
        try {
            if (this.eventCallBack === undefined) {
                this.eventCallBack = {};
            }
            const eventCallBack = this.eventCallBack;
            if (eventCallBack && eventCallBack[eventName] === undefined) {
                eventCallBack[eventName] = [];
            }

            eventCallBack[eventName].push(handler);

        } catch (error) {
            console.error(error);
            return;
        }

        return this;
    }

    /**
     * 注销事件监听
     */
    removeListener(eventName, handler) {
        try {
            const eventCallBack = this.eventCallBack;
            if (eventCallBack[eventName]) {
                const tIndex = eventCallBack[eventName].indexOf(handler);
                if (tIndex !== -1) {
                    eventCallBack[eventName].splice(tIndex, 1);
                }
            }
        } catch (error) {
            console.error(error);
            return;
        }

        return this;
    }

    /**
     * 清空所有监听事件
     */
    clearAllListener() {
        this.eventCallBack = {};
    }

    /**
     * 触发知道时间监听
     */
    fire(eventName, option) {
        const eventCallBack = this.eventCallBack;
        if (eventCallBack && eventCallBack[eventName]) {
            eventCallBack[eventName].forEach((item) => {
                item(option);
            });
        }

        return this;
    }

    componentDidMount() {
        this.popupWindow = emitter.addListener('popupWindow', (windowInfo) => {
            // 事件监听设置 STRAT
            this.clearAllListener();
            if (windowInfo.listener != undefined) {
                // 设置关闭监听
                if (windowInfo.listener.close) {
                    this.addListener('close', windowInfo.listener.close);
                }

            }
            // 事件监听设置 END

            HotKeyHelper.updateDisableHotKey({ isDisableHotKey: true })
            if (typeof windowInfo.style == 'undefined') {
                Object.assign(windowInfo, {
                    style: {},
                });
            }
            this.setState({
                isShow: true,
                isUnVip: windowInfo.isUnVip,
                windowContent: windowInfo.windowContent,
                popupWidth: windowInfo.popupWidth,
                popupHeight: windowInfo.popupHeight,
                style: windowInfo.style,
                background: windowInfo.background,
                // popupTitleBarStyle: windowInfo.popupTitleBarStyle ? windowInfo.popupTitleBarStyle : this.state.popupTitleBarStyle,
                popupTitleBarStyle: windowInfo.popupTitleBarStyle ? windowInfo.popupTitleBarStyle : {},
                popupBodyStyle: windowInfo.popupBodyStyle ? windowInfo.popupBodyStyle : this.state.popupBodyStyle,
                windowStyleU: {},
                isDownLoadSuccess: !!windowInfo.isDownLoadSuccess,
                closePopupPvNew: windowInfo.closePopupPvNew,
                clickMask: !!windowInfo.clickMask,
                popWrapperClassName: windowInfo.popWrapperClassName // 增加可传入唯一性的类目，方便样式复写
            });
        });
        this.popupClose = emitter.addListener('popupClose', (isTouch) => {
            this.onCloseButtonClicked(isTouch);
        });

        this.popupAddListener = emitter.addListener('popupAddListener', (eventName, handler) => {
            this.addListener(eventName, handler);
        });

        this.popupRemoveListener = emitter.addListener('popupRemoveListener', (eventName, handler) => {
            this.removeListener(eventName, handler);
        });

    }

    updateWindowStyleEmitter() {
        this.PopupUpdateWindowStyleEmitter = emitter.addListener('PopupUpdateWindowStyle', (style, isLimit = false) => {
            //isLimit:是否是受限制弹窗
            this.setState({
                windowStyleU: style,
                isLimitModal: isLimit,
            });
        });
    }

    updateWindowClassEmitter() {
        this.PopupUpdateWindowClassEmitter = emitter.addListener('PopupUpdateWindowClass', (style) => {
            //isLimit:是否是受限制弹窗
            this.setState({
                windowContent: style.windowContent,
            });
        });
    }

    componentWillUnmount() {
        emitter.emit('delHotKeyListener');
        emitter.emit('addHotKeyListener');
        // emitter.removeListener(this.popupWindow);
        this.popupWindow.remove();
        this.popupClose.remove();
        this.popupAddListener?.remove()
        this.popupRemoveListener?.remove()
        // this.popupHitTip.remove();
        this.PopupUpdateWindowStyleEmitter.remove();
        this.PopupUpdateWindowClassEmitter.remove();
        this.closePopupBtnListener.remove();
    }

    onCloseButtonClicked(isTouch) {
        try {
            this.fire('close');
            if (this.state.windowContent?.props?.showFlag === 2){
                emitter.emit('satisfactionSurvey');
            }
        } catch (error) {
            console.error('弹窗关闭回调', error);
        }

        sessionStorage.setItem('downloadTimer', Date.now())
        HotKeyHelper.updateDisableHotKey({ isDisableHotKey: false })
        const { isUnVip, closePopupPvNew, isDownLoadSuccess } = this.state;
        if (isUnVip) {
            assetManager.setPv_new(3115, {
                additional: {
                    s0: 'edit-center',
                },
            });
        }
        if (closePopupPvNew && typeof closePopupPvNew === 'number') {
            assetManager.setPv_new(closePopupPvNew)
        }
        /* 记录用户关闭次数 */
        /*
        {
            time:'',
            isPopEd : false
        }
        */
        // 判断是否有分享弹窗弹出
        let show_share = function () {
            let show_share_popup = new Object();
            let date = new Date();
            let load_share_popup = JSON.parse(localStorage.getItem('ue_show_share_popup'));
            show_share_popup.set_time =
                date.getDate().toString() + date.getMonth().toString() + date.getFullYear().toString();
            if (load_share_popup) {
                return Boolean(show_share_popup.set_time === load_share_popup.set_time);
            }
            return false;
        };
        if (this.state.isLimitModal && show_share()) {
            //是受限制弹窗
            let localDatas = localStorage.record_store_site_modal || '{}';
            let parseLocals = JSON.parse(localDatas);
            if (parseLocals.time) {
                //已存在
                let oneDay = 86400000; //ms
                let nowTime = new Date().getTime();
                let newLocal = { ...parseLocals };
                if (!parseLocals.isPopEd || nowTime - parseLocals.time >= oneDay) {
                    //第二次 或者是超过1天
                    if (!parseLocals.isPopEd) {
                        newLocal['isPopEd'] = true;
                    }
                    if (nowTime - parseLocals.time >= oneDay) {
                        newLocal['time'] = nowTime;
                        newLocal['isPopEd'] = false;
                    }
                    emitter.emit('ShowStoreOurWebsiteDialog');
                }
                localStorage.record_store_site_modal = JSON.stringify(newLocal);
            } else {
                //新建
                localStorage.record_store_site_modal = JSON.stringify({ time: new Date().getTime(), isPopEd: false });
            }
        }
        /* 记录用户关闭次数 */
        let newState = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (!this.state.windowContent.props) {
            return;
        }
        if (this.state.windowContent.props.className === 'DownloadPopup' && newState.isDownloadFlag === 1) {
            CanvasPaintedLogic.updateIsDownloadFlag({ isDownloadFlag: 0 });
            // canvasStore.dispatch(paintOnCanvas('UPDATE_ISDOWNLOADFLAG', { isDownloadFlag: 0 }))
            emitter.emit('InfoBarclearInterval');
            let { startDownloadTime } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            assetManager.setDownloadPv_new(92, (new Date().getTime() - startDownloadTime) / 1000, { s0: 1 });
        }
        if (this.state.windowContent.props.className === 'DownloadLimitPopup' && newState.isDownloadFlag === 1) {
            assetManager.setPv_new(4044);
        }
        if (this.state.windowContent.props.className === 'cutOutFaile') {
            assetManager.setPv_new(5022);
        }
        if (
            this.state.windowContent.props.className === 'DownloadPopup' &&
            newState.isDownloadFlag === 2 &&
            isTouch !== false
        ) {
            assetManager.setDownloadPv_new(475);
        }
        if (this.state.windowContent.props.className === 'DownloadPopup') {
            let { jobId } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            if (jobId) {
                assetManager.asyncCloseDownload().then((data) => {
                    data.json().then((resultData) => { });
                });
            }
        }
        if (this.state.windowContent.props.className === 'DownloadPopup' && newState.isDownloadFlag === 70) {
            emitter.emit('DownloadPopup_button_close');
        }
        if (isDownLoadSuccess) {
            emitter.emit('DownLoadSuccessModalClose');
        }
        emitter.emit('addHotKeyListener');
        this.setState({ isShow: false });
    }

    onMouseEnterEvent() {
        assetManager.setPv_new(3177, {
            additional: {
                s0: 'edit-center',
            },
        });
    }

    onMouseLeaveEvent() {
        assetManager.setPv_new(3128, {
            additional: {
                s0: 'edit-center',
            },
        });
    }

    closePopupBtnEmitter() {
        this.closePopupBtnListener = emitter.addListener('closePopupBtn', (status = true) => {
            this.setState({ popCloseBtn: status });
        });
    }
    closeRatifyPopUp() {
        if (this.state.windowContent.props.className === 'RatifyPopUp') {
            emitter.emit('popupClose');
        }
    }

    closePopup = () => {
        const { clickMask } = this.state;
        if (clickMask) {
            emitter.emit('popupClose');
        }
    }

    render() {
        let { popupTitleBarStyle, popupBodyStyle, windowStyleU, isUnVip, isDownLoadSuccess, popCloseBtn } = this.state;

        let windowStyle = {
            width: this.state.popupWidth,
            height: this.state.popupHeight,
            left: (window.innerWidth - this.state.popupWidth) / 2,
            top: this.state.popupHeight > 0 ? (window.innerHeight - this.state.popupHeight) / 3 : 30,
        };
        if (isDownLoadSuccess) {
            windowStyle.top = windowStyle.top + 100;
        }

        // const maskStyle = {
        //     width: window.innerWidth,
        //     height: window.innerHeight
        // }
        const maskStyle = {
            width: '100%',
            height: '100%',
            background: this.state.background,
        };
        let displayStyle = { display: 'none' };
        if (this.state.isShow === true) {
            displayStyle = { display: 'block' };
        }
        if (this.state.isShow === false) {
            displayStyle = { display: 'none' };
        }

        Object.assign(windowStyle, this.state.style);
        Object.assign(windowStyle, windowStyleU);

        return (
            <div className={`popupWrapper ${this.state.popWrapperClassName}`} style={displayStyle} onClick={this.closeRatifyPopUp.bind(this)}>
                <div className="popupMask" style={maskStyle} onClick={this.closePopup}></div>
                <div
                    className="popupWindow"
                    onMouseEnter={isUnVip ? this.onMouseEnterEvent.bind(this) : () => { }}
                    onMouseLeave={isUnVip ? this.onMouseLeaveEvent.bind(this) : () => { }}
                    style={windowStyle}
                >
                    <div className="popupTitleBar" style={popupTitleBarStyle}>
                        {/* <i className="icon iconfont icon-shanchu1" onClick={this.onCloseButtonClicked.bind(this)}></i> */}
                        {/* <div className="ysL"></div> */}
                        {(
                            <div
                                className="iconfont icon-quxiao closePopupBtn"
                                onClick={this.onCloseButtonClicked.bind(this)}
                            ></div>
                        )}
                    </div>
                    <div className="popupBody" style={popupBodyStyle}>
                        {this.state.isShow && this.state.windowContent}
                    </div>
                </div>
            </div>
        );
    }
}



/*赚取画布（鼠标移动画布）*/
class CanvasGrab extends Component {
    constructor(props) {
        super(props);

        this.state = {
            isShow: false,
        };

        this.updateIsShowEmitter();
    }

    /**
     * 画布抓取（点击事件）
     * @param e
     */
    canvasGrabMouseDownEvent(e) {
        emitter.emit('canvasContentCanvasGrabMove', e);
    }

    updateIsShowEmitter() {
        let th = this;
        this.canvasGrabUpdateIsShowEmitter = emitter.addListener('canvasGrabUpdateIsShow', (isShow) => {
            th.setState({
                isShow: isShow,
            });
        });
    }

    componentWillUnmount() {
        this.canvasGrabUpdateIsShowEmitter.remove();
    }

    render() {
        let { isShow } = this.state,
            canvasGrabStyle = {
                display: 'none',
            };

        if (isShow) {
            Object.assign(canvasGrabStyle, {
                display: 'inline-block',
            });
        }

        return (
            <div id="canvasGrab" style={canvasGrabStyle} onMouseDown={this.canvasGrabMouseDownEvent.bind(this)}></div>
        );
    }
}
const mapStateHoverToProps = ((state) => {
    return {
        rt_current_is_moving_asset: state.onCanvasPainted.rt_current_is_moving_asset,
        rt_current_is_drag_asset: state.onCanvasPainted.rt_current_is_drag_asset,
        rt_current_is_rotate_asset: state.onCanvasPainted.rt_current_is_rotate_asset
    };
})
class AssetHoverTips extends Component {
    constructor(props) {
        super(props);

        this.state = {
            pos: {
                left: -1000,
                top: -1000,
            },
            otherPos: {
                left: -1000,
                top: -1000,
            },
            text: '双击替换图片',
            otherText: ''
        };

        this.updatePosEmitter();
    }

    updatePosEmitter() {
        let th = this;
        this.updatePosEmitter = emitter.addListener('AssetHoverTipsUpdatePos', (pos, text) => {
            th.setState({
                pos: pos,
                text: text,
            });
        });
        this.updatePosEmitterOther = emitter.addListener('AssetHoverTipsUpdatePosOther', (pos, text) => {
            th.setState({
                otherPos: pos,
                otherText: text,
            });
        });
    }

    componentWillUnmount() {
        this.updatePosEmitter.remove();
        this.updatePosEmitterOther.remove()
    }

    render() {
        let { pos, text, otherPos, otherText } = this.state;
        const { rt_current_is_moving_asset, rt_current_is_drag_asset, rt_current_is_rotate_asset, toolPanel } = storeAdapter.getStore({

            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const { isCutouting, isCutouted } = isCutout(toolPanel?.asset?.attribute)
        const isCutoutStatus = isCutouting || isCutouted
        let assetHoverTipsStyle = {
            top: pos.top + 'px',
            left: pos.left + 'px',
            zIndex: 999999999,
        };
        let assetHoverTipsOtherStyle = {
            top: otherPos.top + 'px',
            left: otherPos.left + 'px',
            zIndex: 999999999,
        };
        if (rt_current_is_moving_asset || rt_current_is_drag_asset || rt_current_is_rotate_asset) {
            return (<div></div>)
        }
        return (
            <>
                {isCutoutStatus ? null : <div id="assetHoverTips" style={assetHoverTipsStyle}>
                    {typeof text === 'string' ? <div className="tipContent">{text}</div> : text}
                </div>}
                <div id="assetHoverTipsOther" style={assetHoverTipsOtherStyle}>
                    {typeof otherText === 'string' ? <div className="tipContent">{otherText}</div> : otherText}
                </div>
            </>
        );
    }
}
storeHOC(mapStateHoverToProps, AssetHoverTips)
/*设计工具设计师版*/
class DesignerLayout extends PureComponent {
    diffWidthTimer
    safeDistance = 60
    constructor(props) {
        super(props);
        let tempProps = getProps(),
            currentNav = 'background';

        currentNav = 'template';
        let resourceWidth = 72;
        let urlParams = this.urlParametric(window.location.search)
        this.isNew = (urlParams && (urlParams.origin == 'customize_index' || urlParams.origin == 'fixed_blank_index')) ? true : false
        this.isUeEditor = env.editor == 'ue';
        this.contentWrapRef = React.createRef();
        // if (this.isUeEditor) {
        //     resourceWidth = 72 + 360;
        // }
        if (tempProps.picId > 0) {
            currentNav = 'specificWord';
            if (this.isUeEditor) {
                currentNav = 'template'
            }
        } else if (tempProps.assetId > 0) {
            // currentNav = 'element';
        } else if (tempProps.w > 0 && tempProps.h > 0) {
            // currentNav = 'template';
        }
        if (tempProps.isDesigner) {
            currentNav = 'sourceMaterial';
        } else if (tempProps.GroupWordUser) {
            // currentNav = 'template';
            currentNav = 'specificWord';
        } else if (tempProps.is_eb) {
            // 电商编辑器默认打开工具
            currentNav = 'specificWord';
            // currentNav = 'template';
        }

        if (this.isNew) {
            currentNav = 'template';
        }
        // TODO 处理电商和课程表
        // 电商详情临时
        if (tempProps['k2'] == 176 || tempProps['k2'] == 177 || tempProps['k2'] == 212 || tempProps['k2'] == 213) {
            // canvasStore.dispatch(paintOnCanvas('MUTISIZE_SET_IS_DETAIL_PAGE', { bool: true }));
            CanvasPaintedLogic.setfloorCutting({ rt_is_online_detail_page: true });
        }

        // 课程表详情
        if (tempProps.class_id == '1122') {
            tempProps.k1 = '3';
            // canvasStore.dispatch(paintOnCanvas('MUTISIZE_SET_IS_DETAIL_PAGE', { bool: true }));
            CanvasPaintedLogic.setfloorCutting({ rt_is_online_detail_page: true });
        }

        // 地址栏menu参数定位到上传菜单
        if(tempProps.menu === 'uploadFile'){
            currentNav = 'uploadFile'
        }

        let href = window.location.href;
        let query = href.substring(href.indexOf('?') + 1);
        let vars = query.split('&');
        let obj = {};

        for (var i = 0; i < vars.length; i++) {
            let pair = vars[i].split('=');
            obj[pair[0]] = pair[1];
        }

        if (obj.is_second) {
            let { info } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.InfoManage,
            });
            const newInfo = Object.assign({}, info);
            newInfo.kid_1 = Number(obj.k1);
            newInfo.kid_2 = Number(obj.k2);
            newInfo.is_second = Number(obj.is_second);
            newInfo.task_id = Number(obj.task_id);
            InfoManageHelper.updateInfo(newInfo);
        }

        // 电商详情临时 end
        this.state = {
            currentNav: currentNav,
            imgLoading: true,
            resourcePanelWidth: resourceWidth,
            toolPanelWidth: 0,
            diffWidth: 0,
            showLeft: false,
            showRight: false,
        };
        // restore current menu
        storeAdapter.dispatch({
            fun_name: 'updateApp',
            store_name: storeAdapter.store_names.appStore,
            params: [{
                type: 'updateAppMenu',
                params: {
                    menu: {
                        // id: e.currentTarget.dataset.menuid
                        menuKey: currentNav
                    }
                }
            }]
        })
        CanvasPaintedLogic.selectCurrentNav({
            rt_currentNav: currentNav,
        });

        localStorage.removeItem('ue_local_mutisize_uploadtemp');

        CanvasPaintedLogic.updateRtUserCookieTag({});
        assetManager.setPv_new(1875);

        //记录窗口宽度大小的埋点
        let setFlag = true;
        let offsetWid = document.documentElement.clientWidth;
        assetManager.setPv_new(4293, {
            additional: {
                i0: offsetWid,
            },
        });
        setWindowWidthSmallPV();
        window.onresize = function () {
            offsetWid = document.documentElement.clientWidth;
            setWindowWidthSmallPV();
        };
        function setWindowWidthSmallPV() {
            if (setFlag && offsetWid < 1200) {
                assetManager.setPv_new(4294);
                setFlag = false;
            }
            if (offsetWid > 1200) {
                setFlag = true;
            }
        }
    }

    afterDocLoad = () => {
        this.setState({
            showLeft: true,
            showRight: true,
        })
    }

    /**
     * 处理url参数
     */
    urlParametric(url) {
        let obj = {},
            params = url.substr(1);
        let parr = params.split('&');
        for (let i of parr) {
            let arr = i.split('=');
            obj[arr[0]] = arr[1];
        }
        return obj
    }

    setResourcePanelWidth = (value) => {
        const { canvas, rt_editor_type, canvas_wrapper_dom_info, rt_canvas_render_mode } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas
        })
        const canvasWrapperDom = document.querySelector('.' + canvas_wrapper_dom_info.class_name);
        let x = canvas.x - (value - this.state.resourcePanelWidth) / 2
        this.setState({
            resourcePanelWidth: value,
            diffWidth: value - this.state.resourcePanelWidth
        }, () =>{
            if (rt_canvas_render_mode === 'board') {
                return;
            }
            if (canvasWrapperDom) {
                let { width } = canvasWrapperDom.getBoundingClientRect();
                const pageWidth = canvas.width * canvas.scale;
                const disX = this.state.diffWidth;
                const storageFixedToolPanel = localStorage.getItem('fixedToolPanel');
                const fixedToolPanel = storageFixedToolPanel ? storageFixedToolPanel === 'true' : true;
                if (disX === 0) {
                    return;
                }
                if (width > pageWidth) {
                    console.log(disX)
                    if (disX > 0 && !canvas.floorCutting && canvas.width >= canvas.height) {
                        if (fixedToolPanel) {
                            width -= 271
                        }
                        x = (width + Math.abs(disX) - pageWidth) / 2;
                    } else {
                        x = (width - pageWidth) / 2;
                    }
                    TemplateCanvasLogic.setCanvasPositionAndSize({ x });
                } else if (disX > 0 && width + disX > pageWidth) {
                    TemplateCanvasLogic.setCanvasPositionAndSize({ x: this.safeDistance });
                } else {
                    const right = canvas.x + pageWidth;
                    if (disX < 0) {
                        // 侧边栏收起
                        if (right < width) {
                            x = width - pageWidth - this.safeDistance;
                            if (x === canvas.x) {
                                emitter.emit('canvasWrapperDomListener');
                            } else {
                                TemplateCanvasLogic.setCanvasPositionAndSize({ x });
                            }
                        } else {
                            emitter.emit('canvasWrapperDomListener');
                        }
                    } else {
                        // 侧边栏展开
                        if (right > width && canvas.x > this.safeDistance) {
                            TemplateCanvasLogic.setCanvasPositionAndSize({ x });
                        } else {
                            emitter.emit('canvasWrapperDomListener');
                        }
                    }
                }
            }
        })

        if (this.diffWidthTimer) {
            clearTimeout(this.diffWidthTimer);
        }
        this.diffWidthTimer = setTimeout(() => {
            this.setState({
                diffWidth: 0,
            })
        }, 160);

        // canvas 版本不需要这个重置视口
        if (rt_editor_type !== 'canvas') {
            if (this.canvasSize) {
                clearTimeout(this.canvasSize);
            }
            this.canvasSize = setTimeout(() => {
                TemplateCanvasLogic.canvasResizeByWindow();
            }, 180);
        }
    }

    setToolPanelWidth = (value) => {
        this.setState({
            toolPanelWidth: value
        })
    }

    componentDidMount() {
        document.ondragstart = function () {
            return false;
        };
        this.onNavChanged = emitter.addListener('onNavChanged', (currentNav) => {
            console.log('currentNav --- ', currentNav)
            // restore current menu
            storeAdapter.dispatch({
                fun_name: 'updateApp',
                store_name: storeAdapter.store_names.appStore,
                params: [{
                    type: 'updateAppMenu',
                    params: {
                        menu: {
                            // id: e.currentTarget.dataset.menuid
                            menuKey: currentNav
                        }
                    }
                }]
            })
            if (this.state.currentNav === currentNav) {
                // 非ue情况下，点击当前选中的导航，隐藏右侧面板
                !this.isUeEditor && emitter.emit('rightLayoutHideClick', 'hide');
            } else {
                this.setState({ currentNav: currentNav });
            }
            CanvasPaintedLogic.selectCurrentNav({
                rt_currentNav: currentNav,
            });
        });
        // this.hideLoadingImg = emitter.addListener('HideIndexLoadingImg', (loading = false) => {
        //     this.setState({ imgLoading: loading });
        // });

        // assetManager.initAssetTagList(); // 看起来没什么用

        const status = this.props.rt_is_online_detail_page ? 'hide' : ''
        // if (!isDesigner && !this.isNew) {
        emitter.emit('rightLayoutHideClick', status, 'initial');
        PolyFill.init()
        // }
    }

    componentWillUnmount() {
        // emitter.removeListener(this.onNavChanged);
        this.onNavChanged?.remove();
        this.hideLoadingImg?.remove();
    }

    render() {
        let { isDesigner, rt_is_online_detail_page } = this.props

        // let pageInitalLoadingStyle = {
        //     display: this.state.imgLoading ? 'block' : 'none',
        //     // display:true ? 'block' : 'none' ,
        // };

        // const { info } = storeAdapter.getStore({
        //     store_name: storeAdapter.store_names.InfoManage,
        // });
        // let showTemplate;
        // showTemplate =
        //     info &&
        //     info.class_id &&
        //     info.class_id.some((v) => {
        //         return v === '1060' || v === '1059' || v === '1122';
        //     });

        // const animateStyle = {
        //     zIndex: 9999998,
        //     position: 'absolute',
        //     right: '0',
        //     paddingLeft: 0,
        //     paddingTop: "25px",
        //     background: '#fff',
        //     paddingRight: "35px",
        //     top: 0,
        //     transition: "all 0.3s"
        // }
        return (
            <div className="rootLayout">
                {!isDesigner && <AssetHoverTips />}
                {/* <div className="new_version_loading_tips" style={pageInitalLoadingStyle}>
                    <img src="//js.tuguaishou.com/index_img/editorV7.0/new_version_loading.gif" alt=""/>
                    <div className="tips">拼命加载中...</div>
                    <div className="tips">loading...</div>
                </div> */}
                <Popup />
                <PromptBox />
                <PopoverBox />
                <Message />
                <HeadNav />
                <LoginPanel />
                <div className="contentWrapper" ref={this.contentWrapRef}>
                    {
                        env.editor !== 'smart' && this.state.showLeft &&
                        <>
                            <RightLayout setResourcePanelWidth={this.setResourcePanelWidth} currentNav={this.state.currentNav}>
                                {rt_is_online_detail_page && <OnlineDetailComs key="OnlineDetailComs" />}
                                {/* {(env.editor !== 'ecommerce' && env.editor !== 'ecommerceteam') && !showTemplate && <NavigatorTemplate key="NavigatorTemplate" />} */}
                            </RightLayout>
                            {/* <div id='right-page-Animation' className="page-animation" style={animateStyle}>
                                <PageAnimation isLeftPanel={false} isRightPanel={true} />
                            </div> */}
                        </>
                    }
                    <CanvasLayout toolPanelWidth={this.state.toolPanelWidth} resourcePanelWidth={this.state.resourcePanelWidth} diffWidth={this.state.diffWidth} afterDocLoad={this.afterDocLoad} />
                    {
                        env.editor !== 'smart' && this.state.showRight &&
                        <FloatToolPanel toolPanelWidth={this.state.toolPanelWidth} setToolPanelWidth={this.setToolPanelWidth}>
                            {/* <AiAgent /> */}
                        </FloatToolPanel>
                    }
                    <AITextToolPanel ></AITextToolPanel>
                </div>
                <CanvasGrab />
            </div>
        );
    }
}

/*connect*/
const mapStateToProps = (state) => {
    return {
        isDesigner: state.onCanvasPainted.isDesigner,
        rt_is_online_detail_page: state.onCanvasPainted.rt_is_online_detail_page,
        canvasLoadingStatus:state.onCanvasPainted.canvasLoadingStatus,
    };
};
DesignerLayout = connect(mapStateToProps)(DesignerLayout);
CanvasLayout = connect(mapStateToProps)(CanvasLayout)
export { DesignerLayout, CanvasLayout };
