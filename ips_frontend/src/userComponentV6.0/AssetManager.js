import { ipsApi, getProps, ipsDownload, ipswechatopenplatform, ipsSave, ipsPcElectron, ipsUApi } from './IPSConfig';
import { templateFormat } from './TemplateFormat';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { TemplateBackup } from '@v7_logic/TemplateBackup';
import { IPSConfig } from '@v7_utils/IPSConfig';
// import {ToolPanel} from './ToolPanel';
import { env } from '@editorConfig/env';
import { isEcommerceTeam, isUeTeam } from '@v7_utils/webSource';
import { isAIDesign } from '@v7_utils/estimate';
import { IpsUtils } from '@tgs/utils';
let replaceHost = '';// 用来给不同类型的模板替换埋点的host ppt,ecommerce 等
const strIsJSON = (str) => {
    if (typeof str == 'string') {
        try {
            var obj = JSON.parse(str);
            if (typeof obj == 'object' && obj) {
                return true;
            } else {
                return false;
            }

        } catch (e) {
            return false;
        }
    }
}
class AssetManager {
    constructor() {
        this.options = {
            credentials: 'include',
        };

        this.operationId = ''
    }

    fetchList(listType = 'element', userOwned = 0, page = 1) {
        return fetch(ipsApi('/api/assetlist/' + listType + "/" + userOwned + "?page=" + page), this.options);
    }

    fetchUserAssetById(id) {
        return fetch(ipsApi('/api/userasset/' + id), this.options);
    }

    fetchAssetTagList(assetTagType = "element") {
        const typeMap = {
            element: "1",
            background: "2,3",
            text: "5",
            all: "1_2,3_5"
        };
        const assetTagTypeNum = typeMap[assetTagType];
        return fetch(ipsApi('/api/assettaglistedit/' + assetTagTypeNum), this.options);
    }

    initAssetTagList() {
        this.fetchAssetTagList("all").then((data) => {
            data.json().then((assetTagList) => {
                this.assetTagList = assetTagList;
            });
        });
    }

    //用户画像使用，进入编辑器调用
    userPortrait() {
        return fetch(ipsApi('/api/user-portrait'), this.options)
    }

    //获取背景过滤条件
    getBackgroundFilter() {
        return fetch(ipsApi('/api/backgroundtaglist'), this.options)
    }
    //获取元素标签列表
    getAssetTagList() {
        return fetch(ipsApi('/api/assettaglist/1'), this.options)
    }
    //获取风格标签列表
    getStyleTagList() {
        return fetch(ipsApi('/api/assettaglist/2'), this.options)
    }

    setImageClip(data) {
        return fetch(
            ipsApi('/api-image-clip/set-image-clip'),
            Object.assign({}, this.options, {
                method: 'POST',
                body: data
            })
        ).then(response => response.json());
    }

    getImageClipDone(data) {
        return fetch(
            ipsApi('/api-image-clip/get-image-clip-done'),
            Object.assign({}, this.options, {
                method: 'POST',
                body: data
            })
        ).then(response => response.json());
    }

    //获取元素列表
    getAssetList(word, page, type, k1, k2, k3, filters = {}, word2) {
        let extraParams = '';
        if (filters.isPic) {
            let urlProps = getProps();
            let { picId } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });;
            let { info } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.InfoManage,
            });
            let tid = urlProps.picId;
            if (picId > 0) {
                tid = picId;
            }
            if (!tid && info) {
                tid = info.last_templ_id
            }
            extraParams = `&isPic=true&picId=${tid}`
        }
        if (word2) {
            extraParams = `${extraParams}&w2=${word2}`
        }

        // 摄影图VIP
        // if(vipPic){
        //     return fetch(ipsApi('/api/get-asset-list?w='+word+'&p='+page+'&type='+type+'&k1='+k1+'&k2='+k2+'&k3='+k3+'&tagId='+filters.tagId+'&sceneId='+filters.sceneId+'&styleId='+filters.styleId+'&ratioId='+filters.ratioId+extraParams+`&vip_pic=${vipPic}`), this.options)
        // }
        return fetch(ipsApi('/api/get-asset-list?w=' + word + '&p=' + page + '&type=' + type + '&k1=' + k1 + '&k2=' + k2 + '&k3=' + k3 + '&tagId=' + filters.tagId + '&sceneId=' + filters.sceneId + '&styleId=' + filters.styleId + '&ratioId=' + filters.ratioId + extraParams), this.options)
    }

    //获取元素列表(V2.0)
    getAssetListNew(word, page, type, k1, k2, k3, filters = {}) {
        let { isDesigner } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        let filterStr = '';
        if (filters.sort) {
            filterStr += '&sort=' + filters.sort;
        }
        if (filters.use_count) {
            filterStr += '&use_count=' + filters.use_count;
        }
        if (isDesigner) {
            filterStr += '&is_designer=1';
        }
        return fetch(ipsApi('/apiv2/get-asset-list?w=' + word + '&p=' + page + '&type=' + type + '&k1=' + k1 + '&k2=' + k2 + '&k3=' + k3 + '&tagId=' + filters.tagId + '&sceneId=' + filters.sceneId + '&styleId=' + filters.styleId + '&ratioId=' + filters.ratioId + filterStr), this.options)
    }

    //获取图片链接
    getImgRUrl(key) {
        return fetch(key, this.options)
    }

    /**
     * 获取协作时的分享连接
     */
    getCooperationShareUrl(template_id) {
        // if( env.editor === "ueteam" ){
        if (isUeTeam) {
            // const editor = window.location.href.includes('ue.818ps.com') ? 'ue' : 'ecommerce';
            return fetch(ipsApi(`/api/add-team-share-collaboration?template_id=${getProps(true).user_template_team_id}&editor=ueteam&team_id=${getProps().team_id}`),
             {
                ...this.options,
                headers:{
                    ...IPSConfig.getAuthenticatio()
                }
             });
        } else {
            const editor = window.location.href.includes('ue.818ps.com') ? 'ue' : 'ecommerce';
            return fetch(ipsApi('/api/add-team-share?template_id=' + template_id + `&editor=${editor}`),  {
                ...this.options,
                headers:{
                    ...IPSConfig.getAuthenticatio()
                }
             });
        }
    }
    /**
     * 判断用户跳转工作台类型
     */
    getUserJumpType() {
        return fetch(ipsApi('/apiv3/get-user-jump-type'), {
            method: 'GET',
            credentials: 'include'
        })
    }

    /** 判断 upicId 是否发起过协作 */
    getTemplateIsCollaboratived(templateId) {
        const dataStr = `upicId=${templateId}&user_template_team_id=${getProps(true).user_template_team_id || 0}`
        return fetch(ipsApi('/api/select-share-status'), {
            method: 'POST',
            body: dataStr,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            credentials: 'include'
        });
    }

    //获取template
    getTemplate(picId, upicId, team_id, paperId, userTemplateTeamId, use, groupWordId) {
        const urlProps = getProps(true);
        let { isDesigner } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        let { info } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage,
        });

        let { user_template_team_id = 0 } = getProps();
        if (env.teamTemplate || isUeTeam || (env.editor === "shentu" && getProps().user_template_team_id)) {
            upicId = upicId ? upicId : 0;
            user_template_team_id = userTemplateTeamId === 0 ? userTemplateTeamId : user_template_team_id;
            user_template_team_id = user_template_team_id == 0 ? user_template_team_id : +urlProps.version_id? urlProps.version_id : user_template_team_id;
            if (urlProps.share_uid && urlProps.share_id && (/* env.editor === "ueteam" */ isUeTeam || env.editor === 'ecommerceteam' || env.editor === "shentu") && (use !== 'replaceTemplate' && use !== 'addNew')) {
                return fetch(ipsApi(`/api/team-share-get-templ?picId=${picId || 0}&upicId=${upicId}&template_type=1&share_uid=${urlProps.share_uid}&share_id=${urlProps.share_id}&version_id=${urlProps.version_id}&user_template_team_id=${user_template_team_id}&origin=${urlProps.origin || 0}`), this.options);
            }
            return fetch(ipsApi('/api/user-get-templ' + '?' + 'picId=' + picId + '&upicId=' + upicId + '&user_template_team_id=' + user_template_team_id + '&template_type=1'), this.options);
        }

        /*编辑器判断*/
        if (team_id) {
            return fetch(ipsApi('/api/user-get-templ' + '?' + 'picId=' + picId + '&upicId=' + upicId + '&template_type=1&team_id=' + team_id + "&version_id=" + getProps().version_id), this.options);
        } else {
            if (isDesigner) {
                if (info.is_second) {
                    return fetch(ipsApi('/api/templ' + '?' + 'picId=' + picId + '&is_second=' + info.is_second + "&version_id=" + getProps().version_id), this.options);
                }
                return fetch(ipsApi('/api/templ' + '?' + 'picId=' + picId + "&version_id=" + getProps().version_id), this.options);
            } else if (paperId > 0) {
                return fetch(ipsApi('/api/user-get-paper-templ' + '?' + 'picId=l' + picId + '&paperId=' + paperId + "&version_id=" + getProps().version_id), this.options);
            } else if (groupWordId > 0) {
                return fetch(ipsApi('/api/get-group-word' + '?' + 'groupWordId=' + groupWordId), this.options);
            } else {
                const urlProps = getProps();
                if (urlProps.share_uid && urlProps.share_id) {
                    return fetch(ipsApi('/api/team-share-get-templ' + '?' + 'picId=' + picId + '&upicId=' + upicId + '&template_type=1' + '&share_uid=' + urlProps.share_uid + '&share_id=' + urlProps.share_id + "&origin=" + getProps(true).origin + "&version_id=" + urlProps.version_id), this.options);
                }
                return fetch(ipsApi('/api/user-get-templ' + '?' + 'picId=' + picId + '&upicId=' + upicId + "&version_id=" + urlProps.version_id), this.options);
            }
        }

    }

    /** 判断是不是用户模板的所有者 */
    checkIsUpicIdOwner(upicId) {
        return fetch(ipsApi('/apiv2/check-templ-owner?upicid=' + upicId), this.options).then(res => res.json());
    }

    //获取分类id
    changeTemple(picId, upicId, is_paper_temp) {
        return fetch(ipsApi('/api/change-template' + '?' + 'picId=' + picId + "&upicId=" + upicId + "&is_paper_temp=" + is_paper_temp), this.options);
    }

    //获取分类id
    getClassId(template_id) {
        return fetch(ipsApi('/api/get-templ-class' + '?' + 'templ_id=' + template_id), this.options);
    }

    //获取推荐字号
    getKindFont(kid_2) {
        return fetch(ipsApi('/api/get-kind-font' + '?' + 'kid_2=' + kid_2), this.options);
    }

    //复制模板
    copyTemplate(tempId = '') {
        let dataStr = `id=${tempId}`;
        return fetch(ipsApi(`/site-api/copy-temple`), {
            method: 'POST',
            body: dataStr,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            credentials: 'include',
        });
    }

    // 获取背景纹理列表
    getBackgroundTextureList() {
        return fetch(ipsApi('/api/get-background-texture-list'), this.options);
    }

    // 退出登录
    logout() {
        return fetch(ipsApi('/api/logout'), this.options);
    }

    //保存用户上传的template
    saveTemplate(template, handleSave, message_id) {
        let { info } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage,
        });
        let {
            isDesigner,
            canvas,
            doc_createTime,
            rt_mutisize_current_selected_tid,
            rt_first_save_recorder,
            rt_mutisize_current_selected_kidInfo,
            rt_isMutiSizeTemplate,
            rt_mutisize_subtemplates,
            rt_is_online_detail_page,
            save_copy_flag
        } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        let urlProps = IPSConfig.getProps(true);
        /*判断是否有字魂字体 START*/
        let zihunFonts = [
            'zh2hllcht',
            'zh4hcjxkt',
            'zh17hmqgdt',
            'zh19hxyfbt',
            'zh20hstt',
            'zh24hzhss',
            'zh27hbdt',
        ];

        let use_zihun = 0;
        for (let i = 0; i < template.doc.work.pages.length; i++) {
            let page = template.doc.work.pages[i];
            for (let j = 0; j < page.assets.length; j++) {
                let asset = page.assets[j];
                // if(asset.meta && asset.meta.type == 'text' && zihunFonts.indexOf(asset.attribute.fontFamily) >= 0){
                if (asset.meta && asset.meta.type == 'text' && asset.attribute.fontFamily && asset.attribute.fontFamily.indexOf('zh') == 0) {
                    use_zihun = 1;
                    break;
                }
            }
            if (use_zihun) {
                break;
            }
        }
        // 设计师编辑器 ppt模板都保存为ppt格式
        const saveAsPpt = isDesigner && template.template_type == '3'
        /*判断是否有字魂字体 END*/
        template.doc = templateFormat.saveFormat(template.doc,(template.downloadType == 'ppt' || saveAsPpt) ? 'ppt' : '' );
        let apiPath = null
        if (isDesigner) {

            if (rt_is_online_detail_page && template.isSingleFloor) {
                // if(!template.id){
                //     template.id = urlProps['picId']
                // }
            } else {
                template.id = urlProps['picId'];
            }

            Object.assign(template, {
                kid_1: urlProps.k1 > 0 ? urlProps.k1 : (info.kid_1 > 0 ? info.kid_1 : 0),
                kid_2: urlProps.k2 > 0 ? urlProps.k2 : (info.kid_2 > 0 ? info.kid_2 : 0),
                kid_3: urlProps.k3 > 0 ? urlProps.k3 : (info.kid_3 > 0 ? info.kid_3 : 0),
                width: template.width > 0 ? template.width : 0,
                height: template.height > 0 ? template.height : 0,
                class_id:  urlProps.class_id > 0 ? urlProps.class_id : 0,
                use_zihun: use_zihun,
            });
            if (urlProps['class_id1']) {
                template.class_id = `${template.class_id}_${urlProps['class_id1']}`
            }
            // 多模板处理逻辑
            if (rt_isMutiSizeTemplate) {
                const { rt_isMutiSizeInfo } = storeAdapter.getStore({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                });
                const sizeInfo = rt_isMutiSizeInfo.filter((v) => rt_mutisize_current_selected_tid == v.id)[0]
                const subtemplates = rt_mutisize_subtemplates[sizeInfo.infoIndex]
                if (!subtemplates) {
                    return new Promise((resolve, reject) => {
                        console.error(`传入错误的子模板数据,infoIndex:${sizeInfo.infoIndex}`)
                        reject(`传入错误的子模板数据`)
                    })
                }
                template.id = subtemplates.link_tid
                template.picId = subtemplates.link_tid
                template.kid_1 = sizeInfo.kid_1;
                template.kid_2 = sizeInfo.kid_2;

                if (sizeInfo.class_id) {
                    template.class_id = sizeInfo.class_id;
                    if (urlProps['class_id1']) {
                        template.class_id = `${template.class_id}_${urlProps['class_id1']}`
                    }
                }
                // if (template.class_id_ext) {
                //     Object.assign(template,{
                //         class_id:template.class_id  + '_' + template.class_id_ext.join('_')
                //     })
                    
                // }
                // const kid_1 = urlProps['k1'] || template.kid_1 || info.kid_1

                // if(template.mutisize_bindinfo && `${kid_1}`!=='1'){
                //     template.mutisize_bindinfo.some(v=>{
                //         const flag = template.height == v.height && v.width == template.width && v.link_tid != ''
                //         if(flag){
                //             template.id = v.link_tid
                //             template.picId = v.link_tid
                //         }
                //         return flag
                //     })
                // }

            }
            if (template.isSingleFloor) {
                template.class_id = '';
            }
            
            const stashClassId = localStorage.getItem(`class_id_${template.id}`)
            if(stashClassId && urlProps['picId'] && !isAIDesign()){
                template.class_id = JSON.parse(stashClassId).join('_')
            }
            if(template.class_id_ext){
                const tempArr=[]
                urlProps['class_id'] && tempArr.push(urlProps['class_id'])
                urlProps['class_id1'] && tempArr.push(urlProps['class_id1'])
                template.class_id = tempArr.concat(template.class_id_ext).join('_')
            }
            if (urlProps['task_id']) {
                Object.assign(template, {
                    task_id: urlProps['task_id']
                });
            }
            // 注意：针对后端提出的修改需求，直接覆盖上面的 class_id 处理
            // if (localStorage.getItem(`class_id_${template.id}`)) {
            //     const ids = JSON.parse(localStorage.getItem(`class_id_${template.id}`)).join('_');
            //     template.class_id = `${ids}`
            //     if (urlProps['class_id1']) {
            //         template.class_id = `${template.class_id}_${urlProps['class_id1']}`
            //     }
            // }
            //客户端
            if (urlProps['isClientSide'] && urlProps['isClientSide'] == 1) {
                Object.assign(template, {
                    isFromClient: urlProps['isClientSide']
                });
            }

            if (info.is_second && info.old_templ_id) {
                Object.assign(template, {
                    is_second: 1,
                    old_templ_id: info.old_templ_id,
                });
            }
            if(isAIDesign()){
                template['image_container_num'] = template.doc['aiDesignImgContainerNum'] ?? 0
                template['matting_material_num'] = template.doc['aiDesignMattingImgContainerNum'] ?? 0
                
            }
            apiPath = '/api/templ?startTime='
        } else {
            let { picId } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            let tid = urlProps.picId;
            if (picId > 0) {
                tid = picId;
            }
   
            template.lastTemplId = tid;
            // 添加用户保存时间
            template.saveTime = new Date().getTime();

            Object.assign(template, {
                use_zihun: use_zihun
            });
            if (urlProps['pram']) {
                Object.assign(template, {
                    pram: urlProps['pram']
                });
            }
            //客户端
            if (urlProps['isClientSide'] && urlProps['isClientSide'] == 1) {
                Object.assign(template, {
                    isFromClient: urlProps['isClientSide']
                });
            }
            // apiPath='/api/user-save-templ?startTime='
            if (!handleSave && !(urlProps.upicId > 0)) {
                if (env.teamTemplate || isUeTeam) {
                    // lyy
                    // apiPath = '/api/user-save-templ?startTime='
                    apiPath = template.downloadType == 'ppt' ? '/api/user-save-templ?v=2&startTime=' : '/api/user-save-templ?startTime='
                } else {
                    apiPath = '/api/user-save-paper-templ?startTime='
                }
                // apiPath = '/api/user-save-paper-templ?startTime='
            } else {
                // apiPath = '/api/user-save-templ?startTime='
                apiPath = template.downloadType == 'ppt' ? '/api/user-save-templ?v=2&startTime=' : '/api/user-save-templ?startTime='
            }
        }
        const saveHandler = template.downloadType == 'ppt' ? ipsApi : ipsSave
        // 设计师操作时，避免关联模板导致的保存异常
        if (!rt_is_online_detail_page && isDesigner) {
            const { rt_firstSaveRecorder } = template
            const recordeId = rt_first_save_recorder[rt_firstSaveRecorder]
            if (recordeId && (recordeId != template.id)) {
                return new Promise((resolve, reject) => {
                    reject(`保存时未传入正确id：${recordeId}模板，在保存时传入id为${template.id},实际id应为${recordeId}`)
                })
            }
        }

        // 保存备份日志
        let load_save_log = JSON.parse(localStorage.getItem("ue_save_log"));
        if (!load_save_log && !message_id) {
            let save_obj = new Array();
            if (template.id) {
                let new_template = new Object();
                new_template.tid = template.id;
                new_template.times = 2;
                save_obj.push(new_template)
            }
            localStorage.setItem('ue_save_log', JSON.stringify(save_obj));
        } else {
            let exist_flag = false
            load_save_log.forEach((item, index) => {
                if (item.tid == template.id) {
                    item.times++;
                    exist_flag = true;
                    if (item.times > 10 || message_id) {
                        item.times = 1
                        this.saveUserTemplateOperationLog(template, message_id);
                    }
                }
            })
            if (!exist_flag && template.id) {
                let new_template = new Object();
                new_template.tid = template.id;
                new_template.times = 2;
                load_save_log.push(new_template)
            }
            localStorage.setItem('ue_save_log', JSON.stringify(load_save_log));
        }
        // canvasStore.dispatch(paintOnCanvas('save-user-template-operation-log'))
        TemplateBackup.backupTriggers();
        // setTimeout(()=>{
        //     canvasStore.dispatch(paintOnCanvas('save-user-template-operation-log'))
        // },1000)

        delete template.rt_firstSaveRecorder

        if (urlProps.share_uid && urlProps.share_id) {
            template.share_uid = urlProps.share_uid;
            template.share_id = urlProps.share_id;
            // if(env.editor === "ueteam"){
            //     template.template_save_type = 'user_team_template_save';
            // }
            if (env.teamTemplate || isUeTeam) {
                if (template.template_save_type == 'versions') {
                    template.id = undefined;
                    template.pid = +urlProps.upicId || info.pid || 0;
                } else {
                    template.id = +urlProps.version_id || template.id || urlProps.user_template_team_id || 0;
                    template.version_id = undefined;
                }
                template.template_save_type = 'user_team_template_save';
                template.save_type = 'user_team_design';
                // template.team_id = 171231;
                template.upicid = urlProps.upicId || 0;
            }
            return fetch(ipsSave('/api/team-share-save-templ?startTime=' + doc_createTime), {
                // return fetch(ipsApi('/api/user-save-templ'), {
                method: 'POST',
                body: JSON.stringify(template),
                async: false,
                headers: {
                    Accept: 'application/json',
                },
                credentials: 'include',
            });
        }

        if (env.teamTemplate || isUeTeam) {
            if (template.template_save_type == 'versions') {
                template.id = undefined;
                template.pid = +urlProps.upicId || info.pid || 0;
            } else {
                template.id = +urlProps.version_id || template.id || urlProps.user_template_team_id || 0;
                template.version_id = undefined;
            }
            template.template_save_type = 'user_team_template_save';
            // template.team_id = 171231;
            template.team_id = template.team_id || urlProps.team_id || 0;
        }
        return fetch(saveHandler(apiPath + doc_createTime), {
            method: 'POST',
            body: JSON.stringify(template),
            async: false,
            headers: {
                'Accept': 'application/json'
            },
            credentials: 'include'
        })
    }
    // 保存用户当前模板的分类
    saveUserTemplateKind(param){
        return fetch(ipsApi('/apiv2/save-user-template-kind'), {
            method: 'POST',
            body: JSON.stringify(param),
            async: false,
            headers: {
                'Accept': 'application/json'
            },
            credentials: 'include'
        }).then(data=>{
            data.json()
        })
    }
    // 获取用户模板分类
    getUserTemplateKind(param){
        const { picId=0,paperId=0, upicId=0, version_id=0, user_template_team_id=0} = param
        return fetch(ipsApi(`/apiv2/get-template-info?picId=${picId}&paperId=${paperId}&upicId=${upicId}&version_id=${version_id}&user_template_team_id=${user_template_team_id}`))
    }
    //记录用户模版操作流程
    saveUserTemplateOperationLog(template, message_id) {
        let post_data = new Object();
        let urlProps = IPSConfig.getProps(true);
        post_data.doc = template.doc
        post_data.utid = template.id
        post_data.team_id = urlProps.team_id || 0
        post_data.message_id = message_id || ''
        return fetch(ipsSave('/api/save-user-template-operation-log'), {
            method: 'POST',
            body: JSON.stringify(post_data),
            async: false,
            headers: {
                'Accept': 'application/json'
            },
            credentials: 'include'
        })
    }

    //保存用户上传的asset
    saveAsset(asset) {
        return fetch(ipsApi('/api/userasset/' + asset.id), {
            method: 'PUT',
            body: JSON.stringify(asset),
            headers: {
                'Accept': 'application/json'
            },
            credentials: 'include'
        });
    }


    //取消占用
    cancelFreed(id, picId) {
        if (!id) {
            id = 0;
        }
        return fetch(ipsApi('/api/bg-freed?id=' + id + "&picId=" + picId), this.options);
    }

    getAssetImgFilterList() {
        return fetch(ipsApi('/api/get-asset-img-filter-data'), this.options);
    }

    //添加审核回复
    subApproveReply(sendObj) {
        // let words = `word=${JSON.stringify(text)}`;
        // let sendWords =  `user_template_id=${JSON.stringify(text)}`

        return fetch(ipsApi('/api/sub-approve-reply'), {
            method: 'POST',
            body: sendObj,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            credentials: 'include'
        })
    }

    //删除审核回复
    delReplay(sendObj) {
        return fetch(ipsApi('/api/del-replay'), {
            method: 'POST',
            body: sendObj,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            credentials: 'include'
        })
    }

    getFilterInfo(id) {
        return fetch(ipsApi('/api/get-asset-img-filter-info?id=' + id), this.options);
    }

    //删除asset
    deleteAsset(assetId) {
        return fetch(ipsApi('/api/del-asset?id=' + assetId), this.options);
    }

    //批量删除用户上传的素材
    batchDeleteUserAsset(ids) {
        ids = ids.join(',');
        return fetch(ipsApi('/api/userasset/' + ids), {
            method: 'DELETE',
            credentials: 'include'
        });
    }

    // 是否需要下载拦截 后端计算当日已经下载次数决定
    isNeedFilterDownloadBy_checkEveryDayDownloadStatue(){
        return fetch(ipsDownload('/uapi/get-everyday-download-statue '), this.options)
    }

    // 每次拦截的校验完成后需要调用
    getTypeBy_CheckEveryDayDownload(){
        return fetch(ipsDownload('/uapi/check-everyday-download'), this.options)
    }

    //获取用户信息
    getUserInfo() {
        // const { user } = storeAdapter.getStore({store_name: storeAdapter.store_names.paintOnCanvas})
        // const userId = `${user.userId}` || ''
        // const pageAnimationTest = userId > 0 && (['8', '1'].includes(userId[userId.length - 1]) || ['11936767', '9667287', '251262'].includes(userId))

        // if (pageAnimationTest) {
        //     return fetch(ipsUApi('/api/getuserinfo'), this.options)
        // }
        return fetch(ipsUApi('/api/getuserinfo'), this.options)
    }

    // 获取语音token
    getTalkToken() {
        let props = getProps();
        const type = window.location.href.includes('ue.818ps.com') ? 'ue' : 'ecommerce';
        const dataStr = `template_id=${props.upicId}&type=${type}`;
        return fetch(ipsApi('/api/ali-rtc'), {
            method: 'POST',
            body: dataStr,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            credentials: 'include'
        });
    }

    //检查是否存在违禁词
    judgText(text) {
        let words = `word=${JSON.stringify(text)}`;
        return fetch(ipsApi('/h5-api/prohibited-words'), {
            method: 'POST',
            body: words,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            credentials: 'include'
        })
    }

    /**
     * 获取用户模板的信息
     */
    getUsersTemplateInfo(params = {}) {
        return fetch(ipsApi("/h5-api/user-get-info?" + `utid=${params.utid}`), this.options);
    }

    //获取标签
    getTagInfo(kid, class_id) {
        let urlProps = getProps();
        return fetch(ipsApi('/api/get-tag?kid=' + kid + "&class_id=" + class_id), this.options)
    }

    //获取分类名
    getKindInfo(k1, k2 = 0) {
        return fetch(ipsApi('/api/get-kind-info?k1=' + k1 + '&k2=' + k2), this.options).then(res => res.json())
    }

    //获取普通用户的元素列表
    getUserAssetList(page = 1,origin='') {      
        return fetch(ipsApi('/api/get-user-asset-list?pages=' + page+'&limit=30'+'&origin='+origin), this.options)
    }

    // 删除用户的元素
    delDelUserAsset(id) {
        return fetch(ipsApi('/api/del-user-asset?id=' + id), this.options)
    }

    delDelUserAssets(ids) {
        return fetch(ipsApi('/api/del-user-asset?ids=' + ids.join(',')), this.options)
    }

    delBatchAssets(ids, type) {
        const apiMap = {
            img: '/api/del-user-asset?ids=',
            video: '/api/del-user-video-e',
            audio: '/api/del-user-asset-audio',
            gif: '/api/del-user-video-e',
            default: '/api/del-user-asset?ids='
        }

        const api = apiMap[type] || apiMap.default;

       
        if(type !== 'img') {
            const formData = new FormData();
            formData.append('id', ids.join(','))
            return fetch(ipsApi(api), {
                method: 'POST',
                body: formData,
                credentials: 'include',
            });
        } else {
            return fetch(ipsApi(api + ids.join(',')), this.options)

        }
    }

    // 用户下载
    downloadPic(customProps = { force: 0, fileType: 'png', dpi: '150', mark: 0 }) {
        let props = IPSConfig.getProps()

        const startTimer = sessionStorage.getItem('downloadTimer') / 1000;
        const endTimer = Date.now() / 1000;
        const differenceTimer = (endTimer - startTimer).toFixed(2);

        const { operationRecord, rt_is_online_detail_page = false } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        // if( props.upicId > 0 ){
        //     return fetch(ipsApi('/uapi/down-link?upicId='+props.upicId), this.options)
        //     // window.open(ipsApi('/uapi/download?upicId='+props.upicId))
        // }else if( props.picId > 0 ){
        //     return fetch(ipsApi('/uapi/down-link?picId='+props.picId), this.options)
        //     // window.open(ipsApi('/uapi/download?picId='+props.picId))
        // }
        const {info} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage,
        });
        if (props.upicId > 0) {
            let downUrl = '/uapi/async-apply-download?upicId=';
            if (customProps.fileType == 'ppt') {
                // downUrl = '/uapi/async-apply-download-p-pt?upicId=';
                downUrl = '/uapi/async-apply-down-ppt-v5?v=2&upicId='
            }
            let isPart = false; //是否分块
            if (customProps['online_detail_download_type'] && customProps['online_detail_download_type'] === 1) {
                isPart = true;
            }

            const waterMark = customProps.is_watermark;
            let parm = props.upicId + '&source=' + operationRecord.source + '&re_edit=' + differenceTimer + '&force=' + (customProps.force ? customProps.force : 0) + '&format=' + customProps.fileType + `&dpi=${customProps.dpi}` + `${waterMark ? '&is_watermark=1' : ''}` + `${isPart ? '&part=1' : ''}`;

            if (customProps.ticket && customProps.rendstr) {
                parm = props.upicId + '&source=' + operationRecord.source + '&re_edit=' + differenceTimer + '&force=' + (customProps.force ? customProps.force : 0) + '&format=' + customProps.fileType + `&dpi=${customProps.dpi}` + `${isPart ? '&part=1' : ''}` + `&ticket=${customProps.ticket}` + `&rendstr=${customProps.rendstr}`;

            }
            parm = parm + `&version_id=${getProps().version_id || 0}`
            if (props.share_uid && props.share_id) {
                let user_template_team_id = ""
                if (env.teamTemplate || isUeTeam) {
                    user_template_team_id = `&user_template_team_id=${getProps().user_template_team_id || 0}`
                }
                let check = ''
                if (customProps.ticket && customProps.rendstr) {
                    check = `&ticket=${customProps.ticket}` + `&rendstr=${customProps.rendstr}`;
                }
                return fetch(ipsDownload(downUrl + props.upicId + "&share_uid=" + props.share_uid + "&share_id=" + props.share_id + '&source=' + operationRecord.source + '&re_edit=' + differenceTimer + '&force=' + (customProps.force ? customProps.force : 0) + '&format=' + customProps.fileType + `&dpi=${customProps.dpi}` + `${isPart ? '&part=1' : ''}` + '&zoomOutFileSize=' + customProps.fileSize + user_template_team_id + check + '&version_id=' + getProps().version_id || 0), this.options);
            }
            if (env.teamTemplate || isUeTeam) {
                const { user_template_team_id = 0 } = getProps();
                parm = parm + `&user_template_team_id=${props.upicId || 0}`
            }
            const tempClassify = info.template_type == 3 ? 'ppt' : ''
            parm += `&classify=${tempClassify}`
            if(customProps.fileType == 'ppt'){
                return fetch(ipsApi(downUrl + parm), this.options);
            }
            return fetch(ipsDownload(downUrl + parm), this.options);
            // return fetch(ipsDownload(downUrl + props.upicId+'&source='+operationRecord.source+'&re_edit='+operationRecord.re_edit+'&force='+(customProps.force?customtProps.force:0)+'&format='+customProps.fileType+`&dpi=${customProps.dpi}`+`${isPart?'&part=1':''}`), this.options);

        }
    }

    // 用户下载-视频
    downloadVideo(customProps = { force: 0, fileType: 'mp4' }) {
        let props = IPSConfig.getProps()
        let { operationRecord } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas
        });

        if (props.upicId > 0) {
            let downUrl = `/uapi/async-apply-down-movie-effects?format=${customProps.isGif ? `gif&pick_pages=${(customProps.pickPages || []).join('_')}` : 'mp4'}&version_id=${getProps().version_id || 0}&upicId=`;

            let parm = '&source=' + operationRecord.source;
            if (customProps.ticket && customProps.rendstr) {
                parm = '&source=' + operationRecord.source + `&ticket=${customProps.ticket}` + `&rendstr=${customProps.rendstr}`;
            }
            if (props.share_uid && props.share_id) {
                parm += '&share_uid=' + props.share_uid + '&share_id=' + props.share_id;
            }

            if (env.teamTemplate || isUeTeam) {
                parm = parm + `&user_template_team_id=${props.upicId || 0}`
            }

            return fetch(ipsDownload(downUrl + props.upicId + parm), this.options);
        }
    }

    // 用户下载（PPT）
    downloadPPT(customProps = { force: 0, fileType: 'png', dpi: '150', mark: 0 }) {
        let props = getProps()

        let { operationRecord } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.operationRecordRedux,
        });

        // if( props.upicId > 0 ){
        //     return fetch(ipsApi('/uapi/down-link?upicId='+props.upicId), this.options)
        //     // window.open(ipsApi('/uapi/download?upicId='+props.upicId))    
        // }else if( props.picId > 0 ){
        //     return fetch(ipsApi('/uapi/down-link?picId='+props.picId), this.options)
        //     // window.open(ipsApi('/uapi/download?picId='+props.picId))
        // }
        if (props.upicId > 0) {
            return fetch(ipsDownload('/uapi/async-apply-download-p-pt?upicId=' + props.upicId + '&source=' + operationRecord.source + '&re_edit=' + operationRecord.re_edit + '&force=' + (customProps.force ? customProps.force : 0) + '&format=' + customProps.fileType + `&dpi=${customProps.dpi}`), this.options);
        }
    }

    /**
     * 获取素材asset -  高清图等
     * @param {*} customProps 
     * @returns 
     */
    getMaterial(customProps={}) {
        let props = getProps();
        return fetch(ipsApi('/api/get-dl-pic-url?upicId='+props.upicId+''), this.options);
    }

    
    /**
     * 扣除下载次数
     * @param {*} customProps 
     * @returns 
     */
    picDownConsume(customProps={}) {
        let props = getProps();
        return fetch(ipsApi('/api/pic-down-dedu?upicId='+props.upicId+'&picId='+props.picId +'&count='+customProps.count), this.options);
    }



    // 下载状态监测
    downloadFlagCheck(param = {}) {
        let props = getProps();
        let jobId = param['jobId'] ? param['jobId'] : "";
        let teamParm = '';
        if (env.teamTemplate || isUeTeam) {
            teamParm = `&user_template_team_id=${props.user_template_team_id || 0}`;
        }
        if(param.isPpt){
            return fetch(ipsApi(`/uapi/async-check-download?upicId=` + props.upicId + "&jobId=" + jobId + teamParm)  + '&v=2' +'&classify=ppt', this.options)
        }
        //‘localization’为1表示该接口时本地化下载时调用，默认没有该参数，后续优化可以删除
        return fetch(ipsDownload(`/uapi/async-check-download?${param.isVideo ? 'classify=video&' : ''}${param.isPpt ? 'classify=ppt&' : ''}${param.localization ? 'localization=1&' : ''}upicId=` + props.upicId + "&jobId=" + jobId + teamParm) + `&version_id=${getProps().version_id || 0}`, this.options);
    }

    // 下载状态关闭
    asyncCloseDownload(param = {}) {
        let { jobId } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        return fetch(ipsDownload('/uapi/async-close-download?jobId=' + jobId), this.options);
    }

    // 切图模板对比图生成
    asyncQietu(param = {}) {
        let props = getProps();
        let jobId = param['jobId'] ? param['jobId'] : "";
        let teamParm = '';
        if (env.teamTemplate || isUeTeam) {
            teamParm = `&user_template_team_id=${props.user_template_team_id || 0}`;
        }

        return fetch(ipsApi(`/uapi/async-qietu?id=${param['id']}&dl_id=${param['dl_id']}`, this.options));
    }

    // 切图模板对比图生成
    asyncLocal(param = {}) {
        let props = getProps();
        let jobId = param['jobId'] ? param['jobId'] : "";
        let teamParm = '';
        if (env.teamTemplate || isUeTeam) {
            teamParm = `&user_template_team_id=${props.user_template_team_id || 0}`;
        }

        return fetch(ipsApi(`/uapi/async-local?id=${param['id']}&dl_id=${param['dl_id']}`, this.options));
    }

    // 记录操作记录
    setPushOperationRecord() {
        return;
        // return false;
        let props = getProps();
        let abTypeDom = document.getElementById('abType'),
            ip = abTypeDom.getAttribute('ip');
        ip = ip == 'undefined' ? '' : ip;

        // if( !(props['upicId'] > 0) ){
        //     return
        // }

        let dataStr,
            { operationRecord, user } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.operationRecordRedux,
            });

        if (!(user.userId > 0) && !ip) {
            return
        }

        if (this.operationId == '') {
            this.operationId = user.userId + '-' + ip + '-' + props['upicId'] + '-' + props['picId'] + '-' + operationRecord.startDate + '-' + Math.ceil(Math.random() * 10000);
        }

        dataStr = 'operationRecord=' + JSON.stringify(operationRecord) + '&upicId=' + props['upicId'] + '&operationId=' + this.operationId

        return fetch(ipsSave('/cmq/push-operation-record'), {
            // return fetch(ipsApi('/cmq/push-operation-record'), {
            method: 'POST',
            body: dataStr,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            credentials: 'include'
        })
    }

    // 记录操作记录
    setPushOperationRecordDesigner() {
        return;
        // return false;
        let props = getProps();
        let abTypeDom = document.getElementById('abType'),
            ip = abTypeDom.getAttribute('ip');
        ip = ip == 'undefined' ? '' : ip;

        // if( !(props['upicId'] > 0) ){
        //     return
        // }

        let dataStr,
            { operationRecord, user } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.operationRecordRedux,
            });

        if (!(user.userId > 0) && !ip) {
            return
        }

        if (this.operationId == '') {
            this.operationId = user.userId + '-' + ip + '-' + props['upicId'] + '-' + props['picId'] + '-' + operationRecord.startDate + '-' + Math.ceil(Math.random() * 10000);
        }

        dataStr = 'operationRecord=' + JSON.stringify(operationRecord) + '&upicId=' + props['upicId'] + '&operationId=' + this.operationId

        return fetch(ipsSave('/cmq/push-operation-record-designer'), {
            method: 'POST',
            body: dataStr,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            credentials: 'include'
        })
    }

    // 记录操作记录
    setPushOperationRecordNew() {
        return;
        let props = getProps()

        // if( !(props['upicId'] > 0) ){
        //     return
        // }

        let dataStr,
            { operationRecord, user, operationRecordNum } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.operationRecordRedux,
            }),
            tempOperationRecord = Object.assign({}, operationRecord),
            newOperationRecordNum = operationRecord.doc.length;
        if (operationRecordNum == newOperationRecordNum) {
            return false;
        }
        Object.assign(tempOperationRecord, {
            doc: tempOperationRecord.doc.slice(operationRecordNum, newOperationRecordNum),
        });
        if (operationRecordNum === newOperationRecordNum) {
            return;
        }
        if (operationRecordNum === 0 && newOperationRecordNum != 0) {
            tempOperationRecord.doc.unshift({
                command: "INIT",
                recordDate: tempOperationRecord.startDate,
                subCommand: '',
            });
        }

        // canvasStore.dispatch(paintOnCanvas('UPDATE_OPERATIONRECORDNUM', {operationRecordNum: newOperationRecordNum}));
        if (!(user.userId > 0)) {
            return
        }

        if (this.operationId == '') {
            this.operationId = user.userId + '-' + props['upicId'] + '-' + tempOperationRecord.startDate + '-' + Math.ceil(Math.random() * 10000);
        }

        dataStr = 'operationRecord=' + JSON.stringify(tempOperationRecord) + '&upicId=' + props['upicId'] + '&operationId=' + this.operationId

        return fetch(ipsApi('/cmq/push-operation-record-new'), {
            method: 'POST',
            body: dataStr,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            credentials: 'include'
        })
    }

    // 意见反馈
    opinionSubmit(text, lx) {

        let dataStr = 'page=3&contact=' + lx + '&message=' + text

        return fetch(ipsApi('/api/feedback'), {
            method: 'POST',
            body: dataStr,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            credentials: 'include'
        })
    }

    // 获取分享链接
    editShareLog(pid, key, share_id, is_ratify) {
        let dataStr = new Object();
        dataStr[key] = pid;
        if (is_ratify) {
            dataStr.share_type = 2
        }
        if (getProps().version_id) {
            dataStr.version_id = getProps().version_id
        }
        dataStr.share_id = share_id
        if (/* env.editor === 'ueteam' */isUeTeam && getProps().team_id) {
            dataStr.template_id = getProps().user_template_team_id
        }
        return fetch(ipsApi('/api/edit-share-log'), {
            method: 'POST',
            body: JSON.stringify(dataStr),
            credentials: 'include'
        })
    }


    // 请求此接口表示开始分享
    getEditShare(pid, key, is_ratify) {
        let dataStr = new Object();
        dataStr[key] = pid;
        if (is_ratify) {
            dataStr.share_type = 2
        }
        if (getProps().version_id) {
            dataStr.version_id = getProps().version_id
        }
        dataStr.share_source = 1
        if ((env.teamTemplate || isUeTeam || isEcommerceTeam()) && getProps().team_id) {
            dataStr.team_id = getProps().team_id
            dataStr.share_source = 2
            dataStr.template_id = getProps().user_template_team_id

        }
        const type = window.location.href.includes('ue.818ps.com') ? 'ue' : 'ecommerce';
        dataStr.type = type;
        return fetch(ipsApi('/api/get-edit-share'), {
            method: 'POST',
            body: JSON.stringify(dataStr),
            credentials: 'include',
            headers:{
                ...IPSConfig.getAuthenticatio()
            }
        })
    }

    // 获取副本链接
    createCopyLog(dataStr) {
        return fetch(ipsApi('/api/copy-paper-temple'), {
            method: 'POST',
            body: dataStr,
            credentials: 'include',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
        })
    }

    // 元素搜索词次数统计
    assetSearchStat(searchWord, type) {
        return fetch(ipsApi('/uapi/asset-search?keyword=' + searchWord + '&type=' + type), this.options)
    }

    // 元素点击次数统计（关于搜索词）
    assetClickWSearch(type, id) {
        let searchWord;

        if (type == 1) {
            searchWord = storeAdapter.getStore({
                store_name: storeAdapter.store_names.Search
            }).search.imageWord;
        } else {
            searchWord = storeAdapter.getStore({
                store_name: storeAdapter.store_names.Search
            }).search.backgroundWord;
        }
        return fetch(ipsApi('/uapi/asset-click-search?keyword=' + searchWord + '&type=' + type + '&id=' + id), this.options)
    }

    // 更新kid信息
    updateKind(utid, k1, k2) {
        return fetch(ipsApi('/api/update-kind?utid=' + utid + '&kid_1=' + k1 + '&kid_2=' + k2), this.options)
    }

    // 获取元素详情
    getAssetInfo(id) {
        return fetch(ipsApi('/api/get-asset-info?id=' + id), this.options);
    }

    // 获取元素详情
    getUserTemplateVersionslList() {
        if(env.teamTemplate || isUeTeam) {
            return fetch(ipsApi('/api/get-user-team-template-versions-list?user_team_template_id=' + getProps().upicId), this.options);
        }
        return fetch(ipsApi('/api/get-user-template-versions-list?user_template_id=' + getProps().upicId), this.options);
    }

    // 获取上传文件名
    getUploadFilename(filename, root) {
        console.trace(11111111111)
        return fetch(ipsApi('/qiniu/cdn-name?filename=' + filename + '&root=' + root), this.options);
    }

    // 获取上传oss的表单信息
    getUploadOssFormImageEffect(file, value_hint) {
        if (value_hint == undefined) {
            value_hint = 0;
        }

        return fetch(ipsApi('/api/upload-oss-form-image-effect?filename=' + file + '&value_hint=' + value_hint), this.options);
    }

    // 获取上传oss的表单信息
    getUploadOssForm(file, value_hint, orientationFlag = 0, cdnName, skip_validation,origin = '') {
        if (value_hint == undefined) {
            value_hint = 0;
        }
        let url = '/api/upload-oss-form?filename=' + file + '&value_hint=' + value_hint + '&orientationFlag=' + orientationFlag + '&cdnName=' + cdnName + '&upload_restriction=1';
        if (skip_validation) {
            url += '&skip_validation=true';
        }
        return fetch(ipsApi(url), this.options);
    }
    // 获取上传oss的表单信息
    getUploadOssFormForCutout(params) {
        const {
            fileName='', value_hint = '0', orientationFlag = '0', cdnName='', skip_validation=false,origin = '', originId='',upicId='',paperId=''
        } = params;
        const url = IpsUtils.Url.joinUrlParams('/api/upload-oss-form', {
            filename: fileName,
            value_hint,
            orientationFlag,
            cdnName,
            skip_validation,
            origin,
            originId,
            upicId,
            paperId
        })
        // let url = '/api/upload-oss-form?filename=' + fileName + '&value_hint=' + value_hint + '&orientationFlag=' + orientationFlag + '&cdnName=' + cdnName +'&origin='+origin;
        // if (skip_validation) {
        //     url += '&skip_validation=true';
        // }
        // if (originId) {
        //     url += '&originId=' + originId;
        // }
        return fetch(ipsApi(url), this.options);
    }

    // 获取 temp文件 上传oss的表单信息
    getUploadOssTempForm(file, value_hint, orientationFlag = 0, cdnName) {
        if (value_hint == undefined) {
            value_hint = 0;
        }

        return fetch(ipsApi('/api/upload-oss-temp-form?filename=' + file + '&value_hint=' + value_hint + '&orientationFlag=' + orientationFlag + '&cdnName=' + cdnName), this.options);
    }

    // 获取上传“七牛”的表单信息
    getUploadQiniuForm(file, root, fileCate = "", orientationFlag = 0, value_hint = 0, cdnName) {

        return fetch(ipsApi('/qiniu/upload-by-token?filename=' + file + '&root=' + root + '&fileCate=' + fileCate + '&orientationFlag=' + orientationFlag + "&value_hint=" + value_hint + '&cdnName=' + cdnName), this.options);
    }

    // 获取上传oss的表单信息
    getUploadOssFormNew(file, value_hint, qrDoc) {
        if (value_hint == undefined) {
            value_hint = 0;
        }

        return fetch(ipsApi('/api/upload-oss-form-new?filename=' + file + '&value_hint=' + value_hint + '&qrDoc=' + qrDoc), this.options);
    }

    // 获取设计师上传oss的表单信息
    getUploadDsrOssForm(file, fileCate, cdnName, resId='') {
        return fetch(ipsApi('/api/upload-dsr-oss-form?filename=' + file + '&fileCate=' + fileCate + '&cdnName=' + cdnName + '&resId='+ resId), this.options);
    }

    // 获取LOGO上传oss的表单信息
    getUploadLogoOssForm(file) {
        return fetch(ipsApi('/apiv2/upload-logo-oss-form?filename=' + file), this.options);
    }

    // 获取模板列表
    getTemplateList(word, kid_1, kid_2, page, ratioId, t1 = 0, t2 = 0, t3 = 0, sort_type = '', is_zb = 0, class_id = '0_0_0', { formatId, esType } = { formatId: null, esType: null }, width = 0, height = 0, ratio = -1) {
        let { info } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage,
        });
        let { picId } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if (ratioId == undefined) {
            ratioId = -1;
        }
        const ignoreCanvasSize = [213].includes(kid_2);
        if (ignoreCanvasSize) {
            width = 0;
            height = 0;
        }
        let baseApi = '/api/get-template-list?w=' + word + '&p=' + page + '&kid_1=' + kid_1 + '&kid_2=' + kid_2 + '&ratioId=' + ratioId + '&tag1=' + t1 + '&tag2=' + t2 + '&tag3=' + t3 + '&sort_type=' + sort_type + '&is_zb=' + is_zb + '&class_id=' + class_id + '&width=' + width + '&height=' + height + '&ratio=' + ratio;
        if (info.is_second) {
            baseApi += `&es_type=${esType || info.is_second}`
            if (picId) {
                baseApi += `&picId=${picId}`
            }
            if (formatId) {
                baseApi += `&format_id=${formatId}`
            }
        }
        return fetch(ipsApi(baseApi), this.options);
    }
    getOtherTemplateList(word, page, class_id, width, height, conf) {
        let baseApi = `/api/get-template-list?p=${page}`;
        if (conf) {
            baseApi += word ? `&w=${word}` : `&conf=${conf}`;
            if (width & height) {
                baseApi += `&width=${width}&height=${height}&class_id=${class_id}`;
            }
        } else if (width && height) {
            baseApi += `&w=${word}&class_id=${class_id}&width=${width}&height=${height}`;
        } else {
            baseApi += `&w=${word}&class_id=${class_id}`;
        }
        return fetch(ipsApi(baseApi), this.options);
    }

    // 获取tabs筛选模板
    getTemplateTabsList(page, class_id, width, height) {
        let baseApi = `/api/get-template-list?p=${page}&class_id=${class_id}`;
        return fetch(ipsApi(baseApi), this.options);
    }
    // 获取板式列表
    getFormatClass() {
        const { info } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage,
        });
        return fetch(ipsApi(`/api/get-format-class?kid_2=${info.kid_2}`), this.options);
    }
    // 收藏元素
    setFavAsset(assetId) {
        return fetch(ipsApi('/apiv2/fav-asset?assetId=' + assetId), this.options);
    }

    //取消收藏元素
    delFavAsset(assetId) {
        return fetch(ipsApi('/apiv2/del-fav-asset?assetId=' + assetId), this.options);
    }
    // 收藏元素带类型
    setFavTypeAsset(type, assetId) {
        return fetch(ipsApi('/apiv2/fav-asset?type=' + type + '&assetId=' + assetId), this.options);
    }

    //取消收藏元素带类型
    delFavTypeAsset(type, assetId) {
        return fetch(ipsApi('/apiv2/del-fav-asset?type=' + type + '&assetId=' + assetId), this.options);
    }
    // 图表表格收藏元素 type: 1表格，2图表
    setFavAssetTable(type, assetId) {
        return fetch(ipsApi('/apiv2/fav-tables-or-charts?type=' + type + '&assetId=' + assetId), this.options);
    }

    //图表表格取消收藏元素
    delFavAssetTable(type, assetId) {
        return fetch(ipsApi('/apiv2/del-tables-or-charts-fav?type=' + type + '&assetId=' + assetId), this.options);
    }

    //收藏模板
    setFavTemplate(templId) {
        return fetch(ipsApi('/apiv2/add-fav-template?tid=' + templId), this.options);
    }

    //取消收藏模板
    delFavTemplate(templId) {
        return fetch(ipsApi('/apiv2/del-fav-template?tid=' + templId), this.options);
    }

    //获取收藏模板列表
    getFavTemplate() {
        return fetch(ipsApi('/apiv2/get-fav-template'), this.options);
    }

    //获取收藏模板
    getFavBackground() {
        return fetch(ipsApi('/apiv2/get-fav-background'), this.options);
    }

    //获取收藏模板
    getFavImage(kw = '') {
        return fetch(ipsApi('/apiv2/get-fav-image?kw=' + kw), this.options);
    }

    //获取收藏容器
    getFavContainer() {
        return fetch(ipsApi('/apiv2/get-fav-container'), this.options);
    }

    //获取收藏形状
    getFavSVG() {
        return fetch(ipsApi('/apiv2/get-fav-s-v-g'), this.options);
    }

    //获取收藏图片
    getFavPic() {
        return fetch(ipsApi('/apiv2/get-fav-pic'), this.options);
    }

    //根据tag获取收藏图片
    getFavByTag(tagId) {
        return fetch(ipsApi('/apiv2/get-fav-by-tag?tag_id=' + tagId), this.options);
    }
    //根据tag获取收藏图片
    getFavBykind(kid_2) {
        return fetch(ipsApi('/apiv2/get-fav-by-kind?kid=' + kid_2), this.options);
    }

    //获取元素历史记录
    getAssetHistoryRecord(type) {
        return fetch(ipsApi('/apiv2/get-history-record?type=' + type), this.options);
    }

    //设置元素历史记录
    setAssetHistoryRecord(assetId, type) {
        return fetch(ipsApi('/apiv2/set-history-record?type=' + type + '&assetId=' + assetId), this.options);
    }

    //获取全部分类和类型
    getKindTagAll() {
        let { info } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage,
        });
        if (info.is_second) {
            return fetch(ipsApi('/apiv2/get-kind-tag-all?is_second=' + info.is_second), this.options);
        } else {
            return fetch(ipsApi('/apiv2/get-kind-tag-all'), this.options);
        }

    }
    // 获取模板tabs分类信息
    getTemplateTabs() {
        return fetch(ipsApi('/api/get-ue-cate'), this.options);
    }

    //获取全部分类和类型
    getKindTagAll_v2() {
        return fetch(ipsApi('/api/get-change-template-class-tree'), this.options);
    }

    //获取元素所有标签
    getAssetTagAll() {
        return fetch(ipsApi('/apiv2/get-asset-tag-all'), this.options);
    }

    //设置快捷替换图片历史记录
    setHistoryRecordQuickImage(assetId, type) {
        return fetch(ipsApi('/apiv2/set-history-record-quick-image?assetId=' + assetId + '&type=' + type), this.options);
    }

    //获取快捷替换图片历史记录
    getHistoryRecordQuickImage(type) {
        return fetch(ipsApi('/apiv2/get-history-record-quick-image?type=' + type), this.options);
    }

    //删除快捷替换图片历史记录
    delHistoryRecordQuickImage(assetId, type) {
        return fetch(ipsApi('/apiv2/del-history-record-quick-image?assetId=' + assetId + '&type=' + type), this.options);
    }

    //获取文本历史记录
    getHistoryRecordText() {
        return fetch(ipsApi('/apiv2/get-history-record-text'), this.options);
    }

    //设置文本历史记录
    setHistoryRecordText(str) {
        return fetch(ipsApi('/apiv2/set-history-record-text?str=' + str), this.options);
    }

    //获取文本收藏
    getTextFav(type) {
        if (type == undefined) {
            type = 0;
        }
        return fetch(ipsApi('/apiv2/get-text-fav?type=' + type), this.options);
    }

    //设置文本收藏
    setTextFav(txt, hint) {
        return fetch(ipsApi('/apiv2/set-text-fav?txt=' + txt + '&hint=' + hint), this.options);
    }

    //删除文本收藏
    delTextFav(id) {
        return fetch(ipsApi('/apiv2/del-text-fav?id=' + id), this.options);
    }

    //更新文本收藏
    updateTextFav(id, txt, hint) {
        return fetch(ipsApi('/apiv2/update-text-fav?id=' + id + '&txt=' + txt + '&hint=' + hint), this.options);
    }

    //生成二维码
    generateQRcode(t, forecolor, bg, transparent, fg, icon) {
        bg = JSON.stringify(bg);
        fg = JSON.stringify(fg);
        return fetch(ipsDownload('/qrcode/generate-q-rcode?t=' + t + '&forecolor=' + forecolor + '&bg=' + bg + '&transparent=' + transparent + '&fg=' + fg + '&icon=' + icon), this.options);
    }

    //保存二维码样式
    setAssetQrcode(assetId, doc) {
        return fetch(ipsApi('/apiv2/set-asset-qrcode?assetId=' + assetId + '&doc=' + doc), this.options);
        // let dataStr = 'assetId=' + assetId + '&doc=' + doc;
        //
        // return fetch(ipsApi('/apiv2/set-asset-qrcode'), {
        //     method: 'POST',
        //     body: dataStr,
        //     headers: {
        //         'Content-Type': 'application/x-www-form-urlencoded'
        //     },
        //     credentials: 'include'
        // })
    }

    //获取用户二维码列表
    GetUserQRcodeList() {
        return fetch(ipsApi('/apiv2/get-user-q-rcode-list'), this.options);
    }

    //获取用户二维码DOC
    GetUserQRcodeDoc(assetId) {
        return fetch(ipsApi('/apiv2/get-user-q-rcode-doc?assetId=' + assetId), this.options);
    }

    //获取用户logo列表
    getLogoList() {
        return fetch(ipsApi('/apiv2/get-logo-list'), this.options);
    }

    //删除logo
    delLogo(id) {
        return fetch(ipsApi('/apiv2/del-logo?id=' + id), this.options);
    }

    //获取原始模板二维码resid
    getOriginQrocdeResId() {
        return fetch(ipsApi('/api/is-erweima'), this.options);
    }
    getWxQrDecode(params) {
        const dataStr = `username=${params.username}`;
        return fetch(ipsApi('/api/wx-qrcode'), {
            method: 'POST',
            body: dataStr,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            credentials: 'include',
        });
    }

    //获取用户元素详情
    getUserAssetInfo(id) {
        return fetch(ipsApi('/apiv2/get-user-asset-info?id=' + id), this.options);
    }

    //背景点击pv统计
    clickBgPv(bid) {
        let props = getProps()
        if (!(bid > 0)) {
            return;
        }
        if (props.isClientSide) {
            return fetch(ipsPcElectron('/site-api/bg-pv?bid=' + bid + '&pid=2'), this.options);
        }
        return fetch(ipsApi('/site-api/bg-pv?bid=' + bid + '&pid=2'), this.options);
    }

    //元素点击pv统计
    clickAgPv(aid) {
        let props = getProps()
        if (!(aid > 0)) {
            return;
        }
        if (props.isClientSide) {
            return fetch(ipsPcElectron('/site-api/as-pv?aid=' + aid + '&pid=2'), this.options);
        }
        return fetch(ipsApi('/site-api/as-pv?aid=' + aid + '&pid=2'), this.options);
    }

    //获取SVG列表
    searchAssetSvg(word, p, k2) {
        return fetch(ipsApi('/apiv2/search-asset-svg?p=' + p + "&k2=" + k2 + '&word=' + word + '&pageSize=50'), this.options)
    }

    //获取图片容器列表
    searchAssetContainer(word, p, k2) {
        return fetch(ipsApi('/apiv2/search-asset-container?p=' + p + "&k2=" + k2 + '&word=' + word), this.options)
    }

    searchAssetTypeContainer() {
        return fetch(ipsApi('/apiV1/assets/index/search-asset-container'), this.options)
    }

    //获取热词列表
    getKeywordHotList(type) {
        return fetch(ipsApi('/apiv2/get-keyword-hot-list?type=' + type), this.options);
    }

    //添加PV统计
    setPv(page_id, paras = {}) {
        return new Promise((resolve, reject) => { resolve() });
        let dataStr = 'page_id=' + page_id;
        let props = getProps();
        if (props['origin']) {
            dataStr += '&origin=' + props['origin'];
        }
        /*对pram做数据检查*/
        let _pram = props['pram'];
        if (_pram) {
            try {
                let jsonStr = window.atob(_pram);
                let paramIsJSon = strIsJSON(jsonStr);
                if (paramIsJSon) {
                    dataStr += '&pram=' + _pram;
                    if (paramIsJSon['referer']) {
                        dataStr += '&referer=' + (paramIsJSon['referer']);
                    }
                }
            } catch (e) { }
        }

        if (paras.additional) {
            for (let key in paras.additional) {
                dataStr += '&' + key + '=' + paras.additional[key];
            }
        }

        let abTypeDom = document.getElementById('abType');
        if (abTypeDom.getAttribute('abType')) {
            dataStr += "&abType=" + abTypeDom.getAttribute('abType');
        }

        if (props.isClientSide) {
            return fetch(ipsPcElectron('/site/pv'), {
                method: 'POST',
                body: dataStr,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                credentials: 'include'
            });
        }
        return fetch(ipsApi('/site/pv'), {
            method: 'POST',
            body: dataStr,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            credentials: 'include'
        });
    }
    //添加PV统计 新
    setPv_new(page_id, paras = {}) {
        let dataStr = 'page_id=' + page_id;
        let props = getProps();
        if (props['origin']) {
            dataStr += '&origin=' + props['origin'];
        }
        /*对pram做数据检查*/
        let _pram = props['pram'];
        if (_pram) {
            try {
                let jsonStr = window.atob(_pram);
                let obj = JSON.parse(jsonStr);
                let paramIsJSon = strIsJSON(jsonStr);
                if (paramIsJSon) {
                    dataStr += '&pram=' + _pram;
                    for (let key in obj) {
                        if (key == 'referer') {
                            dataStr += `&${key}=${(obj[key])}`;
                        } else {
                            dataStr += `&${key}=${obj[key]}`;
                        }
                    }
                }
            } catch (e) { }
        }

        // 如果埋点没有携带 s4 参数，s4 参数就设定为编辑器 版本号 START
        if( paras.additional ){ 
            if (paras.additional.s4 === undefined) {
                Object.assign(paras.additional, {
                    s4: env.v
                });
            }
        } else {
            Object.assign(paras, {
                additional: {
                    s4: env.v
                }
            });
        }
        // 如果埋点没有携带 s4 参数，s4 参数就设定为编辑器 版本号 END


        if (paras.additional) {
            for (let key in paras.additional) {
                dataStr += '&' + key + '=' + paras.additional[key];
            }
        }

        try {
            // 添加模板 id，设计师模板 id
            let { info } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.InfoManage,
            });

            dataStr += '&tid' + '=' + (info && parseInt(info.last_templ_id) > 0 ? parseInt(info.last_templ_id) : parseInt(props['picId']));
            dataStr += '&utid' + '=' + props['upicId'];
            dataStr += `&ti=${(info && parseInt(info.last_templ_id) > 0 ? parseInt(info.last_templ_id) : parseInt(props['picId']))}&ut=${props['upicId']}`;
            // dataStr += `&ki=${info.kid_1},${info.kid_2}`
        } catch (e) {

        }

        let abTypeDom = document.getElementById('abType');
        if (abTypeDom.getAttribute('abType')) {
            dataStr += "&abType=" + abTypeDom.getAttribute('abType');
        }
        /* 新埋点请求 */
        this.setNewPv(dataStr)
        /* 新埋点请求 */
    }
    //添加页面PV统计
    setPagePv(page_id, type, templ_id, exec_time, kw, paras = {}) {
        return new Promise((resolve, reject) => { resolve() });
        kw = kw === undefined ? '' : encodeURI(kw);
        // return ;
        let props = getProps();
        let dataStr = 'page_id=' + page_id + '&type=' + type + '&templ_id=' + templ_id + '&load_time=' + exec_time + '&kw=' + kw;
        if (props['origin']) {
            dataStr += '&origin=' + props['origin'];
        }
        /*对pram做数据检查*/
        let _pram = props['pram'];
        if (_pram) {
            try {
                let jsonStr = window.atob(_pram);
                let paramIsJSon = strIsJSON(jsonStr);
                if (paramIsJSon) {
                    dataStr += '&pram=' + _pram;
                    if (paramIsJSon['referer']) {
                        dataStr += '&referer=' + (paramIsJSon['referer']);
                    }
                }
            } catch (e) { }
        }
        if (page_id === 151) {
            dataStr += '&waitTime=' + (new Date().getTime() - window.startTime) / 1000;
        }
        if (paras['isDpi']) {
            dataStr += '&isDpi=' + paras['isDpi'];
        }
        if (paras['dpi']) {
            dataStr += '&dpi=' + paras['dpi'];
        }

        if (props.isClientSide) {
            return fetch(ipsPcElectron('/site/pv'), {
                method: 'POST',
                body: dataStr,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                credentials: 'include'
            });
        }

        return fetch(ipsApi('/site/pv'), {
            method: 'POST',
            body: dataStr,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            credentials: 'include'
        });
    }
    //添加页面PV统计 新
    setPagePv_new(page_id, type, templ_id, exec_time, kw, paras = {}) {
        kw = kw === undefined ? '' : encodeURI(kw);
        // return ;
        let tempName = '';
        if (type == 'template') {
            tempName = 'ti'
        } else if (type == 'user') {
            tempName = 'ut'
        } else if (type == 'background') {
            tempName = 'ai'
        }
        let props = getProps();
        let dataStr = 'page_id=' + page_id + '&type=' + type + `${tempName ? '&' + tempName + '=' + templ_id : ''}` + '&load_time=' + exec_time * 1000 + '&kw=' + kw;
        if (props['origin']) {
            dataStr += '&origin=' + props['origin'];
        }
        if (props['upicId'] && tempName != 'ut') {
            dataStr += '&ut=' + props['upicId'];
        }
        /*对pram做数据检查*/
        let _pram = props['pram'];
        if (_pram) {
            try {
                let jsonStr = window.atob(_pram);
                let obj = JSON.parse(jsonStr);
                let paramIsJSon = strIsJSON(jsonStr);
                if (paramIsJSon) {
                    dataStr += '&pram=' + _pram;
                    for (let key in obj) {
                        if (key == 'referer') {
                            dataStr += `&${key}=${(obj[key])}`;
                        } else {
                            dataStr += `&${key}=${obj[key]}`;
                        }
                    }
                }
            } catch (e) { }
        }
        if (page_id === 151) {
            dataStr += '&waitTime=' + (new Date().getTime() - window.startTime) / 1000;
        }
        if (paras['isDpi']) {
            dataStr += '&isDpi=' + paras['isDpi'];
        }
        if (paras['dpi']) {
            dataStr += '&dpi=' + paras['dpi'];
        }
        if (paras.s0) {
            dataStr += '&s0=' + paras.s0;
        }
        if (paras.i0) {
            dataStr += '&i0=' + paras.i0;
        }
        if (paras.i2) {
            dataStr += '&i2=' + paras.i2
        }
        if (paras.i1) {
            dataStr += '&i1=' + paras.i1;
        }
        if (paras.i3) {
            dataStr += '&i3=' + paras.i3;
        }
        if (paras.ti) {
            dataStr += '&ti=' + paras.ti;
        }

        /* 新埋点请求 */
        this.setNewPv(dataStr)
        /* 新埋点请求 */
    }
    setNewPv(dataStr = '', options = {}) {
        let { rt_base64_cookie_tag } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        let props = getProps();
        let paramsArr = dataStr.split('&'),
            newParams = [], newParamsStr = '', deleteParams = ['pram', 'option', 'type'] /* 需要删除的属性*/;
        paramsArr.map(v => {
            let itemArr = v.split('='),
                newKey = this.newPvTransferTools(itemArr[0]);
            /*过滤控制 和值等于 undefined的  */
            if (itemArr[1] && itemArr[1] != 'undefined' && itemArr[1] != 'NaN' && deleteParams.indexOf(newKey) < 0) {
                newParams.push(`${newKey}=${itemArr[1]}`)
            }
        })
        let url = location.href
        if (props['pram'] && !replaceHost) {
            try {
               const jsonStr = window.atob(props['pram']);
                const obj = JSON.parse(jsonStr);
                replaceHost = obj['editor_name'] ?? 'ue'
                if(props['team_id']){
                    replaceHost += 'team'
                }
                replaceHost += '.818ps.com'
            } catch (e) { }
        }
        if (replaceHost) {
            url = url.replace(/\/\/[^\/]+/, '//' + replaceHost )
        }
        /* 获取cookie */
        let userInfoCookie = '';
        userInfoCookie = this.getCookieTools('ui_818ps');
        userInfoCookie ? userInfoCookie = encodeURIComponent(userInfoCookie) : '';
        !userInfoCookie ? userInfoCookie = rt_base64_cookie_tag : '';
        /* 获取cookie */
        userInfoCookie && newParams.push(`ui=${userInfoCookie}`)//用户信息
        newParams.push(`pt=${props.clientPt || props.isClientSide ? 'win' : 'web'}`)//站点信息
        newParams.push(`u=${encodeURIComponent(url)}`)//用户地址信息
        newParams.push(`v=${new Date().getTime()}`)//添加时间戳
        if (props.isClientSide && props.win_version) {
            newParams.push(`ver=${props.win_version}`)//客户端版本号
        }
        newParamsStr = newParams.join('&')

        return fetch('//p.818ps.com/p.gif?' + newParamsStr, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            mode: "no-cors",//后台做过配置 设置为cors会报错
            credentials: 'include'
        }).catch(error => {
            // 捕获错误，防止影响其他功能
        });
    }
    getCookieTools(name) {
        //可以搜索RegExp和match进行学习
        var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
        if (arr = document.cookie.match(reg)) {
            return unescape(arr[2]);
        } else {
            return null;
        }
    }
    /**
     * 转换新pv参数名
     * @param {*} oldKey
     */
    newPvTransferTools(oldKey = '') {
        let keysArr = [//新老键名对比
            { old: 'page_id', new: 'pid' },
            { old: 'referer', new: 'r' },
            { old: 'origin', new: 'ro' },
            { old: 'keyword', new: 'kw' },
            { old: 'abType', new: 'ab' },
            { old: 'templ_id', new: 'ti' },
            { old: 'user_templ_id', new: 'ut' },
            { old: 'load_time', new: 'i0' },
            { old: 'class_id', new: 'ki' },
            { old: 'route_id', new: 'ri' },
            { old: 'route', new: 'rb' },
            { old: 'after_route', new: 'ra' },
            { old: 'dom_load_time', new: 'dom_load_time' },
            { old: 'img_load_time', new: 'img_load_time' },
        ],
            resKey = oldKey;

        for (let i = 0, len = keysArr.length; i < len; ++i) {
            let item = keysArr[i]
            if (oldKey == item.old) {
                resKey = item.new
                break;
            }
        }
        return resKey
    }

    setDownloadPv(page_id, time) {
        return new Promise((resolve, reject) => { resolve() });
        let dataStr = 'page_id=' + page_id + '&load_time=' + time;
        let props = getProps();
        if (props['origin']) {
            dataStr += '&origin=' + props['origin'];
        }
        /*对pram做数据检查*/
        let _pram = props['pram'];
        if (_pram) {
            try {
                let jsonStr = window.atob(_pram);
                let paramIsJSon = strIsJSON(jsonStr);
                if (paramIsJSon) {
                    dataStr += '&pram=' + _pram;
                    if (paramIsJSon['referer']) {
                        dataStr += '&referer=' + (paramIsJSon['referer']);
                    }
                }
            } catch (e) { }
        }

        if (props.isClientSide) {
            return fetch(ipsPcElectron('/site/pv'), {
                method: 'POST',
                body: dataStr,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                credentials: 'include'
            });
        }

        return fetch(ipsApi('/site/pv'), {
            method: 'POST',
            body: dataStr,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            credentials: 'include'
        });
    }
    setDownloadPv_new(page_id, time, options = {}) {
        let dataStr = 'page_id=' + page_id + '&load_time=' + time * 1000
        let props = getProps();
        if (props['origin']) {
            dataStr += '&origin=' + props['origin'];
        }
        dataStr += '&ut=' + props['upicId'];
        for (let key in options) {
            dataStr += `&${key}=${options[key]}`;
        }
        /*对pram做数据检查*/
        let _pram = props['pram'];
        if (_pram) {
            try {
                let jsonStr = window.atob(_pram);
                let paramIsJSon = strIsJSON(jsonStr);
                if (paramIsJSon) {
                    dataStr += '&pram=' + _pram;
                    if (paramIsJSon['referer']) {
                        dataStr += '&referer=' + (paramIsJSon['referer']);
                    }
                }
            } catch (e) { }
        }

        /* 新埋点请求 */
        this.setNewPv(dataStr)
        /* 新埋点请求 */
    }

    /**
     * 预览并分享
     */
    getPreviewAndShare(param) {
        let props = getProps(),
            page = param['page'] >= 0 ? param['page'] : 0;
        param['page'] == 'all' ? page = 'all' : '';
        return fetch(ipsDownload('/render/preview-and-share-v1?tid=' + props['upicId'] + "&page=" + page), this.options);
    }

    /**
     * 预览并分享（分享）
     */
    getPreviewAndShareWX(param) {
        let props = getProps();
        return fetch(ipsDownload('/render/preview-and-share-w-x?tid=' + props['upicId']), this.options);
    }

    /**
     * 获取微信公众号授权链接
     */
    getWebChatOpenPlatformAuthLink(redirect_uri) {
        return fetch(ipswechatopenplatform('/we-chat-open-platform/get-auth-link?redirect_uri=' + escape(redirect_uri)), this.options);
    }

    /**
     * 获取已绑定微信公众号
     */
    getUserWeChatList() {
        return fetch(ipsApi('/api/get-wechat-list'), this.options);
        // return fetch(ipswechatopenplatform('/api/get-wechat-list'), this.options);
    }

    /**
     * 微信公众平台同步素材
     * appid:string
     */
    uploadWeChatPublicMaterial(appid, tid) {
        let formData = new FormData();
        formData.append("appid", appid);
        formData.append("tid", tid)
        const team_id = isUeTeam && getProps().team_id
        if (team_id) {
            formData.append("team_id", team_id);
        }
        return fetch(ipsApi('/api/share-wechat'), { method: 'POST', body: formData, credentials: 'include' });
    }

    // https://wechatopenplatform.818ps.com/api/upload-image-wechat
    /**
     * 上传图片到微信公众号
     */
    uploadImageWechat(appid, url) {
        let props = getProps();
        // return fetch(ipsApi('/api/upload-image-wechat?appid=' + appid + '&url=' + url), this.options);
        return fetch(ipswechatopenplatform('/api/upload-image-wechat?appid=' + appid + '&url=' + url + '&upicId=' + props['upicId']), this.options);
    }

    /**
     * 点击模板发送
     */
    setTemplateClick(tid) {
        // return fetch(ipsApi('/api/upload-image-wechat?appid=' + appid + '&url=' + url), this.options);
        return fetch(ipsApi('/apiv2/template-click?tid=' + tid), this.options);
    }

    /**
     * 绑定手机发送验证码
     */
    sendValidateCodeph(phone, options = {}, token) {
        let str = '';
        for (let key in options) {
            let v = options[key];
            str += `&${key}=${v}`
        }
        // return fetch(ipsApi('/site-api/send-validate-code-ph?num=' + phone + '&codeImg=' + codeImg), this.options);
        return fetch(ipsApi('/site-api/send-validate-code-ph?num=' + phone + str + "&sms_token=" + token), this.options);
    }

    /**
     * 获取请求绑定过的次数
     */
    getPostBingPhoneCount() {
        return fetch(ipsApi('/site-api/get-ip-redis'), this.options);
    }
    /**
     * 绑定手机
     * @param num
     * @param code
     */
    bindPh(num, code) {
        return fetch(ipsApi('/site-api/bind-ph?num=' + num + '&code=' + code), this.options);
    }

    /**
     * 预览图（设计师）
     */
    showPreviewApi() {
        let props = getProps();
        return fetch(ipsDownload('/render/showpreview-api?tid=' + props['picId']), this.options);
    }

    templateRemakeUser(tid, qq) {
        return fetch(ipsApi('/apiv2/template-remake-user?templ_id=' + tid + '&qq=' + qq), this.options);
    }

    /**
     * 获取用户剩余下载量
     */
    getDownloadNumInfo(picId) {
        let props = getProps();
        let { info } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage,
        });
        const { page_map } = storeAdapter.getStore({
          store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (picId) {
            // 如果指定 picId, 用来在添加模板前检查有没有对应的下载权限
            return fetch(ipsApi('/apiv2/get-download-num-info?picid='+ picId), this.options);
        }
        const pageTempList = Object.values(page_map).join(',')
        const tempClassify = info.template_type == 3 ? 'ppt' :''
        if (props && (props['upicId'] || props['picId'])) {
            return fetch(ipsApi('/apiv2/get-download-num-info?page_map='+ pageTempList +'&upicid=' + (props['upicId'] ? props['upicId'] : undefined) + '&picid=' + (info.last_templ_id ? info.last_templ_id : info.id ? info.id : props.picId) + '&team_id=' + (props['team_id'] ? props['team_id'] : '') + '&classify=' + tempClassify), this.options);
            // return fetch(ipsApi('/apiv2/get-download-num-info?upicid='+(props['upicId'] ? props['upicId'] : undefined) +'&picid='+(info.last_templ_id ? info.last_templ_id : info.id ? info.id : props.picId)), this.options);
        }
        return fetch(ipsApi('/apiv2/get-download-num-info?page_map='+ pageTempList), this.options);
    }

    /**
     * 获取用户剩余抠图次数
     */
    getCutOutNumInfo() {
        return fetch(ipsApi('/api/matting-atuth-num'), this.options);
    }
    /**
     * 获取老用户赠送抠图次数
     */
    getCutOutBenefitsNum() {
        return fetch(ipsApi('/api/get-give-matting-count'), this.options);
    }
    /**
     * 领取老用户赠送抠图次数
     */
    submitCutOutBenefits() {
        return fetch(ipsApi('/api/give-matting-count'), this.options);
    }
    /**
     * 获取特效字列表
     */
    getSpecificWordList(navType) {
        navType = navType === undefined ? 0 : navType;

        return fetch(ipsApi('/apiv2/get-specific-word-list?class_id=' + navType), this.options);
        // return fetch(ipsApi('/apiv2/get-specific-word-list-test'), this.options);
    }

    /**
     * 获取特效字分类
     */
    getSpecificWordClass() {
        return fetch(ipsApi('/apiv2/get-specific-word-class'), this.options);
    }

    /**
     * 获取特效字详情
     */
    getSpecificWordInfo(id) {
        return fetch(ipsApi('/apiv2/get-specific-word-info-new?id=' + id), this.options);
    }

    /**
     * 提交组合字
     * @param data
     */
    submitGroupWord(data) {
        let postData = {
            doc: JSON.stringify(data.doc),
            cateId: data.cateId,
            groupWordId: data.groupWordId,
            task_id: data.task_id,
            isAI: data.isAI,
            isNew: parseInt(data.isNew),
            title: data.title
        };
        return fetch(ipsDownload('/api/submit-group-word-new'), {
            method: 'POST',
            // body: "doc=" + JSON.stringify(data.doc) + "&cateId=" + data.cateId + "&groupWordId=" + data.groupWordId + "&isAI=" + data.isAI,
            body: JSON.stringify(postData),
            headers: {
                // 'Content-Type': 'application/x-www-form-urlencoded'
                'Accept': 'application/json'
            },
            credentials: 'include'
        });
    }

    /**
     * 获取组合字
     * @constructor
     */
    getGroupWordList(page, cateId = 0) {
        let { isDesigner } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        return fetch(ipsApi('/api/get-group-word-list-new?p=' + page + '&cateId=' + cateId + "&isDesigner=" + isDesigner), this.options);
    }

    /**
     * 获取组合字
     * @constructor
     * @param { string } word kw
     * @param { number } page
     * @param { string } searchText 搜索框输入文字
     * @param { 0 | 1 } all 搜索是否是全部-包含所有单个分类
     */
    getGroupWordListNew(word, page, searchText = '', all = 0) {
        return fetch(ipsApi('/api/search-groupword-list?w=' + word + '&p=' + page + `&search=${searchText}&all=${all}`), this.options);
    }

    /**
     * 获取组合字详情
     */
    getGroupWordInfo(id) {
        return fetch(ipsApi('/api/get-group-word-info?id=' + id), this.options);
    }

    /**
     * 获取组合字搜索分类
     */
    getGroupWordTags() {
        return fetch(ipsApi('/api/get-designer-recommend-tags'), this.options)
    }

    /**
     * 获取模板标签完整数组
     */
    getTemplateClassArr() {
        return fetch(ipsApi('/api/get-template-class-arr'), this.options);
    }

    /**
     * 获取模板标签完整数组
     * @desc 一级二级分类
     */
    getTemplateClassInfo(picId, kid) {
        return fetch(ipsApi(`/api/get-template-class-info?picId=${picId}&kid=${kid}`), this.options);
    }

    /**
     * 获取模板标签完整数组
     */
    getTemplateClassLinks(picId) {
        let props = getProps();
        return fetch(ipsApi('/api/get-template-class-links?id=' + picId), this.options);
    }

    /**
     * 添加模板标签
     * @param class_id
     */
    addTemplateClassLink(class_id, picId = '', isSingle = 0) {
        let props = getProps();
        picId = picId || props.picId;
        return fetch(ipsApi('/api/add-template-class-link?template_id=' + picId + "&class_id=" + class_id + `${isSingle == 1 ? '&single=1' : ''}`), this.options);
    }

    /**
     * 删除模板标签
     * @param class_id
     */
    delTemplateClassLink(class_id, picId = '') {
        let props = getProps();
        picId = picId || props.picId;
        return fetch(ipsApi('/api/del-template-class-link?template_id=' + picId + "&class_id=" + class_id), this.options);
    }

    /**
     * 获取用户模板列表
     */
    getUserTempls() {
        if (env.teamTemplate || isUeTeam) {
            return fetch(ipsApi(`/api/get-user-templs-team?team_id=${getProps().team_id}`), this.options);
        } else {
            return fetch(ipsApi('/api/get-user-templs'), this.options);
        }
    }


    /**
     * 获取我的设计，我的-设计-图片（全部）
     */
    myDesignerForTemplate(page = 1, limit = 30) {
        return fetch(ipsApi(`/api/my-designer-for-template?type=1&page=${page}&limit=${limit}`), this.options);
    }
    /**
     * 获取我的设计，我的-设计-星标
     */
    getStarTemplate(page = 1, limit = 30) {
        return fetch(ipsApi(`/api/get-star-template?type=1&page=${page}&limit=${limit}`), this.options);
    }
    /**
     * 获取我的收藏，我的-收藏-模版
     */
    myFavForTempl(page = 1, limit = 30) {
        return fetch(ipsApi(`/api/my-fav-for-templ?template_type=1&page=${page}&limit=${limit}`), this.options);
    }
    /**
     * 获取我的收藏，我的-收藏-素材
     */
    getFavList(page = 1, limit = 30) {
        return fetch(ipsApi(`/api/get-fav-list?page=${page}&limit=${limit}`), this.options);
    }
    /**
     * 获取我的收藏，我的-收藏-专题
     */
    myFavForAlbum(page = 1, limit = 30) {
        return fetch(ipsApi(`/api/my-fav-for-album?page=${page}&limit=${limit}`), this.options);
    }
    /**
     * 我的设计，我的-设计-添加\取消星标
     * @param  tid
     */
    addCancelStar(tid) {
        let formData = new FormData();
        formData.append("tid", tid)
        return fetch(ipsApi('/api/add-cancel-star'), { method: 'POST', body: formData, credentials: 'include' });
    }
    /**
     * 我的设计，我的-设计-修改设计标题
     * @param  id,title
     */
    saveTemplateTitle(id, title) {
        let formData = new FormData();
        formData.append("id", id)
        formData.append("title", title)
        return fetch(ipsApi('/api/save-title'), { method: 'POST', body: formData, credentials: 'include' });
    }
    /**
     * 我的设计，我的-收藏-专题-取消收藏
     * @param aid
     */
    removeAlbumFav(aid) {
        let formData = new FormData();
        formData.append("aid", aid)
        return fetch(ipsApi('/api/remove-album-fav'), { method: 'POST', body: formData, credentials: 'include' });
    }
    /**
     * 我的设计-设计-删除设计
     */
    delDdsn(id) {
        return fetch(ipsApi(`/api/del-udsn?is_paper=0&id=${id}`), this.options);
    }
    /**
     * 我的-收藏-专题-专题详情
     */
    albumSubject(aid) {
        return fetch(ipsApi(`/api/album-subject?aid=${aid}`), this.options);
    }
    /**
     * 我的-收藏-专题-专题详情
     */
    mySubscribeAlbum() {
        return fetch(ipsApi(`/api/my-subscribe-album`), this.options);
    }

    /**
     * 第三方应用，谷歌emoji
     */
    getGoogleEmoji(keyword) {
        return fetch(ipsApi(`/api/get-google-emoji?keyword=${keyword}`), this.options);
    }
    /**
     * 第三方应用，腾讯地图搜索
     */
    searchTencentMap(page = 1, keyword = '') {
        return fetch(ipsApi(`/api/search-tencent-map?keyword=${keyword}&page=${page}&limit=${10}`), this.options);
    }
    /**
     * 第三方应用，百度网盘-获取授权链接
     */
    getBaiduNetdiskAuthUrl() {
        return fetch(ipsApi(`/api/get-baidu-netdisk-auth-url`), this.options);
    }
    /**
     * 第三方应用，百度网盘-删除授权
     */
    delBaiduNetdiskAuth() {
        return fetch(ipsApi(`/api/del-baidu-netdisk-auth`), this.options);
    }
    /**
     * 第三方应用，百度网盘-获取图片列表
     */
    getBaiduNetdiskImgList(page = 1, keyword = '') {
        return fetch(ipsApi(`/api/get-baidu-netdisk-img-list?keyword=${keyword}&page=${page}&limit=${20}`), this.options);
    }

    getTencentMapImgPreview(lat, lng, sideLength) {
        return fetch(ipsApi(`/api/get-tencent-map-img-preview?lat=${lat}&lng=${lng}&sideLength=${sideLength}`), this.options);
    }

    /**
     * 第三方应用，百度网盘-获取本地图片链接
     * @param  fs_id,dlink, filename,width,height
     */
    getBaiduNetdiskImgPreview(fs_id, dlink, filename, width, height) {
        let formData = new FormData();
        formData.append("fs_id", fs_id)
        formData.append("dlink", dlink)
        formData.append("filename", filename)
        formData.append("width", width)
        formData.append("height", height)
        return fetch(ipsApi('/api/get-baidu-netdisk-img-preview'), { method: 'POST', body: formData, credentials: 'include' });
    }


    /**
     * 获取设计师模板列表
     */
    getDesignerTemplList() {
        let props = getProps();
        return fetch(ipsApi('/api/get-not-online-tem?templateid=' + props.picId), this.options);
    }

    copyPreview(upicId, picId) {
        return fetch(ipsDownload('/api/copy-preview?upicId=' + upicId + '&picId=' + picId), this.options);
    }

    /**
     * 获取图片特效列表
     */
    getImageEffectList() {
        return fetch(ipsApi('/api/get-image-effect-list'), this.options);
    }

    /**
     * 获取图片特效详情
     * @param id
     */
    getImageEffectInfo(id) {
        return fetch(ipsApi('/api/get-image-effect-info?id=' + id), this.options);
    }

    /**
     * 获取手机上传Url
     */
    getUploadFilePhUrl() {
        return fetch(ipsApi('/api/get-upload-file-ph-url'), this.options);
    }

    /**
     * 检测手机上传状态
     * @param param
     */
    checkUploadFilePh(param) {
        return fetch(ipsApi('/api/check-upload-file-ph?param=' + param), this.options);
    }

    /**
     * 精品数据获取
     */
    getSupermeAsset(param) {
        return fetch(ipsApi('/apiv2/get-superme-asset?&type=' + param.type + '&page=' + param.page));
    }

    /**
     * 获取吐槽元素
     * @param {*} param
     */
    getRoastAsset(param) {
        return fetch(ipsApi('/api/get-roast-asset?type=' + param.type + "&pages=" + param.page));
    }
    /**
     * 编辑器内登录获取图片验证码
     * @param {*} param
     */
    getLoginValidateImg() {
        return fetch(ipsApi('/site-api/validate-code-img'), this.options);
    }
    /**
     * 发送验证码
     * @param {*} param
     */
    sendLoginValidateInfo(param) {
        // return fetch(ipsApi(`/site-api/send-tel-login-code?num=${param.phoneNum}&codeImg=${param.validNum}`),this.options);
        return fetch(ipsApi(`/site-api/send-tel-login-code?num=${param.phoneNum}&sms_token=${param.token}`), this.options);
    }
    /**
    * 编辑器内登录获取图片验证码
    * @param {*} param
    */
    checkValidateTimes(param) {
        return fetch(ipsApi(`/validate/check-tel-hua-kuai?ticket=${param.ticket}&rendstr=${param.rendstr}&tel=${param.tel}`), this.options);
    }
    /**
     * 退出
     * @param {*} param
     */
    sendLoginOut(param) {
        return fetch(ipsApi(`/site/logout`), this.options);
    }
    /**
     * 点击手机登录
     * @param {*} param
     */
    sendLoginPanelIn(param) {
        // const { phoneNum , validNum , phoneMsgNum } = param;
        const { phoneNum, phoneMsgNum } = param;
        let crsfCookie = document.cookie['_csrf'] || '';
        // let postData = `refer=${validNum}&telCode=${phoneMsgNum}&telNum=${phoneNum}`;//&_csrf=${crsfCookie
        let postData = `telCode=${phoneMsgNum}&telNum=${phoneNum}`;//&_csrf=${crsfCookie
        // return fetch(ipsApi('/site/tel-login-callback?isAjax=1&'+postData),this.options);

        return fetch(ipsApi(`/site/tel-login-callback?isAjax=1`), {
            method: 'POST',
            body: postData,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            credentials: 'include'
        });
    }
    /**
     * 获取canvas尺寸列表
     * @param {*} param
     */
    getCanvasSizeList(param) {
        let { isDesigner } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        let url = `/api/get-class-kind?is_client=${Number(!isDesigner)}`;
        if (param) {
            url += `&kid_1=${param}`
        }
        return fetch(ipsApi(url), this.options);
    }

    getMySizeList() {
        const url = '/user-template/get-ut-size'
        return fetch(ipsApi(url), this.options).then(response => response.json());
    }

    saveMySize(params) {
        const { width, height, name, unit } = params;
        let dataStr = `w=${width}&h=${height}&name=${name}&units=${unit}`;
        
        return fetch(ipsApi(`/user-template/add-ut-size`), {
            method: 'POST',
            body: dataStr,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            credentials: 'include',
        }).then(response => response.json());
    }

    delMySize(id) {
        let dataStr = `id=${id}`;
    
        return fetch(ipsApi(`/user-template/del-ut-size`), {
            method: 'POST',
            body: dataStr,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            credentials: 'include',
        }).then(response => response.json());
    }

    /**
     * 获取贴纸的简略分类及部分精选数据
     * @param {*} param
     */
    getStickerBriefList(param) {
        return fetch(ipsApi(`/api/asset-rec`), this.options);
    }

    /**
     * 获取PPT表格列表
     */
    getPPTTables() {
        return fetch(ipsApi("/apiv2/ppt-tables?type=1"), this.options);
    }


    /**
     * 获取PPT表格详情
     */
    getPPTTableDetail(id) {
        return fetch(ipsApi("/apiv2/ppt-table-detail?id=" + id), this.options);
    }

    /**
     * 获取团队素材
     */
    getTeamAssets(id) {
        return fetch(ipsApi("/apiv2/get-my-team-asset"), this.options);
    }



    /**
     * 保存到团队项目
     */
    saveToTeam(team_id, utid, fid) {
        // return fetch(ipsApi("/apiv2/get-my-team-asset"), this.options);
        // https://818ps.com/team/add-project
        // team_id: 团队id
        // utid: 用户模板id
        // fid: 文件夹id

        let dataStr = `team_id=${team_id}&utid=${utid}&fid=${fid}`;
        if (env.teamTemplate || isUeTeam) {
            dataStr = `team_id=${team_id}&uttid=${getProps().user_template_team_id}&fid=${fid}`;
        }
        return fetch(ipsApi(`/apiv2/add-project`), {
            method: 'POST',
            body: dataStr,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            credentials: 'include',
        });
    }

    /**
     * 获取团队素材https://818ps.com/apiv2/get-my-team-and-folder
     */
    getTeamDetailInfomation() {
        return fetch(ipsApi("/apiv2/get-my-team-and-folder"), this.options);
    }

    /**
     * 获取团队内容 /apiv2/get-my-team-content?team_id=&type=&folder_id=&pages=
     */
    getTeamDetailContent(team_id, type, folder_id, pages) {
        return fetch(ipsApi(`/apiv2/get-my-team-content?template_type=1&team_id=${team_id}&type=${type}&folder_id=${folder_id}&pages=${pages}`), this.options);
    }

    getTeamName() {
        return fetch(ipsApi("/apiv2/get-my-team-list"), this.options);
    }

    getTeamFloder(team_id, type) {
        return fetch(ipsApi(`/apiv2/get-my-team-folder?team_id=${team_id}&type=${type}`), this.options);
    }

    getTeamBrandFont(team_id, brand_id) {
        return fetch(ipsApi(`/apiv2/get-my-team-brand-font?team_id=${team_id}&brand_id=${brand_id}`), this.options);
    }

    getTeamBrandColor(team_id, brand_id) {
        return fetch(ipsApi(`/apiv2/get-my-team-brand-color?team_id=${team_id}&brand_id=${brand_id}`), this.options);
    }

    getTeamBrand() {
        return fetch(ipsApi(`/apiv2/get-my-team-brand`), this.options);
    }


    /*********电商编辑器**********/
    /**
     * 获取模板是否是多尺寸模板及对应信息
     */
    getOnlineAssetsInfo({ kid_2, pic_id }) {
        // 如果传入pic_id，则以pic_id自身的kid_2为主，如果不存在pic_id，则以传入的kid_2为准
        let baseUrl = `kid_2=${kid_2}`
        if (pic_id) {
            baseUrl += `&pic_id=${pic_id}`
        }
        return fetch(ipsApi(`/apiv2/get-kind-link-size?${baseUrl}`), this.options);
    }
    /**
     * 保存多尺寸子模板和主模板之间的关联关系
     */
    saveOnlineTemplateSonToParent(params = {}) {
        const { parentId, sonId, width, height } = params;
        return fetch(ipsApi(`/apiv2/save-kind-size-tid?tid=${parentId}&link_tid=${sonId}&width=${width}&height=${height}`), this.options);
    }
    /**
     * 获取多尺寸模板详细信息
     */
    getOnlineTemplateDetail(params = {}) {
        const { tid } = params;
        return fetch(ipsApi(`/apiv2/get-kind-size-link-info?tid=${tid}`), this.options);
    }
    /**
     * 用户获取多尺寸模板详细信息
     */
    getOnlineTemplateDetail_user(params = {}) {
        const { tid, kid_1 } = params;
        if (kid_1) {
            return fetch(ipsApi(`/apiv2/user-get-kind-size-link-info?tid=${tid}&kid_1=${kid_1}`), this.options);
        } else {
            return fetch(ipsApi(`/apiv2/user-get-kind-size-link-info?tid=${tid}`), this.options);
        }

    }
    /**
     * 电商详情 关联单个楼层信息
     */
    setOnlineDetailEachFloorInfo(params = {}) {
        const { tid = '', ptid, part = '' } = params;
        let apiUrl = `/api/templ-part?ptid=${ptid}`
        tid ? apiUrl += `&tid=${tid}` : '';
        part ? apiUrl += `&part=${part}` : '';

        //tid - 新建楼层ID , ptid - 主模板ID , part - 楼层下标 1开始
        return fetch(ipsApi(apiUrl), this.options);
    }
    /*********电商编辑器**********/

    /**
     * 获取词云蒙版素材
     */
    wordCloudList() {
        return fetch(ipsApi(`/api/word-cloud-list`), this.options);
    }

    // https://818ps.com/api/filter-text
    /**
     * 敏感词过滤
     */
    filterText(text) {
        // return fetch(ipsApi(`/api/filter-text`), this.options);
        let dataStr = `text=${JSON.stringify(text)}`;
        return fetch(ipsApi(`/api/filter-text`), {
            method: 'POST',
            body: dataStr,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            credentials: 'include',
        });
    }

    // https://test-web.818ps.com/api/update-user-asset
    /**
     * 修改上传图片标题
     */
    changeUploadPicTitle(id, title) {
        // return fetch(ipsApi(`/api/filter-text`), this.options);
        let dataStr = `id=${id}&title=${title}`;
        // let dataStr = {
        //     id: id,
        //     title: title
        // } ;
        return fetch(ipsApi('/api/update-user-asset'), {
            method: 'POST',
            body: dataStr,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            credentials: 'include',
        });
    }


    /**
     * 获取贴纸元素列表
     */
    getRecommendAssetList(type) {
        return fetch(ipsApi(`/api/get-recommend-asset?type=` + type), this.options);
    }

    /**
     * 获取左侧编辑器素材为你推荐数据
     */
    getForYouRecommendList(desc, page) {
        return fetch(ipsApi(`/apiv2/for-you-recommend?desc=` + desc + '&page=' + page), this.options);
    }

    /**
     * 获取设计师推荐元素列表
     */
    getDesignerRecommendAssetList(type) {
        return fetch(ipsApi(`/api/get-designer-recommend-asset?type=` + type), this.options);
    }

    /**
     * 创建副本
     */
    createCopyTemplate() {
        let props = getProps();
        return fetch(ipsApi(`/api/copy-temple?upicid=` + props['upicId']), this.options);
    }

    // //保存用户上传的asset
    // saveAsset(asset) {
    //     return fetch(ipsApi('/api/userasset/'+asset.id), {
    //         method: 'PUT',
    //         body: JSON.stringify(asset),
    //         headers: {
    //             'Accept': 'application/json'
    //         },
    //         credentials: 'include'
    //     });
    // }
    // -----------------图表---------------------------
    /**
     * 获取图表分类列表
     */
    getChartClass() {
        return fetch(ipsApi("/api/get-chart-class"), this.options);
    }

    /**
     * 获取图表列表
     */
    getPPTCharts(id) {
        if (!id) {
            id = 0;
        }
        return fetch(ipsApi("/apiv2/get-ppt-charts?id=" + id), this.options);
    }


    /**
     * 获取图表详情
     */
    getPPTChartDetail(id) {
        return fetch(ipsApi("/apiv2/get-ppt-chart-detail?id=" + id), this.options);
    }

    /**
     * 获取模板数量
    */
    getTemplateNum() {
        return fetch(ipsApi("/api/get-template-num"), this.options);
    }

    /**
     * 获取审核状态列表
    */
    getApprovalShareList() {
        let urlProps = getProps();
        return fetch(ipsApi(`/api/get-approval-share-list?user_template_id=${urlProps.upicId}&share_type=2`), this.options);
    }


    /**
     * 获取配色方案列表
     * @param {*} param
     */
    getColorTable() {
        return fetch(ipsApi("/api/get-color-table"), this.options);
    }

    /**
     *获取推荐热词
     * @param {*} param
    */
    getRecommendWords(id) {
        return fetch(ipsApi("/apiv2/get-search-keyword-list?type=" + id), this.options);
    }


    /**
     * 获取所有图表列表
     * @param {*} param
     */
    getChartsAll() {
        return fetch(ipsApi("/apiv2/get-ppt-charts-all"), this.options);
    }

    // -----------------图表---------------------------


    // https://818ps.com/pay/get-qrcode
    /**
     * 敏感词过滤
     */
    getRechargeAmount(type, pay_mode) {

        // return fetch(ipsApi(`/api/filter-text`), this.options);
        let dataStr = `type=${type}&pay_origin=ueEditor&classify=1&update=1&pay_mode=${pay_mode}`;
        return fetch(ipsApi(`/pay/get-qrcode`), {
            method: 'POST',
            body: dataStr,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            credentials: 'include',
        });
    }

    getCheckPay() {
        return fetch(ipsApi(`/pay/check-pay`), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            credentials: 'include',
        });
    }

    getPayType() {
        return fetch(ipsApi("/pay/get-pay-type"), this.options);
    }
    getTemTaskKeyword(params) {
        const _params = []
        Object.keys(params).forEach((key) => {
            if (params[key]) {
                _params.push(`${key}=${params[key]}`)
            }
        })
        return fetch(ipsApi(`/api/get-tem-task-keyword?${_params.join('&')}`), this.options);
    }

    /**
     * 获取ai抠图结果
     */
    getAiCutoutImg(isUser, assetId, signal) {
        let options = { ...this.options }
        if (signal) {
            options.signal = signal
        }
        return fetch(`//mattingapi.818ps.com/api/asset-matting-by-url?is_user=${isUser}&asset_id=${assetId}`, options);
    }

    /**
     * 获取ai抠图结果（新）
     */
    getAiCutoutImgNew(imgUrl, signal) {
        let options = { ...this.options }
        if (imgUrl.indexOf('https') < 0) {
            imgUrl = 'https:' + imgUrl
        }
        return fetch(`//mattingapi.818ps.com/api/matting-by-url?url=${imgUrl}`, {
            ...options,
            signal
        });
    }

    /**
     * 获取 ai 抠图 上传 oss 信息
     * @param {string} filename
     * @param {string} cdnName
     */
    getAiCutoutUploadInfo(filename, cdnName) {
        return fetch(ipsApi('/api/picup-upload-oss-form?filename=' + filename + '&cdnName=' + cdnName), this.options).then(response => response.json());
    }

    /**
     * 上传图片到 ai 抠图的 oss
     * @param {*} formData
     */
    uploadToAiCutoutCDN(formData) {
        return fetch('//tuguaishou-pic.oss-cn-shanghai.aliyuncs.com', {
            method: 'POST',
            body: formData,
            credentials: 'include',
        })
    }


    /**
     * 完成抠图触发请求
     */

    RecordCutoutSuccess(params) {
        const { beforeId, afterId, isDesigner, } = params
        return fetch(ipsApi(`//apiv2/matting-log?ps_type=${isDesigner}&ps_id=${beforeId}&ps_after_id=${afterId}`, this.options));
    }
    /**
     * 获取 ai 抠图结果
     * @param {string} url
     */
    getAiCutoutResult(url) {
        return fetch('//mattingapi.818ps.com/api/matting-by-url?url=' + url, this.options).then(response => response.json());
    }

    /**
     * 通过 response header content-type 来读取正确的 mimeType
     * @param {string} url
     */
    getImageMimeType(url) {
        return fetch(url);
    }

    /**
     * 删除模版元素缓存数据
     * @param {int} id
     */
    deleteTemplateAssetCache(id) {
        let formDate = new FormData()
        formDate.append('id', id)
        return fetch(ipsApi('/apiv2/del-template-asset-cache'), { ...this.options, method: 'POST', body: formDate }).then(response => response.json());
    }
    /**
     * 获取新增模版元素缓存数据上传信息
     * @param {int} template_id
     * @param {string} filename
     */
    getAddTemplateAssetCacheInfo(template_id = 0, paperId = 0, filename, renderCaType,) {
        const props = IPSConfig.getProps()
        let collaborativeParams = ''
        if (props.share_uid && props.share_id) {
            collaborativeParams = '&share_uid=' + props.share_uid + '&share_id=' + props.share_id;
        }
        return fetch(ipsApi(`/apiv2/pic-upload-oss-form?template_id=${template_id}${Number.isNaN(paperId) ? '' : '&paperId=' + paperId}&filename=${filename}&renderCaType=${renderCaType}${collaborativeParams}`), this.options).then(response => response.json());
    }
    /**
     * 上传模版元素缓存数据
     * @param {string} url
     * @param {FormData} formData
     */
    uploadTemplateAssetCache(url, formData) {
        return fetch(url, {
            method: 'POST',
            body: formData,
            credentials: 'include',
        })
    }
    /**
     * 申诉图片
     * @param {number} checkId
     */
    appealImage(checkId) {
        let formData = new FormData();
        formData.append('check_id', checkId)
        return fetch(ipsApi('/api/img-for-man-check'), {
            method: 'POST',
            body: formData,
            credentials: 'include',
        }).then(response => response.json());
    }
    /**
     * 不申诉，删除图片
     * @param {number} checkId
     */
    notAppealImage(checkId) {
        let formData = new FormData();
        formData.append('check_id', checkId)
        return fetch(ipsApi('/api/img-not-check'), {
            method: 'POST',
            body: formData,
            credentials: 'include',
        }).then(response => response.json());
    }


    /**
    * 
    * 获取code 保证安全 
    */
    getTelCodeToken(sms_key) {
        return fetch(ipsApi(`/site-api/send-tel-login-code-token?sms_key=${sms_key}`), this.options);
    }

    /**
    * 
    * 获取code 保证安全 
    */
    sendValidateCodePhToken(sms_key) {
        return fetch(ipsApi(`/site-api/send-validate-code-ph-token?sms_key=${sms_key}`), this.options);
    }

    /**
   * 
   * 获取字体数量
   */
    getUsingFontCount() {
        return fetch(ipsApi(`/apiv2/get-font-count`), this.options);
    }

    /**
    * 获取显示字体信息
    */
    async getFontsShowInfomation() {
        return await fetch(ipsApi('/apiv2/font-list'), this.options);
    }

    /**
    * 获取所有字体
    */
    async getAllFontsShowInfomation() {
        return await fetch(ipsApi('/apiv2/font-all-list'), this.options);
    }

    /**
     * 分类字体列表
     */
    async getUserFontList() {
        return await fetch(ipsApi(`/apiv2/user-font-list?source=0`), this.options);
    }

    /**
     * 获取上传psd信息
     * @param {string} fileName 
     */
    getUploadPsdInfo(fileName) {
        let formData = new FormData();
        formData.append('filename', fileName)
        return fetch(ipsApi(`/api/add-user-psd`), {
            method: 'POST',
            body: formData,
            credentials: 'include',
        });
    }
    /**
     * 上传图片到 ai 抠图的 oss
     * @param {string} url oss url
     * @param {*} formData
     */
    uploadPsdToOss(url, formData) {
        return fetch(url, {
            method: 'POST',
            body: formData,
            credentials: 'include',
        })
    }
    /**
     * 开始解析 psd 文件
     * @param {string} filePath
     */
    uploadPsdStartParsing(filePath) {
        let formData = new FormData();
        formData.append('psd', filePath)
        return fetch(ipsApi(`/api/async-psd-add`), {
            method: 'POST',
            body: formData,
            credentials: 'include',
        })
    }
    /**
     * 开始解析 psd 文件
     * @param {string} jobId
     */
    uploadPsdParsingCheck(jobId, team_id, fid) {
        let { origin = null } = getProps();
        let url = `/api/async-psd-check?jobId=${jobId}&team_id=${team_id}&fid=${fid}`
        if (env.teamTemplate || isUeTeam) {
            url = url + '&origin=' + origin;
        }
        return fetch(ipsApi(url), {
            credentials: 'include',
        })
    }

    // 摄影图VIP
    // /**
    // * 
    // * 获取摄影图数量
    // */
    // getPhotographicMapCount() {
    //     return fetch(ipsApi(`/api/get-use-count `), this.options);
    // }

    // /**
    // * 
    // * 消费次数
    // */
    //  consumetionPhotographicMapPhotos(id) {
    //     return fetch(ipsApi(`/api/consume-use?asset_id=${id}`), this.options);
    // }

    /**
    * 
    * 活动截止日期
    */
    getActivityDeadline() {
        return fetch(ipsApi(`/api/get-act-last-time`), this.options);
    }

    /**
    * 
    * 企业商用VIP弹窗挽留限制
    */
    getBuryDetain(method, data) {
        return fetch(ipsApi(`/api/close-pop-window?tpl_id=${data}`), {
            ...this.options,
            method
        }).then(response => response.json());
    }

    /**
     * 获取页面动画列表
     */
    getPageAnimationList() {
        return fetch(ipsApi(`/apiv2/get-animation-effect-list?type=1`), this.options);
    }

    /**
     * 获取页面动画列表
     */
    getCheckWork(id) {
        return fetch(ipsApi(`/api/check-work?type=1&tid=${id}`), this.options);
    }

    /**
     * 获取用户 audio 上传 oss 信息
     * @param {string} filename
     * @param {string} cdnName
     */
    getUserAudioUploadInfo(filename, cdnName) {
        return fetch(ipsApi('/api/audio-upload-oss-form?filename=' + filename + '&value_hint=0&orientationFlag=0&cdnName=' + cdnName), this.options).then(response => response.json());
    }

    /**
     * 上传用户 audio 到 oss
     * @param {string} url
     * @param {*} formData
     */
    uploadUserAudioToCdn(url, formData) {
        return fetch(url, {
            method: 'POST',
            body: formData,
            credentials: 'include',
        }).then(response => response.json());
    }

    /**
     * 检查用户 audio 有没有处理完基本信息
     * @param {string | number} id 
     */
    checkUserAudioParsing(id) {
        const formData = new FormData();
        formData.append('key', id)
        return fetch(ipsApi(`/api/user-audio-upload-stat`), {
            method: 'POST',
            body: formData,
            credentials: 'include',
        }).then(response => response.json());
    }

    /**
     * 获取用户 audio 列表
     * @param {number} pageIndex
     */
    getUserAudioList(pageIndex) {
        return fetch(ipsApi('/api/get-user-audio-list?p=' + pageIndex), this.options).then(response => response.json());
    }

    /**
     * 删除用户 audio
     * @param {string} id 
     */
    deleteUserAudio(id) {
        const formData = new FormData();
        formData.append('id', id)
        return fetch(ipsApi(`/api/del-user-asset-audio`), {
            method: 'POST',
            body: formData,
            credentials: 'include',
        }).then(response => response.json());
    }

    /**
     * 获取用户 video 上传 oss 信息
     * @param {string} filename
     * @param {string} cdnName
     */
    getUserVideoEUploadInfo(filename, cdnName, source = 'video') {
        // type：类型：编辑器数据隔离,1 ue ,2 movie
        // source: 文件来源, video = 视频文件, gif = gif图片 
        return fetch(ipsApi(`/api/video-upload-oss-form?filename=${filename}&cdnName=${cdnName}&type=1&source=${source}`), this.options).then(response => response.json());
    }

    /**
     * 上传用户 video 到 oss
     * @param {string} url
     * @param {*} formData
     */
    uploadUserVideoEToCdn(url, formData) {
        return fetch(url, {
            method: 'POST',
            body: formData,
            credentials: 'include',
        }).then(response => response.json());
    }

    /**
     * 检查用户 video 有没有处理完基本信息
     * @param {string | number} id 
     */
    checkUserVideoEParsing(id) {
        const formData = new FormData();
        formData.append('key', id)
        return fetch(ipsApi(`/api/user-video-e-upload-stat`), {
            method: 'POST',
            body: formData,
            credentials: 'include',
        }).then(response => response.json());
    }

    /**
     * 获取用户 video 列表
     * @param {number} pageIndex
     */
    getUserVideoEList(pageIndex, source = 'video') {
        // type: 类型：编辑器数据隔离,1 ue ,2 movie
        // source: 文件来源, video = 视频文件, gif = gif图片 
        return fetch(ipsApi(`/api/get-user-video-e-list-v2?p=${pageIndex}&type=1&source=${source}`), this.options).then(response => response.json());
    }

    /**
     * 删除用户 video
     * @param {string} id 
     */
    deleteUserVideoE(id) {
        const formData = new FormData();
        formData.append('id', id)
        return fetch(ipsApi(`/api/del-user-video-e`), {
            method: 'POST',
            body: formData,
            credentials: 'include',
        }).then(response => response.json());
    }

    /**
     * 获取 videoe 帧图片
     * @param {string | number} resId 
     * @param {0 | 1} isUser 
     * @param {number[]} frameList 
     * @returns 
     */
    getVideoEFrames(resId, isUser, frameList) {
        return fetch(ipsApi(`/api/get-video-frame?resId=${resId}&isUser=${isUser}&frames=${frameList.join(',')}`), this.options).then(response => response.json());
    }

    /** 获取手机上传二维码 */
    getUploadByPhone(type = 'gif') {
        return fetch(ipsApi(`/api/get-phone-upload?upload_type=${type}`), this.options).then(response => response.json());
    }

    /** 检查手机上传gif */
    checkUploadGifByPhone() {
        return fetch(ipsApi(`/api/check-phone-upload`), this.options).then(response => response.json());
    }

    /**
     *  获取元素特效列表页
     *  @param
     */
    getAssetEffectList() {
        return fetch(ipsApi('/api/get-element-effects-list'), this.options).then(response => response.json())
    }

    /**
     * 获取元素动效列表页
     * @param
     */
    getAssetAnimationList() {
        return fetch(ipsApi('/api/element-asset'), this.options).then(response => response.json())
    }

    /*  限制个人非商用VIP下载企业模板
     *  @param {Number} upicId
     */
    companyTemplateDl(upicId) {
        return fetch(ipsApi(`/api/company-template-dl/?upicId=${upicId - 0}`), this.options).then(response => response.json())
    }

    /**
     * 微博用户授权
     * @param {string} state
     */
    getWeiboAuth(state) {
        const base64State = window.btoa(state);
        return fetch(ipsApi('/api/get-weibo-auth?state=' + base64State), this.options);
    }

    /**
     * 获取微博授权列表
     * @param
     */
    getWeiboList() {
        return fetch(ipsApi('/api/get-weibo-list'), this.options);
    }

    /**
     * 微博公众平台同步素材
     * @param {string} appid
     * @param {number} tid
     * @param {string | null} msg
     */
    uploadShareWeibo(appid, tid, msg) {
        let formData = new FormData();
        formData.append("appid", appid);
        formData.append("tid", tid);
        const team_id = isUeTeam && getProps().team_id
        if (team_id) {
            formData.append("team_id", team_id);
        }
        if (msg) {
            formData.append("msg", msg);
        }
        return fetch(ipsApi('/api/share-weibo'), { method: 'POST', body: formData, credentials: 'include' });
    }

    /**
     * 抖音用户授权
     * @param {string} url
     */
    getTikTokAuth(url) {
        const base64Url = window.btoa(url);
        return fetch(ipsApi('/site/dou-yin-auth?redirect_uri=' + base64Url), this.options);
    }

    /**
     * 抖音授权用户列表
     */
    getTikTokUserList() {
        return fetch(ipsApi('/api/get-dou-yin-list'), this.options);
    }

    /**
     * 抖音平台同步素材
     * @param {number} type // 1图片2视频
     * @param {string} appid
     * @param {string | null} msg
     * @param {string | null} jobId
     */
    uploadShareTikTok(type, appid, msg, jobId) {
        const { upicId } = getProps();
        let formData = new FormData();
        formData.append("type", type);
        formData.append("appid", appid);
        formData.append("tid", upicId);
        if (msg) {
            formData.append("msg", msg);
        }
        if (jobId) {
            formData.append("jobId", jobId);
        }
        const team_id = isUeTeam && getProps().team_id
        if (team_id) {
            formData.append("team_id", team_id);
        }
        return fetch(ipsApi('/api/share-douyin'), { method: 'POST', body: formData, credentials: 'include' });
    }

    /**
     * 获取好友列表
     * @param {number} pageIndex 
     * @param {string} searchText
     */
    getFriendsLists(pageIndex, searchText) {
        const formData = new FormData();
        if (pageIndex >= 0) {
            formData.append('page', pageIndex);
        }
        formData.append('action_id', 'index');
        if (searchText) {
            formData.append('search', searchText);
        }
        if (env.teamTemplate || isUeTeam) {
            formData.append('team_id', getProps().team_id);
            return fetch(ipsApi('/friends-list/team-list'), {
                method: 'POST',
                body: formData,
                credentials: 'include',
            }).then(r => r.json())
        }
        return fetch(ipsApi('/friends-list/list'), {
            method: 'POST',
            body: formData,
            credentials: 'include',
        }).then(r => r.json())
    }

    /**
     * 发送分享链接给站内信好友
     * @param {string} id 
     * @param {string} link_param 
     */
    sendFriendsShareInfo(id, link_param, type) {
        // ueTeam合并到ue,ue的teamTeamplate为false,需要额外增加判断
        if (env.teamTemplate || isUeTeam) {
            link_param += `?jump_type=${isUeTeam ? 'ueTeam' : env.editor}`
        }
        const formData = new FormData();
        formData.append('id', id);
        formData.append('link_param', link_param);
        if (getProps().team_id && (env.teamTemplate || isUeTeam)) {
            formData.append('team_id', getProps().team_id);
            return fetch(ipsApi('/apiv2/team-share-msg'), {
                method: 'POST',
                body: formData,
                credentials: 'include',
            }).then(r => r.json())
        }
        return fetch(ipsApi('/apiv2/share-msg'), {
            method: 'POST',
            body: formData,
            credentials: 'include',
        }).then(r => r.json())
    }

    /** 搜索官方 videoe */
    searchOfficialVideoE(page, keyword) {
        return fetch(ipsApi(`/api/get-video-material?page=${page}&keyword=${keyword}`), this.options).then(r => r.json());
    }

    /** 获取官方 videoe 最近使用 */
    getOfficialVideoEHistory() {
        return fetch(ipsApi(`/apiv2/get-history-record?type=12`), this.options).then(r => r.json());
    }

    /** 设置官方 videoe 最近使用 */
    setOfficialVideoEHistory(resId) {
        return fetch(ipsApi(`/apiv2/set-history-record?type=12&assetId=${resId}`), this.options).then(r => r.json());
    }

    /** 官方 videoe 推荐列表 */
    getOfficialVideoERecommend() {
        return fetch(ipsApi(`/api/get-recommend-asset?type=6`), this.options).then(r => r.json());
    }

    /** 获取官方 videoe 收藏 */
    getOfficialVideoEFavor() {
        return fetch(ipsApi(`/apiv2/get-fav-video-e`), this.options).then(r => r.json());
    }

    /** 设置官方 videoe 收藏 */
    setOfficialVideoEFavor(resId) {
        return fetch(ipsApi(`/apiv2/fav-asset?type=1&assetId=${resId}`), this.options).then(r => r.json());
    }

    /** 设置官方 videoe 收藏 */
    deleteOfficialVideoEFavor(resId) {
        return fetch(ipsApi(`/apiv2/del-fav-asset?type=1&assetId=${resId}`), this.options).then(r => r.json());
    }

    /** 获取音乐素材*/
    searchMusicData(pageId, keyword) {
        return fetch(ipsApi(`/api/get-houzi-music-list?page_id=${pageId}&keyword=${keyword}`), this.options).then(r => r.json());
    }

    /** 获取音乐收藏 */
    getMusicFavor(pageId, keyword) {
        return fetch(ipsApi(`/api/get-houzi-music-fav-list?page=${pageId}&keyword=${keyword}`), this.options).then(r => r.json());
    }

    /** 设置音乐收藏 */
    setMusicFavor(audio_id) {
        return fetch(ipsApi(`/api/handle-houzi-music-fav?audio_id=${audio_id}`), this.options).then(r => r.json());
    }

    /** 设置最近使用音乐 */
    setMusicHistory(audio_id) {
        return fetch(ipsApi(`/apiv2/set-history-record?type=13&assetId=${audio_id}`), this.options).then(r => r.json());
    }

    /** 获取最近使用音乐 */
    getMusicHistory() {
        return fetch(ipsApi(`/apiv2/get-history-record?type=13`), this.options).then(r => r.json());
    }

    /** 检查用户是否有音乐vip */
    checkMusicUserVip() {
        return fetch(ipsApi(`/video/get-audio-vip-info`), this.options).then(r => r.json());
    }

    /** 消费音乐vip次数 */
    payMusicVip(audio_id) {
        return fetch(ipsApi(`/video/consume-music-vip-count?audio_id=${audio_id}`), this.options).then(r => r.json());
    }

    /** 获取素材楼层数据*/
    floorMaterialData(top_id = 0, pid = 0) {
        return fetch(ipsApi(`/api/get-asset-album?top_id=${top_id}&pid=${pid}`), this.options);
    }

    /** 获取专辑落地页数据*/
    getAssetLinkList(top_id = 0, id = 0, page, kid_2='' ) {
        return fetch(ipsApi(`/api/get-asset-link-list?top_id=${top_id}&id=${id}&page=${page}&kid_2=${kid_2}`), this.options);
    }

    /** 素材专辑 - 素材搜索*/
    searchAsset(top_id = '0', keyword = '', page = 1, limit = 20,kid_2='') {
        return fetch(ipsApi(`/api/search-asset?top_id=${top_id}&keyword=${keyword}&page=${page}&limit=${limit}&kid_2=${kid_2}`), this.options);
    }
    /** 获取素材顶部热点搜索词*/
    getAssetAlbumWord() {
        return fetch(ipsApi(`/api/get-asset-album-word`), this.options);
    }
    /** 获取我的收藏接口*/
    getFavList() {
        return fetch(ipsApi(`/api/get-fav-list`), this.options);
    }
    /** 最近使用*/
    getHistoryRecord() {
        return fetch(ipsApi(`/api/get-history-record`), this.options);
    }
    /**
     * 获取相框分类
     * @returns 
     */
    getFrameAssetKind(){
        return fetch(ipsApi('/api/get-kind-kid'), this.options)
    }
    /**
     * 检查元素 id 是否有对应 tag 标签
     * @param { string } assetIds
     * @param { number | string } tagId 
     */
    checkAssetTag(assetIds, tagId) {
        const formData = new FormData();
        formData.append('tagId', tagId);
        formData.append('asset_ids', assetIds);
        return fetch(ipsApi('/api/exist-element'), {
            method: 'POST',
            body: formData,
            credentials: 'include',
        }).then(r => r.json())
    }

    /** 获取自定义菜单列表 */
    getUserMenuList(userId, editor_type) {
        return fetch(ipsApi(`/api/menu-list?editor_type=${editor_type}${userId ? '&uid=' + userId : ''}`), this.options).then((r) => r.json());
    }

    /** 保存用户菜单列表 */
    saveUserMenuList(userId, config, isFirst, editor_type) {
        const formData = new FormData();
        formData.append('uid', userId);
        formData.append('config', JSON.stringify(config));
        formData.append('editor_type', editor_type);
        if (isFirst) {
            formData.append('is_first', 1);
        }
        return fetch(ipsApi('/api/save-menu-config'), {
            method: 'POST',
            body: formData,
            credentials: 'include',
        }).then((r) => r.json());
    }

    /** smart 获取左侧模板列表  */
    getDesignPreview({ picId }) {
        let formData = new FormData();
        formData.append("pic_id", picId);

        // https://818ps.com/api/get-design-preview
        return fetch(ipsApi(`/api/get-design-preview`), {
            method: 'POST',
            body: formData,
            credentials: 'include'
        }).then((r) => r.json());
    }

    /** 获取是否是旧vip或新个人商用vip */
    getIsPersonBusinessVip() {
        return fetch(ipsApi(`/api/is-person-business-vip`), this.options).then((r) => r.json());
    }

    getCollectionList(data) {
        return fetch(ipsApi(`/api/get-asset-collection-list?page=${data.page}&limit=${data.limit}`), this.options).then((r) => r.json());
    }
    getCollectionDetail(data) {
        return fetch(ipsApi(`/api/get-asset-collection-detail?collectionId=${data.collectionId}&assetId=${data.assetId}&page=${data.page}&limit=${data.limit}`), this.options).then((r) => r.json());
    }
    getUploadLimitSpace(data) {
        return fetch(ipsApi(`/api/available-space`), this.options);
    }
    getAssetCount(data) {
        return fetch(ipsApi(`/api/get-user-asset-count`), this.options);
    }
    deleteAllAsset({ type }) {
        let formData = new FormData();
        formData.append("type", type);

        return fetch(ipsApi(`/api/del-user-all-asset`), {
            method: 'POST',
            body: formData,
            credentials: 'include'
        }).then((r) => r.json());
    }
    getQRCode({ type,origin = 'ai_points' }) {
        let formData = new FormData();
        formData.append("type", type);
        formData.append("pay_origin", origin);
        formData.append("classify", '13');
        formData.append("update", false);

        return fetch(ipsApi(`/pay/get-qrcode`), {
            method: 'POST',
            body: formData,
            credentials: 'include'
        }).then((r) => r.json());
    }
    getAIPriceList(data) {
        return fetch(ipsApi(`/aidraw/pay-ai-points`), this.options);
    }
    getAIPriceListNew(data) {
        return fetch(ipsApi(`/aidraw/pay-ai-write?pt=''&ai_type=4`), this.options);
    }
    getUniqueId(mobile, bank_card){
        const formData = new FormData();
        formData.append("mobile", mobile);
        formData.append("bank_card", bank_card);
        return fetch(ipsApi(`/apiv2/get-three-elements-auth-unique-id`), {
            method: 'POST',
            body:formData,
            credentials: 'include'
        }).then((r) => r.json());
    }
    tencentChenckThreeableAuth({mobile,name,card_id,unique_id, bank_card='' }){
        const formData = new FormData();
        formData.append("name", name);
        formData.append("mobile", mobile);
        formData.append("card_id", card_id);
        formData.append("unique_id", unique_id);
        formData.append("bank_card", bank_card);
        return fetch(ipsApi(`/apiv2/three-elements-auth`), {
            method: 'POST',
            body:formData,
            credentials: 'include'
        }).then((r) => r.json());
    }
    checkeAIPayStatus(time) {
        let formData = new FormData();
        formData.append("time", time);

        return fetch(ipsApi(`/pay/check-pay`), {
            method: 'POST',
            body: formData,
            credentials: 'include'
        }).then((r) => r.json());
    }
    sendPhoneCode({code, phone}){
        let formData = new FormData();
        formData.append("codeImg", code);
        formData.append("tel", phone);

        return fetch(ipsApi(`/api/send-validate-code-ph`), {
            method: 'POST',
            body: formData,
            credentials: 'include'
        }).then((r) => r.json());
    }
    checkDelConfirmCode({code, phone}){
        let formData = new FormData();
        formData.append("validate_code", code);
        formData.append("tel", phone);

        return fetch(ipsApi(`/api/confirm-del-asset`), {
            method: 'POST',
            body: formData,
            credentials: 'include'
        }).then((r) => r.json());
    }
    // 2023年5月9号之前的vip，其没有进行过扩容升级，返回true
    checkUserIsSpecialVip(){
        return fetch(ipsApi('/apiv2/get-personal-vip-user'), this.options)
    }
    // 个人vip用户升级上传空间
    upgradePersonalVipUserSpace(){
        return fetch(ipsApi('/apiv2/save-personal-vip-user-space'), this.options)
    }
    // 恢复用户删除的素材
    recoverDeleteAsset({asset_id,type=1}){
        let formData = new FormData();
        formData.append("asset_id", asset_id);
        formData.append("type", type);
        return fetch(ipsApi(`/apiv4/recover-user-asset`), {
            method: 'POST',
            body: formData,
            credentials: 'include'
        }).then((r) => r.json());
    }
    // 获取删除得素材信息
    getDeletedAssetInfo({res_id}){
        return fetch(ipsApi('/apiv4/get-user-asset-info?res_id=' + res_id), this.options)
    }

    isFontVip() {
      return fetch(ipsApi('/api/get-font-vip-info'), this.options).then(res => res.json())
    }
    // 获取效果模板
    getEffectTemplateList(){
        return fetch(ipsApi('/apiv2/get-effect-template-list'),this.options)
    }
    // 获取效果模板楼层展示
    getMoreEffectTemplateList({id,page}){
        return fetch(ipsApi(`/apiv2/get-template-link-list?id=${id}&page=${page}`),this.options)
    }
    // 是否展示效果模板引导弹窗
    isShowGuidePop(){
        return fetch(ipsApi('/apiv2/is-show-guide-pop '),this.options)
    }
    // 关闭效果模板引导弹窗
    closeGuidePop(){
        return fetch(ipsApi('/apiv2/close-guide-pop '),this.options)
    }
    // 获取风格案例模板信息
    getStyleTemplateInfo(picId){
        return fetch(ipsApi(`/api/get-template-info?picId=${picId}`),this.options)
    }
    // 获取风格列表
    getStyleWrapList(){
        return fetch(ipsApi(`/api/get-style-template-list`),this.options)
    }
    getStyleList(params){
        return fetch(ipsApi(`/api/get-style-template-list-more?page=${params.page}&type=${params.type}`),this.options)
    }
    // 搜索尺寸
    searchCanvasSize(picId, keyword, paperId){
        let { isDesigner } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const url = `/api/search-class-size?is_client=${Number(!isDesigner)}&picId=${picId}&keyword=${keyword}`;
        if (paperId) {
            url += `&paperId=${paperId}`;
        }
        return fetch(ipsApi(url),this.options)
    }
    // 违禁词校验
    checkProhibitedWords(word){
        let formData = new FormData();
        formData.append("word", JSON.stringify(word));
        return fetch(ipsApi(`/apiv2/ban-user-words`), {
            method: 'POST',
            body: formData,
            credentials: 'include'
        }).then((r) => r.json());
    }
    // 获取分类信息
    getCategoryTree(pic = '',kid = '') {
        // 5848728
        const url = `/api/get-template-class-info-new?picId=${pic}&kid=${kid}`;
        return fetch(ipsApi(url),this.options).then(r=>r.json());
    }

    // 选中的类目数据
    getCheckedCategory(pic) {
        const url = `/api/get-template-class-links?id=${pic}`;
        return fetch(ipsApi(url),this.options).then(r=>r.json());
    }
    // 获取广告图片及是否到期状态
    getHuodongState(){
        const url = `/apiv4/activity-data`;
        return fetch(ipsApi(url),this.options).then(r=>r.json());
    }
    // 获取预览图列表
    getPreviewList(
        picId = 0, // 模板id
        upicId = 0, // 手动保存id
        versionId = 0, // 分享&协作版本id
        userTemplateTeamId = 0, // 分享&协作团队模板id
        paperId = 0, // 自动保存id
        shareUid = 0 // 分享&协作id
    ) {
        return fetch(ipsApi(`/apiv2/get-template-page-data?picId=${picId}&upicId=${upicId}&version_id=${versionId}&user_template_team_id=${userTemplateTeamId}&paperId=${paperId}&share_uid=${shareUid}`), this.options)
    }
    // 获取vip和模板数量映射关系
    getVipTemplateNumMap(){
        return fetch(ipsApi(`/apiv2/template-num`), this.options)
    }
    // 复制用户模板到团队
    copyUserTempltoTeam({id,team_id,source_type}){
        let formData = new FormData();
        formData.append("id", id);
        formData.append("team_id", team_id);
        formData.append("source_type", source_type);
        return fetch(ipsApi(`/apiv2/copy-user-to-team-template`), {
            method: 'POST',
            body: formData,
            credentials: 'include'
        })
    }

    // 反馈列表
    getFeedbackList(){
        return fetch(ipsApi(`/feedback/feedback-list`), this.options)
    }
    // 提交反馈
    submitFeedback(postData){
        let formData = new FormData();
        formData.append("content", postData.content);
        formData.append("img", postData.img);
        formData.append("phone", postData.phone);
        return fetch(ipsApi(`/feedback/feedback-add`), {
            method: 'POST',
            body: JSON.stringify(postData),
            credentials: 'include',
            headers: {
                Accept: 'application/json',
            },
        })
    }
    // 获取反馈详情
    getFeedbackDetail(id){
        return fetch(ipsApi(`/feedback/feedback-find`),{
            method: 'POST',
            body: JSON.stringify({id}),
            credentials: 'include',
            headers: {
                Accept: 'application/json',
            },
        })
    }

    // 获取上传oss的表单信息
    getFeedbackUploadOssForm(file, value_hint, orientationFlag = 0, cdnName) {
        if (value_hint == undefined) {
            value_hint = 0;
        }
        let url = '/feedback/feedback-upload?filename=' + file + '&value_hint=' + value_hint + '&cdnName=' + cdnName + '&upload_restriction=1';

        return fetch(ipsApi(url), this.options);
    }

    // 获取快捷菜单位置
    getShortcutMenuPosition(userId){
        return fetch(ipsApi(`/apiv2/shortcut-menu-position?uid=${userId}`), this.options)
    }

    // 设置快捷菜单位置
    savehortcutMenuPosition(data){
        return fetch(ipsApi(`/apiv2/shortcut-menu-position-save`), {
            method: 'POST',
            body: JSON.stringify(data),
            credentials: 'include',
            headers: {
                // 'Content-Type': 'application/x-www-form-urlencoded',
                Accept: 'application/json',
            },
        })
    }
    getOssUploadPreviewToken(filename, prefix){
        return fetch(ipsApi(`/apiv3/upload-preview?filename=${filename}&prefix=${prefix}`), {
            ...this.options,
            method: 'GET',
            headers:{
                ...IPSConfig.getAuthenticatio()
            }
        })
    }
    putPreviewPoster(data){
        return fetch(ipsApi(`/apiv2/preview-set`), {
            ...this.options,
            method: 'POST',
            body: data,
            headers:{
                ...IPSConfig.getAuthenticatio(),
            }
        })
    }
    // 查询是否被踢下线
    getCheckKickout() {
        return fetch(ipsUApi(`/apiv2/check-kickout`), {
            ...this.options,
            method: 'get',
            headers: {
                ...IPSConfig.getAuthenticatio(),
            }
        })
    }
    /**
     * 提交文档图文组合
     * @param data
     */
    submitDocData(data) {
        let postData = {
            doc: JSON.stringify(data.doc),
            user_doc: JSON.stringify(data.userDoc),
            kind_id: data.kind_id,
            kind_id2: data.kind_id2,
            picId: data.picId,
        };
        return fetch(ipsApi('/apiv7/submit-doc-graphic'), {
            method: 'POST',
            // body: "doc=" + JSON.stringify(data.doc) + "&cateId=" + data.cateId + "&groupWordId=" + data.groupWordId + "&isAI=" + data.isAI,
            body: JSON.stringify(postData),
            headers: {
                // 'Content-Type': 'application/x-www-form-urlencoded'
                'Accept': 'application/json'
            },
            credentials: 'include'
        });
    }
    /**
     * 获取文档图文组合list
     */
    getDocGraphicList(){
        return fetch(ipsApi(`/apiv7/get-doc-graphic-list`),this.options)
    }
    /**
     * 获取文档图文组合详情数据
     */
    getDocGraphicDetail(id){
        return fetch(ipsApi(`/apiv7/get-doc-graphic?assetId=${id}`),this.options)
    } 
    /**
     * 获取图文组合提交分类
     */
    getDocKindList() {
        return fetch(ipsApi(`/apiv7/get-doc-kinds`),this.options)
    }
    /**
     * @description: 获取划重点列表
     * @return {*}
     */    
    getEmphaticMarkList(){
        return fetch(ipsApi(`/apiv2/template-emphatic-mark`), this.options)
    }
    /**
     * @description: 图片变清晰
     * @return {*}
     */    
    aiEnchangeImage(params){
        const formData = new FormData()
        for (let key in params) { 
            formData.append(key,params[key])
        }
        return fetch(ipsApi(`/aidraw/enhance-image`), {
            method: 'POST',
            body: formData,
            ...this.options
        })
    }
    getAiConsumerPoint(){
        return fetch(ipsApi('/aidraw/get-num'),this.options)
    }
    aiElimateImage(params){
        const formData = new FormData()
        for (let key in params) { 
            formData.append(key,params[key])
        }
        return fetch(ipsApi(`/aidraw/smear-redraw`), {
            method: 'POST',
            body: formData,
            ...this.options
        })
    }
    /**
     * @description: 
     * @return {*}
     */    
    aiDrawImageUpload(name, type = 1){
        return fetch(ipsApi(`/aidraw/upload-oss?name=${name}&type=${type}`), this.options)
    }
    aiTextOcr(imgPath){
        return fetch(ipsApi(`/aidraw/ocr?imgPath=${imgPath}`), this.options)
    }
    aiTextSelectOptionConfig(){
        return fetch(ipsApi(`/apiv5/text-config`), this.options)
    }
    aiTextWordArt(params){
        const formData = new FormData()
        for (let key in params) { 
            formData.append(key,params[key])
        }
        return fetch(ipsApi(`/aidraw/art-word`), {
            method: 'POST',
            body: formData,
            ...this.options
        })
    }
    aiTextWordArtCheck(params){
        const url = IpsUtils.Url.joinUrlParams('/aidraw/art-word-check', params)
        return fetch(ipsApi(url), this.options)
    }
    getWordArtStyleList(){
        return fetch(ipsApi(`/aidraw/art-word-style`), this.options)
    }
    // ai抠图背景配置
    getAiCutoutConfigOption(){
        const api = `/apiv3/ai-matting-bg`
        return fetch(ipsApi(api), this.options)
    }
    // ai生成设计师模板遮罩上传
    aiGenearateDesignerMaskUpload(name,type=1,templateId){
        return fetch(ipsApi(`/aiproducev2/upload-oss?name=${name}&type=${type}&templateId=${templateId}`), this.options)
    }
    getDesignerAiTemplMask(picId = ''){
        const api = `/aiproducev2/get-main-image-mask?picId=${picId}`
        return fetch(ipsApi(api), this.options)
    }

    getPersionCommercialVipList() {
        return fetch(ipsApi('/apiV1/vip/individual-business/list'), this.options).then(r => r.json())
    }

    getFirmVipList() {
        return fetch(ipsApi('/apiV1/vip/firm/list'), this.options).then(r => r.json())
    }

    getVipPayQrcode(type, pay_origin, classify, update, pay_mode) {
        let urlProps = IPSConfig.getProps(true);
        const formData = new FormData();
        formData.append('type', type);
        formData.append('pay_origin', `${pay_origin}${urlProps.isClientSide ? '_isClientSide' : ''}`);
        formData.append('classify', `${classify}`);
        formData.append('update', `${update}`);
        formData.append('pay_mode', pay_mode);
        return fetch(ipsApi('/pay/get-qrcode'), {
            ...this.options,
            method: 'POST',
            body: formData,
        }).then((r) => r.json());
    }

    /**用户满意度调查 提交*/
    submitSatisfactionSurvey(data) {
        const formData = new FormData();
        formData.append('impression_score', data.impression_score);
        formData.append('satisfaction_score', data.satisfaction_score);
        formData.append('phone', data.phone);
        return fetch(ipsApi('/api/questionnaire'), {
            method: 'POST',
            body: formData,
            credentials: 'include',
        }).then((r) => r.json());
    }

    /**用户满意度调查 是否打开*/
    isOpenSatisfactionSurvey() {
        return fetch(ipsApi('/api/is-questionnaire'), this.options).then((r) => r.json());
    }

    /**用户满意度调查 关闭*/
    closeSatisfactionSurvey() {
        return fetch(ipsApi('/api/close-questionnaire'), this.options).then((r) => r.json());
    }
    // 促付费用户情况
    getPromptUserPayInfo(param) {
        return fetch(ipsApi(`/apiV1/common/index/prompt-user-pay-info?source=${param.source}`), this.options).then((r) => r.json());
    }

    aiMariking(picId, previewUrl) {
        return fetch(ipsApi(`/apiV1/template/index/ai-tag-template?tid=${picId}&previewUrl=${encodeURIComponent(previewUrl)}`), this.options).then((r) => r.json());
    }
    /**
     * @description:生成次数
     * @param {object} params
     * @return {*}
     */
    getGenerateNum() {
        return fetch(ipsApi('/aiproduce/get-num'), { ...this.options, credentials: 'include' }).then((r) => r.json());
    }
    getPriceTextListApi() {
        return fetch(ipsApi('/ai-produce-pay/vip-list'), {credentials: 'include' }).then((r) => r.json());
    }
}

export let assetManager = new AssetManager();
