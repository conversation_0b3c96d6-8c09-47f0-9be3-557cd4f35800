import React, { Component } from "react";
import { getProps, ipsApi } from "@component/IPSConfig";
import { emitter } from "@component/Emitter";
import { assetManager } from "@component/AssetManager";
import { tempIdBuilder } from "@component/tool/template";
import { checkoutAssetIsInArea } from "@component/Function";
import { Checkbox, Form, Radio } from '@src/userComponentV6.0/PubComponents'
import { UploadCompletePopup } from './InfoBar'
import { storeAdapter } from "@v7_logic_core/StoreAdapter";
import { UpdateCanvas } from "@v7_logic/CanvasLogic";
import { CanvasPaintedLogic } from "@v7_logic/CanvasPaintedLogic";
import { CategoryPopUp, premake, categoryRoot, categoryTreeMap } from '@v7_render/InfoBar/CategoryPopup';
import { IPSConfig } from '@v7_utils/IPSConfig';
import { isAIDesign, isAIDesignPicDrawTemp, isAIDesignPPT } from "@v7_utils/estimate";
import { InfoManageHelper } from "@v7_logic/StoreLogic";

const FormItem = Form.Item

function getKeywords(k1) {
  const des = {
    titleDesSub: '',
    keywordDesSub: '',
    titleDesSubBottom: '',
  }
  switch (parseInt(k1)) {
    case 1:
      des.titleDesSub = '标题组成：作品中节日/主题+元素+风格+分类<br>' +
        '元素（如：吹风机）分类（如：手机海报、小红书）<br>' +
        '完整标题示范：双十一家电促销吹风机粉色毛绒风小红书封面';
      des.keywordDesSub = '如作品中主体文案+元素+主体文案+节日/主题+颜色+风格+分类等，多关联常用词，关键词中间用空格隔开 <br>完整关键词示范：双十一双11家电产品 促销 活动 满减购物 吹风机 粉色 毛绒风 小红书封面 新媒体';
      des.titleDesSubBottom = '-65';
      break;
    case 3:
      des.titleDesSub = '标题组成：产品+作品主文案+节日/主题+颜色+风格+分类<br>' +
        '产品（如：家电/美妆/珠宝/服饰等）<br>' +
        '分类（如：主图直通车/banner等）<br>' +
        '完整标题示范：电池强劲蓄电淘宝家电节黄黑直通车海报';
      des.keywordDesSub = '如作品中主体文案+元素+主体文案+节日/主题+颜色+风格+分类等，多关联常用词，关键词中间用空格隔开 <br>完整关键词示范：双十一双11家电产品 促销 活动 满减购物 吹风机 粉色 毛绒风 小红书封面 新媒体'
      des.titleDesSubBottom = '-65';
      break;
    case 16:
      des.titleDesSub = '标题组成：节日/主题+元素+颜色+风格+作品主文案+用途+分类<br>' +
        '元素（如：灯笼、玫瑰花、线条等）<br>' +
        '用途（如：促销、党建、企业文化宣传、公益宣传、企业招聘、电影宣传等）<br>' +
        '完整标题示范：2018流体渐变紫色孟菲斯风狂欢音乐节宣传海报';
      des.keywordDesSub = '如作品中主体文案+元素+主体文案+节日/主题+颜色+风格+分类等，多关联常用词，关键词中间用空格隔开 <br>完整关键词示范：双十一双11家电产品 促销 活动 满减购物 吹风机 粉色 毛绒风 小红书封面 新媒体';
      des.titleDesSubBottom = '-80';
      break;
    default:
      des.titleDesSub = '标题组成：作品中节日/主题+元素+风格+分类<br>' +
        '元素（如：吹风机）分类（如：手机海报、小红书）<br>' +
        '完整标题示范：双十一家电促销吹风机粉色毛绒风小红书封面';
      des.keywordDesSub = '如作品中主体文案+元素+主体文案+节日/主题+颜色+风格+分类等，多关联常用词，关键词中间用空格隔开 <br>完整关键词示范：双十一双11家电产品 促销 活动 满减购物 吹风机 粉色 毛绒风 小红书封面 新媒体';
      des.titleDesSubBottom = '-65';
      break;
  }
  return des
}

/**
 *
 * @param data
 * @param {object} [option]
 * @param {string} [option.labelKey]
 * @param {string} [option.valueKey]
 * @param {function} [option.exclude] 类似于foreach的function，但是需要返回Boolean，返回ture则不会将此条数据插入data中
 * @return {*}
 */
function formatOptions(data, { labelKey = 'class_name', valueKey = 'id', exclude } = {}) {
  const builder = (item) => {
    return {
      ...item,
      label: item[labelKey],
      value: `${item[valueKey]}`,
    }
  }
  if (exclude) {
    const temp = []
    data.forEach((item, index) => {
      if (!exclude(item, index)) {
        temp.push(builder(item))
      }
    })
    return temp
  }
  return data.map(item => (builder(item)))
}

function includesValue(arr, value) {
  return arr.some(item => {
    if (item.value) {
      return `${item.value}` === `${value}`
    } else {
      return `${item}` === `${value}`
    }
  })
}

const noOtherOptionArr = [27, 31, 123, 150, 179, 17, 28, 32, 67, 151, 152, 35, 159, 36, 149, 52, 68, 69, 71, 72, 73, 74, 75, 78, 155, 94, 95, 142, 161, 162, 184, 185, 148, 153, 154]

/**
 *  上传模版 组件
 *
 * @class UploadPopup
 * @extends {Component}
 */
class SubmitHandler extends Component {
  constructor(props) {
    super(props)
    const {
      k1,
      info,
      urlProps,
      templAttrData,
      tagGradeData,
      rt_isMutiSizeTemplate,
      rt_is_online_detail_page,
      rt_temp_local_online_checked_id
    } = this.getStoreData()
    const {
      titleDesSub,
      keywordDesSub,
      titleDesSubBottom,
    } = getKeywords(k1)
    this.typeKey = {
      // 分类
      category: 'category',
      categoryValue: 'categoryValue',
      //其他分类
      extra: 'extra',
      extraValue: 'extraValue',
      // 用途
      usage: 'usage',
      usageValue: 'usageValue',
      // 企业一级分类
      enterpriseStair: 'enterpriseStair',
      enterpriseStairValue: 'enterpriseStairValue',
      // 企业二级分类
      enterpriseSecond: 'enterpriseSecond',
      enterpriseSecondValue: 'enterpriseSecondValue',
      // 行业
      industry: 'industry',
      industryValue: 'industryValue',
      // 风格
      styles: 'styles',
      stylesValue: 'stylesValue',
      //场景
      scene: 'scene',
      // sceneValue: 'sceneValue',
    }
    const { onlineDetailSinle = false } = this.props;

    // 添加分类 节日活动 classId = 1038
    //是否是电商淘宝 和 手机店铺首页 （程真加的）
    this.isOnlineTaoBao = k1 == 3

    //添加分类  拼多多店铺首页 classId = 1039
    //手机店铺首页  classId 923
    this.isPhoneOnlineIndex = info.class_id && (info.class_id.some(item => item == 923) || urlProps.class_id == 923)

    //电商详情显示部分 用途
    this.online_detail_show_usefulIds = rt_is_online_detail_page ? [5, 22, 12, 19, 800, 978] : [];

    // 需要过滤的id
    this.needFilterIds = (rt_is_online_detail_page && onlineDetailSinle) ? [31, 33] : [31, 33, 34]
    // 不显示 分类和用途
    this.dontShowClassifyAndUse = (!rt_isMutiSizeTemplate || info.dont_show_more_size) || k1 == 1
    this.state = {
      titleDesSub,
      keywordDesSub,
      titleDesSubBottom,
      // 根据后端数据动态生成的类目
      initLoading: true,
      tagItem: [],
      // 分类
      category: [],
      categoryValue: null,
      // 用途
      usage: [],
      usageValue: null,
      // 企业一级分类,
      enterpriseStair: [],
      enterpriseStairValue: [],
      // 企业二级分类
      enterpriseSecond: [],
      enterpriseSecondValue: [],
      // 行业
      industry: [],
      industryValue: [],
      // 风格
      styles: [],
      stylesValue: tagGradeData ? tagGradeData : null,
      //场景
      scene: [],
      // sceneValue: null,

      extra: this.isPhoneOnlineIndex ? [
        { label: '节日活动', value: '1038' },
        { label: '拼多多店铺首页', value: '1039' },
      ] : [
        { label: '节日活动', value: '1038' },
      ],
      extraValue: null,

      kindInfoName: '',
      templ_attr: 2,

      templateClassLinks: {},
      submitName: '提交',
      submitTip: '',
      isSubmit: true,
      isShowCate: ['咨询', '外包', '会计', '法律', '保险', '汽车', '旅游', '酒业', '培训', '教育', '贷款', '基金', '证券', '理财', '美业', '银行', '地产']
      // 当前选中的分类层级
      , activeCategoryLevel: "1"
      // 是否弹出分类层
      , showCategoryComponent: false,
      // 当前点击层级
      selectedLevel: 0,
      // 选中分类api信息
      selectedCategorys: [],
      dispalySelectedCategorys: [],

      aiMarking: false,
    };

    this.descriptionInput = null
    this.titleInput = null

    this.staticInfo = null
    this.actionQueueTimer = null
    this.actionQueue = {
      deleteQueue: [],
      addQueue: [],
    }
    // 如果时直接从子模版加载数据，则不需要初始化注入
    this.noNeedToInjectTemp = false
  }

  /**
   * @description 统一数据获取入口
   * @return {{canvas, isDesigner, tagGradeData, work, onlineDetailSinle: boolean, rt_isMutiSizeTemplate, pageAttr, canvasInfo, createTime, urlProps: string, rt_is_online_detail_page, fourthGradeData, user, templAttrData, info}}
   */
  getStoreData = () => {
    const { info } = storeAdapter.getStore({
      store_name: storeAdapter.store_names.InfoManage,
    });
    const { onlineDetailSinle = false } = this.props;
    const urlProps = getProps()
    const {
      user,
      canvasInfo,
      rt_is_online_detail_page,
      templAttrData,
      fourthGradeData,
      tagGradeData,
      rt_isMutiSizeInfo,
      rt_mutisize_current_selected_tid,
      rt_mutisize_subtemplates,
      canvas,
      work,
      pageAttr,
      createTime,
      rt_isMutiSizeTemplate,
      isDesigner
    } = storeAdapter.getStore({
      store_name: storeAdapter.store_names.paintOnCanvas,
    });

    // 静态数据，生成一次之后就不需要再次计算
    if (!this.staticInfo) {
      const k1 = urlProps['k1'] ? urlProps['k1'] : info.kid_1;
      const k2 = urlProps['k2'] ? urlProps['k2'] : info.kid_2;
      const k3 = urlProps['k3'] ? urlProps['k3'] : info.kid_3;
      const mainPicId = urlProps['picId'] ? urlProps['picId'] : info.id;
      let class_id = urlProps.class_id;

      const currentSubTempCanvas = rt_mutisize_subtemplates.filter(item => item.id == rt_mutisize_current_selected_tid)[0]
      const currentSubTempInfo = rt_isMutiSizeInfo.filter(item => item.id == rt_mutisize_current_selected_tid)[0]
      if (currentSubTempInfo) {
        class_id = currentSubTempInfo.class_id;
      }
      this.staticInfo = {
        k1,
        k2,
        k3,
        class_id,
        task_id: urlProps.task_id,
        // 主模板picid
        mainPicId,
        // 当前提交的picId
        currentPicId: currentSubTempCanvas ? currentSubTempCanvas.link_tid : '',
        hasMultiSize: rt_mutisize_subtemplates.length > 1,
      }
    }


    return {
      ...this.staticInfo,
      canvasInfo,
      info,
      user,
      urlProps,
      canvas,
      work,
      pageAttr,
      createTime,
      rt_isMutiSizeTemplate,
      isDesigner,
      onlineDetailSinle,
      rt_is_online_detail_page,
      templAttrData,
      fourthGradeData,
      tagGradeData,
      rt_isMutiSizeInfo,
      rt_mutisize_current_selected_tid,
    }
  }

  /**
   * @description 将templateClassLinks的数据分离为每个类目节点的值
   * @param templateClassLinks
   */
  centrifugeValue = (templateClassLinks) => {
    const { rt_is_online_detail_page, class_id, onlineDetailSinle, k1, urlProps } = this.getStoreData()
    const { category, extra, usage, scene, industry, enterpriseStair, categoryValue } = this.state
    const defaultShowCategory = this.showCategory()
    const state = {
      industryValue: [],
      usageValue: null,
      categoryValue: defaultShowCategory ? null : categoryValue,
      // sceneValue: null,
      extraValue: null,
      usage,
      scene,
    }

    let categoryItem = null
    const remaining = Object.keys(templateClassLinks)
    /**
     * 由于usage 和 styles是由category派生出来的数据
     * 所以先分发数据明确的项目，分发剩下的数据存入remaining
     *
     * 第一次遍历可以派发出当前命中的category，同时可以根据命中的category和派发剩下的remaining继续遍历迭代
     */
    remaining.forEach(key => {
      const showCategory = this.showCategory(key)
      // 旧逻辑，如果不需要展示Category，则缓存Category的所有数据，并且不需要动态替换，只取第一次数据即可
      const categoryIndex = (!categoryValue || showCategory) ? category.findIndex(item => item.value === key || item.id == key) : -1

      // 如果显示分类，则过滤当前不应该存入的id，否则通过
      const flagKey = showCategory ? !this.needFilterIds.includes(parseInt(key)) : true
      // console.log(parseInt(key));
      if (categoryIndex > -1 && flagKey) {
        state.categoryValue = key
        categoryItem = category[categoryIndex]
        state.usage = categoryItem.subList ? formatOptions(categoryItem.subList) : []
      }
      if (k1 != 1 && !showCategory && state.usage.length > 0 && state.scene.length === 0) {
        state.usage.some(item => {
          if (item.subList) {
            state.scene = formatOptions(item.subList)
            return true
          }
          return false
        })
      }

      if (includesValue(industry, key)) {
        state.industryValue.push(key)
      }

      if (includesValue(extra, key)) {
        state.extraValue = key
      }

    })

    remaining.forEach(key => {
      if (includesValue(state.usage, key)) {
        state.usageValue = key
      }
      // if (includesValue(state.scene, key)) {
      //   state.sceneValue = key
      // }
    })

    // 数据处理完后，过滤category中一些不需要展示的数据
    state.category = formatOptions(
      category,
      {
        exclude: (item) => {
          const id = parseInt(item.id)
          if (rt_is_online_detail_page && onlineDetailSinle && id !== 34) {//电商淘宝时只显示淘宝分类
            return true;
          }
          if (`${class_id}` === '1154' && item.class_name !== "其他") {
            return true;
          }
          if (this.needFilterIds.includes(id)) {
            return true;
          }
          return false
        }
      })

    if (Number(urlProps.class_id) === 1184) {
      let target = [...state.category, ...state.usage].find(item => Number(item.id) === 1085);
      if (target && target.subList) {
        state.scene = formatOptions(target.subList.filter(i => i.id !== 1184))
      }
    }
    if (Number(urlProps.class_id) === 1) {
      let target = [...state.category, ...state.usage].find(item => Number(item.id) === 1);
      if (target && target.subList) {
        state.scene = formatOptions(target.subList)
      }
    }
    if (Number(urlProps.class_id) === 10) {
      let target = [...state.category, ...state.usage].find(item => Number(item.id) === 10);
      if (target && target.subList) {
        state.scene = formatOptions(target.subList.filter(i => i.id !== 1184))
      }
    }

    // 设计师对联不添加场景
    if (Number(urlProps.class_id) === 1169) {
      state.scene = []
    }

    this.setState(state)
  }

  excludeMainTempDefaultId = (itemIds) => {
    const { currentPicId } = this.getStoreData()
    const { templateClassLinks } = this.state
    const tempLinks = Object.keys(templateClassLinks)
    const itemLinks = Object.keys(itemIds)
    const addLinks = []
    const removeLinks = []
    // 排除主模板中的默认class
    tempLinks.forEach(key => {
      if (templateClassLinks[key].isDefault !== '1') {
        addLinks.push(key)
      }
    })
    // 排除增加当前子模板的默认class
    itemLinks.forEach(key => {
      if (itemIds[key].isDefault !== '1') {
        removeLinks.push(key)
      }
    })
    this.changeTemplateClassLink(currentPicId, removeLinks, true)
    this.changeTemplateClassLink(currentPicId, addLinks)
  }
  /**
   *
   * @param pid
   * @param replace
   */
  getTemplateClassLinks = (pid = '', replace) => {
    const props = getProps()
    const picId = pid || props.picId;
    assetManager.getTemplateClassLinks(picId).then(data => {
      data.json().then(resultData => {
        if (resultData.stat == 1) {
          if (replace) {
            this.excludeMainTempDefaultId(resultData.msg)
          } else {
            this.centrifugeValue(resultData.msg)
            this.setState({
              templateClassLinks: resultData.msg
            });
          }

        }
      });
    });
  }

  getTemplateClassArr = (picId) => {
    const { task_id, k2, k1 } = this.getStoreData()

    const enterpriseStair = [
      { class_name: "地产", id: 1747, label: "地产", value: '1747' },
      { class_name: "银行", id: 1750, label: "银行", value: '1750' },
      { class_name: "理财", id: 1763, label: "理财", value: '1763' },
      { class_name: "证券", id: 1771, label: "证券", value: '1771' },
      { class_name: "基金", id: 1777, label: "基金", value: '1777' },
      { class_name: "贷款", id: 1783, label: "贷款", value: '1783' },
      { class_name: "培训", id: 1795, label: "培训", value: '1795' },
      { class_name: "保险", id: 1819, label: "保险", value: '1819' },
      { class_name: "法律", id: 1825, label: "法律", value: '1825' },
      { class_name: "会计", id: 1831, label: "会计", value: '1831' },
      { class_name: "外包", id: 1837, label: "外包", value: '1837' },
      { class_name: "咨询", id: 1843, label: "咨询", value: '1843' }
    ]
    const enterpriseSecond = [
      { class_name: "人物表彰", id: 1852, label: "人物表彰", value: '1852' },
      { class_name: "产品营销", id: 1853, label: "产品营销", value: '1853' },
      { class_name: "激励日签", id: 1854, label: "激励日签", value: '1854' },
      { class_name: "喜报", id: 1855, label: "喜报", value: '1855' },
      { class_name: "求职招聘", id: 1856, label: "求职招聘", value: '1856' },
      { class_name: "安全防诈骗", id: 1857, label: "安全防诈骗", value: '1857' },
      { class_name: "排行榜", id: 1858, label: "排行榜", value: '1858' },
      { class_name: "知识科普", id: 1859, label: "知识科普", value: '1859' },
      { class_name: "邀请函", id: 1860, label: "邀请函", value: '1860' },
      { class_name: "社交名片", id: 1861, label: "社交名片", value: '1861' },
      { class_name: "直播课程", id: 1862, label: "直播课程", value: '1862' },
      { class_name: "产品介绍", id: 1863, label: "产品介绍", value: '1863' },
      { class_name: "活动宣传", id: 1864, label: "活动宣传", value: '1864' },
      { class_name: "行情资讯", id: 1865, label: "行情资讯", value: '1865' },
      { class_name: "倒计时", id: 1866, label: "倒计时", value: '1866' }
    ]
    assetManager.getTemplateClassInfo(picId, k1 == 494 ? k1 : '').then(data => {
      data.json().then(resultData => {
        if (resultData.stat == 1) {
          if (!(task_id != 0 && noOtherOptionArr.includes(parseInt(k2)))) {
            resultData.msg.push({
              class_name: '其他',
              id: 141,
              subList: []
            });
          }

          this.setState({
            category: formatOptions(resultData.msg),
            enterpriseStair: enterpriseStair,
            enterpriseSecond: enterpriseSecond,
            industry: formatOptions(resultData.industry),
            isSubmit: resultData.is_submit
          });

        }
      });
    });
  }

  getKindInfo = (k1, k2) => {
    const { rt_is_online_detail_page } = this.getStoreData()
    assetManager.getKindInfo(k1, k2).then(res => {
      if (res.stat) {
        let kindInfoName = `${res.msg.k1}>${res.msg.k2}`
        if (rt_is_online_detail_page && this.props.onlineDetailSinle) {
          kindInfoName = res.msg.k1;
        }
        this.setState({
          kindInfoName
        });
      }
    });

  }



  // 获取类目全量数据
  getCategoryTree = (picId) => {
    assetManager.getCategoryTree(picId).then(res => {
      if (res.stat === 1) {
        categoryRoot.subList = res.msg;
      }

      this.getSelectedCategory(picId).then((selectedCategory) => {
        const checkedCategory = selectedCategory.map(_ => ({ ..._ }));

        premake(categoryRoot, checkedCategory);

        const categoryArr = [];
        categoryTreeMap.forEach(value => {
          if (value.level === 0 && value.checked) {
            categoryArr.push(value);
          }
        })

        this.setState({
          dispalySelectedCategorys: [...categoryArr]
        })
      })

    })
  }

  getSelectedCategory = (picId) => {
    return new Promise((resolve) => {
      assetManager.getCheckedCategory(picId).then(res => {
        const checkedCategory = [];
        if (res.stat == 1) {
          // author: "22284891" class_id: "1" id: "5848866" isDefault: "1"

          for (let key in res.msg) {
            checkedCategory.push(res.msg[key]);
          }

          this.setState({
            selectedCategorys: checkedCategory
          })
          resolve(checkedCategory);
          console.log('checkedCategory', checkedCategory);
        }
      })
    })

  }

  getTagInfo = (k2, class_id) => {
    assetManager.getTagInfo(k2, class_id).then(data => {
      data.json().then(resultData => {
        const tagItem = []
        let styles = []
        Object.keys(resultData).forEach(key => {
          const tagName = formatOptions(resultData[key], { labelKey: 'tagName' })
          tagItem.push({
            name: key,
            tagName,
          })
          if (key === '风格') {
            styles = tagName
          }
        })

        this.setState({ tagItem, styles })
      })
    })
  }

  componentDidUpdate(prevProps, prevState, snapshot) {
    const { category, industry, styles, initLoading, enterpriseStair, enterpriseSecond } = this.state
    const { currentPicId } = this.getStoreData()

    if (!this.noNeedToInjectTemp && prevState.industry.length === 0 && industry !== prevState.industry) {
      // 由于injectTemp依赖industry数据，所以在industry数据获取以后 以后再调用
      this.injectTemp()
    }
    if (initLoading && ([category, industry, styles].every(item => item.length > 0) || isAIDesign())) {
      this.getTemplateClassLinks(currentPicId)
      this.setState({
        initLoading: false
      })
    }
  }

  /**
   * 初始化成功后执行的方法
   */
  componentDidMount() {
    let { k1, k2, info, rt_is_online_detail_page, class_id, currentPicId, rt_mutisize_current_selected_tid, rt_isMutiSizeInfo, mainPicId } = this.getStoreData()
    if (this.props.kid_1 && this.props.kid_2) {
      k1 = this.props.kid_1
      k2 = this.props.kid_2
    }
    if (info.kid_2 > 0 && !rt_is_online_detail_page) {
      k2 = info.kid_2
    }
    if (rt_mutisize_current_selected_tid) {
      rt_isMutiSizeInfo.find(i => {
        if (i.id === rt_mutisize_current_selected_tid) {
          k2 = i.kid_2;
          return true;
        }
        return false;
      })
    }

    this.getKindInfo(k1, k2);
    this.getCategoryTree(mainPicId);
    this.getTagInfo(k2, class_id)
    this.getTemplateClassArr(mainPicId)

    if (localStorage.ue_local_mutisize_uploadtemp) {
      this.noNeedToInjectTemp = true
      const { state, title, description } = JSON.parse(localStorage.ue_local_mutisize_uploadtemp)

      setTimeout(() => {
        this.descriptionInput && (this.descriptionInput.value = description)
        this.titleInput && (this.titleInput.value = title)
      })
      this.setState(state)
      this.getTemplateClassLinks(currentPicId, true)
      return
    }
  }

  submitChange = () => {
    const { currentPicId, } = this.getStoreData()
    const { deleteQueue, addQueue } = this.actionQueue
    // 复制数据以后立马清空执行队列，避免队列污染
    const actions = [...deleteQueue, ...addQueue]
    this.actionQueue = {
      deleteQueue: [],
      addQueue: [],
    }
    Promise.all(actions.map(item => item()))
      .then()
      .finally(() => {
        this.getTemplateClassLinks(currentPicId)
      })
  }
  /**
   *
   * @param picId
   * @param {string||string[]} classId
   * @param isDel
   */
  changeTemplateClassLink = (picId, classId, isDel = false) => {
    if (Array.isArray(classId)) {
      classId.forEach(item => {
        this.changeTemplateClassLink(picId, item, isDel)
      })
      return
    }
    if (isDel) {
      this.actionQueue.deleteQueue.push(
        () => assetManager.delTemplateClassLink(classId, picId)
      )
    } else {
      this.actionQueue.addQueue.push(
        () => assetManager.addTemplateClassLink(classId, picId)
      )
    }
    clearTimeout(this.actionQueueTimer)
    this.actionQueueTimer = setTimeout(() => {
      this.submitChange()
    }, 10)
  }
  getEmphaticMarks = (work)=>{
    const emphaticMarkIds = []
    for (let key in work.pages) {
      for (let subKey in work.pages[key].assets) {
        let asset = work.pages[key].assets[subKey];
        if(asset.meta.type != 'text'){
          continue
        }
        if(asset.meta.v == 3){
          asset.attribute.text.forEach((item)=>{
            if(item.emphaticMark){
              emphaticMarkIds.push(item.emphaticMark.id)
            }
          })
        }else{
          if(asset.attribute.emphaticMark){
            emphaticMarkIds.push(asset.attribute.emphaticMark.id)
          }
        }
      }
    }
    return [...emphaticMarkIds]

  }
  // 模板主图
  getPirmaryAsset() {
          const { work, pageInfo } = storeAdapter.getStore({
              store_name: storeAdapter.store_names.paintOnCanvas,
          });
          const curPage = pageInfo.pageNow;
  
          const primaryAsset = work.pages[curPage].assets.find((asset) => {
              return (
                ['image', 'pic', 'background'].includes(asset.meta.type) &&
                  (asset.meta.rt_tag_type == 'primary_img' ||
                      asset.meta.type == 'background')
              );
          });
          return primaryAsset;
  }
  /**
   * 提交模版
   */
  uploadClickEvent = async (e) => {

    let {
      templ_attr,
      templateClassLinks,
      kindInfoName,
      industry,
      enterpriseStair,
      enterpriseStairValue,
      scene,
      categoryValue,
      extraValue,
      // sceneValue,
      stylesValue,
      usageValue,
      industryValue,
      isSubmit,
      enterpriseSecond,
      enterpriseSecondValue
    } = this.state;

    const {
      user,
      canvasInfo,
      canvas,
      work,
      pageAttr,
      createTime,
      rt_isMutiSizeTemplate,
      rt_is_online_detail_page,
      isDesigner,
      info,
      urlProps,
      rt_mutisize_current_selected_tid,
      rt_isMutiSizeInfo
    } = this.getStoreData();

    let array = {},
      k1 = info.kid_1 > 0 ? info.kid_1 : urlProps['k1'],
      k2 = info.kid_2 > 0 ? info.kid_2 : urlProps['k2'],
      k3 = info.kid_3 > 0 ? info.kid_3 : urlProps['k3'];

    if (!isSubmit) {
      const uploadLimitTwoPages = [157, 159] //上传限制两页 宣传单 菜单/价目表
      for (let key in work.pages) {
        for (let subKey in work.pages[key].assets) {
          let subItem = work.pages[key].assets[subKey];
          if (subItem.attribute && subItem.attribute.resId && subItem.attribute.resId.toString().split('-')[0] == 'UA') {
            this.setState({
              submitTip: '存在非法图片'
            })
            return;
          }
        }
      }
      const emphaticMarkIds = this.getEmphaticMarks(work)
      array.emphaticMarkIds = emphaticMarkIds.join(',')
      // 限制 宣传单 菜单/价目表 只能上传两页
      for (let key in templateClassLinks) {
        for (let i = 0; i < uploadLimitTwoPages.length; i++) {
          if (key == uploadLimitTwoPages[i] && work.pages.length != 2) {
            this.setState({
              submitTip: `${kindInfoName} 只能设置2张页面`
            });
            return;
          }
        }
      }


      if (rt_mutisize_current_selected_tid) {
        rt_isMutiSizeInfo.find(i => {
          if (i.id === rt_mutisize_current_selected_tid) {
            k2 = i.kid_2;
            return true;
          }
          return false;
        })
      }

      if (!canvasInfo.isRemake) {
        // 图片属性验证 START
        if (user.super_designer) {
          if (templ_attr != 2 && templ_attr != 4) {
            this.setState({
              submitTip: '请选择图片属性'
            })
            return;
          }
        } else {
          templ_attr = 1;
        }

        if (this.isOnlineTaoBao) {
          array.pid = extraValue
        } else {
          array.pid = categoryValue
        }
        if (usageValue) {
          array.cid = usageValue
        }
        // if (sceneValue) {
        //   array.tid = sceneValue
        // }
        if (stylesValue) {
          array.tag = [stylesValue]
        }
        if (industryValue.length > 0) {
          array.industry = industryValue
        }
        if (enterpriseStairValue.length > 0) {
          array.firm_type_one = enterpriseStairValue[0]
        }
        if (enterpriseStairValue.length > 0 && enterpriseSecondValue.length > 0) {
          array.firm_type_two = enterpriseSecondValue[0]
        }
        array.title = this.titleInput.value
        array.description = this.descriptionInput.value

        if (array.title.length === 0) {
          this.setState({
            submitTip: '标题不能为空'
          })
          return;
        }
        if (array.title.length > 20) {
          this.setState({
            submitTip: '标题不能超过20个字符'
          })
          return;
        }

        if (this.showCategoryUsage() && !(array.cid > 0)) {
          this.setState({
            submitTip: '请选择分类用途'
          })
          return;
        }

        // 需求变更：无论什么条件下，行业必选
        if (industry.length > 0 && !industryValue.length > 0 && !isAIDesign()) {
          this.setState({
            submitTip: '请选择行业'
          })
          return;
        }

        // if (array['pid'] != 141 &&
        //   !array.hasOwnProperty('cid') &&
        //   ((scene.length === 0 && [31, 33].indexOf(categoryValue) >= 0) ||
        //     this.needFilterIds.indexOf(categoryValue) === -1) &&
        //   (!rt_isMutiSizeTemplate || info.dont_show_more_size)
        // ) {
        //   this.setState({
        //     submitTip: '请选择用途'
        //   })
        //   return;
        // }
        // if (scene.length > 0 && !array.hasOwnProperty('tid')) {
        //   this.setState({
        //     submitTip: '请选择场景'
        //   })
        //   return;
        // }

        if (!array['tag'] && !isAIDesign()) {
          this.setState({
            submitTip: '风格不能为空'
          })
          return;
        }

        if (array.description.length === 0) {
          this.setState({
            submitTip: '关键字不能为空'
          })
          return;
        }
        if (array.description.split(' ').length < 7 && !isAIDesign()) {
          this.setState({
            submitTip: '关键词不能少于7个'
          })
          return;
        }
        if (array.description.length < 20 && !isAIDesign()) {
          this.setState({
            submitTip: '关键词不应少于20字'
          })
          return;
        }
        if (array.description.length > 100) {
          this.setState({
            submitTip: '关键字不能超过100个字符'
          })
          return;
        }
        if (rt_isMutiSizeTemplate && !info.dont_show_more_size) {//多尺寸
          let { rt_mutisize_subtemplates } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
          });
          let mutisize_tids = [];
          for (let i = 0, len = rt_mutisize_subtemplates.length; i < len; ++i) {
            if (mutisize_tids.indexOf(Number(rt_mutisize_subtemplates[i].link_tid)) < 0) {
              mutisize_tids.push(Number(rt_mutisize_subtemplates[i].link_tid));
            }
          }
          if ((k1 == 3 && k2 == 202) || (k1 == 3 && k2 == 203)) {//钻展
            if (mutisize_tids.length < 2) {
              this.setState({
                submitTip: '请完成所有尺寸模板制作'
              })
              return;
            }
          } else if (k1 = 1) {

          } else {
            if (mutisize_tids.length < 3) {
              this.setState({
                submitTip: '请完成所有尺寸模板制作'
              })
              return;
            }
          }
        }
      }
    }

    Object.assign(array, {
      id: urlProps['picId'],
      doc: { canvas: canvas, work: work, pageAttr: pageAttr },
      width: canvas.width,
      height: canvas.height,
      kid_1: k1,
      kid_2: k2,
      kid_3: k3,
      templ_attr: templ_attr
    })

    this.setState({
      submitName: '提交中...',
      submitTip: '提交中...'
    })
    //多尺寸 将数据保存到本地
    if (rt_isMutiSizeTemplate && !info.dont_show_more_size) {
      let linksIds = Object.keys(templateClassLinks) || [];
      let obj = {
        title: array['title'],
        description: array['description'],
        allId: {
          tag: array['tag'],
          industry: array['industry'],
          enterpriseStair: array['enterpriseStair'],
          enterpriseSecond: array['enterpriseSecond'],
          tid: array['tid']
        },
        state: this.state
      }
      localStorage.ue_local_mutisize_uploadtemp = JSON.stringify(obj);
    }

    // 电商详情单个模板提交
    if (rt_is_online_detail_page && isDesigner) {
      const { onlineDetailSinle = false } = this.props;
      let {
        online_detail_rightaction_params,
        pageInfo,
        online_detail_single_floor_uploaded_ids
      } = storeAdapter.getStore({
        store_name: storeAdapter.store_names.paintOnCanvas,
      });
      const { rt_online_detail_current_index = 0, pageNow } = pageInfo;
      let _singleFLoorObj = online_detail_single_floor_uploaded_ids[rt_online_detail_current_index] || {
        id: '',
        isUpload: false
      }
      if (onlineDetailSinle && _singleFLoorObj.id != '') {
        let tempCanvas = JSON.parse(JSON.stringify(canvas)),
          tempWork = JSON.parse(JSON.stringify(work));

        const { floorCutting = [] } = tempCanvas;
        let curFloorHeight = floorCutting[rt_online_detail_current_index].height;

        let limitTop = 0, limitBottom = 0; //top : 上界限 , bottom 下界限
        floorCutting.map((v, i) => {
          if (i <= rt_online_detail_current_index) {
            limitBottom += v.height;
          }
        })
        limitTop = limitBottom - curFloorHeight;

        // 过滤 和 重置work中元素位置

        tempWork.pages.map((page, pageNum) => {
          let filterAssets = [];
          page.assets.map((asset, assetIndex) => {
            if (checkoutAssetIsInArea(limitTop, limitBottom, asset)) {
              let tempAsset = JSON.parse(JSON.stringify(asset));
              tempAsset.transform.posY -= limitTop;
              filterAssets.push(tempAsset);
            }
          });
          tempWork.pages[pageNum].assets = filterAssets;
        });
        if (tempWork.pages[0].assets.length == 0) {
          this.setState({
            submitName: '',
            submitTip: ''
          });
          return;
        }
        // 过滤 和 重置work中元素位置 end

        delete tempCanvas.floorCutting;
        tempCanvas.height = curFloorHeight;
        // 覆盖原模板数据
        array = {
          ...array,
          id: _singleFLoorObj.id, //单个楼层提交 如果保存具体楼层的ID  判断提交后 不能再次提交 ，
          doc: {
            ...array.doc,
            canvas: tempCanvas,
            work: tempWork
          },
          height: curFloorHeight,
          isSingleFloor: true
        }
        // 覆盖原模板数据

      }
    }
    // 电商详情单个模板提交end

    array.class_id_ext = this.calcuClassid();
    // return;
    if (isAIDesignPPT()) {
      array.template_type = 3;
    }
    if(isAIDesignPicDrawTemp()){
      const primaryPicAsset = this.getPirmaryAsset();
      if(primaryPicAsset){
        array['design_main_image_id'] = primaryPicAsset.attribute.resId;
      }
    }
    if (this.state.aiMarking) {
        await new Promise((resolve) => {
            emitter.emit('convertCanvasPageToImage', {format:'jpeg',quality:1,index:0, handleSave: true, callback: (urlPath) => {
                array['urlPath'] = urlPath;
                array['ai_marking'] = 1;
                resolve();
            }})
        })
    }
    //多尺寸 将数据保存到本地
    assetManager.saveTemplate(array).then(data => {
      data.json().then(result1 => {
        if (result1.stat == 1) {
          this.setState({
            submitName: '提交成功'
          })
          InfoManageHelper.updateInfo({
            title: array.title,
            description: array.description,
          })
          let {
            rt_mutisize_subtemplates,
            rt_mutisize_current_selected_tid,
            rt_isMutiSizeInfo,
            rt_isMutiSizeTemplate,
            rt_is_online_detail_page,
            isDesigner
          } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
          });
          let shouldNot_Go = false, mutisize_tids = [];
          if (rt_isMutiSizeTemplate) {
            for (let i = 0, len = rt_mutisize_subtemplates.length; i < len; ++i) {
              if (rt_mutisize_current_selected_tid == tempIdBuilder(rt_mutisize_subtemplates[i], i)) {
                rt_mutisize_subtemplates[i].audit_through = 2;
                rt_mutisize_subtemplates[i].full_info = {
                  canvas, work, pageAttr
                };
                break;
              }
            }
            /*for(let i = 0 , len = rt_mutisize_subtemplates.length ; i < len ; ++i){
                if(mutisize_tids.indexOf(Number(rt_mutisize_subtemplates[i].link_tid)) < 0){
                    mutisize_tids.push(Number(rt_mutisize_subtemplates[i].link_tid));
                }
            }*/
            for (let i = 0, len = rt_mutisize_subtemplates.length; i < len; ++i) {
              if (rt_mutisize_subtemplates[i].audit_through == 1) {
                shouldNot_Go = true;
                break;
              }
            }
            CanvasPaintedLogic.mutisizeSaveSubTemplateInfo(rt_mutisize_subtemplates)
            // 将模板设置为已提交
            CanvasPaintedLogic.mutisizeSetCurrentStatusUpdate({
              audit_through: 2,
            })

            if (rt_mutisize_subtemplates.length != rt_isMutiSizeInfo.length) {
              shouldNot_Go = true;
            }
            if (rt_isMutiSizeInfo[0].can_search == 0) {
              shouldNot_Go = false;
            }
            /*if(mutisize_tids.length < 3){
                shouldNot_Go = true;
            }*/
          }

          /* 对电商详情单个楼层保存提交处理 */
          if (rt_is_online_detail_page && isDesigner && result1.stat == 1 && array.isSingleFloor) {
            const { onlineDetailSinle = false } = this.props;
            let { online_detail_single_floor_uploaded_ids, pageInfo } = storeAdapter.getStore({
              store_name: storeAdapter.store_names.paintOnCanvas,
            });
            const { rt_online_detail_current_index = 0, pageNow } = pageInfo;
            let _singleFLoorObj = online_detail_single_floor_uploaded_ids[rt_online_detail_current_index] || {
              id: '',
              isUpload: false
            }
            if (onlineDetailSinle && _singleFLoorObj != '') {
              let floorUploadStatus = [...online_detail_single_floor_uploaded_ids];
              floorUploadStatus[rt_online_detail_current_index].isUpload = true;
              // TODO 处理电商多页
              // canvasStore.dispatch(paintOnCanvas('MUTISIZE_SET_DETAIL_FLOOR_UPLOAD_STATUS', {
              //   eachFloorUploadStatus: floorUploadStatus,
              // }));
            }
            array.isSingleFloor ? shouldNot_Go = true : '';//单个跳转不进行跳转
          }
          /* 对电商详情单个楼层保存提交处理 end  */
          if (!shouldNot_Go) {
            UpdateCanvas.updateCanvas('UPDATE_RT_TEMP_LOCAL_ONLINE_CHECKED_ID', {
              rt_temp_local_online_checked_id: ''
            })
            // canvasStore.getState().onCanvasPainted.rt_temp_local_online_checked_id = '';
          }
          window.onbeforeunload = function (e) {
          }

          /*显示成功弹窗START*/
          let windowInfo = {
            windowContent: <UploadCompletePopup remake={result1.remake}
              shouldNot_Go={(shouldNot_Go && !info.dont_show_more_size)} />,
            popupWidth: 630
          };


          let specificWordIdArr = [],
            groupWordID = [],
            SVGID = [];

          let groupWordNameArr = [];

          work.pages.map((item) => {
            item.assets.map((subItem) => {
              if (subItem.meta.groupWordID) {
                groupWordNameArr.push(subItem.meta.group);
              }
            })
          });

          work.pages.map((item) => {
            item.assets.map((subItem) => {

              if (groupWordNameArr.includes(subItem.meta.group) && !subItem.meta.groupWordID) return;

              if (subItem.attribute.effect) {
                specificWordIdArr.push(subItem.attribute.effect.split('@')[0]);

              }

              if (subItem.meta.groupWordID) {
                groupWordID.push(subItem.meta.groupWordID);
              }

              if (subItem.meta.type == 'SVG') {
                SVGID.push(subItem.attribute.resId);
              }

            })
          });

          if (specificWordIdArr.length > 0) {
            assetManager.setPv_new(320, {
              additional: {
                s0: specificWordIdArr.join(','),
                i0: specificWordIdArr.length,
                i1: createTime,
                ti: urlProps['picId'],
              }
            });
          }
          if (groupWordID.length > 0) {
            assetManager.setPv_new(322, {
              additional: {
                s0: groupWordID.join(','),
                i0: createTime,
                ti: urlProps['picId'],
              }
            });
          }
          if (SVGID.length > 0) {
            assetManager.setPv_new(321, {
              additional: {
                s0: SVGID.join(','),
                i0: createTime,
                ti: urlProps['picId'],
              }
            });
          }
          if (rt_is_online_detail_page && isDesigner && result1.stat == 1 && array.isSingleFloor) {
            emitter.emit('online_detail_upload_feedbacks', { isRight: true, text: '模板提交成功' });
            emitter.emit('popupWindow', windowInfo);
            emitter.emit('popupClose');
          } else {
            emitter.emit('popupWindow', windowInfo);
          }
          !shouldNot_Go ? localStorage.removeItem('ue_local_mutisize_uploadtemp') : '';
          /*显示成功弹窗END*/
        } else {
          if (rt_is_online_detail_page && isDesigner && array.isSingleFloor) {
            emitter.emit('online_detail_upload_feedbacks', { isRight: false, text: '尺寸不符合模板要求' })
          }
          this.setState({
            submitName: '提交',
            submitTip: '提交失败（' + result1.stat + '）'
          })
        }

      });
    });
  }

  tempAttrClickEvent = (e, { value }) => {
    this.setState({
      templ_attr: value
    });
    updateCanvas.updateCanvas('SAVE_SUBMIT_DATA', {
      templ_attr: value,
    });
    // canvasStore.dispatch(paintOnCanvas('SAVE_SUBMIT_DATA', {
    //   templ_attr: value,
    // }))
  }
  onTypeChange = (type, e, { value }) => {
    const { currentPicId } = this.getStoreData()
    const state = {
      [type + 'Value']: value
    }
    const autoReplace = () => {
      this.changeTemplateClassLink(currentPicId, this.state[type + 'Value'], true)
      this.changeTemplateClassLink(currentPicId, value)
    }
    switch (type) {
      //first
      case this.typeKey.category:
        state.usageValue = null
        autoReplace()
        break
      //second
      case this.typeKey.usage:
        autoReplace()
        break
      //third
      case this.typeKey.scene:
        autoReplace()
        break
      //fourthGrade
      case this.typeKey.industry:
        const { industryValue } = this.state
        const oldValue = [...industryValue]
        const newValue = [...value]
        /**
         * 将两个数组重合的部分去除，oldValue剩余的是需要删除的，newValue剩余的事需要新增的
         */
        industryValue.forEach((item) => {
          const i = newValue.findIndex(v => v === item)
          if (i > -1) {
            const j = oldValue.findIndex(v => v === item)
            oldValue.splice(j, 1)
            newValue.splice(i, 1)
          }
        })
        this.changeTemplateClassLink(currentPicId, oldValue, true)
        this.changeTemplateClassLink(currentPicId, newValue)
        break
      case this.typeKey.enterpriseStair:
        const { enterpriseSecondValue } = this.state;
        if (enterpriseSecondValue.length > 0) {
          this.setState({
            enterpriseSecondValue: []
          })
          this.changeTemplateClassLink(currentPicId, enterpriseSecondValue[0], true);
        }
        autoReplace()
        break;
      case this.typeKey.enterpriseSecond:
        autoReplace()
        break;
      case this.typeKey.styles:
        UpdateCanvas.updateCanvas('SAVE_SUBMIT_DATA', {
          tag_grade: value,
        });
        // canvasStore.dispatch(paintOnCanvas('SAVE_SUBMIT_DATA', {
        //   tag_grade: value,
        // }))
        break
      case this.typeKey.extra:
        this.changeTemplateClassLink(currentPicId, value)
        UpdateCanvas.updateCanvas('UPDATE_RT_TEMP_LOCAL_ONLINE_CHECKED_ID', {
          rt_temp_local_online_checked_id: value
        })
        // canvasStore.getState().onCanvasPainted.rt_temp_local_online_checked_id = value;
        break
    }
    this.setState(state)
  }

  /**
   * 组件初始化时，将缓存的数据还原
   */
  injectTemp = () => {
    const {
      info,
      rt_isMutiSizeTemplate,
      rt_temp_local_online_checked_id,
      currentPicId,
    } = this.getStoreData()
    const { industry, templateClassLinks } = this.state
    const state = {
      industryValue: []
    }

    setTimeout(() => {
      if (info && info.title && this.titleInput) {
        this.titleInput.value = info.title
      }
      if (info.description && this.descriptionInput) {
        this.descriptionInput.value = info.description
      }
    }, 100);
    if (Array.isArray(info.tags)) {
      state.stylesValue = info.tags[0]
    }
    // 旧逻辑，暂时保持不动
    if (rt_isMutiSizeTemplate && localStorage.ue_local_mutisize_uploadtemp && !info.dont_show_more_size) {
      const uploadTemp = JSON.parse(localStorage.ue_local_mutisize_uploadtemp);
      if (rt_temp_local_online_checked_id) {
        this.changeTemplateClassLink(rt_temp_local_online_checked_id, currentPicId)
      }
      industry.map(v => {
        if (templateClassLinks[v.id]) {
          this.changeTemplateClassLink(v.id, currentPicId, true)
        }
        let allIds_2 = [uploadTemp.allId.industry, ...uploadTemp.allId.tag, uploadTemp.allId.tid];
        if (allIds_2 && allIds_2.indexOf('' + v.id) > -1) {
          this.changeTemplateClassLink(v.id, currentPicId, true)
          state.industryValue.push(v.id)
        }
      })
    }
    this.setState(state)
  }

  /**
   * 判断当前分类是否是 3D海报
   * @returns
   */
  is3DPoster() {
    const { urlProps } = this.getStoreData()
    if (Number(urlProps.class_id) === 1630) {
      return true;
    }

    return false;
  }

  showCategory = (newCategoryValue) => {
    const { categoryValue, scene } = this.state
    const { k1 } = this.getStoreData()
    if (k1 == 1) {
      return false
    }
    const cValue = newCategoryValue || categoryValue
    const noScene = includesValue([31, 33], cValue) && scene.length === 0
    const withoutSomeIds = !includesValue(this.needFilterIds, cValue)
    // console.log({
    //   noScene,
    //   withoutSomeIds,
    //   categoryValue,
    //   needFilterIds: this.needFilterIds,
    //   result: (noScene || withoutSomeIds) && this.dontShowClassifyAndUse
    // });
    return (noScene || withoutSomeIds) && this.dontShowClassifyAndUse
  }

  /**
   * 是否显示分类 -> 用途
   */
  showCategoryUsage() {
    const is3DPoster = this.is3DPoster();
    let flag = false;
    if (is3DPoster) {
      flag = true;
    }

    return flag;
  }

  showUsage = (class_id) => {
    return [1184, 1, 10].includes(class_id)
  }

  classIdConfirm = (categorys) => {
    this.setState({
      showCategoryComponent: false,
      dispalySelectedCategorys: categorys
    })

  }

  calcuClassid = () => {
    const result = new Set();
    const call = (node) => {
      node.forEach(obj => {
        if(obj.selectedList && obj.selectedList.length ){
          call(obj.selectedList);
        }
        result.add(obj.id);
      })
    }
    call(this.state.dispalySelectedCategorys)
    return Array.from(result);
  }

  calcuCategory = (data) => {
    const result = [];
    let startIndex = 0;
    const call = (node) => {
      node.forEach(obj => {
        call(obj.selectedList);
        if (!result[startIndex]) {
          result[startIndex] = [];
        }
        if (!result[startIndex][obj.level]) {
          result[startIndex][obj.level] = [];
        }

        result[startIndex][obj.level].push(obj);

        if (obj.level === 0) {
          startIndex++;
        }
      })
    }
    call(data)
    const html = [];
    let single = [undefined, undefined, undefined];
    result.forEach(r => {

      for (let m = 0; m < r[0].length || 0; m++) {
        single[r[0][m].level] = r[0][m];
        if (!r[1]) {
          html.push(single);
          single = [undefined, undefined, undefined];
        }
        for (let n = 0; n < (r[1] || []).length; n++) {
          if (r[1][n].parentId === r[0][m].id) {
            single[r[1][n].level] = r[1][n];
            if (!r[2]) {
              html.push(single);
              single = [undefined, undefined, undefined];
            }
          }
          for (let k = 0; k < (r[2] || []).length; k++) {
            if (r[2][k].parentId === r[1][n].id) {
              single[r[2][k].level] = r[2][k];
              html.push(single);
              single = [undefined, undefined, undefined];
            }

          }
        }
      }

    })
    return html;
  }

  categoryPopCancel = () => {
    this.setState({
      showCategoryComponent: false
    })
  }

  categoryPopup = (level) => {

    this.setState({
      showCategoryComponent: true,
      selectedLevel: level
    })

  }

  handleCategoryChange = (categoryObj, params) => {
    categoryObj.checked = params.checked ? 1 : 0;
    categoryObj.selectedList.forEach(selected => {
      if (!params.checked) {
        selected.checked = params.checked ? 1 : 0
      }
    });
    this.setState({
      dispalySelectedCategorys: [...this.state.dispalySelectedCategorys]
    })
  }

  render() {
    const {
      industry,
      industryValue,
      category,
      categoryValue,
      styles,
      stylesValue,
      usage,
      usageValue,
      extra,
      extraValue,
      scene,
      // sceneValue,
      initLoading,
      keywordDesSub,
      templ_attr,
      kindInfoName,
      titleDesSub,
      titleDesSubBottom,
      submitName,
      submitTip,
      isSubmit,
      enterpriseSecond,
      enterpriseSecondValue,
      enterpriseStair,
      enterpriseStairValue,
      isShowCate,
      showCategoryComponent
    } = this.state
    const { user, urlProps, k1 } = this.getStoreData()

    const showCategory = this.showCategory()

    const showUsage = this.showUsage(Number(urlProps.class_id))
    const showCategoryUsage = this.showCategoryUsage();
    console.log(this.state.dispalySelectedCategorys)
    return (
      <div className="uploadPopup">
        {(initLoading && !isSubmit) && <div>加载中...</div>}
        {
          !initLoading && (
            <div
              className="scroll_content_auto"
              style={{
                width: 560,
                marginLeft: '-10px',
                paddingLeft: '10px',
                // height: 350,
                overflowX: "hidden",
                overflowY: "auto",
                marginTop: '12px'
              }}
            >
              <Form>
                <div className="uploadPopupTitle">完善上传信息</div>
                <div className="item" style={{ marginBottom: '65px' }}>
                  <div className="itemName">标题：</div>
                  <div className="itemContent">
                    <input type="text" name="title" ref={ref => this.titleInput = ref} />
                    <p className="desSub"
                      dangerouslySetInnerHTML={{ __html: titleDesSub }}
                      style={{ bottom: titleDesSubBottom + 'px' }} />
                  </div>
                </div>

                <FormItem label='关键词' style={{ marginBottom: '80px' }}>
                  <input type="text" name="description" ref={ref => this.descriptionInput = ref} />
                  <p className="desSub" dangerouslySetInnerHTML={{ __html: keywordDesSub }} />
                </FormItem>

                {/* <Breadcrumb
                  separator=">"
                  items={[
                    {
                      title: 'Home',
                    },
                    {
                      title: 'Application Center',
                      href: '',
                    },
                    {
                      title: 'Application List',
                      href: '',
                    },
                    {
                      title: 'An Application',
                    },
                  ]}
                /> */}
                <div className="category-navigator-container">
                  {
                    !showCategoryComponent &&

                    <div className="category-interactive">
                      <span className="category-level-name" onClick={() => this.categoryPopup("0")}>一级分类</span><span className='arrow-right'></span>
                      <span className="category-level-name" onClick={() => this.categoryPopup("1")}>二级分类</span><span className='arrow-right'></span>
                      <span className="category-level-name" onClick={() => this.categoryPopup("2")}>三级分类</span>
                    </div>
                  }
                  {showCategoryComponent && <CategoryPopUp level={this.state.selectedLevel} selectedCategorys={this.state.selectedCategorys} confirm={this.classIdConfirm} cancel={this.categoryPopCancel} />}
                </div>

                {
                  !showCategoryComponent &&

                  <div className={'item'}>
                    {/* <div className={'itemContent'}> */}
                    {this.calcuCategory(this.state.dispalySelectedCategorys).map(child => {

                      return <div className="itemcontent-layout">
                        {
                          child.map((c, idx) => {
                            return c ?
                              <Checkbox checked={!!c.checked} onChange={(params) => { this.handleCategoryChange(c, params) }}>
                                <span className="category-name"> {c && c.class_name}</span>
                              </Checkbox> :
                              <span className="category-name empty-holder"></span>

                          })
                        }

                      </div>
                    })}
                  </div>
                }

                {/* {
                  this.isOnlineTaoBao && (
                    <FormItem label='分类' extra='非必选'>
                      <Radio.Group
                        onChange={(e, v) => this.onTypeChange(this.typeKey.extra, e, v)}
                        value={extraValue}
                        options={extra}
                      />
                    </FormItem>
                  )
                } */}

                {/* {
                  showCategory && Number(k1) !== 457 && (
                    <FormItem label='分类'>
                      <Radio.Group
                        onChange={(e, v) => this.onTypeChange(this.typeKey.category, e, v)}
                        options={category}
                        value={categoryValue}
                      />
                    </FormItem>
                  )
                } */}
                {/* {
                  (showCategory || showCategoryUsage) && Number(k1) !== 457 && (
                    <FormItem label='用途'>
                      {
                        usage.length > 0 ? (
                          <Radio.Group
                            onChange={(e, v) => this.onTypeChange(this.typeKey.usage, e, v)}
                            options={usage}
                            value={usageValue}
                          />
                        ) : (
                          <p>请先选择分类</p>
                        )
                      }

                    </FormItem>
                  )
                } */}
                {/*{*/}
                {/*  scene.length > 0 && (*/}
                {/*    <FormItem label={showUsage ? '用途' : '场景'}> /!* 需求非要使用文字 "用途"， 但是又没法使用上面那栏用途 *!/*/}
                {/*      <Radio.Group*/}
                {/*        onChange={(e, v) => this.onTypeChange(this.typeKey.scene, e, v)}*/}
                {/*        options={scene}*/}
                {/*        value={sceneValue}*/}
                {/*      />*/}
                {/*    </FormItem>*/}
                {/*  )*/}
                {/*}*/}
                {/* {
                  !isShowCate.includes(kindInfoName.split('>')[0]) &&
                  <FormItem label='企业一级分类'>
                    <Checkbox.Group
                      onChange={(e, v) => this.onTypeChange(this.typeKey.enterpriseStair, e, v)}
                      options={enterpriseStair}
                      value={enterpriseStairValue}
                      max={1}
                    />
                    <span className="notRequired">(该分类非必选)</span>
                  </FormItem>
                } */}
                {/* {
                  (!isShowCate.includes(kindInfoName.split('>')[0]) && enterpriseStairValue.length > 0) &&
                  <FormItem label='企业二级分类'>
                    <Checkbox.Group
                      onChange={(e, v) => this.onTypeChange(this.typeKey.enterpriseSecond, e, v)}
                      options={enterpriseSecond}
                      value={enterpriseSecondValue}
                      max={1}
                    />
                  </FormItem>
                } */}
                <FormItem label='行业'>
                  <Checkbox.Group
                    onChange={(e, v) => this.onTypeChange(this.typeKey.industry, e, v)}
                    options={industry}
                    max={3}
                    value={industryValue}
                  />
                </FormItem>
                {
                  Boolean(user.super_designer) && (
                    <FormItem label='属性勾选'>
                      <Radio.Group
                        onChange={this.tempAttrClickEvent}
                        value={templ_attr}
                        options={[
                          { label: '精品模板', value: 2 },
                          { label: '套图模板', value: 4 },
                        ]}
                      />
                    </FormItem>
                  )
                }

                <FormItem label='风格'>
                  <Radio.Group
                    onChange={(e, v) => this.onTypeChange(this.typeKey.styles, e, v)}
                    options={styles}
                    value={stylesValue}
                  />
                </FormItem>

                {urlProps.ai_marking && <div>
                    <Checkbox checked={this.state.aiMarking} onChange={(v) => { this.setState({aiMarking: v.checked}) }}>
                        <span className="category-name">AI打标</span>
                    </Checkbox>
                </div>}
   

                {submitTip !== '' && <div className="item submitTip" ref="submitTip">{submitTip}</div>}
                <div className="item buttonArea">
                  <a
                    className="buttonType4"
                    onClick={submitName === '提交' ? this.uploadClickEvent : null}>{submitName}</a>
                </div>
              </Form>
            </div>
          )
        }
        {isSubmit &&
          <div
            className="scroll_content_auto"
            style={{
              width: 560,
              marginLeft: '-10px',
              paddingLeft: '10px',
              height: 350,
              overflowX: "hidden",
              overflowY: "auto",
              marginTop: '12px'
            }}
          >
            <Form>
                {urlProps.ai_marking && <div>
                    <Checkbox checked={this.state.aiMarking} onChange={(v) => { this.setState({aiMarking: v.checked}) }}>
                        <span className="category-name">AI打标</span>
                    </Checkbox>
                </div>}
              {submitTip !== '' && <div className="item submitTip" ref="submitTip">{submitTip}</div>}
              <div className="item buttonArea">
                <a
                  className="buttonType4"
                  onClick={submitName === '提交' ? this.uploadClickEvent : null}>{submitName}</a>
              </div>
            </Form>
          </div>
        }
      </div>
    );
  }
}

export default SubmitHandler
