import { env } from "@editorConfig/env";
import { storeAdapter } from "@v7_logic_core/StoreAdapter";
import { klona as cloneDeep } from "klona";
import { applyToPoint, rotateDEG } from 'transformation-matrix';
import { getFontNameValueList } from "./IPSConfig";

// 是否开启富文本 true开启 false关闭
const openRichText = true;
// 模板元素默认值
export const assetDefault = {
    "text": {
        "effectVariant3D": {
            "bevel": {
                "thickness": 1,
                "size": 0,
                "segments": 3
            },
            "cameraInfo": {
                "type": 1,
                "fov": 60
            },
            "materials": {
                "isColor": true,
                "roughness": 0,
                "metalness": 1
            }
        },
    }
};

const formatNumber = (n, len) => {
    if (typeof n !== 'number') {
        let m = Number(n);
        if (isNaN(m)) {
            return n;
        } else {
            n = Number(n);
        }
    }
    return Number(n.toFixed(len));
};

const checkNaN = (n, defaultValue) => {    
    let m = Number.parseFloat(n);
    if (Number.isNaN(m)) {
        return defaultValue ?? 10; // 作为一个比较小的默认值
    }
    return formatNumber(n, 0);
}

const formatVariableSizePara = (variableSizePara) => {
    if (variableSizePara && Array.isArray(variableSizePara)) {
        for (const i of variableSizePara) {
            if (i.range) {
                if (i.range.max) {
                    i.range.max = formatNumber(i.range.max, 10);
                }
                if (i.range.min) {
                    i.range.min = formatNumber(i.range.min, 10);
                }
            }
        }
    }
    return variableSizePara;
};

const formatLayers = (layers) => {
    if (layers && Array.isArray(layers)) {
        for (const i of layers) {
            if (i.strokeWidth) {
                i.strokeWidth = formatNumber(i.strokeWidth, 10);
            }
        }
    }
    return layers;
};

const formatTableCellMergeInfo = (cells) => {
    if (!cells) {
        return cells;
    }
    const mergeInfo = [];
    for (const row in cells) {
        for (const col in cells[row]) {
            const item = cells[row][col];
            if (item.merge && !item.merged) {
                const mergeKey = `${row}-${col}`;
                const startRow = Number(row);
                const endRow = Number(item.merge.x);
                const startCol = Number(col);
                const endCol = Number(item.merge.y);
                mergeInfo.push({
                    key: mergeKey,
                    startRow,
                    startCol,
                    endRow,
                    endCol,
                });
            }
        }
    }
    const rowLength = cells.length;
    for (let row = 0; row < rowLength; row++) {
        const colLength = cells[row].length;
        for (let col = 0; col < colLength; col++) {
            const item = cells[row][col];
            if (item.merge || 'merge' in item) {
                delete item.merge;
            }
            if (item.merged || 'merged' in item) {
                delete item.merged;
            }
            if (mergeInfo.length > 0) {
                mergeInfo.forEach((m) => {
                    if (!cells[m.endRow] || !cells[m.endRow][m.endCol]) {
                        return;
                    }
                    if (m.startRow === row && m.startCol === col) {
                        item.merge = {
                            x: m.endRow,
                            y: m.endCol,
                        };
                    } else if (m.startRow <= row && row <= m.endRow && m.startCol <= col && col <= m.endCol) {
                        item.merged = {
                            x: m.startRow,
                            y: m.startCol,
                        };
                    }
                });
            }
        }
    }
    return cells;
}

/** 元素特效数据格式化 START */
const ksFormatter = (keyMap) => {
    const formatData = {
        kw: {
            ks: {
                c: []
            }
        }
    }

    const effect = (Array.isArray(keyMap) || !keyMap) ? formatData : cloneDeep(keyMap)
    if (effect?.kw?.ks?.c?.length > 0) {
        effect.kw.ks.c.forEach(item => {
            exitRt(item.ks.l)
        })
    }

    return effect
}


const exitRt = (list) => {
    if (list?.length > 0) {
        for (let i = 0; i < list.length; i++) {
            for (const key in list[i]) {
                if (new RegExp("rt_","g").test(key)) {
                   delete list[i][key]
                }
            }
            if (list[i].l) {
                exitRt(list[i].l)
            }
        }
    }
}
/** 元素特效数据格式化 END */

const formatRichText = (attribute) => {
    const fontFamilyNameList = getFontNameValueList()
    attribute.text.forEach((item) => {
        if (typeof item !== 'string') {
            if (typeof item.fontSize !== 'number') {
                item.fontSize = attribute.fontSize;
            }
            if (typeof item.letterSpacing !== 'number') {
                item.letterSpacing = 0;
            }
            if (typeof item.opacity !== 'number' ) {
                item.opacity = 100;
            }
            if (!item.fontFamily || !fontFamilyNameList.hasOwnProperty(item.fontFamily)) {
                item.fontFamily = attribute.fontFamily;
            }
            if (!item.fontWeight) {
                item.fontWeight = attribute.fontWeight;
            }
            if (!item.fontStyle) {
                item.fontStyle = attribute.fontStyle;
            }
            if (!item.color) {
                item.color = attribute.color;
            }
            if(item.emphaticMark){
                delete item.emphaticMark.svgContent
            }
            if(!item.emphaticMark?.path){
                item.emphaticMark = null
            }
            if(item.rt_isChangeFont){
                delete item.rt_isChangeFont;
            }
            // 不知道哪里来的数据，先都删掉
            if(item.writingMode){
                delete item.writingMode;
            }
            if(item.textAlign){
                delete item.textAlign;
            }
            if(item.textDecoration){
                delete item.textDecoration;
            }
        }
    })
}

const calcRotateRect = (size, rotate) => {
    const { x, y, w, h } = size;
    const tl = { x, y };
    const tr = { x: x + w, y };
    const bl = { x, y: y + h };
    const br = { x: x + w, y: y + h };
    const matrix = rotateDEG(rotate, x + w / 2, y + h / 2);
    const newTl = applyToPoint(matrix, tl);
    const newTr = applyToPoint(matrix, tr);
    const newBl = applyToPoint(matrix, bl);
    const newBr = applyToPoint(matrix, br);
    const t = Math.min(newTl.y, newTr.y, newBl.y, newBr.y);
    const b = Math.max(newTl.y, newTr.y, newBl.y, newBr.y);
    const l = Math.min(newTl.x, newTr.x, newBl.x, newBr.x);
    const r = Math.max(newTl.x, newTr.x, newBl.x, newBr.x);
    return {
        x: l,
        y: t,
        w: r - l,
        h: b - t,
    };
};
const saveFormatPpt = (asset) => {
    if (!asset) return;
    switch (asset.meta.type) {
        case 'text':
            asset.meta.v = 2;
            const textArr = asset.attribute.text;
            if (!textArr || textArr.length == 0) return;
            const isSingleText = typeof textArr[0] == 'string';
            if (isSingleText) {
                textArr.forEach((originText,index)=>{
                    textArr[index] = {
                        beginParentTag: 'li',
                        bulletStyle: 'none',
                        closeParentTag: 'li',
                        color: asset.attribute.color,
                        fontFamily: asset.attribute.fontFamily,
                        fontSize: asset.attribute.fontSize,
                        lineHeight: asset.attribute.lineHeight,
                        fontStyle: asset.attribute.fontStyle,
                        fontWeight: asset.attribute.fontWeight,
                        text: originText,
                    };
                })
                // const originText = textArr[0];
                
            } else {
                const textArrLength = textArr.length;
                let canAddBeginTag = true;
                textArr.forEach((textItem, index) => {
                    if (canAddBeginTag) {
                        textItem.beginParentTag = 'li';
                        canAddBeginTag = false;
                    }
                    textItem.bulletStyle = 'none';
                    textItem.fontWeight = asset.attribute.fontWeight;
                    textItem.fontStyle = asset.attribute.fontStyle;
                    // ppt 生成 和ue 针对富文本的换行处理逻辑不一样
                    if (/\n$/.test(textItem.text) || index == textArrLength - 1) {
                        textItem.text = textItem.text.replace(/\n$/g, '');
                        textItem.closeParentTag = 'li';
                        canAddBeginTag = true;
                    }
                });
            }
            break;
        case 'pic':
            break;
        case 'SVG':
            const originColor = asset.attribute.colors;
            if (originColor['gColor_0']) return;
            asset.meta.v = 2;
            const fillColorKey = Object.keys(originColor)[0];
            const fillColorValue = Object.values(originColor)[0];
            asset.attribute.colors = {
                gColor_0: {
                    fillColor: {
                        [fillColorKey]: fillColorValue,
                    },
                    strokeColor: {},
                    tempColor: originColor.fillColor,
                },
            };
            // Object.entries(originColor).forEach(([key, value]) => {
            //     if (key == 'strokeColor') return;
            //     if (key == 'fillColor') {
            //         return (asset.attribute.colors['gColor_0'].tempColor = value);
            //     }
            //     asset.attribute.colors['gColor_0'].fillColor[key] = value;
            // });
            if (asset.attribute.resId == 88895317) {
                console.log(asset.attribute.colors);
            }
            // 处理svg文字
            if (asset.attribute.textAttr) {
                const textArr = asset.attribute.textAttr.text;
                const textArrLength = textArr.length;
                let canAddBeginTag = true;
                textArr.forEach((textItem, index) => {
                    if (canAddBeginTag) {
                        textItem.beginParentTag = 'li';
                        canAddBeginTag = false;
                    }
                    textItem.bulletStyle = 'none';
                    // ppt 生成 和ue 针对富文本的换行处理逻辑不一样
                    if (/\n$/.test(textItem.text) || index == textArrLength - 1) {
                        textItem.text = textItem.text.replace(/\n$/g, '');
                        textItem.closeParentTag = 'li';
                        canAddBeginTag = true;
                    }
                });
            }

            break;
    }
}
/**
 * 模版格式转换
 */
class TemplateFormat {
    /**
     * 可用格式到存储格式的装换
     */
    saveFormat(doc, format) {
        let tempDoc = {
            canvas: {},
            work: []
        }
        let tempAsset,
            tempPages = []

        /* 提交组合字 */
        let positionTemp = {};
        (doc.canvas.positionHeight || doc.canvas.positionHeight == 0) ? positionTemp['positionHeight'] = doc.canvas.positionHeight : '';
        (doc.canvas.positionWidth || doc.canvas.positionWidth == 0) ? positionTemp['positionWidth'] = doc.canvas.positionWidth : '';
        (doc.canvas.positionX || doc.canvas.positionX == 0 ) ? positionTemp['positionX'] = doc.canvas.positionX : '';
        (doc.canvas.positionY || doc.canvas.positionY == 0 ) ? positionTemp['positionY'] = doc.canvas.positionY : '';
        /*****************/
        let aiDesignImgContainerNum = 0,aiDesignMattingImgContainerNum = 0
        Object.assign(tempDoc.canvas, {
            'title': doc.work.meta.title,
            'width': doc.canvas.width,
            'height': doc.canvas.height,
            'backgroundColor': doc.canvas.backgroundColor,
            'showUnit': doc.canvas.showUnit || 'px',
        },positionTemp)
        if(doc.canvas.floorCutting){
            tempDoc.canvas.floorCutting = doc.canvas.floorCutting;
            if (Array.isArray(doc.work)) {
                doc.work = doc.work.filter(page => page?.length > 0);
            }
        }
        let pageAttr = {
            backgroundColor: [],
            backgroundImage: [],
            backgroundOpacity : [],
        };
        for (let pageItem in doc.work.pages) {
            pageItem = doc.work.pages[pageItem]
            let tempBackgroundColor = pageItem['backgroundColor'];
            if (!tempBackgroundColor) {
                tempBackgroundColor = {
                    r: 255,
                    g: 255,
                    b: 255,
                    a: 1
                };
            }
            pageAttr.backgroundColor.push(tempBackgroundColor);
            pageAttr.backgroundOpacity.push(pageItem.isOpacityBg || false);
            tempPages = [];
            for (let assetItem in pageItem.assets) {
                assetItem = pageItem.assets[assetItem]
                /*处理连接线中width和height为0的情况 */
                if (assetItem.meta.type === 'line') {
                    if (assetItem.attribute.width <= 1) {
                        assetItem.attribute.width = 1;
                    }
                    if (assetItem.attribute.height <= 1) {
                        assetItem.attribute.height = 1;
                    }
                }
                /* 过滤ppt模板中非法数据start */
          
                if(
                    assetItem.attribute.width == 0 || assetItem.attribute.height == 0 || 
                    !isFinite(assetItem.attribute.height) || !isFinite(assetItem.attribute.width)
                ){
                    continue
                }
                if(assetItem.attribute.container && ( !assetItem.attribute.container.width || !assetItem.attribute.container.height || !isFinite(assetItem.attribute.container.height) || !isFinite(assetItem.attribute.container.width))){
                    continue
                }
                /* 过滤ppt模板中非法数据end */
                tempAsset = {
                    meta: {},
                    attribute: {},
                    transform: {}
                }
                if (assetItem.meta.type === 'group') {
                    assetItem.meta.type = 'group';
                }
                Object.assign(tempAsset.meta, {
                    rt_tag_type: assetItem.meta.rt_tag_type,
                    rt_color_tag_type: assetItem.meta.rt_color_tag_type,
                })
                if (assetItem.meta.type === 'SVG') {
                    Object.assign(tempAsset.attribute, {
                        resId: assetItem.attribute.resId,
                        width:  formatNumber(assetItem.attribute.width, 4),
                        height: formatNumber(assetItem.attribute.height, 4),
                        cropXFrom: assetItem.attribute.cropXFrom,
                        cropYFrom: assetItem.attribute.cropYFrom,
                        cropXTo: assetItem.attribute.cropXTo,
                        cropYTo: assetItem.attribute.cropYTo,
                        colors: assetItem.attribute.colors,
                        stroke: assetItem.attribute.stroke,
                        // viewBox: assetItem.attribute.viewBox,
                    })
                    if (assetItem.attribute.decorated) {
                        Object.assign(tempAsset.attribute, {
                            decorated: assetItem.attribute.decorated
                        })
                    }
                    if (assetItem.attribute.textAttr) {
                        // 文字非空
                        const exitText = assetItem.attribute.textAttr?.text?.filter(item => item.text!== '');
                        if (exitText?.length > 0) {
                            Object.assign(tempAsset.attribute, {
                                textAttr: assetItem.attribute.textAttr
                            })
                        }
                    }
                } else if (assetItem.meta.type === 'flow') {
                    Object.assign(tempAsset.attribute, {
                        resId: assetItem.attribute.resId,
                        width:  formatNumber(assetItem.attribute.width, 4),
                        height: formatNumber(assetItem.attribute.height, 4),
                        cropXFrom: assetItem.attribute.cropXFrom,
                        cropYFrom: assetItem.attribute.cropYFrom,
                        cropXTo: assetItem.attribute.cropXTo,
                        cropYTo: assetItem.attribute.cropYTo,
                        colors: assetItem.attribute.colors,
                        stroke: assetItem.attribute.stroke,
                        // viewBox: assetItem.attribute.viewBox,
                    })
                } else if (assetItem.meta.type === 'line') {
                    Object.assign(tempAsset.attribute, {
                        // resId: assetItem.attribute.resId,
                        width:  formatNumber(assetItem.attribute.width, 4),
                        height: formatNumber(assetItem.attribute.height, 4),
                        opacity: assetItem.attribute. opacity,
                        color: assetItem.attribute.color,
                        type: assetItem.attribute. type,
                        lineWidth: assetItem.attribute.lineWidth,
                        startObj: assetItem.attribute.startObj,
                        endObj: assetItem.attribute.endObj,
                        startArrow: assetItem.attribute.startArrow,
                        endArrow: assetItem.attribute.endArrow,
                        start: assetItem.attribute.start,
                        end: assetItem.attribute.end,
                        points: assetItem.attribute.points,
                    })
                } else if (assetItem.meta.type === 'image') {
                    Object.assign(tempAsset.attribute, {
                        resId: assetItem.attribute.resId,
                        width: formatNumber(assetItem.attribute.width, 4),
                        height: formatNumber(assetItem.attribute.height, 4),
                        cropXFrom: assetItem.attribute.cropXFrom,
                        cropYFrom: assetItem.attribute.cropYFrom,
                        cropXTo: assetItem.attribute.cropXTo,
                        cropYTo: assetItem.attribute.cropYTo,
                        filters:assetItem.attribute.filters,
                        imageEffects: assetItem.attribute.imageEffects,
                    });
                    if (assetItem.attribute.renderCaId) {
                        Object.assign(tempAsset.attribute,{
                            renderCaId: assetItem.attribute.renderCaId,
                            renderCaType: assetItem.attribute.renderCaType,
                            renderCa: assetItem.attribute.renderCa,
                        })
                    }
                    // 装饰目标
                    if (assetItem.attribute.decorated) {
                        Object.assign(tempAsset.attribute, {
                            decorated: assetItem.attribute.decorated
                        })
                    }
                    // Object.assign(tempAsset.meta, {
                    //     isFav: assetItem.meta.isFav || false
                    // });
                    if (assetItem.attribute.wordCloudInfo) {
                        Object.assign(tempAsset.attribute, {
                            wordCloudInfo: assetItem.attribute.wordCloudInfo
                        });
                    }
                    if (assetItem.meta.imageEffectBeforInfo) {
                        Object.assign(tempAsset.meta, {
                            imageEffectBeforInfo: {
                                resId: assetItem.meta.imageEffectBeforInfo.resId,
                                width: formatNumber(assetItem.meta.imageEffectBeforInfo.width, 4),
                                height: assetItem.meta.imageEffectBeforInfo.height,
                            }
                        });
                    }
                    if (assetItem.attribute.container && assetItem.attribute.container.id > 0) {
                        Object.assign(tempAsset.attribute, {
                            container: {
                                id: assetItem.attribute.container.id,
                                width: formatNumber(assetItem.attribute.container.width, 4),
                                height: formatNumber(assetItem.attribute.container.height, 4),
                                source_width: assetItem.attribute.container.source_width,
                                source_height: assetItem.attribute.container.source_height,
                                posX: formatNumber(assetItem.attribute.container.posX, 4),
                                posY: formatNumber(assetItem.attribute.container.posY, 4),
                                viewBoxWidth: formatNumber(assetItem.attribute.container.viewBoxWidth, 4),
                                viewBoxHeight: formatNumber(assetItem.attribute.container.viewBoxHeight, 4),
                                viewBoxWidthBack: formatNumber(assetItem.attribute.container.viewBoxWidthBack, 4),
                                viewBoxHeightBack: formatNumber(assetItem.attribute.container.viewBoxHeightBack, 4),
                                markedReplace: assetItem.attribute.container.markedReplace
                            },
                            renderCaId: undefined,
                            renderCaType: undefined,
                            renderCa: undefined,
                            // filters: undefined,
                            // imageEffects: undefined
                        });
                    }
                } else if (assetItem.meta.type === 'background') {
                    Object.assign(tempAsset.attribute, {
                        resId: assetItem.attribute.resId,
                        width: formatNumber(assetItem.attribute.width, 4),
                        height: formatNumber(assetItem.attribute.height, 4),
                        cropXFrom: assetItem.attribute.cropXFrom,
                        cropYFrom: assetItem.attribute.cropYFrom,
                        cropXTo: assetItem.attribute.cropXTo,
                        cropYTo: assetItem.attribute.cropYTo,
                        filters:assetItem.attribute.filters,
                        imageEffects: assetItem.attribute.imageEffects,
                    });
                    if (assetItem.attribute.renderCaId) {
                        Object.assign(tempAsset.attribute,{
                            renderCaId: assetItem.attribute.renderCaId,
                            renderCaType: assetItem.attribute.renderCaType,
                            renderCa: assetItem.attribute.renderCa,
                        })
                    }
                    // Object.assign(tempAsset.meta, {
                    //     isFav: assetItem.meta.isFav || false
                    // });
                    if (assetItem.attribute.container && assetItem.attribute.container.id > 0) {
                        Object.assign(tempAsset.attribute, {
                            container: {
                                id: assetItem.attribute.container.id,
                                width: formatNumber(assetItem.attribute.container.width, 4),
                                height: formatNumber(assetItem.attribute.container.height, 4),
                                source_width: assetItem.attribute.container.source_width,
                                source_height: assetItem.attribute.container.source_height,
                                posX: formatNumber(assetItem.attribute.container.posX, 4),
                                posY: formatNumber(assetItem.attribute.container.posY, 4),
                                viewBoxWidth: formatNumber(assetItem.attribute.container.viewBoxWidth, 4),
                                viewBoxHeight: formatNumber(assetItem.attribute.container.viewBoxHeight, 4),
                                viewBoxWidthBack: formatNumber(assetItem.attribute.container.viewBoxWidthBack, 4),
                                viewBoxHeightBack: formatNumber(assetItem.attribute.container.viewBoxHeightBack, 4),
                                markedReplace: assetItem.attribute.container.markedReplace
                            },
                            renderCaId: undefined,
                            renderCaType: undefined,
                            renderCa: undefined,
                            // filters: undefined,
                            // imageEffects: undefined
                        });
                    }
                } else if (assetItem.meta.type === 'pic') {
                    Object.assign(tempAsset.attribute, {
                        resId: assetItem.attribute.resId,
                        width: formatNumber(assetItem.attribute.width, 4),
                        height: formatNumber(assetItem.attribute.height, 4),
                        cropXFrom: assetItem.attribute.cropXFrom,
                        cropYFrom: assetItem.attribute.cropYFrom,
                        cropXTo: assetItem.attribute.cropXTo,
                        cropYTo: assetItem.attribute.cropYTo,
                        filters:assetItem.attribute.filters,
                        imageEffects: assetItem.attribute.imageEffects,
                    });
                    if (assetItem.attribute.renderCaId) {
                        Object.assign(tempAsset.attribute,{
                            renderCaId: assetItem.attribute.renderCaId,
                            renderCaType: assetItem.attribute.renderCaType,
                            renderCa: assetItem.attribute.renderCa,
                        })
                    }
                    // Object.assign(tempAsset.meta, {
                    //     isFav: assetItem.meta.isFav || false
                    // });
                    if (assetItem.attribute.container && assetItem.attribute.container.id > 0) {
                        Object.assign(tempAsset.attribute, {
                            container: {
                                id: assetItem.attribute.container.id,
                                width: formatNumber(assetItem.attribute.container.width, 4),
                                height: formatNumber(assetItem.attribute.container.height, 4),
                                source_width: assetItem.attribute.container.source_width,
                                source_height: assetItem.attribute.container.source_height,
                                posX: formatNumber(assetItem.attribute.container.posX, 4),
                                posY: formatNumber(assetItem.attribute.container.posY, 4),
                                viewBoxWidth: formatNumber(assetItem.attribute.container.viewBoxWidth, 4),
                                viewBoxHeight: formatNumber(assetItem.attribute.container.viewBoxHeight, 4),
                                viewBoxWidthBack: formatNumber(assetItem.attribute.container.viewBoxWidthBack, 4),
                                viewBoxHeightBack: formatNumber(assetItem.attribute.container.viewBoxHeightBack, 4),
                                markedReplace: assetItem.attribute.container.markedReplace
                            },
                            renderCaId: undefined,
                            renderCaType: undefined,
                            renderCa: undefined,
                            // filters: undefined,
                            // imageEffects: undefined
                        });
                    }
                } else if (assetItem.meta.type === 'container') {
                    Object.assign(tempAsset.attribute, {
                        resId: assetItem.attribute.resId,
                        width: formatNumber(assetItem.attribute.width, 4),
                        height: formatNumber(assetItem.attribute.height, 4),
                        contentInfo: [
                            {
                                resId: assetItem.attribute.contentInfo[0].resId,
                                width: formatNumber(assetItem.attribute.contentInfo[0].width, 4),
                                height: formatNumber(assetItem.attribute.contentInfo[0].height, 4),
                                posX: formatNumber(assetItem.attribute.contentInfo[0].posX, 4),
                                posY: formatNumber(assetItem.attribute.contentInfo[0].posY, 4),
                                viewBoxWidth: formatNumber(assetItem.attribute.contentInfo[0].viewBoxWidth, 4),
                                viewBoxHeight: formatNumber(assetItem.attribute.contentInfo[0].viewBoxHeight, 4)
                            }
                        ],
                    })
                } else if (assetItem.meta.type === 'text') {
                    let regStr = /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u233C-\u3299]\uFE0F\u200D|[\u233C-\u3299]\uFE0F|[\u2322-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/ig;
                    if (assetItem.meta.v === 3) {
                        try {
                            let s = '';
                            assetItem.attribute.text.forEach((item, index) => {
                                s += item.text;
                            })
                            s = s.trim();
                            s = s.replace('\n', '')
                            if (s.length === 0) { 
                                continue;
                            }
                        } catch (error) {
                            console.error('check rich text error: ',error)
                        }
                    }
                    assetItem.attribute.text.map((v, i) => {
                        if (assetItem.meta.v === 3 && typeof v !== 'string') {
                            // letterSpacing与charSpacing同步
                            v.letterSpacing = assetItem.attribute.letterSpacing;
                            assetItem.attribute.text[i].text = unescape(escape(v.text).replace(/\%uD.{3}/ig, ''));
                            // 去掉超办不合理的数据
                            // if (v.beginParentTag) delete assetItem.attribute.text[i].beginParentTag;
                            // if (v.closeParentTag) delete assetItem.attribute.text[i].closeParentTag;
                            if (v.lineHeight) delete assetItem.attribute.text[i].lineHeight;
                            // 规避不合理的数据
                            v.opacity = checkNaN(v.opacity,100)
                            v.opacity = Math.min(v.opacity,100);
                            v.opacity = Math.max(v.opacity,0)
                            v.textDecoration = null
                            // if(v.lineThrough){
                            //     assetItem.attribute.textDecoration = 'line-through'
                            // }else if(v.underline){
                            //     assetItem.attribute.textDecoration = 'underline'
                            // }else{
                            //     assetItem.attribute.textDecoration = 'none'
                            // }
                            // assetItem.attribute.text[i] = v.replace(regStr,'');//转换表情包
                        } else {
                            assetItem.attribute.text[i] = unescape(escape(v).replace(/\%uD.{3}/ig, ''));//转换表情包
                        }
                    })
                    Object.assign(tempAsset.attribute, {
                        width: formatNumber(assetItem.attribute.width, 4),
                        height: formatNumber(assetItem.attribute.height, 4),
                        text: cloneDeep(assetItem.attribute.text),
                        fontSize: formatNumber(assetItem.attribute.fontSize, 4),
                        fontWeight: assetItem.attribute.fontWeight,
                        textAlign: assetItem.attribute.textAlign,
                        lineHeight: assetItem.attribute.lineHeight,
                        letterSpacing: formatNumber(assetItem.attribute.letterSpacing, 4),
                        fontFamily: assetItem.attribute.fontFamily,
                        fontStyle: assetItem.attribute.fontStyle,
                        textDecoration: assetItem.attribute.textDecoration,
                        writingMode: assetItem.attribute.writingMode || 'horizontal-tb',
                        emphaticMark: cloneDeep(assetItem.attribute.emphaticMark),
                        emphaticMarkRenderBlocks: cloneDeep(assetItem.attribute.emphaticMarkRenderBlocks),
                        emphaticMarkBlocks: cloneDeep(assetItem.attribute.emphaticMarkBlocks),
                        originalText: assetItem.attribute.originalText,
                        color: {
                            r: assetItem.attribute.color.r || 0,
                            g: assetItem.attribute.color.g || 0,
                            b: assetItem.attribute.color.b || 0,
                            a: assetItem.attribute.color.a ?? 1
                        },
                        backgroundColor: {
                            r: assetItem.attribute.backgroundColor?.r || 255,
                            g: assetItem.attribute.backgroundColor?.g || 255,
                            b: assetItem.attribute.backgroundColor?.b || 255,
                            a: assetItem.attribute.backgroundColor?.a || 0
                        },
                        effect: assetItem.attribute.effect,
                        effectVariant3D: assetItem.attribute.effectVariant3D,
                        rt_renderImage: assetItem.attribute.rt_renderImage,
                    })
                    
                    if(tempAsset.attribute.emphaticMark){
                        delete tempAsset.attribute.emphaticMark.svgContent
                    }
                    if(tempAsset.attribute.emphaticMarkRenderBlocks){
                        tempAsset.attribute.emphaticMarkRenderBlocks.forEach((lineBlocks)=>{
                            lineBlocks?.forEach((block)=>{
                                delete block.svgContent
                            })
                        })
                    }
                    if(tempAsset.attribute.emphaticMarkBlocks){
                        tempAsset.attribute.emphaticMarkBlocks.forEach((block)=>{
                            delete block?.emphaticMark?.svgContent
                        })
                    }
                    if (assetItem.attribute.boxEffect?.length) {
                        Object.assign(tempAsset.attribute, {
                            boxEffect: assetItem.attribute.boxEffect
                        });
                    }

                    if (assetItem.attribute.textBack != undefined) {
                        Object.assign(tempAsset.attribute, {
                            textBack: assetItem.attribute.textBack
                        });
                    }
                    if (assetItem.attribute.effectVariant) {
                        Object.assign(tempAsset.attribute, {
                            effectVariant: {
                                layers: formatLayers(assetItem.attribute.effectVariant.layers),
                                variableColorPara: assetItem.attribute.effectVariant.variableColorPara,
                                variableSizePara: formatVariableSizePara(assetItem.attribute.effectVariant.variableSizePara),
                            },
                        });
                    }
                    if (assetItem.meta.v === 3) {
                        // 格式化统一富文本的属性
                        formatRichText(tempAsset.attribute);
                    }


                    Object.assign(tempAsset.meta, {
                        isTitle: assetItem.meta.isTitle,
                        addFrom: assetItem.meta.addFrom,
                    })
                    let writingMode = assetItem.attribute.writingMode || 'horizontal-tb';
                    let textDom = document.getElementsByClassName(assetItem.meta.className)[0];
                    if(textDom){
                        if(writingMode == 'horizontal-tb'){
                            Object.assign(tempAsset.attribute, {
                                height: Math.round(textDom.offsetHeight / doc.canvas.scale)
                            })
                            if (assetItem.attribute.effectVariant3D && assetItem.attribute.effectVariant3D.resId > 0) {
                                Object.assign(tempAsset.attribute, {
                                    width: Math.round(textDom.offsetWidth / doc.canvas.scale)
                                })
                            }
                        }else{
                            Object.assign(tempAsset.attribute, {
                                width: Math.round(textDom.offsetWidth / doc.canvas.scale)
                            })
                        }
                    }
                } else if (assetItem.meta.type === 'group') {
                    Object.assign(tempAsset.attribute, {
                        width: formatNumber(assetItem.attribute.width, 4),
                        height: formatNumber(assetItem.attribute.height, 4),
                    });
                    Object.assign(tempAsset.meta, {
                        groupWordID: assetItem.meta.groupWordID,
                        // memberClassNames:assetItem.meta.memberClassNames
                    });
                } else if (assetItem.meta.type === 'table') {
                    /* 表格格式处理 START */
                    Object.assign(tempAsset.attribute, {
                        width: formatNumber(assetItem.attribute.width, 4),
                        height: formatNumber(assetItem.attribute.height, 4),
                        cell: formatTableCellMergeInfo(assetItem.attribute.cell),
                        cellSize: assetItem.attribute.cellSize,
                        text: assetItem.attribute.text,
                        resId:assetItem.attribute.resId
                    });
                    assetItem.meta.v = assetItem.meta.v ?? 2;
                    /* 表格格式处理 END */
                } else if (assetItem.meta.type === 'chart') {
                    /* 图表格式处理 START */
                    Object.assign(tempAsset.attribute, {
                        width: formatNumber(assetItem.attribute.width, 4),
                        height: formatNumber(assetItem.attribute.height, 4),
                        colorTable: assetItem.attribute.colorTable,
                        chartBaseId: assetItem.attribute.chartBaseId,
                        chartDrawInfo: assetItem.attribute.chartDrawInfo,
                        chartBgcolor: assetItem.attribute.chartBgcolor||{r:255,g:255,b:255,a:1},
                        chartRule: assetItem.attribute.chartRule,
                        userData: assetItem.attribute.userData,
                        keyText: assetItem.attribute.keyText || {colorTable: [{r:51,g:51,b:51,a:1}],size: 16,dataText:{color : {r:51,g:51,b:51,a:1},size: 12,showData:true}},
                        coordinateText: assetItem.attribute.coordinateText || {size: 16,color: {r:51,g:51,b:51,a:1}},
                        coordinateGuides: assetItem.attribute.coordinateGuides || {color: {r:51,g:51,b:51,a:1}},
                        legendBoxInfo:assetItem.attribute.legendBoxInfo || {show:true,position:'bottom',fontColor : [{r:51,g:51,b:51,a:1}],fontSize : 12},
                    });
                    Object.assign(tempAsset.meta, {
                        isEqualTransform: assetItem.meta.isEqualTransform ? true : false,
                    });
                    if(assetItem.meta.v === 2) {
                        Object.assign(tempAsset.attribute, {
                            accumulation: assetItem.attribute.accumulation,
                            seriesLabelInfo: assetItem.attribute.seriesLabelInfo,
                            axisInfo: assetItem.attribute.axisInfo,
                            barStyleInfo: assetItem.attribute.barStyleInfo
                        });
                        if(assetItem.attribute.renderCaId) {
                            Object.assign(tempAsset.attribute, {
                                renderCa: assetItem.attribute.renderCa,
                                renderCaId: assetItem.attribute.renderCaId,
                                renderCaType: assetItem.attribute.renderCaType,
                            })
                        }
                        Object.assign(tempAsset.meta, {
                            v: assetItem.meta?.v
                        })
                    }
                    /* 图表格式处理 END */
                } else if (assetItem.meta.type === 'videoE') {
                    Object.assign(tempAsset.attribute, {
                        resId: assetItem.attribute.resId,
                        width: assetItem.attribute.width,
                        height: assetItem.attribute.height,
                        isLoop: assetItem.attribute.isLoop,
                        isUser: assetItem.attribute.isUser,

                        //结束时间（单位ms）
                        rt_total_frame: assetItem.attribute.rt_total_frame,
                        // 视频链接
                        rt_url: assetItem.attribute.rt_url,
                        // 预览图链接
                        rt_preview_url: assetItem.attribute.rt_preview_url,
                        // 单帧位置链接
                        rt_frame_url: assetItem.attribute.rt_frame_url,
                        cst:  assetItem.attribute.cst,
                        cet: assetItem.attribute.cet,
                        volume: assetItem.attribute.volume,
                    });
                    
                    if(assetItem.meta.isBackground){
                        Object.assign(tempAsset.meta, {
                            isBackground: assetItem.meta.isBackground,
                        });
                    }
                    
                }else if (assetItem.meta.type === 'qrcode') {
                    Object.assign(tempAsset.attribute, {
                        resId: assetItem.attribute.resId,
                        width: assetItem.attribute.width,
                        height: assetItem.attribute.height,
                        cropXFrom: assetItem.attribute.cropXFrom,
                        cropXTo: assetItem.attribute.cropXTo,
                        cropYFrom: assetItem.attribute.cropYFrom,
                        cropYTo: assetItem.attribute.cropYTo,
                        qrcodeInfo:assetItem.attribute.qrcodeInfo,
                    });
                    if (assetItem.attribute.renderCaId) {
                        Object.assign(tempAsset.attribute,{
                            renderCaId: assetItem.attribute.renderCaId,
                            renderCaType: assetItem.attribute.renderCaType,
                            renderCa: assetItem.attribute.renderCa,
                        })
                    }
                }else if(assetItem.meta.type === 'frame'){
                    Object.assign(tempAsset.attribute, {
                        resId: assetItem.attribute.resId,
                        width: assetItem.attribute.width,
                        height: assetItem.attribute.height,
                        picUrl: assetItem.attribute.picUrl,
                        SVGUrl:assetItem.attribute.SVGUrl,
                        contentResId:assetItem.attribute.contentResId,
                        assetWidth: assetItem.attribute.assetWidth,
                        assetHeight: assetItem.attribute.assetHeight,                  
                        opacity:assetItem.attribute.opacity,
                        picWidth:assetItem.attribute.picWidth,
                        picHeight:assetItem.attribute.picHeight,
                        transformX:assetItem.attribute.transformX,
                        transformY:assetItem.attribute.transformY,
                        isDefaultImage:assetItem.attribute.isDefaultImage,
                        color:assetItem.attribute.color,
                        defaultPicInfo:assetItem.attribute.defaultPicInfo,
                        frameBackgroundInfo:assetItem.attribute.frameBackgroundInfo,
                        transform:assetItem.attribute.transform
                    });
                } 
                else {
                    return {}
                }
                if(['pic','image','background'].includes(assetItem.meta.type)){
                    if(assetItem.meta.rt_tag_type == 'imgContainer') aiDesignImgContainerNum++;
                    if(assetItem.meta.rt_tag_type == 'mattingImgContainer') aiDesignMattingImgContainerNum++;
                }
                let tempGroup;
                if (assetItem.meta.group != undefined) {
                    tempGroup = assetItem.meta.group;
                } else {
                    tempGroup = assetItem.meta.grounp;
                }

                if (assetItem.attribute.ks) {
                    Object.assign(tempAsset.attribute, {
                        ks: {
                            i: ksFormatter(assetItem.attribute.ks.i),
                            s: ksFormatter(assetItem.attribute.ks.s),
                            o: ksFormatter(assetItem.attribute.ks.o)
                        }
                    });
                }

                if (assetItem.attribute.k) {
                    Object.assign(tempAsset.attribute, {
                        k: {
                            i: assetItem.attribute.k.i || {},
                            s: assetItem.attribute.k.s || {},
                            o: assetItem.attribute.k.o || {}
                        }
                    })
                }

                Object.assign(tempAsset.meta, {
                    locked: assetItem.meta.locked,
                    type: assetItem.meta.type,
                    name: assetItem.meta.name ? assetItem.meta.name : '',
                    group: tempGroup ? tempGroup : '',
                    origin: assetItem.meta.origin,
                    addOrigin: assetItem.meta.addOrigin,
                    hash: assetItem.meta.hash,
                    v: assetItem.meta.v,
                    linkedLineIds: assetItem.meta.linkedLineIds,
                    uniqueId: assetItem.meta.uniqueId,
                    replaceForEffectTemplate:assetItem.meta.replaceForEffectTemplate ?? ''
                });

                Object.assign(tempAsset.transform, {
                    posX: formatNumber(assetItem.transform.posX, 4),
                    posY: formatNumber(assetItem.transform.posY, 4),
                    rotate: assetItem.transform.rotate,
                    alpha: assetItem.attribute.opacity,
                    zindex: assetItem.meta.index,
                    horizontalFlip: assetItem.transform.horizontalFlip,
                    verticalFlip: assetItem.transform.verticalFlip,
                })
                format == 'ppt' && saveFormatPpt(tempAsset);
                tempPages.push(tempAsset)
                
            }
            tempDoc.work.push(tempPages)
        }
        if( doc.pageAttr ){
            Object.assign(pageAttr, {backgroundImage: JSON.parse(JSON.stringify(doc.pageAttr.backgroundImage))});
            if (doc.pageAttr.pageInfo && Array.isArray(doc.pageAttr.pageInfo)) {
                pageAttr.pageInfo = doc.pageAttr.pageInfo.map(item => {
                    return {
                        title: item?.title || '',
                        pageTime: item?.pageTime || 5000,
                        k: {
                            i: item?.k?.i?.resId ? { resId: item.k.i.resId } : {},
                            s: item?.k?.s?.resId ? { resId: item.k.s.resId } : {},
                            o: item?.k?.o?.resId ? { resId: item.k.o.resId } : {},
                        },
                        rt_page_ppt_mark: item?.rt_page_ppt_mark,
                        type: item?.type || '',
                    }
                })
            }
            if (Array.isArray(doc.pageAttr.sound)) {
                pageAttr.sound = JSON.parse(JSON.stringify(doc.pageAttr.sound))
            }
            if (doc.pageAttr.pageHash) {
                pageAttr.pageHash = doc.pageAttr.pageHash;
            }
        }

        tempDoc.pageAttr = pageAttr;

        tempDoc.pageAttr.backgroundImage.map(item=>{
            if( item ){
                for (let temp of Object.keys(item)) {
                    if(temp.indexOf('rt_') == 0){
                        delete item[temp];
                    }
                }
            }
        });
        tempDoc['aiDesignImgContainerNum'] = aiDesignImgContainerNum;
        tempDoc['aiDesignMattingImgContainerNum'] = aiDesignMattingImgContainerNum;
        return tempDoc;
    }

    /**
     * 存储格式到可用格式的装换
     */
    getFormat(doc, props = {}) {
        let tempDoc = {
            canvas: {},
            work: {
                meta: {},
                nameSalt: {
                    salt: 0
                },
                pages: [
                    {
                        assets: [],
                    }
                ]
            },
            pageAttr: {
                backgroundColor: [],
                backgroundImage: [
                    {
                        resId: ''
                    }
                ]
            }
        }
        /* 提交组合字 */
        let positionTemp = {};
        if(window.location.href.indexOf('groupWordId=') > -1){
            (doc.canvas.positionHeight || doc.canvas.positionHeight == 0) ? positionTemp['_height'] = doc.canvas.positionHeight : '';
            (doc.canvas.positionWidth || doc.canvas.positionWidth == 0) ? positionTemp['_width'] = doc.canvas.positionWidth : '';
            (doc.canvas.positionX || doc.canvas.positionX == 0) ? positionTemp['positionX'] = doc.canvas.positionX : '';
            (doc.canvas.positionY || doc.canvas.positionY == 0) ? positionTemp['positionY'] = doc.canvas.positionY : '';
        }
        /*****************/
        Object.assign(tempDoc.canvas, {
            width: doc.canvas.width,
            height: doc.canvas.height,
            scale: 1,
            backgroundColor: doc.canvas.backgroundColor,
            showUnit: doc.canvas.showUnit || 'px'
        },positionTemp);

        if(doc.canvas.floorCutting){
            tempDoc.canvas.floorCutting = doc.canvas.floorCutting;
            if (Array.isArray(doc.work)) {
                doc.work = doc.work.filter(page => page?.length > 0);
            }
        }
        Object.assign(tempDoc.work.meta, {
            title: doc.canvas.title
        })
        let tempPages = [],
            tempAssets = []

        
        const fontFamilyNameList = getFontNameValueList()

        for (let key in doc.work) {
            let tempPage = doc.work[key]
            tempAssets = {
                assets: []
            }
            if (doc.pageAttr) {
                if (doc.pageAttr.backgroundColor[key]) {
                    Object.assign(tempAssets, {
                        backgroundColor: doc.pageAttr.backgroundColor[key],
                    });
                }
                if (doc.pageAttr.backgroundOpacity) {
                    Object.assign(tempAssets, {
                        isOpacityBg: doc.pageAttr.backgroundOpacity[key] || false,
                    });
                }
            } else {
                Object.assign(tempAssets, {
                    backgroundColor: {
                        r: 255,
                        g: 255,
                        b: 255,
                        a: 1
                    },
                    isOpacityBg:false
                });
            }
            const isBoard = doc.pageAttr?.pageInfo?.[key]?.type === 'board';
           let groupNameSet = new Set(),
            groupAssetMap = new Map()
            for (let ke in tempPage) {
                let tempAsset = tempPage[ke],
                    assetItem = {
                        meta: {},
                        transform: {},
                        attribute: {}
                    }
                if(!tempAsset.transform){
                    continue
                }
                if (typeof tempAsset.transform.posX === 'string') {
                    tempAsset.transform.posX = Number(tempAsset.transform.posX)
                }
                if (typeof tempAsset.transform.posY === 'string') {
                    tempAsset.transform.posY = Number(tempAsset.transform.posY)
                }
                if (tempAsset.meta.type === 'group') {
                    tempAsset.meta.type = 'group';
                    tempAsset.meta.group && groupNameSet.add(tempAsset.meta.group);
                }
                let tempGroup;
                if (tempAsset.meta.group != undefined) {
                    tempGroup = tempAsset.meta.group;
                } else {
                    tempGroup = tempAsset.meta.grounp;
                }
                let size = {x: tempAsset.transform.posX, y: tempAsset.transform.posY, w: tempAsset.attribute.width, h: tempAsset.attribute.height};
                if (tempAsset.transform.rotate !== 0) {
                    size = calcRotateRect(size, tempAsset.transform.rotate);
                }
                if (!isBoard) {
                    if (Number(size.x) > tempDoc.canvas.width || Number(size.y)  > tempDoc.canvas.height || Number(size.x)  + Number(size.w) < 0 || Number(size.y)  + Number(size.h)  < 0) {
                        continue;
                    }
                }
                  /* 过滤ppt模板中非法数据start */
                if(
                    tempAsset.meta.type!== 'text' && (tempAsset.attribute.width == 0 || tempAsset.attribute.height == 0 || 
                    !isFinite(tempAsset.attribute.height) || !isFinite(tempAsset.attribute.width))
                ){
                    continue
                }
                if(tempAsset.attribute.container && tempAsset.attribute.container.id && ( !tempAsset.attribute.container.width || !tempAsset.attribute.container.height || !isFinite(tempAsset.attribute.container.height) || !isFinite(tempAsset.attribute.container.width))){
                    continue
                }
                /* 过滤ppt模板中非法数据end */
                Object.assign(assetItem.meta, {
                    locked: tempAsset.meta.locked,
                    index: tempAsset.transform.zindex,
                    type: tempAsset.meta.type,
                    className: tempAsset.meta.type + ++tempDoc.work.nameSalt.salt,
                    name: tempAsset.meta.name,
                    group: tempGroup ? tempGroup : '',
                    origin: tempAsset.meta.origin,
                    addOrigin: tempAsset.meta.addOrigin,
                    hash: tempAsset.meta.hash,
                    linkedLineIds: tempAsset.meta.linkedLineIds,
                    uniqueId: tempAsset.meta.uniqueId,
                    rt_page_index: Number(key),
                    rt_page_assets_index: Number(ke),
                    rt_tag_type: tempAsset.meta.rt_tag_type,
                    rt_color_tag_type: tempAsset.meta.rt_color_tag_type,
                    v: tempAsset.meta.v,
                    deleted:tempAsset.meta.deleted ?? 0 // 图片的删除状态，0 未删除，1：删除可恢复 2 物理删除
                })
                Object.assign(assetItem.transform, {
                    posX: tempAsset.transform.posX ?? 0 ,
                    posY: tempAsset.transform.posY ?? 0 ,
                    rotate: formatNumber(tempAsset.transform.rotate ?? 0, 2),
                    horizontalFlip: tempAsset.transform.horizontalFlip,
                    verticalFlip: tempAsset.transform.verticalFlip
                })
                Object.assign(assetItem.attribute, {
                    opacity: tempAsset.transform.alpha,
                })
                const { info } = storeAdapter.getStore({
                    store_name:storeAdapter.store_names.InfoManage
                })
                const isPpt = info?.template_type == 3
                // 过滤ppt模板中非法数据
                if((tempAsset.attribute.width == 0 || tempAsset.attribute.height == 0 )){
                     continue
                }
                if (tempAsset.meta.type === 'text') {           
                    // 兼容ppt格式的文字数据
                    // lyy
                    if (tempAsset.meta.v === 2) {
                        assetItem.attribute.opacity = Math.min(assetItem.attribute.opacity, 100);
                        if (!tempAsset.attribute.fontSize) {
                            const firstText = tempAsset.attribute.text[0];
                            if (firstText) {
                                tempAsset.attribute.fontSize = firstText.fontSize;
                                tempAsset.attribute.fontFamily = firstText.fontFamily;
                                tempAsset.attribute.color = firstText.color;
                                tempAsset.attribute.letterSpacing = firstText.letterSpacing || 0;
                                tempAsset.attribute.fontStyle = firstText.fontStyle || 'normal';
                                tempAsset.attribute.fontWeight = firstText.fontWeight || 'normal';
                            }
                        }
                         // ppt文字透明度不能按富文本单独设置，只能统一一个
                         tempAsset.attribute.text.forEach((textItem)=>{
                            if(textItem.opacity && textItem.opacity != assetItem.attribute.opacity ){
                                textItem.opacity = assetItem.attribute.opacity;
                            }
                        })
                        tempAsset.meta.v = 3
                        if (tempAsset.attribute.effect && tempAsset.attribute.effectVariant) {
                            let plaintext = ''
                            tempAsset.attribute.text.forEach((item)=>{
                                if(!item || !item.text) return
                                plaintext +=item.text
                                if(item.closeParentTag == 'li'){
                                    plaintext += '\n'
                                }
                            })
                            tempAsset.attribute.text = [plaintext];
                            delete tempAsset.meta.v;
                        }
                    } 
                    let regStr = /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u233C-\u3299]\uFE0F\u200D|[\u233C-\u3299]\uFE0F|[\u2322-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/ig;
                    let metaV = tempAsset.meta.v;
                    if(tempAsset.meta.v === 3 && !openRichText) {
                        const textList = tempAsset.attribute.text.map(item=> item.text);
                        if(textList?.length){
                            tempAsset.attribute.text = textList.join('').split('\n');
                        }
                        v = undefined;
                    }
                    
                    if (metaV === 3 && tempAsset.attribute.text?.length && typeof tempAsset.attribute.text[0] === 'string') {
                        metaV = undefined;
                    }
                    if (tempAsset.meta.v === 3) {
                        try {
                            let s = '';
                            tempAsset.attribute.text.forEach((item, index) => {
                                s += item.text;
                            })
                            s = s.trim();
                            s = s.replace('\n', '')
                            if (s.length === 0) { 
                                continue;
                            }
                        } catch (error) {
                            console.error('check rich text error: ',error)
                        }
                    }

                    tempAsset.attribute.text.map((v,i)=>{
                        if (tempAsset.meta.v === 3 && typeof v !== 'string') {
                            tempAsset.attribute.text[i].text = unescape(escape(v.text).replace(/\%uD.{3}/ig, ''));
                            const isLast = i == tempAsset.attribute.text.length - 1
                            if(v.closeParentTag && !/\n$/.test(tempAsset.attribute.text[i].text) && !isLast){   
                                tempAsset.attribute.text[i].text+='\n'
                           
                            }
                             // 去掉超办不合理的数据
                             if(v.closeParentTag) delete tempAsset.attribute.text[i].closeParentTag;
                             if (v.beginParentTag)  delete tempAsset.attribute.text[i].beginParentTag
                             if (v.lineHeight) delete tempAsset.attribute.text[i].lineHeight; 
                            // ppt模板文字透明度不能按富文本单独设置，只能统一一个
                             if(isPpt) {
                                assetItem.attribute.opacity = Math.min(assetItem.attribute.opacity, 100)
                                tempAsset.attribute.text[i].opacity = assetItem.attribute.opacity
                             }
                            // 有的作品存在是富文本，但是又存在特效的情况，这里做一下删除
                            if (tempAsset.attribute.effect) {
                                tempAsset.attribute.effect = void 0;
                                tempAsset.attribute.effectVariant = void 0;
                             }
                          
                     
                        } else {
                            tempAsset.attribute.text[i] = unescape(escape(v).replace(/\%uD.{3}/ig, ''));//转换表情包

                        }
                    })
                    if (!tempAsset.attribute.fontSize) {
                        // 删除没有 fontSize 的文字
                        continue;
                    }
                    // 有模板存在丢字体的情况
                    if(!tempAsset.attribute.fontFamily ){
                        tempAsset.attribute.fontFamily = 'fnsystSCHeavy'
                    }
                    // 有模板存在已下架字体，替换成思源黑体
                    if(!fontFamilyNameList.hasOwnProperty(tempAsset.attribute.fontFamily)){
                        tempAsset.attribute.fontFamily = 'fnsyhtRegular'
                    }
                    if (tempAsset.meta.v === 3) {
                        // 格式化统一富文本的属性
                        formatRichText(tempAsset.attribute);
                    } else {
                        // 删除长度为 0 的空文字，存在用户弄出错误数据，导致浪费 webgl 渲染上下文
                        const string = tempAsset.attribute.text.join('');
                        if (string.length === 0) {
                            continue;
                        }
                    }
                    const fontSize = formatNumber(tempAsset.attribute.fontSize, 0);
                    let backgroundColor = tempAsset.attribute.backgroundColor || { r: 255, g: 255, b: 255, a: 0}
                    if(!tempAsset.meta.v){
                        backgroundColor = { r: 255, g: 255, b: 255, a: 0}
                    }
                    Object.assign(assetItem.attribute, {
                        color: {
                            r: tempAsset.attribute.color?.r || 0,
                            b: tempAsset.attribute.color?.b || 0,
                            g: tempAsset.attribute.color?.g || 0,
                            a: tempAsset.attribute.color?.a ?? 1
                        },
                        fontFamily: tempAsset.attribute.fontFamily,
                        fontSize: fontSize,
                        fontWeight: tempAsset.attribute.fontWeight,
                        letterSpacing: formatNumber(tempAsset.attribute.letterSpacing, 4),
                        lineHeight: tempAsset.attribute.lineHeight,
                        text: tempAsset.attribute.text,
                        textBack: tempAsset.attribute.textBack,
                        textAlign: tempAsset.attribute.textAlign,
                        width: checkNaN(tempAsset.attribute.width, fontSize),
                        height: checkNaN(tempAsset.attribute.height, fontSize),
                        emphaticMark: tempAsset.attribute.emphaticMark,
                        // emphaticMarkRenderBlocks: tempAsset.attribute.emphaticMarkRenderBlocks,
                        emphaticMarkBlocks: tempAsset.attribute.emphaticMarkBlocks,
                        textDecoration: tempAsset.attribute.textDecoration,
                        fontStyle: tempAsset.attribute.fontStyle,
                        effect: tempAsset.attribute.effect,
                        effectVariant: tempAsset.attribute.effectVariant,
                        writingMode: tempAsset.attribute.writingMode || 'horizontal-tb',
                        effectVariant3D: tempAsset.attribute.effectVariant3D,
                        backgroundColor: backgroundColor,
                        originalText: tempAsset.attribute.originalText,
                    });
                    // 设计师编辑器不重置宽度
                    if( props.picId > 0 && !props.isDesigner ){
                        // 文本宽度适应性缩放 START
                        let maxTextNum = 0;
                        tempAsset.attribute.text.map((item) => {
                            if(tempAsset.meta.v === 3 && typeof item !== 'string'){
                                const brIndex = item.text.indexOf('\n');
                                if(brIndex > 0){
                                    const array = item.text.split(brIndex);
                                    maxTextNum += array[0];
                                    array.map(item=> maxTextNum=item.length > maxTextNum ? item.length: maxTextNum);
                                } else {
                                    maxTextNum += item.text.length;
                                }
                            } else {
                                if( maxTextNum < item.length ){
                                    maxTextNum = item.length
                                }
                            }
                        });
                        let letterSpacing = formatNumber(tempAsset.attribute.letterSpacing, 4),
                            textWidth = tempAsset.attribute.width,
                            fontSize = tempAsset.attribute.fontSize,
                            newWidth = formatNumber((maxTextNum + 2) * (letterSpacing + fontSize), 4);
                        
                        if( newWidth < textWidth && tempAsset.meta.v !== 3){
                            if( tempAsset.attribute.textAlign == "center" ){
                                assetItem.transform.posX += ((textWidth - newWidth) / 2);
                                assetItem.attribute.width = newWidth;
                            }else if( tempAsset.attribute.textAlign == "right" ){
                                assetItem.transform.posX += ((textWidth - newWidth));
                                assetItem.attribute.width = newWidth;
                            }else if( tempAsset.attribute.textAlign == "left" ){
                                assetItem.attribute.width = newWidth;
                            }
                        }
                        // 文本宽度适应性缩放 END
                    }

                    Object.assign(assetItem.meta, {
                        isTitle: tempAsset.meta.isTitle,
                        rt_refresh: 1,
                        v: metaV,
                        addFrom: tempAsset.meta.addFrom
                    })
                    // 元素装饰
                    if (tempAsset.attribute.boxEffect?.length) {
                        Object.assign(assetItem.attribute, {
                            boxEffect: tempAsset.attribute.boxEffect
                        });
                    }
                } else if (tempAsset.meta.type === 'image') {
                    if (
                        /psd_import/.test(tempAsset.attribute.rt_picUrl_2k || tempAsset.attribute.picUrl) &&
                        tempAsset.attribute.width === 0 &&
                        tempAsset.attribute.height === 0
                    ) {
                        continue;
                    }
                    Object.assign(assetItem.attribute, {
                        owner:tempAsset.attribute.rt_owner,
                        assetHeight: tempAsset.attribute.sourceHeight,
                        assetWidth: tempAsset.attribute.sourceWidth,
                        cropXFrom: tempAsset.attribute.cropXFrom ?? 0,
                        cropYFrom: tempAsset.attribute.cropYFrom ?? 0,
                        cropXTo: tempAsset.attribute.cropXTo ?? 1,
                        cropYTo: tempAsset.attribute.cropYTo ?? 1,
                        width: parseInt(tempAsset.attribute.width ?? 10),
                        height: parseInt(tempAsset.attribute.height ?? 10),
                        resId: tempAsset.attribute.resId,
                        picUrl: tempAsset.attribute.rt_picUrl_2k || tempAsset.attribute.picUrl,
                        rt_picUrl: tempAsset.attribute.rt_picUrl,
                    });
                    if (tempAsset.attribute.wordCloudInfo) {
                        Object.assign(assetItem.attribute, {
                            wordCloudInfo: tempAsset.attribute.wordCloudInfo
                        });
                    }
                    if (tempAsset.meta.imageEffectBeforInfo) {
                        Object.assign(assetItem.meta, {
                            imageEffectBeforInfo: tempAsset.meta.imageEffectBeforInfo
                        });
                    }
                    Object.assign(assetItem.meta, {
                        isFav: tempAsset.meta.isFav || false,
                        replaceForEffectTemplate:tempAsset.meta.replaceForEffectTemplate ?? ''
                    });
                    if (tempAsset.attribute.container && tempAsset.attribute.container.id > 0) {
                        Object.assign(assetItem.attribute, {
                            container: {
                                id: tempAsset.attribute.container.id,
                                width: tempAsset.attribute.container.width,
                                height: tempAsset.attribute.container.height,
                                source_width: tempAsset.attribute.container.source_width,
                                source_height: tempAsset.attribute.container.source_height,
                                posX: tempAsset.attribute.container.posX,
                                posY: tempAsset.attribute.container.posY,
                                viewBoxWidth: tempAsset.attribute.container.viewBoxWidth,
                                viewBoxHeight: tempAsset.attribute.container.viewBoxHeight,
                                viewBoxWidthBack: tempAsset.attribute.container.viewBoxWidthBack,
                                viewBoxHeightBack: tempAsset.attribute.container.viewBoxHeightBack,
                                source_key: tempAsset.attribute.container.source_key,
                                markedReplace:tempAsset.attribute.container.markedReplace,
                                picUrl: 'loading',
                            },
                        });
                    }
                } else if (tempAsset.meta.type === 'SVG') {
                    let svgColor = tempAsset.attribute.colors;
                    if (tempAsset.meta.v === 2 && tempAsset.attribute.colors['gColor_0']) {
                        
                        if (tempAsset.attribute.colors['gColor_0']['fillColor']) {
                            svgColor = tempAsset.attribute.colors['gColor_0']['fillColor'];
                            if(svgColor) {
                                const fillColorValue = tempAsset.attribute.colors['gColor_0']['tempColor'] ||  Object.values(svgColor)[0]
                                svgColor.fillColor = fillColorValue
                            }
                        } 
                        if (tempAsset.attribute.colors['gColor_0']['strokeColor'] && Object.keys(tempAsset.attribute.colors['gColor_0']['strokeColor'])?.length) {
                            svgColor = tempAsset.attribute.colors['gColor_0']['strokeColor']
                        }
                    }
                    // 处理超办ppt数据
                    if (tempAsset.attribute.textAttr) {
                        const textAttr = tempAsset.attribute.textAttr;
                        textAttr?.text?.forEach((v,i)=>{
                            tempAsset.attribute.textAttr.text[i].text = unescape(escape(v.text).replace(/\%uD.{3}/ig, ''));
                            const isLast = i == tempAsset.attribute.textAttr.text.length - 1
                            if(v.closeParentTag && !/\n$/.test(tempAsset.attribute.textAttr.text[i].text) && !isLast){   
                                tempAsset.attribute.textAttr.text[i].text+='\n'
                            }
                             // 去掉超办不合理的数据
                             if(v.closeParentTag) delete tempAsset.attribute.textAttr.text[i].closeParentTag;
                             if (v.beginParentTag)  delete tempAsset.attribute.textAttr.text[i].beginParentTag
                             if (v.lineHeight) delete tempAsset.attribute.textAttr.text[i].lineHeight; 
                        })
                    }


                    Object.assign(assetItem.attribute, {
                        assetHeight: tempAsset.attribute.sourceHeight,
                        assetWidth: tempAsset.attribute.sourceWidth,
                        cropXFrom: tempAsset.attribute.cropXFrom ?? 0,
                        cropYFrom: tempAsset.attribute.cropYFrom ?? 0,
                        cropXTo: tempAsset.attribute.cropXTo ?? 1,
                        cropYTo: tempAsset.attribute.cropYTo ?? 1,
                        width: parseInt(tempAsset.attribute.width),
                        height: parseInt(tempAsset.attribute.height),
                        resId: tempAsset.attribute.resId,
                        // picUrl: tempAsset.attribute.picUrl,
                        picUrl: 'loading',
                        source_key: tempAsset.attribute.source_key,
                        colors: svgColor,
                        SVGUrl: tempAsset.attribute.SVGUrl,
                        stroke: tempAsset.attribute.stroke,
                        textAttr: tempAsset.attribute.textAttr,
                        // viewBox: tempAsset.attribute.viewBox,
                    })
                    // 元素装饰
                    if (tempAsset.attribute.decorated) {
                        Object.assign(assetItem.attribute, {
                            decorated: tempAsset.attribute.decorated
                        });
                    }
                } else if (tempAsset.meta.type === 'flow') {
                    Object.assign(assetItem.attribute, {
                        assetHeight: tempAsset.attribute.sourceHeight,
                        assetWidth: tempAsset.attribute.sourceWidth,
                        cropXFrom: tempAsset.attribute.cropXFrom ?? 0,
                        cropYFrom: tempAsset.attribute.cropYFrom ?? 0,
                        cropXTo: tempAsset.attribute.cropXTo ?? 1,
                        cropYTo: tempAsset.attribute.cropYTo ?? 1,
                        width: parseInt(tempAsset.attribute.width),
                        height: parseInt(tempAsset.attribute.height),
                        resId: tempAsset.attribute.resId,
                        // picUrl: tempAsset.attribute.picUrl,
                        picUrl: 'loading',
                        source_key: tempAsset.attribute.source_key,
                        colors: tempAsset.attribute.colors,
                        SVGUrl: tempAsset.attribute.SVGUrl,
                        stroke: tempAsset.attribute.stroke,

                    })
                } else if(tempAsset.meta.type === 'line') {
                    Object.assign(assetItem.attribute, {
                        width:  formatNumber(tempAsset.attribute.width, 4),
                        height: formatNumber(tempAsset.attribute.height, 4),
                        opacity: tempAsset.attribute. opacity,
                        color: tempAsset.attribute.color,
                        type: tempAsset.attribute. type,
                        lineWidth: tempAsset.attribute.lineWidth,
                        startObj: tempAsset.attribute.startObj,
                        endObj: tempAsset.attribute.endObj,
                        startArrow: tempAsset.attribute.startArrow,
                        endArrow: tempAsset.attribute.endArrow,
                        start: tempAsset.attribute.start,
                        end: tempAsset.attribute.end,
                        points: tempAsset.attribute.points,
                    })
                } else if (tempAsset.meta.type === 'background') {
                    if (tempAsset.attribute.container && tempAsset.attribute.container.id > 0 && isPpt) {
                        Object.assign(assetItem.meta,{
                            type: 'image'
                        })
                    }
                    Object.assign(assetItem.attribute, {
                        assetHeight: tempAsset.attribute.sourceHeight,
                        assetWidth: tempAsset.attribute.sourceWidth,
                        cropXFrom: tempAsset.attribute.cropXFrom ?? 0,
                        cropYFrom: tempAsset.attribute.cropYFrom ?? 0,
                        cropXTo: tempAsset.attribute.cropXTo ?? 1,
                        cropYTo: tempAsset.attribute.cropYTo ?? 1,
                        width: parseInt(tempAsset.attribute.width),
                        height: parseInt(tempAsset.attribute.height),
                        resId: tempAsset.attribute.resId,
                        picUrl: tempAsset.attribute.rt_picUrl_2k || tempAsset.attribute.picUrl,
                    });
                    Object.assign(assetItem.meta, {
                        isFav: tempAsset.meta.isFav || false,
                        replaceForEffectTemplate:tempAsset.meta.replaceForEffectTemplate ?? ''
                    });
                    if (tempAsset.attribute.container && tempAsset.attribute.container.id > 0) {
                        Object.assign(assetItem.attribute, {
                            container: {
                                id: tempAsset.attribute.container.id,
                                width: tempAsset.attribute.container.width,
                                height: tempAsset.attribute.container.height,
                                source_width: tempAsset.attribute.container.source_width,
                                source_height: tempAsset.attribute.container.source_height,
                                posX: tempAsset.attribute.container.posX,
                                posY: tempAsset.attribute.container.posY,
                                viewBoxWidth: tempAsset.attribute.container.viewBoxWidth,
                                viewBoxHeight: tempAsset.attribute.container.viewBoxHeight,
                                viewBoxWidthBack: tempAsset.attribute.container.viewBoxWidthBack,
                                viewBoxHeightBack: tempAsset.attribute.container.viewBoxHeightBack,
                                source_key: tempAsset.attribute.container.source_key,
                                markedReplace:tempAsset.attribute.container.markedReplace,
                                picUrl: 'loading',
                            },
                        });
                    }
                } else if (tempAsset.meta.type === 'pic') {
                    Object.assign(assetItem.attribute, {
                        owner:tempAsset.attribute.rt_owner,
                        assetHeight: tempAsset.attribute.sourceHeight,
                        assetWidth: tempAsset.attribute.sourceWidth,
                        cropXFrom: tempAsset.attribute.cropXFrom ?? 0,
                        cropYFrom: tempAsset.attribute.cropYFrom ?? 0,
                        cropXTo: tempAsset.attribute.cropXTo ?? 1,
                        cropYTo: tempAsset.attribute.cropYTo ?? 1,
                        width: parseInt(tempAsset.attribute.width),
                        height: parseInt(tempAsset.attribute.height),
                        resId: tempAsset.attribute.resId,
                        picUrl: tempAsset.attribute.rt_picUrl_2k || tempAsset.attribute.picUrl,
                    })
                    Object.assign(assetItem.meta, {
                        isFav: tempAsset.meta.isFav || false,
                        replaceForEffectTemplate:tempAsset.meta.replaceForEffectTemplate ?? ''
                    });
                    if (tempAsset.attribute.container && tempAsset.attribute.container.id > 0) {
                        Object.assign(assetItem.attribute, {
                            container: {
                                id: tempAsset.attribute.container.id,
                                width: tempAsset.attribute.container.width,
                                height: tempAsset.attribute.container.height,
                                source_width: tempAsset.attribute.container.source_width,
                                source_height: tempAsset.attribute.container.source_height,
                                posX: tempAsset.attribute.container.posX,
                                posY: tempAsset.attribute.container.posY,
                                viewBoxWidth: tempAsset.attribute.container.viewBoxWidth,
                                viewBoxHeight: tempAsset.attribute.container.viewBoxHeight,
                                viewBoxWidthBack: tempAsset.attribute.container.viewBoxWidthBack,
                                viewBoxHeightBack: tempAsset.attribute.container.viewBoxHeightBack,
                                source_key: tempAsset.attribute.container.source_key,
                                markedReplace:tempAsset.attribute.container.markedReplace,
                                picUrl: 'loading',
                            },
                        });
                    }
                } else if (tempAsset.meta.type === 'container') {
                    Object.assign(assetItem.attribute, {
                        resId: tempAsset.attribute.resId,
                        width: parseInt(tempAsset.attribute.width),
                        height: parseInt(tempAsset.attribute.height),
                        picUrl: 'loading',
                        contentInfo: tempAsset.attribute.contentInfo,
                        source_key: tempAsset.attribute.source_key,
                    })
                } else if (tempAsset.meta.type === 'group') {
                    if (groupAssetMap.get(tempAsset.meta.group)) {
                        continue;
                    }
                    if(!tempAsset.meta.group) {
                        tempPage[ke] = undefined
                    }else{
                        Object.assign(assetItem.attribute, {
                            width: parseInt(tempAsset.attribute.width),
                            height: parseInt(tempAsset.attribute.height),
                        });
                        Object.assign(assetItem.meta, {
                            groupWordID: tempAsset.meta.groupWordID,
                            memberClassNames:  []
                        });
                        tempAsset.meta.group && groupAssetMap.set(tempAsset.meta.group, assetItem)
                    }
                }else if (tempAsset.meta.type === 'table') {
                    /* 表格格式处理 START */
                    Object.assign(assetItem.attribute, {
                        width: parseInt(tempAsset.attribute.width),
                        height: parseInt(tempAsset.attribute.height),
                        cell: formatTableCellMergeInfo(tempAsset.attribute.cell),
                        cellSize: tempAsset.attribute.cellSize,
                        text: tempAsset.attribute.text,
                        resId:tempAsset.attribute.resId,
                        opacity: 100// 表格现在没有调整体透明度功能了，默认给100
                    });
                    /* 表格格式处理 END */
                }else if (tempAsset.meta.type === 'chart') {
                    /* 图表格式处理 START */
                    Object.assign(assetItem.attribute, {
                        width: parseInt(tempAsset.attribute.width),
                        height: parseInt(tempAsset.attribute.height),
                        colorTable: tempAsset.attribute.colorTable,
                        chartBaseId: tempAsset.attribute.chartBaseId,
                        chartDrawInfo: tempAsset.attribute.chartDrawInfo,
                        chartBgcolor: tempAsset.attribute.chartBgcolor||{r:255,g:255,b:255,a:1},
                        chartRule: tempAsset.attribute.chartRule,
                        userData: tempAsset.attribute.userData,
                        keyText: tempAsset.attribute.keyText || {colorTable: [{r:51,g:51,b:51,a:1}],size: 16,dataText:{color : {r:51,g:51,b:51,a:1},size: 12,showData:true}},
                        coordinateText: tempAsset.attribute.coordinateText || {size: 16,color: {r:51,g:51,b:51,a:1}},
                        coordinateGuides: tempAsset.attribute.coordinateGuides || {color: {r:51,g:51,b:51,a:1}},
                        legendBoxInfo:tempAsset.attribute.legendBoxInfo || {show:true,position:'bottom',fontColor : [{r:51,g:51,b:51,a:1}],fontSize : 12},
                    });
                    Object.assign(assetItem.meta, {
                        isEqualTransform: tempAsset.meta.isEqualTransform ? true : false,
                    })
                    if(tempAsset.meta.v === 2) {
                        Object.assign(assetItem.attribute, {
                            accumulation: tempAsset.attribute.accumulation,
                            seriesLabelInfo: tempAsset.attribute.seriesLabelInfo,
                            axisInfo: tempAsset.attribute.axisInfo,
                            barStyleInfo: tempAsset.attribute.barStyleInfo
                        });
                        Object.assign(assetItem.meta, {
                            v: tempAsset.meta?.v
                        });

                        if(tempAsset.attribute.renderCaId) {
                            Object.assign(assetItem.attribute, {
                                renderCaId: tempAsset.attribute.renderCaId,
                                renderCaType: tempAsset.attribute.renderCaType,
                                renderCa: tempAsset.attribute.renderCa,
                            })
                        }
                    }
                    const {info} = storeAdapter.getStore({
                        store_name:storeAdapter.store_names.InfoManage
                    })
                    // ppt模板背景色设为透明
                    if(info?.template_type == '3' && env.hidePptIncompatibleAsset){
                        Object.assign(assetItem.attribute,{
                            chartBgcolor:{ r:255, g:255, b:255, a:0},
                        })
                    }
                    /* 图表格式处理 END */
                } else if (tempAsset.meta.type === 'videoE') {
                    Object.assign(assetItem.attribute, {
                        resId: tempAsset.attribute.resId,
                        width: tempAsset.attribute.width,
                        height: tempAsset.attribute.height,
                        isLoop: tempAsset.attribute.isLoop,
                        // 总帧数量
                        rt_total_frame: tempAsset.attribute.rt_total_frame,
                        // 视频链接
                        rt_url: tempAsset.attribute.rt_url,
                        // 预览图链接
                        rt_preview_url: tempAsset.attribute.rt_preview_url,
                        // 单帧位置链接
                        rt_frame_url: tempAsset.attribute.rt_frame_url,
                        isUser: tempAsset.attribute.isUser,
                        cst:  tempAsset.attribute.cst,
                        cet: tempAsset.attribute.cet,
                        volume: tempAsset.attribute.volume ?? 100,
                    });
                    if(tempAsset.meta.isBackground){
                        Object.assign(assetItem.meta, {
                            isBackground: true,
                        });
                    }
                }else if (tempAsset.meta.type === 'qrcode') {
                    Object.assign(assetItem.attribute, {
                        resId: tempAsset.attribute.resId,
                        width: tempAsset.attribute.width,
                        height: tempAsset.attribute.height,
                        cropXFrom: tempAsset.attribute.cropXFrom,
                        cropXTo: tempAsset.attribute.cropXTo,
                        cropYFrom: tempAsset.attribute.cropYFrom,
                        cropYTo: tempAsset.attribute.cropYTo,
                        qrcodeInfo:tempAsset.attribute.qrcodeInfo,
                    });
                    if (tempAsset.attribute.renderCaId) {
                        Object.assign(assetItem.attribute,{
                            renderCaId: tempAsset.attribute.renderCaId,
                            renderCaType: tempAsset.attribute.renderCaType,
                            renderCa: tempAsset.attribute.renderCa,
                        })
                    }
                } else if(tempAsset.meta.type === 'frame'){
                    Object.assign(assetItem.attribute, {
                        resId: tempAsset.attribute.resId,
                        width: tempAsset.attribute.width,
                        height: tempAsset.attribute.height,
                        picUrl: tempAsset.attribute.picUrl,
                        SVGUrl:tempAsset.attribute.SVGUrl,
                        contentResId:tempAsset.attribute.contentResId,
                        assetWidth: tempAsset.attribute.assetWidth,
                        assetHeight: tempAsset.attribute.assetHeight,                  
                        opacity:tempAsset.attribute.opacity,
                        picWidth:tempAsset.attribute.picWidth,
                        picHeight:tempAsset.attribute.picHeight,
                        transformX:tempAsset.attribute.transformX,
                        transformY:tempAsset.attribute.transformY,
                        isDefaultImage:tempAsset.attribute.isDefaultImage,
                        color:tempAsset.attribute.color,
                        defaultPicInfo:tempAsset.attribute.defaultPicInfo,
                        frameBackgroundInfo:tempAsset.attribute.frameBackgroundInfo,
                        transform:tempAsset.attribute.transform
                    });
                    if (tempAsset.attribute.renderCaId) {
                        Object.assign(assetItem.attribute,{
                            renderCaId: tempAsset.attribute.renderCaId,
                            renderCaType: tempAsset.attribute.renderCaType,
                            renderCa: tempAsset.attribute.renderCa,
                        })
                    }
                }


                if(tempAsset.meta.type === 'pic' || tempAsset.meta.type === 'background' || tempAsset.meta.type === 'image'){
                    if( tempAsset.attribute.filters ){
                        Object.assign(assetItem.attribute, {
                            filters: {
                                resId:tempAsset.attribute.filters.resId,
                                brightness: parseFloat(tempAsset.attribute.filters.brightness),
                                saturate: parseFloat(tempAsset.attribute.filters.saturate),
                                contrast: parseFloat(tempAsset.attribute.filters.contrast),
                                blur: parseFloat(tempAsset.attribute.filters.blur),
                                sharpen: parseFloat(tempAsset.attribute.filters.sharpen),
                                hue: parseFloat(tempAsset.attribute.filters.hue),
                                'gamma-r': tempAsset.attribute.filters['gamma-r'] === undefined ? 1 : parseFloat(tempAsset.attribute.filters['gamma-r']),
                                'gamma-g': tempAsset.attribute.filters['gamma-g'] === undefined ? 1 : parseFloat(tempAsset.attribute.filters['gamma-g']),
                                'gamma-b': tempAsset.attribute.filters['gamma-b'] === undefined ? 1 : parseFloat(tempAsset.attribute.filters['gamma-b']),
                                'strong': tempAsset.attribute.filters['strong'] === undefined ? 1 : parseFloat(tempAsset.attribute.filters['strong']),
                            }
                        });
                    }else{
                        Object.assign(assetItem.attribute, {
                            filters: {
                                brightness: 0,
                                saturate: 0,
                                contrast: 0,
                                blur: 0,
                                sharpen: 0,
                                hue: 0,
                                'gamma-r': 1,
                                'gamma-g': 1,
                                'gamma-b': 1,
                            }
                        });
                    }
                    if ( tempAsset.attribute.imageEffects ) {
                        Object.assign(assetItem.attribute,{
                            imageEffects: {
                                resId: tempAsset.attribute.imageEffects.resId,
                                strong: tempAsset.attribute.imageEffects.strong,
                                layers: tempAsset.attribute.imageEffects.layers,
                                renderCaId: tempAsset.attribute.imageEffects.renderCaId,
                                renderCaType: tempAsset.attribute.imageEffects.renderCaType,
                                renderCa: tempAsset.attribute.imageEffects.renderCa,
                                sAngle: tempAsset.attribute.imageEffects.sAngle // 该字段不参与实际渲染，只做存储参考
                            }
                        })
                    }
                    if (tempAsset.attribute.renderCaId) {
                        Object.assign(assetItem.attribute,{
                            renderCaId: tempAsset.attribute.renderCaId,
                            renderCaType: tempAsset.attribute.renderCaType,
                            renderCa: tempAsset.attribute.renderCa,
                        })
                    }
                }

                if (tempAsset.attribute.ks) {
                    Object.assign(assetItem.attribute, {
                        ks: tempAsset.attribute.ks
                    });
                }

                if (tempAsset.attribute.k) {
                    Object.assign(assetItem.attribute, {
                        k: tempAsset.attribute.k
                    });
                }

                // 元素装饰
                if (tempAsset.attribute.decorated) {
                    Object.assign(assetItem.attribute, {
                        decorated: tempAsset.attribute.decorated
                    });
                    
                }

                if((assetItem.meta.type == 'group' && assetItem.meta.group) || assetItem.meta.type !='group' ){
                    tempAssets.assets.push(assetItem)
                }

            }


            tempAssets.assets.map(item=>{
                if(!groupNameSet.has(item.meta.group)){
                    item.meta.group = '';
                }else if(item.meta.type !='group' && groupAssetMap.get(item.meta.group) && groupAssetMap.get(item.meta.group).meta?.memberClassNames){
                    groupAssetMap.get(item.meta.group).meta.memberClassNames.push(item.meta.className)
                }
            })
            groupNameSet.clear()
            groupAssetMap.clear()
            tempPages.push(tempAssets)
        }

        Object.assign(tempDoc.work, {
            pages: tempPages
        })
        // 如果缺少pageAttr.pageInfo，则初始化一个
        if (!doc?.pageAttr?.pageInfo) {
            Object.assign(tempDoc.pageAttr, {
                pageInfo: new Array(tempDoc.work.pages?.length ?? 1).fill({
                    pageTime: 5000
                }),
            });
        }
        if(doc.hasOwnProperty('pageAttr')){
            Object.assign(tempDoc.pageAttr, {
                ...JSON.parse(JSON.stringify(doc.pageAttr)),
            })
        }
        // tempDoc.pageAttr.pageInfo[0].type = 'board'

        return tempDoc
    }

}

export let templateFormat = new TemplateFormat()