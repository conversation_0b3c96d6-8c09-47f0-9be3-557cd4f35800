import React, { Component } from 'react';
import { env } from '@editorConfig/env';
import { assetManager } from '../AssetManager';
import { getProps, hostName } from '../IPSConfig';
import { emitter } from '../Emitter';
import { CMD } from "@editorConfig/Cmd";
import { isUeTeam } from '@v7_utils/webSource';
import { PromptPayBanner } from '@v7_render/InfoBar';
const isMac = /macintosh|mac os x/i.test(navigator.userAgent);

export class DownloadSuccess extends Component {

    onUpgradeClickEvent(e) {
        assetManager.setPv_new(3119, {
            additional: {
                s0: "edit-center",
            }
        })
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    onStartDesignClickEvent(e) {
        assetManager.setPv_new(3079)
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    // 悬停1s限制埋点: 移入
    handleMouseEnterLimitSetPv = (pvNum, e) => {
        e?.stopPropagation();
        e?.nativeEvent.stopPropagation();
        if (this.timer4) {
            clearTimeout(this.timer4);
            this.timer4 = null;
        }
        this.timer4 = setTimeout(() => {
            assetManager.setPv_new(pvNum);
        }, 1000);
    }

    // 悬停1s限制埋点: 移出
    handleMouseLeaveLimitSetPv = (e) => {
        e?.stopPropagation();
        e?.nativeEvent.stopPropagation();
        if (this.timer4) {
            clearTimeout(this.timer4);
            this.timer4 = null;
        }
    }

    onViewFileStorageLocateClickEvent(e) {
        assetManager.setPv_new(3082)
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    onJumpCreationClickEvent(e) {
        assetManager.setPv_new(3080)
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    recordClickTypeEvent(e) {
        if (e.button == 0) {
            assetManager.setPv_new(478);
        } else if (e.button == 2) {
            assetManager.setPv_new(477);
        }
    }

    /**
     *  立即反馈（点击事件）
     */
    rightAwayClickEvent(e) {
        window.open('https://818ps.com/apiv2/udesk', '_blank', 'height=544, width=644,toolbar=no,scrollbars=no,menubar=no,status=no');
        assetManager.setPv_new(474);
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    onBackEditClickEvent(e) {
        emitter.emit('popupClose');
        assetManager.setPv_new(3081)
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    /**
     *  看看我的作品（点击事件）
     */
    myWorkBtnClickEvent(e) {
        if (env.teamTemplate || isUeTeam) {
            window.open(`https://team.818ps.com//team/design?team_id=${getProps().team_id}`)
        } else {
            window.open("//" + hostName + "/dash/userdsn");
        }

        // emitter.emit('popupClose');
        assetManager.setPv_new(133);

        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    // 另存为
    reSave = () => {
        // 保存图片
        // CMD.draw.exec({ name: 'download', params: [{ reDownload: true }] });
    }

    render() {
        const {
            showFlag, isSingleDownloadSuccess, info, singleDownloadSuccessHref, user, successImgUrl, work, isLocalGenerate, newUserDlNum
        } = this.props;
        // const dlSuccessUnVipTop = newUserDlNum && newUserDlNum > 0 ? {marginTop: '20px'} : {};
        return (
            <>
                {showFlag == 2 &&
                    <div className="downloadSuccess3v">
                        {/* <img src="https://js.tuguaishou.com/editor/image/downloadAiImg.png" alt="" className='jump_ai_img' onClick={()=>{
                            assetManager.setPv_new(8287);
                            window.open("https://818ps.com/aihaibao?origin=ueEditor",'_blank');
                        }} /> */}
                        <PromptPayBanner />
                        <div className="successText">
                            <span>
                                <i className='iconfont icon-duigou' />
                            </span>
                            下载成功！
                        </div>
                        {   /* 新用户当日首次注册首次下载后弹出 */
                            // newUserDlNum === 1 && <div className='newUserBenefits'>
                            //     <div className='firstText'>福利下载已用完</div>
                            //     <div className='subText'>明日登录可再获得1次“全VIP模版”福利下载次数</div>
                            //     <div className='lastText'>仅明日可获赠，之后不可领取</div>
                            // </div>
                        }
                        {       /* 新用户当日首次注册第二天首次下载弹出 */
                            // newUserDlNum === 2 &&  <div className='newUserBenefitsNext'>
                            //     <div className='firstText'>“全VIP模版”福利下载次数已用完</div>
                            //     <div className='lastText'>升级<a href="https://818ps.com/dash/firm-intro?origin=download_success_upgrade_vip" target='_blank'>企业商用VIP</a>享<span>“全VIP模版+可商用”</span>权益</div>
                            // </div>
                        }

                        {
                            isSingleDownloadSuccess ?
                                <div className="successSingle">
                                    <div className="singleDownloadSuccessDesc">非{info?.is_company_temp === 1 ? "企业" : ""}VIP不可商用，仅可用于个人学习使用</div>
                                    <a className="hrefUpgrade" href={info?.is_company_temp === 1 ? "//818ps.com/dash/firm-intro?classify=1&origin=up_vip_btn" : singleDownloadSuccessHref} target="_blank" rel="nofollow">
                                        <div className='upgradeMemberBtn' onClick={this.onUpgradeClickEvent.bind(this)}>
                                            升级{info?.is_company_temp === 1 ? "企业" : ""}会员免费商用
                                        </div>
                                    </a>
                                </div>
                                :
                                ((user.commercialType === 0 || user.commercialType === 1 || user.commercialType === 2) && info?.is_company_temp === 1 && !user.selfIsFirm) ?
                                    <div className="successSingle">
                                        <div className="singleDownloadSuccessDesc">
                                            尊敬的个人VIP用户，当前为企业专属模板，不可商用
                                        </div>
                                        <a className="hrefUpgrade" href={"//818ps.com/dash/firm-intro?classify=1&origin=up_vip_btn"} target="_blank" rel="nofollow">
                                            <div className='upgradeMemberBtn' onClick={this.onUpgradeClickEvent.bind(this)}>
                                                升级企业VIP免费商用
                                            </div>
                                        </a>
                                    </div>
                                    :
                                    <div>
                                        <div className='successSubText'>{isMac ? '⌘' : 'Ctrl'}+D 收藏网页，下次打开更方便</div>
                                        <div className="successQR">
                                            <div className="successQRImg">
                                                <a className='link'
                                                    href={(env.teamTemplate || isUeTeam) ? `https://team.818ps.com/team/team-index?team_id=${getProps().team_id}` : "https://818ps.com/search.html"}
                                                    target="_blank"
                                                    onClick={this.onStartDesignClickEvent.bind(this)}
                                                    onMouseEnter={this.handleMouseEnterLimitSetPv.bind(this, 6071)}
                                                    onMouseLeave={this.handleMouseLeaveLimitSetPv}
                                                >
                                                    <dl>
                                                        <dt className='iconfont icon-suoyougongju'></dt>
                                                    </dl>
                                                    <p className="desc">开启新设计</p>
                                                </a>
                                            </div>

                                            <div className="successQRApplet">
                                                <a className='link'
                                                    href={(env.teamTemplate || isUeTeam) ? `https://team.818ps.com/team/design?team_id=${getProps().team_id}&uid=${user.userId}` : "//818ps.com/dash/userdsn"}
                                                    target="_blank"
                                                    onClick={this.onJumpCreationClickEvent.bind(this)}
                                                    onMouseEnter={this.handleMouseEnterLimitSetPv.bind(this, 6072)}
                                                    onMouseLeave={this.handleMouseLeaveLimitSetPv}
                                                >
                                                    <dl>
                                                        <dt className='iconfont icon-sheji'></dt>
                                                    </dl>
                                                    <p className="desc">我的作品</p>
                                                </a>
                                            </div>

                                            <div className="successQRApplet">
                                                <a style={{ cursor: "pointer" }} className='link'
                                                    target="_blank"
                                                    onClick={this.onBackEditClickEvent.bind(this)}
                                                    onMouseEnter={this.handleMouseEnterLimitSetPv.bind(this, 6073)}
                                                    onMouseLeave={this.handleMouseLeaveLimitSetPv}
                                                >
                                                    <dl>
                                                        <dt className='iconfont icon-back'></dt>
                                                    </dl>
                                                    <p className="desc">返回编辑</p>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                        }

                        {
                            (isSingleDownloadSuccess || ((user.commercialType === 0 || user.commercialType === 1 || user.commercialType === 2) && info?.is_company_temp === 1 && !user.selfIsFirm)) ? (
                                <div className={"downloadSuccessUnVip"}>
                                    <div className={'downloadLocateDescBox'}>
                                        <div className="downloadLocateDesc">不确定作品下载到哪了？</div>
                                        <div className="downloadLocateUnVip">
                                            <a target="_blank" onClick={this.onViewFileStorageLocateClickEvent.bind(this)} href="https://818ps.com/information/26.html" className="myWorkBtnUnVip">
                                                查看文件存储位置
                                            </a>
                                        </div>
                                    </div>
                                    <div className="checkMyWork" onClick={this.myWorkBtnClickEvent.bind(this)}>
                                        查看我的作品
                                    </div>
                                </div>
                            ) : (
                                <div className={"downloadLocate"}>
                                    <p >不确定作品下载到哪了？</p>
                                    <a target="_blank" onClick={this.onViewFileStorageLocateClickEvent.bind(this)} href="https://818ps.com/information/26.html" className="myWorkBtn">
                                        查看文件存储位置
                                    </a>
                                </div>
                            )
                        }

                        {user.vip == 1 && <div className="loginLoser"> </div>}
                        <div className='unDownloaded'>
                            <div className="successRemind">如未下载，
                                <span onMouseDown={this.recordClickTypeEvent.bind(this)}>
                                    {//判断本地生成图片
                                        isLocalGenerate ?
                                            <a className="rightDown" target="_blank" onClick={this.reSave}></a>
                                            :
                                            <>
                                                {work.pages.length > 1 ?
                                                    <a className="rightDown" href={successImgUrl.src} target="_blank"></a>
                                                    :
                                                    <a target="_blank" href={successImgUrl.src} className="leftCode rightDown">
                                                        <img target="_blank" className="rightDown" alt="" src={successImgUrl.src} />
                                                    </a>
                                                }
                                            </>
                                    }

                                </span>
                                <span> 右键点击此处 </span>
                                {work.pages.length > 1 ? <em>选择“链接另存为”</em> : <em>选择“图片另存为”</em>}
                            </div>
                            <div
                                className={isSingleDownloadSuccess ? "successQuestion singleDownloadSucess" : "successQuestion"}
                                onClick={this.rightAwayClickEvent.bind(this)}
                            >
                                遇到问题？<span>立即反馈</span>
                            </div>
                        </div>
                    </div>
                }
            </>
        )
    }
}
