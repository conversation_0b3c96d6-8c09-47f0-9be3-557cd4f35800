/**
 * Created by hckj on 2017/11/15.
 */
import React, { PureComponent } from 'react';

import { emitter } from '../Emitter';
import addEventListener from 'rc-util/lib/Dom/addEventListener';

import { ToolPanel } from '../canvas/ToolPanel';
import { ToolPanelDefault } from './ToolPanelDefault';
import { storeDecorator } from "@v7_logic/StoreHOC";
import { SelectAsset } from '@v7_logic/AssetLogic';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { ErrorBoundaryDecorator } from '@v7_render/ErrorBoundaryHOC';
import classNames from 'classnames';
import { CanvasStylePanel } from '@v7_render/CanvasStylePanel';
import { assetManager } from '../AssetManager';
import { isAIDesign } from '@v7_utils/estimate';
import { klona } from 'klona';
import { TemplateCanvasLogic } from '@v7_logic/TemplateCanvasLogic';


class HelperLayout extends PureComponent {
    render() {
        let helperLayoutStyle = {};
        if (!this.props.titleTip) {
            helperLayoutStyle = {
                padding: 0,
                width: '100%',
            }
        }
        return (
            <div className="helperLayout" style={helperLayoutStyle}>
                {this.props.children}
            </div>
        );
    }
}

const mapStateToProps = (state) => {
    return {
        canvas: state.onCanvasPainted.canvas,
        pageInfo: state.onCanvasPainted.pageInfo,
        work: state.onCanvasPainted.work,
        pageAttr: state.onCanvasPainted.pageAttr,
        toolPanel: state.onCanvasPainted.toolPanel,
        rt_canvas_render_mode: state.onCanvasPainted.rt_canvas_render_mode,
        isDesigner: state.onCanvasPainted.isDesigner,
    };
};

@ErrorBoundaryDecorator()
@storeDecorator(mapStateToProps)
class FloatToolPanel extends PureComponent {
    constructor(props) {
        super(props);
        // canvasLayout
        const storageFixedToolPanel = localStorage.getItem('fixedToolPanel');
        const fixedToolPanel = storageFixedToolPanel ? storageFixedToolPanel === 'true' : true;
        this.state = {
            floatToolPanelStyle: {
                right: '0px',
            },
            hideFloat: !fixedToolPanel,
            fixedToolPanel: fixedToolPanel,
            curTab: 'canvas',
        }

        this.assetBlur = this.assetBlur.bind(this);

        this.updateState();
    }

    componentDidMount() {
        this.openFontFamilyPanelListener = emitter.addListener("floatMenu.hideFloat", (data, value) => {
            if (data === 'more') {
                this.setState({
                    fixedToolPanel: true,
                    hideFloat: false,
                })
                localStorage.setItem('fixedToolPanel', true);
                emitter.emit('setFixedToolPanel', true);
                assetManager.setPv_new(8527, {
                    additional: {
                        i0: 1
                    }
                })
            } else if (data === 'switch-asset') {
                this.setState({
                    hideFloat: !this.state.fixedToolPanel
                });
            } else if (data === 'base') {
                console.log('base', value);
                
                this.setState({
                    hideFloat: value
                });
            } else {
                this.setState({
                    hideFloat: !this.state.hideFloat
                });
            }
            
        });

        this.updateToolPanelWidth();
    }

    componentDidUpdate() {
        this.updateToolPanelWidth();
    }

    // componentWillUnmount() {
    //     this.openFontFamilyPanelListener && this.openFontFamilyPanelListener.remove();
    // }

    updateToolPanelWidth() {
        let toolPanelWidth = 0;
        if (this.state.fixedToolPanel || !this.state.hideFloat) {
            toolPanelWidth = 270;
        }
        if (toolPanelWidth !== this.props.toolPanelWidth) {
            this.props.setToolPanelWidth(toolPanelWidth);
        }
    }

    updateState() {
        let th = this;
        emitter.addListener("FloatToolPanelUpdateState", () => {
            if (th.state.curTab === 'canvas') {
                // 关闭调整尺寸
                emitter.emit('CanvasSizeCloseEmitter');
                return;
            } else {
                th.setState({ 'curTab': 'canvas' });
            }
            // th.forceUpdate();
        });
    }

    // 自定义画布大小
    sizeAreaClickEvent = (e) => {
        /*编辑器判断*/
        const { isDesigner, rt_is_online_detail_page } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if (!isDesigner || rt_is_online_detail_page) {
            emitter.emit('CanvasSizeShowEmitter');
        }
    }
    assetBlur() {
        SelectAsset.blurAsset();
    }

    /**
     * 用户下载
     */
    download(e) {
        // canvasStore.dispatch(paintOnCanvas('UPDATE_OPERATION_RECORD_ISDOWNLOAD')) // 对应 store 已废弃
        emitter.emit('InfoBarDownload')
    }
    getShowSize = (canvas) => {
        let unit = canvas.showUnit || 'px';
        let showWidth = 0;
        let showHeight = 0;
        switch (unit) {
            case 'cm': {
                showWidth = Math.round(canvas.width / 3000 * 25.4);
                showHeight = Math.round(canvas.height / 3000 * 25.4);
                break;
            }
            case 'mm': {
                showWidth = Math.round(canvas.width / 300 * 25.4);
                showHeight = Math.round(canvas.height / 300 * 25.4);
                break;
            }
            case 'px':
            default: {
                showWidth = canvas.width;
                showHeight = canvas.height;
                break;
            }

        }
        return [showWidth + unit, showHeight + unit];
    }
    switchTab(tab) {
        SelectAsset.blurAsset();
        this.setState({
            curTab: tab
        })
        assetManager.setPv_new(7735, {
            additional: {
                s0: 7735
            }
        })
    }

    setShowToolPanel = () => {
        const { canvas } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        })
        const toolPanelWidth = 270 / 2;
        let canvasX = canvas.x;
        let isFixed = true;
        if (this.state.fixedToolPanel) {
            // 隐藏右侧面板
            this.setState({
                fixedToolPanel: false,
                hideFloat: true,
            })
            isFixed = false;
            canvasX += toolPanelWidth;
        } else {
            this.setState({
                fixedToolPanel: true,
                hideFloat: false,
            })
            canvasX -= toolPanelWidth;
        }
        localStorage.setItem('fixedToolPanel', isFixed);
        emitter.emit('setFixedToolPanel', isFixed);
        assetManager.setPv_new(8527, {
            additional: {
                i0: isFixed ? 1 : 0
            }
        })
        // TemplateCanvasLogic.setCanvasPositionAndSize({x: canvasX})
    }

    render() {
        let { toolPanel, work, pageInfo, isDesigner, canvas, rt_canvas_render_mode } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }),
            assets = work.pages[pageInfo.pageNow].assets,
            assetsFlag = 0,
            toolPanelFlag = 0,
            toolPanelFlagNew = 0,
            quickEditingShow = 0;

        let { floatToolPanelStyle, curTab, isRightHide } = this.state,
            titleTip = '',
            tabItemContent1 = '',
            tabItemContent2 = '',
            showToptit = false;
        const [showWidth, showHeight] = this.getShowSize(canvas);
        floatToolPanelStyle = klona(floatToolPanelStyle);


        /*编辑器判断*/
        // if( !isDesigner ){
        //     assets.map((item) => {
        //         if( item.meta.name != "" && item.meta.name != undefined ){
        //             assetsFlag = 1;
        //             titleTip = '仅需3秒，替换文案就出图';
        //             tabItemContent1 = '快捷编辑';
        //         }
        //     });
        // }

        // if( ( toolPanel.asset != "" || toolPanel.assets.length > 0 ) && !toolPanel.isQuickEdit ){
        toolPanelFlag = 1;
        
        if ((toolPanel.asset != "" || toolPanel.assets.length > 0)) {

            if (toolPanel.asset != "" && toolPanel.asset && toolPanel.asset.meta) {
                if (toolPanel.asset.meta.type == 'text') {
                    titleTip = '文本';
                    showToptit = true;
                } else if (toolPanel.asset.meta.type == 'image') {
                    titleTip = '元素';
                    showToptit = true;
                } else if (toolPanel.asset.meta.type == 'background') {
                    if (rt_canvas_render_mode === 'board') {
                        titleTip = '背景';
                    }
                    // titleTip = '背景';
                    showToptit = true;
                } else if (toolPanel.asset.meta.type == 'group') {
                    titleTip = '组合';
                } else if (toolPanel.asset.meta.type == 'SVG' || toolPanel.asset.meta.type == 'flow') {
                    titleTip = '形状';
                } else if (toolPanel.asset.meta.type == 'table') {
                    titleTip = '表格';
                    showToptit = true;
                } else if (toolPanel.asset.meta.type == 'chart') {
                    titleTip = '图表';
                    showToptit = true;
                } else if (toolPanel.asset.meta.type == 'pic') {
                    titleTip = '图片';
                    showToptit = true;
                } else if (toolPanel.asset.meta.type == 'videoE') {
                    titleTip = '内嵌视频';
                    showToptit = true;
                } else if (toolPanel.asset.meta.type == 'qrcode') {
                    titleTip = '二维码';
                    showToptit = true;
                } else if (toolPanel.asset.meta.type == 'frame') {
                    titleTip = '形状';
                    showToptit = true
                } else if (toolPanel.asset.meta.type == 'line') {
                    titleTip = '线条';
                    showToptit = true
                }
            } else if (toolPanel.assets.length > 0) {
                const isIllegalData = toolPanel.assets.filter(item => item === undefined)
                if (isIllegalData?.length === 0) {
                    titleTip = '多选';
                }

            }
            tabItemContent2 = '高级编辑';
        }

        if (toolPanel.reline) {
            toolPanelFlagNew = 1
            titleTip = '参考线'
        } else if (toolPanel.selectAssetsPageIndex >= 0 && toolPanel.selectAssetsPageIndex != pageInfo.pageNow) {
            // 选中元素的页面和当前页面不匹配的时候，暂时不展示工具栏
            titleTip = '';
        }

        if (!toolPanelFlag) {
            Object.assign(floatToolPanelStyle, {
                display: 'none'
            })
        }

        if (tabItemContent1 != '' && tabItemContent2 == '') {
            quickEditingShow = 1;
        }
        if (!this.state.fixedToolPanel && this.state.hideFloat && !canvas.floorCutting) {
            floatToolPanelStyle.width = 0;
        }
        const isAiDesign = isAIDesign()

        if (rt_canvas_render_mode === 'board' && toolPanel.asset_index < 0 && toolPanel.assets_index?.length === 0) {
            return null;
        }
        return (
            <div className="floatToolPanel" ref="floatToolPanel" style={floatToolPanelStyle}>
                {!canvas.floorCutting && (
                    <div
                        className={`fix-tool-panel ${!this.state.hideFloat ? 'show' : ''}`}
                        onClick={this.setShowToolPanel}
                    >
                        {this.state.fixedToolPanel ? (
                            <i className="iconfont icon-Frame"></i>
                        ) : this.state.hideFloat ? (
                            <div className="content">
                                <i className="iconfont icon-Frame left"></i>
                                <span className="open-text">设置面板</span>
                            </div>
                        ) : (
                            <i className="iconfont icon-Frame"></i>
                        )}
                    </div>
                )}
                {/* {
                !titleTip && (<div className='tab-box'>
                    <div className={classNames('tab-item',curTab == 'canvas' ? 'active':'')} onClick={()=>{this.switchTab('canvas')}}>画布背景</div>
                    <div className={classNames('tab-item',curTab == 'style' ? 'active':'')} onClick={()=>{this.switchTab('style')}}>风格配色</div>
                </div>)
            } */}
                {curTab == 'canvas' || titleTip ? (
                    <>
                        {rt_canvas_render_mode !== 'board' && (
                            <>
                                <div className="defaultTopicContainer">画布</div>
                                {(!isDesigner || isAiDesign) && !toolPanelFlagNew && titleTip == '' && (
                                    <div className="contentTabAreaNew">
                                        <div className="tabItemBig active">尺寸</div>
                                        {/* <span className="edit" onClick={this.sizeAreaClickEvent.bind(this)}><i className="iconfont icon-bianji"></i></span> */}
                                        <span style={{ fontSize: '14px', fontWeight: 400, color: '#797676' }}>
                                            {showWidth + '  × ' + showHeight}
                                        </span>
                                    </div>
                                )}

                                {!toolPanelFlagNew && titleTip != '' && !showToptit && (
                                    <div className="contentTabAreaNew">
                                        <div className="cateSwitchTab">
                                            <div className={'cateSwitchTabItem active'}>{titleTip}</div>
                                            <div className="cateSwitchTabItem" onClick={this.assetBlur}>
                                                画布
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </>
                        )}

                        <HelperLayout titleTip={titleTip}>
                            {/* 画布属性设置 */}
                            <ToolPanelDefault isShow={titleTip == '' ? true : false} />
                            {/* 元素属性设置 */}
                            {titleTip != '' && <ToolPanel toolPanel={toolPanel} />}
                            {/* <ToolPanel toolPanel={'color'} /> */}
                        </HelperLayout>
                    </>
                ) : (
                    <CanvasStylePanel
                        goBack={() => {
                            this.setShowStyleDetailPanel(false);
                        }}
                    ></CanvasStylePanel>
                )}
                {this.props.children}
            </div>
        );
    }
}

export { FloatToolPanel }
