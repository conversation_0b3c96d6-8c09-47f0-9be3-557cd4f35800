import React, {Component} from 'react';
import { PageAnimation } from '@v7_render/Animation/PageAnimation';
import { storeDecorator } from '@v7_logic/StoreHOC';

import {emitter} from '../Emitter';
import {MoreColorsPanel} from '../MoreColorsPanel/MoreColorsPanel';
import {paintOnCanvas} from '../canvas/CanvasRedux';
import addEventListener from 'rc-util/lib/Dom/addEventListener';
import {assetManager} from '../AssetManager';
import MoreColorsPreinstall from '../MoreColorsPanel/MoreColorsPreinstall'
import {matchesSelector} from '../Function';
import {UserUploadArea} from './ToolPanelDefault/UserUploadArea';
import {BackgroundPanel} from '../Designer/toolV6.0/Background/Background';
import UserMutiSize from '../toolV6.0/OnlineRetailer/UserMutiSize';
import classNames from 'classnames';
import {getProps} from '../IPSConfig';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { AssetLogic, SelectAsset, UpdateAsset, DeleteAsset } from '@v7_logic/AssetLogic';
import { CanvasPaintedLogic } from '@v7_logic/CanvasPaintedLogic';
import { SetPageTime } from "@v7_render/SetPageTime"
import { TemplateLogic } from '@v7_logic/TemplateLogic';
import { CanvasGuides } from '@v7_render/AssetToolPanel/components/CanvasGuides';
import { CanvasStylePanel, CanvasStyleWrap } from '@v7_render/CanvasStylePanel';
import { CanvasSize } from '@v7_render/CanvasSize';
import { env } from '@editorConfig/env';
import { isEcommerce } from '@v7_utils/webSource';
import TemplateLoader from '@v7_render/AIDesignTools/TemplateLoader/TemplateLoader';
import { TextRectSwitch } from '@v7_render/AIDesignTools/TextRectSwitch';
import { PPTPageMarking } from '@v7_render/PPTPageMark';
import { isAIDesign, isAIDesignPPT } from '@v7_utils/estimate';
import { TgsCanvas } from '@tgs/canvas';
import { BackgroundCustomCategory } from '@v7_render/AssetToolPanel/Image/ImageEditTool';
import { MaskGenerate } from '@v7_render/AIDesignTools/MaskGenerate';
@storeDecorator((state) => {
    return {
        // user: state.onCanvasPainted.user,
        canvas: state.onCanvasPainted.canvas,
        rt_mutisize_user_show: state.onCanvasPainted.rt_mutisize_user_show,
        pageAttr: state.onCanvasPainted.pageAttr,
        rt_canvas_render_mode: state.onCanvasPainted.rt_canvas_render_mode,
    };
})
class ToolPanelDefault extends Component {
    constructor(props) {
        super(props);

        this.state = {
            isColorsArea: false,
            backgroundSelected: 'color',
            backgroundTextureList: [],
            backgroundTextureSelected: '',
            backgroundSelectImage: '',
            backgroundWidth: 48,
            backgroundHeight: 66,
            showStyleDetailPanel: false, // 显示风格详情面板
            showCanvasSizePanel: false
        };
        this.getMutiSizeTimes = 1;
        this.getInitMutiSizeData();
        this.getBackgroundSelectImage()
        this.deleteBackgroundImage()
        this.changeTemplate()
        this.changeBackgroundImage()
    }

    /**
     * 添加缩略图
     */
    getBackgroundSelectImage = () => {
        if (this.ListAddBackgroundListenner) {
            this.ListAddBackgroundListenner.remove()
        }
        this.ListAddBackgroundListenner = emitter.addListener('ListAddBackground', (background) => { 
            if (background) {
                this.changeImageStatus = true
            } else {
                this.changeImageStatus = false
            }
            this.setState({backgroundSelectImage: background.sample ? background.sample : background.image_url})
            this.checkThumbnailSize(background)
        })
    }

    /**
     * 替换模板
     */
    changeTemplate = () => {
        if (this.changeTemplateListenner) {
            this.changeTemplateListenner.remove()
        }
        this.changeTemplateListenner = emitter.addListener('changeTemplate', asset => {
            const templateBacground = asset?.find(item => item.meta.type === 'background')
            this.setState({backgroundSelectImage: templateBacground ? templateBacground.attribute.picUrl : ''}) 
            templateBacground && this.checkThumbnailSize(templateBacground.attribute)
        })
    }

    /**
     * 替换背景缩略图
     */
    changeBackgroundImage = () => {
        if (this.changeBackgroundImageListener) {
            this.changeBackgroundImageListener.remove()
        }
        this.changeBackgroundImageListener = emitter.addListener('changeBackgroundImage', () => {
            let { work, pageInfo } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            const backgroundMsg = work.pages[pageInfo.pageNow].assets.find(item => item.meta.type === 'background')
            if (backgroundMsg) {
                this.setState({backgroundSelectImage: backgroundMsg.attribute.picUrl})
                this.checkThumbnailSize(backgroundMsg.attribute)
            }
        })
    }

    /**
     * 删除缩略图
     */
    deleteBackgroundImage = () => {
        if (this.deleteAssetBackgroundImageListenner) {
            this.deleteAssetBackgroundImageListenner.remove()
        }
        this.deleteAssetBackgroundImageListenner = emitter.addListener('deleteAssetBackgroundImage', () => {
            let { work, pageInfo } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            const backgroundStatus = work.pages[pageInfo.pageNow].assets.some(item => item.meta.type === 'background')
            !backgroundStatus && this.setState({backgroundSelectImage: ''})
        })
    }

    /**
     * 展示缩略图
     */
    componentDidMount() {
        this.getBackgroundImageListenner = emitter.addListener('getBackgroundImage', (asset) => {
            let { work, pageInfo } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            const pageAssets = work.pages[pageInfo.pageNow].assets;
            const currentHash = pageAssets[0]?.meta?.hash?.split('_')[0];
            const backgroundStatus = pageAssets.find(item => item.meta.type === 'background');
            const isSamePage = currentHash && asset.meta.hash?.indexOf(currentHash) > -1;
            if(asset.meta.type === "background" && !this.changeImageStatus && isSamePage) {
                this.setState({backgroundSelectImage: asset.attribute.picUrl})
                this.checkThumbnailSize(asset.attribute)
            }else if (backgroundStatus && backgroundStatus.attribute.picUrl !== 'loading') {
                this.setState({backgroundSelectImage: backgroundStatus.attribute.picUrl})
            }
            
        })
        this.thumbnailUpdateListenner = emitter.addListener('thumbnailUpdate', (pages) => {
            let { pageInfo } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            let backgroundStatus = pages[pageInfo.pageNow].assets.find(item => item.meta.type === 'background')
            !backgroundStatus && this.setState({backgroundSelectImage: ''})
        })
        this.showCanvasSizePanelListener = emitter.addListener('showCanvasSizePanel', (isShow) => {
            this.setState({showCanvasSizePanel: isShow})
        })

        this.openColorPanelListener = emitter.addListener("openFuncFloat", (type, item) => {
            if (type === "bg-color") {
                this.setState({
                    isColorsArea: item,
                    backgroundSelected: item ? 'color' : ''
                });
                if (this.changeBackgroundColorContentMouseDownListenner) {
                    this.changeBackgroundColorContentMouseDownListenner.remove();
                }
                let th = this;
                this.changeBackgroundColorContentMouseDownListenner = addEventListener(window, "click", (e) => {
                    if (matchesSelector(e.target, '.changeBackgroundColorContent .colorBlock,.backgroundColorWrap *')) {
                        return
                    }
                    emitter.emit("floatMenu.hideFloat", true);
                    th.setState({
                        isColorsArea: false,
                    });
                    th.changeBackgroundColorContentMouseDownListenner.remove();
                });
            } else if (type === "bg-delete") {
                const { work, pageInfo } = storeAdapter.getStore({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                });
                let backgroundIndex = 0;
                work.pages[pageInfo.pageNow].assets.forEach((item, index)=> {
                    if (item.meta.type === 'background') {
                        backgroundIndex = index
                    }
                })
                this.delBackgroundAsset(pageInfo.pageNow, backgroundIndex, null)
            } else if (type === "canvas-size") {
                console.log('canvas-size', item);
                this.setState({
                    isColorsArea: false,
                });
                
                if (!this.state.showCanvasSizePanel) {
                    this.sizeAreaClickEvent();
                } else {
                    SelectAsset.blurAsset();
                    this.setState({
                        showCanvasSizePanel: false
                    })
                    assetManager.setPv_new(7791);
                    emitter.emit('CanvasSizeCloseEmitter');
                }
            }
        });
    }

    componentWillUnmount() {
        this.showCanvasSizePanelListener.remove()
        this.deleteAssetBackgroundImageListenner.remove()
        this.getBackgroundImageListenner.remove()
        this.changeBackgroundImageListener.remove()
        this.ListAddBackgroundListenner.remove()
        this.thumbnailUpdateListenner.remove()
        this.openColorPanelListener.remove()
    }
    componentDidUpdate(prevProps, prevState, snapshot) {
        TgsCanvas.SHOW_TEXT_RECT_ON_AI_DESIGN_FIRST_RENDER = isAIDesign();
        if (this.props.isShow === false && prevProps.isShow === true) {
            this.setState({
                showCanvasSizePanel: false
            })
        }
    }
    /**
     * 判断缩略图的尺寸大小
    */
    checkThumbnailSize(uploadImage){
        // if(Number.parseInt(uploadImage.height) > Number.parseInt(uploadImage.width)) {
        //     this.setState({backgroundWidth: 'auto', backgroundHeight: 66})
        // } else if (Number.parseInt(uploadImage.height) < Number.parseInt(uploadImage.width)) {
        //     this.setState({backgroundWidth: 66, backgroundHeight: 'auto'})
        // }
    }

    onChange(color,type , opacityBg=false) {
        assetManager.setPv_new(2438, { additional: {} });
        if (type =='click') {
            CanvasPaintedLogic.updateBackGroundColor(
                {
                    backgroundColor: color.color , 
                    isOpacity : opacityBg ,
                    fun_name:"UPDATE_BACKGROUNDCOLOR_END"
                }
            )
        } else {
            CanvasPaintedLogic.updateBackGroundColor(
                {
                    backgroundColor: color.color , 
                    isOpacity : opacityBg ,
                    fun_name:"UPDATE_BACKGROUNDCOLOR"
                }
            )
        }
    }

    onEditBackground(asset, pageIndex) {
        const {toolPanel} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const item = toolPanel.asset || asset;
        if (item && item.meta && item.meta.isEdit) {
            return;
        }
        SelectAsset.blurAsset();
        const targets = [
            {
                index: 0,
                className: item.meta.className,
                pageIndex: pageIndex,
                changes: {
                    meta: {
                        isEdit: true,
                    },
                },
            },
        ];
        assetManager.setPv_new(7754,{
            additional: {
                s0: 'right-btn',
            },
        });
        UpdateAsset.updateAssets('ASSET_EDIT_START', targets);
    }

    delBackgroundAsset(page_num, assetIndex, e) {
        // if(!['background'].includes(asset?.meta?.type)) return
        // 删除背景
        assetManager.setPv_new(7727, {additional: { s0: 'right-tool'}});
        DeleteAsset.deleteAssetFun({asset_index_list: [{index: assetIndex, page_num}], origin:''})
        e?.stopPropagation()
    }

    changeBackgroundColorContentClickEvent(e, type) {
        e?.stopPropagation()
        
        let {isColorsArea,backgroundTextureList} = this.state;
        this.setState({
            isColorsArea: !isColorsArea,
            backgroundSelected:type
        });

        if (this.changeBackgroundColorContentMouseDownListenner) {
            this.changeBackgroundColorContentMouseDownListenner.remove();
        }
        let th = this;
        this.changeBackgroundColorContentMouseDownListenner = addEventListener(window, "click", (e) => {
            if (matchesSelector(e.target, '.changeBackgroundColorContent .colorBlock,.backgroundColorWrap *')) {
                return
            }
            th.setState({
                isColorsArea: false,
            });
            th.changeBackgroundColorContentMouseDownListenner.remove();
        });

        if (type == 'texture' && backgroundTextureList.length == 0) {
            assetManager
                .getBackgroundTextureList()
                .then(res => res.json())
                .then(data => {
                    if (data.stat == 1) {
                        this.setState({
                            backgroundTextureList: data.msg
                        })
                    }
                });
        }

        // e.stopPropagation();
        // e.nativeEvent.stopPropagation();
    }

    backgroundSelectNavItemClickEvent(item, e) {
        let {backgroundSelected,backgroundTextureList} = this.state;
        if (backgroundSelected != item) {
            this.setState({
                backgroundSelected: item
            })
        }

        if (item == 'texture' && backgroundTextureList.length == 0) {
            let _this = this;
            assetManager
                .getBackgroundTextureList()
                .then(res => res.json())
                .then(data => {
                    if (data.stat == 1) {
                        _this.setState({
                            backgroundTextureList: data.msg
                        })
                    }
                });
        }


        e.stopPropagation(e)
    }
    // 自定义画布大小
    sizeAreaClickEvent = (e) => {
        /*编辑器判断*/
        const {isDesigner,rt_is_online_detail_page,isAiDesign} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        this.setState({
            showCanvasSizePanel: true
        })
        if (!isDesigner || isAiDesign || rt_is_online_detail_page) {
            if (env.editor === 'ue') {
                SelectAsset.blurAsset();
                emitter.emit('NewCanvasSizeShowEmitter');
            } else {
                emitter.emit('CanvasSizeShowEmitter');
            }
        }
    }

    textureItemClickEvent(item, e) {
        let {backgroundTextureSelected} = this.state;
        if (backgroundTextureSelected != item.id) {
            this.setState({
                backgroundTextureSelected: item.id
            })
            CanvasPaintedLogic.updateBackImage(
                {
                    ...item
                }
            )
            assetManager.setPv_new(8370)
        }
        e.stopPropagation(e)
    }

    stopPropagation(e) {
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    getInitMutiSizeData(){
        let {info} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage,
        });
        let _this = this;
        let props = getProps();
        const kid_2 = info.kid_2 || localStorage.ue_mutisize_temp_kid_2
        if(this.getMutiSizeTimes > 5 || props['isDesigner']) return;
        if(kid_2 && info.kid_1 != 1){
            // assetManager.getOnlineAssetsInfo(14).then(data=>{
            assetManager.getOnlineAssetsInfo({kid_2}).then(data=>{
                data.json().then(res=>{
                    const{ stat , msg =[] } = res;
                    if(stat == 1 && msg.length > 1){
                        CanvasPaintedLogic.mutisizeSetIsMutisizeStatus({
                            bool: true,
                            mutiSizeInfo : msg
                        });
                    }
                    // else if(stat == 1 && msg.length == 0 && info.kid_1 == 1 ){
                    //     canvasStore.dispatch(paintOnCanvas('MUTISIZE_SET_IS_MUTISIZE_STATUS', {
                    //         bool: true,
                    //         mutiSizeInfo : []
                    //     }))
                    // }
                })
            })
        }else if(info.kid_1 == 1){
            const { last_templ_id , id, kid_1 } = info;
            let postId = last_templ_id || id;
            assetManager.getOnlineTemplateDetail_user({tid:postId,kid_1:kid_1}).then(data=>{
                data.json().then(res=>{
                    if(res && res.length > 1){
                        const newmutiSizeInfo = res.map((item)=> ({
                                height: item.height,
                                kid_1: item.full_info.info.kid_1 ,
                                kid_2: item.full_info.info.kid_2 ,
                                link_tid: item.link_tid,
                                tid: item.tid,
                                kname: item.full_info.info.kid2_name,
                                width: item.width,
                                can_search: 2,
                                class_id: item.full_info.class_id
                        }))
                        CanvasPaintedLogic.mutisizeSetIsMutisizeStatus({
                            bool: true,
                            mutiSizeInfo : newmutiSizeInfo
                        });
                    }
                })
            })
        }else{
            let timer = setTimeout(() => {
                _this.getInitMutiSizeData();
                clearTimeout(timer);
                _this.getMutiSizeTimes ++
            }, 300);
        }
    }

    getShowSize = (canvas) => {
        let unit = canvas.showUnit || 'px';
        let showWidth = 0;
        let showHeight = 0;
        switch(unit) {
            case 'cm': {
                showWidth = Math.round(canvas.width / 3000 * 25.4);
                showHeight = Math.round(canvas.height / 3000 * 25.4);
                break;
            }
            case 'mm': {
                showWidth = Math.round(canvas.width / 300 * 25.4);
                showHeight = Math.round(canvas.height / 300 * 25.4);
                break;
            }
            case 'px':
            default: {
                showWidth = canvas.width;
                showHeight = canvas.height;
                break;
            }
                
        }
        return [showWidth + unit, showHeight + unit];
    }

    hasVideoType = (pageAttr, pageNow) => {
        // if (Array.isArray(pageAttr.pageInfo)) {
        //     if (pageAttr.pageInfo[pageNow]?.pageTime) {
        //         return true
        //     }
        // }
        // if (Array.isArray(pageAttr.pageInfo)) {
        //     for(const i of pageAttr.pageInfo) {
        //         if (i?.k) {
        //             for(const key in i.k) {
        //                 if (i.k[key].resId) {
        //                     return true;
        //                 }
        //             }
        //         }
        //     }
        // }
        if (Array.isArray(pageAttr.sound)) {
            for (const i of pageAttr.sound) {
                if (i?.list?.[0]?.resId) {
                    return true;
                }
            }
        }
        if (TemplateLogic.hasAudio(pageAttr) || TemplateLogic.hasVideoE()) {
            return true;
        }
        return false;
    }
    setShowStyleDetailPanel(isShow){
        this.setState({
            showStyleDetailPanel:isShow
        })
    }
    render() {
        let {canvas, work, pageInfo, pageAttr , rt_isMutiSizeTemplate ,rt_mutisize_user_show,rt_is_online_detail_page, user, rt_canvas_render_mode} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        let {isColorsArea, backgroundSelected, backgroundTextureList, backgroundTextureSelected, backgroundSelectImage, backgroundWidth, backgroundHeight , showStyleDetailPanel, showCanvasSizePanel} = this.state;
        let colorBlockStyle;
        let props = getProps();
        if(pageAttr.backgroundImage && pageAttr.backgroundImage[pageInfo.pageNow] && pageAttr.backgroundImage[pageInfo.pageNow].resId != ''){
            colorBlockStyle = {
                backgroundImage:`url(${pageAttr.backgroundImage[pageInfo.pageNow].rt_imageUrl})`
            }
        }else{
            colorBlockStyle = {
                backgroundColor: "rgba(" + work.pages[pageInfo.pageNow].backgroundColor.r + ", " + work.pages[pageInfo.pageNow].backgroundColor.g + ", " + work.pages[pageInfo.pageNow].backgroundColor.b+","+work.pages[pageInfo.pageNow].backgroundColor.a + ")"
            };
        }
        if(work.pages[pageInfo.pageNow]){
            let opacityBg = work.pages[pageInfo.pageNow].isOpacityBg || false ;
            if(opacityBg){
                colorBlockStyle = {
                    ...colorBlockStyle,
                    backgroundImage : `url(//s.tuguaishou.com/image/picEditor/opacity.png)` ,
                    backgroundSize : 'auto 100%',
                    //backgroundRepeat:'no-repeat'
                }
            }
        }

        let changeBackgroundColorContentClickEvent = this.changeBackgroundColorContentClickEvent.bind(this);
        let {isDesigner} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        let {info} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage,
        });
        const urlProps = getProps();
        const k1 = urlProps['k1'] || info.kid_1;
        const k2 = urlProps['k2'] || info.kid_2;
        let toolPanelDefaultScrollStyle = {
            display: 'none'
        }
        const isAiDesign = isAIDesign();
        if (this.props.isShow) {
            // const clientHeight = document.body.clientHeight
            // if (clientHeight <= 721) {
            //     Object.assign(toolPanelDefaultScrollStyle, {
            //         height: 'calc(100% - 45px)',
            //         overflow: 'scroll'
            //     });
            // }
            Object.assign(toolPanelDefaultScrollStyle, {
                display: 'inline-block'
            });
        }
        let [showWidth, showHeight] = this.getShowSize(canvas);
        // 电商详情
        if(rt_is_online_detail_page && canvas.floorCutting && canvas.floorCutting.length > 0){
            let curFloor = canvas.floorCutting[pageInfo.rt_online_detail_current_index || 0];
            showHeight = curFloor.height;
        }
        // 电商详情 end

        // const userId = `${user.userId}`
        // const pageAnimationTest = userId > 0 && (['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'].includes(userId[userId.length - 1]) || ['11936767', '9667287', '251262'].includes(userId))
        let backgroundIndex = 0;
        let backgroundImageShow;
        work.pages[pageInfo.pageNow].assets.forEach((item, index)=> {
            if (item.meta.type === 'background') {
                backgroundImageShow = item
                backgroundIndex = index
            }
        })
        // const isShowUserMutSize  = info?.class_id.indexOf('1169') > -1
        let isShowUserMutSize = false;
        //class_id 包含在列数据展示多模版
        const classIdArr = ['4','5','12','800','956','1025','1026','1169'];
        info?.class_id.forEach((item) => {
            if(classIdArr.includes(item) && !isEcommerce()){
                isShowUserMutSize = true;
            }
        });
        if(window.location.href.includes('ecommerce.818ps.com') ){
            isShowUserMutSize = true;
         }

        const designerPPT = isDesigner && props.ai_marking && (info.template_type === 3 || parseInt(props?.template_type) === 3);
        return (
            <div className="toolPanelDefaultScroll" style={toolPanelDefaultScrollStyle}>
                {env.editor === 'ue' && <CanvasSize showFlag={showCanvasSizePanel && this.props.isShow}></CanvasSize>}

                <div className="toolPanelDefault">
                    {rt_canvas_render_mode !== 'board' && (!isDesigner || isAiDesign) && (
                        <div className="item changeSizeArea">
                            {/* <div className="changeSizeBtn" onClick={this.sizeAreaClickEvent.bind(this)}>
                                  <span>{showWidth}</span>
                                  <span className="desc">宽</span>
                              </div>
                              <i className="margin_i"/>
                              <div className="changeSizeBtn" onClick={this.sizeAreaClickEvent.bind(this)}>
                                  <span>{showHeight}</span>
                                  <span className="desc">高</span>
                              </div> */}
                            <div className="changeSize" onClick={this.sizeAreaClickEvent.bind(this)}>
                                调整尺寸
                            </div>
                        </div>
                    )}

                    {rt_is_online_detail_page && (
                        <div className="allCanvasSizeArea">
                            <div className="itemTitle">总尺寸</div>
                            <div className="item changeSizeArea">
                                <div className="allCanvasWidth changeSizeBtn">
                                    <span>{canvas.width}px</span>
                                    <span className="desc">宽</span>
                                </div>
                                <i className="margin_i"></i>
                                <div className="allCanvasHeight changeSizeBtn">
                                    <span>{canvas.height}px</span>
                                    <span className="desc">高</span>
                                </div>
                            </div>
                        </div>
                    )}

                    {!isDesigner && (
                        <div style={{ display: 'none' }}>
                            <PageAnimation />
                        </div>
                    )}
                    {/* <div className="item">
                        <div className="itemTitle">参考线</div>
                        <CanvasGuides></CanvasGuides>
                    </div> */}
                    {/*<MoreColorsPanel />*/}
                    {!isDesigner && rt_mutisize_user_show && !info.dont_show_more_size && isShowUserMutSize && (
                        <UserMutiSize />
                    )}

                    {/* 动效延长时间组件 */}
                    {this.hasVideoType(pageAttr, pageInfo.pageNow) && <SetPageTime />}
                    <div className="defaultTopicContainer">背景</div>
                    <div className="background-image-title">背景图片</div>
                    {
                        // (!isDesigner && rt_isMutiSizeTemplate && rt_mutisize_user_show && !info.dont_show_more_size) && <UserMutiSize/>
                        !props.GroupWordUser && (
                            <>
                                <div className="userUploadAreaArea">
                                    {(backgroundSelectImage || backgroundImageShow?.attribute?.picUrl) && (
                                        <div className="thumbnail_image">
                                            <div className={'thumbnail_hover_box'}>
                                                <div
                                                    className="thumbnail_hover_btn"
                                                    onClick={() =>
                                                        this.delBackgroundAsset(pageInfo.pageNow, backgroundIndex)
                                                    }
                                                >
                                                    删除背景
                                                </div>
                                                <div
                                                    className="thumbnail_hover_btn"
                                                    onClick={() =>
                                                        this.onEditBackground(backgroundImageShow, pageInfo.pageNow)
                                                    }
                                                >
                                                    编辑背景
                                                </div>
                                            </div>
                                            <div className={'thumbnail_image_position'}>
                                                <img
                                                    style={{ borderRadius: 4 + 'px' }}
                                                    src={
                                                        backgroundImageShow?.attribute?.picUrl
                                                            ? backgroundImageShow?.attribute?.picUrl
                                                            : backgroundSelectImage
                                                    }
                                                    alt=""
                                                />
                                            </div>
                                        </div>
                                    )}
                                    <UserUploadArea
                                        isActive={this.props.isShow}
                                        isScroll={false}
                                        backgroundSelectImage={backgroundSelectImage}
                                        addBackgroundStatus={true}
                                    />
                                </div>
                                <div style={{ marginTop: '10px', overflowX: 'visible' }}>
                                    {backgroundImageShow && (
                                        <BackgroundCustomCategory
                                            backgroundAsset={backgroundImageShow}
                                        ></BackgroundCustomCategory>
                                    )}
                                </div>
                                {rt_canvas_render_mode !== 'board' && <div className="item changeBackgroundColor" style={{ marginTop: '14px' }}>
                                    <div className="itemTitle">背景颜色</div>
                                    <div className="changeBackgroundColorContent">
                                        <div
                                            className="colorBlock"
                                            style={colorBlockStyle}
                                            onClick={(e) => {
                                                this.changeBackgroundColorContentClickEvent(e, 'color');
                                            }}
                                        ></div>
                                        <i
                                            className="icon-bgimage iconfont icon-beijingtuan-01"
                                            onClick={(e) => {
                                                assetManager.setPv_new(8369);
                                                this.changeBackgroundColorContentClickEvent(e, 'texture');
                                            }}
                                        ></i>

                                        {isColorsArea && backgroundSelected == 'color' && (
                                            <MoreColorsPanel
                                                metaType={'background'}
                                                color={work.pages[pageInfo.pageNow].backgroundColor}
                                                style={{
                                                    top: '60px',
                                                    boxShadow: 'none',
                                                    boxsShadow: 'none',
                                                    width: '190px',
                                                    height: '320px',
                                                    padding: 0,
                                                }}
                                                onChange={this.onChange}
                                                isTranparentBgColor={true}
                                                commonColorsAreaStyle={{ padding: '0' }}
                                                colorsAreaStyle={{ marginLeft: '0', width: '100%' }}
                                            />
                                        )}
                                        {/* <MoreColorsPreinstall dropdownStyle={{top: '45px'}} btnStyle={{}}
                                                  onCallback={this.onChange}
                                                  closeColorSelect = {()=>this.setState({isColorsArea : false})}
                            /> */}
                                        {isColorsArea &&
                                            backgroundSelected == 'texture' &&
                                            backgroundTextureList.length > 0 && (
                                                <div className="colorsArea backgroundColorWrap">
                                                    <div className={'backgroundSelectNav'}>
                                                        {/* <p className={classNames('navItem', {active: backgroundSelected == 'color'})}
                                    onClick={this.backgroundSelectNavItemClickEvent.bind(this, 'color')}>颜色</p> */}
                                                        <p
                                                            className={classNames('navItem', {
                                                                active: backgroundSelected == 'texture',
                                                            })}
                                                            onClick={this.backgroundSelectNavItemClickEvent.bind(
                                                                this,
                                                                'texture',
                                                            )}
                                                        >
                                                            背景图案
                                                        </p>
                                                        {/* <div className="switch-block" style={ backgroundSelected == 'color' ? {} : { left:'94px' }}></div> */}
                                                    </div>

                                                    {backgroundSelected == 'texture' &&
                                                        backgroundTextureList.length > 0 && (
                                                            <div className={'backgroundTextureWrap'}>
                                                                {backgroundTextureList.map((item) => {
                                                                    let itemStyle = {
                                                                        backgroundImage: `url(${item.sample})`,
                                                                    };
                                                                    return (
                                                                        <div
                                                                            className={classNames('textureItem', {
                                                                                active:
                                                                                    item.id ==
                                                                                    backgroundTextureSelected,
                                                                            })}
                                                                            key={item.id}
                                                                            style={itemStyle}
                                                                            onClick={this.textureItemClickEvent.bind(
                                                                                this,
                                                                                item,
                                                                            )}
                                                                        />
                                                                    );
                                                                })}
                                                            </div>
                                                        )}
                                                </div>
                                            )}
                                    </div>
                                </div>}
                            </>
                        )
                    }

                    {/* {!isDesigner && !props.GroupWordUser && <div className="userUploadAreaArea">
                        <UserUploadArea isActive = {this.props.isShow} isScroll={false}/>
                    </div>} */}
                    {/* 因为设计师模式暂时没有上传图片button, 这里加一个div撑开元素 */}
                    {isDesigner && <div style={{ width: '260px' }} />}
                    {props.GroupWordUser && (
                        <div className="backgroundPanelArea">
                            <BackgroundPanel isScroll={true} />
                        </div>
                    )}
                    {isAIDesign() && (
                        <>
                            <TemplateLoader />
                            <TextRectSwitch
                                defaultChecked={isAIDesign()}
                                onChange={(check) => {
                                    emitter.emit('TextRectSwitchListener', check);
                                }}
                            />
                            <MaskGenerate></MaskGenerate>
                        </>
                    )}
                    {pageInfo.pageNow >= 0 && (isAIDesignPPT() || designerPPT) && (
                        <PPTPageMarking
                            onChange={(item) => {
                                if (!pageAttr.pageInfo) {
                                    // 没有就创建一个
                                    CanvasPaintedLogic.updatePageAttrPageInfo({
                                        pageInfo: new Array(work.pages.length).fill({
                                            pageTime: 5000,
                                        }),
                                        fun_name: 'UPDATE_PAGE_ATTR_PAGE_INFO',
                                    });
                                }
                                CanvasPaintedLogic.updatePageMark({
                                    rt_page_ppt_mark: item.key,
                                    fun_name: 'UPDATE_PAGE_PPT_MARK',
                                });
                            }}
                            currentMark={pageAttr?.pageInfo?.[pageInfo.pageNow]?.rt_page_ppt_mark ?? ''}
                        />
                    )}
                </div>
            </div>
        );
    }
}

export {ToolPanelDefault};
