


.commonColorPanel{
    *{padding:0;border:0;margin:0;list-style: none;}
    width: 270px;
    height: calc(100vh - 56px);
    padding:0 20px;
    box-sizing: border-box;
    position: absolute;
    top: 56px;
    right: 0;
    bottom: 0;
    z-index: 10000;
    background: #fff;
    font-family: initial;
    border-radius: 16px 0 0 16px;
    .colorSelector{
        left: auto;
        right: 0;
        top:0
    }
    h2{
        height: 55px;
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        font-size: 16px;
        font-weight: 500;
        color:#000000;
        // border-bottom: 1px solid #E9E8E8;
        i{
            margin-right: 15px !important;
            cursor: pointer;
        }
    }
    .cutBox{
        position: relative;
        width: 240px;
        height: calc(100vh - 57px - 75px);
        overflow-y: auto;
        overflow-x: hidden;
        margin-left: -20px;
        padding-left: 20px;
        &:hover{
            &::-webkit-scrollbar{
                display: block;
            }
        }
        &::-webkit-scrollbar{
            display: none;
        }
    }
    .colorContent{
        position: absolute;
        top:0;
        left: 20px;
        width: 220px;
    }
    .colorsBox{
        padding-bottom: 10px;
        border-bottom: 1px solid #E9E8E8;
        padding-bottom: 17px;
        margin-bottom: 25px;
        line-height: initial;
        &:last-child{
            border-bottom: unset;
        }
        &.default{
            h3{
                margin-bottom: 5px;
            }
        }
        h3{
            margin-bottom: 10px;
            color: #000000;
            font-size: 14px;
            font-weight: 500;
        }
        .subtitle{
            margin-bottom: 5px;
            color: #4C4849;
            font-size: 12px;
        }
        .picScale{
            display: flex;
            align-items: center;
            span{
                font-size: 12px;
                color: #4C4849;
            }
            .range{
                //修改滑块的颜色和样式
                &::-webkit-slider-thumb {
                    background-color: #EF3964;
                    width: 10px;
                    height: 10px;
                }
                //修改滑轨的颜色和样式
                &::-webkit-slider-runnable-track {
                    background-color: #EF3964;
                    height: 4px;
                }
                //修改滑块的边框样式
                &::-webkit-slider-thumb {
                    border: 1px solid #EF3964;
                }
                //修改滑轨的边框样式
                &::-webkit-slider-runnable-track {
                    border: 1px solid #000;
                }
                //修改滑块的颜色和样式（适用于 Firefox）
                &::-moz-range-thumb {
                    background-color: #EF3964;
                    width: 10px;
                    height: 10px;
                  }
            }
        }
        ul{
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            li{
                position: relative;
                width: 37px;
                height: 37px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 8px;
                margin-bottom: 8px;
                border:1px solid #E9E8E8;
                border-radius: 5px;
                box-sizing: border-box;
                cursor: pointer;
                background-image: url('https://s.tuguaishou.com/image/picEditor/opcaityBg.png');
                background-size: cover;
                &.moreColors{
                    background:url('https://s.tuguaishou.com/index_img/editorV6.0/icon_colorPicker.png') no-repeat center;
                    background-size: contain;
                }
                &.textureItem{
                    width: 49px;
                    height: 49px;
                    margin-right: 8px;
                    margin-bottom: 8px;
                    &:nth-child(5n){
                        margin-right: 8px;
                    }
                    &:nth-child(4n){
                        margin-right: 0;
                    }
                }
                &:nth-child(5n){
                    margin-right: 0;
                }
                &:hover{
                    b{
                        display: flex;
                        &:hover{
                            display: none;
                        }
                    }
                }
                &.active{
                    border:2px solid #EF3964;
                    .coverColor{
                        // 1像素的内阴影
                        box-shadow: inset 0 0 1px 1px #fff;
                    }
                    &:hover{
                        i{
                            display: flex;
                        }
                    }
                }
                .coverColor{
                    position: absolute;
                    top:0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    border-radius: 5px;
                }
                .opacityLine{
                    position: absolute;
                    width: 46px;
                    height: 1px;
                    background-color: #EF3964;
                    transform: rotate(-45deg);
                }
                i{
                    color: #000;
                    font-size: 16px;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-color: #fff;
                    border-radius: 50%;
                    border:1px solid #E9E8E8;
                    display: none;
                    z-index: 10;
                }
                b{
                    position: absolute;
                    // width: 75px;
                    padding:0 10px;
                    height: 26px;
                    left: 50%;
                    transform: translateX(-50%);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 5px;
                    background: #1F1A1B;
                    color: rgba(255, 255, 255, 0.9);
                    font-size: 12px;
                    font-weight: 400;
                    bottom: -35px;
                    z-index: 10;
                    white-space:nowrap;
                    display: none;
                    // 黑色实心倒三角
                    &:after{
                        content: '';
                        position: absolute;
                        top: -13px;
                        left: 50%;
                        transform: translateX(-50%);
                        width: 0;
                        height: 0;
                        border: 7px solid transparent;
                        border-bottom-color: #1F1A1B;
                    }
                }
            }
        }
    }

    .gc__repeat-section{
        position: relative;
        margin-top: 10px;
        .slideBarGroup{
            align-items: center;
            display: flex;
            .slideBarType{
                color: #4C4849;
                font-size: 12px;
                width: auto;
                margin-right: 15px;
            }
            .slideBar{
                border: 0;
                cursor: pointer;
                left: unset;
                padding: 5px 0;
                position: relative;
                top: unset;
                width: 120px;
                margin-right: 15px;
                .slideBarButtonBar{
                    position: absolute;
                    background: #ff4555;
                    border: 0;
                    height: 2px;
                    top: 4px;
                }
                .slideBarButton{
                    left: 0;
                    position: absolute;
                    background: #ff4555;
                    box-shadow: 0 1px 3px 1px rgba(255,69,85,.3);
                    cursor: pointer;
                    height: 12px;
                    top: -1px;
                    width: 12px;
                    border-radius: 50%;
                }
                .slideBarMask{
                    position: absolute;
                    background: rgba(255,69,85,.2);
                    border: 0;
                    border-radius: 1px;
                    height: 2px;
                    top: 4px;
                    width: inherit;
                }
            }
            .input_wrapper{
                color: #333;
                font-size: 12px;
                display: inline-block;
                position: relative;
            }
        }
    }

    .gradientColor{
        position: absolute;
        background: #fff;
        border-radius: 5px;
        z-index: 10;
        .gradientShow{
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 16px;
            span{
                width: 12px;
                height: 12px;
                border:4px solid #fff;
                border-radius: 50%;
            }
        }
    }

    .replaceColorsBox{
        width: 220px;
        height: 100px;
        position: sticky;
        bottom: 0;
        margin-top: -25px;
        background-color: #ffffff;
        box-shadow: 0px -1px 0px 0px rgba(0, 0, 0, 0.1);

        h3 {
            color: #000000;
            font-size: 14px;
            font-weight: 500;
            line-height: 40px;
        }
    }
    .replace-AllTextColor-box {
        display: flex;
        height: 40px;
        width: 100%;
        justify-content: space-between;
    }
    .replace-AllTextColor-block {
        height: 100%;
        width: 45%;
        display: flex;
        align-items: center;
        justify-content: space-around;
        background-color: #f1f2f4;
        border-radius: 5px;
    }
    .replace-AllTextColor-cover {
        width: 30px;
        height: 30px;
        border-radius: 5px;
    }
    .replace-AllTextColor-button {
        width: 55%;
        margin-left: 5px;
        color: #f7f7f7;
        background-color: #ff3f70;
        font-size: 12px;
        text-align: center;
        border-radius: 5px;
        line-height: 40px;
    }

    .replace-AllTextColor-button:hover {
        cursor: pointer;
        background-color: #ec3965;
    }
}