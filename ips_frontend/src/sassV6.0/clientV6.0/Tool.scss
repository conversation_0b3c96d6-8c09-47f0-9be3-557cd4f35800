@import "Base";

.toolLayout {
  .blockTitle{
    font-size: 16px;
    font-weight: bold;
    color: #333;
  }

  .toolWrapper {
    box-sizing: border-box;
    width: $toolContentWidth;
    margin: 0 auto;
    padding: var(--AssetPanelPadding) 0 0 var(--AssetPanelPadding);
    height: 100%;
  &.toolWrapper_noPadding {
    padding: 0px;
  }
    .tool_tab_container {
      height: 100%;
    }

    .invisible {
      display: none;
    }

    .scroll_content_auto{
      margin-top: 20px;
      // margin-right: -20px;
      overflow-y:overlay;
      // &::-webkit-scrollbar {
      //   display: none;
      // }
      // &:hover {
      //     &::-webkit-scrollbar {
      //         display: block;
      //     }
      // }
    }
  }

  .-reactjs-scrollbar-thumb{
    width: 7px;
    border-radius: 3px;
    background: #ccc;
  }
  .-reactjs-scrollbar-area {
    .-reactjs-scrollbar-track {
      width: 7px;
    }
  }
}

.toolLayout{
  .dividingLine{
    @include dividingLine;
  }
}

.helperLayout{
  .dividingLine{
    @include dividingLine;
  }
}

$templateTitlePadding: 7px 0;
.textEditorBox{
  //background: #F7F7F7;

  .commonCopywritingBtn{
    margin-top: 20px;
    font-size: 12px;
    color: #666;
    text-decoration: underline;
    text-align: center;
    cursor: pointer;

    &:hover{
      color: $primaryColorV6_2;
    }
  }
  .dividingLine{
    width: 276px;
    margin: 0 auto;
    margin-top: 40px;
  }
  .textEditorAddBlock {
    //margin-bottom: 28px;
    display: inline-block;
    width: 100%;

    .addTemplateText{
      width: 276px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      font-size: 16px;
      font-weight: bold;
      color: #fff;
      border-radius: 4px;
      margin: 0 auto;
      margin-top: 16px;
      cursor: pointer;
      background: $primaryColorV6_2;


      i{
        margin-right: 4px;
      }
      &:hover{
        //border-color: $primaryColorV6_2;
        //color: $primaryColorV6_2;
      }
    }
    .textExtend{
      margin: 0 auto;
      text-align: center;
      opacity: 0.7;
      padding: $templateTitlePadding;
    }

    .templateTitle {
      @extend .textExtend;
      font-size: 24px;
      font-weight: bold;
    }

    .templateSubtitle {
      @extend .textExtend;
      font-size: 18px;
      font-weight: bold;
    }

    .templateText {
      @extend .textExtend;
      font-size: 14px;
    }

    .hoverArea:hover{
      background: #dddde3;
      cursor: pointer;
    }
  }

  .copywritingBtnArea{
    display: inline-block;
    margin-top: 30px;

    .btnItem{
      float: left;
      width: 155px;
      height: 30px;
      line-height: 30px;
      font-size: 18px;
      text-align: center;
      color: #666;
      cursor: pointer;
      font-weight: bold;

      i{
        font-size: 12px;
        margin-left: 5px;
      }
      p{
        display: inline;
        font-size: 14px;
      }
      &:hover, &.active{
        color: #f63;
      }
    }
  }

  /*常用文案（悬浮面板） START*/
  .copywritingBtnFloatPanel{
    width: 500px;
    height: 436px;
    //padding: 0 36px;
    border-radius: 4px;
    background: #fff;
    box-shadow:1px 2px 4px rgba(153,153,153,0.36);
    position: fixed;
    top: 50%;
    left: 368px;
    margin-top: -218px;

    .closeBtn{
      position: absolute;
      top: 0;
      right: -44px;
      width: 34px;
      height: 34px;
      line-height: 34px;
      background: RGBA(0, 0, 0, 0.3);
      text-align: center;
      border-radius: 34px;
      cursor: pointer;

      i{
        font-size: 15px;
        color: #fff;
      }
    }
    .areaTitle{
      font-size: 16px;
      color: #666;
      margin-top: 25px;
      text-align: center;
    }
    .areaTip{
      font-size: 12px;
      color: #999;
      margin-top: 10px;
      margin-bottom: 22px;
    }
    .generalInfoArea{
      display: inline-block;

      .infoItem{
        width: 208px;
        height: 32px;
        border: 1px solid #cecece;
        border-radius: 4px;
        position: relative;
        float: left;
        margin: 0 0 8px 8px;
        cursor: pointer;

        .infoContent{
          width: 148px;
          line-height: 32px;
          padding: 0 30px;
          color: #666;
          font-size: 12px;
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          i{
            font-size: 12px;
          }
        }
        .delItemBtn{
          position: absolute;
          top: 4px;
          right: 6px;
          color: #666;
          display: none;

          i{
            font-size: 12px;
          }
          &:hover{
            color: $primaryColorV6_2;
          }
        }
        .editItemBtn{
          position: absolute;
          top: 4px;
          right: 22px;
          color: #666;

          display: none;

          i{
            font-size: 12px;
          }
          &:hover{
            color: $primaryColorV6_2;
          }
        }
        .setGeneral{
          height: 32px;
          line-height: 32px;
          position: absolute;
          top: 0px;
          right: 4px;
          font-size: 14px;
          color: $primaryColorV6_2;
          cursor: pointer;
          display: none;
        }
        .infoEditor{
          display: none;

          .editorInput{
            width: 158px;
            height: 32px;
            line-height: 32px;
            border: 1px solid $primaryColorV6_2;
            border-radius: 4px;
            outline: none;
            padding: 0 5px;
          }
          .editorBtnArea{
            position: absolute;
            top: 0;
            right: 0;

            .addBtn, .cancelBtn{
              width: 32px;
              height: 16px;
              line-height: 16px;
              background: $primaryColorV6_2;
              color: #fff;
              font-size: 12px;
              border-radius: 4px;
              text-align: center;
              cursor: pointer;
            }
            .cancelBtn{
              background: #c8c8c8;
              margin-top: 2px;
            }
          }
        }
        &:nth-child(2n +1){
          margin-left: 0px;
        }
        &:hover{
          background: #eee;
          border-color: transparent;

          .delItemBtn{
            display: inline-block;
          }
          .editItemBtn{
            display: inline-block;
          }
          .setGeneral{
            display: inline-block;
          }
        }
        &.active{
          background: transparent;
          border-width: 0;
          cursor: default;
          height: 34px;

          .infoDefault{
            display: none;
          }
          .infoEditor{
            display: block;
          }
        }
      }
    }

    .historyRecordArea{
      margin-top: 8px;

      .historyRecordTitle{
        font-size: 16px;
        text-align: center;
        color: #666;
      }
    }
  }
  /*常用文案（悬浮面板） END*/
}

// 文字面板显示历史记录
.historyRecordText{
  width: 296px;
  padding-bottom: 10px;
  //padding-bottom: 250px;
  margin: 0 auto;

  .historyRecordTextList{
    //padding-top: 5px;

    .historyRecordTextItem{
      position: relative;

      .textItemContent{
        width: 194px;
        height: 34px;
        line-height: 34px;
        text-align: center;
        font-size: 14px;
        color: #333;
        border: 1px solid #eee;
        background: #fafafa;
        margin-top: 8px;
        cursor: pointer;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 0 50px;
        border-radius: 2px;

        &:hover{
          background: #eee;
        }
      }
      .generalInfoPopu{
        display: none;
        width: 102px;
        position: absolute;
        right: 0px;
        top: 0px;
        padding: 10px;
        background: #fff;
        box-shadow: 0 1px 6px rgba(204, 204, 204, 1);
        z-index: 10;

        .generalInfoPopuTitle{
          p{
            display: inline;
          }
          .Ttext1{
            font-size: 14px;
            color: #333;
          }
          .Ttext2{
            font-size: 12px;
            color: #999;
            line-height: 0px;
          }
          .Ttext3{
            font-size: 12px;
            color: #ff6633;
          }
        }
        .hintList{
          .hintItem{
            display: block;
            margin-top: 10px;
            color: #666;
            font-size: 14px;
            cursor: pointer;

            &:hover{
              color: #ff6633;
            }
            input{
              margin-right: 5px;
            }
          }
        }
      }
      .generalInfo{
        position: absolute;
        top: 50%;
        right: 5px;
        margin-top: -10px;
        height: 20px;
        line-height: 20px;
        font-size: 12px;
        color: #999;
        cursor: pointer;
        display: none;

        &:hover{
          color: #ff6633;
        }
        &.active{
          + .generalInfoPopu{
            display: inline-block;
          }
        }
      }
      &:hover{

        .generalInfo{
          display: inline-block;
        }
      }
    }
  }
}

//常用消息
.generalInfoList{
  padding-bottom: 20px;
  //width: 310px;
  width: 296px;
  margin: 0 auto;

  .generalInfoListList{

    .generalInfoListListSub {
      //padding-top: 28px;
      position: relative;

      .generalInfoListListTitle{
        position: absolute;
        top: 16px;
        left: 0;
        font-size: 12px;
        color: #666;

      }
      .generalInfoListItem {
        position: relative;

        .textItemContent {
          width: 184px;
          height: 34px;
          line-height: 34px;
          text-align: center;
          font-size: 14px;
          color: #333;
          border: 1px solid #eee;
          background: #fafafa;
          margin-top: 8px;
          cursor: pointer;
          overflow: hidden;
          text-overflow:ellipsis;
          white-space: nowrap;
          padding: 0 55px;
          border-radius: 2px;

          p{
            padding-left: 15px;
            margin-left: -6px;
            position: relative;
            display: inline;
            i{
              position: absolute;
              left: 0;
              top: 0;
              height: 19px;
              line-height: 19px;
              font-size: 12px;
            }
          }
          .editorBtn{
            display: none;
            position: absolute;
            right: 32px;
            top: 0;
            height: 36px;
            line-height: 36px;
            color: #666;

            &:hover{
              color: #ff6633;
            }
          }
          .delBtn{
            display: none;
            position: absolute;
            right: 8px;
            top: 0;
            height: 36px;
            line-height: 36px;
            color: #666;

            &:hover{
              color: #ff6633;
            }
          }
          &:hover{
            background: #eee;
            .editorBtn{
              display: inline-block;
            }
            .delBtn{
              display: inline-block;
            }
          }
        }
      }

      .generalInfoListItemEditor {
        position: relative;

        .textItemContent {
          width: 288px;
          height: 34px;
          line-height: 34px;
          text-align: center;
          font-size: 14px;
          color: #ff6633;
          border: 1px solid #eee;
          background: #fafafa;
          margin-top: 28px;
          position: relative;
          overflow: hidden;
          text-overflow:ellipsis;
          white-space: nowrap;
          padding: 0 10px;
          border-radius: 2px;

          .textItemContentInputAdd{
            width: 100%;
            height: 34px;
            line-height: 34px;
            border: 0 solid #eee;
            outline: none;
            background: none;
          }
        }
        .generalInfoPopu{
          display: none;
          width: 102px;
          position: absolute;
          right: 0px;
          top: 0px;
          padding: 10px;
          background: #fff;
          box-shadow: 0 1px 6px rgba(204, 204, 204, 1);
          z-index: 10;

          .generalInfoPopuTitle{
            p{
              display: inline;
            }
            .Ttext1{
              font-size: 14px;
              color: #333;
            }
            .Ttext2{
              font-size: 12px;
              color: #999;
              line-height: 0px;
            }
            .Ttext3{
              font-size: 12px;
              color: #ff6633;
            }
          }
          .hintList{
            .hintItem{
              display: block;
              margin-top: 10px;
              color: #666;
              font-size: 14px;
              cursor: pointer;

              &:hover{
                color: #ff6633;
              }
              input{
                margin-right: 5px;
              }
            }
          }
        }
        .selectCateArea{
          right: 0;
          top: -20px;
          font-size: 12px;
          color: #666;
          position: absolute;
          height: 20px;
          line-height: 20px;
          cursor: pointer;

          &.active{
            + .generalInfoPopu{
              display: inline-block;
            }
          }
        }
        .textItemBtnArea{
          margin-top: 2px;
          display: inline-block;
          width: 100%;

          .textItemBtnSure{
            width: 58px;
            height: 24px;
            line-height: 24px;
            text-align: center;
            background: #ff6633;
            color: #fff;
            font-size: 14px;
            border-radius: 12px;
            float: right;
            cursor: pointer;

            &:hover{
              background: #ff7744;
            }
          }
          .textItemBtnCancel{
            width: 58px;
            height: 24px;
            line-height: 24px;
            text-align: center;
            background: #ccc;
            color: #fff;
            font-size: 14px;
            border-radius: 12px;
            float: right;
            margin-right: 7px;
            cursor: pointer;

            &:hover{
              background: #cfcfcf;
            }
          }
        }
      }
    }

    .addGeneralInfoListItem {
      position: relative;

      .textItemContent {
        width: 274px;
        height: 34px;
        line-height: 34px;
        text-align: center;
        font-size: 14px;
        color: #ff6633;
        border: 1px solid #efd5c6;
        background: #fcefe3;
        margin-top: 8px;
        cursor: pointer;
        overflow: hidden;
        text-overflow:ellipsis;
        white-space: nowrap;
        padding: 0 10px;
        border-radius: 2px;

        &:hover{
          background: #fdf6f0;
        }
        p{
          padding-left: 15px;
          margin-left: -6px;
          position: relative;
          display: inline;
          i{
            position: absolute;
            left: 0;
            top: 0;
            height: 19px;
            line-height: 19px;
            font-size: 12px;
          }
        }
      }
    }
    .addGeneralInfoListItemArea {
      position: relative;

      .textItemContent {
        width: 288px;
        height: 34px;
        line-height: 34px;
        text-align: center;
        font-size: 14px;
        color: #ff6633;
        border: 1px solid #eee;
        background: #fafafa;
        margin-top: 28px;
        position: relative;
        overflow: hidden;
        text-overflow:ellipsis;
        white-space: nowrap;
        padding: 0 10px;
        border-radius: 2px;

        .textItemContentInputAdd{
          width: 100%;
          height: 34px;
          line-height: 34px;
          border: 0 solid #eee;
          outline: none;
          background: none;
        }
      }
      .generalInfoPopu{
        display: none;
        width: 102px;
        position: absolute;
        right: 0px;
        top: 0px;
        padding: 10px;
        background: #fff;
        box-shadow: 0 1px 6px rgba(204, 204, 204, 1);
        z-index: 10;

        .generalInfoPopuTitle{
          p{
            display: inline;
          }
          .Ttext1{
            font-size: 14px;
            color: #333;
          }
          .Ttext2{
            font-size: 12px;
            color: #999;
            line-height: 0px;
          }
          .Ttext3{
            font-size: 12px;
            color: #ff6633;
          }
        }
        .hintList{
          .hintItem{
            display: block;
            margin-top: 10px;
            color: #666;
            font-size: 14px;
            cursor: pointer;

            &:hover{
              color: #ff6633;
            }
            input{
              margin-right: 5px;
            }
          }
        }
      }
      .selectCateArea{
        right: 0;
        top: -20px;
        font-size: 12px;
        color: #666;
        position: absolute;
        height: 20px;
        line-height: 20px;
        cursor: pointer;

        &.active{
          + .generalInfoPopu{
            display: inline-block;
          }
        }
      }
      .textItemBtnArea{
        margin-top: 2px;
        display: inline-block;
        width: 100%;

        .textItemBtnSure{
          width: 58px;
          height: 24px;
          line-height: 24px;
          text-align: center;
          background: #ff6633;
          color: #fff;
          font-size: 14px;
          border-radius: 12px;
          float: right;
          cursor: pointer;

          &:hover{
            background: #ff7744;
          }
        }
        .textItemBtnCancel{
          width: 58px;
          height: 24px;
          line-height: 24px;
          text-align: center;
          background: #ccc;
          color: #fff;
          font-size: 14px;
          border-radius: 12px;
          float: right;
          margin-right: 7px;
          cursor: pointer;

          &:hover{
            background: #cfcfcf;
          }
        }
      }
    }
  }
}

//文字工具栏-艺术字（动画）
@keyframes wordArt_searchBarFloat
{
  from {top: 0;}
  to {top: 66px;}
}
@keyframes wordArt_backToTop
{
  from {bottom: -34px;}
  to {bottom: 10px;}
}
//文字工具栏-艺术字
#wordArt{
  margin-top: 15px;
  padding: 0 10px;
  width: 256px;

  .wordArtTitle{
    font-size: 18px;
    color: #444;
    text-align: center;
    font-weight: bold;
  }
  .wordArtTip{
    font-size: 12px;
    color: #999;
    //margin-top: 10px;
  }
  .searchBarArea {
    width: 256px;
    height: 37px;
    margin: 0 auto;
    margin-top: 10px;

    .searchBarFloat {
      background: #fff;
      z-index: 100;
      top: 66px;
      left: 70px;

      &.active{
        position: fixed;
        animation: wordArt_searchBarFloat 0.5s;
        padding: 15px;
      }
      .searchBar {
        width: 232px;
        //@include line(1, 1, 1, 1, #e2e2e2);
        border-radius:2px;
        //box-shadow:1px 2px 4px rgba(153,153,153,0.24);
        padding: $searchBarInputPadding;
        background: #fff;
        position: relative;
        border: 1px solid #F2F2F2;

        .searchInput {
          width: $searchBarInputWidth;
          height: 23px;
          line-height: 23px;
          font-size: $searchBarInputFontSize;
          color: #999;
          border: none;
          outline: none;
        }
        i {
          position: absolute;
          right: 5px;
          top: 50%;
          margin-top: -11px;
          //margin-left: $searchBarIconMarginLeft;
          font-size: $searchBarIconSize;
          cursor: pointer;
          &:hover {
            color: $fgColor;
          }
        }
      }
    }
  }
  .hotWordArea{
    font-size: 14px;
    color: #777;
    margin-top: 10px;

    .hotWordItem{
      display: inline;
      margin-left: 5px;
      cursor: pointer;

      &:hover{
        color: #f63;
      }
    }
  }
  .assetListArea{
    position: relative;
    width: 288px;
    margin-top: 12px;

    .assetItem {
      //display: inline-block;
      cursor: pointer;
      position:relative;
      //box-shadow: 0px 3px 6px rgba(0,0,0,0.4);
      background-color: #F7F7F7;
      box-shadow:0px 1px 4px 0px rgba(153,153,153,0.36);
      border-radius: 2px;
      overflow: hidden;

      .elementBg{
        right: 0;
        bottom: 0;
        left: 0;
        top: 0;
        position: absolute;
        z-index: 2;
        background:rgba(51, 51, 51, 0.6);
        background-size: cover;
        display: none;

      }
      &:hover{
        .elementBg{
            display: block
          }
      }
      .elementFg{
        right: 0;
        bottom: 0;
        left: 0;
        top: 0;
        position: absolute;
        z-index: 3;
        background-size: cover;
      }
      .elementHover{
        display:none;
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.2);
        z-index: 4;
      }
      .elementInfo{
        display:none;
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        top: 0;
        z-index: 5;
        color: $bgColor2;
        font-size: $textSize1;
        text-align: center;
        padding-top: 10px;

        .elementInfoTitle{
          position: absolute;
          left: 10px;
          right: 40px;
          overflow: hidden;
          text-overflow:ellipsis;
          white-space: nowrap;
        }
        .assetFavBtn{
          width: 26px;
          height: 26px;
          line-height: 28px;
          border-radius: 13px;
          text-align: center;
          position: absolute;
          top: 6px;
          right: 5px;
          background: #fff;

          i{
            font-size: 16px;
            color: #666;
          }
          &:hover, &.active{
            i{
              color: $primaryColorV6_2;
            }
          }
          &.active{
            i{
              &::before{
                content: "\e605";
              }
            }
          }
        }

        .elementTJ{
          display: block;
          position: absolute;
          left: 0;
          bottom: 5px;
          width: 100%;
          text-align: center;
          a{
            color: #fff;
            text-decoration: none;
          }
        }
      }
      &:hover .elementHover,&:hover .elementInfo {
        display: block;
      }
      &:hover .assetAction{
        display: block;
      }
      .assetAction {
        display: none;
        position: absolute;
        height: 26px;
        line-height: 26px;
        bottom:0;
        left:0;
        right:0;
        z-index: 6;
        &.active {
          /*display:block;*/
        }
        &.inactive {
          display:none;
        }
        .hover {
          position: absolute;
          right: 0;
          bottom: 0;
          left: 0;
          top: 0;
          background: $fgColor2;
          opacity: .6;
          z-index: 7;
        }
        .command {
          position: absolute;
          right: 0;
          bottom: 0;
          left: 0;
          top: 0;
          z-index: 8;
          color: $white;
          a{
            float:right;
            margin-right:10px;
            &:hover{
              color: $red1;
            }
          }
        }
      }/*end of assetAction*/
      .checkbox {
        position:absolute;
        z-index:10;
        top:0;
        bottom:0;
        left:0;
        right:0;
        background: black;
        opacity: .5;
        &.active {
          display:block;
        }
        &.inactive {
          display:none;
        }
        .checkboxItem {
          margin: 5px;
          width: 20px;
          height: 20px;
        }
      }
    }
  }

  //置顶
  .backToTop{
    position: fixed;
    bottom: 10px;
    left: 303px;
    z-index: 100;
    width: 36px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    background: #f63;
    border-radius: 4px;
    box-shadow: 0 0 6px rgba(0,0,0,0.3);
    cursor: pointer;
    display: none;

    i{
      color: #fff;
      font-size: 22px;
    }
    &:hover{
      background: #f74;
    }
    &.active{
      display: inline-block;
      animation: wordArt_backToTop 0.5s;
    }
  }

  /*下拉弹窗 START*/
  .dropdownBox{
    animation: dropdownBoxAnimation 0.5s;
    background: RGBA(247, 247, 247, 1);
    width: 300px;
    display: inline-block;
    position: fixed;
    top: 58px;
    left: 50px;
    box-shadow: 0 2px 1px rgba(0, 0, 0, 0.2);
    padding-bottom: 34px;
    z-index: 1000;
    display: inline-block;

    .dropDownBoxInputArea{
      margin: 25px auto 0;
      width: 276px;
      height: 36px;
      background: #fff;
      box-shadow: 0px 1px 6px rgba(153, 153, 153, 0.6);
      position: relative;

      input{
        height: 36px;
        line-height: 36px;
        border: 0 solid #fff;
        outline: none;
        color: #666;
        font-size: 12px;
        padding-left: 15px;
        width: 235px;

        &::placeholder{
          color: #ccc;
        }
      }
      .icon{
        position: absolute;
        right: 5px;
        top: 50%;
        margin-top: -11px;
        font-size: 20px;
        cursor: pointer;
      }
    }
    .dropDownBoxBottomArea{
      margin-top: 22px;
      padding: 0 12px;
      position: relative;

      .dropDownBoxTagArea{
        position: relative;
        display: inline-block;

        .nowType{
          width: 226px;
          height: 28px;
          line-height: 28px;
          background: #fff;
          box-shadow: 0px 1px 6px rgba(153, 153, 153, 0.6);
          color: #666;
          font-size: 12px;
          padding-left: 16px;
          cursor: pointer;

          i{
            position: absolute;
            right: 12px;
            top: 2px;
          }
        }

        .typeList{
          position: absolute;
          top: 35px;
          left: 0;
          width: 242px;
          box-shadow: 0px 1px 6px rgba(153, 153, 153, 0.6);
          display: inline-block;

          .typeItem{
            height: 28px;
            line-height: 28px;
            color: #666;
            font-size: 12px;
            padding-left: 16px;
            background: #fff;
            cursor: pointer;

            &:hover{
              background: #eee;
            }
          }
        }
      }
      .dropDownBoxClose{
        position: absolute;
        top: 0;
        right: 12px;
        width: 28px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        background: rgba(153, 153, 153, 0.2);
        border-radius: 14px;
        font-size: 12px;
        cursor: pointer;
      }
    }
  }
  /*下拉弹窗 END*/
}

.vip-info-line{
  padding: 15px 0 0px 0px;
  font-size: 12px;
  letter-spacing: -0.3px;
  .vip-tag{
    width: 56px;
    height: 16px;
    background: linear-gradient(139deg, #f4dcbc 0%, #eacea4 100%);
    border-radius: 2px;
    font-family: SourceHanSansCN-Medium, SourceHanSansCN;
    font-weight: 500;
    color: #512900;
    line-height: 16px;
    margin: 0 2px;
    cursor: pointer;
    padding: 2px 4px;
  }
}