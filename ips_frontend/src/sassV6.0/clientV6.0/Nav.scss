@import "Base";

.navLayout {

  .navWrapper {
    width: $navLayout;
    margin: 0 auto;
    height: 100%;
    //background: $bgColor;
    // background: #fff;
    //@include line(0,0,1,0, $lineColor);
    border-right: 1px solid #F1F3F7;
    overflow-y: overlay;
    overflow-x: hidden;
    height: calc(100% - 10px);

    a {
      position: relative;
      display:inline-block;
      text-align:center;
      //padding: 25px 39px 15px;
      width: $navLayout - 8;
      height: 46px;
      cursor: pointer;
      margin-top: 20px;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;

      & .nav_badges{
        position: absolute;
        font-size: 12px;
        color: #fff;
        padding: 3px;
        border-radius: 5px;
        background-color: #ff4555;
        top: -20px;
        left: 45px;
        white-space: nowrap;
        &::after{
          content: '';
          position: absolute;
          border-top: 5px solid #ff4555;
          border-left: 5px solid transparent;
          border-right: 5px solid transparent;
          left: -3px;
          bottom: -1px;
          transform: rotate(136deg);
        }
      }
      &:first-child{
        margin-top: 25px;
      }
      &:hover, &.active {
        border-left-color: $primaryColorV6_2;
        i,.navText {
          // color: $primaryColorV6_2;
          color: #ff4555;
        }
        .icon-specificWord{
          background-image: url($imgHost + '/index_img/editorV7.0/navIcon/specialWordNewHover.png');
        }
        .new1 {
          background-image: url($imgHost + '/index_img/editorV7.0/navIcon/3DiconHover.svg');
        }
        .icon-sourceMaterial{
          background-image: url($imgHost + '/index_img/editorV7.0/navIcon/sourceMaterialHover1.png');
        }
        .icon-pic{
          background-image: url($imgHost + '/index_img/editorV7.0/navIcon/photoNewHover.png');
        }
        .icon-table-img{
          background-image: url($imgHost + '/index_img/editorV7.0/navIcon/tableNewHover.png');
        }
        .icon-gallery{
          background-image: url($imgHost + '/index_img/editorV7.0/navIcon/backgroundNewHover.png');
        }
        .icon-upload{
          background-image: url($imgHost + '/index_img/editorV7.0/navIcon/uploadNewHover.png');
        }

      }
      //&:before{
      //  content: '';
      //  border-left: 1px solid $lineColor;
      //  height: 52px;
      //  width: 1px;
      //  position: absolute;
      //  right: 0;
      //  top: 18px;
      //
      //}
      &:last-child{
        &:before {
          border-left: 0px solid #e6e6e6;
        }
      }
      i {
        color: #202020;
        font-size: 26px;
        display: inline-block;
        // width: 24px;
        // height: 24px;
        width: 26px;
        height: 26px;
        margin: 0 auto;
      }
      .iconV7_0{
        width: 26px;
        height: 26px;
        background-size: 26px 26px;
      }
      .icon-background{
        background-image: url($imgHost + '/index_img/editorV7.0/navIcon/background.svg');
        
      }
      .icon-upload{
        // background-image: url($imgHost + '/index_img/editorV7.0/navIcon/upload.svg');
        background-image: url($imgHost + '/index_img/editorV7.0/navIcon/uploadNew.png');
        &:hover,&.active{
          background-image: url($imgHost + '/index_img/editorV7.0/navIcon/uploadNewHover.png');
        }
      }
      .icon-pic{
        // background-image: url($imgHost + '/index_img/editorV7.0/navIcon/pic.svg');
        background-image: url($imgHost + '/index_img/editorV7.0/navIcon/photoNew.png');
        &:hover,&.active{
          background-image: url($imgHost + '/index_img/editorV7.0/navIcon/photoNewHover.png');
        }
      }
      .icon-svg{
        background-image: url($imgHost + '/index_img/editorV7.0/navIcon/svg.svg');
      }
      .icon-png{
        background-image: url($imgHost + '/index_img/editorV7.0/navIcon/png.svg');
      }
      .icon-artWord{
        background-image: url($imgHost + '/index_img/editorV7.0/navIcon/artWord.svg');
      }
      .icon-groupWord{
        background-image: url($imgHost + '/index_img/editorV7.0/navIcon/groupWord.svg');
      }
      .icon-specificWord{
        // background-image: url($imgHost + '/index_img/editorV7.0/navIcon/specificWord.svg');
        background-image: url($imgHost + '/index_img/editorV7.0/navIcon/specialWordNew.png');
        &:hover,&.active{
          background-image: url($imgHost + '/index_img/editorV7.0/navIcon/specialWordNewHover.png');
        }
      }
      .new1 {
        // background-image: url($imgHost + '/index_img/editorV7.0/navIcon/3D.png');
        background-image: url($imgHost + '/index_img/editorV7.0/navIcon/3Dicon.svg');
        background-size: 26px 26px;
        &:hover,&.active{
          background-image: url($imgHost + '/index_img/editorV7.0/navIcon/3DiconHover.svg');
        }
      }
      .icon-gallery{
        // background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAmCAYAAACoPemuAAAAAXNSR0IArs4c6QAABYpJREFUWAntWF1sFFUUPufO7Oy2pShoaAsaSyOSWPGVPsmDkhiQaiytECU+IaiYGG0LLW2ZYKX0x/AiFvukJGBsE3QLEVFClYeGhMQHYwIoJiQmTTWCSNh2Z3Z2rt+d6Szbdtvuso3pQ29yd+7c83O/e86595wdogXa+P/AJU0zQsW0jQSXkSNPc4P5y1zresBkx75lZBTUEcuV5Mq5ZHy6YIsc+pYbzZ9mE5CdjcWkFZ6gcGgLaYJo3LpFSWc7N7Z/N5sce6BCxjcUDldlDUppVFtykjFyk7Vcf/BspkU8UCLyBUUimykevwOeGxQ2nibHuY3+ymzgBOnGS2QYVRS3iGw7+26BVxNF5FIDbDwtJHxLFZykSHgz2YkYCfEqxcQGjC9gvQdJ17+UPW0bM21IzQm471GSWbpvqpakqzSsINPU0kkpUOHwC5RwYuQkXuP3zTNsmrcRZ7Ww3pAHjkW/7MwMTpDk+0Q1AUXSJPl7oAyAgqWSyR28t/3rADi/Z96C9bZS3B6CW2E5gOs2nwvowRPROH8Np28JiQgCXVkqMeaBajj41dQVPHC2BctZP3jgBANcyyRw8wPswIGkPApQS+QJxNQWSthjcOEOzgAqAMnNHTcR01tpPP4jTuwyEnq//Kjt2YCePzCJAD1yJEIx9zPsvtqzlAtQe9tPBYvM9PTAJSI1PjhjGQJ2QB7e/4zizx8Yc5wSdzZi1zVkO//CUtu5fm5QAVhubr6JzdQg5r4nA5bTtA7ZX6tuvDybCn5BiC2oku44aclfc9XoWc5NnveOEfMKosp5AMYUBpDzCOSfEV+lxMZZebh1XS7gZGfrbtxr7dgZ4V48yXWmnb/FcM0i9/0Fa71Mln0FJ7KcQloUV8BT2YCT3W27yNA/Jl0LwZ29NMYfKLn5AEa4JnSkl9/Jjlf74IzV0Dwou5tnBSd7Wt9ATB1F1yDXS5evvINL2PGBSTktnWSz0xRPmjw3Hb5ONlXDrVdxQlfDrTOCk10AJfRPYCkN/MfoMYAaGEgGegUJzSbOAxuzUobc5DduMq8jsfvgIspyRhQxVxnQ1RPu24mYmgCVOEaXr+7hunugFA9yJV3EEXdhTqRiAMy2q1NohKCCL8D8KWBKKdz6mwfOspXlKigkBmWP+aSieaA0zQdl2Z9SeeWedEspHtU8U3kBqGn1SCEPYW5S7vO4pv8wKgubkvIcOda7cOE/01mgqKtlDdx1miLGWlyi11AsDOBa2YcEriOR91H5ure4ri7lvnQdKR96ee4Bu5jG0skzjAsxnzQSXG/+PQNHaloeanqCjPCgB87bMn7G7T6cvreDQE8xpw1SwNLm5n0o92xbSY9UfAgrVyA/nuH9Xd1zLeK7cnj4cdw/u8h1V6HPJePTdR2pKBHlqqpouoAcGamZqC7Uxes3oScwkOQ6BumhOEprA271ryo/tv+Arl4uK7sxIYEycXh4FU7IRSosrECMBfNzP9UhUfy2/TqvX39cCcjR0Z0oo/v89JQhVNVUJh/pOtHdu9dA3cAlJX8qXTqUvEgFBRUUi6n33JphKP7d6MdRZAgaHX3Tq4bHsgnUKUsVFa0FuOcx+7mi4B4TD993ae27fSkOjnKL2mRRTlZXCIKmyntNWxq8KoUZbB6Qs3qmy6ePsxKewpSS9wNwCnUhvC4Cy9ULixZbtFiuFsiVf0HHWKbslcsG0+XTx7noCHhT8gLVxNh9l9Z+SW7ja4+6sV2kNicvXVLi25bf8H1MP0eWFUNVgGyHLJ9tD6GsDqOyYT6FbUpmVv9uoqhSiBQtl65kxsfxiUoMBcA808lLlzah3G1AAi7Juh7TNAv8UVq+vIPXrMFXP6AbGSmE8hYk402geaVHsNCMT/XfQdNGUI8d4tLSCwHff2BIJAiO3V8FAAAAAElFTkSuQmCC");
        background-image: url($imgHost + '/index_img/editorV7.0/navIcon/backgroundNew.png');
        &:hover,&.active{
          background-image: url($imgHost + '/index_img/editorV7.0/navIcon/backgroundNewHover.png');
        }
      }
      .icon-emoji{
        background-image: url("data:image/png;base64,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");
      }
      .icon-tucaoAsset{
        background-image: url($imgHost + '/index_img/editorV7.0/navIcon/tucaoAsset.svg');
      }
      .icon-table-img{
        background-image: url($imgHost + '/index_img/editorV7.0/navIcon/tableNew.png');
        &:hover,&.active{
          background-image: url($imgHost + '/index_img/editorV7.0/navIcon/tableNewHover.png');
        }
      }
      .icon-sourceMaterial{
        background-image: url($imgHost + '/index_img/editorV7.0/navIcon/sourceMaterial1.png');
        &:hover,&.active{
          background-image: url($imgHost + '/index_img/editorV7.0/navIcon/sourceMaterialHover1.png');
        }
      }
      .navText {
        color: $ToolNavColor;
        font-size: 12px;
        margin-top: 4px;
      }
    }
    .redPointed{
      width: 5px;
      height: 5px;
      border-radius: 5px;
      background: #f00;
      position: absolute;
      right: 18px;
      top: 10px;
    }
    &::-webkit-scrollbar{
      width: 0px;
    }
    &::-webkit-scrollbar-thumb{
        border-radius: 10px;
        -webkit-box-shadow:inset 0 0 5px rgba(0,0,0,0.2);
        background-color: rgba(0,0,0,0.1);
    }
    &::-webkit-scrollbar-track{
        -webkit-box-shadow:inset 0 0 5px rgba(0,0,0,0.2);
        border-radius: 0;
        background-color: rgba(0,0,0,0.1);
    }

    .leftNavHitPopup {
      position: absolute;
      display: flex;
      align-items: center;
      left: 49px;
      z-index: 999;
      pointer-events: none;
      .left_arrow {
          width: 0px;
          height: 0px;
          border-width: 7px 11px;
          border-style: solid;
          border-color: transparent;
          border-right-color: #404040;
      }
      .hit_content {
          width: 83px;
          height: 32px;
          background: #404040;
          border-radius: 4px 4px 4px 4px;
          opacity: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-left: 8px;
          padding-right: 8px;
          p {
              text-align: center;
              line-height: 32px;
              font-size: 12px;
              font-family: PingFang SC-Regular, PingFang SC;
              font-weight: 400;
              color: #FFFFFF;
          }
          .shortcutNum {
              text-align: center;
              line-height: 18px;
              width: 18px;
              height: 18px;
              background: #666566;
              border-radius: 2px 2px 2px 2px;
              // opacity: 0.2;
              font-size: 14px;
              font-family: PingFang SC-Regular, PingFang SC;
              font-weight: 400;
              color: #FFFFFF;
          }
      }
    }
  }

  .opinionShowBtn{
    position: absolute;
    display: inline-block;
    text-align: center;
    width: 50px;
    cursor: pointer;
    bottom: 20px;
    left: 0;

    i {
      color: $ToolNavColor;
      font-size: 24px;
      display: inline-block;
      width: 24px;
      height: 24px;
      margin: 15px auto 0;
    }
    &:hover, &.active {
      //background: #fff;
      i,.navText {
        color: $primaryColorV6_2;
      }
    }
    .navText {
      color: $ToolNavColor;
      font-size: 14px;
      margin-bottom: 10px;
    }
  }
  .opinion2ShowBtn{
    position: absolute;
    display: inline-block;
    text-align: center;
    width: 50px;
    cursor: pointer;
    bottom: 100px;
    left: 0;

    i {
      color: $ToolNavColor;
      font-size: 24px;
      display: inline-block;
      width: 24px;
      height: 24px;
      margin: 15px auto 0;
    }
    &:hover, &.active {
      //background: #fff;
      i,.navText {
        color: $primaryColorV6_2;
      }
    }
    .navText {
      color: $ToolNavColor;
      font-size: 12px;
      margin-bottom: 10px;
    }
  }
}
