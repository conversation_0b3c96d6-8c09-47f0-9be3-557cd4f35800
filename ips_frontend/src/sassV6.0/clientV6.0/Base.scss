//域名
//$imgHost: 'http://localhost:3000';
//$imgHost: 'http://app.818ps.com:8080';
//$imgHost: 'http://ue.818ps.com:3000';
//$imgHost: 'http://ue.818ps.com';
$imgHost: '//s.tuguaishou.com';
$fontHost: '//js.tuguaishou.com';

/* 新增团队配色 */
@import '@editorConfig/index.scss';

//阴影
$boxShadowItem: 2px 2px 5px rgba(0, 0, 0, 0.2);

//配色
$primaryColor: #ff7671;
$primaryColorV6_0: #ff6633;
$primaryColorV6_2: rgba(255, 69, 85, 1);
// $primaryColorV6_2: rgba(255, 120, 126, 1);
$primaryColor_RGB_V6_2: 255, 69, 85;
$auxiliaryColorV6_2: rgba(250, 120, 126, 0.5);
$fgColor: #ff7671;
$fgColor4: #e1171f;
$fgColor2: #333333;
$fgColor3: #cccccc;
//$bgColor: #e7e7e7;
$bgColor: #F0F1F5;
$bgColor2: #ffffff;
$bgColor3: #fefefe;
$bgColor4: #999999;
$bgColor5: #f8f9fb;
$fontColorLight: #666666;
$fontColorLight2: #999999;
$btnHover: #f25b4a;
$btnHover: $primaryColorV6_2;
$lineColor: #e6e6e6;
$gradientBtnColor1: #ffa383;
$gradientBtnColor2: #f25b4a;
$gradientBtnColorHover1: #fb8057;
$gradientBtnColorHover2: #e64632;
$ToolNavColor: #666;

$scrollColor: #d8d8d8;

//PPT 新配色
$pptPrimaryColor: #DC512D;
$pptPrimaryHoverColor: rgba(220, 81, 45, 0.7);
$pptPrimaryTextColor: #202020;
$pptSubTextColor: #666;
$pptSubSTextColor: #999;
$pptPrimaryTextSize: 16px;
$pptContentTextSize: 14px;
$pptTipTextSize: 12px;
$pptSplitLineColor: #E4E9EE;
$pptBorderColor: #D6DBE1;
$pptBtnBorderColor: #D6DBE1;
$pptBtnBorderHoverColor: #DC512D;
$pptBtnTextColor: #666;
$pptBtnTextHoverColor: #DC512D;
$pptBtnBgSelectColor: #DC512D;
$pptBtnBorderSelectColor: #DC512D;
$pptBtnTextSelectColor: #fff;
$pptBtnTextHoverColor2: #fff;
$pptBtnBgHoverColor2: #DC512D;
$pptScrollBarColor: #DDDEDE;
$pptAssetHoverShadow: 0px 2px 6px 1px rgba(198,202,209,1);
$pptAssetBorderHoverColor: #DC512D;
$pptContentGreyBgColor: #F1F3F7;


//配色 新
$red1: #ff7b75;
$red2: #ff6161;
$red3: #f25b4a;
$white: #ffffff;
$block: #000000;
$grey1: #999999;
$grey2: #f0f0f0;
$grey3: #fafafa;

//布局
$infoHeight: 50px;
$navWidth: 72px;
$navHeight: 100%;
$toolWidth: 72px + 360px;
$floatToolPanel: 270px;
$floatToolPanelContent: 205px;
$navLayout: 72px;
$navPadding: 16px;
$helperWidth: 360px;
$canvasTop: 100px;
$toolContentWidth: 360px;
$canvasWidthLaptop: 680px;
$canvasHeightLaptop: 480px;
$canvasWidthPC1080: 1080px;
$canvasHeightPC1080: 720px;
$canvasWidthPC4000: 1080px;
$canvasHeightPC4000: 720px;

//图标或文字大小
$textSize1: 11px;
$textSize2: 16px;
$textSize3: 20px;

//组件属性
//搜索框
$searchBarInputWidth: 236px;
$searchBarInputFontSize: 12px;
$searchBarInputPadding: 5px 10px;
$searchBarIconSize: 20px;
$searchBarIconMarginLeft: 5px;
//元素列表
$elementListItemWidth: 80px;
$elementListItemHeight: 80px;
$elementListItemMargin: 5px;

//不同设备的宽度
$widthLaptop: 1280px;
$widthTablet: 1280px;
$widthPC1080: 1080px;
$widthPC4000: 4000px;

//快速设置不同设备宽度
@mixin responsive($width) {
    @if $width==laptop {
        @media only screen and (max-width: $widthLaptop) {@content;}
    }
    @if $width==tablet {
        @media only screen and (max-width: $widthTablet) {@content;}
    }
    @if $width==pc1080 {
        @media only screen and (max-width: $widthPC1080) {@content;}
    }
    @if $width==pc4000 {
        @media only screen and (max-width: $widthPC4000) {@content;}
    }
}

//不同设备的宽度
$widthseachLaptop: 120px;
$widthseachTablet: 120px;
$widthseachPC1080: 180px;
$widthseachPC4000: 400px;
// 设置搜索框
@mixin responseach($width) {
    @if $width==laptop {
        @media only screen and (max-width: $widthseachLaptop) {@content;}
    }
    @if $width==tablet {
        @media only screen and (max-width: $widthseachTablet) {@content;}
    }
    @if $width==pc1080 {
        @media only screen and (max-width: $widthseachPC1080) {@content;}
    }
    @if $width==pc4000 {
        @media only screen and (max-width: $widthseachPC4000) {@content;}
    }
}

//画线
@mixin line($top, $right, $bottom, $left, $color) {
    @if $bottom==1 {
        border-bottom: 1px solid $color;
    }
    @if $right==1 {
        border-right: 1px solid $color;
    }
    @if $left==1 {
        border-left: 1px solid $color;
    }
    @if $top==1 {
        border-top: 1px solid $color;
    }
}

@mixin dividingLine($px: 1px, $color: $lineColor, $opacity: 1){
    border-bottom: $px solid $color;
    opacity: $opacity;
    position: relative;

    .title{
        position: absolute;
        top: 50%;
        margin-top: -14.5px;
        font-size: 22px;
        color: #333;
        width: 100%;
        text-align: center;
        font-weight: bold;

        p{
            display: inline;
            background: #fff;
            padding: 0 10px;
        }
    }
}