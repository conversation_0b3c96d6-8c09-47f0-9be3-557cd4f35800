@import "Base";

.canvasContent {
  position: relative;
  background: $bgColor2;
  //margin: $canvasTop auto 0 auto;
  //border: 1px dashed $fgColor2;
  width: $canvasWidthPC1080;
  height: $canvasHeightPC1080;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 3px 12px 0px;

  @include responsive(laptop) {
    width: $canvasWidthLaptop;
    width: $canvasHeightLaptop;
  }
  @include responsive(pc1080) {
    width: $canvasWidthPC1080;
    height: $canvasHeightPC1080;
  }
  @include responsive(pc4000) {
    width: $canvasWidthPC4000;
    height: $canvasHeightPC4000;
  }
  .paintCanvaStyle {
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
  }
  .page_initaling_loading_tips{
    position: absolute;
    top: -30px;
    width: 100%;
    text-align: center;
    color: #6C6C6C;
    font-size: 14px;
    .iconfont{
      position: absolute;
      left: 50%;
      top: 1px;
      margin-left: -60px;
      animation: loadingTips 2s linear infinite;
    }
    @keyframes loadingTips {
      0%{
        transform: rotate(0deg);
      }
      100%{
        transform: rotate(360deg);
      }
    }
  }


  .new_page_initaling_loading_tips{
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    color: #6C6C6C;
    font-size: 14px;
    z-index: 9999999999;
    

  
    
    
    .container{
      height: 100px;
      position: absolute;
      top:50%;
      left:50%;
      transform:translate(-50%,-50%);
      background: rgba(0, 0, 0, 0.4);
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #FFFFFF;
      
    }

    .iconfont{
      // transform:translate(-50%,-50%);
      // top: 1px;
      // margin-left: -60px;
      animation: loadingTips 2s linear infinite;
      z-index: 99999999999;
    }
    @keyframes loadingTips {
      0%{
        transform: rotate(0deg);
      }
      100%{
        transform: rotate(360deg);
      }
    }
  }



  .page_initaling_loading_tips_v2{
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
    width:212px;
    height:106px;
    background:rgba(0,0,0,0.6);
    border-radius:8px;
    z-index: 9999999;
    .dots{
      .dot_item{
        display: inline-block;
        position: absolute;
        width: 6px;
        height: 6px;
        background: #fff;
        opacity: 0.2;
        border-radius: 50%;
      }
      @keyframes loadingdots {
        0%{
          transform: scale(1);
          opacity: 0.2;
        }
        5%{
          transform: scale(2);
          opacity: 1;
        }
        10%{
          transform: scale(1);
          opacity: 0.2;
        }
        100%{
          transform: scale(1);
          opacity: 0.2;
        }
      }
    }
    .tips{
      font-size:14px;
      font-family:PingFangSC-Regular,PingFang SC;
      font-weight:400;
      color: #fff;
      margin-top: 63px;
      text-align: center;
    }
    .fountainG{
      width:185px;
      height:28px;
      position: absolute;
      width: 185px;
      height: 28px;
      margin-left: 27px;
      margin-top: 28px;
    }
    
    .fountainG_{
      position:absolute;
      top:0;
      background-color:#fff;
      opacity: 1;
      width:14px;
      height:14px;
      animation-name:bounce_fountainG;
      animation-duration:1.5s;
      animation-iteration-count:infinite;
      animation-direction:normal;
      transform:scale(.3);
      border-radius:19px;
    }
    
    .fountainG_1{
      left:0;
      animation-delay:0.6s;
        -o-animation-delay:0.6s;
        -ms-animation-delay:0.6s;
        -webkit-animation-delay:0.6s;
        -moz-animation-delay:0.6s;
    }
    
    .fountainG_2{
      left:20px;
      animation-delay:0.75s;
        -o-animation-delay:0.75s;
        -ms-animation-delay:0.75s;
        -webkit-animation-delay:0.75s;
        -moz-animation-delay:0.75s;
    }
    
    .fountainG_3{
      left:40px;
      animation-delay:0.9s;
        -o-animation-delay:0.9s;
        -ms-animation-delay:0.9s;
        -webkit-animation-delay:0.9s;
        -moz-animation-delay:0.9s;
    }
    
    .fountainG_4{
      left:60px;
      animation-delay:1.05s;
        -o-animation-delay:1.05s;
        -ms-animation-delay:1.05s;
        -webkit-animation-delay:1.05s;
        -moz-animation-delay:1.05s;
    }
    
    .fountainG_5{
      left:80px;
      animation-delay:1.2s;
        -o-animation-delay:1.2s;
        -ms-animation-delay:1.2s;
        -webkit-animation-delay:1.2s;
        -moz-animation-delay:1.2s;
    }
    
    .fountainG_6{
      left:100px;
      animation-delay:1.35s;
        -o-animation-delay:1.35s;
        -ms-animation-delay:1.35s;
        -webkit-animation-delay:1.35s;
        -moz-animation-delay:1.35s;
    }
    
    .fountainG_7{
      left:120px;
      animation-delay:1.5s;
        -o-animation-delay:1.5s;
        -ms-animation-delay:1.5s;
        -webkit-animation-delay:1.5s;
        -moz-animation-delay:1.5s;
    }
    
    .fountainG_8{
      left:140px;
      animation-delay:1.64s;
        -o-animation-delay:1.64s;
        -ms-animation-delay:1.64s;
        -webkit-animation-delay:1.64s;
        -moz-animation-delay:1.64s;
    }
    
    @keyframes bounce_fountainG{
      0%{
        transform:scale(1);
        background-color:#fff;
        opacity: 1;
      }
    
      100%{
        transform:scale(.3);
        background-color:rgb(255,255,255);
        opacity: .2;
      }
    }
  }
  .assetImg {
    display: none;
  }

  .editorWatermark {
    background: url($imgHost + "/site/editor/editorWatermark.png") no-repeat;
    width: 140px;
    height: 25px;
    position: absolute;
    opacity: 0.8;
    z-index: 999999;
  }

  .sidePagingAreaTip {
    position: absolute;
    z-index: 99999999;
    top: 50%;
    right: -220px;
    margin-top: -64px;

    &.unActive {
      display: none;
    }
    .tipArrow {
      width: 0;
      height: 0;
      border-top: 8px solid transparent;
      border-right: 10px solid rgba(0, 0, 0, 0.5);
      border-bottom: 8px solid transparent;
      position: absolute;
      top: 50%;
      left: -10px;
      margin-top: -8px;
    }
    .tipContent {
      width: 153px;
      height: 53px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 4px;

      p {
        display: block;
        width: 126px;
        margin: 0 auto;
        padding-top: 8px;
        color: #fff;
        font-size: 14px;
      }
    }
  }
  .asset{
    // &:hover{
    //   outline: 1px solid rgba(255, 110, 123, 1) !important;
    // }
    &.isActive{

      &:hover{
        outline: 0 !important;
      }
    }
  }
  .assetHoverNone {
    &:hover {
      outline: none !important;
    }
  }
  .assetHoverVisible {
    &:hover {
      outline: 1px solid rgba(255, 110, 123, 1) !important;
    }
  }
  .assetSizeShow {
    display: none;
    width: 70px;
    height: 30px;
    background-color: #000;
    color: #fff;
    white-space: nowrap;
    border-radius: 4px;
    font-size: 12px;
    padding: 6px;
    box-sizing: border-box;
    position: absolute;
    top: 0;
    left: 0;
  }
  .assetBorderBox {
    outline: 1px solid rgba(255, 110, 123, 1) !important;
  }

}
.fixedWaterMaskButton{
  
  a{
    position: absolute;
    z-index: 100000000;
    width: 194px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    transform: translateX(-50%);
    left: 118px;
    bottom: 20px;
    cursor: pointer;
    background: linear-gradient(130deg, #FFF8E6 0%, #EEDAAB 100%);
    box-shadow: 0px 2px 6px 1px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    // border: 1px solid #D0B383;
    font-size: 14px;
    font-weight: 500;
    color: #7A4806;
    
  }
}
.canvasOnScroll{
  width: 14px;
  height: 100%;
  background: #f1f1f1;
  position: absolute;
  right: 0px;
  top:0px;
  transition: all 0.28s ease-in-out;
  display: none;
  z-index: 10;
  overflow: hidden;
  .canvasOnScrollBox{
    width: 14px;
    height: 200px;
    position: absolute;
    right: 0px;
    top:0px;
    background: #c1c1c1;
    &:hover{
      background: #a8a8a8
    }
  }
}
.canvasOnScrollBot{
  width: 100%;
  height: 14px;
  background: #f1f1f1;
  position: absolute;
  left: 0px;
  bottom:0px;
  // transition: all 0.08s ease-in-out;
  display: none;
  z-index: 10;
  overflow: hidden;
  .canvasOnScrollBoxBot{
    width: 200px;
    height: 14px;
    position: absolute;
    left: 0px;
    top:0px;
    background: #c1c1c1;
    &:hover{
      background: #a8a8a8
    }
  }
}

.canvasWrapper {
  display: inline-block;
  width: 100%;
  height: 100%;

  &.animation {
    .canvasContent {
      transition: left 0.15s linear
    }
  }

  // .posistionBox{
  //  overflow: auto;
  // }
  // .posistionBox::-webkit-scrollbar{
  //  width:4px;
  //  height:4px;
  // }
  // .posistionBox::-webkit-scrollbar-button{
  //  display: none
  // }
  // .posistionBox::-webkit-scrollbar-track{
  //  display: none
  // }
  // .posistionBox::-webkit-scrollbar-thumb{
  //  background:rgb(153, 153, 153);
  //  border-radius:4px;
  // }
}

.canvasWrapper, .floatToolPanel {
  .dividingLine {
    @include dividingLine;
  }

  .multipleChoiceBox {
    position: absolute;
    z-index: 9999;
    top: 100px;
    left: 100px;
    width: 100px;
    height: 100px;
    border: 1px solid $btnHover;
    background: rgba(254, 206, 200, 0.1)
  }

  .slideBar {
    position: absolute;
    left: 56px;
    top: 8px;
    border-bottom: 4px solid #ccc;
    width: 111px;

    .slideBarButtonBar {
      position: absolute;
      left: 0;
      top: 0;
      border-bottom: 4px solid $btnHover;
    }
    .slideBarButton {
      position: absolute;
      top: -4px;
      left: 50px;
      background: #fff;
      width: 12px;
      height: 12px;
      box-shadow: 0 1px 6px rgba(0, 0, 0, 0.5);
      border-radius: 15px;
      cursor: default;
      &:hover {
        box-shadow: 0 1px 6px rgba(236, 100, 56, 1);
      }
    }
  }

}

.blockTitle {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-top: 10px;
}

/*元素选中框 START*/
.assetEditBox {
  cursor: move;

  .contentCut {
    .mouldImage{
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #000;
      opacity: 0.5;
    }
    .contentCutImage {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 1;
    }
    /*拖动点通用样式*/
    .assetDragBorder {
      width: 15px;
      height: 15px;
      position: absolute;
      background: url($imgHost + "/site/editor/assetIconV6.1.png") 0 -23px no-repeat;
      margin-top: -5px;
      margin-right: -1px;
      cursor: e-resize;
      z-index: 100;
    }
    /*右上角的拖动点*/
    .rightTopBorder {
      top: -11px;
      right: -10px;
      margin-top: 0;
      cursor: ne-resize;
      //background-position: 0 -100px;
      background: url($imgHost + "/site/editor/assetCircle.svg") -2px -2px  no-repeat;
      transform: rotate(180deg);
    }
    /*右下角的拖动点*/
    .rightBottomBorder {
      bottom: -11px;
      right: -10px;
      margin-top: 0;
      cursor: se-resize;
      //background-position: 0 -100px;
      background: url($imgHost + "/site/editor/assetCircle.svg")  -2px -2px  no-repeat;
      transform: rotate(270deg);
    }
    /*左上角的拖动点*/
    .leftTopBorder {
      top: -11px;
      left: -11px;
      margin-top: 0;
      cursor: nw-resize;
      //background-position: 0 -100px;
      background: url($imgHost + "/site/editor/assetCircle.svg")  -3px -3px  no-repeat;
      transform: rotate(90deg);
    }
    /*左下角的拖动点*/
    .leftBottomBorder {
      bottom: -11px;
      left: -11px;
      margin-top: 0;
      cursor: sw-resize;
      //background-position: 0 -100px;
      background: url($imgHost + "/site/editor/assetCircle.svg") -2px -3px  no-repeat;
      transform: rotate(0deg);
    }

    $drapAreaLength: 3;
    /*边线统一样式*/
    .assetDragLine {
      border: 0 solid #fff;
      //border-image: url($imgHost + "/site/editor/assetEditBorder.png") 3 round;
      //border-image-width: 3;
      position: absolute;
      //margin-left: 3px;
      //margin-top: 4px;
      z-index: 10;
      &:after {
        content: ' ';
        width: inherit;
        height: inherit;
        border: 0 dashed #666;
        position: absolute;
        z-index: 1;
        top: 0;
        left: 0;
      }
    }
    /*左边的边线*/
    .leftLine {
      left: -2px;
      top: 0;
      width: 0px;
      height: 100%;
      border-right-width: 1px;
      &:after{
        border-right-width: 1px;
      }
      .leftLineContent {
        width: $drapAreaLength+px;
        height: 100%;
        position: absolute;
        top: 0;
        left: -$drapAreaLength+px;
      }
    }
    /*上边的边线*/
    .topLine {
      left: 0;
      top: -2px;
      width: 100%;
      height: 0px;
      border-bottom-width: 1px;
      &:after{
        border-bottom-width: 1px;
      }
      .topLineContent {
        height: $drapAreaLength+px;
        width: 100%;
        position: absolute;
        top: -$drapAreaLength+px;
        left: 0px;
      }
    }
    /*右边的边线*/
    .rightLine {
      right: -2px;
      top: 0;
      width: 0px;
      height: 100%;
      border-right-width: 1px;
      &:after{
        border-right-width: 1px;
      }
      .rightLineContent {
        width: $drapAreaLength+px;
        height: 100%;
        position: absolute;
        right: -$drapAreaLength+px;
        top: 0px;
      }
    }
    /*下边的边线*/
    .bottomLine {
      bottom: -2px;
      left: 0;
      width: 100%;
      height: 0;
      border-bottom-width: 1px;
      &:after{
        border-bottom-width: 1px;
      }
      .bottomLineContent {
        height: $drapAreaLength+px;
        width: 100%;
        position: absolute;
        bottom: -$drapAreaLength+px;
        left: 0px;
      }
    }
  }
  .assetEditBorder {
    cursor: move;
    margin: 4px;
    // border: 1px dashed #666;
    .contentCutBtnArea {
      display: inline-block;
      position: absolute;
      z-index: 1000;

      &.replace{
        //display: flex ;
        justify-content: space-between;
        padding: 0 10px;
        //min-width: 110px;

        .btnItem{
          font-size: 14px;
          border-radius: 2px;
          width: auto;
          box-shadow: 0 0 10px #cecece;
          //padding: 0 8px;
          width: 58px;

          &:first-child{
            background-color: $primaryColor;
            color: #fff;
          }
        }
        &:after{
          display: none;
        }
      }
      &:after{
        content: ' ';
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        box-shadow: 0 0 10px #cecece;
      }

      .btnItem {
        width: 36px;
        height: 33px;
        line-height: 33px;
        color: #666;
        font-size: 18px;
        text-align: center;
        background: #fff;
        cursor: pointer;
        position: relative;
        z-index: 100;
        &:hover {
          color: #f63;
        }
      }
    }
    .cutContainerPanel{
      position: absolute;
      z-index: 1000;
      left: 50%;
      transform:translateX(-50%);
      top: -55px;
      width: 348px;
      height: 36px;
      background:rgba(255,255,255,1);
      box-shadow:0px 0px 4px 0px rgba(0,0,0,0.12);
      border-radius:4px;
      display: flex;
      font-size: 12px;
      cursor: unset;
      &.cutoutContainerPanel{
        width: auto;
        .rightSidePanel{
          .rightSideItem{
            &.disabled{
              opacity: 0.5;
              cursor: default;
            }
            &.success{
              color:#EF3964;
            }
            &.reset{
              color: #1F1A1B;
              opacity: 1 !important;
            }
          }
        }
      }
      .leftSidePanel {
        border-right: 1px solid #E4E9EE;
        display: flex;
        align-items: center;
        width: 200px;
        box-sizing: border-box;
        padding: 0 14px 0 18px;
        .slideBar {
          position: relative;
          width: 130px;
          left: unset;
          top: unset;
          border: 0;
          // margin-right: 18px;
          padding: 5px 0;
        }
        .slideBarButtonBar {
          height: 2px;
          background: rgba(255, 69, 85, 1);
          border: 0;
          top: 4px;
        }
        .slideBarButton {
          top: -1px;
          width: 12px;
          height: 12px;
          background: rgba(255, 69, 85, 1);
          box-shadow: 0px 1px 3px 1px rgba(255, 69, 85, 0.3);
          cursor: pointer;
          .showPercent{
            background: rgba(0,0,0,.7);
            border-radius: 3px;
            color: #fff;
            margin-top: 12px;
            padding: 4px 10px;
            text-align: center;
            word-wrap: break-word;
            white-space: pre;
            pointer-events: none;
            position: absolute;
            left: 50%;
            top: 100%;
            transform: translateX(-50%);
            -webkit-font-smoothing: subpixel-antialiased;
            z-index: 1;
            border-radius: 3px;
            &::before{
              content: "";
              position: absolute;
              border: 5px solid transparent;
              border-bottom-color: rgba(0,0,0,.7);
              color: rgba(0,0,0,.8);
              margin: 3px 0 0 -5px;
              height: 0;
              width: 0;
              z-index: 999;
              pointer-events: none;
              top: -13px;
              left: 50%;
            }
          }
        }
        .slideBarMask {
          width: inherit;
          height: 2px;
          border-radius: 1px;
          border: 0;
          background: rgba(255, 69, 85, 0.2);
          top: 4px;
        }
        .slideBarType {
          font-size: 12px;
          color: #666;
          margin-right: 10px;
        }
      }
      .rightSidePanel{
        width: 145px;
        display: flex;
        justify-content: space-around;
        align-items: center;
        box-sizing: border-box;
        padding: 0 5px;
        .rightSideItem{
          width: 30px;
          text-align: center;
          padding: 4px 0;
          opacity: 1;
          cursor: pointer;
          &.success{
            color: #16A401;
          }
        }
        .rightSideLine{
          width: 1px;
          height: 18px;
          background-color: #EDEEF0;
        }
      }
    }
    .assetEditBorderTop {
      position: absolute;
      //top: -28px;
      //left: 50%;
    }
    .assetEditBorderRotate3D{
      width: 26px;
      height: 26px;
      line-height: 26px;
      border-radius: 26px;
      background-color: #000000;
      cursor: url(//s.tuguaishou.com/site/editor/hand.png) 8 8, auto;
      text-align: center;
      position: absolute;
      transform: translate(-10px, -8px);
      z-index: 1000;
      display: none;

      i{
        color: #ffffff;
      }
    }
    .assetEditFavBtn {
      position: absolute;
      top: -33px;
      left: 50%;
      margin-left: -13px;
      width: 30px;
      height: 30px;
      cursor: pointer;
      background: url(//s.tuguaishou.com/site/editor/assetFav.svg) center no-repeat;

      &:hover, &.active {
        background:url(//s.tuguaishou.com/site/editor/assetFavActive.svg) center no-repeat;
      }
    }
    .assetEditBorderTopLocked {
      margin-left: -3px;
      cursor: pointer;
      color: $primaryColor;
    }
    .assetRotate {
      width: 28px;
      height: 28px;
      background: url($imgHost + "/site/editor/assetRotate.svg") center no-repeat;
      margin-left: -16px;
      cursor: url($imgHost + "/site/editor/hand.png") 8 8, auto;
      position: relative;
      // &:after{
      //   content: ' ';
      //   position: absolute;
      //   bottom: 24px;
      //   left: 13px;
      //   height: 38px;
      //   border-left: 1px dashed #666;
      // }
      // &:before{
      //   content: ' ';
      //   position: absolute;
      //   bottom: 24px;
      //   left: 13px;
      //   height: 38px;
      //   border-left: 1px solid #fff;
      // }
    }
    /*拖动点通用样式*/
    .assetDragBorder {
    //   width: 10px;
    //   height: 10px;
      width: 14px;
      height: 14px;
      position: absolute;
      // background: url($imgHost + "/site/editor/assetIconV6.1.png") 0 -23px no-repeat;
      margin-top: -5px;
      margin-right: -0px;
      cursor: e-resize;
      z-index: 100;
      background:rgba(255,255,255,1);
      box-shadow:0px 0px 3px 0px rgba(0,0,0,0.2);
      border:1px solid rgba(255,69,85,1);
      box-sizing: border-box;
      &.table {
        border-color: #4A99F8;

        &.leftTopBorder, &.leftBottomBorder, &.rightBottomBorder, &.rightTopBorder {
          transform: translate(1px, 0px);
          width: 12px;
          height: 12px;
        }
        &.leftBottomBorder {
          transform: translate(1px, -1px);
        }
        &.rightTopBorder {
          transform: translate(0px, 0px);
        }
        &.rightBottomBorder {
          transform: translate(0px, -1px);
        }

        &.bottomBorder {
          transform: translate(4px, 1px);
          width: 16px;
          height: 8px;
        }
        &.topBorder {
          transform: translate(4px, 2px);
          width: 16px;
          height: 8px;
        }
        &.leftBorder {
          transform: translate(1px, 1px);
          height: 16px;
          width: 8px;
        }
        &.rightBorder {
          transform: translate(0, 1px);
          height: 16px;
          width: 8px;
        }
      }
    }
    /*右边的拖动点*/
    .rightBorder {
      margin-top: -3.5px;
      margin-left: 2px;
      //background-position: 0 -66px;
      // background: url($imgHost + "/site/editor/assetSquare.svg") no-repeat;
      //transform: rotate(180deg);
      cursor: e-resize;
      width: 8px;
      height: 18px;
      border-radius: 4px;
    }
    /*左边的拖动点*/
    .leftBorder {
      margin-left: 1px;
      margin-top: -3.5px;
      // background: url($imgHost + "/site/editor/assetSquare.svg") no-repeat;
      //background-position: 0 -66px;
      cursor: w-resize;
      width: 8px;
      height: 18px;
      border-radius: 4px;
    }
    /*上边的拖动点*/
    .topBorder {
      margin-top: 0.5px;
      margin-left: -6.5px;
      // background: url($imgHost + "/site/editor/assetSquare.svg") no-repeat;
      //background-position: 0 -66px;
      //transform: rotate(90deg);
      cursor: n-resize;
      width: 18px;
      height: 8px;
      border-radius: 4px;
    }
    /*下边的拖动点*/
    .bottomBorder {
      margin-top: 1.5px;
      margin-left: -6.5px;
      // background: url($imgHost + "/site/editor/assetSquare.svg") no-repeat;
      //background-position: 0 -66px;
      //transform: rotate(270deg);
      cursor: s-resize;
      width: 18px;
      height: 8px;
      border-radius: 4px;
    }
    /*右上角的拖动点*/
    .rightTopBorder {
      margin-top: 5px;
      cursor: ne-resize;
      border-radius: 10px;
      //background-position: 0 -100px;
      // background: url($imgHost + "/site/editor/assetCircle.svg")  -2px -2px  no-repeat;
      transform: rotate(180deg) translateX(1px);
    }
    /*右下角的拖动点*/
    .rightBottomBorder {
      margin-top: 0;
      cursor: se-resize;
      border-radius: 10px;
      //background-position: 0 -100px;
      // background: url($imgHost + "/site/editor/assetCircle.svg")  -2px -2px  no-repeat;
      transform: rotate(270deg) translate(2px, -1px);
    }
    /*左上角的拖动点*/
    .leftTopBorder {
      margin-top: 5px;
      margin-left: 5px;
      cursor: nw-resize;
      border-radius: 10px;
      //background-position: 0 -100px;
      // background: url($imgHost + "/site/editor/assetCircle.svg")  -3px -3px  no-repeat;
      transform: rotate(90deg);
    }
    /*左下角的拖动点*/
    .leftBottomBorder {
      margin-top: 0;
      margin-left: 5px;
      cursor: sw-resize;
      border-radius: 10px;
      //background-position: 0 -100px;
      // background: url($imgHost + "/site/editor/assetCircle.svg")  -2px -3px  no-repeat;
      transform: rotate(0deg) translateY(-2px);
    }
    /*右上角的拖动点(容器)*/
    .rightTopBorderCut {
      width: 14px;
      margin-top: 0;
      cursor: ne-resize;
      background-position: 0 -118px;
      transform: rotate(90deg);
    }
    /*右下角的拖动点(容器)*/
    .rightBottomBorderCut {
      width: 14px;
      margin-top: 0;
      cursor: se-resize;
      background-position: 0 -118px;
      transform: rotate(180deg);
    }
    /*左上角的拖动点(容器)*/
    .leftTopBorderCut {
      width: 14px;
      margin-top: 0;
      cursor: nw-resize;
      background-position: 0 -118px;
      transform: rotate(360deg);
    }
    /*左下角的拖动点(容器)*/
    .leftBottomBorderCut {
      width: 14px;
      margin-top: 0;
      cursor: sw-resize;
      background-position: 0 -118px;
      transform: rotate(270deg);
    }
    /*右上角的拖动点(裁剪)*/
    .rightTopBorderContainerCut {
      width: 20px;
      height: 20px;
      border: none;
      margin-top: 0;
      cursor: ne-resize;
      background-position: 0 -118px;
      transform: rotate(90deg);
    }
    /*右下角的拖动点(裁剪)*/
    .rightBottomBorderContainerCut {
      width: 20px;
      height: 20px;
      border: none;
      margin-top: 0;
      cursor: se-resize;
      background-position: 0 -118px;
      transform: rotate(180deg);
    }
    /*左上角的拖动点(裁剪)*/
    .leftTopBorderContainerCut {
      width: 20px;
      height: 20px;
      border: none;
      margin-top: 0;
      cursor: nw-resize;
      background-position: 0 -118px;
      transform: rotate(360deg);
    }
    /*左下角的拖动点(裁剪)*/
    .leftBottomBorderContainerCut {
      width: 20px;
      height: 20px;
      border: none;
      margin-top: 0;
      cursor: sw-resize;
      background-position: 0 -118px;
      transform: rotate(270deg);
    }
    /*右边的拖动点(裁剪)*/
    .rightBorderContainer {
      width: 9px;
      height: 9px;
      //margin-top: -2px;
      //margin-left: 3px;
      margin-left: 4.5px;
      background: url($imgHost + "/index_img/editorV6.0/containerPoint.png") 0 0 no-repeat;
      //background-position: 0 -66px;
      //transform: rotate(180deg);
      cursor: e-resize;
      background: #666;
      border: 2px solid #fff;
      height: 12px;
      width: 4px;
    }
    /*左边的拖动点(裁剪)*/
    .leftBorderContainer {
      width: 9px;
      height: 9px;
      //margin-left: -5px;
      //margin-top: -2px;
      margin-left: -4.5px;
      background: url($imgHost + "/index_img/editorV6.0/containerPoint.png") 0 0 no-repeat;
      //background-position: 0 -66px;
      background: #666;
      border: 2px solid #fff;
      height: 12px;
      width: 4px;
      cursor: w-resize;
    }
    /*上边的拖动点(裁剪)*/
    .topBorderContainer {
      width: 9px;
      height: 9px;
      //margin-top: -6px;
      //margin-left: -1px;
      margin-top: -4.5px;
      // background: url($imgHost + "/index_img/editorV6.0/containerPoint.png") 0 0 no-repeat;
      //background-position: 0 -66px;
      //transform: rotate(90deg);
      background: #666;
      border: 2px solid #fff;
      height: 4px;
      width: 12px;
      cursor: n-resize;
    }
    /*下边的拖动点(裁剪)*/
    .bottomBorderContainer {
      width: 9px;
      height: 9px;
      //margin-top: 2px;
      //margin-left: -1px;
      margin-top: 4.5px;
      background: url($imgHost + "/index_img/editorV6.0/containerPoint.png") 0 0 no-repeat;
      //background-position: 0 -66px;
      //transform: rotate(270deg);
      background: #666;
      border: 2px solid #fff;
      height: 4px;
      width: 12px;
      cursor: s-resize;
    }
    /*边线统一样式*/
    .assetDragLine {
      border:0 solid #fff;
      border-color: rgba(255, 110, 123, 1);
      //border-image: url($imgHost + "/site/editor/assetEditBorder.png") 3 round;
      //border-image-width: 3;
      position: absolute;
      margin-left: 4px;
      margin-top: 4px;
      z-index: 10;
      // &:after {
      //   content: ' ';
      //   width: inherit;
      //   height: inherit;
      //   border: 0 dashed #666;
      //   position: absolute;
      //   z-index: 1;
      //   top: 0;
      //   left: 0;
      // }
      &.table {
        pointer-events: none;
        border-color: #4A99F8;

        &.leftLine, &.rightLine {
          border-right-width: 1px;
        }
        &.topLine, &.bottomLine {
          border-bottom-width: 1px;
        }
        &.topLine {
          transform: translateY(1px);
        }
        &.leftLine {
          transform: translateX(1px);
        }
      }
    }

    $drapAreaLength: 3;
    /*左边的边线*/
    .leftLine {
      left: 0;
      top: 0;
      width: 0;
      height: 100px;
      margin-left: 4px;
      border-right-width: 3px;
      // &:after{
      //   border-right-width: 2px;
      // }
      .leftLineContent {
        width: $drapAreaLength+px;
        height: 100%;
        position: absolute;
        top: 0;
        left: -$drapAreaLength+px;
      }
    }
    /*上边的边线*/
    .topLine {
      left: 0;
      top: 0;
      width: 100px;
      height: 0px;
      border-bottom-width: 3px;
      // &:after{
      //   border-bottom-width: 2px;
      // }
      .topLineContent {
        height: $drapAreaLength+px;
        width: 100%;
        position: absolute;
        top: -$drapAreaLength+px;
        left: 0px;
      }
    }
    /*右边的边线*/
    .rightLine {
      left: 100px;
      top: 0;
      width: 0;
      height: 100px;
      margin-left: 5px;
      border-right-width: 3px;
      // &:after{
      //   border-right-width: 2px;
      // }
      .rightLineContent {
        width: $drapAreaLength+px;
        height: 100%;
        position: absolute;
        right: -$drapAreaLength+px;
        top: 0px;
      }
    }
    /*下边的边线*/
    .bottomLine {
      top: 100px;
      left: 0;
      width: 100px;
      height: 0;
      border-bottom-width: 3px;
      margin-top: 5px;
      // &:after{
      //   border-bottom-width: 2px;
      // }
      .bottomLineContent {
        height: $drapAreaLength+px;
        width: 100%;
        position: absolute;
        bottom: -$drapAreaLength+px;
        left: 0px;
      }
    }

    .quickEditingText {
      width: 220px;
      height: 260px;
      position: absolute;
      bottom: 26px;
      left: 50%;
      margin-left: -110px;
      box-shadow: 0px 2px 10px rgba(204, 204, 204, 0.41);
      border-radius: 4px;
      background: #fff;
      cursor: default;

      .dropDownBoxItem {
        width: 168px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        padding: 0 26px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 14px;
        color: RGBA(102, 102, 102, 1);
        background: #fff;
        cursor: pointer;

        &:hover {
          background: RGBA(242, 242, 242, 1);
          color: $primaryColorV6_2;
        }
      }
      .dropDownBoxItemTitle {
        width: 208px;
        padding: 0;
        padding-left: 12px;
        background: RGBA(234, 234, 234, 1);
        color: RGBA(102, 102, 102, 1);
        text-align: left;
        cursor: default;

        &:hover {
          background: RGBA(234, 234, 234, 1);
          color: RGBA(102, 102, 102, 1);
        }
      }
    }
  }

  &.is_asset_move {
    .assetEditFavBtn{
      display: none !important;
    }
    .assetDragBorder{
      display: none !important;
    }
    .assetEditBorderTop{
      // display: none !important;
    }
    .assetDragLine{
      opacity: 0;
    }
  }
}

/*元素选中框 END*/

.canvasWrapper {
  user-select: none;
  .toolPanel {
    width: $floatToolPanelContent;
    display: inline-block;
    background: #f8f9fb;
    //position: absolute;
    //right: -$floatToolPanelContent;
    //top: 88px;
    //z-index: 9999999;
    //bottom: 0;
    //min-height: 315px;
  }
}

.helperLayout {
  .toolPanel {
    width: $floatToolPanelContent;
    display: inline-block;
    background: #fff;
    border-radius: 16px 0 0 16px;
    .textEditorBox, .toolElementPanel, .toolElementPanel {
      //width: 250px;
      width: 205px;
      //margin: 0 auto;
      padding: 10px 0 0 0;
    }
  }
}

/*文本属性栏：文本属性*/
.textEditorPropsBlock {
  padding: 28px 0 28px 25px;
  .radioArea {
    margin-top: 5px;
    .item {
      font-size: 14px;
      min-width: 100px;
      display: inline-block;
      color: #666;
      margin: 15px 0 0 0;
      input[type=radio] {
        margin-right: 5px;
      }
      .otherRadio {
        margin-left: 5px;
        padding: 0px 10px;
        width: 70%;
      }
    }
  }
}

/*文本属性栏：操作*/
.textEditDoBlock {
  margin: 10px 0 0 0;
  //padding-bottom: 100px;
  padding-bottom: 125px;
  .buttonArea {
    display: inline-block;
    margin-top: 6px;
    .doBtn {
      width: 50px;
      float: left;
      text-align: center;
      color: #666;
      margin-left: 16px;
      position: relative;
      display: inline-block;
      float: left;
      margin-top: 10px;

      > p {
        font-size: 12px;
        margin-top: 2px;
      }
      &:nth-child(3n + 1) {
        margin-left: 0;
      }
      &:hover, &.active {
        color: #ff6161;
        cursor: pointer;
      }
      .buttonPopups {
        cursor: default;
        position: absolute;
        color: #333;
        top: 45px;
        left: 25px;
        z-index: 100;
        background: #fff;
        box-shadow: 0px 0px 10px rgba(200, 200, 200, 0.6);
        padding: 15px 10px;
        border-radius: 5px;
        font-size: 12px;
        width: 80px;

        .buttonPopupsItem {
          cursor: pointer;
          margin-top: 15px;
          color: #666;
          &:first-child {
            margin-top: 0;
          }
          &:hover {
            i {
              color: $btnHover;
            }
            p {
              color: $btnHover;
            }
          }
          i {
            float: left;
            margin-right: 5px;
            &:first-child {
              margin-left: 0;
            }
          }
          p {
            font-size: 12px;
            text-align: left;
          }
        }
      }
    }
  }
}

/*元素属性栏：编辑元素*/
/*By Suqi*/
//.elementEditBlock{
//  margin: 0 0 25px 0;
//  > .item{
//    display: inline-block;
//    width: 100%;
//    margin-top: 15px;
//    position: relative;
//
//    &.mb10{
//      margin-bottom: 10px;
//    }
//    p{
//      float: left;
//      color: #666;
//      font-size: 14px;
//    }
//
//    input{
//      //float: right;
//      width: 48px;
//      height: 26px;
//      margin-top: -5px;
//      padding: 0 5px;
//      border: 1px solid $lineColor;
//      float: left;
//      margin-left: 20px;
//    }
//
//    .choiceColor{
//      border: 1px solid #ccc;
//      background: pink;
//      width: 28px;
//      height: 18px;
//      float: right;
//    }
//
//    .typeBtnArea{
//      position: absolute;
//      background: #fff;
//      left: 38px;
//      top: 50%;
//      margin-top: -14px;
//      box-shadow: 0 1px 1px rgba(0,0,0,0.1);
//
//      .typeBtn{
//        width: 60px;
//        height: 26px;
//        border: 1px solid $lineColor;
//        float: left;
//        border-left-width: 0px;
//        line-height: 26px;
//        text-align: center;
//        cursor: pointer;
//        position: relative;
//        background: #fafafa;
//
//        &:first-child{
//          border-left-width: 1px;
//        }
//
//        .item{
//          color: #999;
//        }
//
//        &:hover, &.active{
//          .item{
//            color: $btnHover;
//          }
//        }
//      }
//    }
//
//    .secondLine{
//      display: inline-block;
//
//      .slideBar{
//        position: relative;
//        margin-top: 5px;
//        float: left;
//        left: unset;
//        top: unset;
//      }
//    }
//
//    ///*裁剪框选择 START*/
//    //.cutContainerArea{
//    //  width: 175px;
//    //  height: 364px;
//    //  background: #fff;
//    //  box-shadow: 1px 2px 4px rgba(153, 153, 153, 0.36);
//    //  position: absolute;
//    //  top: 30px;
//    //  left: 0;
//    //  z-index: 100;
//    //  padding: 0 10px;
//    //
//    //  .cutContainerTip{
//    //    font-size: 12px;
//    //    color: #999;
//    //    margin-top: 10px;
//    //  }
//    //  .cutContainerList{
//    //    margin-top: 10px;
//    //
//    //    .cutContainerItem{
//    //      width: 40px;
//    //      height: 40px;
//    //      margin-right: 5px;
//    //      margin-bottom: 5px;
//    //      background: #eee;
//    //      cursor: pointer;
//    //      float: left;
//    //
//    //      &:nth-child(4n){
//    //        margin-right: 0;
//    //      }
//    //    }
//    //  }
//    //}
//    ///*裁剪框选择 END*/
//  }
//
//  .typeBtnList{
//    display: inline-block;
//    position: absolute;
//    top: -3px;
//    left: 38px;
//
//    .typeBtn{
//      float: left;
//      width: 36px;
//      height: 26px;
//      line-height: 26px;
//      border: 1px solid #E2E2E2;
//      border-radius: 2px 0 0 2px;
//      text-align: center;
//      color: #666;
//      cursor: pointer;
//      position: relative;
//
//      &.active{
//        color: $primaryColorV6_2;
//      }
//      &:hover{
//
//        &:before{
//          content: "";
//          width: 36px;
//          height: 26px;
//          display: block;
//          border: 1px solid $primaryColorV6_2;
//          border-radius: 2px 0 0 2px;
//          position: absolute;
//          top: -1px;
//          left: -1px;
//        }
//      }
//    }
//    .typeBtnMore{
//      float: left;
//      width: 20px;
//      height: 26px;
//      line-height: 26px;
//      border: 1px solid #E2E2E2;
//      border-left: 0 solid #e2e2e2;
//      border-radius: 0 2px 2px 0;
//      text-align: center;
//      color: #666;
//      cursor: pointer;
//      position: relative;
//
//      i{
//        font-size: 16px;
//        transform: rotate(90deg);
//      }
//      &:hover{
//
//        i{
//          color: $primaryColorV6_2;
//        }
//        &:before{
//          content: "";
//          width: 20px;
//          height: 26px;
//          display: block;
//          border: 1px solid $primaryColorV6_2;
//          border-radius: 0 2px 2px 0;
//          position: absolute;
//          top: -1px;
//          left: -1px;
//        }
//      }
//    }
//    .typeBtnMoreList{
//      position: absolute;
//      top: 30px;
//      left: 0px;
//      background: #fff;
//      z-index: 100;
//
//      .typeBtnMoreItem{
//        width: 58px;
//        height: 26px;
//        line-height: 26px;
//        border: 1px solid RGBA(226, 226, 226, 1);
//        margin-top: -1px;
//        text-align: center;
//        cursor: pointer;
//        position: relative;
//
//        &:first-child{
//          margin-top: 0px;
//        }
//        &.active{
//
//          i{
//            color: $primaryColorV6_2;
//          }
//        }
//        &:hover{
//
//          i{
//            color: $primaryColorV6_2;
//          }
//          &:before{
//            content: "";
//            width: 58px;
//            height: 26px;
//            display: block;
//            border: 1px solid $primaryColorV6_2;
//            border-radius: 0 2px 2px 0;
//            position: absolute;
//            top: -1px;
//            left: -1px;
//              z-index: 10;
//          }
//        }
//      }
//    }
//  }
//}

/*元素属性栏：元素属性*/
.elementAttrBlock {
  padding: 28px 0 28px 25px;
  .radioArea {
    margin-top: 5px;
    .item {
      font-size: 14px;
      min-width: 100px;
      display: inline-block;
      color: #666;
      margin: 15px 0 0 0;
      input[type=radio] {
        margin-right: 5px;
      }
      .otherRadio {
        margin-left: 5px;
        padding: 0 5px;
      }
    }
  }
}

/*元素属性栏：操作*/
.elementDoBlock {
  margin: 25px 0 25px 0;
  .buttonArea {
    display: inline-block;
    margin-top: 10px;
    .doBtn {
      width: 50px;
      float: left;
      text-align: center;
      color: #666;
      margin-left: 16px;
      position: relative;
      display: inline-block;
      float: left;
      margin-top: 10px;

      > p {
        font-size: 12px;
        margin-top: 2px;
      }
      &:first-child, &:nth-child(5n) {
        margin-left: 0;
      }
      &:hover, &.active {
        color: #ff6161;
        cursor: pointer;
      }
      .buttonPopups {
        cursor: default;
        position: absolute;
        color: #333;
        top: 45px;
        left: 25px;
        z-index: 100;
        background: #fff;
        box-shadow: 0px 0px 10px rgba(200, 200, 200, 0.6);
        padding: 15px 10px;
        border-radius: 5px;
        font-size: 12px;
        width: 80px;

        .buttonPopupsItem {
          cursor: pointer;
          margin-top: 15px;
          color: #666;
          &:first-child {
            margin-top: 0;
          }
          &:hover {
            i {
              color: $btnHover;
            }
            p {
              color: $btnHover;
            }
          }
          i {
            float: left;
            margin-right: 5px;
            &:first-child {
              margin-left: 0;
            }
          }
          p {
            font-size: 12px;
            text-align: left;
          }
        }
      }
    }
  }
}

/*背景属性栏：编辑背景*/
/*By Suqi*/
//.backgroundEditBlock{
//  margin: 0px 0 25px 0;
//  > .item{
//    display: inline-block;
//    width: 100%;
//    margin-top: 15px;
//    position: relative;
//
//    &.mb10{
//      margin-bottom: 10px;
//    }
//    p{
//      float:left;
//      color: #666;
//      font-size: 14px;
//    }
//
//    input{
//      float:right;
//      width: 48px;
//      height: 26px;
//      margin-top: -5px;
//      padding: 0 5px;
//      border: 1px solid $lineColor;
//      margin-left: 20px;
//    }
//
//    .choiceColor{
//      border: 1px solid #ccc;
//      background: pink;
//      width: 28px;
//      height: 18px;
//      float: right;
//    }
//
//    .typeBtnArea{
//      position: absolute;
//      background: #fff;
//      left: 38px;
//      top: 50%;
//      margin-top: -14px;
//      box-shadow: 0 1px 1px rgba(0,0,0,0.1);
//
//      .typeBtn{
//        width: 63px;
//        height: 26px;
//        border: 1px solid $lineColor;
//        float: left;
//        border-left-width: 0px;
//        line-height: 26px;
//        text-align: center;
//        cursor: pointer;
//        position: relative;
//        background: #fafafa;
//
//        &:first-child{
//          border-left-width: 1px;
//        }
//
//        .item{
//          color: #999;
//        }
//
//        &:hover, &.active{
//          .item{
//            color: $btnHover;
//          }
//        }
//      }
//    }
//
//    .secondLine{
//      display: inline-block;
//
//      .slideBar{
//        position: relative;
//        margin-top: 5px;
//        float: left;
//        left: unset;
//        top: unset;
//      }
//    }
//
//    /*裁剪框选择 START*/
//    .cutContainerArea{
//      width: 175px;
//      height: 364px;
//      background: #fff;
//      box-shadow: 1px 2px 4px rgba(153, 153, 153, 0.36);
//      position: absolute;
//      top: 30px;
//      left: 0;
//      z-index: 100;
//      padding: 0 10px;
//
//      .cutContainerTip{
//        font-size: 12px;
//        color: #999;
//        margin-top: 10px;
//      }
//      .cutContainerList{
//        margin-top: 10px;
//
//        .cutContainerItem{
//          width: 40px;
//          height: 40px;
//          margin-right: 5px;
//          margin-bottom: 5px;
//          background: #eee;
//          cursor: pointer;
//          float: left;
//
//          &:nth-child(4n){
//            margin-right: 0;
//          }
//        }
//      }
//    }
//    /*裁剪框选择 END*/
//  }
//
//  .typeBtnList{
//    display: inline-block;
//    position: absolute;
//    top: -3px;
//    left: 37px;
//
//    .typeBtn{
//      float: left;
//      width: 36px;
//      height: 26px;
//      line-height: 26px;
//      border: 1px solid #E2E2E2;
//      border-radius: 2px 0 0 2px;
//      text-align: center;
//      color: #666;
//      cursor: pointer;
//      position: relative;
//
//      &.active{
//        color: $primaryColorV6_2;
//      }
//      &:hover{
//
//        &:before{
//          content: "";
//          width: 36px;
//          height: 26px;
//          display: block;
//          border: 1px solid $primaryColorV6_2;
//          border-radius: 2px 0 0 2px;
//          position: absolute;
//          top: -1px;
//          left: -1px;
//        }
//      }
//    }
//    .typeBtnMore{
//      float: left;
//      width: 20px;
//      height: 26px;
//      line-height: 26px;
//      border: 1px solid #E2E2E2;
//      border-left: 0 solid #e2e2e2;
//      border-radius: 0 2px 2px 0;
//      text-align: center;
//      color: #666;
//      cursor: pointer;
//      position: relative;
//
//      i{
//        font-size: 16px;
//        transform: rotate(90deg);
//      }
//      &:hover{
//
//        i{
//          color: $primaryColorV6_2;
//        }
//        &:before{
//          content: "";
//          width: 20px;
//          height: 26px;
//          display: block;
//          border: 1px solid $primaryColorV6_2;
//          border-radius: 0 2px 2px 0;
//          position: absolute;
//          top: -1px;
//          left: -1px;
//        }
//      }
//    }
//    .typeBtnMoreList{
//      position: absolute;
//      top: 30px;
//      left: 0px;
//      background: #fff;
//      z-index: 100;
//
//      .typeBtnMoreItem{
//        width: 58px;
//        height: 26px;
//        line-height: 26px;
//        border: 1px solid RGBA(226, 226, 226, 1);
//        margin-top: -1px;
//        text-align: center;
//        cursor: pointer;
//        position: relative;
//
//        &:first-child{
//          margin-top: 0px;
//        }
//        &.active{
//
//          i{
//            color: $primaryColorV6_2;
//          }
//        }
//        &:hover{
//
//          i{
//            color: $primaryColorV6_2;
//          }
//          &:before{
//            content: "";
//            width: 58px;
//            height: 26px;
//            display: block;
//            border: 1px solid $primaryColorV6_2;
//            border-radius: 0 2px 2px 0;
//            position: absolute;
//            top: -1px;
//            left: -1px;
//              z-index: 10;
//          }
//        }
//      }
//    }
//  }
//}

/*背景属性栏：操作*/
.backgroundDoBlock {
  margin: 10px 0 25px 0;
  .buttonArea {
    display: inline-block;
    margin-top: 6px;
    .doBtn {
      width: 50px;
      float: left;
      text-align: center;
      color: #666;
      margin-left: 16px;
      position: relative;
      display: inline-block;
      float: left;
      margin-top: 10px;

      > p {
        font-size: 12px;
        margin-top: 2px;
      }
      &:nth-child(3n + 1) {
        margin-left: 0;
      }
      &:hover, &.active {
        color: #ff6161;
        cursor: pointer;
      }
      .buttonPopups {
        cursor: default;
        position: absolute;
        color: #333;
        top: 45px;
        left: 25px;
        z-index: 100;
        background: #fff;
        box-shadow: 0px 0px 10px rgba(200, 200, 200, 0.6);
        padding: 15px 10px;
        border-radius: 5px;
        font-size: 12px;
        width: 80px;

        .buttonPopupsItem {
          cursor: pointer;
          margin-top: 15px;
          color: #666;
          &:first-child {
            margin-top: 0;
          }
          &:hover {
            i {
              color: $btnHover;
            }
            p {
              color: $btnHover;
            }
          }
          i {
            float: left;
            margin-right: 5px;
            &:first-child {
              margin-left: 0;
            }
          }
          p {
            font-size: 12px;
            text-align: left;
          }
        }
      }
    }
  }
}

$assetCutBlockWidth: 600;
$assetCutBlockHeight: 550;
/*元素裁剪框*/
.assetCutPanel {
  position: fixed;
  z-index: 999999999;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  top: 0;
  left: 0;

  .assetCutBlock {
    position: absolute;
    top: 50%;
    left: 50%;
    width: $assetCutBlockWidth + px;
    height: $assetCutBlockHeight + px;
    background: #fff;
    margin-left: -$assetCutBlockWidth * 0.5 + px;
    margin-top: -$assetCutBlockHeight * 0.5 + px;
    border-radius: 5px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);

    .assetCutTop {
      height: 60px;
      line-height: 60px;
      text-align: center;
      font-size: 20px;
      corlor: $fontColorLight;
      .assetCutCancelBtn {
        position: absolute;
        right: 20px;
        top: 0px;
        i {
          transition: all 0.1s;
          cursor: pointer;
          &:hover {
            color: #ff7671
          }
        }
      }
    }

    .assetCutContent {
      position: relative;
      height: 400px;
      width: 555px;
      margin: 0 auto;
      background: url($imgHost + '/site/editor/assetCutBG.png');
      .imgShowStyle {
        background: url($imgHost + '/site/editor/assetCutBG.png');
      }
      .assetCutBox {
        /*上边*/
        .cutBorderTop {
          border-top: 2px dashed #333;
          position: absolute;
          top: -2px;
          right: 0;
          left: 0;
          cursor: n-resize;
        }
        /*右边*/
        .cutBorderRight {
          border-right: 2px dashed #333;
          position: absolute;
          top: 0;
          bottom: 0;
          right: -2px;
          cursor: e-resize;
        }
        /*下边*/
        .cutBorderBottom {
          border-bottom: 2px dashed #333;
          position: absolute;
          right: 0;
          bottom: -2px;
          left: 0;
          cursor: s-resize;
        }
        /*左边*/
        .cutBorderLeft {
          border-left: 2px dashed #333;
          position: absolute;
          top: 0;
          bottom: 0;
          left: -2px;
          cursor: w-resize;
        }

        .cutPoint {
          width: 10px;
          height: 14px;
          position: absolute;
          //background: url($imgHost + "/site/editor/assetIcon2.png") 0 -23px no-repeat;
          background: url($imgHost + '/site/editor/assetIconV6.1.png') no-repeat;
          margin-top: -5px;
          margin-right: -1px;
          cursor: e-resize;
        }
        .cutPointTop {
          background-position: 0 -66px;
          transform: rotate(90deg);
          left: 50%;
          top: 0;
          margin-top: -12px;
          margin-left: -5px;
          cursor: n-resize;
        }
        .cutPointRight {
          background-position: 0 -66px;
          transform: rotate(180deg);
          right: 0;
          top: 50%;
          margin-top: -7px;
          margin-right: -10px;
          cursor: e-resize;
        }
        .cutPointBottom {
          background-position: 0 -66px;
          transform: rotate(270deg);
          bottom: 0;
          left: 50%;
          margin-bottom: -12px;
          margin-left: -5px;
          cursor: s-resize;
        }
        .cutPointLeft {
          background-position: 0 -66px;
          left: 0;
          top: 50%;
          margin-top: -7px;
          margin-left: -10px;
          cursor: w-resize;
        }
        .cutPointLeftTop {
          width: 14px;
          background-position: 0 -100px;
          transform: rotate(360deg);
          left: 0;
          top: 0;
          margin-top: -10px;
          margin-left: -10px;
          cursor: nw-resize;
        }
        .cutPointRightTop {
          width: 14px;
          background-position: 0 -100px;
          transform: rotate(90deg);
          right: 0;
          top: 0;
          margin-top: -10px;
          margin-right: -10px;
          cursor: ne-resize;
        }
        .cutPointRightBottom {
          width: 14px;
          background-position: 0 -100px;
          transform: rotate(180deg);
          right: 0;
          bottom: 0;
          margin-bottom: -10px;
          margin-right: -10px;
          cursor: se-resize;
        }
        .cutPointLeftBottom {
          width: 14px;
          background-position: 0 -100px;
          transform: rotate(270deg);
          left: 0;
          bottom: 0;
          margin-bottom: -10px;
          margin-left: -10px;
          cursor: ne-resize;
        }

      }
    }

    .assetCutBottom {
      height: 90px;
      position: relative;

      .cutBtnItem {
        position: absolute;
        top: 25px;
        left: 50%;
        width: 150px;
        height: 40px;
        line-height: 40px;
        border-radius: 2px;
        color: #fff;
        text-align: center;
        cursor: pointer;
      }

      .saveCutBtn {
        margin-left: -160px;
        background: -webkit-linear-gradient(left top, #ff7b75, #ff6161);
      }
      .cancelCutBtn {
        margin-left: 10px;
        background: #999999;
      }
    }
  }
}

/*导航器*/
.navigator {
  cursor: move;
  position: absolute;
  bottom: 60px;
  right: 10px;
  // background: rgba(153, 153, 153, 0.5);
  width: 150px;
  height: 100px;
  border-radius: 5px;
  // border: 2px solid rgba(153, 153, 153, 0.5);
  z-index: 99999;
  background:rgba(255,255,255,1);
  box-shadow:0px 0px 4px 1px rgba(0,0,0,0.08);
  border-radius:4px;
  contain: layout;
  transition: all 0.2s linear;

  &:hover {
    &:after {
      content: '按住空格键拖拽画布';
      padding: 0 15px;
      height: 30px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 4px;
      position: absolute;
      top: -40px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 5;
      color: #FFF;
      font-size: 12px;
      text-align: center;
      line-height: 30px;
      white-space: nowrap;
    }
  }
  .navigatorWrap {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
;
  .navigatorContent {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 100px;
    height: 50px;
    background: rgba(153, 153, 153, 1);
  }

  .navigatorShowBox {
    position: absolute;
    left: -10px;
    top: -10px;
    width: 20px;
    height: 30px;
    border-radius:1px;
    border:1px solid rgba(255,69,85,1); 
    box-shadow: 0 0 0 100px rgba(252,252,253,.8);
  }
}
.navigator-add-page{
  bottom: 140px;
}
/*上传成功弹窗*/
.uploadCompletePopup {
  .tipComplete {
    font-size: 20px;
    font-weight: bold;
    margin: 145px 0;
    text-align: center;
    line-height: 30px;
  }
  .buttonArea {
    text-align: center;
  }
}

/*元素加载中START*/
@keyframes assetImgLoad {
  from {
    transform:translate3d(-50%,-50%,0) rotate3d(0,0,1,0deg);
  }

  to {
    transform:translate3d(-50%,-50%,0) rotate3d(0,0,1,360deg);
  }
}

.asset {
  contain: layout;
  .assetImgLoad {
    animation: assetImgLoad 1S infinite linear;
    position: absolute;
    font-size: 36px;
    left: 50%;
    top: 50%;
    // margin-top: -18px;
    // margin-left: -18px;
    height: 38px;
    width: 36px;
    line-height: normal;
    color: #f25b4a;
    z-index: 1;
//     webkit-transform: translate3d(0,0,0);
// -moz-transform: translate3d(0,0,0);
// -ms-transform: translate3d(0,0,0);
// -o-transform: translate3d(0,0,0);
// transform: translate3d(0,0,0);

   
  }

  // .newImgLoadHierarchy{
  //   z-index: 990000000000;
  // }

 .bg{
   background:rgba(0, 0, 0, 0.4);
   transform:translate(-50%,-50%);
   position: absolute;
   left: 50%;
   top: 50%;;
  //  z-index: 99000000000;
 }
  .image-watermark {
    width: 100%;
    height: 100%;
    background: url('//s.tuguaishou.com/background_watermark.png') repeat;
    position: absolute;
    z-index: 100;
    top: 0;
    left: 0;
  }
}

/*元素加载中END*/

/*辅助线START*/
.auxiliaryLine {
  position: absolute;
  z-index: 99999;
  width: 0;
  height: 0;
  left: 0;
  top: 0;

  .lineItem {
    position: absolute;
    border-width: 0;
    // border-color: rgba(43, 198, 243, 1);
    // border-top-width: 1px;
    // border-left-width: 1px;
    // border-style: dashed;

    border-color: #FF44FE;
    border-top-width: 1px;
    border-left-width: 1px;  
    border-style: solid;

    // border-image: url(//s.tuguaishou.com/site/editor/auxiliaryLine.png) 4 round;


    &.canvasLine {
      border-color: rgba(243, 43, 111, 1);
    }
  }
}

/*辅助线END*/

/*自定义画布大小START*/
.customizeCanvasSize {
  .itemTitle {
    font-size: 22px;
    font-weight: bold;
    color: $red3;
    text-align: center;
    margin-top: 5px;
  }
  .itemArea {
    display: inline-block;
    margin-top: 35px;

    .itemAreaTitle {
      color: #999;
      font-size: 16px;
      float: left;
    }
    .itemAreaContent {
      float: left;

      .symbol {
        display: inline-block;
        width: 30px;
        color: #999;
        text-align: center;
      }
      .itemAreaBtn {
        outline: none;
        border: 0 solid #fff;
        width: 110px;
        height: 44px;
        line-height: 44px;
        text-align: center;
        background: #eee;
        margin-top: -22px;
        font-size: 16px;
        &:focus {
          border: 1px solid $red3;
          border-radius: 4px;
        }
      }
      .sizeUnit {
        width: 82px;
        height: 44px;
        line-height: 44px;
        border-radius: 4px;
        text-align: center;
        background: #eee;
        border: 1px solid #ccc;
        color: #999;
        float: right;
        margin-top: -11px;
        margin-left: 25px;
        cursor: pointer;
        position: relative;
        &:hover {
          .sizeUnitList {
            display: inline-block;
          }
        }

        i {
          margin-left: 10px;
        }
        .sizeUnitList {
          display: none;
          position: absolute;
          top: 44px;
          left: 0;
          border: 1px solid #ccc;
          background: #eee;
          width: 100%;
          border-radius: 4px;

          p {
            display: inline-block;
            width: 100%;
            height: 22px;
            line-height: 22px;
            text-align: center;
            color: #999;
            &:hover {
              color: $red3;
            }
          }
        }
      }

    }
  }
  .sureBtn {
    width: 180px;
    height: 42px;
    line-height: 42px;
    text-align: center;
    margin: 40px auto 0;
    background: linear-gradient(-90deg, #fd7147 0%, #f65e30 100%);
    border-radius: 20px;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
  }
}

/*自定义画布大小END*/

/*引导面板START*/
.guideArea {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 99999999999;
  .block1 {
    background: url($imgHost + "/site/editor/guide1.png");
    width: 810px;
    height: 178px;
    position: absolute;
    top: 62px;
    left: 0;
  }
  .block2 {
    background: rgba(0, 0, 0, 0.3);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 62px;
  }
  .block3 {
    background: rgba(0, 0, 0, 0.3);
    position: absolute;
    top: 62px;
    left: 810px;
    width: 100%;
    height: 100%;
  }
  .block4 {
    background: rgba(0, 0, 0, 0.3);
    position: absolute;
    top: 240px;
    left: 0;
    width: 810px;
    height: 100%;
  }
  .sureBtn {
    width: 100px;
    height: 34px;
    position: absolute;
    top: 200px;
    left: 704px;
    border-radius: 4px;
    cursor: pointer;
  }
}

/*引导面板END*/

/*意见反馈START*/
.opinionPopup {
  margin: 0 25px;

  .popupTitle {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    color: #ff857f;
    margin-top: 9px;
  }
  .opinionItem {
    margin-top: 25px;

    .opinionItemT {
      color: #ff857f;
      font-size: 16px;

      .copyNum {
        //text-underline: ;
        text-decoration: underline;
        cursor: pointer;
        display: inline;
      }
    }
    .opinionText {
      width: 420px;
      height: 103px;
      box-sizing: border-box;
      resize: none;
      color: rgb(102, 102, 102);
      font-size: 14px;
      border-width: 1px;
      border-style: solid;
      border-color: rgb(253, 205, 205);
      border-image: initial;
      border-radius: 2px;
      margin: 13px 0px 0;
      padding: 12px 15px;
      outline: none;
      font-family: microsoft yahei;

      &::placeholder {
        color: #999;
      }
    }
    .opinionLx {
      width: 420px;
      height: 46px;
      border: 1px solid #fdcdcd;
      border-right: 1px solid #fdcdcd;
      border-radius: 2px;
      line-height: 46px;
      padding: 0 15px;
      color: #666;
      box-sizing: border-box;
      margin: 13px 0 0px;

      &::placeholder {
        color: #999;
      }
    }
    .opinionSubmit {
      display: block;
      margin: 37px auto 0;
      width: 199px;
      height: 40px;
      border-radius: 4px;
      color: #fff;
      font-size: 16px;
      line-height: 40px;
      text-align: center;
      background: #ffa383;
      background: linear-gradient(135deg, #ffa383 0%, #fc766f 100%);
      cursor: pointer;

      &:hover {
        opacity: 0.9;
      }
    }
  }
}

/*意见反馈END*/
/*意见反馈New START*/
.opinionPopupNew {
  text-align: center;

  .popupTitle {
    font-size: 20px;
    color: rgba(56, 64, 68, 1);
    text-align: center;
    margin-top: 42px;
    margin-bottom: 36px;
  }
  .fk-qrcode {
    width: 202px;
    border: 1px #E5E5E5 solid;
    margin-bottom: 32px;
  }
  .fk-p {
    width: 480px;
    display: block;
    text-align: left;
    font-size: 14px;
    color: #384044;
    margin: 0 auto;
    margin-bottom: 16px;
    .sidebar-icon2, &.fk_qq_qun a {
      cursor: pointer;
      border-radius: 4px;
      border: 1px #ff4555 solid;
      width: 80px;
      height: 26px;
      color: #ff4555;
      display: inline-block;
      line-height: 26px;
      text-align: center;
      margin-left: 10px;
      text-decoration: none;
    }
    &.fk_qq_qun {
      padding-left: 88px;
    }
    .commonProblem {
      color: #30A9E9;
    }
  }
}

/*意见反馈New END*/

/*模板数据加载失败弹框 START*/
.templateGetFaillPop {
  text-align: center;
  .failPopupTitle{
    color: #666;
    padding: 50px 0 45px;
    span{
      font-size: 12px;
      color: #1690FF;
      width:78px;
      height:26px;
      line-height: 26px;
      background:rgba(240,248,255,1);
      border-radius:4px;
      border:1px solid rgba(22,144,255,1);
      display: inline-block;
      margin-left: 10px;
      i{
        font-size: 14px;
      }
      cursor: pointer;
    }
  }
  .failPopupSure{
    width: 194px;
    height: 42px;
    background:rgba(255,69,85,1);
    border-radius:4px;
    color:rgba(255,255,255,1);
    font-size:14px;
    line-height: 42px;
    margin: 0 auto;
    display: block;
  }
}

/*模板数据加载失败弹框 END*/


/*文字无法更改?New START*/
.opinion2PopupNew {
  width: 353px;
  height: 312px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -156px;
  margin-left: -176px;
  background: #fff;
  border-radius: 4px;

  &.successContentAreaStyle {
    width: 353px;
    height: 172px;
    margin-top: -86px;
    margin-left: -156px;
  }
  .closeBtn {
    position: absolute;
    top: 0;
    right: -44px;
    width: 34px;
    height: 34px;
    line-height: 34px;
    background: RGBA(0, 0, 0, 0.3);
    text-align: center;
    border-radius: 34px;
    cursor: pointer;

    i {
      font-size: 15px;
      color: #fff;
    }
  }
  .contentArea {

    .titleText {
      margin-top: 48px;
      font-size: 18px;
      color: #444;
      text-align: center;
    }
    .textArea1 {
      margin: 0 auto;
      margin-top: 32px;
      width: 259px;
      height: 20px;
      line-height: 20px;

      .leftText {
        font-size: 14px;
        color: #666;
        float: left;
      }
      .rightText {
        font-size: 12px;
        color: #999;
        float: right;
      }
    }
    .qqInput {
      display: block;
      margin: 0 auto;
      margin-top: 20px;
      width: 249px;
      height: 34px;
      border: 1px solid rgba(206, 206, 206, 1);
      border-radius: 4px;
      //outline: none;
    }
    .tipArea {
      margin-top: 2px;
      font-size: 12px;
      color: #f02f1a;
      margin-left: 50px;
      height: 16px;
    }
    .submitBtn {
      width: 109px;
      height: 36px;
      line-height: 36px;
      text-align: center;
      margin: 0 auto;
      margin-top: 18px;
      cursor: pointer;
      border-radius: 4px;
      background: $primaryColorV6_2;
      color: #fff;
    }
    .tipArea2 {
      margin-top: 28px;
      font-size: 12px;
      color: #999;
      text-align: center;
    }
  }
  .successContentArea {

    .tipText1 {
      font-size: 18px;
      color: #444;
      text-align: center;
      margin-top: 35px;
    }
    .tipText2 {
      margin: 0 auto;
      margin-top: 18px;
      width: 225px;
      height: 38px;
      font-size: 14px;
      color: #666;
      text-align: center;
    }
  }
}

/*文字无法更改?New END*/

///*意见反馈按钮START*/
//.opinionShowBtn{
//  width: 124px;
//  height: 36px;
//  line-height: 36px;
//  background: linear-gradient(135deg, #ffa383 0%,#fc766f 100%);
//  text-align: center;
//  right: 5px;
//  bottom: 10px;
//  color: #fff;
//  position: absolute;
//  z-index: 99999999;
//  border-radius: 4px;
//  cursor: pointer;
//  font-size: 14px;
//
//  &:hover{
//    opacity: 0.9;
//  }
//}
///*意见反馈按钮END*/

/*纯色背景提醒START*/
.solidColorTip {
  position: absolute;
  top: -30px;
  left: 0;
  font-size: 12px;
  color: #ff7671;
  text-align: center;
  width: 100%;
  opacity: 0;
  transition: all 1s;
  cursor: default;
}

/*纯色背景提醒END*/

/*添加辅助线START*/
.addReLine {
  .popTitle {
    font-size: 22px;
    color: $red3;
    width: 100%;
    text-align: center;
  }

  .item {
    margin-top: 45px;
    display: inline-block;
    width: 100%;
    padding-left: 100px;

    .itemTitle {
      color: #999999;
      font-size: 16px;
      float: left;
      margin-right: 30px;
    }
    .itemArea {
      float: left;

      input[type='radio'] {
        margin-right: 10px;
      }
    }

    .relinePlace {

      p {
        font-size: 16px;
        color: #999999;
        float: left;
        margin-left: 10px;
      }
      input {
        float: left;
        width: 110px;
        height: 42px;
        border: 0;
        outline: none;
        background: #f4f4f4;
        font-size: 16px;
        color: #999999;
        text-align: center;
        margin-top: -10.5px;
        border-radius: 4px;
      }
    }
  }

  .addBtn {
    width: 180px;
    height: 42px;
    line-height: 42px;
    text-align: center;
    margin: 35px auto 0;
    background: linear-gradient(-90deg, #fd7147 0%, #f65e30 100%);
    border-radius: 20px;
    color: #fff;
    font-size: 16px;
    letter-spacing: 4px;
    font-weight: bold;
    cursor: pointer;
  }
}

/*添加辅助线END*/

/*辅助线显示区域START*/
.relineContent {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9999;

  .itemReline {
    position: absolute;
    padding: 1px;
    margin: -1px;

    .realLine {
      content: '';
      width: 100%;
      height: 100%;
      background: #f25b4a;
      display: inline-block;
      position: absolute;
    }
  }
}

/*辅助线显示区域END*/

.toolElementPanel {
  .elementEditBlock {
    .item {
      .colorsArea {
        width: 196px;
        height: 38px;
        //background: #f8f8f8;
        border-radius: 4px;
        position: absolute;
        left: 32px;
        top: 50%;
        margin-top: -19px;

        .colorsItem {
          margin-left: 6px;
          margin-top: 6px;
          //width: 20px;
          //height: 20px;
          //border-radius: 4px;
          //cursor: pointer;
          //box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
          border: 1px solid #e6e6e6;
          border-radius: 4px;
          width: 26px;
          height: 26px;
          //float: left;
          //margin-left: 28px;
          position: relative;
          cursor: pointer;
          //margin-top: -3px;

          .colorMorePanel {
            width: 179px;
            height: 148px;
            background: rgba(250, 250, 250, 1);
            box-shadow: 0 2px 4px #ccc;
            border-radius: 4px;
            position: absolute;
            top: 40px;
            left: -38px;
            z-index: 100;
            padding: 0 0 0 14px;
            cursor: default;

            .colorMorePanelTitle {
              height: 36px;
              line-height: 36px;
              font-size: 14px;
              color: #666;
            }
            .colorBlockList {

              .colorBlockItem {
                width: 26px;
                height: 26px;
                box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.1);
                border-radius: 4px;
                margin-left: 9px;
                margin-bottom: 9px;
                float: left;
                cursor: pointer;

                &:nth-child(5n + 1) {
                  margin-left: 0;
                }
                &.colorBlockMore {
                  background-image: url($imgHost + '/index_img/editorV6.0/icon_colorPicker.png');
                  background-repeat: no-repeat;
                  background-position: -6px -5px;
                }
              }
            }
          }
          .colorPanel {
            position: absolute;
            top: 164px;
            left: -21px;
            z-index: 10;
          }
        }
        .sketch-picker {
          position: absolute;
          left: -30px;
          top: 30px;
          z-index: 10;
        }
      }
    }
  }
}