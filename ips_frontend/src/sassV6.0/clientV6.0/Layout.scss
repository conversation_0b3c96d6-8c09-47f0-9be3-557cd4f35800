@import "Base";

body,html {
    height: 100%;
}

body {
    margin: 0px;
}

#root {
    height: 100%;
}
.rootLayout {
    height: 100%;
    min-width: 650px;
    width: 100%;
    min-height: 482px;
    height: 100%;
    .infoLayout {
        height: $infoHeight;
        // background: $topNavBGColor;
        z-index:99999999;
        position: absolute;
        min-width: inherit;
        width: 100%;
        // border-bottom: 1px solid #E9E8E8;
        contain: layout;
        pointer-events: none;
    }

    .contentWrapper {
        min-height: 420px;
        position: absolute;
        // top: $infoHeight;
        top: 0;
        bottom:0px;
        min-width: inherit;
        width: 100%;
        background: $bgColor;
        .left-PageAnimationPanel {
            position: absolute;
            top: 0;
            right: 260px;
            padding-right: 35px;
            background: #FFFFFF;
        }
        .helperLayout {
            background: #fff;
            width: $floatToolPanel - 15;
            padding: 5px 15px;
            padding-right: 0;
            z-index:999999;
            //height: 534px;
            height: 100%;
            border-radius: 16px 0 0 16px;
        }
        .navLayout {
            contain: layout;
            position: absolute;
            z-index: 20;
            left: 0;
            top:0;
            width: $navLayout;
            height: calc(100% - var(--AssetPanelPadding));
            padding-top: var(--AssetPanelPadding);
            text-align: center;
            // z-index: 1000;
            background: #F0F1F5;
            // border-right: 1px solid #E9E8E8;
            .navCustomService{
                position: absolute;
                z-index: 20;
                bottom: 0px;
                left: 0;
                width: 100%;
                display:flex;
                padding: 0;
                flex-direction: column;
                padding-top: 40px;
                background: linear-gradient(180deg, rgba(240, 241, 245, 0) 0%, #F0F1F5 35.48%, #F0F1F5 100%);
                pointer-events: none;

                &.open {
                    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #FFF 35.48%, #FFF 100%);
                }
                
                .uploadBtn {
                    cursor: pointer;
                    width: 72px;
                    height: 72px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    flex-direction: column;
                    color: #1f1a1b;
                    pointer-events: initial;
                    // margin-bottom: 16px;
                    .uploadFont{
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 20px;
                    }
                    .iconfont {
                        box-sizing: border-box;
                        font-size: 22px;
                        width: 32px;
                        height: 32px;
                        padding: 5px;
                        color: #1f1a1b;
                        margin-bottom: 2px;
                    }
                    .icon-bianjiqi-xin-zuocedaohangshangchuanxuanting {
                        display: none;
                    }
                    &.hover,
                    &:hover, &.active{
                        .icon-bianjiqi-xin-zuocedaohangshangchuanmoren {
                            display: none;
                        }
                        .icon-bianjiqi-xin-zuocedaohangshangchuanxuanting {
                            display: block;
                        }
                        .iconfont {
                            color: #ef3964;
                            border-radius: 8px;
                            background: #fff;
                            box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.15);
                        }
                    }

                    &.active{
                        color: #ff4555;
                        .iconfont {
                            color: #ef3964;
                            border-radius: 8px;
                            background: #fff;
                            box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.15);
                        }
                    }
                }
                .navNavigatorIcon {
                    cursor: pointer;
                    width: 40px;
                    height: 28px;
                    background-color: #fff;
                    border-radius: 4px;
                    margin: 0;
                }
                .navNavigatorPopupTip {
                    position: relative;
                    margin-bottom: 32px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    &:hover {
                        color: #ff4555;
                    }
                    .popupTipBox {
                        .popupTipImg {
                            img {
                                width: 50px;
                                height: 50px;
                                position: absolute;
                                top: -11px;
                                left: 15px;
                            }
                        }
                        .popupTip {
                            display: flex;
                            align-items: center;
                            .popupTip_arrow {

                                width: 0px;
                                height: 0px;
                                border-width: 14px 24px;
                                border-style: solid;
                                border-color: transparent;
                                border-right-color:#FF4B76;
                                position: absolute;
                                top: -1px;
                                left: 35px;
                            }
                            .popupTip_content {
                                position: absolute;
                                top: -63px;
                                left: 77px;
                                width: 246px;
                                height: 137px;
                                background: linear-gradient(355deg, #FF416E 0%, #FF7999 100%);
                                box-shadow: 0px 4px 4px 0px rgba(255,69,85,0.15);
                                border-radius: 10px 10px 10px 10px;
                                opacity: 1;
                                display: flex;
                                flex-direction: column;
                                align-items: center;
                                .hitTip {
                                    font-size: 20px;
                                    font-family: PingFang SC-Medium, PingFang SC;
                                    font-weight: 500;
                                    color: #FFFFFF;
                                    line-height: 23px;
                                    text-shadow: 0px 1px 0px rgba(212,0,50,0.5);
                                    margin-top: 14px;
                                }
                                .seekOut {
                                    font-size: 14px;
                                    font-family: PingFang SC-Regular, PingFang SC;
                                    font-weight: 400;
                                    color: #FFFFFF;
                                    line-height: 16px;
                                    text-shadow: 0px 1px 0px rgba(212,0,50,0.5);
                                    margin: 10px 0px 17px 0px;
                                }
                                .know {
                                    cursor: pointer;
                                    text-align: center;
                                    line-height: 30px;
                                    width: 76px;
                                    height: 30px;
                                    box-shadow: 0px 1px 0px 0px #D40032;
                                    border-radius: 4px 4px 4px 4px;
                                    opacity: 1;
                                    border: 1px solid #FFFFFF;
                                    font-size: 14px;
                                    font-family: PingFang SC-Regular, PingFang SC;
                                    font-weight: 400;
                                    color: #FFFFFF;
                                    text-shadow: 0px 1px 0px #D40032;
                                }
                            }
                        }
                    }
                }
                .navigatorIconTips {
                    position: absolute;
                    top: 31%;
                    right: -58px;
                    display: none;
                    width: 72px;
                    line-height: 26px;
                    transform: translateY(-50%);
                    font-size: 12px;
                    background: rgba(0, 0, 0, 0.7);
                    color: #fff;
                    border-radius: 4px;

                    &::before {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: -8px;
                        width: 0;
                        height: 0;
                        transform: translateY(-50%);
                        border-top: 4px solid transparent;
                        border-right: 4px solid rgba(0, 0, 0, 0.7);
                        border-bottom: 4px solid transparent;
                        border-left: 4px solid transparent;
                    }
                }
                .navHelpIcon{
                    cursor: pointer;
                    i{
                        color:#ff4555;
                        font-size: 22px;
                    }
                    .navTextHelp{
                        font-size:12px;
                        font-family:PingFangSC-Regular;
                        font-weight:400;
                        color:rgba(102,102,102,1);
                        line-height:24px;
                        // padding: 5px 0 10px;
                        width: 62px;
                        height: 24px;
                        margin: 0 auto;
                        border-radius: 4px;
                        border: 1px solid rgba(102,102,102,1);
                        display: inline-block;
                        &:hover{
                            border:1px solid rgba(255,69,85,1);
                            color: #FF4555;
                        }
                    }
                }
                .navHoverCustom{
                    position: absolute;
                    bottom: 0;
                    left: $navLayout+2;
                    // box-sizing: border-box;
                    width: 324px;
                    height: 598px;
                    background:rgba(255,255,255,1);
                    box-shadow:0px 2px 6px 2px rgba(228,233,238,1);
                    .navHoverHelpCemter{
                        padding: 70px 20px 0;
                        height: 521px;
                        overflow: auto;
                        box-sizing: border-box;
                        &::-webkit-scrollbar{
                            width: 4px;
                        }
                        &::-webkit-scrollbar-thumb{
                            border-radius: 10px;
                            -webkit-box-shadow:inset 0 0 5px rgba(0,0,0,0.2);
                            background-color: rgba(0,0,0,0.1);
                        }
                        &::-webkit-scrollbar-track{
                            -webkit-box-shadow:inset 0 0 5px rgba(0,0,0,0.2);
                            border-radius: 0;
                            background-color: rgba(0,0,0,0.1);
                        }

                        .helpCenterHeader{
                            text-align: center;
                            padding: 25px 0px 25px;
                            border-bottom: 1px solid rgba(244,244,244,1);
                            font-family:PingFangSC-Regular;
                            color:rgba(32,32,32,1);
                            line-height:22px;
                            letter-spacing:1px;
                            position: absolute;
                            top: 0;
                            left: 20px;
                            width: 284px;
                            box-sizing: border-box;
                            background-color: #fff;
                        }
                        .helpCenterPart{
                            font-size: 14px;
                            text-align: left;
                            border-bottom: 1px solid rgba(244,244,244,1);
                            .helpCenterProblem{
                                padding: 25px 0;
                                cursor: pointer;
                                &:hover{
                                    color:#ff4555;
                                }
                                i{
                                    float: right;
                                    padding: 0  10px;
                                    z-index: 99;
                                }
                            }
                            .helpCenterAnswer{
                                font-family:PingFangSC-Regular;
                                color:rgba(136,136,136,1);
                                .helpAnswer{
                                    line-height:25px;
                                    &:last-child{
                                        margin-bottom: 25px;
                                    }
                                }
                            }
                        }

                    }
                    .customOnline{
                        bottom: 0;
                        // position: absolute;
                        left: 0;
                        width: 324px;
                        box-sizing: border-box;
                        padding: 17px 58px;
                        background-color: #fff;
                        box-shadow:0px 2px 6px 2px rgba(228,233,238,1);
                        .customOnlineButton{
                            display: block;
                            cursor: pointer;
                            width:194px;
                            background:rgba(255,69,85,1);
                            border-radius:27px;
                            text-align: center;
                            color:#fff;
                            font-size:14px;
                            font-family:PingFangSC-Regular;
                            padding: 10px 0 9px;
                            .iconfont{
                                margin-right: 10px;
                                font-size: 22px;
                                vertical-align: middle;
                            }
                        }
                    }
                }
            }
        }
        .toolLayout {
            position: absolute;
            z-index: 10;
            left: 72px;
            top: 0;
            bottom: 0;
            width: $toolWidth - $navLayout;
            //padding: 15px 15px 0;
            background: #fff;
            user-select:none;
            //box-shadow: 1px 0px 6px rgba(0,0,0,0.2);
            transition: left 150ms ease-in-out;

            @keyframes toolLayoutHide {
                0% {
                    display: block;
                    left: 72px;
                }
                100% {
                    display: none;
                    left: 72px - ($toolWidth - $navLayout);
                }
                
            };

            @keyframes toolLayoutShow {
                0% {
                    display: none;
                    left: 72px - ($toolWidth - $navLayout);
                    z-index: 10;
                }
                100% {
                    display: block;
                    left: 72px;
                    z-index: 10;
                }
            }
        }
        .rightLayout {
            position: absolute;
            left: 0;
            top: $infoHeight + 7px;
            bottom: 0;
            box-shadow: -5px 0 5px -5px #cecece;
            width: $toolWidth;
            z-index: 999999;
            background: #F0F1F5;
            // transition: all 0.2s linear;
            contain: layout;

            .hideBtn{
                box-sizing: border-box;
                position: absolute;
                z-index: 100;
                top: 50%;
                right: -13px;
                margin-top: -23px;
                display: flex;
                width: 26px;
                height: 42px;
                padding: 12px 4px;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                flex-shrink: 0;
                cursor: pointer;
                border-radius: 20px;
                border: 1px solid  #D2D1D1;
                background: #FFF;
                box-shadow: 0px 0px 5.2px 0px rgba(0, 0, 0, 0.15);

                i {
                    display: inline-block;
                    position: relative;
                    font-size: 18px;
                    width: 18px;
                    line-height: 34px;
                    border-radius: 15px;
                    color: #4C4849;
                }

                .popover {
                    position: absolute;
                    top: 50%;
                    right: -77px;
                    display: none;
                    width: 72px;
                    line-height: 26px;
                    transform: translateY(-50%);
                    font-size: 12px;
                    background: rgba(0, 0, 0, 0.7);
                    border-radius: 4px;
                    color: #fff;
                    text-align: center;

                    &::before {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: -8px;
                        width: 0;
                        height: 0;
                        transform: translateY(-50%);
                        border-top: 4px solid transparent;
                        border-right: 4px solid rgba(0, 0, 0, 0.7);
                        border-bottom: 4px solid transparent;
                        border-left: 4px solid transparent;
                    }
                }

                &:hover {
                    i {
                        background: #E9E8E8;
                    }
                    .popover {
                        display: block;
                    }
                }
            }
        }
        .navigatorContainer {
            position: absolute;
            top: 0;
            left: -100px;
            width: 168px;
            // width: 100%;
            background: #fff;
            height: 100%;
            // box-sizing: border-box;
            // padding: 14px;
            transition: all 0.3s;
            // max-height: 880px;
            // overflow-y: auto;
            .addNewPage {
                text-align: center;
                width: 140px;
                // width: 100%;
                height: 40px;
                background: #F1F3F7;
                border-radius: 4px;
                line-height: 36px;
                margin: 13px 14px;
                // margin: 13px 0;
                box-sizing: border-box;
                cursor: pointer;
                .addIcon {
                    width: 12px;
                    height: 12px;
                    color: #202020;
                }
                i {
                    font-size: 13px;
                }
                .addText {
                    width: 56px;
                    height: 20px;
                    font-size: 14px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #202020;
                    line-height: 20px;
                    margin-left: 6px;
                }
                // &:hover {
                //    .addIcon {
                //         color: #202020;
                //    }
                //    .addText {
                //        color: #202020;
                //    }
                // }
            }
            .editSaveTemplate {
                // width: 100%;
                width: 168px;
                height: calc(100% - 66px);
                border-radius: 2px;
                overflow-y: scroll;
                box-sizing: border-box;
                padding-bottom: 10px;
                &::-webkit-scrollbar { width: 0 }
                    -ms-overflow-style: none;
                    overflow: -moz-scrollbars-none;
                .image_container_box {
                    padding: 6px 14px;
                    width: 100%;
                    height: 74px;
                    box-sizing: border-box;
                    cursor: pointer;
                    .detail_floor_item {
                        position: relative;
                        border-radius: 2px;
                        border: 1px solid #D6DBE1;
                        background: #fff;
                        &.active {
                            border: 2px solid #FF4555;
                            background: #fff;
                        }
                    }
                    .imageTemplate {
                        // width: 100%;
                        width: 140px;
                        height: 74px;
                        border-radius: 2px;
                        position: relative;
                    }
                    &.active {
                        background: #ECEDF0;
                    }
                    &:hover {
                        background: #ECEDF0;
                        .icon_copy_box {
                            display: block;
                        }
                        .icon_delete_box {
                            display: block;
                        }
                    }
                    .icon_copy_box {
                        display: none;
                        position: absolute;
                        width: 18px;
                        height: 18px;
                        background: rgba(0, 0, 0, 0.6);
                        border-radius: 1px;
                        box-sizing: border-box;
                        line-height: 12px;
                        top: 8px;
                        left: 9px;
                        .icon_copy_position {
                            position: absolute;
                            left: 2px;
                            color: #fff;
                            width: 12px;
                            height: 12px;
                        }
                        i {
                            font-size: 12px;
                            margin: 3px 1px;
                        }
                    }
                    .icon_delete_box {
                        display: none;
                        position: absolute;
                        width: 18px;
                        height: 18px;
                        background: rgba(0, 0, 0, 0.6);
                        border-radius: 1px;
                        box-sizing: border-box;
                        line-height: 12px;
                        top: 8px;
                        right: 10px;
                        .icon_delete_position {
                            position: absolute;
                            right: 5px;
                            color: #fff;
                            width: 12px;
                            height: 12px;
                        }
                        i {
                            font-size: 12px;
                            margin: 3px -2px;
                        }
                    }
                    .image_number_box {
                        width: 14px;
                        height: 14px;
                        background: rgba(0, 0, 0, 0.3);
                        border-radius: 1px;
                        position: absolute;
                        bottom: 7px;
                        right: 10px;
                        padding: 1px 4px;
                        box-sizing: border-box;
                        line-height: 12px;
                        color: #fff;
                        font-size: 12px;
                    }
                    .image_number {
                        position: absolute;
                        font-size: 12px;
                        height: 12px;
                        width: 6px;
                    }
                }
            }
        }
        .canvasLayout {
            position: absolute;
            z-index: 1;
            left: $navLayout;
            right: $floatToolPanel;
            top: 0;
            bottom: 0;
            background: $bgColor;
            // overflow: hidden;
            // contain: layout;
            // transition: all 0.15s linear;
            // 登陆提示
            .loginAlert{
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                background:rgba(0,0,0,0.5);
                z-index: 1000;
                height: 40px;
                line-height: 40px;
                font-size:14px;
                color: #fff;
                text-align: center;
                .btn{
                    width:66px;
                    height:30px;
                    border-radius:4px;
                    display: inline-block;
                    vertical-align: baseline;
                    line-height: 30px;
                    text-align: center;
                    margin: 0 9px;
                    cursor: pointer !important;
                }
                .closeBtn{
                    width:18px;
                    height:18px;
                    background:rgba(0,0,0,0.3);
                    border-radius: 50%;
                    vertical-align: baseline;
                    line-height: 18px;
                    display: inline-block;
                    cursor: pointer;
                    margin-left: 36px;
                }
            }
        }
        .floatToolPanel{
            position: absolute;
            right: 0px;
            top: $infoHeight + 7px;
            bottom: 0px;
            background: #fff;
            width: $floatToolPanel;
            //width: 260px;
            z-index: 9999999;
            border-left: 1px solid #E9E8E8;
            border-radius: 16px 0px 0px 16px;
            box-shadow: 0px 0px 4.8px 0px rgba(0, 0, 0, 0.15);

            .fix-tool-panel {
                box-sizing: border-box;
                position: absolute;
                top: 0;
                left: 0;
                padding: 8px 0;
                text-align: center;
                border-radius: 8px 0 0 8px;
                background: #fff;
                cursor: pointer;
                border: 1px solid #D2D1D1;
                border-right: none;
                transform: translateX(-100%);

                &.show {
                    top: 76px;
                }

                .content {
                    display: flex;
                    align-items: center;

                    .icon-Frame {
                        padding: 0 3px;
                    }

                    .open-text {
                        white-space: nowrap;
                        line-height: 56px;
                        border-left: 1px solid #D2D1D1;
                        margin: -8px 0;
                        width: 80px;
                        color: #000;
                        font-size: 12px;
                        font-weight: 600;
                    }
                }

                .fix-text {
                    display: inline-block;
                    padding: 0 5px;
                    color: #797676;
                    font-size: 12px;
                    line-height: 20px;
                    width: 16px;
                }

                .icon-Frame {
                    color: rgba(165, 163, 164, 1);
                    font-size: 18px;
                    line-height: 40px;
                }

                .left {
                    display: inline-block;
                    transform: rotate(180deg);
                }
            }
            .tab-box{
                display: flex;
                height: 60px;
                width: 100%;
                padding:0 20px;
                .tab-item{
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 115px;
                    border-bottom: 1px solid #E9E8E8;
                    color: var(--unnamed, #1F1A1B);
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 700;
                    cursor: pointer;
                }
                .active{
                    border-bottom: 2px solid #EF3964;
                }

            }
            .defaultTopicContainer{
                box-sizing: border-box;
                padding: 25px 20px 15px;
                font-size: 14px;
                font-weight: 600;
                color: #202020;
                line-height: 14px;
                // border-bottom: 1px solid #EBEBEB;
                width: 100%;
                &:not(:first-child){
                    // border-top: 1px solid #EBEBEB;
                    // margin-top: 30px;
                    padding-top:30px;
                }
            }
        }
        /** 右侧元素动效面板 */
        .right-asset-effect {
            z-index: 100001;
            position: absolute;
            right: -20px;
            background: #fff;
            top: 0;
            transition: all 0.3s ease-in-out;
            height: 100vh;
        }
        .right-asset-animation {
            z-index: 100002;
            position: absolute;
            right: -20px;
            background: #fff;
            top: 0;
            transition: all 0.3s ease-in-out;
            height: 100vh;
        }
        //.floatToolPanel{
        //    position: absolute;
        //    right: 15px;
        //    top: 50px;
        //    background: #fff;
        //    width: $floatToolPanel;
        //    box-shadow: 0 0 10px #cecece;
        //    z-index: 9999999;
        //}
        .disableEditor {
            position: fixed;
            z-index: 10000000;
            top: 58px;
            left: 0;
            right:0px;
            width: 100%;
            min-width: 900px;
            height: calc(100% - 58px);
          }
    }

    /*元素鼠标悬浮提示信息 START*/
    #assetHoverTips,#assetHoverTipsOther{
        position: fixed;
        top: 0px;
        left: 0px;
        z-index: 10000000;

        .tipContent{
            letter-spacing:initial;
            //content:'双击替换图片';
            font-family:initial;
            height:30px;
            line-height: 30px;
            padding: 0 15px;
            background:rgba(0,0,0,.8);
            border-radius:4px;
            white-space: nowrap;
            position: absolute;
            top: -40px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 5;
            color: #FFF;
            font-size: 12px;
            text-align: center;
        }
    }

    #assetClickFont{
        position: fixed;
        top: 0px;
        left: 0px;
        z-index: 10000000;
        .canvasText{
            width: 56px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #FFFFFF;
            line-height: 30px;
        }
        .content{
            height: 44px;
            background: rgba(44, 42, 48, 0.85);
            border-radius: 4px;
            position: absolute;

            z-index: 5;
            display: flex;
            align-items: center;
            .selectedColor{
                width: 28px;
                height: 28px;
                border-radius: 1px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                &:hover{
                    border: 1px solid #FF4555;

                }
            }

            .font{
                height: 28px;
                border-radius: 1px;
                margin-left: 8px;
                margin-right: 8px;
                .fontName{
                    width: 98px;
                    height: 14px;
                    font-size: 14px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #FFFFFF;
                    line-height: 14px;
                }
                .colorMorePanel{
                    width: 198px;
                    background: rgba(255, 255, 255, 1);
                    box-shadow: 0 2px 6px 2px rgba(228, 233, 238, 1);
                    border-radius: 2px;
                    position: absolute;
                    top: 48px;
                    right: 93px;
                    z-index: 100;
                    background: #2C2A30;
                    .colorMorePanelTitle {
                        padding: 0 14px;
                        font-size: 12px;
                        color: #333;
                        line-height: 1;
                        margin-bottom: 22px;
                    }
                    .colorBlockList {
                        padding: 19px 10px;
                        max-height: 260px;
                        overflow-x: hidden;
                        overflow-y: auto;
                        .colorBlockItem {
                            display: flex;
                            margin-bottom: 10px;
                            height: 22px;
                            border-radius: 2px;
                            border: 1px solid rgba(255, 255, 255, 0.2);
                            line-height: 20px;
                            text-align: center;
                            cursor: pointer;
                            position: relative;
                            & > p {
                              flex: 1;
                            }
                        }
                    }

                    .canvas_scroll::-webkit-scrollbar {
                        background: none;
                        height: 2px;
                        width: 6px;
                    }

                    .canvas_scroll::-webkit-scrollbar-track {
                        border-radius: 3px;
                    }

                    .canvas_scroll::-webkit-scrollbar-thumb {
                        background: $scrollColor!important;
                        border-radius: 3px;
                        transition: background .2s ease;
                    }
                }

            }

            .size{
                width: 70px;
                height: 28px;
                border-radius: 1px;
                .value{
                    font-size: 14px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #FFFFFF;
                    line-height: 20px;
                }
            }
        }
        .fontSizeCanvas {
            width: 69px;
            margin-left: 10px;
            cursor: pointer;

            .icon{
                color: rgba(255, 255, 255, 1);
            }
            // 下拉box公共样式
            .canvasCommonSelect {
                width: inherit;
                height: 28px;
                width: 28px;
                border-radius: 2px;
                line-height: 28px;
                cursor: pointer;
              .moreColorIcon {
                display: inline-block;
                width: 36px;
                height: 36px;
                float: right;
                background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAABmJLR0QAAAAAAAD5Q7t/AAAACXBIWXMAAAsSAAALEgHS3X78AAAEhklEQVRYw8WZX2hbVRzHP/cuDbj2Vli3uNarvuhqGcjoFgobttlAuoXa4YvdXGAo2j/qmikImgcfVOrAh1HX1rbIxBHt3gYyO0XY0qrD2gVGoKwbPujsVt3WCU26zCbN9eHeq2l677lJSeoX8pDz++bcT36593fO70RiFUruPSkDXmA3sAPYAjwMlBuWhfT9UzeAa8Al4AIwqUQimUKvJRUIpgKvAYeAR0Te9P1TuUO/A18A/UokMlNUwOTek1XAe8DLgDufz1gAmloEPgXeVSKROad55DzgngemgVfzhXOQ25hrOu7zta06gyF/wgWcaNKmOhu1qYIpBBnM1SBwRIlE0lZBywyG/In1wBmgc0zayri0tQiJs1UncCbu863PC9DI3AjQYo6tAWQLMPL3mw2u3IDLwtwHtOYOjhmAOT/3EvC98boK3DLGPUAt8LTxWudE6K5Ptrrrk31GRv/Vsnsw5E+0AadFEzVpUzRqU/PAx8DAA9+8NCvyx32+avSHohuotIHDXZ80374gBWIjKwBD/kQV+tO60eHLftekTR1uPtcwSwEyQD8HnhHAAdwFnpQCsduw/B78IA+448C+QuEAlEhkFthnzGEHB7ABeH9ZBkP+xKPAL0CZ4BrHekYr3ikUzCabH7rrk29bwJlKAY9Lgdh1M4NdDnBngVAx4ABivx4OueuTZwWWMoMJKeRPyMBvgGpjngfqekYrbhYLEEALP1UDXMHmwQFmgMfMXYkqmKu32HAAUiB2E70S2EkFvDKwR2BaAgaKDZelASAtiO+Wge0Cww89oxV/lIpOCsRmgR8Flh0yesW301ip4LI0LojVuoBqgWG65Hj7y64IotUyoAgMd0sOCKJNq+I6ppbnPdP/IRmIC+Ib1oChShCLy4BoXRU9QMVSnSA2K6Pv4+zkWwPARkHsqgxEBYZdcru2uVRkWnz7ZmCXwHJJRm+q7eTCWLRLpC6sd/WmLsjAz+gLs52Oyu2aqFauSj/91VwNHBVYZoBJOTMsZYCwwFgJDMvtWkGnECL13gpIE6lNQxPpjZUCW1hSohlzP9iP3vHbqQXoKWICe4BnJ1KbmEhbbuIXDab/ehK5XetHb25EOq56U29df8W9tMrMrQM+At7IHm8ou02D60720KCkRLtyAavQS45t4VS9KVRv6lvgxYvbygvqS3pvBaqBz4Bmq3gW5BxQKynRuWWABuQB9KbdDs58Ow/0Ap84gRpgXeht54MirwF5SFKiX5pjK258uV0bBDoEcNkyG/dx9LPAP43xh4AngKa2mo68GndDw0FPeNm1rWpQN/oWrNUBDuPCPoqz4nwFHFmRsNyBzLC0CBwEzjnAFVNfAweDnvCKSmJ5upUZlu6p3tR+1ZsaWgO4IeC5oCd8zyroWHx3Xl44AJzA+dTBUm01HXahO0B30BMeEX3e8YT14rby0+hbokHExTxfLRpz1TnBQYGH6DsvL6jA60AA/VTfUVkZvIG+pPYFPeHiHqJbgJoN/x70tnULeqNdYVgS6Iv9tbaajihwHpgMesIF/w3xDz1gT8CribUHAAAAAElFTkSuQmCC);
                background-position: center;
                background-size: 20px;
                background-repeat: no-repeat;
              }
              &:hover {
                border: 1px solid #FF4555;
                & > i {
                  color: $btnHover;
                }
              }

              & > i {
                position: absolute;
                top: 50%;
                margin-top: -18.5px;
                color: $fgColor2;
                right: 6px;
                font-size: 16px;
              }
              &.colorsArea {

                i {
                  position: unset;
                  margin-top: 0;
                  margin-right: 6px;
                  float: right;
                }
              }
            }

            // font-size 设置
            .fontSizeSelect {
                position: relative;
                width: 63px;
                border-radius: 1px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                .icon{
                    color: rgba(255, 255, 255, 1);
                }
                .fontSizeSelectInput {
                    width: 57px;
                    background: none;
                    border: 0;
                    padding: 8px 0 8px 14px;
                    // width: 178px;
                    outline: 0;
                    box-shadow: none;
                }

                .fontsizeList {
                    width: inherit;
                    max-height: 300px;
                    background: rgba(44, 42, 48, 1);

                    position: absolute;
                    top: 40px;
                    left: 0;
                    z-index: 20;

                    // box-shadow: 0 2px 6px 2px rgba(228, 233, 238, 1);
                    border-radius: 2px;
                    overflow-x: hidden;
                    overflow-y: auto;
                    & > li {
                    width: 49px;
                    height: 32px;
                    line-height: 32px;
                    padding: 0 14px;
                    color: #FFFFFF;
                    font-size: 14px;
                    cursor: pointer;
                    &:hover {
                        background: rgba(86, 84, 89, 1);
                    }
                    }

                }


                .fontsizeList::-webkit-scrollbar {
                    background: none;
                    height: 6px;
                    width: 6px;
                }

                .fontsizeList::-webkit-scrollbar-track {
                    border-radius: 3px;
                }

                // .fontsizeList::-webkit-scrollbar-thumb {
                //     background: #d8d8d8 !important;
                //     border-radius: 3px;
                //     transition: background .2s ease;
                // }

                .fontSizeBtnArea{
                    width: 30px;
                    height: 100%;
                    position: absolute;
                    right: 0;
                    top: 0;
                    border-left: 1px solid #e6e6e6;
                }

                &:hover{
                    .icon{
                        color:rgba(255, 69, 85, 1);
                    }
                }
            }






            // 字体选择
            .fontSelect {
              position: relative;

              .fontNewTip {
                position: absolute;
                left: 0;
                bottom: calc(100% + 14px);
                width: 100%;
                height: 30px;
                line-height: 30px;
                font-size: 12px;
                border-radius: 5px;
                background: #E2EEFF;
                color: #165DBE;
                font-family: SourceHanSansCN-Regular;
                z-index: 1;
                pointer-events: none;

                span {
                  margin: 0 20px;
                  font-family: SourceHanSansCN-Heavy;
                  font-weight: 800;
                }

                &:after {
                  position: absolute;
                  content: '';
                  width: 18px;
                  height: 18px;
                  background: #E2EEFF;
                  transform: rotate(45deg);
                  border-radius: 0 0 2px 0;
                  bottom: -8px;
                  right: 24%;
                  z-index: -1;
                }
              }

              .fontSelectButton {
                display: inline-block;
                position: absolute;
                background-repeat: no-repeat;
                background-position: 10px center;
                background-size: auto 20px;
                width: 170px;
                height: 30px;
                left: 3px;
                top: 50%;
                margin-top: -15px;
              }

              .fontSelectList {
                display: inline-block;
                width: 158px;
              }
              .newFontSelectList {
                width: inherit;
                background: #fff;
                position: absolute;
                top: 40px;
                left: 0;
                box-shadow: 0 2px 6px 2px rgba(228, 233, 238, 1);
                overflow: hidden;
                z-index: 50;
                border-radius: 2px;
                .cateTabBtnArea {
                  display: table;
                  width: inherit;
                  .cateTabBtn {
                    width: 50%;
                    height: 38px;
                    line-height: 38px;
                    text-align: center;
                    background: #F2F2F2;
                    float: left;
                    font-size: 14px;
                    color: #666;

                    &:hover {
                      color: $primaryColorV6_2;
                    }
                    &.active {
                      background: #fff;
                    }
                  }

                }
                .cateTabBtnYyan{
                  // display: table;
                  display: flex;
                  width:162px;
                  height:28px;
                  background:rgba(255,255,255,1);
                  border-radius:4px;
                  border:1px solid rgba(255,69,85,1);
                  margin: 0 auto;
                  margin-bottom: 10px;
                  .cateTabyyan {
                    // width: 50%;
                    flex: 1;
                    height: 26px;
                    line-height: 26px;
                    text-align: center;
                    background: #fff;
                    float: left;
                    font-size: 14px;
                    color: #666666;
                    border-radius:4px;
                    &:hover {
                      color: $primaryColorV6_2;
                    }
                    &.active {
                      background: #FF575E;
                      color: #fff;
                      border-radius:0px;

                    }
                  }
                }
                .cateFontListArea {

                  .cateFontList {

                    .cateTitle {
                      padding: 12px 0;
                      font-size: 14px;
                      color: $primaryColorV6_2;
                      text-align: left;
                      display: inline-block;
                      width: 100%;
                      height:30px;
                      line-height: 8px;
                      cursor: default;
                      background:rgba(241,243,247,1);
                      color: #333333;
                      padding-left: 13px;
                    }
                    .cateFontItem,.canvasCateFontItem {
                      width: inherit;
                      height: 38px;
                      position: relative;
                      .cateFontImg {
                        background-repeat: no-repeat;
                        background-position: 12px center;
                        // background-size: auto 20px;
                        background-size: 80%;
                        width: 100%;
                        height: 100%;
                      }
                      .floatTipArea {
                        display: none;
                      }
                      .newCorner {
                        position: absolute;
                        right: 0;
                        top: 9px;
                        height: 20px;
                        line-height: 20px;
                        border-radius: 20px;
                        background: #FE575E;
                        font-size: 12px;
                        font-weight: 800;
                        color: #ffffff;
                        font-family: SourceHanSansCN-Heavy;
                        padding: 0 4px;
                      }
                      &.needTips {

                        .floatTipArea {
                          position: absolute;
                          top: -44px;
                          left: 5px;
                          font-size: 12px;
                          color: #666;
                          border: 1px solid #efefef;
                          border-radius: 2px;
                          box-shadow: 1px 2px 4px rgba(153, 153, 153, 0.36);
                          width: 130px;
                          padding: 5px;
                          line-height: 1.2;
                          background: #fff;
                        }
                        &:hover {

                          .floatTipArea {
                            display: inline-block;
                          }
                        }
                      }
                      &:hover {
                        background: #f2f2f2;
                      }
                    }

                    .canvasCateFontItem:hover{
                      background: rgba(86, 84, 89, 1)
                    }
                    .brandFont{
                      padding: 0 10px;
                      font-size: 12px;
                      .selectTeam{
                        // float: right;
                        width: 188px;
                        height: 34px;
                        line-height: 34px;
                        box-sizing: border-box;
                        border:1px solid rgba(214,219,225,1);
                        position: relative;
                        padding: 0 16px 0 20px;
                        border-radius:4px;
                        margin: 10px 0;
                        display: flex;
                        .showTeamName{
                          width: 140px;
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                        }
                        i{
                            float: right;
                            color: #202020;
                        }
                        .selections{
                            position: absolute;
                            top: 40px;
                            width: 100%;
                            box-shadow:0px 2px 4px 0px rgba(0,0,0,0.08);
                            border-radius:2px;
                            border:1px solid rgba(214,219,225,1);
                            left: 0;
                            z-index: 99;
                            .option{
                                // padding-left: 20px;
                                // height: 30px;
                                line-height: 30px;
                                background: #fff;
                                cursor: pointer;
                                // border-bottom: 1px solid #D6DBE1;
                                &:last-child{
                                    // border-bottom: none;
                                }
                                p{
                                  padding-left: 15px;
                                  color: #666;
                                  padding-right: 5px;
                                  box-sizing: border-box;
                                  max-width: 185px;
                                  overflow: hidden;
                                  text-overflow: ellipsis;
                                  white-space: nowrap;
                                }
                                .brandItem{
                                  padding-left: 25px;
                                  color: #202020;
                                  max-width: 185px;
                                  overflow: hidden;
                                  text-overflow: ellipsis;
                                  white-space: nowrap;
                                  padding-right: 5px;
                                  box-sizing: border-box;
                                  &:hover{
                                    background: #D6DBE1 ;
                                  }
                                }

                            }
                        }

                      }
                      .noBrandFontList{
                        margin-top: 80px ;
                        text-align: center;
                      }
                    }
                  }
                  .newCopyright_tip {
                    font-size: 10px;
                    padding: 10px 0;
                    box-sizing: border-box;
                    line-height: 12px;
                    display: table;

                    .icon-tishi {
                      display: inline-block;
                      color: #FF6634;
                      float: left;
                      margin-left: 10px;
                      margin-top: 10px;
                    }
                    p {
                      width: 145px;
                      float: left;
                      margin-left: 8px;
                      font-size: 12px;
                      color: #666;
                      line-height: 1.4;
                    }
                    &.show {
                      display: block;
                    }
                  }
                }
              }

            }
            .typeBtnArea {
              position: absolute;
              background: #fff;
              left: 56px;
              top: 50%;
              margin-top: -14px;
              box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);

              .typeBtn {
                width: 63px;
                height: 28px;
                border: 1px solid $lineColor;
                float: left;
                border-left-width: 0px;
                line-height: 28px;
                text-align: center;
                cursor: pointer;
                position: relative;
                background: #fafafa;

                .item {
                  margin-top: 0;
                }
                &:first-child {
                  border-left-width: 1px;
                }

                .item {
                  color: #999;
                }

                &:hover, &.active {
                  .item {
                    color: $btnHover;
                  }
                }

                .buttonPopups {
                  cursor: default;
                  position: absolute;
                  color: #333;
                  top: 40px;
                  left: 0px;
                  z-index: 100;
                  width: 120px;
                  height: 50px;
                  background: #fff;
                  box-shadow: 0px 0px 10px rgba(200, 200, 200, 0.6);
                  padding: 5px 10px;
                  border-radius: 5px;
                  font-size: 12px;
                  .description {
                    float: left;
                  }

                  .slideValue {
                    float: right;
                    font-size: 12px;
                  }

                  .slideBar {
                    left: 10px;
                    top: 40px;
                    width: 120px;
                    .slideBarButton {
                      cursor: pointer;
                    }
                  }
                }

              }
            }
        }

        .canvasSelect{
            height:28px;
            width:158px;
            border: 1px solid rgba(255, 255, 255, 0.2);

            .icon{
              color:rgba(255, 255, 255, 1);

            }

            &:hover{
                .icon{
                    color:rgba(255, 69, 85, 1);
                }
                border: 1px solid #FF4555;
            }

        }
    }


    #assetClickFun{
        z-index: 10000;
        .content{

            .sharpAngle{
                position: absolute;
                left: 43px;
                width: 0;
                height: 0;
                border-top: 5px solid transparent;
                border-bottom: 5px solid transparent;
                border-right: 5px solid rgba(0, 0, 0, 0.7);
                display: none;
            }
            .upTip{
                position: absolute;
                left: 10px;
                width: 76px;
                height: 32px;
                background-color:rgba(0, 0, 0, 0.7) ;

                left: 48px;
                border-radius: 4px;
                display: none;
                justify-content: center;
                align-items: center;
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #FFFFFF;

            }
           .layer{
                position: relative;
                width: 60px;
                height: 65px;
                // height: 131px;
                border-radius: 4px;
                background: rgba(44, 42, 48, 0.85);
                // background: rgba(255, 255, 255, 0.8);
                // box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.50);
                display: flex;
                flex-direction: column;
                align-items: center;

                .up,.down{
                    width: 60px;
                    height: 36px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color:rgba(255, 255, 255, 1) ;
                    border-radius: 50% 50% 0 0;
                    position: relative;
                    cursor: pointer;

                    .text{
                        font-size: 12px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #FFFFFF;
                        // color: #333333;
                        line-height: 12px;
                    }

                    .text:hover{
                        color: rgba(255, 69, 85, 1);
                    }

                    &:hover{
                        color: rgba(255, 69, 85, 1);
                        .sharpAngle{
                            display: block;

                        }

                        .upTip{
                            display: flex;
                        }
                        // background-color:rgba(126,126,132,.8);
                    }
                }

                .up{
                    border-radius: 50% 50% 0 0;
                }

                .down{
                    border-radius: 0 0 50% 50%;
                }

                .border{
                    width: 40px;
                    height: 1px;
                    // background: rgba(255, 255, 255, 0.2);
                    // background: #EBEBEB;

                }
            }
           .copy{
                margin-top: 4px;
                width: 36px;
                height: 36px;
                background: rgba(44, 42, 48, 0.85);
                border-radius: 18px;
                display: flex;
                justify-content: center;
                align-items: center;
                color:rgba(255, 255, 255, 1) ;
                cursor: pointer;
                &:hover{
                    color: rgba(255, 69, 85, 1);
                    .sharpAngle{
                        display: block;

                    }

                    .upTip{
                        display: flex;
                        width: 45px;
                    }
                }
           }

        }
    }




    /*元素鼠标悬浮提示信息 END*/
    .new_version_loading_tips{
        position:absolute;
        z-index: 99;
        left: 50%;
        top: 50%;
        margin-left: -90px;
        transform: translate(-50%,-50%);
        width:212px;
        height:106px;
        background:rgba(249,249,249,0.6);
        border-radius:8px;
        text-align: center;
        img{
            margin-top: 30px;
            width: 120px;
        }
        .tips{
            font-size:15px;
            font-family:PingFangSC-Regular,PingFang SC;
            font-weight:600;
            color: rgb(74,74,74);
            margin-top: 20px;
            text-align: center;
          }
    }

    /*元素名称太长鼠标悬浮提示信息 */
    .longTitleHover{
        position: relative;
        cursor: pointer;
        .spanTips{
            position: absolute;
            padding: 4px 10px;
            background:rgba(0,0,0,0.7);
            border-radius:4px;
            color: #fff;
            font-size: 12px;
            white-space: nowrap;
            line-height: 24px;
            top: 38px;
            left: -6px;
            visibility: hidden;
            opacity: 0;
            transition: all .3s;
            z-index: 999;
            &.spanTipsSide{
                left: 24px;
            }
            &::after{
              content: '';
              position: absolute;
              border-bottom: 5px solid rgba(0,0,0,0.7);
              border-left: 5px solid transparent;
              border-right: 5px solid transparent;
              border-top: none;
              top: -5px;
              left: 50%;
              transform: translateX(-50%);
            }
        }
        &:hover {
            span{
              visibility: visible;
              opacity: 1;
            }
        }
    }

}

/*上传信息完善窗口*/
$uploadPopupItemContentWidth: 400;
.uploadPopup{
    margin: 0 70px;
    display: inline-block;
}

.uploadPopup form,.PubFrom >{
    .category-navigator-container{
        margin-top: 20px;
        position: relative;

            .arrow-right {
                width:0;
                height:0;
                border-top:6px solid transparent;
                border-bottom: 6px solid transparent;
                border-left: 6px solid #666;
                display: inline-block;
                margin: 0 12px;
                margin-top: 10px;
            }


        .category-level-name{
            display: inline-block;
            background-color: #ccc;
            padding: 4px 8px;
            border-radius: 8px;
            color: #333;
            cursor: pointer;
            line-height: 22px;
            height: 22px;

            &:hover {
                background-color: #333;
                color: #fff;
                border-radius: 8px;
            }
        }

        .category-level-name + .category-level-name{
            margin-right: 10px;
        }
    }
    .uploadPopupTitle {
        text-align: center;
        font-size: 24px;
        font-weight: bold;
    }
    .item {
        width: 480px;
        display: inline-block;
        margin-top: 20px;
        &:first-child{
            margin-top: 30px;
        }

        .itemcontent-layout{
            margin-bottom: 10px;

        }

        .category-name{
            display: inline-block;
            font-size: 12px;
            min-width: 88px;
            max-height: 22px;
            line-height: 22px;
            margin-right: 10px;
            vertical-align: top;
            &.empty-holder{
                margin-right: 23px;
            }

            &.empty-holder:last-child {
                margin-bottom: 8px;
            }

        }
        .itemName{
            float: left;
            width: 110px;
        }
        .itemContent{
            float: left;
            width: calc(100% - 110px);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: flex-start;


            .tagItem{
                margin-right: 10px;
                display: inline-block;
                font-size: 12px;
                margin-bottom: 10px;
            }
            .desSub{
                position: absolute;
                bottom: -81px;
                left: -109px;
                font-size: 12px;
                color: $fontColorLight;
            }
            input[type='checkbox']{
                vertical-align: text-bottom;
                margin-right: 1px;
            }

            input[type='text']{
                width: $uploadPopupItemContentWidth - 2 + px;
                height:23px;
                border-radius: 2px;
                border: 1px solid #ccc;
            }
        }
    }
    .item.buttonArea{
        margin-left: auto;
        margin-right: auto;
        display: block;
        text-align: center;
        .buttonType4{
            margin-left: 0;
        }
    }

    .submitTip{
        text-align: center;
        font-size: 14px;
        color: #ff6161;
    }
    .notRequired{
        position: absolute;
        top: 26px;
        left: -106px;
        font-size: 14px;
        color: #797676;
    }
}
// 挽留弹窗样式
.detainBox {
    cursor: pointer;
    // background: url('https://js.tuguaishou.com/editor/image/huodong_wanliu_bg.png') no-repeat;
    // background-size: cover;
    height: 517px;
    padding: 1px 0;
    .close {
        position: absolute;
        top: 0;
        right: 0;
        width: 21px;
        height: 21px;
        border: 1.5px solid #ffffff;
        border-radius: 50%;
        cursor: pointer;
        &::before {
            content: " ";
            display: block;
            width: 12px;
            height: 1.5px;
            background: #ffffff;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(45deg);
        }
        &::after {
            content: " ";
            display: block;
            width: 12px;
            height: 1.5px;
            background: #ffffff;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
        }
    }

    @keyframes scaleSize {
        0%{
            transform: scale(1);
        }
        50%{
            transform: scale(1.1);

        }
        100%{
            transform: scale(1);
        }
    }

    .btn{
        cursor: pointer;
        margin: 380px auto 0;
        width: 240px;
        img{
            width: 100%;
        }
        animation: scaleSize 1s linear infinite;
    }
}

/*下载弹窗START*/
.downloadPopup{
    color: #666;
    a{
        text-decoration: none;
    }

    .abTestCloseBtn{
        position: relative;
        left: 30px;
        cursor: pointer;
        top: -35px;
        i{
          right: 23px;
          top: 18px;
          display: inline-block;
          position: absolute;
          font-size: 8px;
          color: black;
        }

        .downloadSum{
            top: 24px;
            right: 20px;
            font-size: 16px;
        }
        .downloadSumPosition {
            top: 24px;
            right: 20px;
            font-size: 16px;
        }


    }
    .progressBar{
        position: absolute;
        top: 82px;
        left: 50%;
        margin-left: -144px;
        width: 288px;
        height: 22px;
        border-radius: 14px;
        background: #F0EFF2;
        overflow: hidden;

        .progressBarFiller{
            background: $primaryColorV6_2;
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            &.progressBarFiller_generate{
                right:100%;
            }
        }
    }
    .shareProgress{
        position: absolute;
        top: 125px;
        left: calc(100% / 2 - 470px / 2);
        width: 470px;
        height: 10px;
        border-radius: 14px;
        background: #F0EFF2;
        overflow: hidden;
        .shareProgressFiller{
            background: $primaryColorV6_2;
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
        }
    }
    .shareProgressText{
        width: 100%;
        text-align: center;
        font-size: 20px;
        font-weight: 600;
        color: black;
        line-height: 80px;
    }
    .shareProgressDetail{
        width: 100%;
        text-align: center;
        font-size: 12px;
        color: #666666;
        line-height: 16px;
    }
    .shareProgressCancel{
        font-size: 18px;
        width: 100%;
        text-align: center;
        color: rgb(16, 172, 245);
        margin-top: 80px;
        font-weight: 540;
        cursor: pointer;
    }

    .styleText{
        text-align: center;
        line-height: 90px
    }
    .downloadSuccess{
        text-align: center;
        .successText{
            font-weight:bold;
            color: #202020;
            font-size: 24px;
            text-align: center;
            line-height: 30px;

            i {
                font-size: 30px;
                color: #16A91D;
                margin-right: 10px;
            }
        }
        .successSubText {
            font-weight: bold;
            font-size: 18px;
            color: #202020;
            text-align: center;
            margin: 20px 0;
        }
        .successRemind{
            position: absolute;
            left: 31px;
            bottom: 25px;
            font-size:12px;
            font-family:MicrosoftYaHei;
            font-weight:400;
            color:#727272;
            overflow: hidden;
            img,a{
                position: absolute;
                width: 85px;
                height: 100%;
                opacity: 0;
                cursor: pointer;
            }
            a{
                width: 85px;
                height: 100%;
                display: inline-block;
                z-index: 9999;
            }
            .leftCode{
                margin-left: -40px;
            }
            span{
                color: #FF575E;
                width: 85px;
                height: 100%;
                position: relative;
                font-weight: bold;
            }
            em{
                font-style: normal;
            }
        }
        .successQR{
            margin-top: 35px;
            display: flex;
            justify-content: center;
            .successQRImg,.successQRApplet,.QQsuccessQRImg,.QQsuccessQRApplet{
                width:330px;
                height:160px;
                border:1px solid #d5d5d5;
                border-radius: 5px;
                dl{
                    text-align: center;
                    dt{
                        width: 100px;
                        height: 100px;
                        margin: auto;
                        margin-top: 22px;
                        margin-bottom: 27px
                    }
                    dd{
                        font-size:14px;
                        font-family:MicrosoftYaHei;
                        font-weight:400;
                        color:rgba(102,102,102,1);
                    }
                }
            }
            .successQRImg, .successQRApplet{
                margin: 0 20px;
                .link {
                    display: block;
                    height: 100%;
                    padding: 21px;
                    -webkit-box-sizing: border-box;
                    -moz-box-sizing: border-box;
                    box-sizing: border-box;
                }
                dl{
                    display: flex;
                    dt{
                        width: 69px;
                        height: 69px;
                        line-height: 69px;
                        border-radius: 50%;
                        background: #FFE5E6;
                        text-align: center;
                        font-size: 35px;
                        color: #FF575E;
                        margin: 0 21px 0 0;
                    }
                    dd{
                        flex: 1;
                        span{
                            color: #FE7D78
                        }
                        .name {
                            color: #202020;
                            font-weight: bold;
                            font-size: 18px;
                            text-align: left;
                            line-height: 42px;
                        }
                        .describe {
                            color: #8B8B8B;
                            font-size: 14px;
                            text-align: left;
                            line-height: 15px;
                        }
                        a{
                            width: 160px;
                            height: 40px;
                            background: rgba(255,87,94,1);
                            border-radius: 5px;
                            display: block;
                            text-align: center;
                            line-height: 40px;
                            font-weight: 400;
                            color: rgba(255,254,254,1);
                            margin-top: 18px;
                        }
                    }
                }
            }
            .QQsuccessQRImg{
                dl{
                    dt{
                        background: url($imgHost +'/index_img/editorV7.0/user9QRcode.png') no-repeat center;
                        background-size: 100px;
                    }
                    .uesrSelect{
                        user-select:text;
                    }
                }
            }
            //.successQRApplet{
            //    margin-left: 47px;
            //    dl{
            //        div{
            //            font-size: 12px;
            //            padding: 3px 0;
            //        }
            //        dt{
            //            background: url($imgHost +'/index_img/editorV7.0/android-download-client.png') no-repeat center;
            //            margin-top: 0;
            //        }
            //        dd{
            //            a{
            //                width: 150px;
            //                height: 36px;
            //                background: rgba(255,87,94,1);
            //                border-radius: 4px;
            //                display: inline-block;
            //                text-align: center;
            //                line-height: 36px;
            //                font-weight: 400;
            //                color: rgba(255,254,254,1);
            //                span{
            //                    display: inline-block;
            //                    width: 16px;
            //                    height: 16px;
            //                    background: url($imgHost +'/index_img/editorV7.0/download-client-androidIcon.png') no-repeat center;
            //                    background-size: contain;
            //                    position: relative;
            //                    top: 3px;
            //                    right: 3px;
            //                }
            //            }
            //        }
            //
            //    }
            //}
            .QQsuccessQRApplet{
                margin-left: 47px;
                dl{
                    dt{
                        background: url($imgHost +'/index_img/editorV7.0/fuwu.jpg') no-repeat center;
                        background-size: contain
                    }
                }
            }
        }










        .myWorkBtn{
            display: inline-block;
            font-size: 14px;
            font-weight:400;
            text-decoration:underline;
            color:#FF575E;
            cursor: pointer;
            margin: 35px auto 0px
        }
        // .continueEditingBtn{
        //     width: 60px;
        //     height: 20px;
        //     line-height: 20px;
        //     color: #999;
        //     text-align: center;
        //     position: absolute;
        //     bottom: 25px;
        //     left: 50%;
        //     margin-left: -30px;
        //     font-size: 14px;
        //     text-decoration: underline;
        //     cursor: pointer;
        // }
        // .downloadHelpTipsBtn{
        //     height: 20px;
        //     line-height: 20px;
        //     color: #45BBFF;
        //     text-align: center;
        //     position: absolute;
        //     bottom: 10px;
        //     right: 10px;
        //     margin-left: -30px;
        //     font-size: 14px;
        //     text-decoration: underline;
        //     cursor: pointer;
        // }
        .loginLoser{
            font-size:14px;
            font-weight:400;
            color:#727272;
            text-align: center;
            margin-top: 15px;
            a{
                color: #FF575E;
                cursor: pointer;
                text-decoration:underline;
            }
        }
        .successQuestion{
            text-align: right;
            position: absolute;
            right: 32px;
            bottom: 25px;
            font-size:12px;
            font-family:MicrosoftYaHei;
            font-weight:400;
            color:#727272;
            cursor: pointer;
            span{
                color: #FF575E;
                font-weight: bold;
            }
        }
    }

    .downloadSuccess2{
        .successIcon{
            font-size: 60px;
            color: #33cc66;
            position: absolute;
            left: 50%;
            top: 30px;
            margin-left: -30px;
        }
        .successText{
            font-size: 18px;
            position: absolute;
            top: 105px;
            left: 0;
            width: 100%;
            text-align: center;
        }
        .vipTipText{
            font-size: 16px;
            position: absolute;
            bottom: 58px;
            left: 0;
            width: 100%;
            text-align: center;
            p{
                color: #f25b4a;
                display: inline;
            }
            i{
                font-size: 26px;
                color: #000;
            }
        }
        .joinVipBtn{
            border: 1px solid #f25b4a;
            border-radius: 4px;
            background: #fff;
            color: #f25b4a;
            position: absolute;
            bottom: 15px;
            left: 50%;
            margin-left: -40px;
            width: 80px;
            height: 30px;
            line-height: 30px;
            font-size: 14px;
            text-align: center;
            cursor: pointer;

            &:hover{
                color: #fff;
                background: #f25b4a;
            }
        }
    }

    .downloadSuccess3v{

        text-align: center;
        .jump_ai_img{
            position: absolute;
            width: 100%;
            height: auto;
            top:0;
            left: 0;
            transform: translateY(-100%);
            cursor: pointer;
        }
        .successText{
            color: #0FC932;
            font-size: 24px;
            text-align: center;
            line-height: 30px;
            font-weight: 500;

            span {
                background-color: #0FC932;
                width: 24px;
                height: 24px;
                border-radius: 50%;
                margin-right: 10px;
                display: inline-block;
                line-height: 20px;
                text-align: center;
                i {
                    font-size: 16px;
                    color: #fff;
                }
            }
        }
        .newUserBenefits {
            width: 483px;
            height: 111px;
            margin: auto;
            text-align: center;
            background-image: url($imgHost + '/index_img/editorV7.0/downloadBorder.png');
            background-size: contain;
            margin-top: 20px;

            .firstText {
                height: 20px;
                font-size: 12px;
                font-weight: 500;
                line-height: 22px;
                text-align: center;
                color: #fff;
            }
            .subText {
                margin-top: 20px;
                font-size: 16px;
                font-weight: 500;
                line-height: 24px;
                letter-spacing: 0.1em;
                text-align: center;
                background: linear-gradient(100.82deg, #EF3964 4.91%, #EF3964 52.33%, #E47A19 103.87%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
            .lastText {
                margin-top: 18px;
                font-size: 12px;
                font-weight: 500;
                line-height: 20px;
                letter-spacing: 0.1em;
                text-align: center;
                color: rgba(31, 26, 27, 1);
            }
        }
        .newUserBenefitsNext{
            width: 483px;
            height: 96px;
            margin: auto;
            text-align: center;
            background-image: url($imgHost + '/index_img/editorV7.0/notDownBorder.png');
            background-size: contain;
            margin-top: 20px;
            padding-top: 15px;
            box-sizing: border-box;

            .firstText {
                font-size: 18px;
                font-weight: 500;
                line-height: 24px;
                letter-spacing: 0.1em;
                text-align: center;
                color: rgba(239, 57, 100, 1);
            }

            .lastText {
                margin-top: 24px;
                font-size: 12px;
                font-weight: 500;
                line-height: 24px;
                letter-spacing: 0.1em;
                text-align: center;
                a {
                    color: #EF3964;
                    border-bottom: 1px solid;
                }
                span {
                    background:linear-gradient(100.82deg, #EF3964 4.91%, #EF3964 52.33%, #E47A19 103.87%),
                    linear-gradient(0deg, #1F1A1B, #1F1A1B);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                }
            }
        }
        .successSubText {
            font-weight: bold;
            font-size: 18px;
            color: #202020;
            text-align: center;
            margin: 20px 0;
        }
        .unDownloaded{
            position: absolute;
            bottom: 0px;
            left: 0;
            width: 100%;
            background-color: rgba(235,235,235,1);
            height: 60px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;

            &.not-commercial-vip {
                border-bottom-left-radius: 0;
                border-bottom-right-radius: 0;
            }

            .successRemind{
                display: inline-block;
                font-size:12px;
                font-family:MicrosoftYaHei;
                font-weight:400;
                color:#727272;
                overflow: hidden;
                position: relative;
                left: 25px;
                img,a{
                    position: absolute;
                    width: 85px;
                    height: 100%;
                    opacity: 0;
                    cursor: pointer;
                }
                a{
                    width: 85px;
                    height: 100%;
                    display: inline-block;
                    z-index: 9999;
                }
                .leftCode{
                    margin-left: -40px;
                }
                span{
                    color: #FF575E;
                    width: 85px;
                    height: 100%;
                    position: relative;
                    font-weight: bold;
                }
                em{
                    font-style: normal;
                }
            }
            .successQuestion{
                display: inline-block;
                font-size:12px;
                font-family:MicrosoftYaHei;
                font-weight:400;
                color:#727272;
                cursor: pointer;
                margin-top: 0px;
                position: relative;
                right: 25px;
                // left: 322px;
                span{
                    color: #FF575E;
                    font-weight: bold;
                }
            }

            .singleDownloadSucess{
                // left: 320px;
            }
        }

        .to-recommander {
            position: absolute;
            bottom: -60px;
            left: 0;
            width: 100%;
            background-color: #FF4555;
            color: #fff;
            font-size: 18px;
            line-height: 60px;
            text-align: center;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            cursor: pointer;

            span {
                vertical-align: middle;
                font-weight: bold;
            }

            .to-recommander-button {
                vertical-align: middle;
                margin-left: 20px;
                width: 100px;
                color: #FF4555;
                font-size: 14px;
                line-height: 30px;
                border-radius: 15px;
                background: #ffd6d7;
                outline: none;
                border: none;
                cursor: pointer;
            }
        }

        .successQR{
            margin-top: 35px;
            display: flex;
            justify-content: center;
            .successQRImg,.successQRApplet{
                width:330px;
                height:160px;
                // border:1px solid #d5d5d5;
                border-radius: 10px;
                background-color:rgba(241,243,247,1);
                dl{
                    text-align: center;
                    dt{
                        width: 100px;
                        height: 100px;
                        margin: auto;
                        margin-top: 22px;
                        margin-bottom: 27px
                    }
                    dd{
                        font-size:14px;
                        font-family:MicrosoftYaHei;
                        font-weight:400;
                        color:rgba(102,102,102,1);
                    }
                }
            }
            .successQRImg, .successQRApplet{
                margin: 0 20px;
                .link {
                    display: block;
                    height: 100%;
                    // padding: 21px;
                    -webkit-box-sizing: border-box;
                    -moz-box-sizing: border-box;
                    box-sizing: border-box;
                    .desc{
                        font-size:16px;
                        font-family:PingFangSC-Semibold,PingFang SC;
                        font-weight:600;
                        color:rgba(32,32,32,1);
                        line-height:22px;
                    }
                }
                dl{
                    display: flex;
                    dt{
                        width: 69px;
                        height: 69px;
                        line-height: 69px;
                        border-radius: 50%;
                        background: #FFE5E6;
                        text-align: center;
                        font-size: 35px;
                        color: #FF575E;
                        // margin: 0 21px 0 0;
                    }
                    dd{
                        flex: 1;
                        span{
                            color: #FE7D78
                        }
                        .name {
                            color: #202020;
                            font-weight: bold;
                            font-size: 18px;
                            text-align: left;
                            line-height: 42px;
                        }
                        .describe {
                            color: #8B8B8B;
                            font-size: 14px;
                            text-align: left;
                            line-height: 15px;
                        }
                        a{
                            width: 160px;
                            height: 40px;
                            background: rgba(255,87,94,1);
                            border-radius: 5px;
                            display: block;
                            text-align: center;
                            line-height: 40px;
                            font-weight: 400;
                            color: rgba(255,254,254,1);
                            margin-top: 18px;
                            cursor: pointer;
                        }
                    }
                }
            }

        }

        .successSingle{
            margin-top: 45px;
            display: flex;
            flex-direction:column;
            align-items: center;
            margin-left: 22px;
            margin-right: 22px;
            position: relative;
            height:auto;

            .singleDownloadSuccessDesc{
                height: 25px;
                font-size: 18px;
                font-weight: 400;
                color: #202020;
                line-height: 25px;
            }

            .downloadCount{
                font-size: 24px;
                font-family: PingFangSC-Semibold, PingFang SC;
                font-weight: 600;
                color: #FF4555;
                line-height: 33px;
                margin-bottom: 10px;
                p{
                    display: inline-block;
                    span{

                        background: #DFDFDF;
                        border-radius: 4px;
                        margin:4px;

                        font-family: PingFangSC-Semibold, PingFang SC;
                        font-weight: 600;
                        color: #202020;
                        line-height: 28px;
                        padding-left: 4px;
                        padding-right: 4px;

                    }
                }

                letter-spacing: 2px;

            }

            .hrefUpgrade{
                width:200px;
                height:48px;
                background:rgba(255,69,85,1);
                border-radius:4px;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-top: 45px;
                cursor: pointer;
                .upgradeMemberBtn{
                    font-size:14px;
                    font-weight:600;
                    color:rgba(255,255,255,1);
                    line-height:20px;
                    letter-spacing: 2px;
                    width: 100%;
                    height: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
            }



            .tipMessage{
                position: absolute;
                left: 360px;
                top: 61px;
                cursor: pointer;
                // border:1px dashed rgba(255,69,85,1);
                width: 125px;
                height: 23px;
                display: flex;
                justify-content: center;
                align-items: center;
                background: url("https://js.tuguaishou.com/image/editor/single_download_success.png");
                a{
                    font-size:12px;
                    font-family:PingFangSC-Regular,PingFang SC;
                    font-weight:400;
                    color:rgba(237,0,20,1);
                    line-height:14px;
                    width: 100%;
                    height: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;
                }
            }
        }

        .downloadSuccessUnVip{
            display: flex;
            justify-content: space-between;

            font-size: 14px;
            margin-left: 25px;
            margin-right: 25px;
            margin-top: 109px;
            .downloadLocateDescBox{
                display: flex;
                justify-items: flex-start;
                flex: 1;
            }
            .downloadLocateUnVip{
                .myWorkBtnUnVip{
                    text-decoration:underline;
                    cursor: pointer;
                    display: inline-block;
                    font-size:14px;
                    font-family:PingFangSC-Regular,PingFang SC;
                    font-weight:400;
                    color:rgba(102,102,102,1);
                }
            }

            .checkMyWork{
                font-size: 14px;
                font-weight:400;
                text-decoration:underline;
                color:#FF575E;
                cursor: pointer;
                display: inline-block;
                font-family:PingFangSC-Regular,PingFang SC;
                margin-left: 235px;
            }


        }

        .downloadLocate{
            text-align: left;
            a{
                cursor: pointer;
            }
            p{
                display: inline-block;
                font-size:14px;
                font-family:PingFangSC-Regular,PingFang SC;
                font-weight:400;
                color:rgba(102,102,102,1);
                line-height:20px;
                margin-left: 20px;
            }
            .myWorkBtn{
                font-size: 14px;
                font-weight:400;
                text-decoration:underline;
                color:#FF575E;
                cursor: pointer;
                margin: 35px auto 0px;
                display: inline-block;
                font-size:14px;
                font-family:PingFangSC-Regular,PingFang SC;
                font-weight:400;
                color:rgba(102,102,102,1);
                line-height:20px;
            }

        }


        .singleDownloadSuccessdownloadLocate{
            margin-top: 40px;
            height: auto;
        }

        .myWorkSingleDownloadSucessBtn{
            display: inline-block;
            font-size: 14px;
            font-weight:400;
            text-decoration:underline;
            color:#FF575E;
            cursor: pointer;
            position: absolute;
            left: 564px;
            top: 302px
            // margin: 35px auto 0px
        }


        .loginLoser{
            font-size:14px;
            font-weight:400;
            color:#727272;
            text-align: center;
            margin-top: 15px;
            a{
                color: #FF575E;
                cursor: pointer;
                text-decoration:underline;
            }
        }
    }

    .downloadFaile{
        background-color: rgb(255, 255, 255);
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        border-radius: 12px;
        .successIcon{
            font-size: 60px;
            color: #f25b4a;
            position: absolute;
            left: 50%;
            top: 55px;
            margin-left: -30px;
        }
        .successText{
            font-size: 18px;
            position: absolute;
            bottom: 66px;
            left: 0;
            width: 100%;
            text-align: center;
        }
        .joinVipBtn{
            border: 1px solid #f25b4a;
            border-radius: 4px;
            background: #fff;
            color: #f25b4a;
            position: absolute;
            bottom: 15px;
            left: 50%;
            margin-left: -40px;
            width: 80px;
            height: 30px;
            line-height: 30px;
            font-size: 14px;
            text-align: center;
            cursor: pointer;

            &:hover{
                color: #fff;
                background: #f25b4a;
            }
        }

        .joinVipText{
            width: 180px;
            height: 30px;
            background: #FFE7D9;
            border-radius: 6px;
            position: relative;
            top: 260px;
            left: 120px;
            font-size: 12px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #F86538;
            line-height: 30px;
            padding-left: 16px;
            span{
                font-size: 18px;
            }
        }
        .joinVipText::after{
            border-left: 7px solid transparent;
            border-right: 7px solid transparent;
            border-top: 7px solid #FFE7D9;
            content: "";
            position: relative;
            width: 0;
            top: 27px;
            right: 87px;
        }

        .joinVipBtnContainer{
            top: 252px;
            left: 220px;
            transform: translateX(-50%);
            position: relative;
            width: 216px;
            height: 50px;
        }
        .joinVipBtnNew{
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            img{
                width: 168px;
                height: 40px;
                border-radius: 30px;
                font-weight: 600;
                cursor: pointer;
                box-shadow: 0px 2px 5px 2px rgba(249, 96, 50, 0.36);
                animation: changeButtonSize 0.8s infinite cubic-bezier(0.42,0,0.58,1);
            }

        }
        @keyframes changeButtonSize {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.25);
            }
            100% {
                transform: scale(1);
            }
        }

        .joinVipBtn2{
            width: 180px;
            height: 42px;
            line-height: 42px;
            text-align: center;
            // margin: 40px auto 0;
            background: linear-gradient(-90deg, #ff601e, #ff590c);
            border-radius: 20px;
            filter:saturate(120%);
            color: #fff;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            display: inline-block;
            position: absolute;
            left: 50%;
            bottom: 45px;
            transform: translateX(-50%);
            // margin-left: -90px;
            box-shadow: 0px 2px 5px 2px rgba(249, 96, 50, 0.36);
        }
        // .businessJoinVipBtn2 {
        //     width: 232px;
        //     height: 42px;
        //     background: linear-gradient(85deg, #79513A 0%, #531B00 100%);
        //     border-radius: 31px 31px 31px 31px;
        //     opacity: 1;
        //     margin-left: 0px;
        //     transform: translateX(-50%);
        //     box-shadow: none;
        //     &:hover {
        //         background: linear-gradient(85deg, #94674D 0%, #823510 100%);
        //     }
        // }
        .vip_huodong{

            position: absolute;
            bottom: 0;
            width: 100%;

            img {
                position: absolute;
                left: -22px;
                width: 481px;
                bottom: -11px;
            }
        }
        .activityBtn2{
            font-size: 12px;
            color: #096AFF;
            margin-top: 14px;
            position: absolute;
            bottom: 24px;
            left: 0;
            width: 100%;
            text-align: center;
        }
        .activityBtn3{
            font-size: 12px;
            color: rgba(249,96,50,1);
            margin-top: 14px;
            position: absolute;
            bottom: 24px;
            left: 0;
            width: 100%;
            text-align: center;
        }
        .qqTip{
            width: 100%;
            margin-top: 60px;
            text-align: center;
            font-size: 14px;
            color: #7a7a7a;
            //font-weight: bold;
            position: absolute;
            left: 0;
            bottom: 0;
            background: #faefeb;
            height: 38px;
            line-height: 38px;

            p, a{
                color: #f37061;
                display: inline-block;
                font-weight: bold;
            }
            a{
                text-decoration: underline;
            }
        }
        .textMS1{
            position: absolute;
            width: 100%;
            top: 40px;
            left: 0;
            text-align: center;
            font-size: 25px;
            color: #f37061;
        }
        .textMS2{
            position: absolute;
            width: 100%;
            top: 85px;
            left: 0;
            text-align: center;
            font-size: 16px;
            color: #666;
        }
        .textMS5{
            border-radius: 15px 15px 0 0;
            position: absolute;
            width: 100%;
            top: 0;
            left: 0;
            padding: 22px 0 0;
            text-align: center;
            font-size: 24px;
            color: #FF3B00;
            background:linear-gradient(180deg,rgba(255,230,215,1),rgba(255,255,255,1));
            white-space: pre;
        }
        .businessTextMS5 {
            // padding: 28px 0px;
            // border-radius: 14px;
            // background: none;
        }
        .businessTextMS6 {
            // color: #773912;
            // font-size: 25px;
            // font-family: Source Han Sans CN-Bold, Source Han Sans CN;
            // font-weight: bold;
        }
        .textMS6{
            width: 100%;
            text-align: center;
            font-size: 25px;
            color: #FF3C0A;
            font-weight: bold;
        }
        .textMS9 {
            font-size: 16px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #202020;
            line-height: 28px;
            margin-bottom: 17px;
        }
        .textMS3{
            width: 100%;
            text-align: center;
            font-size: 16px;
            color: #1F1E1E;
            line-height: 20px;
            margin-top: 10px;
            margin-bottom: 5px;
            span{
                width: 50px;
                height: 1px;
                background-color: #FFBDAA;
                display: inline-block;
                margin: 10px 20px 5px;
                color:rgba(32,32,32,1);
                font-family:Source Han Sans CN;
            }
            div{
                display: inline-block;
            }
        }
        .businessTextMS3 {
            // color: #783A15;
            // font-weight: 400;
            // margin-top: 15px;
            // span {
            //     display: inline-block;
            //     width: 43px;
            //     height: 0px;
            //     opacity: 1;
            //     border: 0.5px solid #935C35;
            // }
        }
        .textMS4{
            position: absolute;
            width: 100%;
            top: 94px;
            left: 0;
            color:rgba(135,135,135,1);
            font-size:16px;
            // text-align: center;
            div{
                margin: 12px 0 0 109px;
                span{
                    font-weight:bold;
                    color:rgba(249,96,50,1);
                    margin-right: 10px;
                }
            }
            &.company{
                top:129px
            }
            &.new{
                box-sizing: border-box;
                width: 100%;
                height: 154px;
                display: flex;
                padding: 0 20px;
                justify-content: space-between;
                align-items: center;
                top:116px;
             div{
                 box-sizing: border-box;
                 margin: 0;
                 display: flex;
                 flex-direction: column;
                 align-items: center;
                 justify-content: space-between;
                 padding: 19px 0 25px 0;
                 width: 94px;
                 height: 100%;
                 a{
                     display: block;
                     width: 56px;
                     height: 56px;
                     border-radius: 50%;
                     background: linear-gradient(180deg, #feb81a, #ff8a00);
                     box-shadow: 0px 3px 6px 0px rgba(255, 152, 9, 0.62);
                     filter:saturate(120%);
                     line-height: 56px;
                     text-align: center;
                     font-size: 16px;
                     font-family: PingFangSC-Semibold, PingFang SC;
                     font-weight: 600;
                     color: #FFFFFF;
                     &:hover{
                         background: linear-gradient(158deg, #FF901F 0%, #FF681F 100%);
                         box-shadow: 0px 3px 6px 0px rgba(255, 106, 9, 0.62);
                     }
                 }
                 span{
                     display: block;
                     width: 94px;
                     text-align: center;
                     font-size: 14px;
                     font-family: PingFangSC-Regular, PingFang SC;
                     font-weight: 400;
                     color: #252525;
                     line-height: 20px;
                     margin: 0;
                 }
             }
           }
            &.businessVip {
                top: 116px;
                // div {
                //     a {
                //         box-shadow: none;
                //         background: linear-gradient(45deg, #FBBB66 0%, #FFDFA4 100%);
                //         color: #71301A;;
                //         &:hover {
                //             background: linear-gradient(45deg, #FFC881 0%, #FFE8BE 100%);
                //             box-shadow: 0px 2px 6px 0px #D4B489;
                //         }
                //     }
                //     span {
                //         color: #743400;
                //         &:hover {
                //             color: #874F3C;
                //         }
                //     }
                // }
           }
        }
     }
     .isNotCompany{
        .joinVipBtn2{
            bottom: 23px;
        }
     }
    .remindShare{
        background: linear-gradient(180deg, #ffd6d9 0%, #FFFFFF 50%);
        box-shadow: 0px 1px 6px 1px rgba(0, 0, 0, 0.1);
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        border-radius: 10px;
        //width: 512px;
        display: flex;
        flex-direction: column;
        align-items: center;
        .textContainer{
            width: 100%;
            margin-top: 23px;
            display: flex;
            flex-direction: column;
            align-items: center;
            .boldText{
                font-size: 24px;
                font-weight: 600;
                color: #202020;
                line-height: 36px;
                margin-bottom: 5px;
            }
            .detailText{
                margin-top: 17px;
                div{
                    padding-top: 17px;
                    display: flex;
                    margin-left: 10px;
                    font-size: 17px;
                    font-weight: 400;
                    color: #202020;
                    line-height: 22px;
                    &>span{
                        font-weight: 600;
                        color: #FF4555;
                        line-height: 22px;
                        margin-right:10px ;
                    }
                }
            }
        }
        .buttonContainer{
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
            width: 326px;
            .rejectBTN{
                display: flex;
                align-items: center;
                justify-content: center;
                width: 150px;
                height: 40px;
                background: #DBDBDB;
                border-radius: 4px;
                span{
                    font-size: 14px;
                    font-weight: 600;
                    color: #202020;
                    line-height: 20px;
                }
                .cry{
                    width: 26px;
                    height: 26px;
                    background-size: 26px 26px;
                    background-image: url($imgHost + "/editor/image/cry.png") ;
                    overflow: hidden;
                    margin-right: 10px;
                }
            }
            .acceptBTN{
                display: flex;
                align-items: center;
                justify-content: center;
                width: 150px;
                height: 40px;
                background: #FF4555;
                box-shadow: 0px 2px 8px 0px rgba(255, 69, 85, 0.37);
                border-radius: 4px;
                span{
                    font-size: 14px;
                    font-weight: 600;
                    color: #FFFFFF;
                    line-height: 20px;
                }
                .smile{
                    width: 26px;
                    height: 26px;
                    background-size: 26px 26px;
                    background-image: url( $imgHost + "/editor/image/smile.png") ;
                    overflow: hidden;
                    margin-right: 10px;
                }
            }
        }
        .noMoreShowCheck{
            width: 475px;
            margin-top: 50px;
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: 400;
            color: #A6A6A6;
            line-height: 20px;
            span{
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 14px;
                height: 14px;
                border-radius: 1px;
                border: 1px solid #A6A6A6;
                margin-right: 3px;
            }
            .check::after {
                content:"";
                position: absolute;
                width: 9px;
                height: 5px;
                border: 1px solid #A6A6A6;
                //border-radius: 1px;
                border-top: none;
                border-right: none;
                background: transparent;
                transform: rotate(-50deg) translateX(2px) translateY(-1.5px);
            }

        }
    }
    .downloadFaile2 {
        .successIcon {
            font-size: 60px;
            color: #f25b4a;
            position: absolute;
            left: 50%;
            top: 30px;
            margin-left: -30px;
        }
        .successText {
            font-size: 18px;
            position: absolute;
            top: 106px;
            left: 0;
            width: 100%;
            text-align: center;
        }
        .redownloadBtn {
            background: linear-gradient(180deg, #fd7147 0%, #f65e30 100%);
            color: #fff;
            font-weight: bold;
            width: 180px;
            height: 42px;
            line-height: 42px;
            text-align: center;
            border-radius: 18px;
            cursor: pointer;
            position: absolute;
            bottom: 36px;
            left: 50%;
            margin-left: -90px;
        }
        .areaSubBtn{
            position: absolute;
            bottom: 36px;
            left: 50%;
            // margin-left: -90px;
            transform: translateX(-50%);

            .contactBtn{
                height: 30px;
                line-height: 29xp;
                box-sizing: border-box;
                border-bottom: 1px solid rgba(250, 105, 59, 1);
                color: rgba(250, 105, 59, 1);
                font-size: 16px;
                text-align: center;
                float: left;
                cursor: pointer;
            }
            .redownload2Btn{
                width: 100px;
                height: 30px;
                line-height: 30px;
                box-sizing: border-box;
                text-align: center;
                background-color: rgba(250, 105, 59, 1);
                color: #fff;
                border-radius: 5px;
                float: left;
                margin-left: 20px;
                font-size: 16px;
                cursor: pointer;
            }
        }

    }

    .downloadFaile3 {
        .successText {
            font-size: 18px;
            position: absolute;
            top: 72px;
            left: 0;
            width: 100%;
            text-align: center;
        }
        .bindPhoneBtn {
            border: 1px solid #f63;
            border-radius: 4px;
            font-size: 20px;
            color: #f63;
            font-weight: bold;
            width: 202px;
            height: 47px;
            line-height: 47px;
            text-align: center;
            cursor: pointer;
            position: absolute;
            bottom: 49px;
            left: 50%;
            margin-left: -101px;
        }
    }

    .downloadFaile4{
        background-color: rgb(255, 255, 255);
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.15);
        border-radius: 8px;

        // display: flex;
        // // justify-content: center;
        // flex-direction: column;
        // align-items: center;

        .surplus,.consumption{
            font-size: 16px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
            line-height: 22px;
            margin-left: 110px;
        }
        .surplus{
            margin-top: 69px;
            margin-bottom: 30px;
        }
        .consumption{
            margin-bottom: 48px;
        }

        span{
            font-size: 16px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #FF4555;
            line-height: 22px;
        }

        .btn{
            width: 240px;
            height: 42px;
            background: #FF4555;
            border-radius: 4px;

            font-size: 16px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #FFFFFF;
            line-height: 42px;
            text-align: center;
            margin-left: 54px;
        }
     }

    /*加载中效果START*/
    #editorLoadingIn{
        position: absolute;
        top:0;
        left: 0;
        bottom: 0;
        right: 0;
        z-index: 1000000000;
    }
    #editorLoadingIn .loader {
        position: absolute;
        top: 50%;
        left: 50%;
        margin-right: -60px;
        margin-top: -24px;
        transform: translate3d(-50%, -50%, 0);
    }
    #editorLoadingIn .dot {
        width: 24px;
        height: 24px;
        background: #3ac;
        border-radius: 100%;
        display: inline-block;
        animation: slideIn 1s infinite;
    }
    #editorLoadingIn .dot:nth-child(1) {
        animation-delay: 0.1s;
        background: #ffce6f;
    }
    #editorLoadingIn .dot:nth-child(2) {
        animation-delay: 0.2s;
        background: #ffa868;
    }
    #editorLoadingIn .dot:nth-child(3) {
        animation-delay: 0.3s;
        background: #ff8963;
    }
    #editorLoadingIn .dot:nth-child(4) {
        animation-delay: 0.4s;
        background: #fc7245;
    }
    #editorLoadingIn .dot:nth-child(5) {
        animation-delay: 0.5s;
        background: #f25b4a;
    }
    @-moz-keyframes slideIn {
        0% {
            transform: scale(1);
        }
        50% {
            opacity: 0.3;
            transform: scale(2);
        }
        100% {
            transform: scale(1);
        }
    }
    @-webkit-keyframes slideIn {
        0% {
            transform: scale(1);
        }
        50% {
            opacity: 0.3;
            transform: scale(2);
        }
        100% {
            transform: scale(1);
        }
    }
    @-o-keyframes slideIn {
        0% {
            transform: scale(1);
        }
        50% {
            opacity: 0.3;
            transform: scale(2);
        }
        100% {
            transform: scale(1);
        }
    }
    @keyframes slideIn {
        0% {
            transform: scale(1);
        }
        50% {
            opacity: 0.3;
            transform: scale(2);
        }
        100% {
            transform: scale(1);
        }
    }
    #editorLoadingIn{
        .wrap {
            margin-top: 65px;
            .progressBar {
                top: 170px;
            }
            .downloadTextNum {
                margin-top: 20px;
                font-size: 20px;
                font-weight: 500;
                line-height: 28px;
                letter-spacing: 1.2px;
                text-align: center;
                color: rgba(31, 26, 27, 1);
            }
            .downloadLoadingText {
                margin-top: 20px;
                width: 100%;
                margin-top: 18px;
                text-align: center;
                color: rgba(121, 118, 118, 1);
                white-space:nowrap;
            }
        }
        .loaderText{
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            margin-top: 20px;
            text-align: center;
            color: #666;
            white-space:nowrap;
        }

    }

    /*加载中效果END*/
}
/*下载弹窗END*/

/*分享限制弹窗START*/
#noShare{
    .abTestCloseBtn{
        left: 10px;
        top: -40px;
    }
    .noShareTitle{
        margin-top: 40px;
        text-align: center;
        font-size: 20px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FF4555;
        line-height: 30px;

    }
    .noShareConter{
        text-align: center;
        margin: 0;
        padding: 0;
        font-size: 16px;
        color: #333333;
        margin-top: 30px;
    }
    .noShareBtn{
        width: 108px;
        height: 38px;
        background: #FF4555;
        border-radius: 4px;
        margin: 0 auto;
        text-align: center;
        line-height: 38px;
        color: #fff;
        font-size: 14px;
        margin-top: 35px;
        cursor: pointer;
    }
}
    /*分享限制弹窗EDN*/
//消息弹窗
.promptBox{
    position: absolute;
    z-index: 9999999999999;
    width: 100%;
    top: -0px;
    // top: 30px !important;

    .promptBoxContent{
        position: absolute;
        transition: all 0.5s ease-in;
        left: 50%;
        top: 0px;
        transform: translate(-50%, 0);
        border-radius: 5px 5px 5px 5px;
        min-width: 100px;
        padding: 0px 20px;
        min-height: 50px;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 1px 2px rgba(153, 153, 153, 0.8);
        transition: all 0.5s ease-in;
        // opacity: 1;
        opacity: 0;
        //display: none;
        .errorMsg{
            height: 50px;
            display: flex;
            width: calc(100% + 40px);
            margin-left: -20px;
            align-items: center;
            justify-content: center;
            background: #FDEBF0;
            border-radius: 5px 5px 5px 5px;
            opacity: 1;
            border: 1px solid #FCD7E0;
            font-size: 14px;
            font-weight: 400;
            color: #1F1A1B;
            i{
                color: #EF3964;
                margin-right: 12px;
                font-size: 20px;
            }
        }
        .successMsg{
            height: 50px;
            display: flex;
            width: calc(100% + 40px);
            margin-left: -20px;
            align-items: center;
            justify-content: center;
            background: #E7FFE7;
            border-radius: 5px 5px 5px 5px;
            opacity: 1;
            border: 1px solid #0EB52D;
            font-size: 14px;
            font-weight: 400;
            color: #1F1A1B;
            i{
                color: #fff;
                font-size: 14px;
            }
        }
        .warnMsg{
            height: 50px;
            display: flex;
            width: calc(100% + 40px);
            margin-left: -20px;
            align-items: center;
            justify-content: center;
            background: #FFF2E6;
            border-radius: 5px 5px 5px 5px;
            opacity: 1;
            border: 1px solid #E57A19;
            font-size: 14px;
            font-weight: 400;
            color: #1F1A1B;
            i{
                color: #E57A19;
                margin-right: 12px;
                font-size: 20px;
            }
        }
    }
    &.active{
        .promptBoxContent{
            opacity: 1;
            top: 62px;
        }
    }
}

/*二维码弹窗START*/
.QRcodeGeneratePopup{
    color: #666;
    a{
        text-decoration: none;
    }
    .downloadSuccess{
        .successIcon{
            font-size: 60px;
            color: #f25b4a;
            position: absolute;
            left: 50%;
            top: 55px;
            margin-left: -30px;
        }
        .successText{
            font-size: 18px;
            position: absolute;
            bottom: 66px;
            left: 0;
            width: 100%;
            text-align: center;
        }
    }

    /*加载中效果START*/
    #editorLoadingIn{
        position: absolute;
        top:0;
        left: 0;
        bottom: 0;
        right: 0;
        z-index: 1000000000;
    }
    #editorLoadingIn .loader {
        position: absolute;
        top: 50%;
        left: 50%;
        margin-right: -60px;
        margin-top: -24px;
        transform: translate3d(-50%, -50%, 0);
    }
    #editorLoadingIn .dot {
        width: 24px;
        height: 24px;
        background: #3ac;
        border-radius: 100%;
        display: inline-block;
        animation: slideIn 1s infinite;
    }
    #editorLoadingIn .dot:nth-child(1) {
        animation-delay: 0.1s;
        background: #ffce6f;
    }
    #editorLoadingIn .dot:nth-child(2) {
        animation-delay: 0.2s;
        background: #ffa868;
    }
    #editorLoadingIn .dot:nth-child(3) {
        animation-delay: 0.3s;
        background: #ff8963;
    }
    #editorLoadingIn .dot:nth-child(4) {
        animation-delay: 0.4s;
        background: #fc7245;
    }
    #editorLoadingIn .dot:nth-child(5) {
        animation-delay: 0.5s;
        background: #f25b4a;
    }
    @-moz-keyframes slideIn {
        0% {
            transform: scale(1);
        }
        50% {
            opacity: 0.3;
            transform: scale(2);
        }
        100% {
            transform: scale(1);
        }
    }
    @-webkit-keyframes slideIn {
        0% {
            transform: scale(1);
        }
        50% {
            opacity: 0.3;
            transform: scale(2);
        }
        100% {
            transform: scale(1);
        }
    }
    @-o-keyframes slideIn {
        0% {
            transform: scale(1);
        }
        50% {
            opacity: 0.3;
            transform: scale(2);
        }
        100% {
            transform: scale(1);
        }
    }
    @keyframes slideIn {
        0% {
            transform: scale(1);
        }
        50% {
            opacity: 0.3;
            transform: scale(2);
        }
        100% {
            transform: scale(1);
        }
    }
    #editorLoadingIn .loaderText{
        position: absolute;
        top: 50%;
        left: 0;
        width: 100%;
        margin-top: 20px;
        text-align: center;
        color: #666;
        white-space:nowrap;
    }
    /*加载中效果END*/
}
/*二维码弹窗END*/

/*赚取画布（鼠标移动画布）START*/
#canvasGrab{
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100000000;
    cursor: url($imgHost + "/site/editor/hand.png") 8 8, auto;
}
/*赚取画布（鼠标移动画布）END*/

/*副本限制弹窗START*/
#noCreatCopy{
    .abTestCloseBtn{
        left: 10px;
        top: -40px;
    }
    .noCreatCopyTitle{
        margin-top: 40px;
        text-align: center;
        font-size: 20px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FF4555;
        line-height: 30px;

    }
    .noCreatCopyConter{
        text-align: center;
        margin: 0;
        padding: 0;
        font-size: 16px;
        color: #333333;
        margin-top: 30px;
    }
    .noCreatCopyBtn{
        width: 108px;
        height: 38px;
        background: #FF4555;
        border-radius: 4px;
        margin: 0 auto;
        text-align: center;
        line-height: 38px;
        color: #fff;
        font-size: 14px;
        margin-top: 35px;
        cursor: pointer;
    }


}
/*副本限制弹窗EDN*/

// 会缩放动画的按钮
.btn-scale-drew {
    animation: scaleDrew 2s ease-in-out infinite;
}
@keyframes scaleDrew {
    0% {
        transform: scale(0.9);
    }

    25% {
        transform: scale(1.1);
    }

    50% {
        transform: scale(0.9);
    }

    75% {
        transform: scale(1.1);
    }

    100%{
        transform: scale(0.9);
    }
}
