@import "../../clientV6.0/Base";

.ppt-table-panel{
    height: 100%;
    overflow-x: hidden;
    overflow-y: overlay;

    .table-content{
        display: flex;
        justify-content:flex-start;
        flex-wrap: wrap;
        align-content: start;

        .cus-new-table{
            width:196px;
            height:38px;
            text-align: center;
            line-height: 38px;
            font-size: 12px;
            color: #202020;
            border: 1px dashed #B7B7B7;
            margin: 25px 0 28px 58px;
            cursor: pointer;
            position: relative;
        }
        .list-item{
            width:150px;
            height:150px;
            margin: 0 10px 10px 0;
            margin-right: 10px;
            cursor: pointer;
            border-radius: 4px;
            // margin-bottom: 50px;
            background:rgba(249,250,252,1);
            display: flex;
            justify-content: center;
            align-items: center;
            &:nth-of-type(2n + 1){
                margin-right: 0;
            }
            &:hover{
                //box-shadow:0 2px 6px 1px rgba(198,202,209,1);
                // border-color: #D6DBE1;
                // box-shadow:0px 2px 6px 1px #C6CAD1;
                background:rgba(0,29,90,0.08);
            }
        }
        .isActive{
            box-shadow:0px 2px 6px 1px rgba(198,202,209,1);
        }
        .show-more-item {
            position: relative;
            width: 68px;
            height: 68px;
            border-radius: 4px;
            background-color: #F1F3F7;
            text-align: center;
            color: #666666;
            font-size: 12px;
            float: left;
            margin-left: 12px;
            margin-top: 11px;
            line-height: 1.8;
            cursor: pointer;

            i {
                display: block;
                font-size: 16px;
                padding-top: 17px;
            }
        }
        .tool-down-wrapper {
            position: absolute;
            top: 100%;
            left: 0;
            width: 280px;
            padding-top: 4px;
            cursor: default;

            &-inner {
                background: rgba(255,255,255,1);
                box-shadow: 0 2px 6px 2px rgba(184,191,204,0.2);
                border-radius: 4px;
                border: 1px solid rgba(214,219,225,1);
                overflow: hidden;

                .title {
                    height: 42px;
                    line-height: 42px;
                    background: #eeeeee;
                    padding-left: 18px;
                    font-size: 14px;
                    color: #202020;
                    font-weight: 500;
                    text-align: left;
                }

                .container {
                    max-height: 694px - 42 - 10 - 20;
                    overflow: auto;
                    padding-left: 5px;
                    padding-bottom: 10px;
                    padding-top: 20px;

                    @media screen and (max-height: 850px) {
                        max-height: 447px - 42 - 10 - 20;
                    }

                    .list-item {
                        width: 248px;
                        height: 140px;
                        margin-top: 10px;
                        margin-left: 10px;

                        &:hover {
                            box-shadow: 0px 2px 6px 1px rgba(198,202,209,1);
                        }
                    }
                }
            }
        }
    }
}
.cus-new-table .table-custom-panel{
    position: absolute;
    left: 0;
    top: 74px;
    width:228px;
    height:291px;
    padding: 9px 0 0 0;
    background:rgba(255,255,255,1);
    box-shadow:0px 2px 8px 2px rgba(0,0,0,0.08);
    border-radius:4px;
    border:1px solid rgba(214,219,225,1);
    text-align: left;
    cursor: default;
    .cus-title{
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        padding-left: 21px;
        background: #F1F3F7;
        span{
            color: #EA1A1A;
        }
    }
    .table-short-cut{
        padding: 8px 10px 3px 10px;
        border-bottom: 1px solid $pptBorderColor;
        .cus-table-row{
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            .cell-item{
                display: inline-block;
                width:16px;
                height:16px;
                background:#fff;
                border-radius:1px;
                border:1px solid $pptBtnTextColor;
                box-sizing: border-box;
            }
        }
    }
    .cus-comfirm{
        padding-left: 10px;
        font-size: 14px;
        color: $pptBtnTextColor;
        line-height: 35px;
        span{
            display: inline-block;
            vertical-align: middle;
            margin-right: 4px;
            &:last-child{
                margin-top: -2px;
            }
        }
    }
}
.tableEditorBox{
    padding: 10px 0 0 5px !important;
    margin-top: 0;

    .tableDescStyle{
        width:56px;
        height:20px;
        font-size:14px;
        font-family:PingFangSC-Regular,PingFang SC;
        font-weight: 600;
        color:rgba(102,102,102,1);
        line-height:20px;
        margin-top: -10px;
        margin-left: 20px;
    }

    .previewTable{
        margin-left: 20px;
        width:220px;
        height:68px;
        background:rgba(246,247,249,1);
        border-radius:4px 4px 0px 0px;
        margin-top:12px;
        position: relative;
        cursor: pointer;
        .currentTableStyle{
            margin-left: 46px;
            margin-top: 12px;
            width: 70px;
            height: 70px;
            // background-color: rgba(216, 216, 216, 1);
            border-radius:4px;
            display: flex;
            align-items: center;
            justify-content: center;
            
        }  
        
        .icon{
            position: relative;
            left: 180px;
            top: -43px; 
        }
        .iconfont{
            cursor: pointer;
        }
        // }
    }

    .toolColorPanel{
        margin-left: 20px;
        width:220px;
        // height:94px;
        background:rgba(246,247,249,1);
        border-radius:0px 0px 4px 4px;
        // margin-top:14px;
        position: relative;
        border-top-style: none;
        background-color: RGBA(255, 255, 255, 1);
        position: relative;
        .colorSets{
            margin-top: 12px;
            display: flex;
            padding: 9px 12px;
            background: #f7f7f7;
            .colorItem{
                flex: 1;
                height: 30px;
                border-radius: 4px;
                cursor: pointer;
                margin-right: 12px;
            }
            .borderColorItem{
                border: 1px solid #B7B7B7;
            }
            .more-color {
                line-height: 30px;
                cursor: pointer;
            }
        }
        .topInfo{
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 0px 17px 0px 17px;
            padding-top: 7px;
            span{
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #202020;
                line-height: 17px;
            }
            .colorIcon{
                cursor: pointer;

                &:hover{
                    color: #FF4555;
                }
            }
        }

        .presetColorContainer{
            width:220px;
            // height:571px;
            background:rgba(255,255,255,1);
            box-shadow:0px 2px 6px 2px rgba(0,0,0,0.1);
            border-radius:4px;
            position: absolute;
            top: 50px;
            left: 0;
            padding-bottom: 16px;
            z-index: 100;
            p{
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #666666;
                line-height: 17px;
                margin-left: 18px;
                margin-top:14px;
                margin-bottom: 8px;
            }

            .presetColorContent{
                display: flex;
                flex-wrap:wrap;
                margin-left: 15px;
                margin-right: 10px;
                .presetColorItem{
                    width: 60px;
                    height: 32px;
                    display:flex;
                    margin-bottom:5px;
                    margin-top:5px;
                    cursor: pointer;
                    .shemeItemBlockItem{
                        width: 20px;
                        height: 32px;
                        &:nth-child(1){
                            margin-left: 5px;
                            border-radius: 4px 0px 0px 4px;
                        }

                        &:nth-child(3){
                            margin-right: 5px;
                            border-radius: 0px 4px 4px 0px;
                        }
                    }

                    .shemeItemBlockItemDouble{
                        width: 20px;
                        height: 32px;
                        &:nth-child(1){
                            margin-left: 5px;
                            border-radius: 4px 0px 0px 4px;
                        }

                        &:nth-child(2){
                            margin-right: 5px;
                            border-radius: 0px 4px 4px 0px;
                        }
                    }
                }
            }
            // right: 10px;
        }
    
    }

    .tableTransparent{
        display:flex;
        margin:23px 30px 23px 20px;
        justify-content: space-between;
        .desc{
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #666666;
            line-height: 16px;
        }
        .sliderContainer{
            padding-top: 3px;
        }
        // .transparentControlSwitchC{
        //     width:35px;
        //     height:18px;
        //     // background:rgba(191,191,191,1);
        //     background-color: white;
        //     border:1px solid rgba(204,204,204,1);
        //     border-radius:10px;
        //     position: relative;
        //     transition: all 0.28s;
        //     cursor: pointer;
        //     margin-left: 117px;
            // i{
            //     position: absolute;
            //     top: 0px;
            //     left: 0px;
            //     transition: all 0.28s;
            //     width:16px;
            //     height:16px;
            //     background:rgba(191,191,191,1);
            //     border-radius:10px;
            // }

    //   &.active{
    //     // background:rgba(255,69,85,1);

    //     i{
    //       left: 18px;
    //     }
    //     .transparentControlSwitchOff{
    //       background-color: #FF4555;
    //     }
    //     .transparentControlSwitchOn{
    //         background-color: red;
    //       }
        
        
    //     }
    // }

    }

    .tableEditContainer{
        margin-top:12px;
        margin-bottom: 14px;
        p{
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #666666;
            line-height: 20px;
            margin-bottom:14px;
        }
        .tableEdit{
            width: 220px;
            height: 38px;
            background: #ff4555;
            border-radius: 4px;
            border: 1px solid #D6DBE1;
            display: flex;
            justify-content: center;
            align-items: center;

            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #fff;
            line-height: 20px;

        }
    }

    .fontContainer{
        .tableFontFamily{
            display: flex;
            // padding-top: 12px;
        }
        .fontSizeDesc{
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #202020;
            line-height: 17px;
            margin-bottom: 8px;
        }
        .item {
            margin-top: 0!important;

                .commonSelect{
                    .color-area{
                        height: 44px;
                    }
                  .choice-color-wrap{
                    width: 105px;
                    height: 44px;
                    box-sizing: border-box;
                    border-radius: 5px;
                    background: var(---, #F7F7F7);
                    .choiceColor{
                      box-sizing: border-box;
                      width: 30px;
                      height: 30px;
                      border-radius: 4px;
                      margin-top: 7px;
                      margin-right: 8px;
                    }
                    .color-tip{
                      font-size: 14px;
                      font-weight: 600;
                      line-height: 44px;
                    }
                    i{
                      margin: unset;
                    }
                  }
               
                    .fontSizeSelectInput{
                        padding:0;
                    }
                
                }
                .fontSizeSelect{
                    width: 105px;
                    margin:0;
                    margin-right: 10px;
                }
           
              
        }
    }

    
    .previewTableList{
        width: 220px;
        height: 431px;
        background:RGBA(255, 255, 255, 1);
        box-shadow:0px 2px 6px 2px rgba(0,0,0,0.1);
        border-radius:4px;
        position: absolute;
        top: 104px;
        left: 20px;
        z-index: 99;
        overflow: hidden;

        .previewTableContent{
            margin: 19px 14px 17px 15px;
            display: flex;
            justify-content:flex-start;
            flex-wrap: wrap;
            .list-item{
                width: 90px;
                height: 90px;
                background:rgba(249,250,252,1);
                border-radius:4px;
                margin-right: 11px;
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                &:hover {
                    background-color: rgba(0,29,90,0.08);
                }
                &:nth-child(2n){
                    margin-right: 0;
                }
            }
            .selectedPreviewTable{
                border:1px solid rgba(255,69,85,1);
            }

        }
    }

    .tableBorderBox{
        width: 224px;
        height: 276px;
        background-color: #aaa;
        position: absolute;
        top: -20px;
        right: 30px;
    }

    .tableEditBlock{
        padding-left: 0;
        // flex-direction: row;
        .item-table{
            display: flex;
            justify-content: space-between;
            padding-bottom: 25px;
            border-bottom: 1px solid $pptSplitLineColor;
            margin-bottom: 25px;
        }
        .table-color-item{
            width: 50%;
            display: flex;
            justify-content: space-around;
            align-items: center;
            .table-label{
                font-size: 12px;
            }
            .commonSelect{
                width: 65%;
            }
            .choiceColor{
                width: 25px;
                margin: 8px 0 0 8px;
            }
        }
        .table-item-title{
            margin-bottom: 18px;
            font-size: 14px;
            color: $pptPrimaryTextColor;
        }
    }
}
