
.assetActionBar {
  position: absolute;
  top: 100px;
  left: 100px;
  z-index: 9999999;
  width: 250px;
  max-height: calc(100vh - 50px - 10px - 40px);
  background: rgba(255, 255, 255, 1);
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.15);
  padding: 4px 0;
  color: rgba(0, 0, 0, 1);
  overflow: auto;

  &.table {
    width: 196px
  }

  * {
    box-sizing: border-box;
  }
  ul, li {
    list-style: none;
  }
  ::-webkit-scrollbar {
    background: none;
    height: 6px;
    width: 6px
  }
  ::-webkit-scrollbar-track {
    border-radius: 3px
  }
  ::-webkit-scrollbar-thumb {
    background: $scrollColor;
    border-radius: 3px;
    transition: background .2s ease
  }
  .actionItemWrap {
    padding: 4px 0;
    border-bottom: 1px solid rgba(233, 232, 232, 1);
    &.noline {
        border-bottom: none;
    
    }
  }
  .actionItem {
    margin: 0 10px;
    padding: 0 10px;
    cursor: pointer;
    line-height: 36px;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    &:hover {
        border-radius: 5px ;
        background: rgba(247, 247, 247, 1);
    }
  }
  .noActionItem {
    margin: 0 10px;
    padding: 0 10px;
    line-height: 36px;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    color: #999;
  }

  .actionOperate {
    color: rgba(165, 163, 164, 1);
  }

  .layersWrap {
    border-bottom: 1px solid rgba(233, 232, 232, 1);
    padding: 4px 0;
    &:last-child {
        border-bottom: none;
    }
  }

  .markedAsBackground{
    border-bottom: 1px solid rgba(233, 232, 232, 1);
    padding: 4px 0;
    .actionItem {
        justify-content: start;
        align-items: center;
        .fav {
            color: #EF3964;
        }
    }
    .iconfont {
        margin-right: 5px;
    }
  }
  .groupHandlerBox{
   @extend .markedAsBackground;
    .actionItem{
      justify-content: flex-start;
      align-items: center;
      .iconfont {
        transform: translateY(1px)
      }
    }
  }
  .multipleSelectBox {
   @extend .markedAsBackground;
   .actionItem{
    justify-content: flex-start;
  }
  }
  .wrapTitle {
    line-height: 36px;
    padding-left: 20px;
    font-size: 14px;
  }
  // .layersList {
  //   max-height: 268px;
  //   overflow-x: hidden;
  //   overflow-y: auto;
  // }
  .layerItem {
    padding: 4px 0 4px 20px;
    display: flex;
    align-items: center;
    cursor: pointer;
    text-align: center;
    line-height: 43px;
    &:hover, &.active {
        background: #FDEBF0;
        color: #EF3964;
      .iconfont {
        color: $primaryColor;
      }
    }
  }

  .layerThumbnail {
    width: 45px;
    height: 45px;
    overflow: hidden;
    border: 1px solid #e4e9ee;
    background-color: #fff;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    position: relative;
    .svgWrap {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .zuhe {
        position: absolute;
        width: 28px;
        height: 14px;
        font-size: 11px !important;
        right: 2px;
        bottom:2px;
        z-index: 10;
        border-radius: 2.333px;
        background: rgba(31, 26, 27, 0.2);
        color: #FFFFFF!important;
        display: block;
        line-height: 14px;
        &::before {
          content: "组合";
        }
    }
    .iconfont {
      font-size: 18px;
      color: #333;
    }
    .icon-tupianbianjiqizuoceicon_biaoge{
      font-size: 26px;
    }
    svg {
      width: 100%;
      height: 100%;
    }
  }
  .layerInfo {
    padding-left: 14px;
    font-size: 14px;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 160px;
    overflow: hidden;
  }
  //table
  .table-action-menu{
    .table-menu-item{
      box-sizing: content-box;
      height: 36px;
      cursor: pointer;
      span{
        font-size: 14px;
        display: block;
        line-height: 36px;
        padding-left: 24px;
      }
      &:hover{
        span{
          background: #e8e8e8;
        }
      }
    }
    .borderBottom-item{
      border-bottom: 1px solid #e4e9ee;
      margin-bottom: 4px;
      padding-bottom: 4px;
    }
  }
}