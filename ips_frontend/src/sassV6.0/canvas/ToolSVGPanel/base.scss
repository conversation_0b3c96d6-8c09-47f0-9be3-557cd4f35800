/*
  note：新文本面板  样式覆盖
  date: 2018-09-18

*/
.helperLayout .toolPanelNew {
  position: absolute;
  top: 60px;
  left: 0;
  right: 0;
  bottom: 83px;

  width: 100% !important;
  box-sizing: border-box;


  ::-webkit-scrollbar {
    background: none;
    height: 6px;
    width: 6px
  }
  ::-webkit-scrollbar-track {
    border-radius: 4px
  }
  ::-webkit-scrollbar-thumb {
    background: $scrollColor;
    border-radius: 3px;
    transition: background .2s ease
  }
}

.toolSVGPanel {
  * {
    box-sizing: border-box;
  }
  &.hasGroup {
    .assetsAdjust-wrap {
        height: calc(100vh - 230px);
    }
  }
  .assetsAdjust-wrap {
    height: calc(100vh - 110px);
    overflow-y: auto;
    overflow-x: hidden;
    padding-bottom: 20px;
  }

  .title {
    font-size: 16px;
    font-weight: 500;
    line-height: 25px;
    padding: 16px 20px 0 ;
  }

  ul,li{
    margin: 0;
    list-style: none;
    padding: 0;
  }

//   input {
//     font-size: 14px;
//     color: $fgColor2;
//     border: 1px solid $lineColor;
//     background: #fff;
//     outline: none;
//     padding: 0 14px;
//   }

  width: 100% !important;
  height: 100%;
  padding: 0 !important;
  padding-left:5px !important;
  overflow-x: hidden;
  overflow-y: auto;
  .BaseTool {
    padding: 0 20px!important;
  }
  .hr {
    height: 1px;
    background: #E9E8E8;
    margin: 16px auto;
    width: 230px;
  }


}
