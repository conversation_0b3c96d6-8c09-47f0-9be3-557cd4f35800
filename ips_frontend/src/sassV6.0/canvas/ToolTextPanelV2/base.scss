.textV2Container {
  width: 100% !important;
  overflow-x: hidden;
  overflow-y: auto;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  .item{
    .commonSelect{
      .choice-color-wrap{
        width: 110px;
        box-sizing: border-box;
        border-radius: 5px;
        background: var(---, #F7F7F7);
        margin-right: 10px;
        .choiceColor{
          box-sizing: border-box;
          width: 30px;
          height: 30px;
          border-radius: 4px;
          margin-top: 7px;
          margin-right: 8px;
        }
        .color-tip{
          font-size: 14px;
          font-weight: 600;
          line-height: 44px;
        }
        i{
          margin: unset;
        }
      }
     

    }
    .colorsArea{
      height: 40px;
      background: unset;
    }
   
  }
 
  .backgroundItem{
    .commonSelect{
      .choiceColor{
        width: 154px;
      }
    }
  }
    // 标题编辑
    .quickEditInput {
      position: relative;
      width: 230px;
      margin-bottom: 4px;
      margin-top: 6px;
      input {
        width: inherit;
        height: 38px;
        line-height: 38px;
        background: rgba(241, 243, 247, 1);
        border-radius: 2px;
  
        &:focus {
          & + .quickEditingText {
            display: inline-block;
          }
        }
      }
      .quickEditingText {
        width: inherit;
        position: absolute;
        top: 32px;
        left: 0;
        background: #fff;
        z-index: 100;
        box-shadow: 0 2px 4px #ccc;
        border-radius: 2px;
  
        .dropDownBoxItem {
          width: inherit;
          height: 32px;
          line-height: 32px;
          text-align: center;
          padding: 0 18px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 12px;
          color: #666;
          cursor: pointer;
          &:hover {
            color: $primaryColorV6_2;
            background: #f2f2f2;
          }
        }
      }
    }
}

.textV2Tab {
  font-size: 16px;
  font-weight: 500;
  height: 60px;
  padding: 0 20px;
  display: flex;
}

.textV2TabItem {
  width: 50%;
  line-height: 25px;
  text-align: center;
  padding: 15px 0;
  border-bottom: 1px solid #E9E8E8;
  cursor: pointer;
}
.textV2TabActive {
  border-bottom: 2px solid #EF3964;
  font-weight: 600;
}
