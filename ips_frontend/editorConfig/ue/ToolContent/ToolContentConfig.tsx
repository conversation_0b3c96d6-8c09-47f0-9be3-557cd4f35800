import React from 'react';

import { SpecificWord, DisplaySpecificWordTool } from '@v7_render/SpecificWord';
import { SourceMaterialPanel, DisplaySourceMaterialTool } from '@v7_render/SourceMaterialPanel';
import { SpecificWord3D, DisplaySpecificWord3DTool } from '@v7_render/SpecificWord3D';
import { PicPanel, DisplayPicTool } from '@v7_render/PicPanel';
import { TableChartPanel, DisplayTableChartTool } from '@v7_render/TableChartPanel';
import { QRcodePanelFn, DisplayQRcodePanelFn } from '@v7_render/QRcodePanelFn';

import { GalleryPanel, DisplayGalleryTool } from '@v7_render/GalleryPanel';
import { CopyWriting, DisplayCopywritingTool } from '@v7_render/CopyWriting';
// import { DisplayUploadFileTool } from '@v7_render/UserUploadArea';
import { TeamWorkPanel, DisplayTeamWorkTool } from '@v7_render/TeamWorkPanel';
import { BackgroundPanel, DisplayBackgroundTool } from '@v7_render/BackgroundPanel';
import {
    TemplatePanel,
    DisplayTemplate,
    DisplayTemplate3D,
    // DisplayTemplateIns,
    // DisplayTemplateCartoon,
    // DisplayTemplateCharacters,
    DisplayTemplateEstate,
    DisplayTemplateBank,
    DisplayTemplateBeauty,
} from '@v7_render/TemplatePanel';
// import {UserUploadArea} from '@component/toolV6.0/UserUploadArea';
import { PageAnimationPanel, DisplayPageAnimationTool } from '@v7_render/PageAnimationPanel';
import { MusicTab, DisplayMusicTool } from '@v7_render/AssetPanel/Music';
import { DisplayVideoETool, VideoEPanel } from '@v7_render/AssetPanel/VideoE';
import { CustomizeMenuMore, DisplayCustomizeMenuMore } from '@v7_render/CustomizeMenu';
import { DisplayMedicalMaterialTool, MedicalMaterialPanel } from '@v7_render/AssetPanel/MedicalMaterial';
import { DisplayGoodNewsMaterialTool, GoodNewsMaterialPanel } from '@v7_render/AssetPanel/GoodNewsMaterial';
import { DisplayFindJobMaterialTool, FindJobMaterialPanel } from '@v7_render/AssetPanel/FindJobMaterial';
import { DisplaySchoolMaterialTool, SchoolMaterialPanel } from '@v7_render/AssetPanel/SchoolMaterial';
import { DisplayWordartMaterialTool, WordartMaterialPanel } from '@v7_render/AssetPanel/WordartMaterial';
import { DisplayTemplateFinance } from '@v7_render/TemplatePanel/TemplateFinance';
import { AiText, DisplayAiText } from '@v7_render/AssetPanel/AiText';
import { AiDraw, DisplayAiDraw } from '@v7_render/AssetPanel/AiDraw';
import { AiProducts, DisplayAiProducts } from '@v7_render/AssetPanel/AiProducts';
import { AiCutout, DisplayAiCutout } from '@v7_render/AssetPanel/AiCutout';
import { DisplayMyTool, MyPanel } from '@v7_render/AssetPanel/MyPanel';
import { DisplayBaiduNetdiskToll, BaiduNetdisk } from '@v7_render/AssetPanel/ThirdAssets/BaiduNetdisk';
import { DisplayEmojiPageToll, EmojiPage } from '@v7_render/AssetPanel/ThirdAssets/EmojiPage';
import { DisplayTencentMapsToll, TencentMaps } from '@v7_render/AssetPanel/ThirdAssets/TencentMaps';
import { DisplayAddMenuTool, AddMenu } from '@v7_render/AssetPanel/AddMenu';
import { AIToolGeneralPanel } from '@v7_render/AssetPanel/AiToolGeneralPanel';
import { AIChat } from '@v7_render/AssetPanel/AIChat';
import {
    DisplayAIChatPanel,
    DisplayAICutoutPanel,
    DisplayAIEliminatePanel,
    DisplayAIEliminateRedrawPanel,
    DisplayAIEnhancePanel,
} from '@v7_render/AssetPanel/AiToolGeneralPanel/AIToolGeneralPanel';
import { ETool } from '@v7_logic/Enum';

/**
 * Canvas 插槽组件配置
 */
export const TOOL_CONTENT_CONFIG = {
    tool: [
        {
            showComponentFun: DisplaySpecificWordTool,
            component: <SpecificWord />,
        },
        {
            showComponentFun: DisplaySpecificWord3DTool,
            component: <SpecificWord3D />,
        },
        {
            showComponentFun: DisplayMyTool,
            component: <MyPanel />,
        },
        {
            showComponentFun: DisplayBaiduNetdiskToll,
            component: <BaiduNetdisk />,
        },
        {
            showComponentFun: DisplayEmojiPageToll,
            component: <EmojiPage />,
        },
        {
            showComponentFun: DisplayTencentMapsToll,
            component: <TencentMaps />,
        },
        {
            showComponentFun: DisplaySourceMaterialTool,
            component: <SourceMaterialPanel />,
        },
        {
            showComponentFun: DisplayBackgroundTool,
            component: <BackgroundPanel />,
        },
        {
            showComponentFun: DisplayPicTool,
            component: <PicPanel />,
        },
        {
            showComponentFun: DisplayTableChartTool,
            component: <TableChartPanel />,
        },
        {
            showComponentFun: DisplayGalleryTool,
            component: <GalleryPanel />,
        },
        {
            showComponentFun: DisplayCopywritingTool,
            component: <CopyWriting />,
        },
        // {
        //     showComponentFun: DisplayUploadFileTool,
        //     component: <UserUploadArea />,
        // },
        {
            showComponentFun: DisplayTeamWorkTool,
            component: <TeamWorkPanel />,
        },
        {
            showComponentFun: DisplayTemplate,
            component: <TemplatePanel type="template" />,
        },
        {
            showComponentFun: DisplayTemplate3D,
            component: <TemplatePanel type="template3D" />,
        },
        {
            showComponentFun: DisplayQRcodePanelFn,
            component: <QRcodePanelFn />,
        },
        // {
        //     showComponentFun: DisplayTemplateIns,
        //     component: <TemplatePanel type="templateIns" />,
        // },
        // {
        //     showComponentFun: DisplayTemplateCartoon,
        //     component: <TemplatePanel type="templateCartoon" />,
        // },
        // {
        //     showComponentFun: DisplayTemplateCharacters,
        //     component: <TemplatePanel type="templateCharacters" />,
        // },
        {
            showComponentFun: DisplayTemplateEstate,
            component: <TemplatePanel type="templateEstate" />,
        },
        {
            showComponentFun: DisplayTemplateBank,
            component: <TemplatePanel type="templateBank" />,
        },
        {
            showComponentFun: DisplayTemplateBeauty,
            component: <TemplatePanel type="templateBeauty" />,
        },
        {
            showComponentFun: DisplayTemplateFinance,
            component: <TemplatePanel type="templateFinance" />,
        },
        {
            // 2023-6-9 菜单切换成下线
            showComponentFun: DisplayPageAnimationTool,
            component: <PageAnimationPanel />,
        },
        {
            showComponentFun: DisplayVideoETool,
            component: <VideoEPanel />,
        },
        {
            showComponentFun: DisplayMusicTool,
            component: <MusicTab />,
        },
        {
            showComponentFun: DisplayCustomizeMenuMore,
            component: <CustomizeMenuMore />,
        },
        {
            showComponentFun: DisplayMedicalMaterialTool,
            component: <MedicalMaterialPanel />,
        },
        {
            showComponentFun: DisplayGoodNewsMaterialTool,
            component: <GoodNewsMaterialPanel />,
        },
        {
            showComponentFun: DisplayFindJobMaterialTool,
            component: <FindJobMaterialPanel />,
        },
        {
            showComponentFun: DisplaySchoolMaterialTool,
            component: <SchoolMaterialPanel />,
        },
        {
            showComponentFun: DisplayWordartMaterialTool,
            component: <WordartMaterialPanel />,
        },
        {
            showComponentFun: DisplayAiText,
            component: <AiText />,
        },
        {
            showComponentFun: DisplayAiDraw,
            component: <AiDraw />,
        },
        {
            showComponentFun: DisplayAICutoutPanel,
            component: <AIToolGeneralPanel toolType={ETool.AI_CUTOUT} />,
        },
        {
            showComponentFun: DisplayAiProducts,
            component: <AiProducts />,
        },
        {
            showComponentFun: DisplayAddMenuTool,
            component: <AddMenu/>,
        },
        {
            showComponentFun: DisplayAIEnhancePanel,
            component: <AIToolGeneralPanel toolType={ETool.AI_ENHANCE} />,
        },
        {
            showComponentFun: DisplayAIEliminatePanel,
            component: <AIToolGeneralPanel toolType={ETool.AI_ELIMINATE} />,
        },
        {
            showComponentFun: DisplayAIEliminateRedrawPanel,
            component: <AIToolGeneralPanel toolType={ETool.AI_ELIMINATEREDRAW} />,
        },
        {
            showComponentFun: DisplayAIChatPanel,
            component: <AIChat />,
        }
    ],
};
