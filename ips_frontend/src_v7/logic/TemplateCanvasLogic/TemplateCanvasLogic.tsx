import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import type React from 'react';
import { EventListener, addEventListener } from '@v7_utils/AddEventListener';
import { TStoreAction } from '@v7_logic/Interface';
import { SelectAsset } from '@v7_logic/AssetLogic';
import { CANVAS_GAP, getLFPanelFixedInfo } from '@v7_utils/canvas';
// 待替换组件
import { assetManager } from '@src/userComponentV6.0/AssetManager';
import { emitter } from '@src/userComponentV6.0/Emitter';
import { env } from '@editorConfig/env';
import { getProps } from '@src/userComponentV6.0/IPSConfig';

/**
 * 画板状态管理
 */
class TemplateCanvasLogic {
    private canvasGrabMoveListener: EventListener;
    private canvasGrabUpListener: EventListener;
    private showOffset = 50;
    private resizeTimer: number = undefined;
    private oldWidth: number = undefined;
    static autoScale: number;
    static fullScale: number;
    /**
     * 设置 canvas_dom
     */
    public setCanvasDom(params: { dom: HTMLElement }) {
        const { dom } = params;

        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'SET_CANVAS_DOM',
            params: [
                {
                    type: 'setCanvasDom',
                    params: {
                        canvas_dom: dom,
                    },
                },
            ],
        });
    }

    /**
     * 获取 canvas_dom
     */
    public getCanvasDom(): HTMLElement {
        const store = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        let result = undefined;

        if (store.canvas_dom_info.is_exist) {
            result = document.querySelector('.' + store.canvas_dom_info.class_name) as HTMLElement;
        }

        return result;
    }

    /**
     * 设置 canvas_dom
     */
    public setCanvasDomInfo(params: { class_name?: string; is_exist?: boolean }) {
        const { class_name = '', is_exist = true } = params;

        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'SET_CANVAS_DOM_INFO',
            params: [
                {
                    type: 'setCanvasDomInfo',
                    params: {
                        class_name: class_name,
                        is_exist: is_exist,
                    },
                },
            ],
        });
    }

    /**
     * 修改 canvas 定位和尺寸
     */
    public setCanvasPositionAndSize(params: {
        x?: number;
        y?: number;
        width?: number;
        height?: number;
        showUnit?: 'px' | 'cm' | 'mm';
        scale?: number;
    }) {
        const { x, y, width, height, showUnit, scale } = params;
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'SET_CANVAS_POSITION_AND_SIZE',
            params: [
                {
                    type: 'updateCanvasInfo',
                    params: {
                        canvas: {
                            x: x,
                            y: y,
                            width: width,
                            height: height,
                            showUnit,
                            scale,
                        },
                    },
                },
            ],
        });
    }

    /**
     * 更新 canvas 缩放值根据步进值
     */
    public setCanvasScaleByStep(params: { offset_type: string; offset_step?: number; x?: number; y?: number }) {
        const { offset_type, offset_step = 5, x, y } = params;
        const { toolPanel,canvas, canvas_wrapper_dom_info } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        let scale;
        const canvasWidth = canvas.width * canvas.scale;
        const canvasHeight = canvas.height * canvas.scale;
        const canvasWrapperDom: HTMLElement = document.querySelector('.' + canvas_wrapper_dom_info.class_name);
        const canvasWrapperWidth = canvasWrapperDom.offsetWidth;
        const canvasWrapperHeight = canvasWrapperDom.offsetHeight;

        let canvas_left: number = canvas.x,
            canvas_top: number = canvas.y,
            mouseTop = canvasHeight / 2,
            mouseLeft = canvasWidth / 2;
        mouseTop = y !== undefined ? y : mouseTop;
        mouseLeft = x !== undefined ? x : mouseLeft;
        // 相框编辑状态下阻止缩放
        if( toolPanel.asset && toolPanel.asset.meta?.isClip ) return 
        if (offset_type === 'plus') {
            scale = (Math.round((canvas.scale * 100) / offset_step) * offset_step + offset_step) / 100;
        } else if (offset_type === 'minus') {
            scale = (Math.round((canvas.scale * 100) / offset_step) * offset_step - offset_step) / 100;
        } else if (offset_type === '1') {
            scale = 1;
        } else {
            return;
        }

        scale = scale > 2 ? 2 : scale < 0.1 ? 0.1 : scale;

        const offsetWidth = canvas.width * (scale - canvas.scale);
        const offsetHeight = canvas.height * (scale - canvas.scale);
        // TODO 与后续操作合并成一次 store 触发
        this.setCanvasScale(scale);

        if (canvasWrapperWidth < canvasWidth || canvasWrapperHeight < canvasHeight) {
            canvas_left = canvas_left - offsetWidth * (mouseLeft / canvasWidth);
            canvas_top = canvas_top - offsetHeight * (mouseTop / canvasHeight);
        } else {
            canvas_left = (canvasWrapperDom.offsetWidth - canvas.width * scale) / 2;
            canvas_top = (canvasWrapperDom.offsetHeight - canvas.height * scale) / 2;
        }

        this.setCanvasPositionAndSize({
            x: canvas_left,
            y: canvas_top,
        });

        SelectAsset.scaleAssetInfo();
    }

    /**
     * 更新 canvas 缩放值
     */
    public setCanvasScale(scale: number, actions?: TStoreAction[]) {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'UPDATE_SCALE',
            params: [
                {
                    type: 'updateCanvasInfo',
                    params: {
                        canvas: {
                            scale: scale,
                        },
                    },
                },
                ...(actions || []),
            ],
        });
    }

    /**
     * 画板适应浏览器变动
     */
    public canvasResizeByWindow = () => {
        const { canvas_wrapper_dom_info, canvas, rt_editor_type, rt_canvas_render_mode, pageInfo, pageAttr, newTemplate } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const {info} = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
            store_name: storeAdapter.store_names.InfoManage,
        })
        console.log('newTemplate', newTemplate)
        if (!canvas_wrapper_dom_info['class_name']) {
            return false;
        }
        const canvasWrapperDom: HTMLDivElement = document.querySelector('.' + canvas_wrapper_dom_info['class_name']);
        if (!canvasWrapperDom) {
            return false;
        }
        const {isNavOpen, fixedToolPanel, subPanelWidth: leftPanelWidth, leftToolWidth} = getLFPanelFixedInfo()
        const ppt = info.template_type === 3 || location.href.includes('mode=ppt')
        const {diffwidth = '0'} = canvasWrapperDom.dataset
        let canvasWrapperDomWidth = canvasWrapperDom.offsetWidth - Number.parseInt(diffwidth)

        clearTimeout(this.resizeTimer);
        this.oldWidth = canvasWrapperDomWidth;
        this.resizeTimer = window.setTimeout(() => {
        const newWidth = canvasWrapperDom.offsetWidth - Number.parseInt(diffwidth);
            if (newWidth !== this.oldWidth) {
                this.canvasResizeByWindow();
            }
        }, 200);
        let top = canvasWrapperDom.offsetHeight * 0.054;
        let left = canvasWrapperDomWidth * 0.127;
        let scale: number;
        let width = canvasWrapperDom.offsetWidth;
        let height = canvasWrapperDom.offsetHeight - 50;
        const paddingLR = 120;
        const {TopGap, BaseGap, BottomGap } = CANVAS_GAP;
        const topGap = fixedToolPanel ? BaseGap : TopGap; 
        const rightPanelWidth = 271;
        const canvas_width = parseInt(`${canvas.width}`);
        const canvas_height = parseInt(`${canvas.height}`);
        // const canvasRatio = canvas_width / canvas_height;
        // const isSpecialScale = canvasRatio < 0.5 && !canvas.floorCutting && canvas_height/(height -150) > 2; // 图片的横竖比小于0.5时
        if (canvas_width >= canvas_height && !canvas.floorCutting) {
            if (isNavOpen) {
                width -= leftPanelWidth;
                canvasWrapperDomWidth -= leftPanelWidth;
            }
        }
        if (fixedToolPanel) {
            width -= rightPanelWidth;
            canvasWrapperDomWidth -= rightPanelWidth;
        } else {
            width -= rightPanelWidth + 80;
        }
        if (ppt && !isNavOpen) {
            width = canvasWrapperDomWidth * 0.7;
        }
        // else if (isSpecialScale) {
        //     width -=583;
        //     canvasWrapperDomWidth -= 583;
        // }
        //混合缩放处理 start
        let k = 10;
        let h = TopGap + BottomGap;
        if (rt_editor_type === 'canvas') {
            if (rt_canvas_render_mode === 'pull') {
                h += 30;
                k = 60;
            } else {
                if(pageAttr?.pageInfo?.length >1 && canvas_width > canvas_height) {
                    k = 120;
                }
            }
        }

        const scaleHeight = (height - h) / canvas_height; // 原210
        const scaleWidth = (width - paddingLR) / canvas_width;
        scale = scaleHeight > scaleWidth ? scaleWidth : scaleHeight;
        // 图片的横竖比小于0.5时
        // if (isSpecialScale ) {
        //     if (canvasRatio < 0.2) {
        //         const hScale = width * 0.4 / canvas_width;
        //         scale = Math.max(hScale, 0.5);
        //     } else {
        //         const hScale = width * 0.6 / canvas_width;
        //         scale = Math.min(hScale, 0.5);
        //     }
        // }
        left = (canvasWrapperDomWidth - canvas_width * scale) / 2;
        top = (canvasWrapperDom.offsetHeight - canvas_height * scale) / 2 - k; // 原55
        if (top < topGap) {
            top = topGap;
        }
        //混合缩放处理 end
        
        if (canvas_width > canvas_height && canvas_width / canvas_height > 1.1 && !(env.editor === "shentu" && getProps().height)) {
            if(env.editor === "shentu"){
                width = document.body.offsetWidth 
                height = document.body.offsetHeight
                top = 0
            }
        } else {
            if(env.editor === "shentu"){
                top = 0
                height = getProps().height ? getProps().height : window.innerHeight
            }
        }
        if (scale >= 2 && env.editor !== "shentu" ) {
            scale = 2;
            top = (canvasWrapperDom.offsetHeight - canvas_height * scale) / 2;
            left = (canvasWrapperDomWidth - canvas_width * scale) / 2;
        }
        if ((canvas_width >= canvas_height) && !canvas.floorCutting) {
            if (isNavOpen) {
                left += leftPanelWidth;
            } else {
                // left += rightPanelWidth;
            }
        }
        top += 50; // 安全距离
        // 如果是canvas编辑器 展示选中页面
        if (rt_editor_type === 'canvas' && pageInfo?.pageNow > 0 && rt_canvas_render_mode === '') {
            top = -(canvas.height * scale * pageInfo.pageNow + pageInfo.pageNow * BaseGap) + 50;
        }
        TemplateCanvasLogic.autoScale = scale;
        emitter.emit('finishLoadPage');
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'UPDATE_RESIZE_CANVAS',
            params: [
                {
                    type: 'updateCanvasInfo',
                    params: {
                        canvas: {
                            x: left,
                            y: top,
                            width: canvas_width,
                            height: canvas_height,
                            scale: scale,
                        },
                    },
                },
            ],
        });
    };

    /**
     * 充满整个屏幕
     */
    public canvasFullContainer() {
        const { canvas_wrapper_dom_info, canvas, rt_editor_type, rt_canvas_render_mode, pageInfo, pageAttr } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (!canvas_wrapper_dom_info['class_name']) {
            return false;
        }
        const canvasWrapperDom: HTMLDivElement = document.querySelector('.' + canvas_wrapper_dom_info['class_name']);
        if (!canvasWrapperDom) {
            return false;
        }
        const {isNavOpen, fixedToolPanel, subPanelWidth, leftToolWidth} = getLFPanelFixedInfo()

        let canvasWrapperDomWidth = canvasWrapperDom.offsetWidth

        let top = canvasWrapperDom.offsetHeight * 0.054;
        let left = leftToolWidth || canvasWrapperDomWidth * 0.127;
        let scale: number;
        let width = canvasWrapperDom.offsetWidth;
        const {TopGap, BaseGap, BottomGap} = CANVAS_GAP;
        const canvas_width = parseInt(`${canvas.width}`);
        const canvas_height = parseInt(`${canvas.height}`);
        if (!canvas.floorCutting) {
            if (isNavOpen) {
                width -= subPanelWidth;
                canvasWrapperDomWidth -= subPanelWidth;
                if (fixedToolPanel) {
                    width -= 271;
                    canvasWrapperDomWidth -= 271;
                }
            }
        }

        //混合缩放处理 start
        let k = 10;
        if (rt_editor_type === 'canvas') {
            if (rt_canvas_render_mode === 'pull') {
                k = 60;
            } else {
                if (pageAttr?.pageInfo?.length >1 && canvas_width > canvas_height) {
                    k = 120;
                }
            }
        }
        // 横向计算
        scale = (width - 40) / canvas_width;
        left = (canvasWrapperDomWidth - canvas_width * scale) / 2;
        top = (canvasWrapperDom.offsetHeight - canvas_height * scale) / 2 - k; // 原55
        if (isNavOpen) {
            left += subPanelWidth;
        }
        if (top < 60) {
            top = 60;
        }
        
        if (scale >= 2 && env.editor !== "shentu" ) {
            scale = 2;
            top = (canvasWrapperDom.offsetHeight - canvas_height * scale) / 2;
            left = (canvasWrapperDomWidth - canvas_width * scale) / 2;
        }

        // 如果是canvas编辑器 展示选中页面
        if (rt_editor_type === 'canvas' && pageInfo?.pageNow > 0 && rt_canvas_render_mode === '') {
            top = -(canvas.height * scale * pageInfo.pageNow + pageInfo.pageNow * BaseGap) + 50;
        }
        TemplateCanvasLogic.fullScale = scale;
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'UPDATE_RESIZE_CANVAS',
            params: [
                {
                    type: 'updateCanvasInfo',
                    params: {
                        canvas: {
                            x: left,
                            y: top,
                            width: canvas_width,
                            height: canvas_height,
                            scale: scale,
                        },
                    },
                },
            ],
        });
    }

    /**
     * 按住空格鼠标拖动画布
     */
    public canvasContentCanvasGrabMove(e: React.MouseEvent) {
        const { canvas, canvas_wrapper_dom_info } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const canvasWrapper = document.querySelector(`.${canvas_wrapper_dom_info.class_name}`);
        if (!canvas_wrapper_dom_info.is_exist || !canvasWrapper) {
            return;
        }
        const { x, y, width, height, scale }: { x: number; y: number; width: number; height: number; scale: number } =
            canvas;
        const { clientX, clientY } = e;
        const canvasWidth = width * scale;
        const canvasHeight = height * scale;
        const { clientWidth: canvasWrapperWidth, clientHeight: canvasWrapperHeight } = canvasWrapper;

        if (this.canvasGrabMoveListener) {
            this.canvasGrabMoveListener.remove();
        }

        if (this.canvasGrabUpListener) {
            this.canvasGrabUpListener.remove();
        }
        let isFirstMove = 1;
        this.canvasGrabMoveListener = addEventListener(window, 'mousemove', (e: MouseEvent) => {
            const offsetX = e.clientX - clientX;
            const offsetY = e.clientY - clientY;
            let canvasLeft = x + offsetX;
            let canvasTop = y + offsetY;

            /*边界判断START*/
            //左边
            canvasLeft = canvasLeft > -(canvasWidth - this.showOffset) ? canvasLeft : -(canvasWidth - this.showOffset);
            //上边
            canvasTop = canvasTop > -(canvasHeight - this.showOffset) ? canvasTop : -(canvasHeight - this.showOffset);
            //右边
            canvasLeft =
                canvasLeft < canvasWrapperWidth - this.showOffset ? canvasLeft : canvasWrapperWidth - this.showOffset;
            //下边
            canvasTop =
                canvasTop < canvasWrapperHeight - this.showOffset ? canvasTop : canvasWrapperHeight - this.showOffset;
            /*边界判断END*/

            this.setCanvasPositionAndSize({
                x: canvasLeft,
                y: canvasTop,
            });

            if (isFirstMove) {
                isFirstMove = 0;
                assetManager.setPv_new(183, { additional: {} });
            }
        });
        this.canvasGrabUpListener = addEventListener(window, 'mouseup', (e) => {
            this.canvasGrabUpListener.remove();
            this.canvasGrabMoveListener.remove();
        });
        return;
    }

    /**
     * 更新画布尺寸
     */
    public updateCanvasSize(params: { width: number; height: number; autoSave: number; showUnit?: string, }): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'UPDATE_CANVAS_SIZE',
            params: [
                {
                    type: 'updateCanvasSize',
                    params,
                },
            ],
        });
        emitter.emit('InfoBarUpdateState');
    }
    /**
     * 拖拽更新画布尺寸 start
     */
    public dragUpdateCanvasSizeStart(params: { width?: number; height?: number; autoSave: number; showUnit?: string, }): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'DRAG_UPDATE_CANVAS_SIZE_START',
            params: [
                {
                    type: 'updateCanvasSize',
                    params,
                },
            ],
        });
    }
    /**
     * 拖拽更新画布尺寸 start
     */
    public dragUpdateCanvasSizeEnd(params: { width?: number; height?: number; autoSave: number; showUnit?: string, }): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'DRAG_UPDATE_CANVAS_SIZE_END',
            params: [
                {
                    type: 'updateCanvasSize',
                    params,
                },
            ],
        });
    }
    /**
     * 适应画布
     */
    public getAutoScale() {
        return TemplateCanvasLogic.autoScale
    }
    /**
     * 填满屏幕
     */
    public getFullScale() {
        return TemplateCanvasLogic.fullScale
    }
}

/**
 * 画板状态管理
 */
const templateCanvasLogic = new TemplateCanvasLogic();
export { templateCanvasLogic as TemplateCanvasLogic };
