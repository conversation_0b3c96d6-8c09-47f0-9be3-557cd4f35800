import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { IAsset, IAssetInfo, ICanvas, IPageAttr, IUserInfo, IWork } from '@v7_logic/Interface';
import { klona as cloneDeep } from 'klona';
import { AssetHelper } from '@v7_logic/AssetHelper';
import { DeleteAsset, SelectAsset } from '@v7_logic/AssetLogic';
import { copyAssets } from '@src/userComponentV6.0/Function';
import { GroupAndMultipleSelectLogic } from '@v7_logic/GroupAndMultipleSelectLogic';
import { ESelectType } from '@v7_logic/Enum';
import { IUserAIInfo, IUserUploadLimitInfo } from '@v7_logic/Interface/UserInfo';
/**
 * 编辑器逻辑
 */
export class EditorLogic {
    /**
     * 更新编辑器是否是设计师编辑器
     */
    public static async updateIsDesigner(isDesigner: boolean): Promise<void> {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'UPDATE_ISDESIGNER',
            params: [
                {
                    type: 'updateIsDesigner',
                    params: {
                        isDesigner,
                    },
                },
            ],
        });
    }
        /**
     * 更新编辑器是否是ai相关设计师编辑器
     */
        public static async updateIsAiDesigner(isAiDesign: boolean): Promise<void> {
            storeAdapter.dispatch({
                store_name: storeAdapter.store_names.paintOnCanvas,
                fun_name: 'UPDATE_ISDESIGNER',
                params: [
                    {
                        type: 'updateIsAiDesign',
                        params: {
                            isAiDesign,
                        },
                    },
                ],
            });
        }
     /**
     * 更新当前模板是否是效果模板
     */
     public static async updateIsEffectTemplate(isEffectTemplate: boolean): Promise<void> {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'UPDATE_ISEFFECTTEMPLATE',
            params: [
                {
                    type: 'updateIsEffectTemplate',
                    params: {
                        isEffectTemplate,
                    },
                },
            ],
        });
    }
    /**
     * 设置为 group word user
     */
    public static updateGroupWordUser(isGroupWordUser: boolean): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'UPDATE_GROUPWORDUSER',
            params: [
                {
                    type: 'updateGroupWordUser',
                    params: {
                        isGroupWordUser,
                    },
                },
            ],
        });
    }

    /**
     * 更新是否是超级用户
     */
    public static updateIsSuperUser(isSuperUser: boolean): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'UPDATE_ISSUPERUSER',
            params: [
                {
                    type: 'updateIsSuperUser',
                    params: {
                        isSuperUser,
                    },
                },
            ],
        });
    }

    /**
     * 将格式化后的模板数据存到 store 中
     */
    public static updateCanvasMap(params: {
        canvas: ICanvas;
        work: IWork;
        pageAttr: IPageAttr;
        picId: string;
        lastTemplId: string;
        preview: string[];
        newTemplate?: boolean;
        rt_canvas_render_mode?: '' | 'pull' | 'board';
    }): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'UPDATE_CANVAS_MAP',
            params: [
                {
                    type: 'updateCanvasMap',
                    params,
                },
                {
                    type: 'resetAssetIndex',
                    params: {
                        allPages: true
                    },
                },
            ],
        });
    }

    /**
     * 删除所有 group 元素
     */
    public static delGroupInfoAll(): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'DEL_GROUP_INFO_ALL',
            params: [
                {
                    type: 'delGroupInfoAll',
                    params: {},
                },
            ],
        });
    }

    /**
     * 将格式化后的模板数据存到 store 中
     */
    public static updateUserid(params: IUserInfo): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'UPDATE_USERID',
            params: [
                {
                    type: 'updateUserid',
                    params,
                },
            ],
        });
    }
     /**
    * 将用户ai点数存到 store 中
    */
   public static updateUserAIInfo(params: IUserAIInfo): void {
       storeAdapter.dispatch({
           store_name: storeAdapter.store_names.paintOnCanvas,
           fun_name: 'UPDATE_USER_AI_INFO',
           params: [
               {
                   type: 'updateUserAiInfo',
                   params,
               },
           ],
       });
   }
   public static updateUserUploadInfo(params: IUserUploadLimitInfo): void {
    storeAdapter.dispatch({
        store_name: storeAdapter.store_names.paintOnCanvas,
        fun_name: 'UPDATE_USER_UPLOAD_INFO',
        params: [
            {
                type: 'updateUserUploadInfo',
                params,
            },
        ],
    });
}
    /**
     * 复制元素
     */
    public static copyAssets(copyText = ''): void {
        const { canvas, work, pageInfo, toolPanel, user, copyAssetUniqueTimeStr, editorTag } = storeAdapter.getStore<
            typeof storeAdapter.store_names.paintOnCanvas
        >({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const { info } = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
            store_name: storeAdapter.store_names.InfoManage,
        });
        if(toolPanel.asset?.meta.locked){
            return;
        }
        if (typeof toolPanel.asset_index !== 'number' && toolPanel.assets_index.length === 0) {
            return;
        }
        const time = new Date().getTime();
        let assetList: {
            assets?: IAsset[];
            assetsInfo?: IAssetInfo;
            asset?: IAsset;
            assetsGroup?: IAsset[];
            userId?: string;
            picId?: string;
            createTime?: number;
            canvasTime?: number;
        };
        if (toolPanel.assets_index.length > 0) {
            const assetsInfo = cloneDeep(toolPanel.assetsInfo);
            assetsInfo.posX /= canvas.scale;
            assetsInfo.posY /= canvas.scale;
            assetsInfo.height /= canvas.scale;
            assetsInfo.width /= canvas.scale;
            const groupName: string[] = [];
            const assets = AssetHelper.findAll(
                work,
                pageInfo,
                toolPanel.assets_index.map((i) => {
                    return {
                        index: i,
                    };
                }),
            );
            assets.forEach((a) => {
                if (a?.meta.type === 'group') {
                    groupName.push(a.meta.group);
                }
            });
            assetList = {
                assets: [
                    ...assets.filter(item=>!item.meta.group || item.meta.type === 'group'),
                    ...work.pages[pageInfo.pageNow].assets.filter(
                        (item) => item.meta.type !== 'group' && groupName.includes(item.meta.group),
                    ),
                ],
                assetsInfo: assetsInfo,
                userId: user.userId,
                picId: undefined, // info.id, // TODO 传入正确值会导致其他编辑器粘贴时候进入错误逻辑，因为其他编辑器逻辑之前没写对
                createTime: time,
                canvasTime: copyAssetUniqueTimeStr,
            };
        } else if (typeof toolPanel.asset_index === 'number' && toolPanel.asset_index >= 0) {
            const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
            if (asset.meta.type === 'group') {
                // group 元素
                const assetsGroup: IAsset[] = [];
                work.pages[pageInfo.pageNow].assets.forEach((a) => {
                    if (a.meta.type !== 'group' && a.meta.group === asset.meta.group) {
                        const newAsset = cloneDeep(a);
                        newAsset.meta.group = '';
                        assetsGroup.push(newAsset);
                    }
                });
                assetList = {
                    asset,
                    assetsGroup,
                };
            } else {
                const dom = document.querySelector(`.${asset.meta.className}`);
                const newAsset = cloneDeep(asset);
                if (asset.meta.type === 'text' && copyText && copyText !== '') {
                    newAsset.attribute.text = copyText.split('\n')
                }
                // const newAsset = cloneDeep(asset);
                if (dom && !asset.attribute.container) {
                    asset.attribute.width = dom.clientWidth / canvas.scale;
                    asset.attribute.height = dom.clientHeight / canvas.scale;
                }
                if (newAsset.meta.group) {
                    delete newAsset.meta.group;
                }
                assetList = {
                    asset: newAsset,
                };
            }
            assetList = {
                ...assetList,
                userId: user.userId,
                picId: undefined, // info.id, // TODO 传入正确值会导致其他编辑器粘贴时候进入错误逻辑，因为其他编辑器逻辑之前没写对
                createTime: time,
                canvasTime: copyAssetUniqueTimeStr,
            };
        }
        const copyData = {
            ...assetList,
            editorTag: editorTag,
        };
        localStorage.setItem('ue_copyAsset', JSON.stringify(copyData));
        copyAssets(JSON.stringify(copyData));
    }

    /**
     * 剪切元素
     */
    public static cutAsset(): void {
        this.copyAssets();
        const { toolPanel, work, pageInfo, } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        let list :{index: number}[]= [];
        if (toolPanel.assets_index.length > 0) {
            list = toolPanel.assets_index.map((i) => ({ index: i }))
        } else if (typeof toolPanel.asset_index === 'number' && toolPanel.asset_index >= 0) {
            list.push({ index: toolPanel.asset_index })
            const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
            if (asset.meta.type === 'group') {
                // group 元素
                work.pages[pageInfo.pageNow].assets.forEach((a, i) => {
                    if (a.meta.type !== 'group' && a.meta.group === asset.meta.group) {
                        list.push({
                            index: i
                        });
                    }
                });
            }
            
            if (list.length === 1 && asset?.meta.group) {
                GroupAndMultipleSelectLogic.updateGroupMembers(asset.meta.className, asset.meta.group, 'remove')
            }
        } else {
            list = [{ index: toolPanel.asset_index }]
        }

        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'CUT_ASSET',
            params: DeleteAsset.getDeleteActions({
                asset_index_list: list
            }),
        });
    }

    /** 复制元素逻辑 */
    public static pasteAsset(): void {

        // TODO 支持从剪切板读取数据
        
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'PASTE_ASSET',
            params: [
                {
                    type: 'pasteAsset',
                    params: {},
                },
                {
                    type: 'resetAssetIndex',
                    params: {},
                }
            ],
        });
        const { toolPanel, pageInfo, work } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if (toolPanel.assets_index?.length > 1) {
            const assets = work.pages[pageInfo.pageNow].assets;
            const paramArr = toolPanel.assets_index.map(index => {
                const item = assets[index];
                return  {
                    asset_index: index,
                    page_num: pageInfo.pageNow,
                    className: item.className
                }
            })
            SelectAsset.setSelectAssets(paramArr);
        } else if (toolPanel.asset_index >= 0) {
            const asset = work.pages[pageInfo.pageNow].assets.find((item) => item.meta.index === toolPanel.asset_index);
            if (asset && !asset.meta.locked) {
                SelectAsset.setSelectAssets([{
                    asset_index: toolPanel.asset_index,
                    page_num: pageInfo.pageNow,
                    className: asset.meta.className
                }])
            }
        }

    }

    /** 复制工具栏元素逻辑 */
    public static copyToolPanelAsset(): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'COPY_ASSET',
            params: [
                {
                    type: 'copyToolPanelAsset',
                    params: {},
                },
            ],
        });
    }

    /** 元素 css index 排序 */
    public static toolPanelSortAsset(params: {
        type: 'up' | 'upZ' | 'down' | 'downZ';
        indexMin?: number | undefined;
        indexMax?: number | undefined;
    }): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'MOVE_ASSET',
            params: [
                {
                    type: 'moveAssetIndex',
                    params,
                },
                {
                    type: 'resetAssetIndex',
                    params: {},
                },
            ],
        });
    }

    /** 元素 css index 排序 */
    public static toolPanelSortAssetSlider(params: { assetArr: { index: number }[] }): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'MOVE_ASSET_NEW',
            params: [
                {
                    type: 'moveAssetIndexNew',
                    params,
                },
                {
                    type: 'resetAssetIndex',
                    params: {},
                },
            ],
        });
    }

    /** 重置元素图层数值 */
    public static resetAssetIndex(): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'RESET_ASSET',
            params: [
                {
                    type: 'resetAssetIndex',
                    params: {},
                },
            ],
        });
    }

        /**
     * 设置为 文档编辑器设计师
     */
    public static updateDocDesigner(isDocDesigner: boolean): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'UPDATE_DOCDESIGNER',
            params: [
                {
                    type: 'updateDocDesigner',
                    params: {
                        isDocDesigner,
                    },
                },
            ],
        });
    }
        /**
     * 更新编辑器是否是设计师编辑器
     */
    public static async updateNewTemplate(newTemplate: boolean): Promise<void> {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'UPDATE_NEW_TEMPLATE',
            params: [
                {
                    type: 'updateNewTemplate',
                    params: {
                        newTemplate,
                    },
                },
            ],
        });
    }
}
