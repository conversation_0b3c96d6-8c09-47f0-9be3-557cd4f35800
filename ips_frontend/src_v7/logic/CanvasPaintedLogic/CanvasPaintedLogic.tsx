import { UpdateCanvas } from '../CanvasLogic';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { klona as cloneDeep } from 'klona';
import {
    IAsset,
    IAttribute,
    ICanvas,
    ICanvasPainted,
    IColor,
    IEffectVariant,
    IMutiSizeInfo,
    IPageAnimation,
    IPageAttr,
    ITeamUserAsset,
    IUserAsset,
    IWork,
} from '../Interface';
import { AssetLogic, UpdateAsset } from '../AssetLogic';
import { assetManager } from '@component/AssetManager';
import { tempIdBuilder } from '@src/userComponentV6.0/tool/template';
import { IPSConfig } from '@v7_utils/IPSConfig';
import { IPaintOnCanvasState } from '@v7_store/redux/reducers/onCanvasPaintedReducer';
import { emitter } from '@src/userComponentV6.0/Emitter';
import { ESelectType } from '../Enum';
import { AudioLogic } from '@v7_logic/AudioLogic';
import { RedoAndUndo } from '@v7_store/logic/onCanvasPaintedReducer';
import { IGenerateImages } from '@src/userComponentV6.0/toolV6.0/GeneratePicPopup/type'
import { DeletedRecoveryLogic } from '@v7_logic/DeletedRecoveryLogic';
import { GroupAndMultipleSelectLogic } from '@v7_logic/GroupAndMultipleSelectLogic';
import { IStyleItemInfo } from '@v7_render/CanvasStylePanel/StyleItem/StyleItem';
import { InfoManageHelper , RelineHelper} from '@v7_logic/StoreLogic';


export class CanvasPaintedLogic {
    static afterPageTimeUpdateTimer: number;

    public static updateUserAssetList(params: { assetList: IUserAsset[]; page?: number }): void {
        const { assetList, page } = params;
        const targetsCanvas = {
            userAssetList: assetList,
        } as Partial<IPaintOnCanvasState>;

        if (page) {
            targetsCanvas.userAssetListPage = page;
        }

        UpdateCanvas.updateCanvas('UPDATE_USERASSETLIST', targetsCanvas);
    }

    public static updateTeamUserAssetList(params: { assetList: ITeamUserAsset[]; page?: number }): void {
        const { assetList, page } = params;

        const targetsCanvas = {
            userTeamAssetsList: assetList,
        } as Partial<IPaintOnCanvasState>;

        if (page) {
            targetsCanvas.userTeamAssetsListPage = page;
        }

        UpdateCanvas.updateCanvas('UPDATE_TEAM_USERASSETLIST', targetsCanvas);
    }

    /* 设置编辑器标识tag */
    public static setEditor(params: { editorTag: string }): void {
        const { editorTag } = params;
        const targetsCanvas = {
            editorTag,
        };

        UpdateCanvas.updateCanvas('SET_EDITOR_TAG', targetsCanvas);
    }

    public static updateCanvasInfoIsReMake(params: { isRemake: number; fun_name?: string }): void {
        const { isRemake } = params;

        const { canvasInfo } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const newCanvasInfo = cloneDeep(canvasInfo);

        Object.assign(newCanvasInfo, {
            isRemake,
        });

        const targetsCanvas = {
            canvasInfo: newCanvasInfo,
        };

        UpdateCanvas.updateCanvas('UPDATE_CANVAS_INFO_ISREMAKE', targetsCanvas);
    }

    public static updateJobid(params: { jobId: string }): void {
        const { jobId } = params;
        const targetsCanvas = [
            {
                changes: {
                    jobId,
                },
            },
        ];

        UpdateCanvas.updateCanvas('UPDATE_JOBID', targetsCanvas);
    }

    public static updateSuccessImgPath(params: { successImgPath: string }): void {
        const { successImgPath } = params;
        const targetsCanvas = {
            successImgPath,
        };

        UpdateCanvas.updateCanvas('UPDATE_SUCCESSIMGPATH', targetsCanvas);
    }

    public static updateSpecificWordList(params: { specificWordList: string }): void {
        const { specificWordList } = params;
        const targetsCanvas = {
            specificWordList,
        };

        UpdateCanvas.updateCanvas('UPDATE_SPECIFICWORDLIST', targetsCanvas);
    }

    public static selectCurrentNav(params: { rt_currentNav: string }): void {
        const { rt_currentNav } = params;
        const targetsCanvas = {
            rt_currentNav,
        };
        UpdateCanvas.updateCanvas('SELECT_CURRENT_NAV', targetsCanvas);
    }

    public static sendPicIdNew(params: { picId: string; fun_name?: string }): void {
        const { picId, fun_name = 'SEND_PICID_NEW' } = params;
        const targetsCanvas = {
            picId,
        };

        UpdateCanvas.updateCanvas(fun_name, targetsCanvas);
    }

    public static updateRtUserCookieTag(params: Record<string, never>): void {
        const cookieName = 'ui_818ps';
        let cookieValue = '';
        let arr;
        const reg = new RegExp('(^| )' + cookieName + '=([^;]*)(;|$)');

        if ((arr = document.cookie.match(reg))) {
            cookieValue = unescape(arr[2]);
        } else {
            cookieValue = undefined;
        }
        const targetsCanvas = {
            rt_base64_cookie_tag: encodeURIComponent(cookieValue),
        };

        UpdateCanvas.updateCanvas('UPDATE_RT_USER_COOKIE_TAG', targetsCanvas);
    }

    public static updateTempDownloadInterval(params: { tempDownloadInterval: string; fun_name?: string }): void {
        const { tempDownloadInterval } = params;
        const targetsCanvas = {
            tempDownloadInterval,
        };

        UpdateCanvas.updateCanvas('UPDATE_TEMPDOWNLOADINTERVAL', targetsCanvas);
    }

    public static updateIsDownloadFlag(params: { isDownloadFlag: number }): void {
        const { isDownloadFlag } = params;
        const targetsCanvas = {
            isDownloadFlag,
        };

        UpdateCanvas.updateCanvas('UPDATE_ISDOWNLOADFLAG', targetsCanvas);
    }

    /*更新下载开始时间*/
    public static updateStartDownloadTime(params: { startDownloadTime?: string }): void {
        const { startDownloadTime = new Date().getTime() } = params;
        const targetsCanvas = {
            startDownloadTime,
        };

        UpdateCanvas.updateCanvas('UPDATE_STARTDOWNLOADTIME', targetsCanvas);
    }

    /*更新容器编辑状态*/
    public static updateIsContainerEditor(params: {
        isNotDo?: boolean;
        isContainerEditor?: boolean;
        assetIndex?: number;
        className?: string;
        pageIndex?: number;
    }): void {
        const { isContainerEditor, assetIndex, pageIndex, className, isNotDo } = params;
        const { toolPanel, containerEditorBak } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if (isNotDo) {
            const newContainerEditorBak = cloneDeep(containerEditorBak);
            const targetsAsset = [
                {
                    index: assetIndex ? assetIndex : toolPanel.asset_index,
                    className: className ? className : toolPanel.asset.meta.className,
                    pageIndex,
                    changes: {
                        attribute: {
                            contentInfo: newContainerEditorBak,
                        },
                    },
                },
            ];
            UpdateAsset.updateAssets('UPDATE_STARTDOWNLOADTIME', targetsAsset);
        }

        if (isContainerEditor === true) {
            const targetsCanvas = {
                containerEditorBak: cloneDeep(toolPanel.asset.attribute.contentInfo),
            };
            UpdateCanvas.updateCanvas('UPDATE_STARTDOWNLOADTIME', targetsCanvas);
        } else {
            const targetsCanvas = {
                containerEditorBak: '',
            };

            UpdateCanvas.updateCanvas('UPDATE_STARTDOWNLOADTIME', targetsCanvas);
        }
    }

    /*更新下载开始时间*/
    public static saveSubmitData(params: { templ_attr?: string; fourth_grade?: string; tag_grade?: string }): void {
        const { templ_attr, fourth_grade, tag_grade } = params;

        let changes: Partial<ICanvasPainted> = [];
        if (templ_attr) {
            changes = {
                templAttrData: templ_attr,
            };
        } else if (fourth_grade) {
            changes = {
                fourthGradeData: fourth_grade,
            };
        } else if (tag_grade) {
            changes = {
                tagGradeData: tag_grade,
            };
        }
        const targetsCanvas = { ...changes };
        UpdateCanvas.updateCanvas('SAVE_SUBMIT_DATA', targetsCanvas);
    }

    /*更新asset完成状态*/
    public static updateShotIsComplete(params: { pageIndex: number; asset: IAsset }): void {
        const { asset, pageIndex } = params;
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'UPDATE_SHOTISCOMPLETE',
            params: [
                {
                    type: 'updateShotIsComplete',
                    params: {
                        pageNum: pageIndex,
                        asset,
                    },
                },
            ],
        });
    }

    /** 多尺寸 是否是多尺寸模板 */
    public static mutisizeSetIsMutisizeStatus(params: { bool: boolean; mutiSizeInfo: IMutiSizeInfo[] }): void {
        const changes: {
            rt_isMutiSizeTemplate: boolean;
            rt_isMutiSizeInfo: IMutiSizeInfo[];
            rt_is_online_detail_page?: boolean;
        } = {
            rt_isMutiSizeTemplate: params.bool,
            rt_isMutiSizeInfo: params.mutiSizeInfo.map((v, i) => ({
                id: tempIdBuilder(v, i),
                infoIndex: i,
                ...v,
            })),
        };
        if (params.bool) {
            changes.rt_is_online_detail_page = false;
        }

        UpdateCanvas.updateCanvas('MUTISIZE_SET_IS_MUTISIZE_STATUS', changes);
    }

    /** 更改模板状态 */
    public static mutisizeSetCurrentStatusUpdate(params: { audit_through?: number; created?: boolean }): void {
        const { rt_isMutiSizeInfo, rt_mutisize_current_selected_tid } = storeAdapter.getStore<
            typeof storeAdapter.store_names.paintOnCanvas
        >({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const index = rt_isMutiSizeInfo.findIndex((v: any) => v.id === rt_mutisize_current_selected_tid);

        const isMutiSizeInfo = [...rt_isMutiSizeInfo];
        isMutiSizeInfo.splice(index, 1, {
            ...rt_isMutiSizeInfo[index],
            ...params,
        });

        UpdateCanvas.updateCanvas('MUTISIZE_SET_CURRENT_STATUS_UPDATE', {
            rt_isMutiSizeInfo: isMutiSizeInfo,
        });
    }

    public static mutisizeSaveSubTemplateInfo(
        params: {
            full_info: {
                canvas: ICanvas;
                work: IWork;
                pageAttr: IPageAttr;
                preview: string;
            };
        }[],
    ): void {
        UpdateCanvas.updateCanvas('MUTISIZE_SAVE_SUBTEMPLATE_INFO', {
            rt_mutisize_subtemplates: params,
        });
    }

    /** 多尺寸 用户是否显示 */
    public static mutisizeUserShowSizeList(params: { bool: boolean } = { bool: true }): void {
        UpdateCanvas.updateCanvas('MUTISIZE_USER_SHOW_SIZELIST', {
            rt_mutisize_user_show: params.bool,
        });
    }

    /** 多尺寸 保存当前多尺寸子模板信息 */
    public static mutisizeSaveCurrentSizeData(params: { key: string } = { key: '' }): void {
        const { rt_mutisize_subtemplates, rt_mutisize_current_selected_tid, canvas, work, pageAttr, preview } =
            storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
        const urlProps = IPSConfig.getProps();
        const tid = rt_mutisize_current_selected_tid || urlProps['picId'];
        const subTemplates = cloneDeep(rt_mutisize_subtemplates);
        subTemplates.some((v: any, i: number) => {
            if (tid === tempIdBuilder(v, i)) {
                v.full_info = {
                    canvas,
                    work,
                    pageAttr,
                    preview,
                };
                return true;
            }
        });
        UpdateCanvas.updateCanvas('MUTISIZE_SAVE_CURRENT_SIZE_DATA', {
            rt_mutisize_current_selected_tid: params.key,
            rt_mutisize_subtemplates: subTemplates,
        });
    }

    /** 多尺寸 */
    public static mutisizeSetCurrentSelectedTid(params: { tid: string; kid_1: number; kid_2: number }): void {
        const { rt_mutisize_current_selected_kidInfo, rt_mutisize_subtemplates } = storeAdapter.getStore<
            typeof storeAdapter.store_names.paintOnCanvas
        >({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        let changes;
        for (let i = 0, len = rt_mutisize_subtemplates.length; i < len; ++i) {
            if (params.tid === tempIdBuilder(rt_mutisize_subtemplates[i], i)) {
                const _fullInfo = rt_mutisize_subtemplates[i].full_info;
                changes = {
                    canvas: _fullInfo.canvas,
                    work: _fullInfo.work,
                    pageAttr: _fullInfo.pageAttr,
                    preview: _fullInfo.preview,
                };
                break;
            }
        }
        UpdateCanvas.updateCanvas('MUTISIZE_SET_CURRENT_SELECTED_TID', {
            rt_mutisize_current_selected_tid: params.tid,
            rt_mutisize_current_selected_kidInfo: {
                kid_1: params.kid_1 || rt_mutisize_current_selected_kidInfo.kid_1,
                kid_2: params.kid_2 || rt_mutisize_current_selected_kidInfo.kid_2,
            },
            ...changes,
        });
    }

    /* 多尺寸 切换时清除状态 */
    public static mutisizeClearStatus(params: Record<string, never>): void {
        RedoAndUndo.reset()
        // const targetsCanvas = {
        //     // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //     // @ts-ignore
        //     past: [],
        //     // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //     // @ts-ignore
        //     future: [],
        // };
        // UpdateCanvas.updateCanvas('MUTISIZE_CLEAR_STATUS', targetsCanvas);
    }

    public static firstSaveRecorder(params: { templateId: string; templateName: string }): void {
        const { templateId, templateName } = params;
        const { rt_first_save_recorder } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        // 如果不存在关联模板tid,标识不存在关联模板，只有主模板
        // 如果id相等不需要重复赋值
        if (templateId === rt_first_save_recorder[templateName]) {
            return;
        }
        UpdateCanvas.updateCanvas('FIRST_SAVE_RECORDER', {
            rt_first_save_recorder: {
                // ...rt_first_save_recorder, // TODO 测试确认效果是否符合预期
                [templateName]: templateId,
            },
        });
    }

    /*aiPPT打标*/
    public static updatePageMark(params: {
        rt_page_ppt_mark: string;
        fun_name?: string;
    }): void {
        const { fun_name, rt_page_ppt_mark } = params;
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name,
            params: [
                {
                    type: 'updatePageMark',
                    params: {
                        rt_page_ppt_mark
                    },
                },
            ],
        });
    }

    // 全量更新pageAttr中的pageInfo
    public static updatePageAttrPageInfo(params: {
        pageInfo: Array<any>;
        fun_name?: string;
    }): void {
        const { fun_name, pageInfo } = params;
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name,
            params: [
                {
                    type: 'updatePageAttrPageInfo',
                    params: {
                        pageInfo,
                    },
                },
            ],
        });
    }

    /*修改画板背景颜色*/
    public static updateBackGroundColor(params: {
        backgroundColor: IColor;
        isOpacity?: boolean;
        styleInfo?:IStyleItemInfo;
        originBackgroundInfo?:{
            backgroundColor:IColor,
            isOpacity:boolean;
        };
        fun_name?: string;
    }): void {
        const { fun_name, backgroundColor, isOpacity, styleInfo, originBackgroundInfo } = params;
        const backgroundObj = CanvasPaintedLogic.deleteCanvasBackgroundImage();
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name,
            params: [
                {
                    type: 'updateBackgroundColor',
                    params: {
                        backgroundColor,
                        isOpacity,
                        styleInfo,
                        originBackgroundInfo
                    },
                },
                ...(backgroundObj !==undefined ? [backgroundObj] : [])
            ],
        });
    }

    /*修改画板背景图片*/
    public static updateBackImage(params: { id: number; sample: string; width: number; height: number }): void {
        const { id, sample, width, height } = params;
        const backgroundObj = CanvasPaintedLogic.deleteCanvasBackgroundImage();
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'UPDATE_BACKGROUNDIMAGE',
            params: [
                {
                    type: 'updateBackgroundImage',
                    params: {
                        id,
                        sample,
                        width,
                        height,
                    },
                },
                ...(backgroundObj !==undefined ? [backgroundObj] : [])
            ],
        });
    }

    /*更新编辑器类型*/
    public static updateIsEB(params: Record<string, never>): void {
        const targetsCanvas = {
            isEB: true,
        };
        UpdateCanvas.updateCanvas('UPDATE_ISEB', targetsCanvas);
    }

    public static setFloatingState(params: { floatState: 0 | 1 }): void {
        const { floatState } = params;
        const targetsCanvas = {
            float_state: floatState,
        };
        UpdateCanvas.updateCanvas('SET_FLOATING_STATE', targetsCanvas);
    }
    /*翻页（上一页）*/
    public static prePage(params: Record<string, never>): void {
        const { pageInfo } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        let pageNow = pageInfo.pageNow - 1;
        pageNow = pageNow >= 0 ? pageNow : 0;

        const targetsCanvas = {
            pageInfo: {
                pageNow,
            },
        };
        UpdateCanvas.updateCanvas('PRE_PAGE', targetsCanvas);
    }
    /*翻页（下一页）*/
    public static nextPage(params: Record<string, never>): void {
        const { pageInfo, work } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        let pageNow = pageInfo.pageNow + 1;
        pageNow = pageNow >= work.pages.length ? work.pages.length : pageNow;

        const targetsCanvas = {
            pageInfo: {
                pageNow,
            },
        };
        UpdateCanvas.updateCanvas('NEXT_PAGE', targetsCanvas);
    }

    //  选中页面
    public static selectPage(params: { pageNumber: number }): void {
        const { pageNumber } = params;
        const targetsCanvas = {
            pageInfo: {
                pageNow: pageNumber,
            },
        };
        UpdateCanvas.updateCanvas('SELECT_PAGE', targetsCanvas);
    }

    /*添加新页*/
    public static addPage(params: Record<string, never>): void {
        const { isDesigner, work } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        const newWork = cloneDeep(work);

        if (isDesigner) {
            if (work.pages.length >= 30) {
                return;
            }
        } else {
            if (work.pages.length >= 30) {
                return;
            }
        }

        newWork.pages.push({
            assets: [],
            backgroundColor: {
                r: 255,
                g: 255,
                b: 255,
                a: 1,
            },
        });

        const targetsCanvas = {
            work: newWork,
            pageInfo: {
                pageNow: newWork.pages.length - 1,
            },
        };
        // TODO 恢复调用时，请处理协作，并验证性能
        // UpdateCanvas.updateCanvas('ADD_PAGE', targetsCanvas);
    }

    /*复制页面*/
    public static copyPage(params: { pageNow: number }): void {
        const { pageNow } = params;

        const { isDesigner, canvas, pageInfo, work } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if (isDesigner) {
            if (work.pages.length >= 30) {
                return;
            }
        } else {
            if (work.pages.length >= 30) {
                return;
            }
        }
        const page = cloneDeep(work.pages[pageNow]);
        page.assets.map((item: IAsset, key: number) => {
            page.assets[key].meta.className = item.meta.className + '_' + new Date().getTime();
        });
        if (pageInfo.pageNow === 0 && canvas.backgroundColor && canvas.backgroundColor.r) {
            page.backgroundColor = cloneDeep(canvas.backgroundColor);
        }

        const newWork = cloneDeep(work);
        newWork.pages.push(page);

        const targetsCanvas = {
            work: newWork,
            pageInfo: {
                pageNow: newWork.pages.length - 1,
            },
        };
        // TODO 恢复调用时，请处理协作，并验证性能
        // UpdateCanvas.updateCanvas('COPY_PAGE', targetsCanvas);
    }

    /*删除页面*/
    public static delPage(params: { pageNow: number }): void {
        const { pageNow } = params;

        const { work } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        const newWork = cloneDeep(work);

        newWork.pages.splice(pageNow, 1);
        const newPageNow = pageNow - 1;

        const targetsCanvas = {
            work: newWork,
            pageInfo: {
                pageNow: newPageNow >= 0 ? newPageNow : 0,
            },
        };
        // TODO 恢复调用时，请处理协作，并验证性能
        // UpdateCanvas.updateCanvas('DEL_PAGE', targetsCanvas);
    }

    /*更新元素*/
    public static updateAssetInfo(params: {
        newPageNow?: number;
        info: { attribute: IAttribute };
        index: number;
        isRepalceAsset?: boolean;
        autoSave?: 0 | 1 | 2;
        unrecordable?: boolean
    }, updateAction = 'UPDATE_ASSET_INFO'): void {
        const { newPageNow, isRepalceAsset, index, info, autoSave, unrecordable } = params;

        const { work, pageInfo, isDesigner, isEffectTemplate } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        let page_now = pageInfo.pageNow;
        if (newPageNow >= 0) {
            page_now = newPageNow;
        }

        // 如果更新 resId 则重置 renderCa START
        if (info.attribute.resId) {
            Object.assign(info.attribute, {
                renderCa: undefined,
                renderCaId: undefined,
                renderCaType: undefined,
            });
        }
        const oldAsset = work.pages[page_now].assets[index]
        // 如果更新 resId 则重置 renderCa END
        const asset = cloneDeep(work.pages[page_now].assets[index]);
        if(!asset) return
        // 被删除的图片替换后更新删除状态
        if(asset.meta.type == 'image' && asset.meta.deleted){
            Object.assign(asset.meta, {
                deleted: 0,
            }); 
            DeletedRecoveryLogic.hideRecoveryToolBox()
        }
        if (isRepalceAsset) {
            Object.assign(asset.meta, {
                type: 'image',
                deleted: 0,
            });
            Object.assign(asset.attribute, info.attribute);
        } else {
            Object.assign(asset.attribute, info.attribute);
            const attribute = asset.attribute;
            if (attribute.renderCaId) {
                if (!isDesigner && attribute.renderCaType === 1) {
                    assetManager.deleteTemplateAssetCache(attribute.renderCaId);
                }
                attribute.renderCaId = undefined;
                attribute.renderCa = undefined;
                attribute.renderCaType = undefined;
            }
        }

        const attr = asset.attribute;
        if (attr.container) {
            Object.assign(asset.attribute.container, {
                // isEdit: true,
                cutPicUrl: info.attribute.picUrl,
            });
        }
        if (attr.container && attr.container.id) {
            attr.width = (parseFloat(attr.assetWidth as string) * attr.container.height) / parseFloat(attr.assetHeight as string);
            attr.height = attr.container.height;
            if (attr.width < attr.container.width) {
                attr.height = (attr.height * attr.container.width) / attr.width;
                attr.width = attr.container.width;
                Object.assign(asset.attribute.container, {
                    posX: 0,
                    posY: -(attr.height - attr.container.height) / 2,
                    rt_isReplace: true,
                });
            } else {
                Object.assign(asset.attribute.container, {
                    posX: -(attr.width - attr.container.width) / 2,
                    posY: 0,
                    rt_isReplace: true,
                });
            }
        }
        if (info.attribute.resId && info.attribute.picUrl && attr.container && attr.container.isEdit) {
            if (attr.width == attr.container.width) {
                Object.assign(asset.attribute, {
                    height: attr.height,
                    width: attr.width,
                });
                Object.assign(asset.attribute.container, {
                    posX: 0,
                    posY: -(attr.height - attr.container.height) / 2,
                    isEdit:false,
                    rt_isReplace: true,
                });
            } else {
                Object.assign(asset.attribute, {
                    height: attr.height,
                    width: attr.width,
                });
                Object.assign(asset.attribute.container, {
                    posX: -(attr.width - attr.container.width) / 2,
                    posY: 0,
                    isEdit:false,
                    rt_isReplace: true,
                });
            }
        }
        // 用户替换效果模板中容器图片，记录该图片元素，用于下次效果模板替换

        if(isEffectTemplate && !isDesigner){
             if(['pic','image','background'].includes(asset.meta.type) && (asset.attribute.container?.markedReplace || asset.meta.replaceForEffectTemplate == 0)){
                Object.assign(asset.meta, {
                    replaceForEffectTemplate: 1
                })
             }
        }
        UpdateAsset.updateAssets(updateAction, [{
            index,
            className: asset.meta.className,
            changes: {
                attribute: asset.attribute,
                meta: asset.meta,
            }
        }], autoSave, undefined, unrecordable)
        const isSizeChanged = asset.attribute.width !== oldAsset.attribute.width || asset.attribute.height !== oldAsset.attribute.height
        // 组内元素尺寸变化后更新组的尺寸
        // if(asset.meta.group && isSizeChanged){
        //     GroupAndMultipleSelectLogic.updateGroupBoundingRectAfterAssetChange(asset, asset.meta.group)
        // }
    }
    /**
     * 资源更新不保存 只用于用户视觉效果
     * 用途：加载资源的过渡状态
     * @param params 
     */
    public static updateAssetInfoForVision(params: {
        newPageNow?: number;
        info: { attribute: IAttribute };
        index: number;
        isRepalceAsset?: boolean;
        autoSave?: 0 | 1 | 2;
    }): void { 
        CanvasPaintedLogic.updateAssetInfo(params,'UPDATE_ASSET_INFO_VISION')
    }

    /*替换模板*/
    public static replaceCanvasMap(params: {
        canvas: ICanvas;
        work: IWork;
        pageAttr?: IPageAttr;
        picId: string;
        preview: string;
    }): void {
        const { info } = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
            store_name: storeAdapter.store_names.InfoManage,
        });
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'UPDATE_CANVAS_MAP_REPLACE',
            params: [
                {
                    type: 'selectAsset',
                    params: {
                        select_type: ESelectType['unset'],
                    },
                },
                {
                    type: 'replaceTemplate',
                    params: {
                        ...params,
                        templateId: info.id,
                    },
                },
                {
                    type: 'resetAssetIndex',
                    params: {},
                },
            ],
        });
    }

    /*使用空白模板*/
    public static editorBlankTempl(params: { width: number; height: number }): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'EDITOR_BLANK_TEMPL',
            params: [
                {
                    type: 'editorBlankTempl',
                    params,
                },
            ],
        });
    }

    public static updatePageMap(params: { page_map: Record<string, string> }): void {
      const { page_map } = params;
      storeAdapter.dispatch({
          store_name: storeAdapter.store_names.paintOnCanvas,
          fun_name: 'UPDATE_PAGE_MAP',
          params: [
              {
                  type: 'updatePageMap',
                  params: { page_map },
              },
          ],
      });
  }

    /*更新容器列表*/
    public static updateContainerList(params: { containerList: any[] }): void {
        const { containerList } = params;
        const targetsCanvas = {
            containerList: containerList,
        };
        UpdateCanvas.updateCanvas('UPDATE_CONTAINERLIST', targetsCanvas);
    }

    public static updateAssetName(params: { asset: IAsset }): void {
        const { asset } = params;
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'UPDATE_ASSET_NAME',
            params: [
                {
                    type: 'updateAssetName',
                    params: { ...asset },
                },
            ],
        });
    }

    public static mutiSizeSetDetailFloorHeight(params: { floorHeights: string[]; allCanvasHeight: number }): void {
        // const { floorHeights, allCanvasHeight } = params;
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'MUTISIZE_SET_DETAIL_FLOOR_HEIGHT',
            params: [
                {
                    type: 'updateSetDetailFloorHeight',
                    params,
                },
            ],
        });
    }

    /*更新画布中心点*/
    public static updateCommonCanvasCenter(): void {
        const canvasWrapperDOM = document.getElementsByClassName('canvasWrapper')[0] as HTMLDivElement;
        const canvasContentDOM = document.getElementsByClassName('canvasContent')[0] as HTMLDivElement;

        if (!canvasWrapperDOM || !canvasContentDOM) {
            return;
        }

        const targetsCanvas = {
            common: {
                canvasCPX: Math.floor(canvasWrapperDOM.offsetWidth / 2) - parseInt(canvasContentDOM.style.left),
                canvasCPY: Math.floor(canvasWrapperDOM.offsetHeight / 2) - parseInt(canvasContentDOM.style.top),
            },
        };
        UpdateCanvas.updateCanvas('UPDATE_COMMON_CANVAS_CENTER', targetsCanvas);
    }

    public static setfloorCutting(params: { rt_is_online_detail_page: boolean }): void {
        const { rt_is_online_detail_page } = params;
        const targetsCanvas = {
            rt_is_online_detail_page: rt_is_online_detail_page,
        };
        UpdateCanvas.updateCanvas('MUTISIZE_SET_IS_DETAIL_PAGE', targetsCanvas);
    }

    public static setDetailFloorStatus(params: {
        online_detail_single_floor_uploaded_ids: {
            id: string;
            isUpload: boolean;
        }[];
    }): void {
        const { online_detail_single_floor_uploaded_ids } = params;
        const targetsCanvas = {
            online_detail_single_floor_uploaded_ids: online_detail_single_floor_uploaded_ids,
        };
        UpdateCanvas.updateCanvas('MUTISIZE_SET_DETAIL_FLOOR_UPLOAD_STATUS', targetsCanvas);
    }

    public static setDetailFloorCurrentIndex(params: { currentFloorIndex: number; clickPos: string }): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'MUTISIZE_SET_DETAIL_FLOOR_CURRENTINDEX',
            params: [
                {
                    type: 'setDetailFloorCurrentIndex',
                    params,
                },
            ],
        });
    }

    public static setDetailFloorHoverCurrentIndex(params: { rt_online_current_hover_index: number }): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'MUTISIZE_SET_DETAIL_FLOOR_CURRENT_HOVER_INDEX',
            params: [
                {
                    type: 'setDetailFloorHoverCurrentIndex',
                    params,
                },
            ],
        });
    }

    public static addRightMenuEmpty(): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'MUTISIZE_RIGHTMENU_ITEM_ADD_EMPTY',
            params: [
                {
                    type: 'addRightMenuEmpty',
                    params: {},
                },
            ],
        });
    }

    public static dragPreviewChange(params: { dragFlagFloorIndex: number }): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'MUTISIZE_PREVIEW_DRAG_CHANGE_SEQUENCE',
            params: [
                {
                    type: 'previewDragChangeSequence',
                    params,
                },
            ],
        });
    }

    public static copyRightMenuEmpty(): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'MUTISIZE_RIGHTMENU_ITEM_COPY',
            params: [
                {
                    type: 'copyRightMenu',
                    params: {},
                },
            ],
        });
    }

    public static pasteRightMenuEmpty(): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'MUTISIZE_RIGHTMENU_ITEM_PASTE',
            params: [
                {
                    type: 'pasteRightMenu',
                    params: {},
                },
            ],
        });
    }

    public static delCutRightMenuEmpty(): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'MUTISIZE_RIGHTMENU_ITEM_DELETE',
            params: [
                {
                    type: 'delRightMenuEmpty',
                    params: {},
                },
                // {
                //     type: 'cutRightMenuEmpty',
                //     params: {},
                // }
            ],
        });
    }

    public static addNewCanvasMap(params: {
        picId?: string;
        canvas: ICanvas;
        work: IWork;
        preview?: string;
        pageAttr?: IPageAttr;
    }): void {
        const { info } = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
            store_name: storeAdapter.store_names.InfoManage,
        });
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'ADD_NEW_CANVAS_MAP',
            params: [
                {
                    type: 'addNewCanvasMap',
                    params: {
                        ...params,
                        templateId: info.id,
                    },
                },
            ],
        });
    }

    public static selectPageTemplate(params: { pageNumber: number }): void {
        const { pageInfo: nowPageInfo, pageAttr, rt_canvas_render_mode } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (nowPageInfo.pageNow === params.pageNumber) {
            return;
        }
        const data: any = {
            pageInfo: {
                pageNow: params.pageNumber,
            },
            rt_canvas_render_mode: pageAttr.pageInfo?.[params.pageNumber]?.type || rt_canvas_render_mode
        }
        if ('rt_canvas_render_mode' in params) {
            data.rt_canvas_render_mode = params.rt_canvas_render_mode;
        }
        UpdateCanvas.updateCanvas('SELECT_PAGE_TEMPLATE', data);
        const { work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        
        emitter.emit('changeTemplate', work.pages[pageInfo.pageNow]?.assets);
    }

    public static mutisizeSetFloorTemplateCurrentindex(params: {
        floorTemplateIndex: number;
        clickTem: 'canvas';
    }): void {
        const { toolPanel } = storeAdapter.getStore<typeof  storeAdapter.store_names.paintOnCanvas>({
            store_name:  storeAdapter.store_names.paintOnCanvas,
        });
        UpdateCanvas.updateCanvas('MUTISIZE_SET_FLOOR_TEMPLATE_CURRENTINDEX', {
            pageInfo: {
                rt_floor_template_current_index: params.floorTemplateIndex,
            },
            rt_page_click_floor_template_position: params.clickTem,
        });
        if (toolPanel.reline) {
            RelineHelper.blurReline();
        }
      
    }

    public static mutisizeRightmenuItemAddEmptyTemplate(pageNumber?: number): void {
        const { info } = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
            store_name: storeAdapter.store_names.InfoManage,
        });
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'MUTISIZE_RIGHTMENU_ITEM_ADD_EMPTY_TEMPLATE',
            params: [
                {
                    type: 'mutisizeRightmenuItemAddEmptyTemplate',
                    params: {
                        templateId: info.id,
                        pageNumber
                    },
                },
            ],
        });
    }

    public static mutisizeRightmenuItemCopyEmptyTemplate(params: { pageNow: number }): void {
        const { info } = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
            store_name: storeAdapter.store_names.InfoManage,
        });
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'MUTISIZE_RIGHTMENU_ITEM_COPY_EMPTY_TEMPLATE',
            params: [
                {
                    type: 'mutisizeRightmenuItemCopyEmptyTemplate',
                    params: {
                        ...params,
                        templateId: info.id,
                    },
                },
            ],
        });
    }

    public static mutisizeRightmenuItemDeleteEmptyTemplate(params: { pageNow: number }): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'MUTISIZE_RIGHTMENU_ITEM_DELETE_EMPTY_TEMPLATE',
            params: [
                {
                    type: 'mutisizeRightmenuItemDeleteEmptyTemplate',
                    params,
                },
            ],
        });
        const { page_map } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const { info } = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
            store_name: storeAdapter.store_names.InfoManage,
        });
        let hasLastTemplateId = false;
        if (info.last_templ_id) {
            for (const key in page_map) {
                if (page_map[key] === String(info.last_templ_id)) {
                    hasLastTemplateId = true;
                    break;
                }
            }
        }
        if (!hasLastTemplateId) {
            InfoManageHelper.updateInfo({ ...info, last_templ_id: page_map[0] });
        }
    }

    public static mutisizeRightmenuItemReplaceTemplate(params: { pageCount: number, assetIdx: number, image: IGenerateImages }): void {
        const { info } = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
            store_name: storeAdapter.store_names.InfoManage,
        });
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'MUTISIZE_RIGHTMENU_ITEM_REPLACE_TEMPLATE',
            params: [
                {
                    type: 'mutisizeRightmenuItemReplaceTemplate',
                    params: {
                        ...params,
                        templateId: info.id,
                    },
                },
            ],
        });
    }

    /**
     * 去除画板水印
     */
    public static removeWaterMask(): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'REMOVE_CANVAS_WATER_MASK',
            params: [
                {
                    type: 'updateWaterMask',
                    params: {
                        canvasWaterMaskFlag: false,
                    },
                },
            ],
        });
    }

    /**
     * 设置出血线
     * @param params
     */
    public static setBleedingLine(params: { bleedingLine: number | boolean }): void {
        UpdateCanvas.updateCanvas('UPDATE_BLEEDING_LINE', {
            bleedingLine: params.bleedingLine,
        });
    }

    /**
     *  判断是否是第一次加载
     */
    public static firstLoadCanvas(params: { rt_first_load_is_done: boolean }): void {
        const { rt_first_load_is_done } = params;
        const targetsCanvas = {
            rt_first_load_is_done: rt_first_load_is_done,
        };
        UpdateCanvas.updateCanvas('FIRST_LOAD_CANVAS', targetsCanvas);
    }

    /**
     * 设置当前页的页面动画
     * @param animationData - 页面动画
     * @param key - 动画分类 i | s | o
     */
    public static setPageAnimation(animationData: IPageAnimation, isPreview: boolean, key: 'i' | 's' | 'o') {
        const {
            pageAttr,
            pageInfo: { pageNow },
        } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const pageInfo = pageAttr.pageInfo ? cloneDeep(pageAttr.pageInfo) : [];
        if (!pageInfo[pageNow]) {
            pageInfo[pageNow] = {
                title: '',
                pageTime: 5000,
                k: {
                    i: {},
                    s: {},
                    o: {},
                },
                rt_previewAnimaton: {},
            };
        }
        if (!pageInfo[pageNow].k) {
            pageInfo[pageNow].k = {
                i: {},
                s: {},
                o: {},
            };
        }
        if (isPreview) {
            pageInfo[pageNow].rt_previewAnimaton = animationData ? cloneDeep(animationData) : {};
        } else {
            pageInfo[pageNow].k[key] = animationData ? cloneDeep(animationData) : {};
            pageInfo[pageNow].rt_previewAnimaton = {};
        }
        UpdateCanvas.updateCanvas(
            'UPDATE_PAGE_ANIMATION',
            {
                pageAttr: {
                    pageInfo,
                },
            },
            undefined,
            !isPreview,
        );
    }
    /**
     * 移除预览动画
     * @param pageIndex - 页面索引
     */
    public static removePageAnimationPreview(pageIndex: number) {
        const { pageAttr } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        let pageInfo = pageAttr.pageInfo || [];
        if (pageInfo[pageIndex]?.rt_previewAnimaton) {
            pageInfo = cloneDeep(pageInfo);
            pageInfo[pageIndex].rt_previewAnimaton = undefined;
            UpdateCanvas.updateCanvas('REMOVE_PAGE_ANIMATION_PREVIEW', {
                pageAttr: {
                    pageInfo,
                },
            });
        }
    }

    /**
     *  记录动画每一帧 
     *  @param rt_animateFrame - 总帧数
     *  @param pageIndex - 当前预览页
     */
    public static saveAnimateFrame(rt_animateFrame: number, pageIndex?: number, playPageTime?: number):void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'SAVE_ANIMATE_FRAME',
            params: [
                {
                    type: 'saveAnimateFrame',
                    params: {
                        rt_animateFrame,
                        pageIndex,
                        playPageTime
                    },
                },
            ],
        });
    }

    public static prevewAnimationFrame(rt_previewFrame: number): void {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'SAVE_PREVIEW_ANIMATE_FRAME',
            params: [
                {
                    type: 'prevewAnimationFrame',
                    params: {
                        rt_previewFrame,
                    },
                },
            ],
        });
    }

    public static updateHoverPreview(params: {rt_hoverPreview: boolean}): void {
        const { rt_hoverPreview } = params
        UpdateCanvas.updateCanvas('UPDATE_HOVER_PREVIEW', {
            rt_hoverPreview
        });
    }

    /**
     *  修改pageAttr中pageTime的时间
     */
    public static updateAnimationPageTime(params: {pageIndex: number, pageTime: number}): void {
        const { pageAttr } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const { pageIndex, pageTime } = params
        let pageInfo = pageAttr.pageInfo || [];
        pageInfo = cloneDeep(pageInfo);
        pageInfo[pageIndex].pageTime = pageTime * 1000;
        pageInfo[pageIndex].rt_isUserUpdatedTime = true;
        UpdateCanvas.updateCanvas('UPDATE_AINMATION_PAGETIME', {
            pageAttr: {
                pageInfo
            },
        });
        if (CanvasPaintedLogic.afterPageTimeUpdateTimer) {
            clearTimeout(CanvasPaintedLogic.afterPageTimeUpdateTimer);
        }
        CanvasPaintedLogic.afterPageTimeUpdateTimer = window.setTimeout(() => {
            AudioLogic.onPageTimeChange();
            CanvasPaintedLogic.onPageTimeUpdatedVideoESliceUpdate();
        }, 1000);
    }

    /** 
     * 当有 videoe 元素发生变化时对应页面的页面时间
     */
    public static updatePageTimeOnUpdateVideoEAsset() {
        const { work, pageInfo: { pageNow }, pageAttr } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        })
        const pageInfo = pageAttr.pageInfo ? cloneDeep(pageAttr.pageInfo) : [];
        const len = work.pages.length
        if (pageInfo.length !== len) {
            for (let i = 0; i < len;i ++) {
                if (!pageInfo[i]) {
                    pageInfo[i] = {
                        pageTime: 5000,
                    }
                }
                if (pageInfo[i] && !pageInfo[i].pageTime) {
                    pageInfo[i].pageTime = 5000;
                }
            }
        }
        const currentPageAssets = work.pages[pageNow].assets
        let maxTime = 0;
        currentPageAssets.forEach(asset => {
            if (asset.meta.type === 'videoE') {
                maxTime = Math.max(maxTime, asset.attribute.cet - asset.attribute.cst)
            }
        })
        pageInfo[pageNow].pageTime = maxTime;
        UpdateCanvas.updateCanvas(
            'UPDATE_PAGETIME_ON_UPDATE_VIDEOE',
            {
                pageAttr: {
                    pageInfo,
                },
            },
            undefined,
            true,
        );
    }

    /**
     * 当页面时间更新时，在延迟操作中更新当前页的所有 videoe 元素的裁剪时间
     */
    public static onPageTimeUpdatedVideoESliceUpdate() {
        const {
            work,
            pageInfo: { pageNow },
            pageAttr,
        } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (pageAttr.pageInfo?.[pageNow].pageTime > 0) {
            const targets: Parameters<typeof UpdateAsset.updateAssets>[1] = [];
            work.pages[pageNow].assets.forEach((a, index) => {
                if (a.meta.type === 'videoE') {
                    const newCet = a.attribute.cst + pageAttr.pageInfo[pageNow].pageTime;
                    if (newCet < a.attribute.cet) {
                        targets.push({
                            index,
                            className: a.meta.className,
                            pageIndex: pageNow,
                            changes: {
                                attribute: {
                                    cet: newCet,
                                },
                            },
                        });
                    }
                }
            });
            targets.length > 0 && UpdateAsset.updateAssets('UPDATE_PAGE_ALL_VIDEOE_SLICE', targets);
        }
    }

    public static updateEditTextFocusStatus(params: { rt_isTextFocus: boolean; fun_name: string }):void {
        const { rt_isTextFocus, fun_name } = params
        UpdateCanvas.updateCanvas(fun_name, {rt_isTextFocus: rt_isTextFocus})
    }
    /** 
     * 更改page的title
     */
    public static updatePageTitle(params: {title: string, pageNumer: number}): void {
        const { pageAttr } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        })
        const pageInfo = pageAttr.pageInfo ? cloneDeep(pageAttr.pageInfo) : [];
        const { title, pageNumer } = params;
        if (!pageInfo[pageNumer]) {
            pageInfo[pageNumer] = {
                pageTime: 5000,
            }
        }
        pageInfo[pageNumer].title = title;
        UpdateCanvas.updateCanvas(
            'UPDATE_PAGETITLE',
            {
                pageAttr: {
                    pageInfo,
                },
            },
            undefined,
            true,
        );
    }
    /* 更改渲染模式 */ 
    public static updateCanvasRenderMode(params: { mode: '' | 'pull' | 'board'; pageAttr?: IPageAttr }):void {
        const { mode, pageAttr } = params;
        const data: {
            rt_canvas_render_mode: '' | 'pull' | 'board';
            pageAttr?: IPageAttr;
        } = {
            rt_canvas_render_mode: mode,
        }
        if (pageAttr) {
            data.pageAttr = pageAttr;
        }
        UpdateCanvas.updateCanvas('UPDATE_CANVAS_RENDER_MODE', data)
    }
    /* 更改编辑器内核类型 */
    public static updateEditorType(type: string):void { 
        UpdateCanvas.updateCanvas('UPDATE_EDITOR_RENDER_TYPE', {rt_editor_type: type})
    }
    public static updateCanvasLoadingStatus(status:boolean):void{
        UpdateCanvas.updateCanvas('UPDATE_CANVAS_LOADING_STATUS', {canvasLoadingStatus: status})
    }

    public static deleteCanvasBackgroundImage() {
        // 如果有背景图就删除背景图片
        const {work, pageInfo} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas
        });
        const pageNow = pageInfo.pageNow;
        const backgroundIndex = work.pages[pageNow].assets.findIndex((item: IAsset) => item.meta.type === 'background');
        if (backgroundIndex > -1) {
            return {
                type: 'deleteAsset',
                params: {
                    asset_index_list: [
                        {
                            index: backgroundIndex,
                            page_num: pageNow,
                        },
                    ],
                }
            }
        } 
    }
}
