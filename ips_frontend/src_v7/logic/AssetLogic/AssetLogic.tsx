import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import {
    IColor,
    IEffectVariant3D,
    IEffectVariant,
    IAsset,
    IRenderCache,
    IAttribute,
    IText,
    IUpdateTarget,
    IStroke,
} from '@v7_logic/Interface';
import { SelectAsset } from './SelectAsset';
import { DeleteAsset } from './DeleteAsset';
import { emitter } from '../../../src/userComponentV6.0/Emitter';
import { AssetTextLogic } from '@v7_logic/AssetTextLogic';
import { AssetImageLogic } from '@v7_logic/AssetImageLogic';
import { AssetSvgLogic } from '@v7_logic/AssetSvgLogic';
import { AssetHelper } from '@v7_logic/AssetHelper';
import { AssetTableLogic } from '@v7_logic/AssetTableLogic';
import { AssetChartLogic } from '@v7_logic/AssetChartLogic';
import { AssetCropLogic } from '@v7_logic/AssetCropLogic';
import { FavAssetLogic } from '@v7_logic/FavAssetLogic';
import { AssetDragLogic } from '@v7_logic/AssetDragLogic';
import { AssetGroupLogic } from '@v7_logic/AssetGroupLogic';
import { AssetLineLogic } from '@v7_logic/AssetLineLogic';
import { klona as cloneDeep } from 'klona';
// 待替换组件
import { assetManager } from '@component/AssetManager';
import { IFilters } from '../Interface/Asset';
import { UpdateAsset } from './UpdateAsset';

import { RichTextLogic } from '@v7_logic/RichTextLogic/RichTextLogic';
import { IAccumulationInAttribute } from '@v7_logic/AssetEChartLogic/type/type';
import { AssetFrameLogic } from '@v7_logic/AssetFrameLogic';
import { FrameClipUpdateInfo, FrameImageUpdateInfo } from '@v7_render/Asset/Frame/AssetFrame';
import { IAssetPostionEnum } from '@v7_logic/Enum';
import { canvasStore } from '@v7_store/redux/store';
import { GroupAndMultipleSelectLogic } from '@v7_logic/GroupAndMultipleSelectLogic';
import { TgsTypes } from '@tgs/types';

/**
 * 元素状态管理
 */
export class AssetLogic {
    static timerSendPointRecords: Record<string, number> = {};
    /**
     * 通过元素获取相关组合列表
     */
    public static getGroupListByAsset = (params: { asset_index: number; page_num?: number }): number[] => {
        const { pageInfo, work } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            }),
            { asset_index, page_num = pageInfo.pageNow } = params;
        const assets_index: number[] = [],
            wpassetsEs = work.pages[page_num].assets,
            select_asset = wpassetsEs[asset_index];

        for (let j = 0; j < wpassetsEs.length; j++) {
            if (wpassetsEs[j]?.meta.group === select_asset.meta.group) {
                assets_index.push(j);
            }
        }

        return assets_index;
    };

    /**
     * 删除单前选中元素
     */
    public static deleteSelectAsset(): boolean {
        return DeleteAsset.deleteSelectAsset({});
    }

    /**
     * 删除单前选中元素（快捷键）
     */
    public static deleteSelectAssetKeydown(): boolean {
        return DeleteAsset.deleteSelectAsset({
            origin: 'key',
        });
    }

    /**
     * 更新字体元素属性
     */
    public static updateAssetText(
        params: {
            assetIndex: number;
            attribute: string;
            key: string;
            value: string[] | IColor | number | string | boolean | TgsTypes.ITextEmphaticMark;
            oldText?: string[];
            fun_name: string;
            className?: string;
        },
        unrecordable = false,
    ): void {
        const { className } = params;
        let { assetIndex } = params;
        const { toolPanel, canvas, work, pageInfo } = storeAdapter.getStore<
            typeof storeAdapter.store_names.paintOnCanvas
        >({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        let asset = AssetHelper.find(work, pageInfo, { index: assetIndex, className });
        if (assetIndex === toolPanel.asset_index && asset?.meta.type === 'group' && asset.groupTextEditAsset) {
            assetIndex = AssetHelper.getIndex(work, pageInfo, { className: asset.groupTextEditAsset.meta.className });
            asset = AssetHelper.find(work, pageInfo, { index: assetIndex });
        }

        const { richText, activeIndex } = RichTextLogic.getSelection();
        if (asset.meta.type === 'SVG') {
            if (params.value === null) {
                return;
            }
            // @ts-ignore
            AssetSvgLogic.updateAssetSvgText({ ...params, richText, activeIndex }, unrecordable);
            return;
        }else {
            if (richText?.length > 0) {
                AssetTextLogic.updateAssetText({ ...params, v: 3, richText, activeIndex }, unrecordable);
                return;
            }
    
            AssetTextLogic.updateAssetText(params, unrecordable);
        }

    }

    /*获取选择选择字体的属性*/
    public static getSelectedTextProperty(key: any, value: any) {
        const { toolPanel } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (!toolPanel.asset) return value;
        const { activeIndex } = RichTextLogic.getSelection();
        if (toolPanel.asset.meta.v === 3 && activeIndex?.length) {
            return RichTextLogic.getSelectedTextProperty(key, value);
        } else if (toolPanel.asset.meta.v === 3) {
            return toolPanel.asset.attribute.text[0]?.[key] ?? value;
        } else if(toolPanel.asset.attribute.effect && toolPanel.asset.attribute.emphaticMarkBlocks && key == 'emphaticMark' && activeIndex?.length){
            return RichTextLogic.getSelectedTextProperty(key, value, true);
        } if (toolPanel.asset.meta.type === 'SVG' && toolPanel.asset.attribute.textAttr && activeIndex?.length) {
            const index = activeIndex[0] || 0;
            return toolPanel.asset.attribute.textAttr?.text?.[index]?.[key] ?? value;
        } else {
            return value;
        }
    }

    /*是否选择字体*/
    public static isSelectedText() {
        const { toolPanel } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (!toolPanel.asset) return false;
        const { activeIndex } = RichTextLogic.getSelection();
        if (toolPanel.asset.meta.v === 3 || activeIndex?.length) {
            return activeIndex?.length ? true : false;
        } else {
            return false;
        }
    }
    /*组元素内文字编辑*/
    public static updateGroupTextEditor(params: { assetIndex: number; asset: IAsset; fun_name: string }): void {
        AssetTextLogic.updateGroupTextEditor(params);
    }

    /* 组元素内容定位修改（文字修改）*/
    public static updateGroupContentPos(): void {
        AssetTextLogic.updateGroupContentPos();
    }

    /*设置字体颜色 end*/
    public static updateAssetFontColorEnd(params: {
        assetIndex: number;
        typeInfo: {
            type: string;
            typeItem: object;
        };
        className?: string;
        color: IColor;
        fun_name: string;
    }): void {
        AssetTextLogic.updateAssetFontColorEnd(params);
        emitter.emit('TextEditorUpdateTime');
    }

    /**
     * 图片滤镜
     */
    public static updateAssetFilter(params: {
        assetIndex: number;
        filterType: string;
        value: number;
        // asset: IAsset;
        fun_name: string;
        className?: string;
    }): void {
        AssetImageLogic.updateFilter({ ...params, fun_name: 'UPDATE_TOOLPANEL_IMAGE_FILTER' });
    }

    /**
     * 更新图片元素属性
     */
    public static updateAssetImage(params: {
        assetIndex: number;
        value: number;
        key: string;
        attribute: string;
        fun_name: string;
        isNew?: boolean;
    }): void {
        AssetImageLogic.updateAsset(params);
    }

    /**
     * 添加特效字
     */
    public static updateAssetSpecificWordEffect(params: {
        assetIndex: number;
        effect: string;
        className?: string;
    }): void {
        AssetTextLogic.addSpecificWordEffect({ ...params, fun_name: 'UPDATE_TEXT_SPECIFIC_WORD_EFFECT' });
    }

    /*特效字内容更新*/
    public static updateTextSpecificWordInfo(params: {
        propAsset: IAsset;
        effectVariant: IEffectVariant;
        assetIndex: number;
        effect?: string;
    }): void {
        AssetTextLogic.updateSpecificWordInfo({ ...params, fun_name: 'UPDATE_TEXT_SPECIFIC_WORD_INFO' });
    }

    /*特效字配色更新*/
    public static updateTextSpecificWordColorMatch(params: { effectColorMatch: string; assetIndex: number }): void {
        AssetTextLogic.updateSpecificWordColorMatch({ ...params, fun_name: 'UPDATE_TEXT_SPECIFIC_WORD_COLORMATCH' });
    }

    /*特效字配色修改*/
    public static updateTextSpecificWordLineArgradient(params: { payload: object; assetIndex: number }): void {
        AssetTextLogic.updateTextSpecificWordLineArgradient({
            ...params,
            fun_name: 'UPDATE_TEXT_SPECIFIC_WORD_LINEARGRADIENT',
        });
    }

    /**
     * 添加3D特效字
     */
    public static updateAsset3DSpecificWordEffect(params: {
        assetIndex: number;
        className?: string;
        effectVariant3D: IEffectVariant3D;
    }): void {
        AssetTextLogic.add3DSpecificWordEffect({ ...params, fun_name: 'UPDATE_TEXT_3D_WORD_EFFECT' });
    }

    // 更新 3D特效字 视角
    public static updateText3DFov(params: { fov: number }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }
        AssetTextLogic.updateText3DFov({
            ...params,
            asset: asset,
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_TEXT_3D_FOV',
        });
    }

    /**
     * 修改文本属性名
     */
    public static updateTextValueHint(params: { name: string }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }
        AssetTextLogic.updateValueHint({
            ...params,
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_VALUEHINT',
        });
    }

    /**
     * 修改文本高度
     */
    public static updateTextHeight(params: { height: number }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }
        AssetTextLogic.updateHeight({
            ...params,
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_HEIGHT',
        });
    }
    /* 设置颜色 */
    public static updateTextColorBactch(params: { color: IColor }): void {
        AssetTextLogic.updateColorBactch({
            ...params,
            fun_name: 'UPDATE_COLOR_BATCH',
        });
    }

    /* 特效字内容更新 */
    public static updateMutipleTextSpecificWorldInfo(params: {
        assets: IAsset[];
        effectVariant: IEffectVariant;
    }): void {
        AssetTextLogic.updateMutipleTextSpecificWorldInfo({
            ...params,
            fun_name: 'UPDATE_MUTIPLE_TEXT_SPECIFIC_WORD_INFO',
        });
    }

    /* 设置厚度 */
    public static update3DDepth(params: { depth: number }): void {
        AssetTextLogic.update3DDepth({
            ...params,
            fun_name: 'UPDATE_DEPTH',
        });
    }

    /* 修改 3D 字材质 */
    public static update3DMateral(params: { type: string; payload: { roughness?: number } }): void {
        AssetTextLogic.update3DMateral({
            ...params,
            fun_name: 'UPDATE_3D_MATERIAL',
        });
    }

    /* 修改 3D 字材质 */
    public static update3DBevel(params: { size: number; thickness: number; segments: number }): void {
        AssetTextLogic.update3DBevel({
            ...params,
            fun_name: 'UPDATE_3D_BEVEL',
        });
    }

    /*3D字内容更新*/
    public static update3DTextWord(params: { asset: IAsset }): void {
        AssetTextLogic.update3DTextWord({ ...params, fun_name: 'UPDATE_3D_TEXT_WORD_INFO' });
    }

    /*修改图层锁定*/
    public static updateLocked(params: { locked: boolean; pageIndex?: number }): void {
        AssetTextLogic.updateLocked({
            ...params,
        });
    }

    /*更新文字是否是标题*/
    public static updateTextIsTitle(params: {
        isTitle: number;
        assetIndex?: number;
        className?: string;
        pageIndex?: number;
    }): void {
        const { className, assetIndex } = params;
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }
        let aIndex = toolPanel.asset_index;
        let nowAsset;
        if (
            asset.meta &&
            asset.meta.type === 'group' &&
            asset.groupTextEditAsset &&
            asset.groupTextEditAsset.meta.type === 'text'
        ) {
            aIndex = AssetHelper.getIndex(work, pageInfo, { className: asset.groupTextEditAsset.meta.className });
            nowAsset = cloneDeep(AssetHelper.find(work, pageInfo, { index: aIndex }));
        } else {
            nowAsset = cloneDeep(asset);
        }
        AssetTextLogic.updateTextIsTitle({
            ...params,
            asset: nowAsset,
            assetIndex: assetIndex >= 0 ? assetIndex : aIndex,
            className: className ? className : nowAsset.meta.className,
            fun_name: 'UPDATE_TEXT_ISTITLE',
        });
    }

    /*特效字配色更新（新）*/
    public static updateTextSpecificWordColorMathNew(params: {
        index: number;
        changeColor: IColor;
        assetIndex?: number;
        className?: string;
        pageIndex?: number;
    }): void {
        const { className, assetIndex } = params;
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }
        let aIndex = toolPanel.asset_index;
        let nowAsset: IAsset;
        if (
            asset.meta &&
            asset.meta.type === 'group' &&
            asset.groupTextEditAsset &&
            asset.groupTextEditAsset.meta.type === 'text'
        ) {
            aIndex = AssetHelper.getIndex(work, pageInfo, { className: asset.groupTextEditAsset.meta.className });
            nowAsset = cloneDeep(AssetHelper.find(work, pageInfo, { index: aIndex }));
        } else {
            nowAsset = cloneDeep(asset);
        }

        AssetTextLogic.updateTextSpecificWordColorMathNew({
            ...params,
            asset: nowAsset,
            assetIndex: assetIndex ? assetIndex : aIndex,
            className: className ? className : nowAsset.meta.className,
            fun_name: 'UPDATE_TEXT_SPECIFIC_WORD_COLORMATCH_NEW',
        });
    }

    /*特效字配色更新（新）*/
    public static updateTextSpecificWordColorMathNewEnd(params: {
        index: number;
        color: IColor;
        pageIndex?: number;
    }): void {
        AssetTextLogic.updateTextSpecificWordColorMathNewEnd({
            ...params,
            fun_name: 'UPDATE_TEXT_SPECIFIC_WORD_COLORMATCH_NEW_END',
        });
    }

    /*更新特效字特效大小（修改）*/
    public static updateTextSpecificSizes(params: { proportion: number }): void {
        AssetTextLogic.updateTextSpecificSizes({ ...params, fun_name: 'UPDATE_TEXT_SPECIFIC_SIZES' });
    }

    /*特效字配色更新（新）（双色）*/
    public static updateTextSpecificWordColorMathDichroic(params: {
        colors: IColor[];
        assetIndex?: number;
        className?: string;
        pageIndex?: number;
    }): void {
        const { className, assetIndex } = params;
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }
        let aIndex = toolPanel.asset_index;
        let nowAsset;
        if (
            asset.meta &&
            asset.meta.type === 'group' &&
            asset.groupTextEditAsset &&
            asset.groupTextEditAsset.meta.type === 'text'
        ) {
            aIndex = AssetHelper.getIndex(work, pageInfo, { className: asset.groupTextEditAsset.meta.className });
            nowAsset = cloneDeep(AssetHelper.find(work, pageInfo, { index: aIndex }));
        } else {
            nowAsset = cloneDeep(asset);
        }

        AssetTextLogic.updateTextSpecificWordColorMathDichroic({
            ...params,
            asset: nowAsset,
            assetIndex: assetIndex ? assetIndex : aIndex,
            className: className ? className : nowAsset.meta.className,
            fun_name: 'UPDATE_TEXT_SPECIFIC_WORD_COLORMATCH_DICHROIC',
        });
    }

    public static updateTextSpecificWordLineargradientStyle(params: {
        assetIndex: number;
        type: string;
        position: number;
        color?: IColor;
        color_end?: IColor;
        angle?: number;
        fun_name: string;
    }): void {
        AssetTextLogic.updateTextSpecificWordLineargradientStyle({
            ...params,
            fun_name: 'UPDATE_TEXT_SPECIFIC_WORD_LINEARGRADIENTSTYLE',
        });
    }

    // 更新 3D特效字 视角
    public static updateText3DAspect(params: { aspect: number; assetIndex?: number; pageIndex?: number }): void {
        const { assetIndex } = params;
        const { toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        AssetTextLogic.updateText3DAspect({
            ...params,
            assetIndex: assetIndex >= 0 ? assetIndex : toolPanel.asset_index,
            // className: className ? className : toolPanel.asset.meta.className,
            fun_name: 'UPDATE_TEXT_3D_ASPECT',
        });
    }

    /**
     * 更新选中元素图片滤镜
     */
    public static updateAssetImageFilter(params: { filters: IFilters }): void {
        AssetImageLogic.updateSelectedImageFilter({ ...params, fun_name: 'UPDATE_IMAGE_FILTER' });
    }

    /**
     * 更新table
     */
    public static updateAssetTableStyle(params: { asset: IAsset; assetIndex: number }): void {
        AssetTableLogic.updateStyle({ ...params, fun_name: 'UPDATE_TABLE_STYLE' });
    }
    /**
     * 更新table中字体
     */
    public static updateAssetTableText(params: {
        tbKeys: string[];
        updateType: string;
        value: string | string[];
        fun_name: string;
    }): void {
        AssetTableLogic.updateText(params);
    }

    /**
     * 更新table中字体
     */
    public static updateAssetTableCell(params: {
        updateType: string;
        value: string | number;
        opacityBg?: number;
    }): void {
        AssetTableLogic.updateCell({ ...params, fun_name: 'UPDATE_TABLE_ALLCELLS_ATTRIBUTE' });
    }

    public static updateTableText(params: {
        assetClassName: string;
        assetIndex: number;
        row: number;
        col: number;
        value: string[];
        pageIndex?: number;
    }): void {
        AssetTableLogic.updateTextSpecial({
            ...params,
            fun_name: 'UPDATE_TABLETEXT',
        });
    }

    /* 更新表格指定行的高度 同时增加table的高度 */
    public static updateTableHeightAndRowHeight(params: {
        assetClassName: string;
        row: number;
        col: number;
        tableHeight: number;
        rowSize: number;
        assetIndex?: number;
        className?: string;
        pageIndex?: number;
    }): void {
        const { toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        AssetTableLogic.updateHeightAndRowHeight({
            ...params,
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_TABLE_HEIGHT_AND_ROWHEIGHT',
        });
    }

    /**
     * 更新图表元素
     */
    public static updateAssetChart(params: { assetIndex: number; colorTable: IColor[] }): void {
        AssetChartLogic.updateChart({ ...params, fun_name: 'UPDATE_CHART_COLORTABLE' });
    }

    public static updateAssetChartUserDataMap(params: { content: [][]; dataX: number; dataY: number }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }
        const userData = cloneDeep(asset.attribute.userData);

        AssetChartLogic.updateUserDataMap({
            ...params,
            assetIndex: toolPanel.asset_index,
            userData,
            fun_name: 'UPDATE_CHART_USERDATA_MAP',
        });
    }

    public static updateAssetChartUserData(params: { content: string; dataX: number; dataY: number }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        const userData = cloneDeep(asset.attribute.userData);

        AssetChartLogic.updateUserData({
            ...params,
            userData,
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_CHART_USERDATA',
        });
    }
    /*删除chart行*/
    public static deleteChartRow(params: { rowIndex: number }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }
        const userData = cloneDeep(asset.attribute.userData);
        AssetChartLogic.deleteRow({
            ...params,
            userData,
            assetIndex: toolPanel.asset_index,
            fun_name: 'DELETE_CHART_DETEIL_ROW',
        });
    }

    /*删除chart行*/
    public static deleteChartCol(params: { colIndex: number }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }
        const userData = cloneDeep(asset.attribute.userData);
        AssetChartLogic.deleteCol({
            ...params,
            userData,
            assetIndex: toolPanel.asset_index,
            fun_name: 'DELETE_CHART_DETEIL_COL',
        });
    }

    /*新增chart列*/
    public static addChartCol(params: { pos: number }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }
        const userData = cloneDeep(asset.attribute.userData);
        AssetChartLogic.addCol({
            ...params,
            userData,
            assetIndex: toolPanel.asset_index,
            fun_name: 'ADD_CHART_DETEIL_COL',
        });
    }

    /*新增chart列*/
    public static setChartDetailSeting(params: { pos: number; attribute: IAttribute }): void {
        const { attribute } = params;
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }
        AssetChartLogic.setDetailSeting({
            ...params,
            attribute,
            asset: cloneDeep(asset),
            assetIndex: toolPanel.asset_index,
            fun_name: 'ADD_CHART_DETEIL_COL',
        });
    }

    /* 改变table的高度 */
    public static updateTableOutterHeight(params: { height: number }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }
        AssetTableLogic.updateOutterHeight({
            ...params,
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_TABLE_OUTTER_HEIGHT',
        });
    }

    /* *******表格右键操作 ********** */
    public static updateTableRightMenuMerge(params: {
        minRow: number;
        maxRow: number;
        minCol: number;
        maxCol: number;
    }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }
        AssetTableLogic.rightMenuMerge({
            ...params,
            assetIndex: toolPanel.asset_index,
            cell: cloneDeep(asset.attribute.cell),
            fun_name: 'UPDATE_TABLERIGHTMENU_MERGE',
        });
    }

    public static updateTableRightMenuSplit(params: {
        minRow: number;
        maxRow: number;
        minCol: number;
        maxCol: number;
    }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }
        AssetTableLogic.rightMenuSplit({
            ...params,
            cell: asset.attribute.cell,
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_TABLERIGHTMENU_SPLIT',
        });
    }

    public static tableRightMenuInsertLeft(params: {
        minRow: number;
        maxRow: number;
        minCol: number;
        maxCol: number;
    }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const { maxCol } = params;
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        const cellSizeCol = asset.attribute.cellSize['col'];
        // 获取插入的预设宽度的比例
        const totalColRatio = cellSizeCol.reduce((old, now) => {
            return old + now;
        }, 0);
        const insertNewColRatio = cellSizeCol[maxCol] / totalColRatio;
        // 获取向左插入的预设宽度
        const insertNewColWidth = asset.attribute.width * insertNewColRatio;
        AssetTableLogic.rightMenuInsertLeft({
            ...params,
            cell: asset.attribute.cell,
            text: asset.attribute.text as IText[][],
            cellSize: asset.attribute.cellSize,
            assetIndex: toolPanel.asset_index,
            width: asset.attribute.width,
            insertNewColWidth,
            fun_name: 'UPDATE_TABLERIGHTMENU_INSERTLEFT',
        });
    }

    public static tableRightMenuInsertRight(params: {
        minRow: number;
        maxRow: number;
        minCol: number;
        maxCol: number;
    }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const { maxCol } = params;
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }
        const cellSizeCol = asset.attribute.cellSize['col'];
        // 获取插入的预设宽度的比例
        const totalColRatio = cellSizeCol.reduce((old, now) => {
            return old + now;
        }, 0);
        const insertNewColRatio = cellSizeCol[maxCol] / totalColRatio;
        // 获取向右插入的预设宽度
        const insertNewColWidth = asset.attribute.width * insertNewColRatio;
        AssetTableLogic.rightMenuInsertRight({
            ...params,
            cell: asset.attribute.cell,
            text: asset.attribute.text as IText[][],
            cellSize: asset.attribute.cellSize,
            assetIndex: toolPanel.asset_index,
            width: asset.attribute.width,
            insertNewColWidth,
            fun_name: 'UPDATE_TABLERIGHTMENU_INSERTRIGHT',
        });
    }

    public static tableRightMenuInsertTop(params: {
        minRow: number;
        maxRow: number;
        minCol: number;
        maxCol: number;
    }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const { maxRow } = params;
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        const cellSizeRow = asset.attribute.cellSize['row'];
        // 获取插入的预设行的比例
        const totalRowRatio = cellSizeRow.reduce((old, now) => {
            return old + now;
        }, 0);
        const insertNewRowRatio = cellSizeRow[maxRow] / totalRowRatio;
        // 获取向上插入的预设宽度
        const insertNewRowHeight = asset.attribute.height * insertNewRowRatio;
        AssetTableLogic.rightMenuInsertTop({
            ...params,
            cell: asset.attribute.cell,
            text: asset.attribute.text as IText[][],
            cellSize: asset.attribute.cellSize,
            assetIndex: toolPanel.asset_index,
            height: asset.attribute.height,
            insertNewRowHeight,
            fun_name: 'UPDATE_TABLERIGHTMENU_INSERTTOP',
        });
    }

    public static tableRightMenuInsertBottom(params: {
        minRow: number;
        maxRow: number;
        minCol: number;
        maxCol: number;
    }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const { maxRow } = params;
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        const cellSizeRow = asset.attribute.cellSize['row'];
        // 获取插入的预设行的比例
        const totalRowRatio = cellSizeRow.reduce((old, now) => {
            return old + now;
        }, 0);
        const insertNewRowRatio = cellSizeRow[maxRow] / totalRowRatio;
        // 获取向下插入的预设宽度
        const insertNewRowHeight = asset.attribute.height * insertNewRowRatio;
        AssetTableLogic.rightMenuInsertBottom({
            ...params,
            cell: asset.attribute.cell,
            text: asset.attribute.text as IText[][],
            cellSize: asset.attribute.cellSize,
            height: asset.attribute.height,
            insertNewRowHeight,
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_TABLERIGHTMENU_INSERTBOTTOM',
        });
    }

    public static tableRightMenuDeleteRow(params: {
        minRow: number;
        maxRow: number;
        minCol: number;
        maxCol: number;
    }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const { maxRow } = params;
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        const cellSizeRow = asset.attribute.cellSize['row'];
        // 获取删除的预设行的比例
        const totalRowRatio = cellSizeRow.reduce((old, now) => {
            return old + now;
        }, 0);
        const deleteRowRatio = cellSizeRow[maxRow] / totalRowRatio;
        // 获取删除的预设宽度
        const deleteRowHeight = asset.attribute.height * deleteRowRatio;
        AssetTableLogic.rightMenuDeleteRow({
            ...params,
            cell: asset.attribute.cell,
            text: asset.attribute.text as IText[][],
            cellSize: asset.attribute.cellSize,
            assetIndex: toolPanel.asset_index,
            height: asset.attribute.height,
            choosedTdKeys: asset.choosedTdKeys,
            deleteRowHeight,
            fun_name: 'UPDATE_TABLERIGHTMENU_DELETEROW',
        });
    }

    public static tableRightMenuDeleteCol(params: {
        minRow: number;
        maxRow: number;
        minCol: number;
        maxCol: number;
    }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const { maxCol } = params;
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        const cellSizeCol = asset.attribute.cellSize['col'];
        // 获取删除的预设行的比例
        const totalColRatio = cellSizeCol.reduce((old, now) => {
            return old + now;
        }, 0);
        const deleteColRatio = cellSizeCol[maxCol] / totalColRatio;
        // 获取删除的预设宽度
        const deleteColWidth = asset.attribute.width * deleteColRatio;

        AssetTableLogic.rightMenuDeleteCol({
            ...params,
            cell: asset.attribute.cell,
            text: asset.attribute.text as IText[][],
            cellSize: asset.attribute.cellSize,
            assetIndex: toolPanel.asset_index,
            width: asset.attribute.width,
            choosedTdKeys: asset.choosedTdKeys,
            deleteColWidth,
            fun_name: 'UPDATE_TABLERIGHTMENU_DELETECOL',
        });
    }

    public static tableClearSelectContents(params: { choosedTdKeys: string[] }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }
        AssetTableLogic.clearSelectContents({
            ...params,
            assetIndex: toolPanel.asset_index,
            text: asset.attribute.text as IText[][],
            fun_name: 'TABLE_CLEARSELECTCONTENTS',
        });
    }

    // 下面更新表格缩放的方式暂时未使用
    public static updateTableTdScale(params: {
        direction: string;
        pointIndex: number;
        value: number;
        tableClass: string;
        dynamicMinHeight?: number; // 添加动态最小高度参数
    }): void {
        AssetTableLogic.updateTdScale({
            ...params,
            fun_name: 'UPDATE_TABLE_TD_SCALE',
        });
    }

    public static updateTableScale(params: {
        direction: string;
        pointIndex: number;
        value: number;
        tableClass: string;
    }): void {
        AssetTableLogic.updateTableScale({
            ...params,
            fun_name: 'UPDATE_TABLE_SCALE',
        });
    }

    /* 设置表格选中的td key值 */
    public static updateTableSelectKey(params: { tdKeys: string[]; className?: string }): void {
        AssetTableLogic.updateSelectKey({
            ...params,
            fun_name: 'UPDATE_TABLE_SELECTED_KEY',
        });
    }

    /* 取消表格选中的td key值 */
    public static cancelTableSelectKey(params: { tdKeys: string[]; className?: string }): void {
        AssetTableLogic.updateSelectKey({
            ...params,
            fun_name: 'CANCEL_TABLE_SELECTED_KEY',
        });
    }

    /* 设置表格选中的td key值 */
    public static updateTableBorderColor(params: { tbKeys: string[]; color: IColor }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        AssetTableLogic.updateBorderColor({
            ...params,
            cell: asset.attribute.cell,
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_TABLEBORDER_COLOR',
        });
    }

    /* 设置表格背景颜色 */
    public static updateBgColor(params: { tbKeys: string[]; color: IColor; opacityBg?: boolean }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        AssetTableLogic.updateBgColor({
            ...params,
            cell: asset.attribute.cell,
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_TABLEBG_COLOR',
        });
    }

    /* 设置表格背景颜色 */
    public static updateTableTextColor(params: { tbKeys: string[]; color: IColor }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        AssetTableLogic.updateTextColor({
            ...params,
            text: asset.attribute.text as IText[][],
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_TABLETEXT_COLOR',
        });
    }

    /* 设置表格背景颜色 */
    public static updateTableTextOpacity(params: { tbKeys: string[]; opacity: number }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        AssetTableLogic.updateTextOpacity({
            ...params,
            text: asset.attribute.text as IText[][],
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_TABLETEXT_OPACITY',
        });
    }

    /*新增chart行*/
    public static addChartRow(params: {
        pos: number;
        assetIndex?: number;
        className?: string;
        pageIndex?: number;
    }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }
        const userData = cloneDeep(asset.attribute.userData);
        AssetChartLogic.addRow({
            ...params,
            userData,
            assetIndex: toolPanel.asset_index,
            fun_name: 'ADD_CHART_DETEIL_ROW',
        });
    }

    /**
     * 更新图表背景
     */
    public static updateAssetBgChart(params: { assetIndex: number; color: IColor }): void {
        AssetChartLogic.updateBg({ ...params, fun_name: 'SET_CHART_BG_COLOR' });
    }

    /**
     * 更新图表colorTable
     */
    public static updateAssetColorTable(params: { assetIndex: number; colorTable: IColor[] }): void {
        AssetChartLogic.updateAssetColorTable({ ...params, fun_name: 'UPDATE_CHART_ASSET_COLOR_TABLE' });
    }

    /* 更新图片缓存 */
    public static updateAssetImageRenderCache(params: { renderCache: IRenderCache; willUpdateAsset: IAsset }): void {
        const { willUpdateAsset } = params;
        const { work, pageInfo } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        AssetImageLogic.updateImageRender({
            ...params,
            assetIndex: work.pages[pageInfo.pageNow].assets.indexOf(willUpdateAsset),
            className: willUpdateAsset.meta.className,
            fun_name: 'UPDATE_IMAGE_RENDER_CACHE',
        });
    }

    /*编辑数据中确认修改*/
    public static updateAssetChartData(params: { userData: string[][] }): void {
        const { toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        AssetChartLogic.updateData({
            ...params,
            assetIndex: toolPanel.asset_index,
            fun_name: 'EDIT_CHART_MODAL_CONFIRM_ADJUST',
        });
    }

    /*更新图表类型*/
    public static updateAssetChartBaseId(params: { chartBaseId: number }): void {
        const { toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        AssetChartLogic.updateAssetChartBaseId({
            ...params,
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_CHART_BASE_ID',
        });
    }

    /*更新堆积*/
    public static updateAssetChartAccumulation(params: { accumulation: IAccumulationInAttribute }): void {
        const { toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        AssetChartLogic.updateAssetChartAccumulation({
            ...params,
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_CHART_ACCUMULATION',
        });
    }

    /*更新rt_url*/
    public static updateAssetChartRtUrl(params: { rt_url: string }): void {
        const { toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        AssetChartLogic.updateAssetChartRtUrl({
            ...params,
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_CHART_RT_URL',
        });
    }

    /**
     * 画布中旋转3d字
     */

    public static updateText3DRotation(params: {
        assetIndex: number;
        rotationX: number;
        rotationY: number;
        asset: IAsset;
    }): void {
        AssetDragLogic.updateText3DRotation({ ...params, fun_name: 'UPDATE_TEXT_3D_ROTATION' });
    }

    /**
     * 画布中旋转元素
     */
    public static updateAssetRotate(params: { rotate: number; assetsGrounp?: any; fun_name: string }): void {
        AssetDragLogic.updateAssetRotate(params);
    }

    /**
     * 画布中旋转元素状态更新
     */
    public static updateAssetRotateStatus(params: { rt_current_is_rotate_asset: boolean; fun_name: string }): void {
        AssetDragLogic.updateAssetRotateStatus(params);
    }

    /*向左旋转90度*/
    public static degrees90Left(params: {
        /*  */
    }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        let rotate = Math.floor(asset.transform.rotate / 90) - 1;
        rotate = rotate < 0 ? 3 : rotate;
        rotate = (rotate * 90) % 360;
        AssetDragLogic.updateAssetRotate({ rotate, fun_name: 'DEGREES_90_LEFT' });
    }

    /*向左旋转90度*/
    public static degrees90Right(params: {
        /*  */
    }): void {
        const { toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const rotate = ((Math.floor(toolPanel.asset.transform.rotate / 90) + 1) * 90) % 360;

        AssetDragLogic.updateAssetRotate({ rotate, fun_name: 'DEGREES_90_RIGHT' });
    }

    /*修改元素大小（拖动-左下边）（文本特殊版本）*/

    public static updateAssetDragPointText(params: {
        width: number;
        height: number;
        posX: number;
        posY: number;
        asset: IAsset;
        assetIndex: number;
        fontSize: number;
        fun_name: string;
    }): void {
        AssetDragLogic.updateDragPointText(params);
    }

    /*修改元素拖动状态*/

    public static updateAssetIsDrag(params: { is_drag: boolean; fun_name: string }): void {
        AssetDragLogic.updateDragStatus({ ...params, fun_name: 'UPDATE_ASSET_IS_DRAG' });
    }

    /*拖拽元素(组)*/
    public static dragAssetGroup(params: {
        assetsGrounp: Record<string, { posX: number; posY: number }>;
        fun_name: string;
    }): void {
        AssetDragLogic.dragAssetGroup({ ...params, fun_name: 'DRAP_ASSETSGROUNP' });
    }

    /*拖拽元素(多选)*/
    public static dragAssets(params: { assetsPos: { posX: number; posY: number }[]; fun_name: string }): void {
        AssetDragLogic.dragAssets({ ...params, fun_name: 'DRAP_ASSETS' });
    }

    /*拖动元素结束删除元素*/
    public static dragAssetEndDel(params: {
        parameter?: {
            /*  */
        };
        fun_name: string;
    }): void {
        AssetDragLogic.dragAssetEndDel({ ...params, fun_name: 'DRAP_ASSET_END_DEL' });
    }

    /*拖动元素结束*/
    public static dragAssetEnd(params: { fun_name: string; isChange?: boolean }): void {
        AssetDragLogic.dragAssetEnd({ ...params, fun_name: 'DRAP_ASSET_END' });
    }

    /*拖动元素*/
    public static dragAsset(params: { posX: number; posY: number; fun_name: string }): void {
        AssetDragLogic.dragAsset({ ...params, fun_name: 'DRAP_ASSET' });
    }

    /*修改元素大小（拖动-右下边）*/
    public static dragPointRightBottom(params: {
        height: number;
        width: number;
        posY: number;
        posX: number;
        rotate?: number;
        assetsGrounp?: {
            posX: number;
            posY: number;
            height: number;
            width: number;
            fontSize: number;
            text?: { [key: string]: string }[];
        }[];
        fun_name: string;
    }): void {
        let { rt_sendTableDragPointTimer } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (!(typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0)) {
            const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
            if (asset) {
                if (asset.meta.type === 'table') {
                    clearTimeout(rt_sendTableDragPointTimer);
                    rt_sendTableDragPointTimer = setTimeout(() => {
                        assetManager.setPv_new(2351, {
                            additional: {
                                so: 'RIGHT_BOTTOM',
                            },
                        });
                        clearTimeout(rt_sendTableDragPointTimer);
                    }, 500);
                }
            }
        }

        AssetDragLogic.dragCornerPoint({ ...params, fun_name: 'DRAG_POINT_RIGHT_BOTTOM' });
    }

    /*修改元素大小（拖动-右上边）*/
    public static dragPointRightTop(params: {
        height: number;
        width: number;
        posY: number;
        posX: number;
        rotate?: number;
        assetsGrounp?: {
            posX: number;
            posY: number;
            height: number;
            width: number;
            fontSize: number;
            text?: { [key: string]: string }[];
        }[];
        fun_name: string;
    }): void {
        let { rt_sendTableDragPointTimer } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (!(typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0)) {
            const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
            if (asset) {
                if (asset.meta.type === 'table') {
                    clearTimeout(rt_sendTableDragPointTimer);
                    rt_sendTableDragPointTimer = setTimeout(() => {
                        assetManager.setPv_new(2351, {
                            additional: {
                                so: 'RIGHT_TOP',
                            },
                        });
                        clearTimeout(rt_sendTableDragPointTimer);
                    }, 500);
                }
            }
        }

        AssetDragLogic.dragCornerPoint({ ...params, fun_name: 'DRAG_POINT_RIGHT_TOP' });
    }
    /*修改元素大小（拖动-左上边）*/
    public static dragPointLeftTop(params: {
        height: number;
        width: number;
        posY: number;
        posX: number;
        rotate?: number;
        assetsGrounp?: {
            posX: number;
            posY: number;
            height: number;
            width: number;
            fontSize: number;
            text?: { [key: string]: string }[];
        }[];
        fun_name: string;
    }): void {
        let { rt_sendTableDragPointTimer } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (!(typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0)) {
            const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
            if (asset) {
                if (asset.meta.type === 'table') {
                    clearTimeout(rt_sendTableDragPointTimer);
                    rt_sendTableDragPointTimer = setTimeout(() => {
                        assetManager.setPv_new(2351, {
                            additional: {
                                so: 'LEFT_TOP',
                            },
                        });
                        clearTimeout(rt_sendTableDragPointTimer);
                    }, 500);
                }
            }
        }

        AssetDragLogic.dragCornerPoint({ ...params, fun_name: 'DRAG_POINT_LEFT_TOP' });
    }

    /*修改元素大小（拖动-左下边）*/
    public static dragPointLeftBottom(params: {
        height: number;
        width: number;
        posY: number;
        posX: number;
        rotate?: number;
        assetsGrounp?: {
            posX: number;
            posY: number;
            height: number;
            width: number;
            fontSize: number;
            text?: { [key: string]: string }[];
        }[];
        fun_name: string;
    }): void {
        let { rt_sendTableDragPointTimer } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (!(typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0)) {
            const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
            if (asset) {
                if (asset.meta.type === 'table') {
                    clearTimeout(rt_sendTableDragPointTimer);
                    rt_sendTableDragPointTimer = setTimeout(() => {
                        assetManager.setPv_new(2351, {
                            additional: {
                                so: 'LEFT_BOTTOM',
                            },
                        });
                        clearTimeout(rt_sendTableDragPointTimer);
                    }, 500);
                }
            }
        }

        AssetDragLogic.dragCornerPoint({ ...params, fun_name: 'DRAG_POINT_LEFT_BOTTOM' });
    }

    /*修改元素大小（拖动-左边）*/
    public static dragPointLeft(params: {
        height: number;
        width: number;
        posY: number;
        posX: number;
        contentInfo: { width: number; height: number; posX: number; posY: number };
        fun_name: string;
    }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        let { rt_sendTableDragPointTimer } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if (asset.meta.type === 'table') {
            clearTimeout(rt_sendTableDragPointTimer);
            rt_sendTableDragPointTimer = setTimeout(() => {
                assetManager.setPv_new(2351, {
                    additional: {
                        so: 'LEFT',
                    },
                });
                clearTimeout(rt_sendTableDragPointTimer);
            }, 500);
        }
        AssetDragLogic.dragCenterPoint({ ...params, fun_name: 'DRAG_POINT_LEFT' });
    }

    /*修改元素大小（拖动-右边）*/
    public static dragPointRight(params: {
        height: number;
        width: number;
        posY: number;
        posX: number;
        contentInfo: { width: number; height: number; posX: number; posY: number };
        fun_name: string;
    }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        let { rt_sendTableDragPointTimer } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if (asset.meta.type === 'table') {
            clearTimeout(rt_sendTableDragPointTimer);
            rt_sendTableDragPointTimer = setTimeout(() => {
                assetManager.setPv_new(2351, {
                    additional: {
                        so: 'RIGHT',
                    },
                });
                clearTimeout(rt_sendTableDragPointTimer);
            }, 500);
        }

        AssetDragLogic.dragCenterPoint({ ...params, fun_name: 'DRAG_POINT_RIGHT' });
    }

    /*修改元素大小（拖动-上边）*/
    public static dragPointUp(params: {
        height: number;
        width: number;
        posY: number;
        posX: number;
        contentInfo: { width: number; height: number; posX: number; posY: number };
        fun_name: string;
    }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        let { rt_sendTableDragPointTimer } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if (asset.meta.type === 'table') {
            clearTimeout(rt_sendTableDragPointTimer);
            rt_sendTableDragPointTimer = setTimeout(() => {
                assetManager.setPv_new(2351, {
                    additional: {
                        so: 'TOP',
                    },
                });
                clearTimeout(rt_sendTableDragPointTimer);
            }, 500);
        }
        AssetDragLogic.dragCenterPoint({ ...params, fun_name: 'DRAG_POINT_UP' });
    }

    /*修改元素大小（拖动-下边）*/
    public static dragPointBottom(params: {
        height: number;
        width: number;
        posY: number;
        posX: number;
        contentInfo: { width: number; height: number; posX: number; posY: number };
        fun_name: string;
    }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        let { rt_sendTableDragPointTimer } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if (asset.meta.type === 'table') {
            clearTimeout(rt_sendTableDragPointTimer);
            rt_sendTableDragPointTimer = setTimeout(() => {
                assetManager.setPv_new(2351, {
                    additional: {
                        so: 'BOTTOM',
                    },
                });
                clearTimeout(rt_sendTableDragPointTimer);
            }, 500);
        }
        AssetDragLogic.dragCenterPoint({ ...params, fun_name: 'DRAG_POINT_BOTTOM' });
    }

    /*容器左下角(显示区)*/
    public static dragPointLeftBottomContentCutShow(params: {
        height: number;
        contentInfo: { width: number; height: number; posX: number; posY: number };
        width: number;
        posX: number;
        posY: number;
        fun_name: string;
    }): void {
        AssetDragLogic.dragPointLeftBottomContentCutShow({
            ...params,
            fun_name: 'DRAG_POINT_LEFT_BOTTOM_CONTENTCUTSHOW',
        });
    }

    /*容器左上角(显示区)*/
    public static dragPointLeftTopContentCutShow(params: {
        height: number;
        contentInfo: { width: number; height: number; posX: number; posY: number };
        width: number;
        posX: number;
        posY: number;
        fun_name: string;
    }): void {
        AssetDragLogic.dragPointLeftBottomContentCutShow({
            ...params,
            fun_name: 'DRAG_POINT_LEFT_TOP_CONTENTCUTSHOW',
        });
    }

    /*容器右上角(显示区)*/
    public static dragPointRightTopContentCutShow(params: {
        height: number;
        contentInfo: { width: number; height: number; posX: number; posY: number };
        width: number;
        posX: number;
        posY: number;
        fun_name: string;
    }): void {
        AssetDragLogic.dragPointRightTopContentCutShow({
            ...params,
            fun_name: 'DRAG_POINT_RIGHT_TOP_CONTENTCUTSHOW',
        });
    }

    /*容器右下角(显示区)*/
    public static dragPointRightBottomContentCutShow(params: {
        height: number;
        contentInfo: { width: number; height: number; posX: number; posY: number };
        width: number;
        posX: number;
        posY: number;
        fun_name: string;
    }): void {
        AssetDragLogic.dragPointRightBottomContentCutShow({
            ...params,
            fun_name: 'DRAG_POINT_RIGHT_BOTTOM_CONTENTCUTSHOW',
        });
    }

    /*修改元素收藏状态*/

    public static updateAssetIsFav(params: {
        isFav: boolean;
        // assetId: number;
        assetIndex: number;
        fun_name: string;
    }): void {
        FavAssetLogic.updateFavStatus(params);
    }

    /*容器左边(显示区)*/
    public static dragPointLeftContainerCut(params: {
        width: number;
        height: number;
        posX: number;
        posY: number;
        contentInfo?: object;
        assetIndex: number;
        contentPosX?: number;
        contentPosY?: number;
    }): void {
        AssetCropLogic.dragPointContainerCut({ ...params, fun_name: 'DRAG_POINT_LEFT_CONTAINERCUT' });
    }

    /*容器右边(显示区)*/
    public static drapPointRightContainerCut(params: {
        width: number;
        height: number;
        posX: number;
        posY: number;
        contentInfo?: object;
        assetIndex: number;
        contentPosX?: number;
        contentPosY?: number;
        fun_name: string;
    }): void {
        AssetCropLogic.dragPointContainerCut({ ...params, fun_name: 'DRAG_POINT_RIGHT_CONTAINERCUT' });
    }

    /*容器上边(显示区)*/
    public static dragPointTopContainerCut(params: {
        width: number;
        height: number;
        posX: number;
        posY: number;
        contentInfo?: object;
        assetIndex: number;
        contentPosX?: number;
        contentPosY?: number;
        fun_name: string;
    }): void {
        AssetCropLogic.dragPointContainerCut({ ...params, fun_name: 'DRAG_POINT_TOP_CONTAINERCUT' });
    }

    /*容器下边(显示区)*/
    public static dragPointBottomContainerCut(params: {
        width: number;
        height: number;
        posX: number;
        posY: number;
        contentInfo?: object;
        assetIndex: number;
        contentPosX?: number;
        contentPosY?: number;
        fun_name: string;
    }): void {
        AssetCropLogic.dragPointContainerCut({ ...params, fun_name: 'DRAG_POINT_BOTTOM_CONTAINERCUT' });
    }

    /*容器右下角(显示区)*/
    public static dragPointRightBottomContainerCut(params: {
        width: number;
        height: number;
        posX: number;
        posY: number;
        contentInfo?: object;
        assetIndex: number;
        contentPosX?: number;
        contentPosY?: number;
        fun_name: string;
    }): void {
        AssetCropLogic.dragPointContainerCut({ ...params, fun_name: 'DRAG_POINT_RIGHT_BOTTOM_CONTAINERCUT' });
    }

    /*容器左上角(显示区)*/
    public static dragPointLeftTopContainerCut(params: {
        width: number;
        height: number;
        posX: number;
        posY: number;
        contentInfo?: object;
        assetIndex: number;
        contentPosX?: number;
        contentPosY?: number;
        fun_name: string;
    }): void {
        AssetCropLogic.dragPointContainerCut({ ...params, fun_name: 'DRAG_POINT_LEFT_TOP_CONTAINERCUT' });
    }

    /*容器左下角(显示区)*/
    public static dragPointLeftBottomContainerCut(params: {
        width: number;
        height: number;
        posX: number;
        posY: number;
        contentInfo?: object;
        assetIndex: number;
        contentPosX?: number;
        contentPosY?: number;
        fun_name: string;
    }): void {
        AssetCropLogic.dragPointContainerCut({ ...params, fun_name: 'DRAG_POINT_LEFT_BOTTOM_CONTAINERCUT' });
    }

    /*容器右上角(显示区)*/
    public static dragPointRightTopContainerCut(params: {
        width: number;
        height: number;
        posX: number;
        posY: number;
        contentInfo?: object;
        assetIndex: number;
        contentPosX?: number;
        contentPosY?: number;
        fun_name: string;
    }): void {
        AssetCropLogic.dragPointContainerCut({ ...params, fun_name: 'DRAG_POINT_RIGHT_TOP_CONTAINERCUT' });
    }

    /*更新元素裁剪（编辑状态）*/
    public static updateAssetContainerIsEdit(params: {
        isEdit: boolean;
        asset: IAsset;
        assetIndex: number;
        className: string;
    }): void {
        let fun_name = 'UPDATE_ASSET_CONTAINER_ISEDIT';
        if (params.isEdit === false) {
            fun_name = 'UPDATE_ASSET_CONTAINER_ISEDIT_FALSE';
        }
        AssetCropLogic.updateContainerEditStatus({ ...params, fun_name });
    }

    /*更新元素裁剪框*/
    public static updateAssetContainer(params: {
        container?: {
            width: number;
            height: number;
            posX: number;
            posY: number;
        };
        asset: IAsset;
        assetIndex: number;
        tempPos?: string;
    }): void {
        AssetCropLogic.updateContainer({ ...params, fun_name: 'UPDATE_ASSET_CONTAINER' });
    }

    /*清除元素裁剪*/
    public static delAssetContainer(): void {
        AssetCropLogic.delContainer({ fun_name: 'DEL_ASSET_CONTAINER' });
    }

    /* 缩放裁剪元素 */
    public static scaleAssetContainer(params: { assetIndex: number; scaleSize: number }): void {
        AssetCropLogic.scaleContainer({ ...params, fun_name: 'SCALE_CONTAINER_SIZE' });
    }

    /*更新元素裁剪框（结束）*/
    public static updateAssetCutContainerEnd(params: {
        tempAsset: {
            attribute?: IAttribute;
            match?: object;
            asset?: IAsset;
            picUrl?: string;
            resId?: number;
        };
        // assetIndex: number;
        // className: string;
    }): void {
        AssetCropLogic.updateCutContainerEnd({ ...params, fun_name: 'UPDATE_ASSET_CONTAINER_END' });
    }

    /*更新元素裁剪框*/
    public static updateAssetCutContainerViewBox(params: {
        container: {
            viewBoxWidth: number;
            viewBoxHeight: number;
        };
        assetIndex: number;
        asset: IAsset;
    }): void {
        AssetCropLogic.updatCutContainerViewBox({ ...params, fun_name: 'UPDATE_ASSET_CONTAINER_VIEWBOX' });
    }

    /*更新元素裁剪框位置*/
    public static updateAssetContainerPos(params: { posX: number; posY: number }): void {
        const { toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        AssetCropLogic.updateContainerPos({
            ...params,
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_ASSET_CONTAINER_POS',
        });
    }

    /*容器左下角*/
    public static dragPointLeftBottomContentCut(params: { width: number; height: number; posX: number }): void {
        AssetCropLogic.dragPointLeftBottomContentCut({
            ...params,
            fun_name: 'DRAG_POINT_LEFT_BOTTOM_CONTENTCUT',
        });
    }

    /*容器左上角*/
    public static dragPointLeftTopContentCut(params: {
        width: number;
        height: number;
        posX: number;
        posY: number;
    }): void {
        AssetCropLogic.dragPointLeftTopContentCut({
            ...params,
            fun_name: 'DRAG_POINT_LEFT_TOP_CONTENTCUT',
        });
    }

    /*容器右上角*/
    public static dragPointRightTopContentCut(params: { width: number; height: number; posY: number }): void {
        AssetCropLogic.dragPointRightTopContentCut({
            ...params,
            fun_name: 'DRAG_POINT_RIGHT_TOP_CONTENTCUT',
        });
    }

    /*容器右下角*/
    public static dragPointRightBottomContentCut(params: { width: number; height: number }): void {
        AssetCropLogic.dragPointRightBottomContentCut({
            ...params,
            fun_name: 'DRAG_POINT_RIGHT_BOTTOM_CONTENTCUT',
        });
    }

    /* 清除添加的辅助值 前一个宽度值和高度值 */
    public static dragEndClearPreInfo(params: { width: number; height: number }): void {
        AssetCropLogic.dragEndClearPreInfo({
            ...params,
            fun_name: 'DRAG_END_CLEAR_PREINFO',
        });
    }

    // /*元素右下角（裁剪框）*/
    public static dragPointRightBottomAssetContainerCut(params: { width: number; height: number }): void {
        AssetCropLogic.dragPointRightBottomAssetContainerCut({
            ...params,
            fun_name: 'DRAG_POINT_RIGHT_BOTTOM_ASSETCONTAINERCUT',
        });
    }

    /*元素右上角（裁剪框）*/
    public static dragPointRightTopAssetContainerCut(params: {
        width: number;
        height: number;
        posX: number;
        posY: number;
    }): void {
        AssetCropLogic.dragPointRightTopAssetContainerCut({
            ...params,
            fun_name: 'DRAG_POINT_RIGHT_TOP_ASSETCONTAINERCUT',
        });
    }

    /*元素左上角（裁剪框）*/
    public static dragPointLeftTopAssetContainerCut(params: {
        width: number;
        height: number;
        posX: number;
        posY: number;
    }): void {
        AssetCropLogic.dragPointLeftTopAssetContainerCut({
            ...params,
            fun_name: 'DRAG_POINT_LEFT_TOP_ASSETCONTAINERCUT',
        });
    }

    /*元素左上角（裁剪框）*/
    public static dragPointLeftBottomAssetContainerCut(params: { width: number; height: number; posX: number }): void {
        AssetCropLogic.dragPointLeftBottomAssetContainerCut({
            ...params,
            fun_name: 'DRAG_POINT_LEFT_BOTTOM_ASSETCONTAINERCUT',
        });
    }

    /*3D字内容更新*/
    public static update3DTextWordInfo(params: { assetIndex: number; fun_name: string }): void {
        AssetTextLogic.update3DWordInfo(params);
    }

    /*元素组合*/
    public static combinationAsset(params: {
        assets?: IAsset[];
        assetsIndex?: number[];
        /*  */
    }): void {
        AssetGroupLogic.combinationAssetEvent(params);
        setTimeout(() => {
            const { work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            const as = work.pages[pageInfo.pageNow].assets;
            // console.log('as', as)
            const target = as[as.length - 1];
            const newMemberClass: string[] = [];

            as.forEach((item) => {
                if (item.meta.type !== 'group' && item.meta.group === target.meta.group) {
                    newMemberClass.push(item.meta.className);
                }
            });
            // 修复 组A 和 组B 再组合后，选中的bug
            if (target.meta.memberClassNames.length !== newMemberClass.length) {
                target.meta.memberClassNames = newMemberClass;
                UpdateAsset.updateAssets('UPDATE_ASSET', [
                    {
                        index: as.length - 1,
                        changes: {
                            meta: {
                                memberClassNames: newMemberClass,
                            },
                        },
                    },
                ]);
            }

            // 修复 组A和文字组合后，默认选中新组合
            SelectAsset.selectAsset({
                asset_index: as.length - 1,
            });
        }, 10);
    }

    /*拆分元素*/
    public static splitAsset(params: {
        groupName?: string;
        /*  */
    }): void {
        // 解组以后 继续多选
        const { work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        AssetGroupLogic.splitAssetEvent(params);

        if (params.groupName) {
            const classNameList: string[] = [];
            work.pages[pageInfo.pageNow].assets.forEach((asset: IAsset) => {
                if (asset.meta.group == params.groupName && asset.meta.type !== 'group') {
                    classNameList.push(asset.meta.className);
                }
            });
            SelectAsset.selectAssetsByGroup(params, classNameList);
        }
        // 结组后自动解锁
        if (params.groupName) {
            const targets: IUpdateTarget[] = [];
            work.pages[pageInfo.pageNow].assets.forEach((asset: IAsset) => {
                if (asset.meta.group == params.groupName && asset.meta.locked && asset.meta.type !== 'group') {
                    targets.push({
                        index: asset.meta.index,
                        className: asset.meta.className,
                        changes: {
                            meta: {
                                locked: false,
                            },
                        },
                    });
                }
            });
            UpdateAsset.updateAssets('UPDATE_LOCKED', targets, void 0, [], true);
        }
    }

    public static alignDistribute(params: { handle: string }): void {
        AssetGroupLogic.alignDistribute(params);
    }

    /*更新SVG颜色*/
    public static updateAssetSvgColors(params: { colors: IColor; asset: IAsset; pageIndex?: number }): void {
        const { asset, colors } = params;
        const { work, pageInfo } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        let assetIndex = -1;
        assetIndex = work.pages[pageInfo.pageNow].assets.findIndex(
            (item: IAsset, index: number) => item.meta.className === asset.meta.className,
        );

        if (assetIndex > -1) {
            const tempColors = work.pages[pageInfo.pageNow].assets[assetIndex].attribute.colors;
            Object.assign(colors, tempColors);
            AssetSvgLogic.updateColors({
                ...params,
                colors,
                assetIndex: assetIndex,
                fun_name: 'UPDATE_SVG_COLORS',
            });
        }
    }

    /*更新SVG颜色详情*/
    public static updateAssetSvgColorsInfo(params: { color: IColor; kColor: string }): void {
        const { color, kColor } = params;
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        const colors = cloneDeep(asset.attribute.colors);

        Object.assign(colors, {
            [kColor]: color,
        });

        AssetSvgLogic.updateColors({
            ...params,
            colors,
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_SVG_COLORS_INFO',
        });
    }
    /* 更新svg描边颜色*/
    public static updateAssetSvgStrokeColor(params: { color: IColor }): void {
        const { color } = params;
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        let stroke: IStroke = {width: 0, color: {r: 0, g: 0, b: 0, a: 1}, style: 'solid'}; 
        if (asset.attribute.stroke) {
            stroke = cloneDeep(asset.attribute.stroke);
        }

        Object.assign(stroke, {
            color: color,
        });

        AssetSvgLogic.updateStroke({
            ...params,
            stroke,
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_STROKE_COLOR',
        })
    }

    /*更新SVG描边详情*/
    public static updateAssetSvgStrokeInfo(params: { color?: IColor; width?: string, style?: string }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        let stroke: IStroke = {width: 0, color: {r: 0, g: 0, b: 0, a: 1}, style: 'solid'}; 
        if (asset.attribute.stroke) {
            stroke = cloneDeep(asset.attribute.stroke);
        }

        Object.assign(stroke, {
            ...params,
        });

        AssetSvgLogic.updateStroke({
            ...params,
            stroke,
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_STROKE_INFO',
        });
    }
    /* 保存更新SVG描边 */
    public static saveAssetSvgStroke(): void {
        const { toolPanel} = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        AssetSvgLogic.startUpdateStroke({
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_STROKE_START',
        });
    }
    /*更新相框颜色详情*/
    public static updateAssetFrameColorsInfo(params: { color: IColor }, updateType: 'content' | 'background'): void {
        const { color } = params;
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        switch (updateType) {
            case 'content':
                const originColor = cloneDeep(asset.attribute.color);
                Object.assign(originColor, color);
                AssetFrameLogic.updateContentColors({
                    ...params,
                    color: originColor,
                    assetIndex: toolPanel.asset_index,
                    fun_name: 'UPDATE_FRAME_CONTENT_COLORS_INFO',
                });
                break;
            case 'background':
                const originBackgroundColor = cloneDeep(asset.attribute.frameBackgroundInfo.color);
                Object.assign(originBackgroundColor, color);
                AssetFrameLogic.updateFrameBackgroundColors({
                    ...params,
                    color: originBackgroundColor,
                    assetIndex: toolPanel.asset_index,
                    fun_name: 'UPDATE_FRAME_BACKGROUND_COLORS_INFO',
                });
                break;
        }
    }
    /*更新相框图片详情*/
    public static updateAssetFrameImageInfo(
        updateInfo: FrameImageUpdateInfo,
        className?: string,
        fun_name?: string,
    ): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index, className: className });
        if (!asset) {
            return;
        }
        const originInfo = cloneDeep(asset.attribute);
        switch (updateInfo.type) {
            case 'content':
                Object.assign(originInfo, updateInfo);
                AssetFrameLogic.updateAsset({
                    assetIndex: toolPanel.asset_index,
                    className,
                    attribute: originInfo,
                    fun_name: fun_name ?? 'UPDATE_FRAME_CONTENT_IMAGE_INFO',
                });
                break;
            case 'background':
                originInfo.frameBackgroundInfo = {
                    ...originInfo.frameBackgroundInfo,
                    ...updateInfo,
                };
                AssetFrameLogic.updateAsset({
                    assetIndex: toolPanel.asset_index,
                    className,
                    attribute: originInfo,
                    fun_name: 'UPDATE_FRAME_BACKGROUND_IMAGE_INFO',
                });
                break;
        }
    }
    /**
     * 初始化相框信息
     * @param updateInfo
     * @returns
     */
    public static initFrameAttribute(updateInfo: Partial<IAttribute>, className?: string) {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index, className: className });
        if (!asset) {
            return;
        }
        const attribute = cloneDeep(asset.attribute);
        Object.assign(attribute, updateInfo);
        AssetFrameLogic.updateFrameAttributeInfo({
            attribute,
            assetIndex: toolPanel.asset_index,
            className: className,
            fun_name: 'INIT_FRAME_ATTRIBUTE_INFO',
        });
    }
    /**
     * 更新相框初始化信息
     * @param updateInfo
     * @returns
     */
    public static updateFrameAttribute(updateInfo: Partial<IAttribute>) {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        const attribute = cloneDeep(asset.attribute);
        Object.assign(attribute, updateInfo);
        AssetFrameLogic.updateFrameAttributeInfo({
            attribute,
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_FRAME_ATTRIBUTE_INFO',
        });
    }
    /**
     * 更新相框裁剪状态
     * @param isClip
     * @param assetIndex
     * @returns
     */
    public static setClipFrameStatus(isClip: boolean, className?: string) {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index, className: className });
        if (!asset) {
            return;
        }
        const meta = cloneDeep(asset.meta);
        Object.assign(meta, { isClip });
        AssetFrameLogic.updateFrameClipStatus({
            meta,
            assetIndex: toolPanel.asset_index,
            className,
            fun_name: 'UPDATE_FRAME_CLIP_STATUS',
        });
    }
    /*快速编辑更新模版*/
    public static quickEditingUpdateText(params: { text: IAsset['attribute']['text'] }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        let asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }
        let aIndex = toolPanel.asset_index;
        if (asset.meta.type === 'group' && asset?.groupTextEditAsset?.meta.type === 'text') {
            aIndex = AssetHelper.getIndex(work, pageInfo, { className: asset.groupTextEditAsset.meta.className });
            asset = AssetHelper.find(work, pageInfo, { index: aIndex });
        }
        const tempArr = asset.meta.className.split('.');
        tempArr[0] += '.' + new Date().valueOf();
        const targets = [
            {
                index: aIndex,
                changes: {
                    meta: {
                        isEditor: 1,
                        className: tempArr[0],
                    },
                    attribute: {
                        text: params.text,
                    },
                },
            },
        ];
        UpdateAsset.updateAssets('QUICK_EDITING_UPDATE_TEXT', targets);
    }

    /*修改多选文字元素字号*/
    public static updateAssetsFontsize(params: {
        type: 'add' | 'minus' | undefined;
        fontSize: number;
        autoSaveFlag: number;
    }): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if (toolPanel.assets_index.length > 1) {
            const assets = AssetHelper.findAll(
                work,
                pageInfo,
                toolPanel.assets_index.map((i) => ({ index: i })),
            ).filter((a) => a);
            if (assets.length === 0) {
                return;
            }
            const assetTargets: Record<string, IAsset> = {};
            assets.forEach((a, i) => {
                if (a.meta.type === 'group') {
                    const groupAssets = AssetHelper.getGroupAssets(work, pageInfo, a.meta.group);
                    for (const index in groupAssets) {
                        if (groupAssets[index].meta.type === 'text') {
                            assetTargets[index] = groupAssets[index];
                        }
                    }
                } else {
                    assetTargets[toolPanel.assets_index[i]] = a;
                }
            });
            const tragets = Object.entries(assetTargets).map(([i, a]) => {
                let fs = a.attribute.fontSize;
                if (params.type === 'add') {
                    fs++;
                } else if (params.type === 'minus') {
                    fs--;
                } else {
                    fs = params.fontSize;
                }
                return {
                    index: Number(i),
                    changes: {
                        attribute: {
                            fontSize: fs,
                        },
                    },
                };
            });
            UpdateAsset.updateAssets('UPDATE_ASSETS_FONTSIZE', tragets, params.autoSaveFlag);
        }
        localStorage.setItem('rt_mutiple_select_new', 'true');
    }

    public static assetCut(params: { cropXFrom: number; cropYFrom: number; cropXTo: number; cropYTo: number }): void {
        const { toolPanel, canvas, work, pageInfo } = storeAdapter.getStore<
            typeof storeAdapter.store_names.paintOnCanvas
        >({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        const width = (params.cropXTo - params.cropXFrom) * asset.attribute.width;
        const height = (params.cropYTo - params.cropYFrom) * asset.attribute.height;
        let posX = params.cropXFrom * asset.attribute.width + asset.transform.posX;
        let posY = params.cropYFrom * asset.attribute.height + asset.transform.posY;

        posX = posX < -width ? 0 : posX;
        posX = posX > canvas.width ? canvas.width - width : posX;
        posY = posY < -height ? 0 : posY;
        posY = posY > canvas.height ? canvas.height - height : posY;

        const targets: { index: number; changes: Partial<IAsset> }[] = [
            {
                index: toolPanel.asset_index,
                changes: {
                    attribute: {
                        cropXFrom: params.cropXFrom,
                        cropYFrom: params.cropYFrom,
                        cropXTo: params.cropXTo,
                        cropYTo: params.cropYTo,
                        width: width > canvas.width ? canvas.width : width,
                        height: ((width > canvas.width ? canvas.width : width) * height) / width,
                    },
                    transform: {
                        posX: posX < 0 ? 0 : posX,
                        posY: posY < 0 ? 0 : posY,
                    },
                },
            },
        ];

        UpdateAsset.updateAssets('ASSET_CUT', targets);
    }

    public static updateAssetPage(
        list: { index: number; page_num?: number; pre_page_num?: number; posY: number }[],
    ): void {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        // const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        // if (!asset) {
        //     return;
        // }

        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: '',
            params: [
                {
                    type: 'changeAssetPage',
                    params: {
                        asset_index_list: list,
                    },
                },
            ],
        });
    }
    public static adjustAssetPosition(key: IAssetPostionEnum, asset?: IAsset) {
        const { canvas_dom_info, canvas, toolPanel } = canvasStore.getState().onCanvasPainted;
        const handleAsset = asset ?? (toolPanel.asset as IAsset);
        if (handleAsset.meta.locked) return;
        if (!canvas_dom_info?.is_exist) {
            return;
        }
        let { posX = 0, posY = 0 } = handleAsset.transform;
        let { width, height } = handleAsset.attribute;
        let area = AssetHelper.countAssetMaxArea(handleAsset);
        // 组内元素使用组合的宽高计算
        if (handleAsset.meta.group && handleAsset.meta.type !== 'group') {
            const groupAsset = GroupAndMultipleSelectLogic.fingGroup(handleAsset.meta.group) as IAsset;
            if (groupAsset.meta.locked) return;
            posX = groupAsset.transform.posX;
            posY = groupAsset.transform.posY;
            width = groupAsset.attribute.width;
            height = groupAsset.attribute.height;
            area = AssetHelper.countAssetMaxArea(groupAsset);
        }
        if (handleAsset.meta.type === 'text') {
            const dom = document.querySelector<HTMLElement>(
                `.${canvas_dom_info.class_name} .${handleAsset.meta.className}`,
            );
            if (dom) {
                width = dom.clientWidth / canvas.scale;
                height = dom.clientHeight / canvas.scale;
            }
        }
        let moveX, moveY;
        switch (key) {
            case 'lt':
            case 'l':
            case 'lb':
                moveX = 0 - area.l;
                break;
            case 't':
            case 'c':
            case 'b':
                moveX = (canvas.width - width) / 2 - posX;
                break;
            case 'rt':
            case 'r':
            case 'rb':
                moveX = canvas.width - area.r;
                break;
        }
        switch (key) {
            case 'lt':
            case 't':
            case 'rt':
                moveY = 0 - area.t;
                break;
            case 'l':
            case 'c':
            case 'r':
                moveY = (canvas.height - height) / 2 - posY;
                break;
            case 'lb':
            case 'b':
            case 'rb':
                moveY = canvas.height - area.b;
                break;
        }
        if (handleAsset.meta.group) {
            GroupAndMultipleSelectLogic.updateAssetsPosition({
                posX: posX + moveX,
                posY: posY + moveY,
            });
        } else {
            UpdateAsset.updateAssets('UPDATE_ASSETS', [
                {
                    index: toolPanel.asset_index,
                    className: handleAsset.meta.className,
                    changes: {
                        transform: {
                            posX: posX + moveX,
                            posY: posY + moveY,
                        },
                    },
                },
            ]);
        }
    }
    
    /**
     * 改变频繁触发的埋点发送规则
     * @param {*} pageId
     * @param {*} _this 正确的this对象
     * @param {*} timer 延时多久发送
     * @param {*} otherParams 埋点需要的额外发送参数
     */
    public static changeFrequentTrigPointRule(pageId = '', timer = 60 * 1000, otherParams = {}): void {
        if (pageId) {
            if (this.timerSendPointRecords[pageId]) {
                this.timerSendPointRecords[pageId]++;
            } else {
                this.timerSendPointRecords[pageId] = 1;
            }

            if (!this[`timerSend${pageId}`]) {
                this[`timerSend${pageId}`] = setTimeout(() => {
                    assetManager.setPv_new(pageId, {
                        additional: {
                            ot: this.timerSendPointRecords[pageId],
                            ...otherParams,
                        },
                    });
                    clearTimeout(this[`timerSend${pageId}`]);
                    this[`timerSend${pageId}`] = null;
                    this.timerSendPointRecords[pageId] = 0;
                }, timer);
            }
        }
    }
    
    public static getSvgAssetChangeText(params: {
        assetIndex: number;
        attribute: string;
        key: string;
        value: string[] | IColor | number | string | boolean;
        oldText?: string[];
        richText?: any;
        activeIndex?: number[];
        className?: string;
        fun_name?: string;
    }) {
        return AssetSvgLogic.getSvgTextChange(params);
    }
    /**
     * @description: 获取
     * @return {*}
     */    
    public static getCurPageBackgroundAsset() {
        const { work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        return work.pages[pageInfo.pageNow].assets.find((item) => item.meta.type === 'background');
    }

    /**
     * @description: 更新线条颜色
     * @param {*}
     * @return {*}
     */
    public static updateLineColors(params: { color: IColor }) {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        AssetLineLogic.updateColors({
            ...params,
            color: params.color,
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_LINE_COLORS',
        });
    }
    /**
     * @description: 更新线条宽度
     * @param {*}
     * @return {*}
     */
    public static updateLineWidth(params: { width: number }) {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        AssetLineLogic.updateLineWidth({
            ...params,
            lineWidth: params.width,
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_LINE_WIDTH',
        });
    }

    /**
     * @description: 更新线条箭头
     * @param {*}
     * @return {*}
     */
    public static updateAssetLineArrow(params: { arrow: string, key: string }) {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }

        AssetLineLogic.updateLineArrow({
            ...params,
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_LINE_ARROW',
        });
    }

    public static updateAssetLineType(type) {
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }
        AssetLineLogic.updateLineType({
            type,
            assetIndex: toolPanel.asset_index,
            fun_name: 'UPDATE_LINE_TYPE',
        });
    }
}

// export let assetLogic = new AssetLogic();
