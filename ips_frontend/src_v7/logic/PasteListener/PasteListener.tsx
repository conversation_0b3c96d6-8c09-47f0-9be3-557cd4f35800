import { addEventListener, EventListener } from '@v7_utils/AddEventListener';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { ListenerEventInterface, IPasteAssets, IAsset } from '@v7_logic/Interface';
import { EditorLogic } from '@v7_logic/EditorLogic';

// 待替换组件
import { emitter } from '@component/Emitter';
import { assetManager } from '@component/AssetManager';
import { readPasteFromClipboard, isObjectCopyText, readPasteTextFromClipboard, readPasteHtmlFromClipboard } from '@component/Function';
import { AssetHelper } from '@v7_logic/AssetHelper';
import { AssetTableLogic } from '@v7_logic/AssetTableLogic';
import { createTextAssets } from './createTextAssets';
import { parseClipboardTable } from './util';
import { createTableAssets } from './createTableAssets';

/**
 * 粘贴监听
 */
class PasterListener implements ListenerEventInterface {
    private listener_obj: EventListener;

    /**
     * 监听注册
     */
    public listenerEvent = () => {
        this.unListenerEvent();

        this.listener_obj = addEventListener(window, 'paste', async (e: ClipboardEvent) => {
            if ((e.target as Node).nodeName === 'INPUT') {
                return;
            }
            if (!(e.clipboardData && e.clipboardData.items && e.clipboardData.items.length > 0)) {
                return;
            } else {
                const { isDesigner, toolPanel, work, pageInfo,  } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                });
                let tableAsset: IAsset = undefined
                if (toolPanel.asset_index >= 0) {
                    const asset = AssetHelper.find(work, pageInfo, {index: toolPanel.asset_index})
                    if (asset?.meta?.type === 'table' && asset.choosedTdKeys?.length > 0) {
                        tableAsset = asset;
                    }
                }
                // 粘贴表格单元格数据
                if (tableAsset && localStorage.getItem('ueCopyTds')) {
                    AssetTableLogic.pasteSelectedTds(tableAsset, toolPanel.asset_index);
                } else {
                    // 粘贴除表格外的其他内容
                    for (let i = 0, len = e.clipboardData.items.length; i < len; i++) {
                        const item = e.clipboardData.items[i];
                        // 粘贴文件
                        if (item.kind === 'file' && !isDesigner) {
                            const pasteFile = item.getAsFile();
                            emitter.emit('UserUploadArea_SendFiles', [pasteFile], 'ctrlV');
                            emitter.emit('changeActivedMenuId', undefined)
                            emitter.emit('onNavChanged', 'uploadFile');
                            emitter.emit('rightLayoutHideClick', 'open', 'uploadFile');

                            assetManager.setPv_new(173);

                            const input = document.createElement('input');
                            document.body.appendChild(input);
                            input.setAttribute('value', ' ');
                            input.select();
                            // if (document.execCommand('copy')) {
                            //     document.execCommand('copy');
                            // }
                            // 删除'虚拟'DOM
                            document.body.removeChild(input);
                        } else {
                            const html = e.clipboardData.getData('text/html')
                            const parser = new DOMParser();
                            const doc = parser.parseFromString(html, 'text/html');
                            const table = doc.querySelector('table');
                            // 粘贴除文件外的其他内容
                            const { isHotKeyEvent } = storeAdapter.getStore<typeof storeAdapter.store_names.HotKey>({
                                store_name: storeAdapter.store_names.HotKey,
                            });
                            if (isHotKeyEvent) {
                                if(table){
                                    const tableData = parseClipboardTable(table)
                                    if(tableData){
                                        const assets = createTableAssets(tableData)
                                        assets && localStorage.setItem('ue_copyAsset',JSON.stringify(assets));
                                        EditorLogic.pasteAsset();
                                    }
                                    assetManager.setPv_new(9096);
                                    return
                                }

                                // 解决快捷键不能跨编辑器粘贴的问题
                                
                                const isObjectCopy = await isObjectCopyText()
                                console.log(isObjectCopy, 'isStringCopy')

                                if (!isObjectCopy) {
                                    // const copyText = await navigator.clipboard.readText();
                                    readPasteTextFromClipboard().then((pasteText) => {
                                        if (typeof pasteText == "string" && pasteText.length > 0) {
                                            if(pasteText == ' '){
                                                return
                                            }
                                            
                                            const assets = createTextAssets(pasteText)
                                            assetManager.setPv_new(8033);
                                            assets && localStorage.setItem('ue_copyAsset',JSON.stringify(assets));
                                            //    window.setTimeout(() => {
                                            //         const { toolPanel, user:{userId} } = storeAdapter.getStore<
                                            //             typeof storeAdapter.store_names.paintOnCanvas
                                            //         >({
                                            //             store_name: storeAdapter.store_names.paintOnCanvas,
                                            //         });

                                            //          emitter.emit('addTextEditingEventListener', toolPanel.asset);
                                            //     }, 100);
                                        }
                                        EditorLogic.pasteAsset();
                                    })
                                    return
                                }
                                readPasteFromClipboard((assets:any) => {
                                    const copyType = window.location.href.includes('ue.818ps.com') ? 'ue' : window.location.href.includes('ecommerce.818ps.com') ? 'ecommerce' : null;
                                    const {editorTag} = assets;
                                    (copyType && !editorTag.includes(copyType)) && assetManager.setPv_new(5112);
                                    let hasTextAsset = false;
                                    // 增加添加标识
                                    if(assets.asset && assets.asset.meta.type == 'text'){
                                        assets.asset.meta.addFrom = 'paste'
                                        hasTextAsset = true;
                                    }else if(assets.assetsGroup){
                                        for(let i = 0; i < assets.assetsGroup.length; i++){
                                            const asset = assets.assetsGroup[i]
                                            if(asset && asset.meta.type == 'text'){
                                                hasTextAsset = true;
                                                asset.meta.addFrom = 'paste'
                                            }
                                        }
                                    }else if(assets.assets){
                                        for(let i = 0; i < assets.assets.length; i++){
                                            const asset = assets.assets[i]
                                            if(asset && asset.meta.type == 'text'){
                                                hasTextAsset = true;
                                                asset.meta.addFrom = 'paste'
                                            }
                                        }
                                    }
                                    assets && localStorage.setItem('ue_copyAsset',JSON.stringify(assets));
                                    EditorLogic.pasteAsset();
                                    if(hasTextAsset){
                                         assetManager.setPv_new(8900, { additional: {} });
                                    }
                                })
                          
                                assetManager.setPv_new(2442, { additional: {} });
                            }
                            break;
                        }
                    }
                }
            }
        });

        return this.unListenerEvent;
    };

    /**
     * 监听注销
     */
    public unListenerEvent = () => {
        this.listener_obj && this.listener_obj.remove();
    };
}

const pasterListener = new PasterListener();
export { pasterListener as PasterListener };
