export interface TableData {
  headers: string[];
  rows: string[][];
}

// 解析粘贴内容中的表格数据
export const parseClipboardTable = (table: Element): TableData | null => {
  
  const headers: string[] = [];
  const rows: string[][] = [];
  
  // 提取表头
  const theadElements = table.querySelectorAll('thead th');
  if (theadElements.length > 0) {
    theadElements.forEach(th => {
      headers.push(th.textContent?.trim() || '');
    });
  }
  
  // 提取表体数据
  const tbodyRows = table.querySelectorAll('tbody tr');
  if (tbodyRows.length > 0) {
    tbodyRows.forEach(tr => {
      const rowData: string[] = [];
      const cells = tr.querySelectorAll('td, th');
      cells.forEach(td => {
        rowData.push(td.textContent?.trim() || '');
      });
      if (rowData.length > 0) {
        rows.push(rowData);
      }
    });
  } else {
    // 如果没有tbody，获取所有行（除了第一行，如果它是表头）
    const allRows = table.querySelectorAll('tr');
    const startIndex = theadElements.length > 0 ? 0 : 1; // 如果有thead就从0开始，否则跳过第一行
    
    for (let i = startIndex; i < allRows.length; i++) {
      const tr = allRows[i];
      const rowData: string[] = [];
      const cells = tr.querySelectorAll('td, th');
      cells.forEach(cell => {
        rowData.push(cell.textContent?.trim() || '');
      });
      if (rowData.length > 0) {
        rows.push(rowData);
      }
    }
  }
  
  return { headers, rows };
};