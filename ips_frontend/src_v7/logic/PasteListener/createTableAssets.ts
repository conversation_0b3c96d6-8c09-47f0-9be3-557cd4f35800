import { assetTemplate } from "@src/userComponentV6.0/AssetMap"
import { fitPageDomSIze } from "@v7_logic/AssetAddListener/FitPageDomSize";
import { IAsset } from "@v7_logic/Interface";
import { storeAdapter } from "@v7_logic_core/StoreAdapter"
import { IPaintOnCanvasState } from "@v7_store/redux/reducers/onCanvasPaintedReducer";
import { klona as cloneDeep } from 'klona';

interface TableData {
  headers: string[];
  rows: string[][];
}

// 默认单元格样式
const defaultCellStyle = {
  "lineStyle": ["solid", "solid", "solid", "solid"],
  "lineColor": [
    { "r": 218, "g": 218, "b": 218, "a": 1 },
    { "r": 218, "g": 218, "b": 218, "a": 1 },
    { "r": 218, "g": 218, "b": 218, "a": 1 },
    { "r": 218, "g": 218, "b": 218, "a": 1 }
  ],
  "lineWidth": [1, 1, 1, 1],
  "background": {
    "color": { "r": 255, "g": 255, "b": 255, "a": 1 }
  }
};

// 表头单元格样式（带背景色）
const headerCellStyle = {
  ...defaultCellStyle,
  "background": {
    "color": { "r": 245, "g": 161, "b": 127, "a": 1 }
  }
};

// 默认文本样式
const defaultTextStyle = {
  "alpha": 1,
  "horizontalFlip": false,
  "verticalFlip": false,
  "fontSize": 36,
  "fontWeight": 400,
  "textAlign": "center",
  "lineHeight": 1.3,
  "fontFamily": "fnsyhtRegular",
  "color": { "r": 51, "g": 51, "b": 51, "a": 1 }
};

// 表头文本样式（加粗）
const headerTextStyle = {
  ...defaultTextStyle,
  "fontWeight": 600
};

export function createTableAssets(tableData: TableData) {
    
  const { headers, rows } = tableData;
  console.log(headers, rows, 'headers, rows')
  
  if (!headers.length && !rows.length) {
    return null;
  }

  // 是否有表头
  const hasHeaders = headers.length > 0;
  
  // 计算表格尺寸
  const colCount = Math.max(headers.length, ...rows.map(row => row.length));
  const rowCount = (hasHeaders ? 1 : 0) + rows.length;
  
  // 默认列宽和行高
  const defaultColWidth = 150;
  const defaultRowHeight = 56;
  
  // 生成单元格样式数组
  const generateCells = () => {
    const cells: any[][] = [];
    
    for (let rowIndex = 0; rowIndex < rowCount; rowIndex++) {
      const cellRow: any[] = [];
      const isHeaderRow = hasHeaders && rowIndex === 0;
      
      for (let colIndex = 0; colIndex < colCount; colIndex++) {
        const cellStyle = isHeaderRow ? headerCellStyle : defaultCellStyle;
        
        // 处理边框线宽（避免重复边框）
        const lineWidth = [
          1, // top
          colIndex === colCount - 1 ? 1 : 0, // right (只有最后一列显示右边框)
          1, // bottom
          colIndex === 0 ? 1 : 0  // left (只有第一列显示左边框)
        ];
        
        cellRow.push({
          ...cellStyle,
          lineWidth
        });
      }
      
      cells.push(cellRow);
    }
    
    return cells;
  };

  // 生成文本内容数组
  const generateTexts = () => {
    const texts: any[][] = [];
    
    for (let rowIndex = 0; rowIndex < rowCount; rowIndex++) {
      const textRow: any[] = [];
      const isHeaderRow = hasHeaders && rowIndex === 0;
      
      for (let colIndex = 0; colIndex < colCount; colIndex++) {
        let content = '';
        
        if (isHeaderRow) {
          content = headers[colIndex] || '';
        } else {
          const dataRowIndex = hasHeaders ? rowIndex - 1 : rowIndex;
          content = rows[dataRowIndex]?.[colIndex] || '';
        }
        
        const textStyle = isHeaderRow ? headerTextStyle : defaultTextStyle;
        
        textRow.push({
          ...textStyle,
          content: [content]
        });
      }
      
      texts.push(textRow);
    }
    
    return texts;
  };

  // 生成尺寸配置
  const generateCellSize = () => {
    return {
      col: Array(colCount).fill(defaultColWidth),
      row: Array(rowCount).fill(defaultRowHeight)
    };
  };

  // 创建表格 asset
  let tableAsset: IAsset = cloneDeep(assetTemplate.table) as any;
  
  const { canvas } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
    store_name: storeAdapter.store_names.paintOnCanvas,
  });

  const totalWidth = colCount * defaultColWidth;
  const totalHeight = rowCount * defaultRowHeight;

  tableAsset = {
    ...tableAsset,
    meta: {
      ...tableAsset.meta,
      type: 'table',
      className: 'table7',
      addFrom: 'paste' // 标记来源为粘贴
    },
    transform: {
      ...tableAsset.transform,
      posX: ((canvas.width - totalWidth) / 2) * canvas.scale,
      posY: ((canvas.height - totalHeight) / 2) * canvas.scale,
    },
    attribute: {
      ...tableAsset.attribute,
      width: totalWidth,
      height: totalHeight,
      cell: generateCells(),
      cellSize: generateCellSize(),
      text: generateTexts(),
      resId: "3"
    }
  };

  // 适配页面尺寸
  const paintOnCanvasState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
    store_name: storeAdapter.store_names.paintOnCanvas,
  }) as IPaintOnCanvasState;
  
  fitPageDomSIze(paintOnCanvasState, tableAsset);

  const { user } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
    store_name: storeAdapter.store_names.paintOnCanvas,
  });

  const assets = {
    asset: tableAsset,
    userId: user.userId,
    createTime: Date.now(),
    canvasTime: Date.now()
  };

  return assets;
}

const asset = {
  "meta": {
      "index": 7,
      "type": "table",
      "className": "table7",
      "name": "",
      "group": "",
      "hash": "474125ca9797d203bf2ef5332a9f3336-22cf8d4e392f7514253a9891e9117524",
      "rt_page_index": 0,
      "rt_page_assets_index": 6,
      "v": 2,
      "deleted": 0,
      "rt_rntime": 0
  },
  "transform": {
      "posX": 27,
      "posY": 202,
      "rotate": 0,
      "horizontalFlip": false,
      "verticalFlip": false
  },
  "attribute": {
      "opacity": 100,
      "width": 1188,
      "height": 485,
      "cell": [
          [
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      0,
                      1,
                      1
                  ],
                  "background": {
                      "color": {
                          "r": 255,
                          "g": 255,
                          "b": 255,
                          "a": 1
                      }
                  }
              },
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      0,
                      1,
                      0
                  ],
                  "background": {
                      "color": {
                          "r": 255,
                          "g": 255,
                          "b": 255,
                          "a": 1
                      }
                  }
              },
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      0,
                      1,
                      0
                  ],
                  "background": {
                      "color": {
                          "r": 255,
                          "g": 255,
                          "b": 255,
                          "a": 1
                      }
                  }
              },
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      0,
                      1,
                      0
                  ],
                  "background": {
                      "color": {
                          "r": 255,
                          "g": 255,
                          "b": 255,
                          "a": 1
                      }
                  }
              },
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      1,
                      1,
                      0
                  ],
                  "background": {
                      "color": {
                          "r": 255,
                          "g": 255,
                          "b": 255,
                          "a": 1
                      }
                  }
              }
          ],
          [
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      0,
                      1,
                      1
                  ],
                  "background": {
                      "color": {
                          "r": 245,
                          "g": 161,
                          "b": 127,
                          "a": 1
                      }
                  }
              },
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      0,
                      1,
                      0
                  ],
                  "background": {
                      "color": {
                          "r": 245,
                          "g": 161,
                          "b": 127,
                          "a": 1
                      }
                  }
              },
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      0,
                      1,
                      0
                  ],
                  "background": {
                      "color": {
                          "r": 245,
                          "g": 161,
                          "b": 127,
                          "a": 1
                      }
                  }
              },
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      0,
                      1,
                      0
                  ],
                  "background": {
                      "color": {
                          "r": 245,
                          "g": 161,
                          "b": 127,
                          "a": 1
                      }
                  }
              },
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      1,
                      1,
                      0
                  ],
                  "background": {
                      "color": {
                          "r": 245,
                          "g": 161,
                          "b": 127,
                          "a": 1
                      }
                  }
              }
          ],
          [
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      0,
                      1,
                      1
                  ],
                  "background": {
                      "color": {
                          "r": 255,
                          "g": 255,
                          "b": 255,
                          "a": 1
                      }
                  }
              },
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      0,
                      1,
                      0
                  ],
                  "background": {
                      "color": {
                          "r": 255,
                          "g": 255,
                          "b": 255,
                          "a": 1
                      }
                  }
              },
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      0,
                      1,
                      0
                  ],
                  "background": {
                      "color": {
                          "r": 255,
                          "g": 255,
                          "b": 255,
                          "a": 1
                      }
                  }
              },
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      0,
                      1,
                      0
                  ],
                  "background": {
                      "color": {
                          "r": 255,
                          "g": 255,
                          "b": 255,
                          "a": 1
                      }
                  },
                  "merge": {
                      "x": 2,
                      "y": 4
                  }
              },
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      1,
                      1,
                      0
                  ],
                  "background": {
                      "color": {
                          "r": 255,
                          "g": 255,
                          "b": 255,
                          "a": 1
                      }
                  },
                  "merged": {
                      "x": 2,
                      "y": 3
                  }
              }
          ],
          [
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      0,
                      1,
                      1
                  ],
                  "background": {
                      "color": {
                          "r": 245,
                          "g": 161,
                          "b": 127,
                          "a": 1
                      }
                  }
              },
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      0,
                      1,
                      0
                  ],
                  "background": {
                      "color": {
                          "r": 245,
                          "g": 161,
                          "b": 127,
                          "a": 1
                      }
                  }
              },
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      0,
                      1,
                      0
                  ],
                  "background": {
                      "color": {
                          "r": 245,
                          "g": 161,
                          "b": 127,
                          "a": 1
                      }
                  }
              },
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      0,
                      1,
                      0
                  ],
                  "background": {
                      "color": {
                          "r": 245,
                          "g": 161,
                          "b": 127,
                          "a": 1
                      }
                  }
              },
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      1,
                      1,
                      0
                  ],
                  "background": {
                      "color": {
                          "r": 245,
                          "g": 161,
                          "b": 127,
                          "a": 1
                      }
                  }
              }
          ],
          [
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      0,
                      1,
                      1
                  ],
                  "background": {
                      "color": {
                          "r": 255,
                          "g": 255,
                          "b": 255,
                          "a": 1
                      }
                  }
              },
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      0,
                      1,
                      0
                  ],
                  "background": {
                      "color": {
                          "r": 255,
                          "g": 255,
                          "b": 255,
                          "a": 1
                      }
                  }
              },
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      0,
                      1,
                      0
                  ],
                  "background": {
                      "color": {
                          "r": 255,
                          "g": 255,
                          "b": 255,
                          "a": 1
                      }
                  }
              },
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      0,
                      1,
                      0
                  ],
                  "background": {
                      "color": {
                          "r": 255,
                          "g": 255,
                          "b": 255,
                          "a": 1
                      }
                  }
              },
              {
                  "lineStyle": [
                      "solid",
                      "solid",
                      "solid",
                      "solid"
                  ],
                  "lineColor": [
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      },
                      {
                          "r": 218,
                          "g": 218,
                          "b": 218,
                          "a": 1
                      }
                  ],
                  "lineWidth": [
                      1,
                      1,
                      1,
                      0
                  ],
                  "background": {
                      "color": {
                          "r": 255,
                          "g": 255,
                          "b": 255,
                          "a": 1
                      }
                  }
              }
          ]
      ],
      "cellSize": {
          "col": [
              136,
              152,
              136,
              152,
              136
          ],
          "row": [
              56,
              56,
              56,
              56,
              56
          ]
      },
      "text": [
          [
              {
                  "content": [
                      "尺寸"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 600,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              },
              {
                  "content": [
                      "身高范围"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 600,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              },
              {
                  "content": [
                      "衣长"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 600,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              },
              {
                  "content": [
                      "肩宽"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 600,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              },
              {
                  "content": [
                      "袖长"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 600,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              }
          ],
          [
              {
                  "content": [
                      "S2"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 400,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              },
              {
                  "content": [
                      "155-160"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 400,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              },
              {
                  "content": [
                      "63"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 400,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              },
              {
                  "content": [
                      "37.51"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 400,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              },
              {
                  "content": [
                      "58.5"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 400,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              }
          ],
          [
              {
                  "content": [
                      "23"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 400,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              },
              {
                  "content": [
                      "160-165"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 400,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              },
              {
                  "content": [
                      "65"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 400,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              },
              {
                  "content": [
                      "37.52"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 400,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "zh158hmqchqq",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              },
              {
                  "content": [
                      "58.5"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 400,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              }
          ],
          [
              {
                  "content": [
                      "L"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 400,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              },
              {
                  "content": [
                      "165-170"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 400,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              },
              {
                  "content": [
                      "67"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 400,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              },
              {
                  "content": [
                      "12312"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 400,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              },
              {
                  "content": [
                      "58.5"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 400,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              }
          ],
          [
              {
                  "content": [
                      "XL"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 400,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              },
              {
                  "content": [
                      "170-175"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 400,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              },
              {
                  "content": [
                      "69"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 400,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              },
              {
                  "content": [
                      "37.5"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 400,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              },
              {
                  "content": [
                      "58.5"
                  ],
                  "alpha": 1,
                  "horizontalFlip": false,
                  "verticalFlip": false,
                  "fontSize": 36,
                  "fontWeight": 400,
                  "textAlign": "center",
                  "lineHeight": 1.3,
                  "fontFamily": "fnsyhtRegular",
                  "color": {
                      "r": 51,
                      "g": 51,
                      "b": 51,
                      "a": 1
                  }
              }
          ]
      ],
      "resId": "3"
  }
}