enum ETool {
    AI_PRODUCTS = 'aiProducts',
    AI_TEXT = 'aiText',
    AI_DRAW = 'aiDraw',
    AI_CUTOUT = 'aiCutout',
    AI_ENHANCE = 'aiEnhance',
    AI_ELIMINATE = 'aiEliminate',
    AI_ELIMINATEREDRAW = 'aiEliminateReDraw',
    AI_CHAT = 'aiChat',
    SPECIFIC_WORD = 'specificWord',
    SPECIFIC_WORD_3D = 'specificWord3D',
    MYPANEL = 'myPanel',
    SOURCE_MATERIAL = 'sourceMaterial',
    BACKGROUND = 'background',
    PIC = 'pic',
    TABLE_CHART = 'tableChart',
    GALLERY = 'gallery',
    COPYWRITING = 'copywriting',
    UPLOAD_FILE = 'uploadFile',
    TEAM_WORK = 'teamWork',
    TEMPLATE = 'template',
    TEMPLATE3D = 'template3D',
    TEMPLATEIns = 'templateIns',
    TEMPLATECartoon = 'templateCartoon',
    TEMPLATECharacters = 'templateCharacters',
    TEMPLATEESTATE = 'templateEstate',
    TEMPLATEBANK = 'templateBank',
    TEMPLATEBEAUTY = 'templateBeauty',
    TEMPLATEFINANCE = 'templateFinance',
    GROUP_WORD = 'groupWord',
    TOOL_TIK = 'toolTik',
    DESIGNER = 'designer',
    ANIMATION_EFFECT = 'animationEffect',
    VIDEOE = 'videoe',
    MUSIC = 'music',
    MENUMORE = 'menuMore',
    MEDICALMATERIAL = 'medicalMaterial',
    GoodNewsMaterial = 'goodNewsMaterial',
    FindJobMaterial = 'findJobMaterial',
    SchoolMaterial = 'schoolMaterial',
    WORDARTMATERIAL = 'wordartMaterial',
    ADDCANVAS = 'addCanvas',
    QRCODEPANELFN = "qrcodePanelFn",

    EMOJIPAGE = 'emojiPage',
    TENCENTMAPS = 'tencentMaps',
    BAIDUNETDISK = 'baiduNetdisk',
    FRAME = 'frame', // 相框
    ADD_MENU = 'addMenu',

}

export { ETool };
