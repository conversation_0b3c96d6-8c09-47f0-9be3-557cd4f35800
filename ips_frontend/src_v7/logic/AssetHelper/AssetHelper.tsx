import { DocHash } from '@v7_logic/DocHash';
import { DeepPartial, IAsset, IAssetInfo, ICanvas, IPageAttr, IPageInfo, IRichText, IText, IWork } from '@v7_logic/Interface';
import { IPaintOnCanvasState } from '@v7_store/redux/reducers/onCanvasPaintedReducer';
import { IPSConfig } from '@v7_utils/IPSConfig';
import { klona as cloneDeep } from 'klona';
import { plusAssetOffset } from '../AssetAddListener/FitPageDomSize';
import { GroupAndMultipleSelectLogic } from '@v7_logic/GroupAndMultipleSelectLogic';
import { IGroupMemberClassNames } from '@v7_logic/Interface/Asset';
import {generateShortUUID} from '@src/userComponentV6.0/Function'
import { ESelectType } from '@v7_logic/Enum';

// 不要在此文件引入 store, 会导致循环引用

interface IFindTarget {
    index: number; // 元素在当前 page 中的 index
    className?: string; // 校验, 如果 index 不准确, 则使用 className 查找
    pageIndex?: number; // 考虑到如果同时渲染多页的话
    [key: string]: any;
}

export class AssetHelper {
    /**
     * 查找单个 asset
     */
    public static find(work: IWork, pageInfo: IPageInfo, target: IFindTarget): IAsset | undefined {
        const pageIndex = target.pageIndex ?? pageInfo.pageNow;
        const page = work.pages[pageIndex];
        if (target.className) {
            if (page.assets[target.index]?.meta.className === target.className) {
                return page.assets[target.index];
            }
            const asset = page.assets.find((a) => a.meta.className === target.className);
            if (asset) {
                return asset;
            } else {
                for (const p of work.pages) {
                    for (const a of p.assets) {
                        if (a.meta.className === target.className) {
                            return a;
                        }
                    }
                }
            }
        } else {
            return page.assets[target.index]; // 如果 index > page.assets.length - 1, 返回 undefined
        }
        return undefined;
    }

    /**
     * 查找多个 asset, 根据 targets 传入顺序返回
     */
    public static findAll(work: IWork, pageInfo: IPageInfo, targets: IFindTarget[]): IAsset[] | undefined {
        const result: IAsset[] = [];
        const fail: { [key: string]: number } = {}; // { className: index }
        let failCount = 0;
        targets.forEach((t, i) => {
            const pageIndex = t.pageIndex ?? pageInfo.pageNow;
            const page = work.pages[pageIndex];
            if (t.className) {
                if (page.assets[t.index]?.meta.className === t.className) {
                    result[i] = page.assets[t.index];
                } else {
                    result[i] = undefined;
                    fail[t.className] = i;
                    failCount++;
                }
            } else {
                result[i] = page.assets[t.index];
            }
        });
        if (failCount > 0) {
            for (const p of work.pages) {
                for (const a of p.assets) {
                    if (fail[a.meta.className] >= 0) {
                        result[fail[a.meta.className]] = a;
                        failCount--;
                        if (failCount === 0) {
                            break;
                        }
                    }
                }
            }
        }
        return result.length > 0 ? result : undefined;
    }

    /**
     * 根据 hash 查找元素
     */
    public static findByHash(
        work: IWork,
        pageAttr: IPageAttr,
        target: { assetHash: string; pageHash: string },
    ): { pageIndex: number; index: number; asset: IAsset; assetHash: string; pageHash: string } {
        const pi = pageAttr.pageHash?.[target.pageHash];
        if (typeof pi === 'number' && pi >= 0) {
            const pageAssets = work.pages[pi].assets;
            const index = pageAssets.findIndex((a) => a.meta.hash === target.assetHash);
            if (index >= 0) {
                return {
                    pageIndex: pi,
                    index,
                    asset: pageAssets[index],
                    ...target,
                };
            }
        }
        return undefined;
    }

    /**
     * 根据 hash 查找元素（数组格式）
     */
    public static findAllByHash(
        work: IWork,
        pageAttr: IPageAttr,
        targets: { assetHash: string; pageHash: string }[],
    ): { pageIndex: number; index: number; asset: IAsset; assetHash: string; pageHash: string }[] {
        const sortTargetByPage: Record<
            keyof IPageAttr['pageHash'],
            {
                pi: number;
                assetsHash: IAsset['meta']['hash'][];
            }
        > = {};
        // 将 assetHash 根据 pageHash 归类
        targets.forEach((t) => {
            if (pageAttr.pageHash?.[t.pageHash] >= 0) {
                if (!sortTargetByPage[t.pageHash]) {
                    sortTargetByPage[t.pageHash] = {
                        pi: pageAttr.pageHash[t.pageHash],
                        assetsHash: [],
                    };
                }
                sortTargetByPage[t.pageHash].assetsHash.push(t.assetHash);
            }
        });
        const result: { pageIndex: number; index: number; asset: IAsset; assetHash: string; pageHash: string }[] = [];
        for (const pageHash in sortTargetByPage) {
            const pi = sortTargetByPage[pageHash].pi;
            if (typeof pi === 'number' && pi >= 0) {
                const pageAssets = work.pages[pi].assets;
                if (pageAssets.length > 0 && pageAssets[0].meta?.hash?.split('-')[0] !== pageHash) {
                    continue;
                } else {
                    pageAssets.forEach((a, index) => {
                        if (sortTargetByPage[pageHash].assetsHash.includes(a.meta.hash)) {
                            result.push({
                                pageIndex: pi,
                                index,
                                asset: a,
                                pageHash,
                                assetHash: a.meta.hash,
                            });
                        }
                    });
                }
            }
        }
        return result.length > 0 ? result : undefined;
    }

    /**
     * 只支持修改可 json 化的属性值
     */
    public static update(asset: IAsset, changes: DeepPartial<IAsset>): void {
        this.updateProperty(asset, changes);
    }

    // eslint-disable-next-line @typescript-eslint/ban-types
    private static updateProperty(target: object, changes: object) {
        for (const key in changes) {
            if (key !== '__proto__') {
                if (Array.isArray(changes[key])) {
                    target[key] = cloneDeep(changes[key]);
                } else if (Object.prototype.toString.call(changes[key]) === '[object Object]') {
                    if (Array.isArray(target[key])) {
                        target[key] = {};
                    }
                    target[key] ? this.updateProperty(target[key], changes[key]) : (target[key] = changes[key]);
                } else {
                    if (!((key === 'posX' || key === 'posY') && Number.isNaN(changes[key]))) {
                        target[key] = changes[key];
                    }
                }
            }
        }
    }

    /**
     * 改变 canvas size 时缩放元素
     */
    public static onCanvasSizeChange(width: number, height: number, canvas: ICanvas, work: IWork): void {
        const canvasAspectRatio = canvas.width / canvas.height;
        const updateAspectRatio = width / height;
        let ratio = 1;
        let offsetX = 0;
        let offsetY = 0;
        const cWidth = canvas.width;
        const cHeight = canvas.height;

        if (canvasAspectRatio - updateAspectRatio > 0) {
            ratio = width / canvas.width;
            offsetY = Math.abs(height - cHeight * ratio) / 2;
        } else if (canvasAspectRatio - updateAspectRatio < 0) {
            ratio = height / canvas.height;
            offsetX = Math.abs(width - cWidth * ratio) / 2;
        } else {
            ratio = width / canvas.width;
        }

        work.pages.forEach((p) => {
            p.assets.forEach((asset) => {
                if (asset?.meta?.type !== 'text') {
                    asset.attribute.height *= ratio;
                } else {
                    asset.attribute.fontSize *= ratio;
                    asset.attribute.letterSpacing *= ratio;

                    // 文字元素画布缩放适配
                    if (asset.attribute.text[0] instanceof Object) {
                        asset.attribute.text.forEach((item) => {
                            (item as IRichText ).fontSize *= ratio;
                            (item as IRichText ).letterSpacing *= ratio;
                        })
                    }
                }
                asset.attribute.width *= ratio;

                if (asset.attribute.container) {
                    asset.attribute.container.width *= ratio;
                    asset.attribute.container.height *= ratio;
                    
                    /**
                     * fix: 裁剪图片修改页面尺寸后，图片显示不正常
                    */
                    asset.attribute.container.viewBoxWidth *= ratio;
                    asset.attribute.container.viewBoxHeight *= ratio;

                    asset.attribute.container.posX *= ratio;
                    asset.attribute.container.posY *= ratio;

                }

                asset.transform.posX *= ratio;
                asset.transform.posY *= ratio;

                asset.transform.posX += offsetX;
                asset.transform.posY += offsetY;
            });
        });
    }

    /**
     * 获取 group 关联元素，包括 group 自身, 返回 { index: asset }
     */
    public static getGroupAssets(
        work: IWork,
        pageInfo: IPageInfo,
        groupKey: string,
        pageIndex?: number,
    ): Record<string, IAsset> {
        const pIndex = pageIndex ?? pageInfo.pageNow;
        const assets: Record<string, IAsset> = {};
        work.pages[pIndex].assets.forEach((a, i) => {
            if (a.meta.group === groupKey) {
                assets[i] = a;
            }
        });
        return assets;
    }

    /** 剪切板复制 */
    public static pasteAsset(state: IPaintOnCanvasState): IAsset[] {
        const { canvas, work, pageInfo, toolPanel, user, editorTag, copy } = state;
        // TODO 改成读取剪切板数据复制
        const getNewStateCopy = JSON.parse(localStorage.getItem('ue_copyAsset'));
        let newCopy = copy;
        if (getNewStateCopy && getNewStateCopy.userId === user.userId) {
            newCopy = getNewStateCopy;
        } else if (newCopy === '' || !newCopy) {
            return;
        }

        const isFromGif = /gif/.test(newCopy.editorTag);
        const isFromMovie = /movie/.test(newCopy.editorTag);
        if (isFromGif || isFromMovie) {
            // 图片只能从图片复制
            return;
        }

        const isMovieEditor = /movie/.test(editorTag);
        const params = IPSConfig.getProps();
        if (!params.isDesigner && isMovieEditor) {
            return;
        }
        const isSelf = newCopy.editorTag === editorTag;
        if (params.isDesigner && !isSelf && !user.COPY_TEMPLATE_USER) {
            return;
        }

        const copyAsset: {
            assets?: IAsset[];
            assetsInfo?: IAssetInfo;
            asset?: IAsset;
            assetsGroup?: IAsset[];
            userId?: string;
            picId?: string;
            createTime?: number;
            canvasTime?: number;
        } = cloneDeep(newCopy);

        let index, newAsset;

        const { assetOffsetX, assetOffsetY } = plusAssetOffset();

        const diff: IAsset[] = [];
        const lineMap = new Map<string, string>();

        if (copyAsset.assets && copyAsset.assets.length > 0) {
            const assetsInfo = copyAsset.assetsInfo;
            let assetsArr: IAsset[] = [];

            assetsInfo.posX += assetOffsetX;
            assetsInfo.posY += assetOffsetY;

            const diffPosX = newCopy.assetsInfo.posX - assetsInfo.posX;
            const diffPosY = newCopy.assetsInfo.posY - assetsInfo.posY;
            const copyGroup: IAsset[] = [];

            // 多选 既有group 又有其他元素的情况
            copyAsset.assets.map((item) => {
                const newItem = item;
                if (item.meta.type === 'group') {
                    newItem.transform.posX += assetOffsetX;
                    newItem.transform.posY += assetOffsetX;
                    copyGroup.push(item)
                } else {
                    newItem.transform.posX -= diffPosX;
                    newItem.transform.posY -= diffPosY;
                    // 先生成新的 uniqueId
                    assetsArr.push(newItem);
                    if (item.meta.uniqueId) {
                        lineMap.set(item.meta.uniqueId, generateShortUUID());
                    }
                }
            });

            const groupMembersMap = new Map<string, IGroupMemberClassNames>()
            copyGroup.forEach((item)=> {
                // let num = Number(JSON.stringify(work.nameSalt.salt));
                const newGroupName = item.meta.type + ++work.nameSalt.salt + '_' + new Date().getTime();
                const oldGroupName = item.meta.group;
                assetsArr.forEach((asset) => {
                    if (asset.meta.group === oldGroupName) {
                        asset.meta.group = newGroupName;
                    }
                })
                item.meta.group = newGroupName
                item.meta.memberClassNames = []
                groupMembersMap.set(newGroupName, item.meta.memberClassNames )
            })
            assetsArr = copyGroup.concat(assetsArr);
            toolPanel.assets_index = [];
            toolPanel.asset_index = -1;
            toolPanel.asset = undefined;
            const len = work.pages[pageInfo.pageNow].assets.length;
            assetsArr.map((item, i) => {
                item.meta.locked = false;
                item.meta.className = item.meta.type + ++work.nameSalt.salt + '_' + state.user.userId;
                item.meta.index += 10000;
                item.meta.rt_page_index = pageInfo.pageNow;
                item.meta.rt_page_assets_index = len + i;
                // 尝试给元素生成 hash, 如果不存在 pageAttr.pageHash 则无事发生
                item.meta.hash = undefined; // 由于是复制，先清除原先的 hash
                
                AssetHelper.pasteLineRelation(item, lineMap);
            
                DocHash.createAssetHash(state, pageInfo.pageNow, item);
                /* 记录变化 */
                diff.push(item);
                /* 记录变化 */
                work.pages[pageInfo.pageNow].assets.push(item);
                toolPanel.assets_index.push(work.pages[pageInfo.pageNow].assets.indexOf(item));

                // 更新group组中的值
                groupMembersMap.get(item.meta.group)?.push(item.meta.className)
            });
            assetsInfo.posX *= canvas.scale;
            assetsInfo.posY *= canvas.scale;
            assetsInfo.height *= canvas.scale;
            assetsInfo.width *= canvas.scale;
            toolPanel.assetsInfo = assetsInfo;
        } else if (copyAsset.asset?.meta?.type === 'group') {
            const asset = copyAsset.asset;
            const assetsArr: IAsset[] = [];

            asset.transform.posX += assetOffsetX;
            asset.transform.posY += assetOffsetY;

            let num = Number(JSON.stringify(work.nameSalt.salt));
            asset.meta.group = asset.meta.type + ++num + '_' + new Date().getTime();
            asset.meta.className = asset.meta.type + ++work.nameSalt.salt + '_' + state.user.userId;
            asset.meta.memberClassNames = [];

            const diffPosX = newCopy.asset.transform.posX - asset.transform.posX;
            const diffPosY = newCopy.asset.transform.posY - asset.transform.posY;

            copyAsset.assetsGroup.map((item) => {
                const newItem = item;
                newItem.meta.group = asset.meta.group;
                newItem.transform.posX -= diffPosX;
                newItem.transform.posY -= diffPosY;
                newItem.meta.className = newItem.meta.type + ++work.nameSalt.salt + '_' + state.user.userId;
                asset.meta.memberClassNames.push(newItem.meta.className);
                assetsArr.push(newItem);
                if (item.meta.uniqueId && newItem.meta.type !== 'group') {
                    lineMap.set(item.meta.uniqueId, generateShortUUID());
                }
            });
            assetsArr.push(asset);
            const len = work.pages[pageInfo.pageNow].assets.length;
            assetsArr.map((item, i) => {
                item.meta.locked = false;
                item.meta.index += 10000;
                item.meta.rt_page_index = pageInfo.pageNow;
                item.meta.rt_page_assets_index = len + i;
                // 尝试给元素生成 hash, 如果不存在 pageAttr.pageHash 则无事发生
                item.meta.hash = undefined; // 由于是复制，先清除原先的 hash
                // 处理连接线关系
                AssetHelper.pasteLineRelation(item, lineMap);
                DocHash.createAssetHash(state, pageInfo.pageNow, item);
                /* 记录变化 */
                diff.push(item);
                /* 记录变化 */
                work.pages[pageInfo.pageNow].assets.push(item);
                if (item.meta.type === 'group') {
                    toolPanel.asset_index = len + i;
                }else{
                    asset.meta.memberClassNames.push(item.meta.className)
                }
            });
        } else {
            const asset = copyAsset.asset;
            asset.transform.posX += assetOffsetX;
            asset.transform.posY += assetOffsetY;
            asset.meta.index += 100000;
            newAsset = cloneDeep(asset);
            newAsset.meta.locked = false;
            newAsset.meta.className = newAsset.meta.type + ++work.nameSalt.salt + '_' + state.user.userId;
            newAsset.meta.rt_page_index = pageInfo.pageNow;
            newAsset.meta.rt_page_assets_index = work.pages[pageInfo.pageNow].assets.length;
            // 尝试给元素生成 hash, 如果不存在 pageAttr.pageHash 则无事发生
            newAsset.meta.hash = undefined; // 由于是复制，先清除原先的 hash
            newAsset.meta.linkedLineIds = undefined;
            newAsset.meta.uniqueId = undefined
            DocHash.createAssetHash(state, pageInfo.pageNow, newAsset);
            /* 记录变化 */
            diff.push(newAsset);
            /* 记录变化 */
            toolPanel.assets_index = [];
            toolPanel.asset = newAsset;
            toolPanel.asset_index = work.pages[pageInfo.pageNow].assets.length;
            toolPanel.select_type = ESelectType.asset;
            work.pages[pageInfo.pageNow].assets.push(newAsset);
        }
        return diff;
    }

    static pasteCopyAsset(copyAsset: any, newCopy: any, state: IPaintOnCanvasState) {
        const { canvas, work, pageInfo, toolPanel } = state;
        const { assetOffsetX, assetOffsetY } = plusAssetOffset();
        const diff: IAsset[] = [];
        let newAsset;
        const lineMap = new Map<string, string>();
        if (copyAsset.assets && copyAsset.assets.length > 0) {
            const assetsInfo = copyAsset.assetsInfo;
            let assetsArr: IAsset[] = [];

            assetsInfo.posX += assetOffsetX;
            assetsInfo.posY += assetOffsetY;

            // const diffPosX = newCopy.assetsInfo.posX - assetsInfo.posX;
            // const diffPosY = newCopy.assetsInfo.posY - assetsInfo.posY;
            const copyGroup: IAsset[] = [];
            // 多选 既有group 又有其他元素的情况
            copyAsset.assets.map((item: IAsset) => {
                const newItem = item;
                if (item.meta.type === 'group') {
                    newItem.transform.posX += assetOffsetX;
                    newItem.transform.posY += assetOffsetX;
                    copyGroup.push(item)
                } else {
                    newItem.transform.posX -= assetOffsetX;
                    newItem.transform.posY -= assetOffsetX;
                    assetsArr.push(newItem);
                    if (item.meta.uniqueId) {
                        lineMap.set(item.meta.uniqueId, generateShortUUID());
                    }
                }
            });
            const groupMembersMap = new Map<string, IGroupMemberClassNames>()
            copyGroup.forEach((item)=> {
                // let num = Number(JSON.stringify(work.nameSalt.salt));
                const newGroupName = item.meta.type + ++work.nameSalt.salt + '_' + new Date().getTime();
                const oldGroupName = item.meta.group;
                assetsArr.forEach((asset) => {
                    if (asset.meta.group === oldGroupName) {
                        asset.meta.group = newGroupName;
                    }
                })
                item.meta.group = newGroupName
                item.meta.memberClassNames = []
                groupMembersMap.set(newGroupName, item.meta.memberClassNames )
            })
            assetsArr = copyGroup.concat(assetsArr);
            toolPanel.assets_index = [];
            toolPanel.asset_index = -1;
            toolPanel.asset = undefined;
            const len = work.pages[pageInfo.pageNow].assets.length;
            assetsArr.map((item, i) => {
                item.meta.locked = false;
                item.meta.className = item.meta.type + ++work.nameSalt.salt + '_' + state.user.userId;
                item.meta.index += 10000;
                item.meta.rt_page_index = pageInfo.pageNow;
                item.meta.rt_page_assets_index = len + i;
                // 尝试给元素生成 hash, 如果不存在 pageAttr.pageHash 则无事发生
                item.meta.hash = undefined; // 由于是复制，先清除原先的 hash
                // 处理连接关系
                AssetHelper.pasteLineRelation(item, lineMap);
                DocHash.createAssetHash(state, pageInfo.pageNow, item);
                /* 记录变化 */
                diff.push(item);
                /* 记录变化 */
                work.pages[pageInfo.pageNow].assets.push(item);
                toolPanel.assets_index.push(work.pages[pageInfo.pageNow].assets.indexOf(item));

                // 更新group组中的值
                groupMembersMap.get(item.meta.group)?.push(item.meta.className)
            });
            assetsInfo.posX *= canvas.scale;
            assetsInfo.posY *= canvas.scale;
            assetsInfo.height *= canvas.scale;
            assetsInfo.width *= canvas.scale;
            toolPanel.assetsInfo = assetsInfo;
        } else if (copyAsset.asset?.meta?.type === 'group') {
            const asset = copyAsset.asset;
            const assetsGroup: IAsset[] = [];
            work.pages[pageInfo.pageNow].assets.forEach((a) => {
                if (a.meta.type !== 'group' && a.meta.group === asset.meta.group) {
                    const cloneAsset = cloneDeep(a);
                    cloneAsset.meta.group = '';
                    assetsGroup.push(cloneAsset);
                    if (a.meta.uniqueId) {
                        lineMap.set(a.meta.uniqueId, generateShortUUID());
                    }
                }
            });
            const assetsArr: IAsset[] = [];

            asset.transform.posX += assetOffsetX;
            asset.transform.posY += assetOffsetY;

            let num = Number(JSON.stringify(work.nameSalt.salt));
            asset.meta.group = asset.meta.type + ++num + '_' + new Date().getTime();
            asset.meta.className = asset.meta.type + ++work.nameSalt.salt + '_' + state.user.userId;
            asset.meta.memberClassNames = [];

            // const diffPosX = newCopy.asset.transform.posX - asset.transform.posX;
            // const diffPosY = newCopy.asset.transform.posY - asset.transform.posY;

            assetsGroup.map((item: IAsset) => {
                const newItem = item;
                newItem.meta.group = asset.meta.group;
                newItem.transform.posX -= assetOffsetX;
                newItem.transform.posY -= assetOffsetX;
                newItem.meta.className = newItem.meta.type + ++work.nameSalt.salt + '_' + state.user.userId;
                asset.meta.memberClassNames.push(newItem.meta.className);
                assetsArr.push(newItem);
            });
            assetsArr.push(asset);
            const len = work.pages[pageInfo.pageNow].assets.length;
            assetsArr.map((item, i) => {
                item.meta.locked = false;
                item.meta.index += 10000;
                item.meta.rt_page_index = pageInfo.pageNow;
                item.meta.rt_page_assets_index = len + i;
                // 尝试给元素生成 hash, 如果不存在 pageAttr.pageHash 则无事发生
                item.meta.hash = undefined; // 由于是复制，先清除原先的 hash
                // 处理连接线关系
                AssetHelper.pasteLineRelation(item, lineMap);
                DocHash.createAssetHash(state, pageInfo.pageNow, item);
                /* 记录变化 */
                diff.push(item);
                /* 记录变化 */
                work.pages[pageInfo.pageNow].assets.push(item);
                if (item.meta.type === 'group') {
                    toolPanel.asset_index = len + i;
                }else{
                    asset.meta.memberClassNames.push(item.meta.className)
                }
            });
        } else {
            const asset = copyAsset.asset;
            asset.transform.posX += assetOffsetX;
            asset.transform.posY += assetOffsetY;
            asset.meta.index += 100000;
            newAsset = cloneDeep(asset);
            newAsset.meta.locked = false;
            newAsset.meta.className = newAsset.meta.type + ++work.nameSalt.salt + '_' + state.user.userId;
            newAsset.meta.rt_page_index = pageInfo.pageNow;
            newAsset.meta.rt_page_assets_index = work.pages[pageInfo.pageNow].assets.length;
            // 尝试给元素生成 hash, 如果不存在 pageAttr.pageHash 则无事发生
            newAsset.meta.hash = undefined; // 由于是复制，先清除原先的 hash
            newAsset.meta.linkedLineIds = undefined;
            newAsset.meta.uniqueId = undefined;
            if (newAsset.meta.group) {
                delete newAsset.meta.group;
            }
            DocHash.createAssetHash(state, pageInfo.pageNow, newAsset);
            /* 记录变化 */
            diff.push(newAsset);
            /* 记录变化 */
            toolPanel.assets_index = [];
            toolPanel.asset = newAsset;
            toolPanel.asset_index = work.pages[pageInfo.pageNow].assets.length;
            work.pages[pageInfo.pageNow].assets.push(newAsset);
        }
        return diff;
    }

    /** 工具栏复制 */
    public static copyToolPanelAsset(state: IPaintOnCanvasState): IAsset[] {
        const { work, pageInfo, toolPanel } = state;
        const currentPageAssets = work.pages[pageInfo.pageNow].assets;
        const diff: IAsset[] = [];
        return AssetHelper.pasteCopyAsset(toolPanel, toolPanel, state);
        // if (typeof toolPanel.asset_index === 'number' && toolPanel.asset_index >= 0) {
        //     const target = currentPageAssets[toolPanel.asset_index];
        //     const grounpName = target.meta.group;
       
        //     const newAsset = cloneDeep(target);
        //     newAsset.meta.className =
        //         newAsset.meta.type + ++work.nameSalt.salt + '_' + state.user.userId + '_' + new Date().getTime();
        //     newAsset.transform.posX += 10;
        //     newAsset.transform.posY += 10;
        //     currentPageAssets.push(newAsset);
        //     const index = currentPageAssets.length - 1;
        //     currentPageAssets[index].meta.index = index;
        //     currentPageAssets[index].meta.rt_page_assets_index = index;
        //     toolPanel.asset_index = index;
        //     // 尝试给元素生成 hash, 如果不存在 pageAttr.pageHash 则无事发生
        //     newAsset.meta.hash = undefined; // 由于是复制，先清除原先的 hash
        //     DocHash.createAssetHash(state, pageInfo.pageNow, currentPageAssets[index]);
        //     /* 记录变化 */
        //     diff.push(currentPageAssets[index]);
        //     // if(grounpName && target.type !=='group'){
        //     //     const groupAsset = currentPageAssets.find((asset)=>{
        //     //         return asset.meta.type == 'group' && asset.meta.group == grounpName
        //     //     })
        //     //     groupAsset.meta.memberClassNames?.push(newAsset.meta.className)
        //     // }
        //     /* 记录变化 */
        //     if (newAsset.meta.type === 'group') {
        //         newAsset.meta.group = newAsset.meta.className;
        //         newAsset.meta.memberClassNames = []
        //         const limiteNum = currentPageAssets.length;
        //         let count = 0;
        //         for (let i = 0; i < limiteNum; i++) {
        //             if (grounpName === currentPageAssets[i].meta.group && currentPageAssets[i].meta.type !== 'group') {
        //                 const a = cloneDeep(currentPageAssets[i]);
        //                 a.meta.className = a.meta.type + ++work.nameSalt.salt + '_' + state.user.userId;
        //                 a.meta.group = newAsset.meta.group;
        //                 a.transform.posX += 10;
        //                 a.transform.posY += 10;
        //                 a.meta.index += 1000000;
        //                 a.meta.rt_page_assets_index = index + ++count;
        //                 newAsset.meta.memberClassNames.push(a.meta.className)
        //                 // 尝试给元素生成 hash, 如果不存在 pageAttr.pageHash 则无事发生
        //                 DocHash.createAssetHash(state, pageInfo.pageNow, a);
        //                 /* 记录变化 */
        //                 diff.push(a);
        //                 /* 记录变化 */
        //                 currentPageAssets.push(a);
        //             }
        //         }
        //     }
          
        // } else if (toolPanel.assets_index.length > 0) {
        //     return AssetHelper.pasteCopyAsset(toolPanel, toolPanel, state);
        //     let index, newAsset;
        //     const assetList = [];
        //     for (let i = 0; i < toolPanel.assets_index.length; i++) {
        //         newAsset = cloneDeep(currentPageAssets[toolPanel.assets_index[i]]);
        //         newAsset.meta.className = newAsset.meta.type + ++work.nameSalt.salt + '_' + state.user.userId;
        //         newAsset.transform.posX += 10;
        //         newAsset.transform.posY += 10;
        //         currentPageAssets.push(newAsset);
        //         index = currentPageAssets.length - 1;
        //         currentPageAssets[index].meta.index = index;
        //         currentPageAssets[index].meta.rt_page_assets_index = index;
        //         assetList.push(index);
        //         // 尝试给元素生成 hash, 如果不存在 pageAttr.pageHash 则无事发生
        //         DocHash.createAssetHash(state, pageInfo.pageNow, currentPageAssets[index]);
        //         /* 记录变化 */
        //         diff.push(currentPageAssets[index]);
        //         /* 记录变化 */
        //     }
        //     toolPanel.assets_index = assetList;
        // }
        // return diff;
    }

    /** 处理粘贴连接线关系 */
    public static pasteLineRelation(item: IAsset, lineMap: Map<string, string>): void {
        if (item.meta.linkedLineIds) {
            item.meta.linkedLineIds = item.meta.linkedLineIds?.map(item=> lineMap.has(item) && lineMap.get(item));
        } else {
            item.meta.linkedLineIds = undefined;
        }
        if (item.meta.uniqueId && lineMap.has(item.meta.uniqueId)) {
            item.meta.uniqueId = lineMap.get(item.meta.uniqueId);
        } else {
            item.meta.uniqueId = undefined;
        };
        if (item.meta.type === 'line') {
            const startObj = item.attribute.startObj;
            const endObj = item.attribute.endObj;
            if (startObj.id && lineMap.has(startObj.id)) {
                item.attribute.startObj.id = lineMap.get(item.attribute.startObj.id);
            }
            if (endObj.id && lineMap.has(endObj.id)) {
                item.attribute.endObj.id = lineMap.get(item.attribute.endObj.id);
            }
        }
    }

    /** 根据 className 查找 */
    public static findByClassName(
        work: IWork,
        pageInfo: IPageInfo,
        target: { className: string; pageIndex?: number },
    ): IAsset | undefined {
        const pageIndex = target.pageIndex ?? pageInfo.pageNow;
        const page = work.pages[pageIndex];
        return page.assets.find((a) => a.meta.className === target.className);
    }

    /**
     * 根据 className 查找索引
     * 如果不存在为 -1
     */
    public static getIndex(
        work: IWork,
        pageInfo: IPageInfo,
        target: {
            className: string;
            pageIndex?: number;
        },
    ): number {
        const pageIndex = target.pageIndex ?? pageInfo.pageNow;
        const page = work.pages[pageIndex];
        return page.assets.findIndex((a) => a.meta.className === target.className);
    }

    /**
     * 根据 className 数组查找索引
     * 返回与传入顺序对应的 index 数组
     * 如果不存在, index 为 -1
     */
    public static getIndexs(
        work: IWork,
        pageInfo: IPageInfo,
        targets: {
            className: string;
            pageIndex?: number;
        }[],
    ): number[] {
        const result: number[] = [];
        const pAssets: Record<string, Record<string, number>> = {};
        targets.forEach((t, i) => {
            const pageIndex = t.pageIndex ?? pageInfo.pageNow;
            pAssets[pageIndex]
                ? (pAssets[pageIndex][t.className] = i)
                : (pAssets[pageIndex] = {
                      [t.className]: i,
                  });
            result[i] = -1; // 先标记成不存在，如果找到了对应 asset 则用索引覆盖
        });
        Object.entries(pAssets).forEach(([pageIndex, assets]) => {
            const pageAssets = work.pages[Number(pageIndex)].assets;
            const classNames = Object.keys(assets);
            for (const i in pageAssets) {
                if (classNames.includes(pageAssets[i].meta.className)) {
                    result[assets[pageAssets[i].meta.className]] = Number(i);
                }
            }
        });
        return result;
    }

    /** 元素 css index 排序 */
    public static moveAssetIndex(
        state: IPaintOnCanvasState,
        params: {
            type: 'up' | 'upZ' | 'down' | 'downZ';
            indexMin?: number | undefined;
            indexMax?: number | undefined;
        },
    ): void {
        const { work, pageInfo, toolPanel } = state;
        const pageAssets = work.pages[pageInfo.pageNow].assets;
        let asset: IAsset | null = null;
        if (typeof toolPanel.asset_index === 'number' && toolPanel.asset_index >= 0) {
            const selectedAsset = pageAssets[toolPanel.asset_index];
            if(selectedAsset.meta.type != 'group' && selectedAsset.meta.group) {//保持组合内元素与组合行为一致
                asset = pageAssets.find(a => a.meta.type == 'group' && a.meta.group == selectedAsset.meta.group);
            } else {
                asset = selectedAsset;
            }
        }
        const addIndex = 1000000;
        // TODO 减少循环次数
        switch (params.type) {
            case 'up': {
                if (asset) {
                    if (asset.meta.type === 'group') {
                        let indexMax = 0,
                            tempIndex;
                        for (let i = 0; i < pageAssets.length; i++) {
                            if (asset.meta.group === pageAssets[i].meta.group) {
                                if (indexMax < pageAssets[i].meta.index) {
                                    indexMax = pageAssets[i].meta.index;
                                }
                            }
                        }
                        tempIndex = indexMax;
                        for (let i = 0; i < pageAssets.length; i++) {
                            if (indexMax < pageAssets[i].meta.index) {
                                if (tempIndex > pageAssets[i].meta.index) {
                                    tempIndex = pageAssets[i].meta.index;
                                } else if (indexMax === tempIndex) {
                                    tempIndex = pageAssets[i].meta.index;
                                }
                            }
                        }
                        indexMax = tempIndex;
                        for (let i = 0; i < pageAssets.length; i++) {
                            if (asset.meta.group === pageAssets[i].meta.group) {
                                pageAssets[i].meta.index += addIndex;
                            } else if (indexMax < pageAssets[i].meta.index) {
                                pageAssets[i].meta.index += addIndex;
                            }
                        }
                    } else {
                        let indexMax = 0,
                            grounpName = '',
                            grounpIndexMax = 0;
                        for (let i = pageAssets.length - 1; i >= 0; i--) {
                            if (indexMax <= pageAssets[i].meta.index) {
                                indexMax = pageAssets[i].meta.index;
                            }
                        }
                        if (indexMax <= asset.meta.index) {
                            break;
                        }
                        for (let i = pageAssets.length - 1; i >= 0; i--) {
                            if (pageAssets[i].meta.index === asset.meta.index + 1) {
                                if (pageAssets[i].meta.group !== '') {
                                    grounpName = pageAssets[i].meta.group;
                                    break;
                                }
                                pageAssets[i].meta.index = asset.meta.index;
                                break;
                            }
                        }
                        if (grounpName !== '') {
                            for (let i = 0; i < pageAssets.length; i++) {
                                if (grounpName === pageAssets[i].meta.group) {
                                    if (grounpIndexMax < pageAssets[i].meta.index) {
                                        grounpIndexMax = pageAssets[i].meta.index;
                                    }
                                }
                            }
                            for (let i = 0; i < pageAssets.length; i++) {
                                if (grounpIndexMax < pageAssets[i].meta.index) {
                                    pageAssets[i].meta.index += addIndex;
                                }
                            }
                            asset.meta.index += addIndex;
                        } else {
                            asset.meta.index += 1;
                        }
                    }
                }

                break;
            }

            case 'upZ': {
                if (asset) {
                    if (asset.meta.type === 'group') {
                        for (let i = 0; i < pageAssets.length; i++) {
                            if (asset.meta.group === pageAssets[i].meta.group) {
                                pageAssets[i].meta.index += addIndex;
                            }
                        }
                    } else {
                        const hasIndexMax = params.indexMax !== undefined;
                        let indexMax = hasIndexMax ? params.indexMax : 0;

                        for (let i = pageAssets.length - 1; i >= 0; i--) {
                            if (!hasIndexMax && indexMax <= pageAssets[i].meta.index) {
                                indexMax = pageAssets[i].meta.index;
                            }

                            if (pageAssets[i].meta.index > asset.meta.index) {
                                pageAssets[i].meta.index -= 1;
                            }
                        }

                        asset.meta.index = indexMax;
                    }
                }
                break;
            }

            case 'down': {
                if (asset) {
                    if (asset.meta.type === 'group') {
                        let indexMin = 9999999,
                            tempIndex;
                        for (let i = 0; i < pageAssets.length; i++) {
                            // 找组合中最大的
                            if (asset.meta.group === pageAssets[i].meta.group) {
                                if (indexMin > pageAssets[i].meta.index) {
                                    indexMin = pageAssets[i].meta.index;
                                }
                            }
                        }
                        tempIndex = indexMin;
                        for (let i = 0; i < pageAssets.length; i++) {
                            if (indexMin > pageAssets[i].meta.index) {
                                if (tempIndex < pageAssets[i].meta.index) {
                                    tempIndex = pageAssets[i].meta.index;
                                } else if (tempIndex === indexMin) {
                                    tempIndex = pageAssets[i].meta.index;
                                }
                            }
                        }
                        indexMin = tempIndex;
                        for (let i = 0; i < pageAssets.length; i++) {
                            if (asset.meta.group === pageAssets[i].meta.group) {
                                pageAssets[i].meta.index -= addIndex;
                            } else if (indexMin > pageAssets[i].meta.index) {
                                pageAssets[i].meta.index -= addIndex;
                            }
                        }
                    } else {
                        let indexMin = 9999999,
                            grounpName = '',
                            grounpIndexMin = 9999999;

                        for (let i = pageAssets.length - 1; i >= 0; i--) {
                            if (indexMin >= pageAssets[i].meta.index) {
                                indexMin = pageAssets[i].meta.index;
                            }
                        }
                        if (indexMin >= asset.meta.index) {
                            break;
                        }
                        for (let i = pageAssets.length - 1; i >= 0; i--) {
                            if (pageAssets[i].meta.index === asset.meta.index - 1) {
                                if (pageAssets[i].meta.group !== '') {
                                    grounpName = pageAssets[i].meta.group;
                                    break;
                                }
                                pageAssets[i].meta.index = asset.meta.index;
                                break;
                            }
                        }
                        if (grounpName !== '') {
                            for (let i = 0; i < pageAssets.length; i++) {
                                if (grounpName === pageAssets[i].meta.group) {
                                    if (grounpIndexMin > pageAssets[i].meta.index) {
                                        grounpIndexMin = pageAssets[i].meta.index;
                                    }
                                }
                            }
                            for (let i = 0; i < pageAssets.length; i++) {
                                if (grounpIndexMin > pageAssets[i].meta.index) {
                                    pageAssets[i].meta.index -= addIndex;
                                }
                            }
                            asset.meta.index -= addIndex;
                        } else {
                            asset.meta.index -= 1;
                        }
                    }
                }
                break;
            }

            case 'downZ': {
                if (asset) {
                    const hasIndexMin = params.indexMin !== undefined;
                    let indexMin = hasIndexMin ? params.indexMin : 9999999;
                    for (let i = pageAssets.length - 1; i >= 0; i--) {
                        if (!hasIndexMin && indexMin >= pageAssets[i].meta.index) {
                            indexMin = pageAssets[i].meta.index;
                        }

                        if (pageAssets[i].meta.type !== 'background' && pageAssets[i].meta.index < asset.meta.index) {
                            pageAssets[i].meta.index += 1;
                        }
                    }
                    asset.meta.index = indexMin;
                    if(asset.meta.type == 'group'){
                        for (let i = 0; i < pageAssets.length; i++) {
                            if (asset.meta.group === pageAssets[i].meta.group) {
                                pageAssets[i].meta.index = indexMin;
                            }
                        }
                    }
                }
                break;
            }

            default:
                break;
        }
    }

    /** 滑动条排序 */
    public static moveAssetIndexNew(
        state: IPaintOnCanvasState,
        params: {
            assetArr: { index: number }[];
        },
    ): void {
        const {
            work,
            pageInfo: { pageNow },
        } = state;
        params.assetArr.forEach((item, index) => {
            work.pages[pageNow].assets[index].meta.index = item.index;
        });
    }
    /**
     * 查找当前页面的第一个image元素
     * @param work 
     * @param pageInfo 
     */
    public static findAssetByType(work:IWork,pageInfo: IPageInfo,target:{
        pageIndex?:number;
        assetType:string | string[]
    }){
        const pageIndex =target?.pageIndex ?? pageInfo.pageNow;
        const page = work.pages[pageIndex];
        return page.assets.find((asset)=>{
            if(typeof target.assetType == 'string') return asset.meta.type == target.assetType && asset.meta.delete == 0
            return target.assetType.includes(asset.meta.type) && asset.meta.delete == 0
        })
    }

    /**
     * 计算元素的最大区域
     */
    public static countAssetMaxArea(asset: IAsset) {
        const t = {
            width: asset.attribute.width,
            height: asset.attribute.height,
            cx: asset.transform.posX + asset.attribute.width / 2,
            cy: asset.transform.posY + asset.attribute.height / 2,
            angle: asset.transform.rotate
        }
        const tMartix = 
        fabric.util.composeMatrix({
            translateX: t.cx,
            translateY: t.cy,
            angle: t.angle,
            scaleX: 1,
            scaleY: 1,
            skewX: 0,
            skewY: 0,
            flipX: false,
            flipY: false
        })
        const points = ([
            [-t.width / 2, -t.height / 2] as const,
            [t.width / 2, -t.height / 2] as const,
            [-t.width / 2, t.height / 2] as const,
            [t.width / 2, t.height / 2] as const
        ]).map((item) => fabric.util.transformPoint(new fabric.Point(...item), tMartix))
        const xList = points.map(item => item.x)
        const yList = points.map(item => item.y)
        return {
            l: Math.min(...xList),
            r: Math.max(...xList),
            t: Math.min(...yList),
            b: Math.max(...yList)
        }
    }
}
