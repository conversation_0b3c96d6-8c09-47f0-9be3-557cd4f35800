import { ListenerEventInterface } from '@v7_logic/Interface';
import { emitter } from '@component/Emitter';
import { EventSubscription } from 'fbemitter';
import React from 'react';

import {storeAdapter} from '@v7_logic_core/StoreAdapter';
import {addEventListener, EventListener} from '@v7_utils/AddEventListener';
import { assetManager } from '@src/userComponentV6.0/AssetManager';


class CanvasGrabMoveListener implements ListenerEventInterface {
    private listener_obj: EventSubscription;

    /**
     * 监听事件建立
     */
    public listenerEvent = (): (() => void) => {
        this.addListener();

        return this.unListenerEvent;
    };

    /**
     * 监听事件销毁
     */
    public unListenerEvent = (): void => {
        this.delListener();
    };

    clientX: number;
    clientY: number;
    canvasLeft: number;
    canvasTop: number;
    canvasWidth: number;
    canvasHeight: number;
    canvasWrapperWidth: number;
    canvasWrapperHeight: number;
    showOffset: number;
    canvasGrabMoveListener: EventListener;
    canvasGrabUpListener: EventListener;
    isFirstMove: number;

    /**
     * 画布抓取（移动事件）
     */
    addListener = (): void => {
        this.listener_obj = emitter.addListener('canvasContentCanvasGrabMove', (e: React.MouseEvent) => {
            const {canvas, canvas_dom_info, canvas_wrapper_dom_info, rt_canvas_render_mode} = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            
            
            if (!canvas_dom_info.is_exist || !canvas_wrapper_dom_info.is_exist) {
                return ;
            }
    
            const canvasContentStyle = document.querySelector('.' + canvas_dom_info.class_name) as HTMLDivElement,
                canvasWrapperDom = document.querySelector('.' + canvas_wrapper_dom_info.class_name) as HTMLDivElement;

            this.clientX = e.clientX;
            this.clientY = e.clientY;
            this.canvasLeft = canvas.x; // parseInt(canvasContentStyle.style.left);
            this.canvasTop = canvas.y; // parseInt(canvasContentStyle.style.top);
            this.canvasWidth = canvas.width * canvas.scale;
            this.canvasHeight = canvas.height * canvas.scale;
            this.canvasWrapperWidth = canvasWrapperDom.clientWidth;
            this.canvasWrapperHeight = canvasWrapperDom.clientHeight;
            this.showOffset = 50;

            if( this.canvasGrabMoveListener ){
                this.canvasGrabMoveListener.remove();
            }

            if( this.canvasGrabUpListener ){
                this.canvasGrabUpListener.remove();
            }
            this.isFirstMove = 1;
            this.canvasGrabMoveListener = addEventListener(window, 'mousemove', (e: MouseEvent) => {
                const offsetX = e.clientX - this.clientX,
                    offsetY = e.clientY - this.clientY;
                let canvasLeft = this.canvasLeft + offsetX,
                    canvasTop = this.canvasTop + offsetY;

                if (rt_canvas_render_mode !== 'board') {
                    /*边界判断START*/
                        //左边
                        canvasLeft = canvasLeft > -(this.canvasWidth - this.showOffset) ? canvasLeft : -(this.canvasWidth - this.showOffset);
                        //上边
                        canvasTop = canvasTop > -(this.canvasHeight - this.showOffset) ? canvasTop : -(this.canvasHeight - this.showOffset);
                        //右边
                        canvasLeft = canvasLeft < (this.canvasWrapperWidth - this.showOffset) ? canvasLeft : (this.canvasWrapperWidth - this.showOffset);
                        //下边
                        canvasTop = canvasTop < (this.canvasWrapperHeight - this.showOffset) ? canvasTop : (this.canvasWrapperHeight - this.showOffset);
                    /*边界判断END*/
                }

                // this.setState(Object.assign(canvasContentStyle, {
                //     left: canvasLeft,
                //     top: canvasTop
                // }));
                storeAdapter.dispatch({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                    fun_name: 'UPDATE_RESIZE_CANVAS',
                    params: [
                        {
                            type: 'updateCanvasInfo',
                            params: {
                                canvas: {
                                    x: canvasLeft,
                                    y: canvasTop
                                },
                            },
                        },
                    ],
                });

                if( this.isFirstMove ){
                    this.isFirstMove = 0;
                    assetManager.setPv_new(183, {additional: {

                    }});
                }
            });

            this.canvasGrabUpListener = addEventListener(window, 'mouseup', (e) => {
                this.canvasGrabUpListener.remove();
                this.canvasGrabMoveListener.remove();
            });
        });
    }

    delListener(): void {
        this.listener_obj.remove();
    }
}

const canvasGrabMoveListener = new CanvasGrabMoveListener();
export { canvasGrabMoveListener as CanvasGrabMoveListener };