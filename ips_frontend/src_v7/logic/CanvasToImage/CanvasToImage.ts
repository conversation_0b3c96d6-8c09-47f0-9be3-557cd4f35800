import { TgsCanvas } from '@tgs/canvas';
import { Decorator, IpsUtils } from '@tgs/utils';
import { IConvertPageToImageOptions } from '@tgs/types';
import { assetManager } from '@src/userComponentV6.0/AssetManager';

export class CanvasToImage {
    static lastUploadTime = 0;
    private static convertCanvasToImage(canvas: TgsCanvas, options: IConvertPageToImageOptions) {
        if (!canvas || !canvas.convertPageToImage) return '';
        const { format = 'jpeg', quality = 1, index = 0, enableRetinaScaling = true } = options;
        const dataUrl = canvas?.convertPageToImage({ format, quality, index, enableRetinaScaling });
        // 确保返回有效的 base64 字符串
        return dataUrl || '';
    }
    public static exportCanvasPoster(canvas: TgsCanvas, options: IConvertPageToImageOptions) {
        if (!canvas || !options) return;
        if (options.handleSave) {
            this.main(canvas, options);
        } else {
            this.debounceMain(canvas, options);
        }
    }
    private static main(canvas: TgsCanvas, options: IConvertPageToImageOptions) {
        this.startTask(canvas, options);
    }
    /**
     * @description: 自动保寸30s传一次
     * @return {*}
     */
    @Decorator.Performance.Debounce(1000 * 20, { trailing: true })
    private static debounceMain(canvas: TgsCanvas, options: IConvertPageToImageOptions) {
        this.main(canvas, options);
    }
    @Decorator.Performance.IdleCallback
    private static startTask(canvas: TgsCanvas, options: IConvertPageToImageOptions) {
        const blob = this.getImageBlob(canvas, options);
        if (!blob) return;
        this.upload(blob, options);
    }
    private static getImageBlob(canvas: TgsCanvas, options: IConvertPageToImageOptions) {
        const dataUrl = this.convertCanvasToImage(canvas, options);
        if (!dataUrl) return null;
            const imageBlob = IpsUtils.File.base64ToBlob(dataUrl);
            return imageBlob;
    }
    private static upload(blob: Blob, options: IConvertPageToImageOptions) {
        const formData = new FormData();
        const file = new File([blob], 'poster.png', { type: blob.type });
        const {
            paperId = '0',
            upicId = '0',
            user_template_team_id = '0',
            version_id = '0',
            isDesigner = '0',
            picId,
        } = IpsUtils.Url.getUrlParams(window.location.search);
        let prefix = 'ips_user_preview_api';
        if (isDesigner === '1') {
            prefix = 'ips_template_preview_api';
        } if (user_template_team_id !== '0') {
            prefix = 'user_preview_team_ue_api';
        } else if (version_id !== '0') {
            prefix = 'ips_user_preview_versions_api';
        } else if (paperId !== '0') {
            prefix = 'ips_user_preview_paper_api';
        }
        assetManager
            .getOssUploadPreviewToken(blob.type, prefix)
            .then((data) => {
                data.json().then((resultData) => {
                    if (resultData.stat == 1) {
                        const data = resultData.msg,
                            openUrl = '//' + data.bucket + '.' + data.endpoint;

                        formData.append('OSSAccessKeyId', data.id);
                        formData.append('policy', data.base64policy);
                        formData.append('Signature', data.signature);
                        formData.append('key', data.key);
                        formData.append('callback', data.base64callback_body);
                        formData.append('file', file);
                        const xhr = new window.XMLHttpRequest();
                        xhr.onload = () => {
                            if (xhr.readyState === xhr.DONE) {
                                if (xhr.status === 200) {
                                    const res = JSON.parse(xhr.response);
                                    if (res.stat === 1) {
                                        const path = res.msg.path;
                                        const uploadFormData = new FormData();
                                        uploadFormData.append('previewUrl', path);
                                        uploadFormData.append(
                                            'ut_id',
                                            user_template_team_id != '0' ? '0' : (upicId as string),
                                        );
                                        uploadFormData.append('utt_id', user_template_team_id as string);
                                        uploadFormData.append('utv_id', version_id as string);
                                        uploadFormData.append('paperId', paperId as string);
                                        if (picId && isDesigner === '1') {
                                            uploadFormData.append('tid', picId as string);
                                        }
                                        assetManager.putPreviewPoster(uploadFormData).then((res: any) => {
                                            res.json().then((data: any) => {
                                                // console.log(data);
                                                options.callback?.(path);
                                            });
                                        });
                                    }
                                    if (res.stat === -300 || res.stat === -500) {
                                        return;
                                    }
                                }
                            }
                        };
                        xhr.withCredentials = true; //发送跨域cookie
                        xhr.open('post', openUrl, true);
                        xhr.send(formData);
                    }
                });
            })
            .catch((err) => {
                console.error(err);
            });
    }
}
