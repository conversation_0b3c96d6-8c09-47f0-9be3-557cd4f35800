import { IAiDesignPageTagTypeEnum } from '@tgs/types';
import { IPageAnimation } from './Animation';
import { IPageAudio } from './Audio';
import { IColor } from './Color';
import { IGifInfo } from './GifInfo';

export type TPageType = 'board' | ''

export interface IPageAttr {
    backgroundColor?: IColor[];
    backgroundImage?: {
        resId?: string;
        rt_imageUrl?: string;
        backgroundSize?: {
            width: string | number;
            height: string | number;
        };
        [key: string]: any;
    }[];
    backgroundOpacity?: boolean[];
    pageInfo?: {
        [key: string]: any;
        gifInfo?: IGifInfo | null;
        title?: string;
        /** [key] 关键帧动效 */
        k?: {
            /** [in] 进场动画 */
            i: IPageAnimation;
            /** [stay] 驻场动画 */
            s: IPageAnimation;
            /** [out] 出场动画 */
            o: IPageAnimation;
        };
        rt_previewAnimaton?: IPageAnimation;
        pageTime?: number;
        rt_isUserUpdatedTime?: boolean;
        rt_page_ppt_mark?: IAiDesignPageTagTypeEnum;
        type?: TPageType | undefined;
    }[];
    sound?: {
        list: [IPageAudio];
    }[];
    pageHash?: Record<string, number>;
    styleId?: string; // 风格id
}
