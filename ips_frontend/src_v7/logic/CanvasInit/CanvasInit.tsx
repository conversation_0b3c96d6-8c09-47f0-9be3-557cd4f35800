import React from 'react';
import { IPSConfig } from '@v7_utils/IPSConfig';
import { klona as cloneDeep } from 'klona';

import { EditorLogic } from '@v7_logic/EditorLogic';
import { IAnyObj, IAsset, ICanvas, IPage, IPageAttr, IUserInfo, IWork } from '@v7_logic/Interface';
import { TextFont } from '@v7_logic/TextFont';
import { AssetHelper } from '@v7_logic/AssetHelper';
import { AssetLogic, UpdateAsset } from '@v7_logic/AssetLogic';
import { TemplateCanvasLogic } from '@v7_logic/TemplateCanvasLogic';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { InfoManageHelper, RecommendManageHelper, SearchHelper } from '@v7_logic/StoreLogic';
import { TemplateGetFaillPop } from '@v7_render/TemplateGetFaillPop';
import { SocketClient } from '@v7_collaborativeService/index';
import { RedoAndUndo } from '@v7_store/logic/onCanvasPaintedReducer';
import { DocHashLogic } from '@v7_logic/DocHash';
// 待替换组件
import { emitter } from '@component/Emitter';
// import { canvasStore } from '@redux/CanvasStore';
// import {
//     paintOnCanvas,
//     searchManage,
// } from '@component/canvas/CanvasRedux';
import { assetManager } from '@component/AssetManager';
import { templateFormat } from '@component/TemplateFormat';
import { getFontNameList } from '@component/IPSConfig';
import { UserCheck } from '@component/UserCheck/UserCheck';
import { fontsList } from '@component/FontsList/FontsList';
import { CanvasPaintedLogic } from '../CanvasPaintedLogic';
import { showUploadPsd } from '@component/UploadPsd/UploadPsd';
import { TemplResExceptionPv } from '../TemplResExceptionPv';
import { OperationRecord } from '../OperationRecord';
import { env } from '@editorConfig/env';
import { AssetQrcodeELogic } from '@v7_logic/AssetQrcodeELogic';
import { isUeTeam } from '@v7_utils/webSource';
import { CanvasToImage } from '@v7_logic/CanvasToImage';
import { IpsUtils } from '@tgs/utils';
import { isAIDesign } from '@v7_utils/estimate';
import { LoadFont } from '@tgs/canvas';
import { UserLogic } from '@v7_logic/UserLogic';
// import {showEffectTemplateTipPopup } from '@v7_render/TemplatePanel/EffectTemplatePanel/EffectTemplatePopup';

/**
 * 画板初始化
 */
export class CanvasInit {
    private static loadedSmallFontsLists: { [key: string]: any };
    private static checkLoginStatusTimeout: number;
    private static _recordLoadTimeTimer: number;
    private static isPostedNewTimeRecord: number;
    /* 检查元素是否加载完 */
    private static showInitalLoading = true;
    private static isInitCheck = false;
    private static isChecking = false;
    private static isCheckFinish = false;
    private static totalAssetsCount = 0;
    private static checkImgAndFontMap = new Map<string, IAsset>();
    /* 检查元素是否加载完 */
    static bleedingLine: boolean;
    static commerenceOnline: number;

    public static init(cb?: () => void): boolean {
        // 模板异常资源监控开始
        // TemplResExceptionPv.startPvListener();

        const urlProps: IAnyObj = IPSConfig.getProps();
        const { oldQrcodeReplace } = AssetQrcodeELogic();
        CanvasPaintedLogic.setEditor({
            editorTag: `${'ue'}-${new Date().getTime()}-${Math.round(Math.random() * 100000000)}`,
        });
        if (urlProps.isDesigner) {
            EditorLogic.updateIsDesigner(true);
            emitter.emit('InfoBarUpdateState');
        }
        if(isAIDesign()){
            EditorLogic.updateIsAiDesigner(true);
        }
        if((urlProps?.k1 == '488') || urlProps.user_asset_id_2){
            EditorLogic.updateIsEffectTemplate(true)
        }
        if (urlProps.GroupWordUser) {
            EditorLogic.updateGroupWordUser(true);
            emitter.emit('InfoBarUpdateState');
        }
        // 文档编辑器设计师端
        if (urlProps.DocDesigner) {
            EditorLogic.updateDocDesigner(true);
            emitter.emit('InfoBarUpdateState');
        }
        
        if (urlProps.is_eb) {
            CanvasPaintedLogic.updateIsEB({});
        }
        if (urlProps.isDesigner && urlProps.isNeedOpacityBg) {
            CanvasPaintedLogic.updateBackGroundColor({
                backgroundColor: { r: 255, g: 255, b: 255, a: 1 },
                isOpacity: true,
                fun_name: 'UPDATE_BACKGROUNDCOLOR',
            });
        }
    
        if (urlProps.w > 0 && urlProps.h > 0) {
            TemplateCanvasLogic.setCanvasPositionAndSize({
                width: parseInt(urlProps.w),
                height: parseInt(urlProps.h),
            });
            // TODO 处理 floorCutting
            emitter.emit('InfoBarUpdateState');
        }

        if(!urlProps.picId || urlProps.isDesigner){
            CanvasPaintedLogic.updateCanvasLoadingStatus(false)
        }
        // 降级措施，防止无法预知的情况一直处于加载状态
        setTimeout(()=>{
            CanvasPaintedLogic.updateCanvasLoadingStatus(false)
        }, 5000)

        fontsList.initFontData(false);

        // 二次翻新初始化判断 START
        if (urlProps['is_second']) {
            setTimeout(() => {
                // 打开模板中心 START
                // emitter.emit('updateTemplateList');
                emitter.emit('onNavChanged', 'template');
                // emitter.emit('rightLayoutHideClick');
                // 打开模板中心 END
            }, 500);
        }
        // 二次翻新初始化判断 END

        //判断是否是第一次编辑
        if (urlProps.upicId > 0) {
            urlProps['origin'] = 'second_time';
            // canvasStore.dispatch(paintOnCanvas('UPDATE_RE_EDIT', { re_edit: 1 })); // TODO 是否移除，case 在 operationRecordRedux 中
        }

        if((env.teamTemplate || isUeTeam) && !urlProps.team_id){
            if(isUeTeam && window.location.href.includes('ueteam.818ps.com')){
                window.location.href = window.location.href.replace('ueteam','ue');
                return;
            } else if (env.editor === 'ecommerceteam' && window.location.href.includes('ecommerceteam.818ps.com')) {
                // 暂时只有v3电商合到ue
                if(location.href.includes('https://ecommerceteam.818ps.com/v3')){
                    location.href = location.href.replace(/https:\/\/ecommerceteam.818ps.com(\/v\d+)?/g, 'https://ue.818ps.com/v4') + '&ecommerce=1';
                }else{
                    location.href = location.href.replace('ecommerceteam','ecommerce');
                }
               
                return;
            }
        }
        InfoManageHelper.setVipTemplateNumMap()
        /* 保存接口用户信息到redux */
        const saveUserInfo=()=> {
            assetManager.getUserInfo().then(data=>{
                data.json().then(userInfo => {
                    let daysRemaining = 0
                    if(userInfo.vip >= 1){
                        daysRemaining = Math.floor((new Date().getTime() - new Date().getTime())/1000/24/60/60)
                    }
                    if (userInfo.id > 0) { 
                        EditorLogic.updateUserid({
                            userId: userInfo.id,
                            vip: userInfo.vip,
                            vipFont: userInfo.vip_font,
                            created: userInfo.created,
                            userType: userInfo.userType,
                            DESIGNER_EDITOR_ASSET_USER: userInfo.DESIGNER_EDITOR_ASSET_USER,
                            super_designer: userInfo.super_designer,
                            isNewUser: userInfo.isNewUser,
                            GROUP_WORD_USER:userInfo.GROUP_WORD_USER,
                            COPY_TEMPLATE_USER:userInfo.COPY_TEMPLATE_USER,
                            daysRemaining: daysRemaining,
                            downloadable: userInfo.vip==1 ? 1: userInfo.amount_max,
                            userName : userInfo.username,
                            userPayCount:userInfo.user_pay_count,
                            userPay53Count:userInfo.user_pay_53_count,
                            vip_name: userInfo.vip_name,
                            is_firm_vip: userInfo.is_firm,
                            isJumpCommercial: userInfo.is_jump_commercial,
                            commercialType: userInfo.commercial_type,
                            avatar: userInfo.avatar,
                            selfVip: userInfo.selfVip,
                            // selfIsFirm: userInfo.self_is_firm,
                            selfIsFirm: userInfo.is_firm,
                            bind_phone: userInfo.bind_phone,
                            tel: userInfo.tel,
                        });
                    } else {
                        EditorLogic.updateUserid({
                            isJumpCommercial: userInfo.is_jump_commercial,
                            commercialType: userInfo.commercial_type,
                        } as IUserInfo);
                    }
                });
            });
        }
       
        /**
         * 初始进入编辑器，调用用户画像相关接口
         */
        assetManager.userPortrait();

        const th = this;
        window.addEventListener('resize', TemplateCanvasLogic.canvasResizeByWindow);
        if (urlProps.picId > 0 || urlProps.upicId > 0 || urlProps.paperId > 0 || urlProps.user_template_team_id > 0 || urlProps.groupWordId > 0) {
            let tempFirst = 0;
            if (urlProps.picId > 0) {
                tempFirst = 1;
            }

            localStorage.removeItem('ue_mutisize_temp_kid_2');
            assetManager.getTemplate(urlProps.picId, urlProps.upicId, '', urlProps.paperId, '', '', urlProps.groupWordId).then((data) => {
                if (data.status >= 500) {
                    const windowInfo = {
                        windowContent: <TemplateGetFaillPop></TemplateGetFaillPop>,
                        popupWidth: 462,
                        popupHeight: 218,
                        style: {
                            padding: 0,
                            borderRadius: '4px',
                            top: '50%',
                            transform: 'translateY(-50%)',
                        },
                        popupTitleBarStyle: {
                            width: 0,
                            height: 0,
                            display: 'none',
                        },
                    };
                    emitter.emit('popupWindow', windowInfo);
                    CanvasPaintedLogic.updateCanvasLoadingStatus(false)
                }

                data.json().then((resultData) => {
                    if (Number(resultData.stat) === -3) {
                        // 去除upicId参数 模板不存在
                        window.location.href = 'https://818ps.com/imageeditor.html?route_area=avatarLeft';
                        return;
                    }
                    let { info, page_map } = resultData;
                    if(resultData.class_id){
                        localStorage.setItem(`class_id_${info?.id}`, JSON.stringify(resultData.class_id));
                    }else{
                        localStorage.removeItem(`class_id_${info?.id}`)
                    }
                    // 如果页面初始化没有这个page_map。就用info.id作为第一个templateId
                    if (!(page_map && Object.keys(page_map).length)) {
                      page_map = {1: String(info?.last_templ_id)}
                    }
                    try {
                        if (Array.isArray(page_map)) {
                            const pageMapObj = {}
                            page_map.forEach((item, index) => {
                                pageMapObj[index+1] = item
                            })
                            page_map = pageMapObj
                        }
                        for (const key in page_map) {
                            if (!resultData.doc.work[Number.parseInt(key) - 1]) {
                                delete page_map[key];
                            }
                        }
                    } catch (error) {
                        console.error('format picid map error: ', error)
                    }
                    CanvasPaintedLogic.updatePageMap({page_map})
                    const recommendFont = resultData.recommendFont,
                        templateFrom = resultData.templateFrom,
                        designerTemplateId = resultData.designerTemplateId;
                    localStorage.ue_mutisize_temp_kid_2 = info.kid_2;
                    window.user_template_get_ended = new Date().getTime();

                    const bleeding_class_id = [
                            '33',
                            '16',
                            '743',
                            '160',
                            '744',
                            '922', //电商PC店铺首页
                            '19', //店招
                        ],
                        commerence_class_id = ['922', '923', '1039', '1099'];
                    bleeding_class_id.map((item) => {
                        if (!this.bleedingLine) {
                            this.bleedingLine = resultData.class_id && resultData.class_id.includes(item);
                        }
                    });

                    CanvasPaintedLogic.setBleedingLine({ bleedingLine: this.bleedingLine });
                    commerence_class_id.map((item: string) => {
                        if (resultData.class_id && resultData.class_id.includes(item)) {
                            if (item === '922') {
                                this.commerenceOnline = 0.4;
                            } else {
                                this.commerenceOnline = 0.75;
                            }
                        }
                    });

                    if (Number(resultData.stat) !== 1) {
                        const windowInfo = {
                            windowContent: <TemplateGetFaillPop />,
                            popupWidth: 462,
                            popupHeight: 218,
                            style: {
                                padding: 0,
                                borderRadius: '4px',
                                top: '50%',
                                transform: 'translateY(-50%)',
                            },
                            popupTitleBarStyle: {
                                width: 0,
                                height: 0,
                                display: 'none',
                            },
                        };
                        emitter.emit('popupWindow', windowInfo);
                    }

                    if (resultData.isRemake) {
                        // UPDATE_CANVAS_INFO_ISREMAKE
                        CanvasPaintedLogic.updateCanvasInfoIsReMake({ isRemake: 1 });
                    }

                    if (designerTemplateId > 0) {
                        let tempUrl = document.location.href.replace(/upicId=[^&#]*/, '');

                        tempUrl = tempUrl + '&picId=' + designerTemplateId;
                        history.pushState('', document.title, tempUrl);
                        const props = IPSConfig.getProps();
                        Object.assign(props, {
                            upicId: 0,
                            picId: designerTemplateId,
                        });
                    }

                    const urlProps: IAnyObj = IPSConfig.getProps();
                    if (!resultData.is_user_templ) {
                        const notUpicIdUrl = document.location.href.replace(/upicId=[^&#]*/, 'upicId=0').replace(/user_template_team_id=[^&#]*/,'user_template_team_id=0');
                        history.pushState('', document.title, notUpicIdUrl);
                        const props = IPSConfig.getProps();
                        Object.assign(props, {
                            upicId: 0,
                        });
                    }
                    /*打开保存到公众号弹窗 START*/
                    if (urlProps.weChatFileUrl != undefined) {
                        emitter.emit('InfoBarBindWeChatOpenPlatform', { uploadFileUrl: urlProps.weChatFileUrl });
                    }
                    /*打开保存到公众号弹窗 END*/

                    Object.assign(urlProps, {
                        k1: resultData.info.kid_1,
                        k2: resultData.info.kid_2,
                        k3: resultData.info.kid_3,
                    });
                    const kid_1 = parseInt(resultData.info.kid_1);
                    const kid_2 = parseInt(resultData.info.kid_2);
                    const floorCuttingLength = resultData.doc.canvas.floorCutting?.length;
                    // 详情页 楼层分割模板
                    if (
                        [176, 177].includes(kid_2) ||
                        ([45, 63, 330].includes(kid_2) &&
                            (urlProps.isDesigner ||
                                (floorCuttingLength > 0 && floorCuttingLength === resultData.doc.work?.length)))
                    ) {
                        if (window.location.href.includes('ue.818ps.com') && !isUeTeam) {
                            location.href = location.href.replace(
                                /https:\/\/ue.818ps.com(\/v\d+)?/g,
                                'https://ecommerce.818ps.com/v2',
                            );
                            return;
                        } else if (window.location.href.includes('ueteam.818ps.com') || isUeTeam) {
                            location.href = location.href.replace(
                                /https:\/\/ue(.*).818ps.com(\/v\d+)?/g,
                                'https://ecommerceteam.818ps.com/v2',
                            );
                            return;
                        } else if (
                            window.location.href.includes('ecommerce.818ps.com') &&
                            !window.location.href.includes('https://ecommerce.818ps.com/v2') &&
                            !window.location.href.includes('https://ecommerce.818ps.com/v1')
                        ) {
                            location.href = location.href.replace(
                                /https:\/\/ecommerce.818ps.com(\/v\d+)?/g,
                                'https://ecommerce.818ps.com/v2',
                            );
                            return;
                        } else if (
                            window.location.href.includes('ecommerceteam.818ps.com') &&
                            !window.location.href.includes('https://ecommerce.818ps.com/v2') &&
                            !window.location.href.includes('https://ecommerce.818ps.com/v1')
                        ) {
                            location.href = location.href.replace(
                                /https:\/\/ecommerceteam.818ps.com(\/v\d+)?/g,
                                'https://ecommerceteam.818ps.com/v2',
                            );
                            return;
                        }
                        // TODO 电商多页 case
                        CanvasPaintedLogic.setfloorCutting({ rt_is_online_detail_page: true });
                    } 
                    // TODO COMBINE
                    else if (
                        [
                            14, 15, 29, 38, 45, 93, 132, 133, 134, 160, 176, 177, 178, 180, 190, 202, 204, 206, 207,
                            246, 290, 302, 307, 328, 329, 330,
                        ].includes(kid_2)
                    ) {
                        // 数组没有剔除 176 177 是为了保留完整的 电商 kid_2
                        // if (window.location.href.includes('ue.818ps.com') && !isUeTeam) {
                        //     location.href = location.href.replace(
                        //         /https:\/\/ue.818ps.com(\/v\d+)?/g,
                        //         'https://ecommerce.818ps.com',
                        //     );
                        //     return;
                        // }  
                        
                        // if (window.location.href.includes('ueteam.818ps.com') || isUeTeam) {
                        //     location.href = location.href.replace(
                        //         /https:\/\/ue(.*).818ps.com(\/v\d+)?/g,
                        //         'https://ecommerceteam.818ps.com',
                        //     );
                        //     return;
                        // }
                        const withEcommerceUrl = IpsUtils.Url.replaceUrlParams(window.location.href, { ecommerce:1 } )
                        IpsUtils.Url.replaceUrlNoJump(withEcommerceUrl);
                        // return
                    }
                    // ueteam重定向到ue
                    if (window.location.href.includes('ueteam.818ps.com')) {
                        location.href = location.href.replace(/https:\/\/ueteam.818ps.com(\/v\d+)?/g, 'https://ue.818ps.com');
                    }
                    if (
                        resultData?.info?.template_type === '10' &&
                        !location.href.includes('/doc') &&
                        !location.port
                    ) {
                        location.href = location.href.replace(
                            /https:\/\/ue.818ps.com(\/v\d+)?/g,
                            'https://ue.818ps.com/doc',
                        );
                    }
                    /*用户关闭页面提示START*/
                    if (env.editor !== 'shentu') {
                        window.onbeforeunload = function () {
                            return '作品还未保存或下载，是否要离开？';
                        };
                    }

                    /*用户关闭页面提示END*/

                    /*设置超级用户START*/
                    EditorLogic.updateIsSuperUser(resultData.isSuperUser);
                    /*设置超级用户END*/

                    if (!(urlProps.upicId > 0) && !(urlProps.picId > 0) && !(urlProps.groupWordId > 0)) {
                        info.title = '未命名';
                    }
                    info = Object.assign(info, {
                        isFlag: 1,
                        class_id: resultData.class_id ? resultData.class_id : [],
                        switch_size: resultData.switch_size ? resultData.switch_size : [],
                    });
                    emitter.emit('getPicId', info.last_templ_id);
                    // 更新 store_name: storeAdapter.store_names.InfoManage,
                    InfoManageHelper.updateInfo(cloneDeep(info));
                    emitter.emit('TitleQuickEditChangeTitle');

                    assetManager.setPagePv_new(151);
                    if (info?.is_company_temp === 1) {
                        assetManager.setPagePv_new(4729);
                    }
                    /* 图片二维码替换 */
                    resultData = oldQrcodeReplace(resultData);

                    /* 加载小字体 */
                    const small_fonts = resultData.small_font || [];
                    const fontlistObj = getFontNameList();
                    const newFontFamily = document.createElement('style');
                    newFontFamily.id = 'newFontFamily';
                    this.loadedSmallFontsLists = small_fonts;
                    // 更新小字体列表
                    TextFont.updateSmallFontList({
                        small_font_list: small_fonts,
                    });
                    const smallFontKeys = {};
                    if (this.loadedSmallFontsLists) {
                        this.loadedSmallFontsLists.forEach((font: { [key: string]: any }) => {
                            smallFontKeys[font.font_family] = true;
                        });
                        localStorage.setItem('local_smallFontKeys', JSON.stringify(smallFontKeys));
                    }
                    for (const key in fontlistObj) {
                        let existSmallFont = false;
                        small_fonts.map((font: { [key: string]: any }) => {
                            if (fontlistObj[key] == font.font_family) {
                                //加载小字体
                                newFontFamily.appendChild(
                                    document.createTextNode(`
                                    @font-face {
                                        font-family: '${key}_small';
                                        src:url('${font.font_path}') format('truetype');
                                    }
                                `),
                                );
                                newFontFamily.appendChild(
                                    document.createTextNode(`
                                    @font-face {
                                        font-family: '${key}';
                                        src:url('//js.tuguaishou.com/fonts/${fontlistObj[key]}.woff') format('truetype');
                                    }
                                `),
                                );
                                existSmallFont = true;
                            }
                        });
                        if (!existSmallFont) {
                            //加载大字体
                            newFontFamily.appendChild(
                                document.createTextNode(`
                                @font-face {
                                    font-family: '${key}';
                                    src:url('//js.tuguaishou.com/fonts/${fontlistObj[key]}.woff') format('truetype');
                                }
                            `),
                            );
                        }
                    }

                    document.head.appendChild(newFontFamily);

                    //页面PV记录
                    const pvClassIds = resultData.class_id
                        ? resultData.class_id
                              .filter((i: number) => i)
                              .map((s: number) => `a-${s}`)
                              .join(',')
                        : '';
                    if (urlProps.picId > 0) {
                        if (urlProps.isDesigner === '1') {
                            assetManager.setPagePv_new(1855, 'template', urlProps.picId);
                        } else {
                            assetManager.setPagePv_new(1855, 'template', urlProps.picId, undefined, undefined, {
                                s0: pvClassIds,
                                i0: resultData.template_attr ? parseInt(resultData.template_attr) : 0,
                                i1: resultData.info ? resultData.info.last_templ_id || parseInt(resultData.info.id) : 0,
                                i2: info?.is_company_temp.toString(),
                                i3: urlProps.ai_template_type ?? 1,
                            });
                        }
                    } else if (urlProps.upicId > 0) {
                        assetManager.setPagePv_new(1855, 'user', urlProps.upicId, undefined, undefined, {
                            s0: pvClassIds,
                            i0: resultData.template_attr ? parseInt(resultData.template_attr) : 0,
                            i1: resultData.info ? resultData.info.last_templ_id || parseInt(resultData.info.id) : 0,
                            i2: resultData.info?.is_company_temp.toString(),
                        });
                    }
                    /* 加载小字体 end*/

                    const preview = resultData.preview,
                        allNeedPostDatas: IAsset[] = [];
                    const resInfo = resultData.info;
                    resultData = resultData.doc;
                    // 针对标题中存在敏感字
                    if (resInfo?.title && resultData?.canvas) {
                        resultData.canvas.title = resInfo.title
                    }
                    resultData = templateFormat.getFormat(resultData, { picId: urlProps.picId, isDesigner:urlProps.isDesigner });
                    if (kid_1 === 958 || urlProps.mode === 'board' || urlProps.picId === '5925767') {
                        resultData.pageAttr.pageInfo = (resultData.pageAttr.pageInfo || new Array(resultData.work.pages.length || 1).fill({})).map((item: IPage) => {
                            return {
                                ...item,
                                pageTime: 5000,
                                type: 'board',
                            };
                        })
                    }
                    // 初始化元素打点数据 - rt_rntime = 0 > 初始状态；1 > 已经加载
                    const { pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                        store_name: storeAdapter.store_names.paintOnCanvas,
                    });
                    resultData.work.pages.forEach((item: IAsset) => {
                        item.rt_page_tem_tag = Math.random() * 1000;
                    });
                    if(!resultData.work.pages || resultData.work.pages.length <= 5){
                        CanvasPaintedLogic.updateCanvasLoadingStatus(false)
                    }
                    let isExitBg = true;
                    resultData.work.pages[pageInfo.pageNow].assets.forEach((item: IAsset, index: number) => {
                        Object.assign(item.meta, {
                            rt_rntime: 0,
                        });
                        if (item.meta.type === 'SVG') {
                            item['rt_noNeedPost'] = true;
                            allNeedPostDatas.push(item);
                        } else if (
                            item.meta.type === 'text' &&
                            item.attribute.effect && //特效字
                            !(item.attribute.effectVariant && item.attribute.effectVariant.rt_name)
                        ) {
                            item['rt_noNeedPost'] = true;
                            allNeedPostDatas.push(item);
                        } else if (
                            item.meta.type === 'image' ||
                            item.meta.type === 'background' ||
                            item.meta.type === 'pic'
                        ) {
                            if (isExitBg && item.meta.type === 'background') {
                                assetManager.setPagePv_new(7753);
                                isExitBg = false;
                            }
                            //图片类型
                            if (item.attribute.container) {
                                // 裁剪
                                item['rt_noNeedPost'] = true;
                                allNeedPostDatas.push(item);
                            }
                        }
                    });
                    /* 初始化需要请求资源的元素 */
                    this.initalNeedPostDatas(allNeedPostDatas);
                    /* 初始化需要请求资源的元素 */
                    // 初始化元素打点数据
                    CanvasPaintedLogic.sendPicIdNew({ picId: urlProps.picId });
                    if (resultData.work.pages?.[0].assets) {
                        this.loadTemplateImageFirst(resultData.work.pages?.[0].assets, preview)
                    }else{
                         window.loadedFirstPageAllImage = true
                    }

                    const mode = info?.template_type === 3 ? 'pull' : resultData.pageAttr?.pageInfo?.[0]?.type === 'board' ? 'board' : '';
                    EditorLogic.updateCanvasMap({
                        canvas: resultData.canvas,
                        work: resultData.work,
                        pageAttr: resultData.pageAttr,
                        picId: urlProps.picId,
                        lastTemplId: info['last_templ_id'],
                        preview: preview,
                        rt_canvas_render_mode: mode,
                    });
                    DocHashLogic.addPageHash();
                    storeAdapter.dispatch({
                        store_name: storeAdapter.store_names.paintOnCanvas,
                        fun_name: 'updateWorkPagesState',
                        params: [
                            {
                                type: 'updateWorkPagesState',
                                params: {},
                            },
                        ],
                    });
                    setTimeout(() => {
                        // 延迟 500ms 显示左右面板
                        cb?.();                        
                    }, 800);
                    /* 编辑器-发起审批按钮埋点*/
                    if (info.approve_status === '2') {
                        assetManager.setPv_new(5162);
                    } else if (info.approve_status === '1') {
                        assetManager.setPv_new(5162);
                    } else {
                        assetManager.setPv_new(5134);
                    }
                    /* 用户时 判断是否是电商详情模板*/
                    if (resultData.canvas.floorCutting && resultData.canvas.floorCutting.length > 0) {
                        // canvasStore.dispatch(paintOnCanvas('MUTISIZE_SET_IS_DETAIL_PAGE',{bool : true})); // TODO 电商多页 case
                        CanvasPaintedLogic.setfloorCutting({ rt_is_online_detail_page: true });
                    }
                    /* 用户时 判断是否是电商详情模板 end*/
                    /* 清楚group元素，先干掉，后面看看有没有问题 */
                    // if (!urlProps.isDesigner && tempFirst == 1) {
                    //     EditorLogic.delGroupInfoAll();
                    // }
                    // 海报类型ai生产模板，全部解组
                    if(urlProps.isDesigner && urlProps.ai_produce=='pic'){
                        EditorLogic.delGroupInfoAll();
                    }
                    emitter.emit('quickEditingUpdateState');

                    /*初始化画布位置START*/
                    TemplateCanvasLogic.canvasResizeByWindow();
                    /*初始化画布位置END*/
                    // window.addEventListener('resize', TemplateCanvasLogic.canvasResizeByWindow);

                    // canvasStore.dispatch(paintOnCanvas('UPDATE_COMMON_CANVAS_CENTER')) // 相关属性未使用
                    RecommendManageHelper.updateRecommendFont({ recommendFont: recommendFont });
                    // canvasStore.dispatch(paintOnCanvas('UPDATE_SOURCE', {source: parseInt(info.templ_id)})); // 目标 store 废弃
                    OperationRecord.source = parseInt(info.templ_id);
                    if (urlProps.picId > 0 && !(urlProps.upicId > 0)) {
                        // 替换快捷编辑信息
                        emitter.emit('quickEditingReplaceFavTextEmitter');
                    }
                    // 切换账号处理
                    let switchName = false;
                    if (templateFrom === 'designer') {
                        switchName = true;
                    }

                    // 切换账号处理
                    if (!urlProps.isDesigner && !urlProps.GroupWordUser) {
                        // 自动保存
                        //     emitter.emit('autoSaveTempl');
                        assetManager.getUserInfo().then((data) => {
                            data.json().then((userInfo) => {
                                if (userInfo.id > 0) {
                                    // 开启用户状态检测
                                    UserCheck.initFun();
                                    UserCheck.startCheck();

                                    if (th.checkLoginStatusTimeout) {
                                        clearTimeout(th.checkLoginStatusTimeout);
                                    }
                                    emitter.emit('UserProfileUpdateState', {
                                        uid: userInfo.id,
                                        username: userInfo.username,
                                        picurl: userInfo.avatar,
                                    });
                                    let daysRemaining = 0;
                                    if (userInfo.vip >= 1) {
                                        daysRemaining = Math.floor(
                                            (new Date().getTime() - new Date().getTime()) / 1000 / 24 / 60 / 60,
                                        );
                                    }
                                    EditorLogic.updateUserid({
                                        userId: userInfo.id,
                                        vip: userInfo.vip,
                                        vipFont: userInfo.vip_font,
                                        created: userInfo.created,
                                        userType: userInfo.userType,
                                        DESIGNER_EDITOR_ASSET_USER: userInfo.DESIGNER_EDITOR_ASSET_USER,
                                        super_designer: userInfo.super_designer,
                                        isNewUser: userInfo.isNewUser,
                                        GROUP_WORD_USER: userInfo.GROUP_WORD_USER,
                                        COPY_TEMPLATE_USER: userInfo.COPY_TEMPLATE_USER,
                                        daysRemaining: daysRemaining,
                                        downloadable: userInfo.vip == 1 ? 1 : userInfo.amount_max,
                                        userName: userInfo.username,
                                        userPayCount: userInfo.user_pay_count,
                                        userPay53Count: userInfo.user_pay_53_count,
                                        vip_name: userInfo.vip_name,
                                        is_firm_vip: userInfo.is_firm,
                                        isJumpCommercial: userInfo.is_jump_commercial,
                                        commercialType: userInfo.commercial_type,
                                        avatar: userInfo.avatar,
                                        selfVip: userInfo.selfVip,
                                        selfIsFirm: userInfo.self_is_firm,
                                        bind_phone: userInfo.bind_phone,
                                        tel: userInfo.tel,
                                    });

                                    /* 相关功能添加于 http://gitlab.818ps.com/ips/ips_editor_all/-/merge_requests/534
                                       失效于 http://gitlab.818ps.com/ips/ips_editor_all/-/commit/6d2ea8936b9543b7a1a155f0e2d1c52ad9273559
                                       决定删除该行调用 */
                                    // emitter.emit('getDownloadNumInfo')

                                    // 自动保存
                                    // emitter.emit('autoSaveTempl','','',{switchName:switchName,newpicId:designerTemplateId});

                                    /*保存用户操作记录START*/
                                    assetManager.setPushOperationRecord();
                                    // assetManager.setPushOperationRecordNew();
                                    /*保存用户操作记录END*/

                                    /* 初始化 socket 协作 */
                                    const showCollaborative =
                                        !urlProps.isDesigner &&
                                        !urlProps.isClientSide &&
                                        !(
                                            window.navigator.userAgent.toLowerCase().indexOf('mac') > -1 &&
                                            window.navigator.userAgent.toLowerCase().indexOf('electron/') > -1
                                        );
                                    const showCollaborativeCondition =
                                        Number.parseInt(urlProps.upicId) > 0 ||
                                        (urlProps.user_template_team_id && env.editor === 'ueteam');
                                    if (showCollaborativeCondition && Number.parseInt(urlProps.upicId) > 0) {
                                        showCollaborative &&
                                            SocketClient.init({
                                                userId: userInfo.id,
                                                n: userInfo.username,
                                                avatar: userInfo.picurl,
                                                templateId: urlProps.upicId,
                                            });
                                    }
                                    if (showCollaborative && !showCollaborativeCondition) {
                                        let intervalId: number = null;
                                        const getUpicId = () => {
                                            const urlPropsTime = IPSConfig.getProps();
                                            if (Number.parseInt(urlPropsTime.upicId) > 0) {
                                                clearInterval(intervalId);
                                                SocketClient.init({
                                                    userId: userInfo.id,
                                                    n: userInfo.username,
                                                    avatar: userInfo.picurl,
                                                    templateId: urlPropsTime.upicId,
                                                });
                                            }
                                        };
                                        intervalId = window.setInterval(getUpicId, 1000);
                                    }
                                } else {
                                    EditorLogic.updateUserid({
                                        isJumpCommercial: userInfo.is_jump_commercial,
                                        commercialType: userInfo.commercial_type,
                                    } as IUserInfo);
                                }
                            });
                        });
                    }
                    if (urlProps.isDesigner || urlProps.GroupWordUser) {
                        saveUserInfo();
                    }
                    if( urlProps.uassetId > 0 ){
                        const {work, pageInfo} = storeAdapter.getStore({
                            store_name: storeAdapter.store_names.paintOnCanvas,
                        });
                        let propsIndex = 0;
                        work.pages[pageInfo.pageNow].assets.map((item: IAsset, index: number) => {
                            if (item.meta.name === '二维码') {
                                propsIndex = index;
                            }
                        });
                        assetManager.getUserAssetInfo(urlProps.uassetId).then((data) => {
                            data.json().then((resultData) => {
                                if (Number(resultData.stat) === 1) {
                                    const item = resultData.msg;
                                    const info = {
                                        attribute: {
                                            resId: item.id,
                                            picUrl: item.path,
                                            width: item.width,
                                            height: item.height,
                                            assetWidth: item.width,
                                            assetHeight: item.height,
                                            rt_picUrl: item.path,
                                        },
                                    };
                                    Object.assign(info.attribute, {
                                        assetWidth: info.attribute.width,
                                        assetHeight: info.attribute.height,
                                    });
                                    if (info.attribute.width > info.attribute.height) {
                                        info.attribute.width =
                                            work.pages[pageInfo.pageNow].assets[propsIndex].attribute.width;
                                        info.attribute.height =
                                            (info.attribute.width * info.attribute.assetHeight) /
                                            info.attribute.assetWidth;
                                    } else {
                                        info.attribute.height =
                                            work.pages[pageInfo.pageNow].assets[propsIndex].attribute.height;
                                        info.attribute.width =
                                            (info.attribute.height * info.attribute.assetWidth) /
                                            info.attribute.assetHeight;
                                    }
                                    CanvasPaintedLogic.updateAssetInfo({
                                        index: propsIndex,
                                        info,
                                    });
                                }
                            });
                        });
                    }

                    // 修改模板筛选比例
                    const tempNum = resultData.canvas.width / resultData.canvas.height,
                        ratio = tempNum > 1.6 ? 1 : tempNum >= 0.75 ? 0 : 2;
                    // TODO 合并操作
                    SearchHelper.updateTemplateRatioId({
                        search: {
                            templateRatioId: ratio,
                        },
                    });

                    // 修改模板筛选分类
                    SearchHelper.updateTemplateK1({
                        search: {
                            templateK1: info.kid_1,
                        },
                    });

                    SearchHelper.updateTemplateK2({
                        search: {
                            templateK2: info.kid_2,
                        },
                    });

                    // 获取模板列表
                    // emitter.emit('updateTemplateList');

                    emitter.emit('CanvasContentSidePagingAreaTipCheckShow');
                    
                });
            });
            // 区分是否是效果模板
            assetManager.getUserTemplateKind({
                picId:urlProps.picId,
                paperId:urlProps.paperId,
                upicId:urlProps.upicId,
                version_id:urlProps.version_id,
                user_template_team_id:urlProps.user_template_team_id
            }).then(data=>{
                data.json().then(res=>{
                    if(res.code ==1){
                        // 判断是否是特效模板
                        if((res.data.kid_1 == 488) ){
                        EditorLogic.updateIsEffectTemplate(true);
                        // 用户首次进入，提示引导弹窗
                            // if(urlProps.picId && !urlProps.upicId){
                            //     assetManager.isShowGuidePop().then(data=>{
                            //         data.json().then(res=>{
                            //             if(res.code > 0){
                            //                 showEffectTemplateTipPopup()
                            //             }
                            //         })
                            //     })
                            // }
                        }
                    }
                })
            })
           
            assetManager.getUserInfo().then((data) => {
                data.json().then((userInfo: IAnyObj) => {
                    if (!(userInfo.id > 0)) {
                        emitter.emit('LoginPanelShow', 'firstLogin');
                        assetManager.setPv_new(4572, {
                            additional: {
                                s0: 'firstLogin',
                            },
                        });
                    }
                })
            });
        } else {
            cb?.();
            if (
                (urlProps.origin == 'home_recommend_edit' || urlProps.origin == 'home_create_design_edit') &&
                urlProps.class_id
            ) {
                setTimeout(() => {
                    emitter.emit('onNavChanged', 'template');
                    emitter.emit('rightLayoutHideClick', '', 'template', undefined);
                }, 1000);
            }
            const k2 = Number.parseInt(urlProps.k2);
            if ([176, 177, 45, 63, 330].includes(k2)) {
                if (
                    !window.location.href.includes('https://ecommerce.818ps.com:3000') &&
                    !window.location.href.includes('https://ecommerce.818ps.com/v2')
                ) {
                    location.href = 'https://ecommerce.818ps.com/v2/' + location.search;
                    return;
                }
            }
            let mode: '' | 'pull' | 'board' = ''
            if (urlProps.mode === 'board') {
                mode = 'board'
            } else if (urlProps.mode === 'ppt') {
                mode = 'pull'
                InfoManageHelper.updateInfo({
                    template_type: 3,
                    class_id: ['290'],
                })
            }
            if (!urlProps.isDesigner && mode) {
                const data: {mode: typeof mode, pageAttr?: IPageAttr } = { mode}
                if (mode === 'board') {
                    const { pageAttr } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                        store_name: storeAdapter.store_names.paintOnCanvas,
                    });
                    data.pageAttr = cloneDeep({
                        pageInfo: (pageAttr.pageInfo || [{}]).map((item) => {
                            return {
                                ...item,
                                pageTime: 5000,
                                type: 'board',
                            };
                        }),
                    })
                }
                CanvasPaintedLogic.updateCanvasRenderMode(data)
            }
            if (urlProps.ispsd) {
                DocHashLogic.addPageHash();
                // 进入上传 psd 模式
                assetManager.getUserInfo().then((data) => {
                    data.json().then((userInfo) => {
                        if (userInfo.id > 0) {
                            // 延迟执行等其他组件初始化
                            setTimeout(() => {
                                showUploadPsd();
                            }, 500);
                        } else {
                            emitter.emit('LoginPanelShow');
                        }
                    });
                });
            } else if (
                (!(urlProps.w > 0) || !(urlProps.h > 0)) &&
                (!(urlProps.assetId > 0) || urlProps.assetIdFlag != 1 || !(urlProps.paperId > 0))
            ) {
                alert('非法链接，请从正规路径打开链接，或者联系服务人员。\n点击确认后将跳转到首页');
                window.location.href = '//818ps.com';
                return false;
            } else if (urlProps.w > 0 && urlProps.h > 0) {
                EditorLogic.updateNewTemplate(true)
                DocHashLogic.addPageHash();
                saveUserInfo();
                if (!urlProps.menu) {
                    setTimeout(() => {
                        // emitter.emit('changeActiveMenu', '55');
                        emitter.emit('onNavChanged', 'addMenu');
                        emitter.emit('rightLayoutHideClick', 'hover', 'addMenu', undefined);
                    }, 1000);
                }
            }
            // ueteam重定向到ue
            this.replaceUrlForEditorCombine();
            /* 加载小字体 */
            const fontlistObj = getFontNameList();
            const newFontFamily = document.createElement('style');
            // let fontlistObj = fontsList.getAllFonts();
            // const newFontFamily = document.createElement('style');
            // if (Object.keys(fontlistObj).length == 0) {
            //     fontlistObj = getFontNameList();
            // }

            for (const key in fontlistObj) {
                newFontFamily.appendChild(
                    document.createTextNode(`
                        @font-face {
                            font-family: '${key}';
                            src:url('//js.tuguaishou.com/fonts/${
                                fontlistObj[key]
                            }.woff?t=${new Date().getTime()}') format('truetype');
                        }
                    `),
                );
            }

            document.head.appendChild(newFontFamily);

            /* 加载小字体 end*/
            if (urlProps.assetId > 0) {
                assetManager.getAssetInfo(urlProps.assetId).then((data) => {
                    data.json().then((resultData) => {
                        if (Number(resultData.stat) === 1) {
                            if (!(urlProps.w > 0 && urlProps.h > 0)) {
                                TemplateCanvasLogic.updateCanvasSize({
                                    width: resultData.msg.width,
                                    height: resultData.msg.height,
                                    autoSave: 2,
                                });
                                emitter.emit('CanvasWindowResize');
                            }
                            if (Number(resultData.msg.kid_1) === 1) {
                                OperationRecord.source = -2;
                                // canvasStore.dispatch(paintOnCanvas('UPDATE_SOURCE', { source: -2 })); // TODO 是否移除，case 在 operationRecordRedux 中
                                emitter.emit('ListAddElement', resultData.msg, 2);
                            } else {
                                OperationRecord.source = -1;
                                // canvasStore.dispatch(paintOnCanvas('UPDATE_SOURCE', { source: -1 })); // TODO 是否移除，case 在 operationRecordRedux 中
                                emitter.emit('ListAddBackground', resultData.msg, 2);
                            }

                            // 自动保存
                            const { user } = storeAdapter.getStore({
                                store_name: storeAdapter.store_names.paintOnCanvas,
                            });
                            if (user.userId > 0) {
                                emitter.emit('autoSaveTempl');
                            }
                        } else {
                            alert('元素获取失败');
                        }
                    });
                });

                //页面PV记录
                // assetManager.setPagePv_new(40, 'background', urlProps.assetId);
                assetManager.setPagePv_new(1855, 'background', urlProps.assetId);
            } else {
                //页面PV记录
                // assetManager.setPagePv_new(40, 'blank', 0);
                assetManager.setPagePv_new(1855, 'blank', 0);
            }

            // document.getElementById('editorLoading').style.display = 'none';
            // document.getElementById('editorLoadingNew').style.display = 'none';
            assetManager.setPagePv_new(151);
            /*导航开启检测START*/
            // emitter.emit('GuideAreaUS', true)
            /*导航开启检测END*/
            /*用户关闭页面提示START*/
            if (env.editor !== 'shentu') {
                window.onbeforeunload = function (e: Event) {
                    return '作品还未保存或下载，是否要离开？';
                };
            }
            /*用户关闭页面提示END*/

            // 修改模板筛选比例
            if (parseInt(urlProps.w) === 900 && parseInt(urlProps.h) === 500) {
                SearchHelper.updateTemplateK2({
                    search: {
                        templateK2: 4,
                        // templateRatioId: -1,
                    },
                });
            } else if (parseInt(urlProps.w) === 600 && parseInt(urlProps.h) === 600) {
                SearchHelper.updateTemplateK2({
                    search: {
                        templateK2: 5,
                        // templateRatioId: -1,
                    },
                });
            } else if (parseInt(urlProps.w) === 720 && parseInt(urlProps.h) === 1280) {
                SearchHelper.updateTemplateK2({
                    search: {
                        templateK2: 8,
                        // templateRatioId: -1,
                    },
                });
            } else if (parseInt(urlProps.w) === 922 && parseInt(urlProps.h) === 302) {
                SearchHelper.updateTemplateK2({
                    search: {
                        templateK2: 6,
                        // templateRatioId: -1,
                    },
                });
            } else if (parseInt(urlProps.w) === 588 && parseInt(urlProps.h) === 273) {
                SearchHelper.updateTemplateK2({
                    search: {
                        templateK2: 7,
                        // templateRatioId: -1,
                    },
                });
            } else if (parseInt(urlProps.w) === 600 && parseInt(urlProps.h) === 200) {
                SearchHelper.updateTemplateK2({
                    search: {
                        templateK2: 9,
                        // templateRatioId: -1,
                    },
                });
            } else if (parseInt(urlProps.w) === 1920 && parseInt(urlProps.h) === 650) {
                SearchHelper.updateTemplateK2({
                    search: {
                        templateK2: 15,
                        // templateRatioId: -1,
                    },
                });
            } else if (parseInt(urlProps.w) === 800 && parseInt(urlProps.h) === 800) {
                SearchHelper.updateTemplateK2({
                    search: {
                        templateK2: 14,
                        // templateRatioId: -1,
                    },
                });
            } else if (parseInt(urlProps.w) === 2480 && parseInt(urlProps.h) === 3366) {
                SearchHelper.updateTemplateK2({
                    search: {
                        templateK2: 17,
                        // templateRatioId: -1,
                    },
                });
            } else {
                const tempNum = urlProps.w / urlProps.h,
                    ratio = tempNum > 1.6 ? 1 : tempNum >= 0.75 ? 0 : 2;
                SearchHelper.updateTemplateRatioId({
                    search: {
                        templateRatioId: ratio,
                    },
                });
            }
            // 针对设计师风格模板生产做单独处理
            if(urlProps.isDesigner && urlProps['class_id'] == 1916 && urlProps.k2 == 496){
                assetManager.getStyleTemplateInfo(urlProps['style_tid']).then(data=>{
                    data.json().then(res=>{
                        if(res.code == 1){
                           const resultData = templateFormat.getFormat(res.data.doc, { picId: '0' });
                            EditorLogic.updateCanvasMap({
                                canvas: resultData.canvas as ICanvas,
                                work:resultData.work as IWork,
                                pageAttr: resultData.pageAttr,
                                picId: '0',
                                lastTemplId: '0',
                                preview: [''],
                            });
                            TemplateCanvasLogic.canvasResizeByWindow();
                        }
                    })
                })
               
            }
            if(!urlProps.picId) window.loadedFirstPageAllImage = true
        }
        UserLogic.initUserAIInfo();
        this.recordLoadTimeTimer();
        this.recordFCPAndLCP();
        this.recordFID();
        this.loadDefaultFonts();
        this.autoShowImgEhanceUploadLoad();
    }
    private static autoShowImgEhanceUploadLoad(){
        const urlProps: IAnyObj = IPSConfig.getProps();
         // 变清晰弹弹窗
         if(urlProps.imgEnhance == 1){
            setTimeout(()=>{
                emitter.emit('UserUploadBoxUploadFile', 'imgEnhance')
                assetManager.setPv_new(8795)
            }, 2000)
        }
    }
    /* 记录时间 */
    private static recordLoadTimeTimer() {
        const urlProps = IPSConfig.getProps();
        const { picId, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        this._recordLoadTimeTimer = window.setTimeout(() => {
            if (
                window.user_template_get_ended &&
                window.user_resorce_post_end &&
                window.dom_onload_is_loaded &&
                this.isPostedNewTimeRecord !== 1 &&
                window.recordLoadTime_is_done
            ) {
                this.isPostedNewTimeRecord = 1;
                // 新时间统计
                /*
                数据接收时间 = 数据接收结束时间 - 数据接收开始时间
                dom加载时间  = dom解析完成时间 - dom开始加载时间  （不包含用户模板数据）
                资源加载时间 = 页面加载完成时间 - dom解析完成时间
                用户模板dom加��时间 = 全部dom完全加载时间 - 用户模板数据请求结束
                用户模板资源加载时间 = 用户资源加载最晚时间 - 用户资源加载最早时间
    
                 */
                const nowTime = new Date().getTime();
                const newTimeObj = {
                    dateRecieve: window.recieveEndTime - window.performance.timing.navigationStart, //数据接收
                    domLoaded: window.dom_parse_end_time - window.recieveEndTime, //dom加载
                    resorceLoaded: window.all_resorce_loaded - window.recieveEndTime, //资源加载
                    userTemplateLoaded: nowTime - window.user_template_get_ended, //用户模板dom加载时间
                    userTemplateResorceLoaded: window.user_resorce_post_end - window.user_resorce_post_start, //用户模板资源加载时间
                };
                const timing = performance.timing;
                try {
                    assetManager.setPv_new(2570, {
                        additional: {
                            ut: urlProps.upicId,
                            ti: picId,
                            i0: newTimeObj.dateRecieve,
                            i1: newTimeObj.domLoaded,
                            i2: newTimeObj.resorceLoaded,
                            i3: newTimeObj.dateRecieve + newTimeObj.domLoaded + newTimeObj.resorceLoaded, //基础数据总时间
                            dt: newTimeObj.userTemplateLoaded,
                            it: newTimeObj.userTemplateResorceLoaded,
                            st:timing.domainLookupEnd - timing.domainLookupStart,
                            rt:timing.connectEnd - timing.connectStart,
                            i4:timing.responseStart - timing.requestStart,
                        },
                    });
                    // emitter.emit('getDownloadNumInfo')
                    emitter.emit('finishLoadPage');
                    TemplResExceptionPv.setEndLoadTime();
                } catch (e) {
                    console.error(e, '----e----');
                }
                window.record_time_info_is_done = true;

                // 新时间统计

                clearTimeout(this._recordLoadTimeTimer);
                this._recordLoadTimeTimer = null;
            } else {
                /* *******图片首次加载时 loading在画布上方显示******** */
                this.checkImgAndFontLoad();

                this.recordLoadTimeTimer();
            }
        }, 100);
    }

    /**
     * 初始化需请求资源的元素
     */
    private static initalNeedPostDatas(datas: IAsset[] = []) {
        //标记元素
        window.user_resorce_post_start = new Date().getTime();
        const svgResource: Record<string, string> = {};
        const svgUrl: Record<string, string> = {};
        const svgAssets: Record<string, IAsset[]> = {};
        const specialTextResource: Record<string, IAsset[]> = {};
        const cutImageResource: Record<string, string> = {};
        const cutImageAssets: Record<string, IAsset[]> = {};
        // const svgPostKeys = {}; //svg
        // const specialTags = {}; //特效字 IDs
        // const specialAssets: any[] = []; //特效字元素s
        const cuttedImgs = []; //裁剪图片s
        const cuttedContainers = {}; //裁剪框
        datas.forEach((v) => {
            const { type } = v.meta;
            const { resId = '', source_key = '', container, SVGUrl } = v.attribute;
            if (type === 'SVG') {
                // svgPostKeys[resId] = source_key;
                svgResource[resId] = source_key;
                svgUrl[resId] = SVGUrl;
                svgAssets[resId] ? svgAssets[resId].push(v) : (svgAssets[resId] = [v]);
            }
            if (type === 'text') {
                const k = v.attribute.effect.split('@')[0];
                specialTextResource[k] ? specialTextResource[k].push(v) : (specialTextResource[k] = [v]);
                // specialTags[v.attribute.effect.split('@')[0]] = 1; //只需要前面的 特效字标识ID
                // specialAssets.push(v);
            }
            if (type === 'image' || type === 'background' || type === 'pic') {
                //图片裁剪
                cuttedImgs.push(v);
                const obj = {
                    postUrl: container['source_key'],
                    resId: resId,
                };
                cuttedContainers[container.id] = obj;
                cutImageResource[container.id] = container['source_key'];
                cutImageAssets[container.id]
                    ? cutImageAssets[container.id].push(v)
                    : (cutImageAssets[container.id] = [v]);
            }
        });
        for (const key in svgResource) {
            // svg
            if (svgUrl[key]) {
                fetch(svgUrl[key]).then((res) => {
                    res.blob().then((blob) => {
                        const reader = new FileReader();
                        reader.onload = (event) => {
                            let content: string = reader.result as string; //内容就在这里
                            content = content.substring(content.indexOf('<svg'), content.length);
                            UpdateAsset.updateAssets(
                                'ADD_SVG_END',
                                svgAssets[key].map((a) => {
                                    return {
                                        index: a.meta.rt_page_assets_index,
                                        pageIndex: a.meta.rt_page_index,
                                        className: a.meta.className,
                                        changes: {
                                            attribute: {
                                                picUrl: content,
                                            },
                                        },
                                    };
                                }),
                                2,
                            );
                            window.user_resorce_post_end = new Date().getTime();
                        };
                        reader.readAsText(blob);
                    });
                });
            } else {
                assetManager.getImgRUrl(svgResource[key]).then((data) => {
                    data.json().then((resultData) => {
                        if (Number(resultData.stat) === 0) {
                            alert('服务器错误，请联系客服：1994432176');
                            return false;
                        }
                        UpdateAsset.updateAssets(
                            'ADD_SVG_END',
                            svgAssets[key].map((a) => {
                                return {
                                    index: a.meta.rt_page_assets_index,
                                    pageIndex: a.meta.rt_page_index,
                                    className: a.meta.className,
                                    changes: {
                                        attribute: {
                                            picUrl: resultData.msg,
                                        },
                                    },
                                };
                            }),
                            2,
                        );
                        window.user_resorce_post_end = new Date().getTime();
                    });
                });
            }
        }
        for (const key in specialTextResource) {
            //特效字
            // const curAssets = specialAssets.filter((v) => v.attribute.effect.split('@')[0] == key);
            assetManager.getSpecificWordInfo(key).then((data) => {
                data.json().then((resultData) => {
                    if (Number(resultData.stat) === 1) {
                        AssetLogic.updateMutipleTextSpecificWorldInfo({
                            assets: specialTextResource[key],
                            effectVariant: resultData.msg,
                        });
                    }
                    specialTextResource[key].forEach((a) => {
                        emitter.emit('changeRntimeType', a.meta);
                    });
                    // curAssets.map((_asset) => {
                    //     emitter.emit('changeRntimeType', _asset.meta);
                    // });
                    window.user_resorce_post_end = new Date().getTime();
                });
            });
        }
        for (const key in cutImageResource) {
            //图片裁剪框元素
            // const postUrl = cuttedContainers[key].postUrl,
            //     _resId = cuttedContainers[key].resId;

            assetManager.getImgRUrl(cutImageResource[key]).then((data) => {
                data.json().then((resultData) => {
                    if (Number(resultData.stat) === 0) {
                        alert('服务器错误，请联系客服：1994432176');
                        return false;
                    }

                    UpdateAsset.updateAssets(
                        'ADD_SVG_END',
                        cutImageAssets[key].map((a) => {
                            console.log(a.meta.className);
                            return {
                                index: a.meta.rt_page_assets_index,
                                pageIndex: a.meta.rt_page_index,
                                className: a.meta.className,
                                changes: {
                                    attribute: {
                                        container: {
                                            picUrl: resultData.msg,
                                        },
                                    },
                                },
                            };
                        }),
                        2,
                    );
                });
            });
        }
        window.user_resorce_post_end = new Date().getTime();
        //标记结束
    }

    /* 检查元素是否加载完 */
    private static checkImgAndFontLoad() {
        if (this.isChecking || this.isCheckFinish) {
            return;
        }
        this.isChecking = true;
        const { work, pageInfo, rt_is_online_detail_page, rt_loadedTextFontClass, isDesigner } = storeAdapter.getStore<
            typeof storeAdapter.store_names.paintOnCanvas
        >({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (RedoAndUndo.getPastLength() === 0 && RedoAndUndo.getFutureLength() === 0) {
            if (!this.isInitCheck && this.checkImgAndFontMap.size === 0) {
                this.totalAssetsCount = 0;
                let pages;
                if (rt_is_online_detail_page) {
                    pages = work.pages;
                } else {
                    pages = [work.pages[pageInfo.pageNow]];
                }
                pages.forEach((page: IPage) => {
                    this.totalAssetsCount += page.assets.length;
                    !isDesigner &&
                        page.assets.forEach((asset: IAsset) => {
                            const type = asset.meta.type;
                            // 只有存在 meta.rt_page_index 的元素 认为是首屏元素
                            if (type === 'SVG' || type === 'pic' || type === 'image' || type === 'background') {
                                if (asset.attribute.picUrl === 'loading' && asset.meta.rt_page_index >= 0) {
                                    this.checkImgAndFontMap.set(asset.meta.className, asset);
                                }
                            }
                            if (
                                type === 'text' &&
                                asset.meta.rt_page_index >= 0 &&
                                !rt_loadedTextFontClass[asset.meta.className]
                            ) {
                                this.checkImgAndFontMap.set(asset.meta.className, asset);
                            }
                        });
                });
                if (this.totalAssetsCount > 0) {
                    this.isInitCheck = true;
                }
                if (isDesigner && this.totalAssetsCount === 0) {
                    this.isInitCheck = true;
                    this.isCheckFinish = true;
                    this.showInitalLoading = false;
                    this.totalAssetsCount = 1; // 用于结束触发判断
                }
            } else {
                this.checkImgAndFontMap.forEach((asset: IAsset, className: string) => {
                    const type = asset.meta.type;
                    if (type === 'SVG' || type === 'pic' || type === 'image' || type === 'background') {
                        const newAsset = AssetHelper.find(work, pageInfo, {
                            index: asset.meta.rt_page_assets_index,
                            pageIndex: asset.meta.rt_page_index,
                            className,
                        });
                        // work.pages[asset.meta.rt_page_index].assets.find((a: IAsset) => a.meta.className === asset.meta.className)
                        if (newAsset && newAsset.attribute && newAsset.attribute.picUrl !== 'loading') {
                            this.checkImgAndFontMap.delete(className);
                        } else if (!newAsset) {
                            // 如果加载期间元素已经被删除，从统计 map 里面移除
                            this.checkImgAndFontMap.delete(className);
                        }
                    }
                    if (type === 'text' && rt_loadedTextFontClass[className]) {
                        this.checkImgAndFontMap.delete(className);
                    }
                });
                if (this.checkImgAndFontMap.size === 0) {
                    this.isCheckFinish = true;
                    this.showInitalLoading = false;
                }
            }
        } else {
            this.isInitCheck = true;
            this.isCheckFinish = true;
            this.showInitalLoading = false;
            this.totalAssetsCount = 1; // 用于结束触发判断
        }
        this.isChecking = false;
        if (
            !this.showInitalLoading &&
            !window.recordLoadTime_is_done &&
            this.totalAssetsCount !== 0 &&
            this.isPostedNewTimeRecord !== 1
        ) {
            // 完全加载完成记录时间点
            window.recordLoadTime_is_done = 1;
            CanvasPaintedLogic.firstLoadCanvas({ rt_first_load_is_done: true });
            console.log(new Date().getTime() - window.performance.timing.navigationStart, '-----loading end------');
        }
    }

    /* 专门统计 fcp, lcp */
    private static recordFCPAndLCP() {
        if (process.env.NODE_ENV === 'development' || !env.stats_FCP_LCP_FID) {
            return;
        }

        const sendFCP = () => {
            const urlProps = IPSConfig.getProps();
            const { picId } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            assetManager.setPv_new(7728, {
                additional: {
                    ut: urlProps.upicId,
                    ti: picId,
                    i0: window.fcpTime,
                    // i1: window.lcpTime ?? 0, // 改为单独埋点
                    i2: window.dom_parse_end_time - window.recieveEndTime, //dom加载,
                    i3: window.all_resorce_loaded - window.recieveEndTime, //资源加载,
                    i4: window.recieveEndTime,
                },
            });
        }

        const sendLCP = (value: number) => {
            const urlProps = IPSConfig.getProps();
            const { picId } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            assetManager.setPv_new(7785, {
                additional: {
                    ut: urlProps.upicId,
                    ti: picId || urlProps.picId,
                    i0: value,
                    i1: window.recieveEndTime,
                },
            });
        }

        const timer = window.setInterval(() => {
            try {
                if (
                    typeof window.fcpTime === 'number' &&
                    // (typeof window.lcpTime === 'number' || typeof window.fidDuration === 'number') &&
                    typeof window.dom_parse_end_time === 'number' &&
                    typeof window.all_resorce_loaded === 'number'
                ) {
                    sendFCP();
                    clearInterval(timer);
                    // 清除原先的 LCP 监听
                    webVitals.onLCP(undefined);
                    const timeOut = setTimeout(() => {
                        // 增加新的监听，用于捕获非首次的 LCP 变化
                        webVitals.onLCP(
                            (info: any) => {
                                window.lcpTime = Math.round(info.value);
                                sendLCP(window.lcpTime);
                            },
                            { reportAllChanges: true },
                        );
                        clearTimeout(timeOut);
                    }, 100);
                }
            } catch (e) {
                console.error('recordFCPAndLCP error: ', e);
            }
        }, 200);
    }

    // 专门统计 fid
    private static recordFID() {
        if (process.env.NODE_ENV === 'development' || !env.stats_FCP_LCP_FID) {
            return;
        }
        const timer = window.setInterval(() => {
            try {
                if (
                    typeof window.fidDuration === 'number' &&
                    typeof window.fidStart === 'number'
                ) {
                    const urlProps = IPSConfig.getProps();
                    const { picId } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                        store_name: storeAdapter.store_names.paintOnCanvas,
                    });
                    assetManager.setPv_new(7729, {
                        additional: {
                            ut: urlProps.upicId,
                            ti: picId,
                            i0: window.fidDuration,
                            i1: window.fidStart,
                            i2: window.recieveEndTime,
                        },
                    });
                    clearInterval(timer);
                }
            } catch (e) {
                console.error('recordFID error: ', e);
            }
        }, 200);
    }

    private static loadTemplateImageFirst(assets: IAsset[], preview: string[]) {
        let total = 0;
        let count = 0;
        const countCallback = () => {
            count++;
            if (count === total) {
                window.loadedFirstPageAllImage = true;
            }
        }
        assets.forEach((a) => {
            if (
                (a.meta.type === 'image' || a.meta.type === 'background' || a.meta.type === 'pic') &&
                a.attribute.picUrl &&
                a.attribute.picUrl !== 'loading'
            ) {
                total++;
                const img = new Image();
                img.onload = countCallback
                img.onerror = countCallback
                img.src = a.attribute.picUrl;
            }
        })
        if (total === 0) {
            window.loadedFirstPageAllImage = true;
        } else {
            if (preview?.[0]) {
                const img = new Image();
                img.src = preview[0];
            }
        }
    }
    private static replaceUrlForEditorCombine(){
        // 合并ueTeam到ue
        if (window.location.href.includes('ueteam.818ps.com')) {
            location.href = location.href.replace(/https:\/\/ueteam.818ps.com(\/v\d+)?/g, 'https://ue.818ps.com');
        }
        // 合并ecommerce/v3 到 ue
        if(window.location.href.includes('ecommerce.818ps.com/v3')){
            location.href = location.href.replace(/https:\/\/ecommerce.818ps.com(\/v\d+)?/g, 'https://ue.818ps.com/v4') + '&ecommerce=1';
        }
        // 合并ecommerceteam 到 ue, v1,v2版本保留
        if(window.location.href.includes('ecommerceteam.818ps.com') && !window.location.href.includes('ecommerceteam.818ps.com/v1') && !window.location.href.includes('ecommerceteam.818ps.com/v2')){
            location.href = location.href.replace(/https:\/\/ecommerceteam.818ps.com(\/v\d+)?/g, 'https://ue.818ps.com/v4') + '&ecommerce=1';
        }
    }
    private static loadDefaultFonts(){
        // 默认加载思源黑体
        const loadFamilys= ['font130']
        if(window.requestIdleCallback){
            window.requestIdleCallback(()=>{
                LoadFont.loadFont(loadFamilys)
            })
        }else{
            LoadFont.loadFont(loadFamilys)
        }  
    }

}
