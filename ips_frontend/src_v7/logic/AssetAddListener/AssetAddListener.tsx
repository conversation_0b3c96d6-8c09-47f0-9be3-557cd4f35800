import { EventSubscription } from 'fbemitter';
import { multiply } from 'mathjs';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { IPSConfig } from '@v7_utils/IPSConfig';
import { addEventListener, EventListener } from '@v7_utils/AddEventListener';
import {
    IAsset,
    ListenerEventInterface,
    TStoreAction,
    IEffectVariant3D,
    IGroupWordListItemInfo,
    IDoc,
    IAnyObj,
    IUserListVideoE,
} from '@v7_logic/Interface';
import { ESelectType } from '@v7_logic/Enum';
import { UpdateAsset, UpdateAssetTypeContainer, SelectAsset, AssetLogic } from '@v7_logic/AssetLogic';
import { fitPageDomSIze } from './FitPageDomSize';
import { klona as cloneDeep } from 'klona';
// 待更新组件
import { emitter } from '@component/Emitter';
import { assetTemplate } from '@component/AssetMap';
import { assetManager } from '@component/AssetManager';
import { attributes } from '@component/canvas/data';
import { templateFormat } from '@src/userComponentV6.0/TemplateFormat';
import { CanvasPaintedLogic } from '@v7_logic/CanvasPaintedLogic';
import { IPaintOnCanvasState } from '@v7_store/redux/reducers/onCanvasPaintedReducer';
import { colorTable } from '@v7_logic/AssetEChartLogic/data/const';
import { IAccumulationInAttribute } from '@v7_logic/AssetEChartLogic/type/type';
import { AssetFrameLogic } from '@v7_logic/AssetFrameLogic';
import { env } from '@editorConfig/env';
import { IRichText } from '@tgs/types';
import { TemplateCanvasLogic } from '@v7_logic/TemplateCanvasLogic/TemplateCanvasLogic';
import { fontsList } from '@component/FontsList/FontsList';
import {generateShortUUID} from '@src/userComponentV6.0/Function'
import { getSpecificWordInfo } from './cacheData';
import { applyToPoint } from 'transformation-matrix';

const math = { multiply };

/**
 * 添加
 */
class AssetAddListener implements ListenerEventInterface {
    private emitter_listener: {
        [key: string]: EventSubscription;
    } = {};

    private dragAddMoveListener: EventListener;
    private dragAddUpListener: EventListener;
    private dragClientX: number;
    private dragClientY: number;
    private assetsBack: IAnyObj;
    private isDragAssetEnterFrame:boolean; // 拖动的元素是否进入相框区域内

    /**
     * 监听新建
     */
    public listenerEvent = (): (() => void) => {
        this.unListenerEvent();

        this.addSVGEmitter();
        this.addElementEmitter();
        this.addBackgroundEmitter();
        this.addWordArtEmitter();
        this.addEmojiEmitter();
        this.addPicEmitter();
        this.addPhotographyEmitter();
        this.addPersonEmitter();
        this.addChartEmitter();
        this.addTableEmitter();
        this.addContainerEmitter();
        this.dragAddDownEmitter();
        this.turnpage();
        this.addFrameEmitter();// 订阅添加相框
        this.listenIsEnterFrame() // 监听鼠标拖动元素过程中是否在相框元素内
        this.addFlowEmitter();
        this.addLineEmitter();
        return this.unListenerEvent;
    };

    /**
     * 监听销毁
     */
    public unListenerEvent = () => {
        for (const key in this.emitter_listener) {
            this.emitter_listener[key].remove();
        }
        this.emitter_listener = {};
    };

    /**
     * 调用 store 更新
     */
    private addAssets(fun_name: string, assets: (IAsset | { asset: IAsset; pageIndex: number })[], autoSave: 0 | 1 | 2 | number, actions: TStoreAction[] = [], unrecordable = false) {
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: fun_name,
            params: [
                {
                    type: 'addAssets',
                    params: {
                        assets,
                        autoSave,
                        unrecordable,
                        isGroupWord: fun_name == 'ADD_GROUP_WORD',
                    },
                },
                ...(actions || []),
            ],
        });
    }

    /**
     * 添加背景元素
     */
    private addBackgroundAssets(
        fun_name: string,
        imageInfo: {
            height: number;
            width: number;
            rt_searchWord?: string;
            rt_isNowAdd?: boolean;
            rt_ratioId?: string;
            id: string;
            image_url: string;
            source_height: number;
            source_width: number;
            source_key?: string;
        },
        autoSaveFlag: 0 | 1 | 2 | number,
        params: {
            subFunName?: string;
            pageIndex?: number;
        },
    ) {
        let asset: IAsset = cloneDeep(assetTemplate.background);
        const { subFunName } = params;
        const { canvas } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        let width;
        let height;

        width = canvas.width;
        height = (width * imageInfo.height) / imageInfo.width;

        if (height < canvas.height) {
            height = canvas.height;
            width = (height * imageInfo.width) / imageInfo.height;
        }

        asset = {
            ...asset,
            meta: {
                ...asset.meta,
                type: 'background',
                rt_searchWord: imageInfo.rt_searchWord,
                rt_isNowAdd: imageInfo.rt_isNowAdd,
                rt_ratioId: imageInfo.rt_ratioId,
            },
            attribute: {
                ...asset.attribute,
                resId: imageInfo.id,
                picUrl: imageInfo.image_url ? imageInfo.image_url : 'loading',
                width: Math.round(width),
                height: Math.round(height),
                assetHeight: imageInfo.source_height,
                assetWidth: imageInfo.source_width,
                cropXTo: 1,
                cropYTo: 1,
            },
            transform: {
                ...asset.transform,
                posX: (canvas.width - width) / 2,
                posY: (canvas.height - height) / 2,
            },
            // subRecord: { // 已经不统计了
            //     origin: props.origin,
            // },
        };

        /* 单一背景 */
        let { work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const pageIndex = params.pageIndex || pageInfo.pageNow;
        const page = work.pages[pageIndex];
        const len = page.assets.length;
        let index = -1;
        let cssIndex = 0;
        let className: string;
        for (let i = len - 1; i >= 0; i--) {
            if (page.assets[i].meta.type === 'background') {
                if (page.assets[i].meta.index >= cssIndex) {
                    // 更新最上层的背景元素, 复制时会复制出多个背景, 所以只改最上面的一个
                    cssIndex = page.assets[i].meta.index;
                    index = i;
                    className = page.assets[i].meta.className;
                }
            }
        }
        const tempProps = IPSConfig.getProps();
        if (index === -1) {
            index = work.pages[pageIndex].assets.length;
            this.addAssets(
                fun_name,
                [asset],
                autoSaveFlag,
                !tempProps.isDesigner
                    ? [
                          {
                              type: 'selectAsset',
                              params: {
                                  asset_index: index,
                                  page_num: pageIndex,
                                  select_type: ESelectType.asset,
                              },
                          },
                      ]
                    : [],
            );
            ({ work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                store_name: storeAdapter.store_names.paintOnCanvas,
            }));
            className = work.pages[pageIndex].assets[index].meta.className;
        } else {
            asset.meta.index = cssIndex;
            UpdateAsset.updateAssets(
                fun_name,
                [
                    {
                        index,
                        className,
                        changes: {
                            ...asset,
                        },
                    },
                ],
                autoSaveFlag,
                !tempProps.isDesigner
                    ? [
                          {
                              type: 'selectAsset',
                              params: {
                                  asset_index: index,
                                  page_num: pageIndex,
                                  select_type: ESelectType.asset,
                              },
                          },
                      ]
                    : [],
            );
        }

        if (!imageInfo.image_url) {
            const { work } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            const className = work.pages[pageIndex].assets[index].meta.className;
            this.updatePicUrl(imageInfo.source_key, index, className, undefined, subFunName);
        }
    }

    /**
     * 点击添加艺术字（全局事件）
     */
    private turnpage() {
        // if (this.emitter_listener.emoji_emitter) {
        //     this.emitter_listener.emoji_emitter.remove();
        // }
        // window.addEventListener('message', e => {
        //     // e.data为子页面发送的数据
        //     // @ts-ignore
        //     // if( e.data.pages && e.data.pages.length>0){
        //     //     // CanvasPaintedLogic.updatePages(e.data.pages)
        //     // }else if(e.data === "clearAllAssets"){
        //     //     // CanvasPaintedLogic.clearAllAssets()
        //     // }
        //     console.log(e)
        // })
    }

    /**
     * 调整宽高
     */
    private fitWidthAndHeight(
        elementWidth: number,
        elementInfo: {
            width: number;
            height: number;
        },
        ratio = 2,
    ) {
        const { canvas } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        let width = Math.round(elementWidth);
        let height: number;
        width = canvas.width / ratio < width ? canvas.width / ratio : width;
        height = (width * Math.round(elementInfo.height)) / Math.round(elementInfo.width);
        if (height > canvas.height) {
            height = canvas.height / ratio < height ? canvas.height / ratio : height;
            width = (height * Math.round(elementInfo.width)) / Math.round(elementInfo.height);
        }
        return {
            width,
            height,
        };
    }

    /**
     * 添加到收藏列表
     */
    private updateFav(resId: string | number, type: number) {
        assetManager.setAssetHistoryRecord(resId, type).then((data) => {
            data.json().then((resultData) => {
                if (resultData.stat === 1) {
                    emitter.emit('updateFavImageList');
                }
            });
        });
    }

    private updatePicUrl(
        sourceKey: string,
        index: number,
        className: string,
        autoSave: 0 | 1 | 2 | number,
        fun_name: string,
    ) {
        assetManager.getImgRUrl(sourceKey).then((data) => {
            data.json().then((resultData) => {
                if (resultData.stat === 0) {
                    alert('服务器错误，请联系客服：1994432176');
                    return false;
                }
                UpdateAsset.updateAssets(
                    fun_name,
                    [
                        {
                            index,
                            className,
                            changes: {
                                attribute: {
                                    picUrl: resultData.sample_2k || resultData.sample,
                                    rt_picUrl: resultData.sample_2k || resultData.sample,
                                },
                            },
                        },
                    ],
                    autoSave,
                );
            });
        });
    }
    /**
     * 监听元素拖动是否进入相框
     */
    private listenIsEnterFrame(){
        emitter.addListener('checkIsEnterFrame',(isEnterFrame:boolean)=>{
            this.isDragAssetEnterFrame = isEnterFrame
        })
    }
    /**
     * 拖动添加元素（鼠标按下事件）(全局监听事件)
     **/
    dragAddDownEmitter() {
        if (this.emitter_listener.add_down_emitter) {
            this.emitter_listener.add_down_emitter.remove();
        }
        this.emitter_listener.add_down_emitter = emitter.addListener(
            'ListDragAddDown',
            (
                element: {
                    [key: string]: any;
                    [key: number]: any;
                },
                type: string,
                e: MouseEvent,
            ) => {
                this.dragAddDown(element, type, e);
            },
        );
    }

    /**
     * 拖动添加元素（鼠标按下事件）
     **/
    dragAddDown(
        element: {
            [key: string]: any;
            [key: number]: any;
        },
        type: string,
        e: MouseEvent,
    ) {
        let extend = {
            width: (e.target as HTMLElement).offsetWidth,
            height: (e.target as HTMLElement).offsetHeight,
        };
        if (extend.width < 90) {
            extend = Object.assign(extend, {
                width: 90,
                height: (90 * element.height || 1) / (element.width || 1),
                opacity: 0.5,
            });
        }
        const { work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        // 取消背景状态编辑
        SelectAsset.blurAssetBackground()

        if (this.dragAddMoveListener) {
            this.dragAddMoveListener.remove();
        }

        if (this.dragAddUpListener) {
            this.dragAddUpListener.remove();
        }

        this.dragClientX = e.clientX;
        this.dragClientY = e.clientY;

        this.assetsBack = work.pages[pageInfo.pageNow].assets;

        this.dragAddMoveListener = addEventListener(window, 'mousemove', (e: MouseEvent) => {
            this.dragAddMoveEvent(element, type, extend, e);
        });

        this.dragAddUpListener = addEventListener(window, 'mouseup', async (e: MouseEvent) => {

            this.dragAddUpListener.remove();
            this.dragAddMoveListener.remove();
            emitter.emit('dragAssetEnd')

            const { containerAddedAsset, rt_editor_type, rt_canvas_render_mode, canvas, pageAttr, work, pageInfo } = storeAdapter.getStore<
                typeof storeAdapter.store_names.paintOnCanvas
            >({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            // 清空待添加容器内容
            if (containerAddedAsset != '' || containerAddedAsset === undefined) {
                // canvasStore.dispatch(paintOnCanvas('UPDATE_CONTAINER_CONTENTINFO_END'));
                // canvasStore.dispatch(paintOnCanvas('UPDATE_CONTAINER_ADDED_ASSET', {containerAddedAsset: ''}));
                // TODO 合并成一次 store 更新
                UpdateAssetTypeContainer.updateContainerContentInfoEnd();
                UpdateAssetTypeContainer.updateContainerAddedAsset({ containerAddedAsset: '' });
                return false;
            }

            const canvasLayoutDom: HTMLElement = document.getElementsByClassName('canvasLayout')[0] as HTMLElement,
                infoLayoutDom: HTMLElement = document.getElementsByClassName('infoLayout')[0] as HTMLElement,
                canvasContentDom: HTMLElement = document.getElementsByClassName('canvasContent')[0] as HTMLElement,
                rightLayoutDom: HTMLDivElement = document.querySelector('.rightLayout');
            const baseGap = 60;
            
            let isEnterCanvas = true;
            if (e.clientY < infoLayoutDom.offsetHeight + canvas.y) {
                isEnterCanvas =  false;
            }
            const len = pageAttr?.pageInfo?.length || pageAttr?.backgroundImage?.length || 1;
            // 多页模式
            let canvasAllPageHeight = canvas.height * canvas.scale + canvas.y;
            // 拉起模式下
            if (rt_editor_type === 'canvas' && rt_canvas_render_mode === '') {
                canvasAllPageHeight = canvas.height * canvas.scale * len + baseGap * (len - 1) + canvas.y;
            } 

            if (e.clientY > infoLayoutDom.offsetHeight + canvasAllPageHeight) {
                isEnterCanvas =  false;
            }

            if (e.clientX < canvasLayoutDom.offsetLeft + canvas.x) {
                isEnterCanvas = false;
            }

            if (e.clientX > canvasLayoutDom.offsetLeft + canvas.x + canvas.width * canvas.scale) {
                isEnterCanvas = false;
            }

            if (e.clientX < rightLayoutDom.offsetLeft + rightLayoutDom.offsetWidth) {
                isEnterCanvas = false;
            }
            const dragAreaDom = document.getElementById('dragArea');
            if (isEnterCanvas || rt_canvas_render_mode === 'board') {
                // 进入画布
                dragAreaDom.style.display = 'none';
                dragAreaDom.innerHTML = '';
            } else {
                // 未进入画布 交互 做一个返回原位置的动画 
                dragAreaDom.style.left = this.dragClientX - extend.width + 'px';
                dragAreaDom.style.top = this.dragClientY - extend.height + 'px';
                dragAreaDom.style.transform = 'scale(0.5)';
                dragAreaDom.style.opacity = '0';
                dragAreaDom.style.transition= 'all .4s ease-in-out';
                const resetDragDomTimer =  setTimeout(() => { 
                    dragAreaDom.style.opacity = '1';
                    dragAreaDom.style.display = 'none';
                    dragAreaDom.innerHTML = '';
                    dragAreaDom.style.transition = '';
                    dragAreaDom.style.transform = '';
                    clearTimeout(resetDragDomTimer);
                }, 400)
                return false;                
            }
            let asset: IAsset, width: number, height: number;

            const pagesSize: {
                top: number;
                bottom: number;
            }[] = [];
            const defaultY = e.clientY - infoLayoutDom.offsetHeight - parseFloat(canvasContentDom.style.top);
            let pi = pageInfo.pageNow;
            let offsetY = 0;
            if (rt_editor_type === 'canvas' && !rt_canvas_render_mode) {
                for (let i = 0; i < work.pages.length; i++) {
                    pagesSize.push({
                        top: (canvas.height * canvas.scale + baseGap) * i,
                        bottom: (canvas.height * canvas.scale) * (i + 1) + baseGap * i,
                    });
                }
                for (const i in pagesSize) {
                    if (pagesSize[i].top<= defaultY && defaultY <= pagesSize[i].bottom) {
                        pi = Number(i)
                    }
                }
                if (defaultY < 0) {
                    pi = 0;
                }
                if (defaultY > pagesSize[pagesSize.length - 1].bottom) {
                    pi = pagesSize.length - 1;
                }
                offsetY = -pagesSize[pi].top;
            }

            if (type === 'image') {
                if(this.isDragAssetEnterFrame) return
                asset = cloneDeep(assetTemplate.image);
                width = extend.width * 2 >= 300 ? 300 : extend.width * 2;
                ({ width, height } = this.fitWidthAndHeight(width, { width: element.width, height: element.height }));
                width = width / canvas.scale;
                height = height / canvas.scale;

                const posX = e.clientX - canvasLayoutDom.offsetLeft - parseFloat(canvasContentDom.style.left),
                    posY = defaultY + offsetY;

                asset = {
                    ...asset,
                    meta: {
                        ...asset.meta,
                        type: 'image',
                    },
                    attribute: {
                        ...asset.attribute,
                        resId: element.id,
                        picUrl: element.image_url ? element.image_url : 'loading',
                        width: Math.round(width),
                        height: Math.round(height),
                        assetHeight: element.source_height,
                        assetWidth: element.source_width,
                        cropXTo: 1,
                        cropYTo: 1,
                        filters: {
                            brightness: 0,
                            saturate: 0,
                            contrast: 0,
                            blur: 0,
                            sharpen: 0,
                            hue: 0,
                        },
                    },
                    transform: {
                        posX: posX / canvas.scale - Math.round(width) / 2,
                        posY: posY / canvas.scale - Math.round(height) / 2,
                    },
                };

                const index = work.pages[pi].assets.length;
                this.addAssets('IMAGE_DRAG_ADD', [{asset, pageIndex: pi}], undefined);

                if (!element.image_url) {
                    const { work } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                        store_name: storeAdapter.store_names.paintOnCanvas,
                    });
                    const className = work.pages[pi].assets[index].meta.className;
                    this.updatePicUrl(element.source_key, index, className, undefined, 'IMAGE_DRAG_ADD_END');
                }
                assetManager.setPv_new(176, {
                    additional: {
                        s0: element.id,
                    },
                });
            } else if (type === 'pic') {
                if(this.isDragAssetEnterFrame) return
                asset = cloneDeep(assetTemplate.image);
                width = extend.width * 2 >= 300 ? 300 : extend.width * 2;
                ({ width, height } = this.fitWidthAndHeight(width, { width: element.width, height: element.height }));
                width = width / canvas.scale;
                height = height / canvas.scale;

                const posX = e.clientX - canvasLayoutDom.offsetLeft - parseFloat(canvasContentDom.style.left),
                    posY = defaultY + offsetY;

                asset = {
                    ...asset,
                    meta: {
                        ...asset.meta,
                        type: 'pic',
                    },
                    attribute: {
                        ...asset.attribute,
                        owner: element.owner,
                        resId: element.id,
                        picUrl: element.image_url ? element.image_url : 'loading',
                        width: Math.round(width),
                        height: Math.round(height),
                        assetHeight: element.source_height,
                        assetWidth: element.source_width,
                        cropXTo: 1,
                        cropYTo: 1,
                        filters: {
                            brightness: 0,
                            saturate: 0,
                            contrast: 0,
                            blur: 0,
                            sharpen: 0,
                            hue: 0,
                        },
                    },
                    transform: {
                        ...asset.transform,
                        posX: posX / canvas.scale - Math.round(width) / 2,
                        posY: posY / canvas.scale - Math.round(height) / 2,
                    },
                };

                if (e.clientY - this.dragClientY > 20 || e.clientX - this.dragClientX > 20) {
                    const index = work.pages[pi].assets.length;
                    this.addAssets('PIC_DRAG_ADD', [{asset, pageIndex: pi}], undefined);

                    if (!element.image_url) {
                        const { work } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                            store_name: storeAdapter.store_names.paintOnCanvas,
                        });
                        const className = work.pages[pi].assets[index].meta.className;
                        this.updatePicUrl(element.source_key, index, className, undefined, 'PIC_DRAG_ADD_END');
                    }
                }
                assetManager.setPv_new(176, {
                    additional: {
                        s0: element.id,
                    },
                });
                if (element?.album_type === '3') {
                    this.updateFav(element.id, 4);
                } else if (element?.album_type === '2' || element?.album_type === '5') {
                    this.updateFav(element.id, 1);
                }
            } else if (type === 'background') {
                this.addBackgroundAssets(
                    'BACKGROUND_DRAG_ADD',
                    {
                        height: element.height,
                        width: element.width,
                        rt_searchWord: element.rt_searchWord,
                        rt_isNowAdd: element.rt_isNowAdd,
                        rt_ratioId: element.rt_ratioId,
                        id: element.id,
                        image_url: element.image_url,
                        source_height: element.source_height,
                        source_width: element.source_width,
                        source_key: element.source_key,
                    },
                    undefined,
                    {
                        subFunName: 'BACKGROUND_DRAG_ADD_END',
                        pageIndex: pi,
                    },
                );
                assetManager.setPv_new(2465, { additional: {} });
                emitter.emit('ListAddBackground', element);
                // if (!element.image_url) {
                //     const { work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                //         store_name: storeAdapter.store_names.paintOnCanvas,
                //     });
                //     const className = work.pages[pageInfo.pageNow].assets[index].meta.className;
                // this.updatePicUrl(element.source_key, index, className, undefined, 'BACKGROUND_DRAG_ADD_END');
                // }
                assetManager.setPv_new(176, {
                    additional: {
                        s0: element.id,
                    },
                });
            } else if (type === 'text') {
                asset = element as IAsset;
                width = element.attribute.width;
                height = extend.height;

                const posX = e.clientX - canvasLayoutDom.offsetLeft - parseFloat(canvasContentDom.style.left),
                    posY = defaultY + offsetY;

                asset.transform = {
                    ...asset.transform,
                    posX: posX / canvas.scale - Math.round(width) / 2,
                    posY: posY / canvas.scale - Math.round(height) / 2,
                };

                if (e.clientY - this.dragClientY > 20 || e.clientX - this.dragClientX > 20) {
                    if (!asset.attribute.height) asset.attribute.height = asset.attribute.fontSize + 20 * 2;

                    const index = work.pages[pi].assets.length;
                    this.addAssets('TEXTEDITOR_DRAG_ADD', [{asset, pageIndex: pi}], undefined, [
                        {
                            type: 'selectAsset',
                            params: {
                                asset_index: work.pages[pi].assets.length,
                                page_num: pi,
                                select_type: ESelectType.asset,
                            },
                        },
                    ]);
                    const s0 = asset.attribute.effect ? 'specialWord' : '';
                    assetManager.setPv_new(2462, { additional: { s0, } });
                    assetManager.setPv_new(2461, { additional: { s0: type } });
                    // if(asset.attribute.effect){
                    //     // 普通文字不走dom编辑
                    //     window.setTimeout(() => {
                    //         emitter.emit('addTextEditingEventListener', asset);
                    //     }, 100);
                    // }
                    if (asset.attribute.effect) {
                        const effectVariant = await getSpecificWordInfo(asset.attribute.effect.split('@')[0]);
                        if (!effectVariant) {
                            return;
                        }
                        const { work } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                            store_name: storeAdapter.store_names.paintOnCanvas,
                        });
                        AssetLogic.updateTextSpecificWordInfo({
                            propAsset: work.pages[pi].assets[index],
                            effectVariant,
                            assetIndex: index,
                            effect: asset.attribute.effect,
                        });
                    }
                }
                

            } else if (type === 'container') {
                let asset: IAsset = cloneDeep(assetTemplate.image),
                    width,
                    height;
                width = extend.width * 2 >= 300 ? 300 : extend.width * 2;
                ({ width, height } = this.fitWidthAndHeight(width, { width: element.width, height: element.height }));
                width = width / canvas.scale;
                height = height / canvas.scale;

                const posX = e.clientX - canvasLayoutDom.offsetLeft - parseFloat(canvasContentDom.style.left),
                    posY = defaultY + offsetY;

                asset = {
                    ...asset,
                    meta: {
                        ...asset.meta,
                        type: 'container',
                    },
                    attribute: {
                        ...asset.attribute,
                        resId: element.id,
                        picUrl: element.image_url ? element.image_url : 'loading',
                        width: Math.round(width),
                        height: Math.round(height),
                        assetHeight: element.source_height,
                        assetWidth: element.source_width,
                        cropXTo: 1,
                        cropYTo: 1,
                        source_key: element.source_key,
                        contentInfo: [
                            {
                                resId: '',
                                imageUrl: '',
                                width: 0,
                                height: 0,
                                posX: 0,
                                posY: 0,
                                viewBoxWidth: 0,
                                viewBoxHeight: 0,
                            },
                        ],
                    },
                    transform: {
                        ...asset.transform,
                    },
                };
                asset.meta.type = 'container';
                asset.attribute = {
                    posX: posX / canvas.scale - Math.round(width) / 2,
                    posY: posY / canvas.scale - Math.round(height) / 2,
                };

                const index = work.pages[pi].assets.length;
                this.addAssets('CONTAINER_DRAG_ADD', [{asset, pageIndex: pi}], undefined, [
                    {
                        type: 'selectAsset',
                        params: {
                            asset_index: index,
                            page_num: pi,
                            select_type: ESelectType.asset,
                        },
                    },
                ]);

                // if( element.image_url ){
                //     Object.assign(asset.attribute, {
                //         picUrl: element.image_url,
                //     });
                //     canvasStore.dispatch(paintOnCanvas('CONTAINER_DRAG_ADD', {asset: Object.assign(asset)}));
                // }else{
                //     canvasStore.dispatch(paintOnCanvas('CONTAINER_DRAG_ADD', {asset: Object.assign(asset)}));
                // }
            } else if (type === 'SVG') {
                let asset: IAsset = cloneDeep(assetTemplate.SVG),
                    width,
                    height;
                width = extend.width * 2 >= 150 ? 150 : extend.width * 2;
                ({ width, height } = this.fitWidthAndHeight(width, { width: element.width, height: element.height }));
                width = width / canvas.scale;
                height = height / canvas.scale;

                const posX = e.clientX - canvasLayoutDom.offsetLeft - parseFloat(canvasContentDom.style.left),
                    posY = defaultY + offsetY;
                const strokeAlpha = element?.album_type == '132'? 1: 0;
                asset = {
                    ...asset,
                    meta: {
                        ...asset.meta,
                        type: 'SVG',
                    },
                    attribute: {
                        ...asset.attribute,
                        resId: element.id,
                        picUrl: element.image_url ? element.image_url : 'loading',
                        width: Math.round(width),
                        height: Math.round(height),
                        assetHeight: element.source_height,
                        assetWidth: element.source_width,
                        cropXTo: 1,
                        cropYTo: 1,
                        source_key: element.source_key,
                        stroke: {
                            color: { r: 0, g: 0, b: 0, a: strokeAlpha },
                            width: 10,
                            style: 'solid',
                        }
                    },
                    transform: {
                        ...asset.transform,
                        posX: posX / canvas.scale - Math.round(width) / 2,
                        posY: posY / canvas.scale - Math.round(height) / 2,
                    },
                };


                if (e.clientY - this.dragClientY > 20 || e.clientX - this.dragClientX > 20) {
                    const paintOnCanvasState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                        store_name: storeAdapter.store_names.paintOnCanvas,
                    }) as IPaintOnCanvasState;
                    fitPageDomSIze(paintOnCanvasState, asset);
                    const { work, pageInfo, pageAttr, canvas, canvas_dom_info } = paintOnCanvasState;
                    const pageType = pageAttr.pageInfo?.[pageInfo.pageNow].type || '';
                    if (pageType === 'board') {
                        const { width, height } = asset.attribute;
                        asset.attribute.width = 560;
                        asset.attribute.height = Math.round(560 / width * height);
                        asset.transform.posX = Math.floor(posX / canvas.scale - asset.attribute.width / 4);
                        asset.transform.posY = Math.floor(posY / canvas.scale - asset.attribute.height / 4);
                    } else {
                        asset.transform.posX = Math.floor(posX / canvas.scale - asset.attribute.width / 4);
                        asset.transform.posY = Math.floor(posY / canvas.scale - asset.attribute.height / 4);
                        asset.attribute.width = Math.floor(asset.attribute.width / 2);
                        asset.attribute.height = Math.floor(asset.attribute.height / 2);
                    }

                    const index = work.pages[pi].assets.length;
                    this.addAssets('SVG_DRAG_ADD', [{asset, pageIndex: pi}], undefined, [
                        {
                            type: 'selectAsset',
                            params: {
                                asset_index: index,
                                page_num: pi,
                                select_type: ESelectType.asset,
                            },
                        },
                    ]);
                }
                this.updateFav(element.id, 5);
            } else if (type === 'frame') {
                const asset: IAsset = cloneDeep(assetTemplate.frame)
                let  width,height;
                width = extend.width * 2 >= 300 ? 300 : extend.width * 2;
                ({ width, height } = this.fitWidthAndHeight(width, { width: element.width, height: element.height }));
                width = width / canvas.scale;
                height = height / canvas.scale;

                const posX = e.clientX - canvasLayoutDom.offsetLeft - parseFloat(canvasContentDom.style.left),
                    posY = defaultY + offsetY;
                    asset.meta.type = 'frame';
                    asset.attribute = {
                        ...asset.attribute,
                        resId: element.id,
                        picUrl: '',
                        width: Math.round(width),
                        height: Math.round(height),
                        assetHeight: element.source_height,
                        assetWidth: element.source_width,
                        cropXTo: 1,
                        cropYTo: 1,
                        source_key: element.source_key,
                        SVGUrl: element.sample_svg,
                        sample: element.sample,
                        // clipPath:element.clipPath,
                        frameBackgroundInfo:{
                            ...asset.attribute.frameBackgroundInfo,
                            ...element.frameBackgroundInfo,
                            picUrl: ''
                        },
                    };
                    asset.transform = {
                        ...asset.transform,
                        posX: posX / canvas.scale - Math.round(width) / 2,
                        posY: posY / canvas.scale - Math.round(height) / 2,
                    }
                if (e.clientY - this.dragClientY > 20 || e.clientX - this.dragClientX > 20) {
                    const index = work.pages[pi].assets.length;
                    this.addAssets('FRAME_DRAG_ADD', [{asset, pageIndex: pi}], undefined, [
                        {
                            type: 'selectAsset',
                            params: {
                                asset_index: index,
                                page_num: pi,
                                select_type: ESelectType.asset,
                            },
                        },
                    ]);
                }
                this.updateFav(element.id, 16);
            } else if (type === 'group') {
                const assets: IAsset[] = await this.getGroupWordInfo(element as IGroupWordListItemInfo) as IAsset[]
                const groupAsset = assets.find(asset => asset.meta.type === 'group')
                const width = groupAsset.attribute.width / 2 * canvas.scale;
                const height = groupAsset.attribute.height / 2 * canvas.scale;
                const posX = e.clientX - canvasLayoutDom.offsetLeft - parseFloat(canvasContentDom.style.left) - width,
                    posY = defaultY + offsetY - height;
                    console.log(defaultY, offsetY, height)
                assets.forEach((asset) => {
                    if (asset.meta.type !== 'group') {
                        const disX = asset.transform.posX - groupAsset.transform.posX;
                        const disY = asset.transform.posY - groupAsset.transform.posY;
                        asset.transform = {
                            ...asset.transform,
                            posX: posX / canvas.scale + disX,
                            posY: posY  / canvas.scale+ disY,
                        }
                    }
                })
                groupAsset.transform = {
                        ...groupAsset.transform,
                        posX: posX / canvas.scale,
                        posY: posY / canvas.scale,
                    }
                if (e.clientY - this.dragClientY > 20 || e.clientX - this.dragClientX > 20) {
                    const index = work.pages[pi].assets.length + assets.length;
                    this.addAssets('GROUP_DRAG_ADD', assets, undefined, [
                        {
                            type: 'selectAsset',
                            params: {
                                asset_index: index,
                                page_num: pageInfo.pageNow,
                                select_type: ESelectType.asset,
                            },
                        }
                    ]);
                }
            } else if(type === 'line') {
                const asset: IAsset = cloneDeep(assetTemplate.image);
                const { width, height } = this.fitWidthAndHeight(element.width, {
                    width: element.width,
                    height: 5,
                });
                const posX = e.clientX - canvasLayoutDom.offsetLeft - parseFloat(canvasContentDom.style.left),
                posY = defaultY + offsetY;
                let startArrow = 'line';
                let endArrow = 'line';
                if (element.id == 2 || element.id == 3) {
                    endArrow = 'arrow';
                }
                if (element.id == 1 || element.id == 3) {
                    startArrow = 'arrow';
                }
                const lineWidth = 10;
                asset.meta = {
                    ...asset.meta,
                    type: 'line',
                    uniqueId: generateShortUUID(),
                    ...(element.meta || {}),
                }
                asset.transform = {
                    ...asset.transform,
                    posX: posX / canvas.scale - Math.round(width) / 2,
                    posY: posY / canvas.scale - Math.round(height) / 2,
                }

                asset.attribute = {
                    ...asset.attribute,
                    resId: element.id,
                    width: Math.round(width),
                    height: Math.round(height),
                    cropXTo: 1,
                    cropYTo: 1,
                    color: { r: 0, g: 0, b: 0, a: 1 },
                    rt_isNowAdd: element.rt_isNowAdd || true,
                    lineWidth,
                    lineType: 'solid',
                    type: 'poly',
                    startArrow,
                    endArrow,
                    ...(element.attribute || {}),
                };

                const paintOnCanvasState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                }) as IPaintOnCanvasState;
                if (!element.fromCanvas) {
                    asset.attribute.start = {
                        x: 0,
                        y: 0,
                    }
                    asset.attribute.end = {
                        x: asset.attribute.width,
                        y: height - lineWidth,
                    }
                }

                const { work, pageInfo } = paintOnCanvasState;
                const index = work.pages[pageInfo.pageNow].assets.length;

                if (e.clientY - this.dragClientY > 20 || e.clientX - this.dragClientX > 20) {
                    const index = work.pages[pi].assets.length;
                    this.addAssets('LINE_DRAG_ADD', [{asset, pageIndex: pi}], undefined, [
                        {
                            type: 'selectAsset',
                            params: {
                                asset_index: index,
                                page_num: pi,
                                select_type: ESelectType.asset,
                            },
                        },
                    ]);
                }
            }
        });
    }

    /**
     * 拖动添加元素（移动事件）
     **/
    dragAddMoveEvent(
        element: {
            [key: string]: any;
            [key: number]: any;
        },
        type: string,
        extend: {
            [key: string]: any;
        },
        e: MouseEvent,
    ) {
        const width = extend.width * 2 >= 300 ? 300 : extend.width * 2;
        let height = (width * Math.round(element.height)) / Math.round(element.width);
        const dragAreaDom = document.getElementById('dragArea');
        const imgUrl = element.image_url ? element.image_url : element.sample;

        if (Math.abs(this.dragClientX - e.clientX) <= 5 && Math.abs(this.dragClientY - e.clientY) <= 5) {
            dragAreaDom.style.display = 'none';
            return false;
        }

        dragAreaDom.style.display = 'inline-block';

        if (type === 'image' || type === 'background' || type === 'pic' || type === 'container' || type === 'SVG' || type === 'frame' || type === 'group' || type === 'line') {
            const str =
                '<img style="width:' + width + 'px; height:' + height + 'px" src="' + imgUrl + '" draggable="false" />';

            dragAreaDom.innerHTML = str;
            dragAreaDom.style.position = 'absolute';
            dragAreaDom.style.left = e.clientX - width / 2 + 'px';
            dragAreaDom.style.top = e.clientY - height / 2 + 'px';
        } else if (type === 'text') {
            const { canvas } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });

            let scale = canvas.scale;

            if (scale >= 0.8) {
                scale = 0.8;
            }
            if (scale <= 0.3) {
                scale = 0.3;
            }

            const str =
                '<p style="font-size:' +
                element.attribute.fontSize * scale +
                'px;font-weight: ' +
                element.attribute.fontWeight * scale +
                ';text-align:' +
                element.attribute.textAlign +
                ';width:' +
                element.attribute.width * scale +
                'px;">' +
                element.attribute.text.join('') +
                '</p>';

            height = extend.height;

            dragAreaDom.innerHTML = str;
            dragAreaDom.style.position = 'absolute';
            dragAreaDom.style.left = e.clientX - (parseInt(element.attribute.width) * scale) / 2 + 'px';
            dragAreaDom.style.top = e.clientY - (height * scale) / 2 + 'px';
        }
        if (type === 'image'){
            emitter.emit('dragPicAsset',{element,e})
        }
        if (type === 'pic') {
            emitter.emit('dragPicAsset',{element,e})
            // TODO UpdateAssetTypeContainer 合并成一次 store 后, 适配变动
            UpdateAssetTypeContainer.updateContainerAddedAsset({ containerAddedAsset: '' });

            const canvasLayoutDom: HTMLElement = document.getElementsByClassName('canvasLayout')[0] as HTMLElement,
                infoLayoutDom: HTMLElement = document.getElementsByClassName('infoLayout')[0] as HTMLElement,
                canvasContentDom: HTMLElement = document.getElementsByClassName('canvasContent')[0] as HTMLElement;
            const mouseCClientX = e.clientX - (canvasLayoutDom.offsetLeft + canvasContentDom.offsetLeft),
                mouseCClientY = e.clientY - (infoLayoutDom.offsetHeight + canvasContentDom.offsetTop);
            let mouseAClientX,
                mouseAClientY,
                assetPosX,
                assetPosY,
                assetWidth,
                assetHeight,
                assetCCenterX,
                assetCCenterY;
            let oriPosMatrices, transformMatrices, tempRadian, tempClient;
            // math.multiply(oriPosMatrices, transformMatrices)
            const { canvas, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            let tempItem;
            let tempIndex = -1;
            for (let i = 0; i < work.pages[pageInfo.pageNow].assets.length; i++) {
                tempItem = work.pages[pageInfo.pageNow].assets[i];
                if (tempItem.meta.type === 'container') {
                    if (tempIndex > tempItem.meta.index) {
                        continue;
                    }

                    assetWidth = tempItem.attribute.width * canvas.scale;
                    assetHeight = tempItem.attribute.height * canvas.scale;
                    assetCCenterX = tempItem.transform.posX * canvas.scale + assetWidth / 2;
                    assetCCenterY = tempItem.transform.posY * canvas.scale + assetHeight / 2;
                    assetPosX = -assetWidth / 2;
                    assetPosY = assetHeight / 2;

                    tempRadian = (-tempItem.transform.rotate * Math.PI) / 180;
                    transformMatrices = [
                        [Math.cos(tempRadian), -Math.sin(tempRadian)],
                        [Math.sin(tempRadian), Math.cos(tempRadian)],
                    ];
                    oriPosMatrices = [[mouseCClientX - assetCCenterX, assetCCenterY - mouseCClientY]];

                    tempClient = math.multiply(oriPosMatrices, transformMatrices);
                    mouseAClientX = tempClient[0][0];
                    mouseAClientY = tempClient[0][1];

                    if (
                        mouseAClientX > assetPosX &&
                        mouseAClientX < assetPosX + assetWidth &&
                        mouseAClientY < assetPosY &&
                        mouseAClientY > assetPosY - assetHeight
                    ) {
                        let tempWidth = tempItem.attribute.width,
                            tempHeight = (tempWidth * element.source_height) / element.source_width;
                        if (tempHeight < tempItem.attribute.height) {
                            tempHeight = tempItem.attribute.height;
                            tempWidth = (tempHeight * element.source_width) / element.source_height;
                        }
                        tempIndex = tempItem.transform.index;
                        // TODO UpdateAssetTypeContainer 合并成一次 store 后, 适配变动
                        UpdateAssetTypeContainer.updateContainerAddedAsset({
                            containerAddedAsset: {
                                index: i,
                                info: {
                                    resId: element.id,
                                    imageUrl: element.sample,
                                    width:
                                        tempWidth /
                                        (tempItem.attribute.width / tempItem.attribute.contentInfo[0].viewBoxWidth),
                                    height:
                                        tempHeight /
                                        (tempItem.attribute.height / tempItem.attribute.contentInfo[0].viewBoxHeight),
                                    posX:
                                        -(tempWidth - tempItem.attribute.width) /
                                        2 /
                                        (tempItem.attribute.width / tempItem.attribute.contentInfo[0].viewBoxWidth),
                                    posY:
                                        -(tempHeight - tempItem.attribute.height) /
                                        2 /
                                        (tempItem.attribute.height / tempItem.attribute.contentInfo[0].viewBoxHeight),
                                },
                            },
                        });
                    }
                }
            }
        }
    }

    /**
     * 点击添加人物图（全局事件）
     */
    addPersonEmitter() {
        if (this.emitter_listener.person_emitter) {
            this.emitter_listener.person_emitter.remove();
        }
        this.emitter_listener.person_emitter = emitter.addListener(
            'ListAddPerson',
            (
                element: {
                    [key: string]: any;
                    [key: number]: any;
                },
                autoSaveFlag: number,
            ) => {
                let asset: IAsset = cloneDeep(assetTemplate.image);
                const { width, height } = this.fitWidthAndHeight(element.width, {
                    width: element.width,
                    height: element.height,
                });

                asset = {
                    ...asset,
                    meta: {
                        ...asset.meta,
                        type: 'pic',
                        addOrigin: element.addOrigin,
                        rt_searchWord: element.rt_searchWord,
                        rt_isNowAdd: element.rt_isNowAdd,
                        rt_tagId: element.rt_tagId,
                    },
                    attribute: {
                        ...asset.attribute,
                        resId: element.id,
                        picUrl: element.image_url ? element.image_url : 'loading',
                        width: Math.round(width),
                        height: Math.round(height),
                        assetHeight: element.source_height,
                        assetWidth: element.source_width,
                        cropXTo: 1,
                        cropYTo: 1,
                        filters: {
                            brightness: 0,
                            saturate: 0,
                            contrast: 0,
                            blur: 0,
                            sharpen: 0,
                            hue: 0,
                        },
                    },
                };

                const paintOnCanvasState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                }) as IPaintOnCanvasState;
                fitPageDomSIze(paintOnCanvasState, asset);

                const { work, pageInfo } = paintOnCanvasState;
                const index = work.pages[pageInfo.pageNow].assets.length;
                this.addAssets('ADD_PERSON', [asset], autoSaveFlag, [
                    {
                        type: 'selectAsset',
                        params: {
                            asset_index: index,
                            page_num: pageInfo.pageNow,
                            select_type: ESelectType.asset,
                        },
                    },
                ]);

                if (!element.image_url) {
                    const { work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                        store_name: storeAdapter.store_names.paintOnCanvas,
                    });
                    const className = work.pages[pageInfo.pageNow].assets[index].meta.className;
                    this.updatePicUrl(element.source_key, index, className, autoSaveFlag, 'ADD_PERSON_END');
                }
                this.updateFav(element.id, 7);
            },
        );
    }

    /**
     * 点击添加摄影（全局事件）
     */
    addPhotographyEmitter() {
        if (this.emitter_listener.photography_emitter) {
            this.emitter_listener.photography_emitter.remove();
        }
        this.emitter_listener.photography_emitter = emitter.addListener(
            'ListAddPhotography',
            (
                element: {
                    [key: string]: any;
                    [key: number]: any;
                },
                autoSaveFlag: number,
            ) => {
                let asset: IAsset = cloneDeep(assetTemplate.image);
                const { width, height } = this.fitWidthAndHeight(element.width, {
                    width: element.width,
                    height: element.height,
                });

                asset = {
                    ...asset,
                    meta: {
                        ...asset.meta,
                        type: 'pic',
                        addOrigin: element.addOrigin,
                        rt_searchWord: element.rt_searchWord,
                        rt_isNowAdd: element.rt_isNowAdd,
                        rt_tagId: element.rt_tagId,
                    },
                    attribute: {
                        owner: element.owner,
                        resId: element.id,
                        picUrl: element.image_url ? element.image_url : 'loading',
                        width: Math.round(width),
                        height: Math.round(height),
                        assetHeight: element.source_height,
                        assetWidth: element.source_width,
                        cropXTo: 1,
                        cropYTo: 1,
                        filters: {
                            brightness: 0,
                            saturate: 0,
                            contrast: 0,
                            blur: 0,
                            sharpen: 0,
                            hue: 0,
                        },
                    },
                };

                const paintOnCanvasState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                }) as IPaintOnCanvasState;
                fitPageDomSIze(paintOnCanvasState, asset);

                const { work, pageInfo } = paintOnCanvasState;
                const index = work.pages[pageInfo.pageNow].assets.length;
                this.addAssets('ADD_PHOTOGRAPHY', [asset], autoSaveFlag, [
                    {
                        type: 'selectAsset',
                        params: {
                            asset_index: index,
                            page_num: pageInfo.pageNow,
                            select_type: ESelectType.asset,
                        },
                    },
                ]);

                if (!element.image_url) {
                    const { work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                        store_name: storeAdapter.store_names.paintOnCanvas,
                    });
                    const className = work.pages[pageInfo.pageNow].assets[index].meta.className;
                    this.updatePicUrl(element.source_key, index, className, autoSaveFlag, 'ADD_PHOTOGRAPHY_END');
                }
                this.updateFav(element.id, 6);
            },
        );
    }

    /**
     * 点击添加艺术字（全局事件）
     */
    addEmojiEmitter() {
        if (this.emitter_listener.emoji_emitter) {
            this.emitter_listener.emoji_emitter.remove();
        }
        this.emitter_listener.emoji_emitter = emitter.addListener(
            'ListAddEmoji',
            (
                element: {
                    [key: string]: any;
                    [key: number]: any;
                },
                autoSaveFlag: number,
            ) => {
                let asset: IAsset = cloneDeep(assetTemplate.image);
                const { width, height } = this.fitWidthAndHeight(element.width, {
                    width: element.width,
                    height: element.height,
                });

                asset = {
                    ...asset,
                    meta: {
                        ...asset.meta,
                        type: 'image',
                        addOrigin: element.addOrigin,
                        rt_searchWord: element.rt_searchWord,
                        rt_isNowAdd: element.rt_isNowAdd,
                    },
                    attribute: {
                        ...asset.attribute,
                        resId: element.id,
                        picUrl: element.image_url ? element.image_url : 'loading',
                        width: Math.round(width),
                        height: Math.round(height),
                        assetHeight: element.source_height,
                        assetWidth: element.source_width,
                        cropXTo: 1,
                        cropYTo: 1,
                        viewBox: {},
                        filters: {
                            brightness: 0,
                            saturate: 0,
                            contrast: 0,
                            blur: 0,
                            sharpen: 0,
                            hue: 0,
                        },
                    },
                };

                const paintOnCanvasState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                }) as IPaintOnCanvasState;
                fitPageDomSIze(paintOnCanvasState, asset);

                const { work, pageInfo } = paintOnCanvasState;
                const index = work.pages[pageInfo.pageNow].assets.length;
                this.addAssets('ADD_EMOJI', [asset], autoSaveFlag, [
                    {
                        type: 'selectAsset',
                        params: {
                            asset_index: index,
                            page_num: pageInfo.pageNow,
                            select_type: ESelectType.asset,
                        },
                    },
                ]);

                if (!element.image_url) {
                    const { work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                        store_name: storeAdapter.store_names.paintOnCanvas,
                    });
                    const className = work.pages[pageInfo.pageNow].assets[index].meta.className;
                    this.updatePicUrl(element.source_key, index, className, autoSaveFlag, 'ADD_EMOJI_END');
                }
                this.updateFav(element.id, 9);
            },
        );
    }

    /**
     * 点击添加艺术字（全局事件）
     */
    addWordArtEmitter() {
        if (this.emitter_listener.wordArt_emitter) {
            this.emitter_listener.wordArt_emitter.remove();
        }
        this.emitter_listener.wordArt_emitter = emitter.addListener(
            'ListAddWordArt',
            (
                element: {
                    [key: string]: any;
                    [key: number]: any;
                },
                autoSaveFlag: number,
            ) => {
                let asset: IAsset = cloneDeep(assetTemplate.image);
                const { width, height } = this.fitWidthAndHeight(element.width, {
                    width: element.width,
                    height: element.height,
                });

                asset = {
                    ...asset,
                    meta: {
                        ...asset.meta,
                        type: 'image',
                        addOrigin: element.addOrigin,
                        rt_searchWord: element.rt_searchWord,
                        rt_isNowAdd: element.rt_isNowAdd,
                    },
                    attribute: {
                        ...asset.attribute,
                        resId: element.id,
                        picUrl: element.image_url ? element.image_url : 'loading',
                        width: Math.round(width),
                        height: Math.round(height),
                        assetHeight: element.source_height,
                        assetWidth: element.source_width,
                        cropXTo: 1,
                        cropYTo: 1,
                        viewBox: {},
                        filters: {
                            brightness: 0,
                            saturate: 0,
                            contrast: 0,
                            blur: 0,
                            sharpen: 0,
                            hue: 0,
                        },
                    },
                };

                const paintOnCanvasState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                }) as IPaintOnCanvasState;
                fitPageDomSIze(paintOnCanvasState, asset);

                const { work, pageInfo } = paintOnCanvasState;
                const index = work.pages[pageInfo.pageNow].assets.length;
                this.addAssets('ADD_WORDART', [asset], autoSaveFlag, [
                    {
                        type: 'selectAsset',
                        params: {
                            asset_index: index,
                            page_num: pageInfo.pageNow,
                            select_type: ESelectType.asset,
                        },
                    },
                ]);

                if (!element.image_url) {
                    const { work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                        store_name: storeAdapter.store_names.paintOnCanvas,
                    });
                    const className = work.pages[pageInfo.pageNow].assets[index].meta.className;
                    this.updatePicUrl(element.source_key, index, className, autoSaveFlag, 'ADD_WORDART_END');
                }
                this.updateFav(element.id, 8);
            },
        );
    }

    /**
     * 点击添加背景（全局事件）
     */
    addBackgroundEmitter() {
        if (this.emitter_listener.background_emitter) {
            this.emitter_listener.background_emitter.remove();
        }
        this.emitter_listener.background_emitter = emitter.addListener(
            'ListAddBackground',
            (
                background: {
                    [key: string]: any;
                    [key: number]: any;
                },
                autoSaveFlag: number,
                props:{
                    [key: string]: any
                } = {}
            ) => {
                const {unrecordable = false} = props;
                let asset: IAsset = cloneDeep(assetTemplate.background);
                const { canvas } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                });
                let width;
                let height;

                width = canvas.width;
                height = (width * background.height) / background.width;

                if (height < canvas.height) {
                    height = canvas.height;
                    width = (height * background.width) / background.height;
                }
                asset = {
                    ...asset,
                    meta: {
                        ...asset.meta,
                        type: 'background',
                        rt_searchWord: background.rt_searchWord,
                        rt_isNowAdd: background.rt_isNowAdd,
                        rt_ratioId: background.rt_ratioId,
                        replaceForEffectTemplate:background.replaceForEffectTemplate,
                    },
                    attribute: {
                        ...asset.attribute,
                        resId: background.id,
                        picUrl: background.image_url ? background.image_url : 'loading',
                        width: Math.round(width),
                        height: Math.round(height),
                        assetHeight: background.source_height,
                        assetWidth: background.source_width,
                        cropXTo: 1,
                        cropYTo: 1,
                    },
                    transform: {
                        ...asset.transform,
                        posX: (canvas.width - width) / 2,
                        posY: (canvas.height - height) / 2,
                    },
                    // subRecord: { // 已经不统计了
                    //     origin: props.origin,
                    // },
                };

                /* 单一背景 */
                let { work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                });
                const page = work.pages[pageInfo.pageNow];
                const len = page.assets.length;
                let index = -1;
                let cssIndex = 0;
                let className: string;
                for (let i = len - 1; i >= 0; i--) {
                    if (page.assets[i].meta.type === 'background') {
                        if (page.assets[i].meta.index >= cssIndex) {
                            // 更新最上层的背景元素, 复制时会复制出多个背景, 所以只改最上面的一个
                            cssIndex = page.assets[i].meta.index;
                            index = i;
                            className = page.assets[i].meta.className;
                        }
                    }
                }
                const tempProps = IPSConfig.getProps();
                if (index === -1) {
                    index = work.pages[pageInfo.pageNow].assets.length;
                    this.addAssets(
                        'ADD_BACKGROUND',
                        [asset],
                        autoSaveFlag,
                        // !tempProps.isDesigner
                        //     ? [
                        //           {
                        //               type: 'selectAsset',
                        //               params: {
                        //                   asset_index: index,
                        //                   page_num: pageInfo.pageNow,
                        //                   select_type: ESelectType.asset,
                        //               },
                        //           },
                        //       ]
                        //     : [],
                        [],
                        unrecordable
                    );
                    ({ work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                        store_name: storeAdapter.store_names.paintOnCanvas,
                    }));
                    className = work.pages[pageInfo.pageNow].assets[index].meta.className;
                } else {
                    asset.meta.index = cssIndex;

                    if (work.pages[pageInfo.pageNow].assets[index].attribute?.container?.id) {
                        const info = {
                            attribute: {
                                resId: background.id,
                                picUrl: background.sample ? background.sample : background.image_url,
                                width: background.width,
                                height: background.height,
                                assetWidth: background.width,
                                assetHeight: background.height,
                                rt_picUrl: background.sample ? background.sample : background.image_url, // path 是原图尺寸
                            },
                        };
                        Object.assign(info.attribute, {
                            assetWidth: info.attribute.width,
                            assetHeight: info.attribute.height,
                        });
                        if (info.attribute.width > info.attribute.height) {
                            info.attribute.width = asset.attribute.width;
                            info.attribute.height =
                                (info.attribute.width * info.attribute.assetHeight) / info.attribute.assetWidth;
                        } else {
                            info.attribute.height = asset.attribute.height;
                            info.attribute.width =
                                (info.attribute.height * info.attribute.assetWidth) / info.attribute.assetHeight;
                        }
                        CanvasPaintedLogic.updateAssetInfo({
                            index: index,
                            info,
                            unrecordable,
                        });
                    } else {
                        UpdateAsset.updateAssets(
                            'UPDATE_BACKGROUND',
                            [
                                {
                                    index,
                                    className,
                                    changes: {
                                        ...asset,
                                    },
                                },
                            ],
                            autoSaveFlag,
                            // !tempProps.isDesigner
                            //     ? [
                            //           {
                            //               type: 'selectAsset',
                            //               params: {
                            //                   asset_index: index,
                            //                   page_num: pageInfo.pageNow,
                            //                   select_type: ESelectType.asset,
                            //               },
                            //           },
                            //       ]
                            //     : [],
                            undefined,
                            unrecordable
                        );
                    }

                    // UpdateAsset.updateAssets(
                    //     'ADD_BACKGROUND',
                    //     [
                    //         {
                    //             index,
                    //             className,
                    //             changes: {
                    //                 ...asset,
                    //             },
                    //         },
                    //     ],
                    //     autoSaveFlag,
                    //     !tempProps.isDesigner
                    //         ? [
                    //               {
                    //                   type: 'selectAsset',
                    //                   params: {
                    //                       asset_index: index,
                    //                       page_num: pageInfo.pageNow,
                    //                       select_type: ESelectType.asset,
                    //                   },
                    //               },
                    //           ]
                    //         : [],
                    // );
                }
                /* 单一背景 */
                if (!background.image_url) {
                    this.updatePicUrl(background.source_key, index, className, autoSaveFlag, 'ADD_BACKGROUND_END');
                }
                this.updateFav(background.id, 2);
            },
        );
    }

    /**
     * 点击添加SVG（全局事件）
     */
    addSVGEmitter() {
        if (this.emitter_listener.svg_emitter) {
            this.emitter_listener.svg_emitter.remove();
        }
        this.emitter_listener.svg_emitter = emitter.addListener(
            'ListAddSVG',
            (
                element: {
                    [key: string]: any;
                    [key: number]: any;
                },
                autoSaveFlag: number,
            ) => {
                const asset: IAsset = cloneDeep(assetTemplate.SVG);
                const { width, height } = this.fitWidthAndHeight(element.width, {
                    width: element.width,
                    height: element.height,
                });
                const strokeAlpha = element?.album_type == '132'? 1: 0;

                asset.meta.type = 'SVG';
                asset.attribute = {
                    ...asset.attribute,
                    resId: element.id,
                    picUrl: element.image_url ? element.image_url : 'loading',
                    width: Math.round(width),
                    height: Math.round(height),
                    assetHeight: element.source_height,
                    assetWidth: element.source_width,
                    cropXTo: 1,
                    cropYTo: 1,
                    source_key: element.source_key,
                    rt_isNowAdd: element.rt_isNowAdd || true,
                    SVGUrl: element.SVGUrl,
                    stroke: {
                        color: { r: 0, g: 0, b: 0, a: strokeAlpha },
                        width: 10,
                        style: 'solid',
                    }
                };

                const paintOnCanvasState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                }) as IPaintOnCanvasState;
                fitPageDomSIze(paintOnCanvasState, asset);
                const { work, pageInfo, pageAttr, canvas, canvas_dom_info } = paintOnCanvasState;
                const pageType = pageAttr.pageInfo?.[pageInfo.pageNow].type || '';
                if (pageType === 'board') {
                    const { width, height } = asset.attribute;
                    asset.attribute.width = 560;
                    asset.attribute.height = Math.round(560 / width * height);
                    asset.transform.posX -= (asset.attribute.width - width) / 2;
                    asset.transform.posY -= (asset.attribute.height - height) / 2;
                    // 视口居中写不对，放弃了
                    // const dom = document.getElementsByClassName(canvas_dom_info.class_name)[0] as HTMLElement;
                    // const canvasWidth = dom.offsetWidth ;
                    // const canvasHeight = dom.offsetHeight;
                    // const centerLogicalX = (canvasWidth / 2 - canvas.x) / canvas.scale;
                    // const centerLogicalY = (canvasHeight / 2 - canvas.y) / canvas.scale;
                    // asset.transform.posX += centerLogicalX + (canvas.width / 2);
                    // asset.transform.posY += centerLogicalY + (canvas.height / 2);
                    // console.log(centerLogicalX, centerLogicalY, asset.transform.posX, asset.transform.posY);
                } else {
                    asset.transform.posX += Math.floor(asset.attribute.width / 4);
                    asset.transform.posY += Math.floor(asset.attribute.height / 4);
                    asset.attribute.width = Math.floor(asset.attribute.width / 2);
                    asset.attribute.height = Math.floor(asset.attribute.height / 2);
                }
                // addSvgTextAttr(asset);
                const index = work.pages[pageInfo.pageNow].assets.length;
                this.addAssets('ADD_SVG', [asset], autoSaveFlag, [
                    {
                        type: 'selectAsset',
                        params: {
                            asset_index: index,
                            page_num: pageInfo.pageNow,
                            select_type: ESelectType.asset,
                        },
                    },
                ]);

                // if (!element.image_url) {
                //     const className = work.pages[pageInfo.pageNow].assets[index].meta.className;
                //     this.updatePicUrl(element.source_key, index, className, autoSaveFlag);
                // }

                // if( element.image_url ){
                //     Object.assign(asset.attribute, {
                //         picUrl: element.image_url,
                //     });
                //     canvasStore.dispatch(paintOnCanvas('ADD_SVG', Object.assign(asset, {autoSaveFlag: autoSaveFlag})));
                // }else{
                //     canvasStore.dispatch(paintOnCanvas('ADD_SVG', Object.assign(asset, {autoSaveFlag: autoSaveFlag})));
                // }

                this.updateFav(element.id, 5);
            },
        );
    }



    /**
     * 点击添加图片元素（全局事件）
     */
    addElementEmitter() {
        if (this.emitter_listener.element_emitter) {
            this.emitter_listener.element_emitter.remove();
        }
        this.emitter_listener.element_emitter = emitter.addListener(
            'ListAddElement',
            (
                element: {
                    [key: string]: any;
                    [key: number]: any;
                },
                autoSaveFlag: number,
                storeActions: TStoreAction[] = [],
                deleteCount = 0, // 指添加元素的目标页中的删除元素的个数
                // props: { // 已经不统计了
                //     [key: string]: any
                // } = {}
            ) => {
                let asset: IAsset = cloneDeep(assetTemplate.image);
                let width=element.width,height = element.height
                if(!element.noNeedFitSize){
                    const { width:assetWidth, height:assetHeight } = this.fitWidthAndHeight(element.width, {
                        width: element.width,
                        height: element.height,
                    });
                    width = assetWidth
                    height = assetHeight
                }

                asset = {
                    ...asset,
                    meta: {
                        ...asset.meta,
                        type: 'image',
                        addOrigin: element.addOrigin,
                        rt_searchWord: element.rt_searchWord,
                        rt_isNowAdd: element.rt_isNowAdd ?? true,
                        rt_tagId: element.rt_tagId,
                        replaceForEffectTemplate:element.replaceForEffectTemplate ?? ''
                    },
                    attribute: {
                        ...asset.attribute,
                        resId: element.id,
                        picUrl: element.image_url ? element.image_url : 'loading',
                        rt_picUrl: element.image_url ? element.image_url : '',
                        width: Math.round(width),
                        height: Math.round(height),
                        assetHeight: element.source_height,
                        assetWidth: element.source_width,
                        cropXTo: 1,
                        cropYTo: 1,
                        filters: {
                            brightness: 0,
                            saturate: 0,
                            contrast: 0,
                            blur: 0,
                            sharpen: 0,
                            hue: 0,
                        },
                        wordCloudInfo: element.wordCloudInfo,
                    },
                    // subRecord: { // 已经不统计了
                    //     origin: props.origin,
                    // },
                };
                const paintOnCanvasState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                }) as IPaintOnCanvasState;
                if(!element.noNeedFitSize){
                    fitPageDomSIze(paintOnCanvasState, asset);
                }
               if(element.transform){
                    asset.transform = {
                        ...asset.transform,
                        ...element.transform
                    }
               }
                const { work, pageInfo } = paintOnCanvasState;
                const index = work.pages[pageInfo.pageNow].assets.length - deleteCount;
                this.addAssets('ADD_ELEMENT', [asset], autoSaveFlag, [
                    ...storeActions,
                    {
                        type: 'selectAsset',
                        params: {
                            asset_index: index,
                            page_num: pageInfo.pageNow,
                            select_type: ESelectType.asset,
                        },
                    },
                ]);

                if (!element.image_url) {
                    const { work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                        store_name: storeAdapter.store_names.paintOnCanvas,
                    });
                    const className = work.pages[pageInfo.pageNow].assets[index].meta.className;
                    this.updatePicUrl(element.source_key, index, className, autoSaveFlag, 'ADD_ELEMENT_END');
                }
                this.updateFav(element.id, 1);
            },
        );
    }

    /**
     * 点击添加图表元素（全局事件）
     */
    addChartEmitter() {
        if (this.emitter_listener.chart_emitter) {
            this.emitter_listener.chart_emitter.remove();
        }

        this.emitter_listener.chart_emitter = emitter.addListener(
            'ListAddChart',
            (chart: IAnyObj, autoSaveFlag: number) => {
                const asset: IAsset = cloneDeep(assetTemplate.chart);
                const { width, height } = this.fitWidthAndHeight(
                    chart.width,
                    { width: chart.width, height: chart.height },
                    3,
                );
                asset.meta.type = 'chart';
                if (chart.meta) {
                    asset.meta = {
                        ...asset.meta,
                        ...chart.meta,
                    };
                }

                asset.attribute = {
                    ...asset.attribute,
                    resId: chart.id,
                    chartBaseId: chart.chartBaseId,
                    // rt_id: chart.id + Math.random(), // 旧的更新机制
                    width: Math.round(width),
                    height: Math.round(height),
                    assetHeight: chart.height,
                    assetWidth: chart.width,
                    rt_ready: true,
                };
                if (chart.attribute) {
                    asset.attribute = {
                        ...asset.attribute,
                        ...chart.attribute,
                    };
                }
                const { canvas } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                });
                asset.transform = {
                    ...asset.transform,
                    posX: (canvas.width - width) / 2,
                    posY: (canvas.height - height) / 2,
                };

                const paintOnCanvasState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                }) as IPaintOnCanvasState;
                const { info } = storeAdapter.getStore({
                    store_name: storeAdapter.store_names.InfoManage,
                })
                const setGgTransparentForPPt =  info.template_type=='3' && env.hidePptIncompatibleAsset
                // fitPageDomSIze(paintOnCanvasState, asset);
                if(setGgTransparentForPPt){
                    asset.attribute.chartBgcolor = { r:255, g:255, b:255, a:0}
                }
                let { work, pageInfo } = paintOnCanvasState;
                const index = work.pages[pageInfo.pageNow].assets.length;
                this.addAssets('ADD_CHART', [asset], autoSaveFlag, [
                    {
                        type: 'selectAsset',
                        params: {
                            asset_index: index,
                            page_num: pageInfo.pageNow,
                            select_type: ESelectType.asset,
                        },
                    },
                ]);
                ({ work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                }));
                const className = work.pages[pageInfo.pageNow].assets[index].meta.className;
                assetManager.getPPTChartDetail(chart.id).then((data) => {
                    data.json().then((resultData) => {
                        if (resultData.stat === 0) {
                            alert('服务器错误，请联系客服：1994432176');
                            return false;
                        }
                        if (resultData.data) {
                            const item = resultData.data;
                            UpdateAsset.updateAssets(
                                'ADD_CHART_END',
                                [
                                    {
                                        index,
                                        className,
                                        changes: {
                                            meta: (!Array.isArray(item.meta) && item.meta) || {},
                                            attribute: {
                                                chartBaseId: item.chartBaseId,
                                                rt_defaultData: item.rt_defaultData,
                                                userData: item.rt_defaultData,
                                                chartDrawInfo: item.chartDrawInfo,
                                                chartRule: item.chartRule,
                                                colorTable: item.colorTable,
                                                rt_ready: false,
                                            },
                                        },
                                    },
                                ],
                                autoSaveFlag,
                            );
                        }
                    });
                });
                this.updateFav(chart.id, 14);
            },
        );
    }
    // 点击添加图表元素新事件
    addNewChart(EChart: {
        width: number;
        height: number;
        rt_url: string;
        userData: any;
        chartBaseId: number;
        accumulation?: IAccumulationInAttribute;
    }) {
        let asset: IAsset = cloneDeep(assetTemplate.eChart);
        const { width, height } = this.fitWidthAndHeight(EChart.width, {
            width: EChart.width,
            height: EChart.height,
        });
        console.log(width, height);

        asset = {
            ...asset,
            meta: {
                ...asset.meta,
                type: 'chart',
                v: 2,
            },
            attribute: {
                ...asset.attribute,
                chartBaseId: EChart.chartBaseId,
                width,
                height,
                rt_url: EChart.rt_url,
                // option: EChart.option,
                userData: cloneDeep(EChart.userData),
                colorTable,
                accumulation: EChart.accumulation,
                chartBgcolor: { r: 255, g: 255, b: 255, a: 1 },
            },
        };

        const paintOnCanvasState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }) as IPaintOnCanvasState;

        fitPageDomSIze(paintOnCanvasState, asset);
        const { work, pageInfo } = paintOnCanvasState;
        const index = work.pages[pageInfo.pageNow].assets.length;
        this.addAssets('ADD_E_CHART', [asset], undefined, [
            {
                type: 'selectAsset',
                params: {
                    asset_index: index,
                    page_num: pageInfo.pageNow,
                    select_type: ESelectType.asset,
                },
            },
        ]);
    }

    /**
     * 点击添加表格元素（全局事件）
     */
    addTableEmitter() {
        if (this.emitter_listener.table_emitter) {
            this.emitter_listener.table_emitter.remove();
        }
        this.emitter_listener.table_emitter = emitter.addListener(
            'ListAddTable',
            (
                tableAsset: {
                    meta: { [key: string]: any };
                    attribute: { [key: string]: any };
                    transform: { [key: string]: any };
                    [key: string]: any;
                    [key: number]: any;
                },
                autoSaveFlag: number,
            ) => {
                let asset: IAsset = {
                    meta: cloneDeep(tableAsset.meta),
                    attribute: cloneDeep(tableAsset.attribute),
                    transform: {
                        posX: 0,
                        posY: 0,
                        rotate: 0,
                        horizontalFlip: false,
                        verticalFlip: false,
                        ...cloneDeep(tableAsset.transform),
                    },
                };
                const { canvas } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                });
                let width = Math.round(tableAsset.width);
                let height = Math.round(tableAsset.height);
                /*  width = (canvas.width / 2) < width ? canvas.width / 2 : width
            height = width * Math.round(tableAsset.height) / Math.round(tableAsset.width)
            if( height > canvas.height ){
                height = canvas.height / 2 < height ? canvas.height / 2: height
                width = height * Math.round(tableAsset.width) / Math.round(tableAsset.height)
            } */
                width = Math.round((canvas.width / 3) * 2);
                height = Math.round(tableAsset.height * (width / tableAsset.width));

                asset = {
                    meta: {
                        ...asset.meta,
                        type: 'table',
                    },
                    attribute: {
                        ...asset.attribute,
                        width: Math.round(width),
                        height: Math.round(height),
                    },
                    transform: {
                        ...asset.transform,
                        posX: (canvas.width - width) / 2,
                        posY: (canvas.height - height) / 2,
                    },
                };
                if (tableAsset.id) {
                    Object.assign(asset.attribute, {
                        resId: tableAsset.id,
                        rt_id: tableAsset.id + Math.random(),
                        rt_ready: true,
                    });
                }

                let { work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                });
                const index = work.pages[pageInfo.pageNow].assets.length;
                this.addAssets('ADD_TABLE', [asset], autoSaveFlag, [
                    {
                        type: 'selectAsset',
                        params: {
                            asset_index: index,
                            page_num: pageInfo.pageNow,
                            select_type: ESelectType.asset,
                        },
                    },
                ]);

                ({ work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                }));
                const className = work.pages[pageInfo.pageNow].assets[index].meta.className;

                if(tableAsset.id) {
                    assetManager.getPPTTableDetail(tableAsset.id).then((data) => {
                        data.json().then((resultData) => {
                            if (resultData.stat != 1) {
                                alert('服务器错误，请联系客服：1994432176');
                                return false;
                            }
                            const item = resultData.data;

                            // 启用本地配置
                            for (let i = 0; i < attributes.length; i++) {
                                if (attributes[i].id === Number(tableAsset.id)) {
                                    item.attribute = cloneDeep(attributes[i].attribute);
                                    break;
                                }
                            }

                            const tempAsset = {
                                attribute: {
                                    ...item.attribute,
                                    ...asset.attribute,
                                    resId: tableAsset.id,
                                    rt_ready: false,
                                },
                                transform: { ...item.transform, ...asset.transform },
                            };

                            const { info } = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
                                store_name: storeAdapter.store_names.InfoManage,
                            });

                            if (info.kid_1 && info.kid_1 === 16) {
                                for (let i = 0; i < tempAsset.attribute.text.length; i++) {
                                    for (let t = 0; t < tempAsset.attribute.text[i].length; t++) {
                                        tempAsset.attribute.text[i][t].fontFamily = 'fnsyhtRegular';
                                        tempAsset.attribute.text[i][t].fontSize = 80;
                                    }
                                }
                                // 以前留下的逻辑, 不知道有什么意义, 而且导致表格变很大, 所以注释了
                                // tempAsset.attribute.height = 1670;
                                // tempAsset.attribute.height = 3780;
                            }

                            UpdateAsset.updateAssets(
                                'ADD_TABLE_END',
                                [
                                    {
                                        index,
                                        className,
                                        changes: tempAsset,
                                    },
                                ],
                                autoSaveFlag,
                            );
                        });
                    });
                }else {

                }

                this.updateFav(tableAsset.id, 15);
            },
        );
    }

    /**
     * 点击添加pic（全局事件）
     */
    addPicEmitter() {
        if (this.emitter_listener.pic_emitter) {
            this.emitter_listener.pic_emitter.remove();
        }
        this.emitter_listener.pic_emitter = emitter.addListener(
            'ListAddPic',
            (
                element: {
                    [key: string]: any;
                    [key: number]: any;
                },
                autoSaveFlag: number,
            ) => {
                let asset: IAsset = cloneDeep(assetTemplate.image);
                const { width, height } = this.fitWidthAndHeight(element.width, {
                    width: element.width,
                    height: element.height,
                });

                asset = {
                    ...asset,
                    meta: {
                        ...asset.meta,
                        type: 'pic',
                        rt_searchWord: element.rt_searchWord,
                        rt_isNowAdd: element.rt_isNowAdd,
                        rt_tagId: element.rt_tagId,
                    },
                    attribute: {
                        ...asset.attribute,
                        resId: element.id,
                        picUrl: element.image_url ? element.image_url : 'loading',
                        width: Math.round(width),
                        height: Math.round(height),
                        assetHeight: element.source_height,
                        assetWidth: element.source_width,
                        cropXTo: 1,
                        cropYTo: 1,
                        filters: {
                            brightness: 0,
                            saturate: 0,
                            contrast: 0,
                            blur: 0,
                            sharpen: 0,
                            hue: 0,
                            'gamma-r': 1,
                            'gamma-b': 1,
                            'gamma-g': 1,
                        },
                        ...(element.extends?.attribute || {}),
                    },
                };

                const paintOnCanvasState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                }) as IPaintOnCanvasState;
                fitPageDomSIze(paintOnCanvasState, asset);

                const { work, pageInfo } = paintOnCanvasState;
                const index = work.pages[pageInfo.pageNow].assets.length;
                this.addAssets('ADD_PIC', [asset], autoSaveFlag, [
                    {
                        type: 'selectAsset',
                        params: {
                            asset_index: index,
                            page_num: pageInfo.pageNow,
                            select_type: ESelectType.asset,
                        },
                    },
                ]);

                if (!element.image_url) {
                    const { work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                        store_name: storeAdapter.store_names.paintOnCanvas,
                    });
                    const className = work.pages[pageInfo.pageNow].assets[index].meta.className;
                    this.updatePicUrl(element.source_key, index, className, autoSaveFlag, 'ADD_PIC_END');
                }
                this.updateFav(element.id, 4);
            },
        );
    }
    /**
     * 点击添加图片容器（全局事件）
     */
    addContainerEmitter() {
        if (this.emitter_listener.container_emitter) {
            this.emitter_listener.container_emitter.remove();
        }
        this.emitter_listener.container_emitter = emitter.addListener(
            'ListAddContainer',
            (
                element: {
                    [key: string]: any;
                    [key: number]: any;
                },
                autoSaveFlag: number,
            ) => {
                const asset: IAsset = cloneDeep(assetTemplate.image);
                const { width, height } = this.fitWidthAndHeight(element.width, {
                    width: element.width,
                    height: element.height,
                });

                asset.meta.type = 'container';
                asset.attribute = {
                    resId: element.id,
                    picUrl: element.image_url ? element.image_url : 'loading',
                    width: Math.round(width),
                    height: Math.round(height),
                    assetHeight: element.source_height,
                    assetWidth: element.source_width,
                    cropXTo: 1,
                    cropYTo: 1,
                    source_key: element.source_key,
                    contentInfo: [
                        {
                            resId: '',
                            imageUrl: '',
                            width: 0,
                            height: 0,
                            posX: 0,
                            posY: 0,
                            viewBoxWidth: 0,
                            viewBoxHeight: 0,
                        },
                    ],
                    filters: {
                        brightness: 0,
                        saturate: 0,
                        contrast: 0,
                        blur: 0,
                        sharpen: 0,
                        hue: 0,
                    },
                };

                const paintOnCanvasState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                }) as IPaintOnCanvasState;
                fitPageDomSIze(paintOnCanvasState, asset);

                const { work, pageInfo } = paintOnCanvasState;
                const index = work.pages[pageInfo.pageNow].assets.length;
                this.addAssets('ADD_CONTAINER', [asset], autoSaveFlag, [
                    {
                        type: 'selectAsset',
                        params: {
                            asset_index: index,
                            page_num: pageInfo.pageNow,
                            select_type: ESelectType.asset,
                        },
                    },
                ]);
                this.updateFav(element.id, 4);
            },
        );
    }

    /**
     * 添加文字、特效字、3D字
     */
    async addText(
        assetProps: {
            fontSize: number;
            fontWeight: 'bold' | 'normal';
            ranking: string;
            isSmallFontSize?: boolean;
        } = {
            fontSize: 24,
            fontWeight: 'normal',
            ranking: '0',
        },
        param: {
            id: string | number;
            fontFamily?: string;
            textType?: 'specificWord' | '3D';
            effectVariant3D?: IEffectVariant3D;
            addFrom?: string;
        } = { id: undefined },
        defaultText = '双击编辑文字',
        type?: string,
        tabId?: string | number, // 左侧菜单 navId 用于埋点
    ) {
        if (!defaultText) {
            defaultText = '双击编辑文字';
        }
        let asset: IAsset = cloneDeep(assetTemplate.text);
        const { canvas, isDesigner, user:{userId} } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const { recommendFont } = storeAdapter.getStore<typeof storeAdapter.store_names.RecommendManage>({
            store_name: storeAdapter.store_names.RecommendManage,
        });
        let recommendFontFlag = false;
        let width = 200;
        let height = 50;

        asset = {
            ...asset,
            meta: {
                ...asset.meta,
                type: 'text',
                isEdit: true,
                addOrigin: param.id ? 'specificWord' : 'text',
                addFrom: isDesigner ?'designer' : (param.addFrom || 'custom')
            },
            attribute: {
                ...asset.attribute,
                text: [defaultText],
                fontSize: assetProps.fontSize,
                fontWeight: assetProps.fontWeight,
                textAlign: 'center',
                width: width,
                opacity: 100,
                lineHeight: 13,
                letterSpacing: 0,
                fontFamily: param?.fontFamily ?? 'fnsyhtRegular',
                // color: { r: 74, g: 74, b: 74, a: 1 },
                color: {r: 0, g: 0, b: 0, a: 1},
                rt_fontFamily_important: param?.fontFamily,
            },
        };

        asset.attribute.rt_isNowAdd = true;
        if (param.id) {
            if (tabId) {
                asset.attribute.tabId = tabId;
            }
        } else {
            asset.attribute.writingMode = 'horizontal-tb';
        }

        try {
            if (recommendFont?.[assetProps.ranking]) {
                width = parseInt(recommendFont[assetProps.ranking]['font_size']) * 9;
                asset.attribute.width = width;
                asset.attribute.fontSize = parseInt(recommendFont[assetProps.ranking]['font_size']);
                recommendFontFlag = true;
            }
        } catch (error) {
            console.error('try recommendFont error: ', error);
        }
        if (!recommendFontFlag) {
            let tempFontSize: number;
            // const fontNum = (asset.attribute.text[0] as string).length;
            const fontNum = 6;
            if (assetProps.ranking === '2') {
                tempFontSize = (canvas.width * 1) / 3 / fontNum;
            } else if (assetProps.ranking === '1') {
                tempFontSize = (canvas.width * 1) / 2 / fontNum;
            } else {
                tempFontSize = (canvas.width * 2) / 3 / fontNum;
            }
            tempFontSize = Math.floor(tempFontSize);
            width = tempFontSize * 9;
            height = tempFontSize;

            asset.attribute.fontSize = tempFontSize;
            asset.attribute.width = width;
        }
        asset.transform = {
            ...asset.transform,
            posX: ((canvas.width - width) / 2) * canvas.scale,
            posY: ((canvas.height - height) / 2) * canvas.scale,
        };

        const textType = param['textType'] ? param['textType'] : 'specificWord';
        if (!param.id) {
            // 标题 正文
            if (assetProps.isSmallFontSize) {
                asset.attribute.rt_fontSize = 'small';
            }
            if (assetProps.ranking === '0' && !isDesigner) {
                if (assetProps.isSmallFontSize) {
                    asset.meta.isTitle = 1;
                }
            }
            const { info } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.InfoManage,
            });
            const font = fontsList.getAllFontsByArray()[asset.attribute.fontFamily]
         
            assetManager.setPv_new(2461, { additional: {
                i0: font?.id,
                s0: font?.is_zihun,
                s1: font?.s1 ? font.s1 : '',
                tid: info.last_templ_id,
                utid: info.id
            } });
        } else {
            // 特效字/3D字
            if (!isDesigner) {
                asset.meta.isTitle = 1;
            }
            // createTime = Math.floor(new Date().getTime() / 1000)
            const { createTime } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });

            if (textType === 'specificWord') {
                asset.attribute = {
                    ...asset.attribute,
                    effect: param.id + '@0',
                    effectVariant: {
                        state: 'add',
                    },
                };
                assetManager.setPv_new(163, {
                    additional: {
                        s0: 'specificWordId',
                        s1: param.id,
                        s2: createTime,
                    },
                });
            } else if (textType === '3D') {
                const id = Number(param.id);
                if ([30, 31].includes(id)) {
                    asset.attribute.text = ['TGS'];
                }
                if (param.effectVariant3D) {
                    asset.attribute.effectVariant3D = cloneDeep(param.effectVariant3D);
                }
                assetManager.setPv_new(id === 1 ? 2471 : id === 2 ? 2472 : id === 3 ? 2473 : id === 4 ? 2474 : 2475, {
                    additional: {},
                });
                assetManager.setPv_new(2406, {
                    additional: {
                        s0: '3DWordId',
                        s1: id,
                        s2: 'left',
                        s3: param.fontFamily,
                        // kid_1:createTime
                    },
                });
            }
        }

        let paintOnCanvasState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }) as IPaintOnCanvasState;
        fitPageDomSIze(paintOnCanvasState, asset);

        const { work, pageInfo } = paintOnCanvasState;
        const index = work.pages[pageInfo.pageNow].assets.length;
        this.addAssets('ADD_TEXTEDITOR', [asset], undefined, [
            {
                type: 'selectAsset',
                params: {
                    asset_index: index,
                    page_num: pageInfo.pageNow,
                    select_type: ESelectType.asset,
                },
            },
        ]);
        if(param.id && param.textType === '3D'){
            // 普通文字不走dom编辑
            window.setTimeout(() => {
                emitter.emit('addTextEditingEventListener', asset);
            }, 100);
        }
        if (textType === 'specificWord') {
            const effectVariant = await getSpecificWordInfo(param.id);
            if (!effectVariant) {
                return;
            }
            paintOnCanvasState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                store_name: storeAdapter.store_names.paintOnCanvas,
            }) as IPaintOnCanvasState;
            const { work, pageInfo } = paintOnCanvasState;

            AssetLogic.updateTextSpecificWordInfo({
                propAsset: work.pages[pageInfo.pageNow].assets[index],
                effectVariant,
                assetIndex: index,
                effect: asset.attribute.effect,
            });
        }
    }
    /**
     * 获取组合字数据
     */
    async getGroupWordInfo(groupWordListItem: IGroupWordListItemInfo, isFromCopyWriting?: boolean) {
        return new Promise((resolve, reject) => {
            assetManager.getGroupWordInfo(groupWordListItem.id).then((data) => {
                data.json().then((resultData) => {
                    const doc = templateFormat.getFormat(resultData.msg.doc) as IDoc;
                    // return ;
                    if (resultData.stat === 1) {
                        /*适应画布START*/
                        const { canvas } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                            store_name: storeAdapter.store_names.paintOnCanvas,
                        });
                        const groupAsset = doc.work.pages[0].assets.find((item) => item.meta.type === 'group');
                        if (!groupAsset) {
                            return;
                        }
                        groupAsset.attribute.rt_isNowAdd = true;
                        let width = canvas.width / 2,
                            height =
                                (width * Math.round(groupAsset.attribute.height)) / Math.round(groupAsset.attribute.width);
                        // width = canvas.width / 2 < width ? canvas.width / 2 : width
                        // height = width * Math.round(groupAsset.attribute.height) / Math.round(groupAsset.attribute.width)
                        if (height > canvas.height) {
                            height = canvas.height / 2 < height ? canvas.height / 2 : height;
                            width =
                                (height * Math.round(groupAsset.attribute.width)) / Math.round(groupAsset.attribute.height);
                        }
                        const widthScale = width / groupAsset.attribute.width,
                            heightScale = height / groupAsset.attribute.height;
                        const minScale = Math.min(widthScale, heightScale);
                        const newWidth = groupAsset.attribute.width * minScale;
                        const newHeight = groupAsset.attribute.height * minScale;
                        const offsetPosX = (canvas.width - newWidth) / 2;
                        const offsetPosY = (canvas.height - newHeight) / 2;

                        doc.work.pages[0].assets.forEach((item) => {
                            if (item.meta.type === 'group') {
                                item.attribute.width *= minScale;
                                item.attribute.height *= minScale;
                                item.meta.groupWordID = groupWordListItem.id;
                                if (isFromCopyWriting) {
                                    item.meta.addOrigin = 'Copywriting';
                                }
                                item.transform.posX += offsetPosX;
                                item.transform.posY += offsetPosY;
                                return;
                            }
                            if (item.attribute.container && item.attribute.container.id) {
                                const tempWidth = item.attribute.container.width * minScale,
                                    tempHeight = item.attribute.container.height * minScale;
                                item.attribute.container.posY =
                                    (item.attribute.container.posY / item.attribute.container.height) * tempHeight;
                                item.attribute.container.posX =
                                    (item.attribute.container.posX / item.attribute.container.width) * tempWidth;
    
                                item.attribute.width = (item.attribute.width / item.attribute.container.width) * tempWidth;
                                item.attribute.container.viewBoxWidth = item.attribute.container.width = tempWidth;
                                item.attribute.height =
                                    (item.attribute.height / item.attribute.container.height) * tempHeight;
                                item.attribute.container.viewBoxHeight = item.attribute.container.height = tempHeight;
                            } else {
                                if (item.meta.type === 'text') {
                                    item.attribute.fontSize *= minScale;
                                    item.attribute.letterSpacing *= minScale;
                                    item.attribute.text = item.attribute.text.map(textItem => {
                                        if (typeof textItem !== 'string') {
                                            return {
                                                ...textItem,
                                                fontSize: (textItem as IRichText).fontSize * minScale,
                                                letterSpacing: (textItem as IRichText).letterSpacing * minScale,
                                            }
                                        } else {
                                            return textItem
                                        }
                                    })
                                } else {
                                    item.attribute.height *= minScale;
                                }
                                item.attribute.width *= minScale;
                            }
                            item.transform.posX *= minScale;
                            item.transform.posY *= minScale;
                            item.transform.posX += offsetPosX;
                            item.transform.posY += offsetPosY;
                            item.rt_hideLoading = true;
                        });
                        /*适应画布END*/
    
                        const paintOnCanvasState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                            store_name: storeAdapter.store_names.paintOnCanvas,
                        }) as IPaintOnCanvasState;
                        const assets = doc.work.pages[0].assets;
                        const ga = assets.find((a) => a?.meta?.type === 'group');
                        if (ga) {
                            assets.forEach((item) => {
                                if (item?.meta?.type === 'group') {
                                    item.meta.className =
                                        item.meta.type + ++doc.work.nameSalt.salt + '_' + paintOnCanvasState.user.userId;
                                    item.meta.group = item.meta.className + '_' + new Date().getTime();
                                }
                            });
                            assets.forEach((a) => {
                                if (a?.meta?.type !== 'group') {
                                    a.meta.group = ga?.meta?.group;
                                    a.meta.rt_refresh = 0;
                                }
                            });
                        }
                        resolve(assets)
                    }
                });
            });
        })

    }
    /**
     * 添加组合字
     */
    async addGroupWord(
        groupWordListItem: IGroupWordListItemInfo,
        isFromCopyWriting: boolean,
        setPv?: { onSearch: boolean; onClickContent: (id: string, keyword: string) => void },
        keyword?: string,
    ) {
        const { canvas, pageInfo, work } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const assets = await this.getGroupWordInfo(groupWordListItem, isFromCopyWriting) as IAsset[];

        const groupIndex = assets.findIndex((a: IAsset) => a?.meta?.type === 'group') || 0;
        const index = work.pages[pageInfo.pageNow].assets.length + groupIndex;
        this.addAssets('ADD_GROUP_WORD', assets, undefined, [
            {
                type: 'resetAssetIndex',
                params: {},
            } ,{
                type: 'selectAsset',
                params: {
                    asset_index: index,
                    page_num: pageInfo.pageNow,
                    select_type: ESelectType.asset,
                },
            },
        ]);
        // createTime = Math.floor(new Date().getTime() / 1000)
        const { createTime } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (isFromCopyWriting) {
            setPv && setPv.onSearch && setPv.onClickContent(groupWordListItem.id, keyword); //
        } else {
            assetManager.setPv_new(168, {
                additional: {
                    s0: 'groupWordID',
                    s1: groupWordListItem.id,
                    s2: createTime,
                },
            });

            const url_props = IPSConfig.getProps();
            if (url_props['isDesigner']) {
                assetManager.setPv_new(3481, {
                    additional: {
                        s1: groupWordListItem.id,
                    },
                });
            }
        }
    }

    /** videoE 类型的元素 */
    addVideoE(
        videoE: {
            sample: string;
            width: string;
            height: string;
            preview: string;
            id: string;
            total_frame: string;
            duration: string;
        },
        isUser = true,
    ) {
        let asset: IAsset = cloneDeep(assetTemplate.video);
        const { width, height } = this.fitWidthAndHeight(Number.parseInt(videoE.width), {
            width: Number.parseInt(videoE.width),
            height: Number.parseInt(videoE.height),
        });

        asset = {
            ...asset,
            meta: {
                ...asset.meta,
                type: 'videoE',
                isBackground: false,
            },
            attribute: {
                ...asset.attribute,
                width: Math.round(width),
                height: Math.round(height),
                rt_url: videoE.sample,
                rt_frame_url: videoE.preview,
                resId: Number(videoE.id),
                rt_total_frame: Number(videoE.total_frame),
                isLoop: true,
                cst: 0,
                cet: Number(videoE.duration),
                isUser:isUser,
                volume: 100,
            },
        };

        const paintOnCanvasState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }) as IPaintOnCanvasState;
        fitPageDomSIze(paintOnCanvasState, asset);

        const { work, pageInfo } = paintOnCanvasState;
        const index = work.pages[pageInfo.pageNow].assets.length;
        this.addAssets('ADD_VIDEOE', [asset], undefined, [
            {
                type: 'selectAsset',
                params: {
                    asset_index: index,
                    page_num: pageInfo.pageNow,
                    select_type: ESelectType.asset,
                },
            },
        ]);
    }

    /** qrcode 类型的元素 */
    addQrcode(qrcode: any) {
        let asset: any = cloneDeep(assetTemplate.qrcode);
        const { width, height } = this.fitWidthAndHeight(Number.parseInt(qrcode.width), {
            width: Number.parseInt(qrcode.width),
            height: Number.parseInt(qrcode.height),
        });

        asset = {
            ...asset,
            meta: {
                ...asset.meta,
                type: 'qrcode',
            },
            attribute: {
                ...asset.attribute,
                width: Math.round(width),
                height: Math.round(height),
                qrcodeInfo: qrcode.qrcodeinfo,
            },
        };
        const paintOnCanvasState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }) as IPaintOnCanvasState;
        fitPageDomSIze(paintOnCanvasState, asset);

        const { work, pageInfo } = paintOnCanvasState;
        const index = work.pages[pageInfo.pageNow].assets.length;
        this.addAssets('ADD_QRCODE', [asset], undefined, [
            {
                type: 'selectAsset',
                params: {
                    asset_index: index,
                    page_num: pageInfo.pageNow,
                    select_type: ESelectType.asset,
                },
            },
        ]);
    }
    /**
     * 添加frame元素
     * @param frame frame 元素
     */
    addFrameEmitter() {
        const FRAME_EMITTER = 'frame_emitter';
        if (this.emitter_listener[FRAME_EMITTER]) {
            this.emitter_listener[FRAME_EMITTER].remove();
        }
        this.emitter_listener[FRAME_EMITTER] = emitter.addListener('ListAddFrame', (element: {
            [key: string]: any;
            [key: number]: any;
        },
        autoSaveFlag: number)=>{
            const asset: IAsset = cloneDeep(assetTemplate['frame']);
            const { width, height } = this.fitWidthAndHeight(Number.parseInt(element.width), {
                width: Number.parseInt(element.width),
                height: Number.parseInt(element.height),
            });
            asset.meta.type = 'frame';
            asset.meta.rt_isNowAdd = true;
            asset.attribute = {
                ...asset.attribute,
                resId: element.id,
                contentResId:element.resId ?? '',
                picUrl: element.picUrl ?? '',
                picWidth:element.picWidth ?? '',
                picHeight:element.picHeight ?? '',
                width: Math.round(width),
                height: Math.round(height),
                assetHeight: element.source_height,
                assetWidth: element.source_width,
                svg: element.svg ?? '',
                cropXTo: 1,
                cropYTo: 1,
                source_key: element.source_key,
                SVGUrl: element.sample_svg,
                sample: element.sample,
                // clipPath:element.clipPath,
                frameBackgroundInfo:{
                    ...asset.attribute.frameBackgroundInfo,
                    ...element.frameBackgroundInfo,
                    picUrl: ''
                },
            };

            const paintOnCanvasState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                store_name: storeAdapter.store_names.paintOnCanvas,
            }) as IPaintOnCanvasState;
            const { work, pageInfo } = paintOnCanvasState;
            fitPageDomSIze(paintOnCanvasState, asset);
            if(element.transform){
                asset.transform ={
                    posX:element.transform.posX,
                    posY:element.transform.posY
                }
            }
            console.log(asset.transform )

            const index = work.pages[pageInfo.pageNow].assets.length;
            if(element.attribute){
                this.addAssets('ADD_FRAME_BY_IMAGE', [asset], undefined, [
                    {
                        type: 'selectAsset',
                        params: {
                            asset_index: index,
                            page_num: pageInfo.pageNow,
                            select_type: ESelectType.asset,
                        },
                    },
                ]);
            }else{
                this.addAssets('ADD_FRAME', [asset], undefined, [
                    {
                        type: 'selectAsset',
                        params: {
                            asset_index: index,
                            page_num: pageInfo.pageNow,
                            select_type: ESelectType.asset,
                        },
                    },
                ]);
            }
            this.updateFav(asset.attribute.resId, 16);
        })
        
    }
    /**
     * 添加文字、特效字、3D字
     */
    addDocGraphicText(
        asset: IAsset,
        type?: string,
        tabId?: string | number, // 左侧菜单 navId 用于埋点
    ) {
        const { canvas, isDesigner, user:{userId} } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        const width = 200;
        const height = 50;

        asset = {
            ...asset,
            meta: {
                ...asset.meta,
                type: 'text',
                isEdit: false,
                addOrigin: 'text',
            },
            attribute: {
                ...asset.attribute,
                width: width,
            },
        };
        asset.transform = {
            ...asset.transform,
            posX: ((canvas.width - width) / 2) * canvas.scale,
            posY: ((canvas.height - height) / 2) * canvas.scale,
        };


        const paintOnCanvasState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }) as IPaintOnCanvasState;
        fitPageDomSIze(paintOnCanvasState, asset);

        const { work, pageInfo } = paintOnCanvasState;
        const index = work.pages[pageInfo.pageNow].assets.length;
        this.addAssets('ADD_TEXTEDITOR', [asset], undefined, [
            {
                type: 'selectAsset',
                params: {
                    asset_index: index,
                    page_num: pageInfo.pageNow,
                    select_type: ESelectType.asset,
                },
            },
        ]);

    }
    /**
     * 点击添加SVG（全局事件）
     */
    addFlowEmitter() {
        if (this.emitter_listener.flow_emitter) {
            this.emitter_listener.flow_emitter.remove();
        }
        this.emitter_listener.flow_emitter = emitter.addListener(
            'ListAddFlow',
            (
                element: {
                    [key: string]: any;
                    [key: number]: any;
                },
                autoSaveFlag: number,
            ) => {
                const asset: IAsset = cloneDeep(assetTemplate.image);
                const { width, height } = this.fitWidthAndHeight(element.width, {
                    width: element.width,
                    height: element.height,
                });
                asset.meta.type = 'flow';
                asset.attribute = {
                    ...asset.attribute,
                    resId: element.id,
                    picUrl: element.image_url ? element.image_url : 'loading',
                    width: Math.round(width),
                    height: Math.round(height),
                    assetHeight: element.source_height,
                    assetWidth: element.source_width,
                    cropXTo: 1,
                    cropYTo: 1,
                    source_key: element.source_key,
                    rt_isNowAdd: element.rt_isNowAdd || true,
                    SVGUrl: element.SVGUrl,
                    stroke: {
                        color: { r: 0, g: 0, b: 0, a: 1 },
                        width: 0,
                        style: 'solid',
                    }
                };

                const paintOnCanvasState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                }) as IPaintOnCanvasState;
                fitPageDomSIze(paintOnCanvasState, asset);

                const { work, pageInfo } = paintOnCanvasState;
                const index = work.pages[pageInfo.pageNow].assets.length;
                this.addAssets('ADD_FLOW', [asset], autoSaveFlag, [
                    {
                        type: 'selectAsset',
                        params: {
                            asset_index: index,
                            page_num: pageInfo.pageNow,
                            select_type: ESelectType.asset,
                        },
                    },
                ]);

                this.updateFav(element.id, 5);
            },
        );
    }
    /**
     * 添加连接线（全局事件）
     */
    addLineEmitter() {
        if (this.emitter_listener.line_emitter) {
            this.emitter_listener.line_emitter.remove();
        }
        this.emitter_listener.line_emitter = emitter.addListener(
            'ListAddLine',
            (
                element: {
                    fromCanvas: boolean;
                    [key: string]: any;
                    [key: number]: any;
                },
                autoSaveFlag: number,
            ) => {
                const asset: IAsset = cloneDeep(assetTemplate.image);
                const { width, height } = this.fitWidthAndHeight(element.width, {
                    width: element.width,
                    height: element.height,
                });
                let startArrow = 'line';
                let endArrow = 'line';
                if (element.id == 2 || element.id == 3) {
                    endArrow = 'arrow';
                }
                if (element.id == 1 || element.id == 3) {
                    startArrow = 'arrow';
                }
                const lineWidth = element.lineWidth ?? 10;
                asset.meta = {
                    ...asset.meta,
                    type: 'line',
                    uniqueId: generateShortUUID(),
                    ...(element.meta || {}),
                }
                asset.transform = {
                    ...asset.transform,
                    ...(element.transform || {}),
                }

                asset.attribute = {
                    ...asset.attribute,
                    resId: element.id,
                    width: Math.round(width),
                    height: Math.round(height),
                    cropXTo: 1,
                    cropYTo: 1,
                    color: { r: 0, g: 0, b: 0, a: 1 },
                    rt_isNowAdd: element.rt_isNowAdd || true,
                    lineWidth,
                    lineType: 'solid',
                    type: 'poly',
                    startArrow,
                    endArrow,
                    ...(element.attribute || {}),
                };

                const paintOnCanvasState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                }) as IPaintOnCanvasState;
                if (!element.fromCanvas) {
                    fitPageDomSIze(paintOnCanvasState, asset);
                    asset.attribute.start = {
                        x: 0,
                        y: 0,
                    }
                    asset.attribute.end = {
                        x: asset.attribute.width,
                        y: height - lineWidth,
                    }
                }

                const { work, pageInfo } = paintOnCanvasState;
                const index = work.pages[pageInfo.pageNow].assets.length;
                this.addAssets('ADD_LINE', element.copyShape ? [element.copyShape, asset] : [asset], autoSaveFlag, [
                    {
                        type: 'selectAsset',
                        params: {
                            asset_index: index,
                            page_num: pageInfo.pageNow,
                            select_type: ESelectType.asset,
                        },
                    },
                ]);
                // if (element.copyShape) {
                //     this.addAssets('ADD_SVG', [element.copyShape], autoSaveFlag, [
                //         {
                //             type: 'selectAsset',
                //             params: {
                //                 asset_index: index,
                //                 page_num: pageInfo.pageNow,
                //                 select_type: ESelectType.asset,
                //             },
                //         },
                //     ]);
                // }
            },
        );
    }
}

const assetAddListener = new AssetAddListener();
export { assetAddListener as AssetAddListener };
