import React, { PureComponent } from 'react';
import { storeDecorator } from '@v7_logic/StoreHOC';
import { ICanvas, IPageAttr, IPageInfo, IWork } from '@v7_logic/Interface';
import { EditorThumbnailLogic } from '@v7_logic/EditorThumbnailLogic';
import { Asset } from '@v7_render/Asset';
import { emitter } from '@src/userComponentV6.0/Emitter';
import type { EventSubscription } from 'fbemitter';

interface IEditorThumbnailProps {
    canvas?: ICanvas;
    pageInfo?: IPageInfo;
    work?: IWork;
    canvas_wrapper_dom_info?: {
        class_name: string;
        is_exist: boolean;
    };
    canvas_dom_info?: {
        class_name: string;
        is_exist: boolean;
    };
    pageAttr?: IPageAttr;
    preview?: string[];
    toolPanelWidth?: number;
    rt_canvas_render_mode?: '' | 'pull' | 'board';
}

const mapStateToProps = (state: { [key: string]: any }) => {
    return {
        canvas: state.onCanvasPainted.canvas,
        pageInfo: state.onCanvasPainted.pageInfo,
        work: state.onCanvasPainted.work,
        canvas_wrapper_dom_info: state.onCanvasPainted.canvas_wrapper_dom_info,
        canvas_dom_info: state.onCanvasPainted.canvas_dom_info,
        pageAttr: state.onCanvasPainted.pageAttr,
        preview: state.onCanvasPainted.preview,
        rt_canvas_render_mode: state.onCanvasPainted.rt_canvas_render_mode,
    };
};

/**
 * 模板导航（预览小窗）
 */
@storeDecorator(mapStateToProps)
export class EditorThumbnail extends PureComponent<IEditorThumbnailProps, {canvasAddPage:boolean, showPreviewImage: boolean}> {
    navigator = React.createRef<HTMLDivElement>();
    navigatorContent = React.createRef<HTMLDivElement>();
    navigatorShowBox = React.createRef<HTMLDivElement>();
    previewCanvasScaleRef = React.createRef<HTMLDivElement>();
    isCanvasMoving = false;
    onCanvasMoving: EventSubscription;
    onCanvasMoveEnd: EventSubscription;
    setCanvasAddPageListener:EventSubscription;
    constructor(props:IEditorThumbnailProps){
        super(props)
        this.state = {
            canvasAddPage: false,
            showPreviewImage: true,
        };
    }

    navigatorMouseDown = (e: React.MouseEvent): void => {
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
        const proportion = parseInt(this.navigatorContent.current.style.height) / this.props.canvas.height;
        EditorThumbnailLogic.onMouseDown(e, proportion, (l: number, t: number) => {
            const navigatorShowBoxDom = this.navigatorShowBox.current;
            navigatorShowBoxDom.style.left = (-l / this.props.canvas.scale) * proportion + 'px';
            navigatorShowBoxDom.style.top = (-t / this.props.canvas.scale) * proportion + 'px';
        });
    };

    updateStyle = (): void => {
        const { canvas, canvas_wrapper_dom_info, canvas_dom_info } = this.props;
        if (!canvas_dom_info.is_exist && !canvas_wrapper_dom_info.is_exist) {
            return;
        }
        const navigatorShowBoxDom = this.navigatorShowBox.current,
            navigatorContentDom = this.navigatorContent.current,
            previewCanvasScaleDom = this.previewCanvasScaleRef.current,
            navigatorDom = this.navigator.current,
            canvasContentDom: HTMLDivElement = document.querySelector('.' + canvas_dom_info.class_name),
            canvasWrapperDom: HTMLDivElement = document.querySelector('.' + canvas_wrapper_dom_info.class_name);
        const {diffwidth = '0'} = canvasWrapperDom.dataset
        const canvasWrapperDomWidth = canvasWrapperDom.clientWidth - Number.parseInt(diffwidth)

        let tempWidth, tempHeight;

        navigatorDom.style.width = '120px';
        navigatorDom.style.height =
            (parseInt(navigatorDom.style.width) * canvasWrapperDom.clientHeight) / canvasWrapperDomWidth + 'px';

        tempHeight = parseInt(navigatorDom.style.height) - 10;
        tempWidth = (tempHeight * canvas.width) / canvas.height;
        if (tempWidth > tempHeight) {
            tempWidth = parseInt(navigatorDom.style.width) - 10;
            tempHeight = (tempWidth * canvas.height) / canvas.width;
        }

        navigatorContentDom.style.height = tempHeight + 'px';
        navigatorContentDom.style.width = tempWidth + 'px';
        navigatorContentDom.style.marginTop = -tempHeight / 2 + 'px';
        navigatorContentDom.style.marginLeft = -tempWidth / 2 + 'px';

        previewCanvasScaleDom.style.transform = `scale(${tempWidth / (canvas.width * canvas.scale)})`;
        if (
            (canvas.width * canvas.scale) / canvasWrapperDomWidth > 1 ||
            (canvas.height * canvas.scale) / canvasWrapperDom.clientHeight > 1 ||
            this.isCanvasMoving
        ) {
            navigatorDom.style.visibility = 'visible';
        } else {
            navigatorDom.style.visibility = 'hidden';
        }

        const proportion = parseInt(navigatorContentDom.style.height) / canvas.height;
        navigatorShowBoxDom.style.left = (-parseInt(canvasContentDom.style.left) / canvas.scale) * proportion + 'px';
        navigatorShowBoxDom.style.top = (-parseInt(canvasContentDom.style.top) / canvas.scale) * proportion + 'px';
        navigatorShowBoxDom.style.width = (canvasWrapperDomWidth / canvas.scale) * proportion + 'px';
        navigatorShowBoxDom.style.height = (canvasWrapperDom.clientHeight / canvas.scale) * proportion + 'px';
    };
    canvasAddPageEmitter(){
        this.setCanvasAddPageListener = emitter.addListener('setCanvasAddPage', (isShowMorePage:boolean) => {
            this.setState({ canvasAddPage: !isShowMorePage });
        });
    }
    canvasAddPageEmitterRemove(){
        if(!this.setCanvasAddPageListener) return
        this.setCanvasAddPageListener.remove()
    }
    componentDidMount(): void {
        this.onCanvasMoving = emitter.addListener('onCanvasMoving', () => {
            this.isCanvasMoving = true;
        });
        this.onCanvasMoveEnd = emitter.addListener('onCanvasMoveEnd', () => {
            this.isCanvasMoving = false;
            this.updateStyle();
        });
        this.canvasAddPageEmitter()
    }

    componentWillUnmount(): void {
        this.onCanvasMoving?.remove();
        this.onCanvasMoveEnd?.remove();
        this.canvasAddPageEmitterRemove()
    }

    componentDidUpdate(prevProps: IEditorThumbnailProps): void {
        if (this.props.rt_canvas_render_mode === 'board') {
            return null; // 如果是 board 模式，则不渲染缩略图
        }
        this.updateStyle();
        if (this.state.showPreviewImage && prevProps.work.pages[prevProps.pageInfo.pageNow].assets !== this.props.work.pages[this.props.pageInfo.pageNow].assets) {
            this.setState({
                showPreviewImage: false,
            })
        }
    }

    render(): React.ReactNode {
        const { canvasAddPage, showPreviewImage } = this.state
        const { canvas, pageInfo, work, pageAttr, preview, rt_canvas_render_mode } = this.props;
        if (rt_canvas_render_mode === 'board') {
            return null; // 如果是 board 模式，则不渲染缩略图
        }
        const page = work.pages[pageInfo.pageNow];
        let paintCanvasStyle: React.CSSProperties = {
            width: '100%',
            height: '100%',
            overflow: 'hidden',
            position: 'absolute',
            top: 0,
            left: 0,
        };

        //透明背景
        if (work.pages[pageInfo.pageNow].isOpacityBg) {
            paintCanvasStyle = {
                ...paintCanvasStyle,
                backgroundImage: `url(//s.tuguaishou.com/image/picEditor/opacity.png)`,
                backgroundSize: '20px',
            };
        }
        //透明背景 end
        if (pageInfo.pageNow === 0) {
            if (canvas?.backgroundColor?.r) {
                paintCanvasStyle.background = `rgba(${canvas.backgroundColor.r}, ${canvas.backgroundColor.g}, ${canvas.backgroundColor.b}, ${canvas.backgroundColor.a})`;
            }
        }

        if (
            pageAttr.backgroundImage &&
            pageAttr.backgroundImage[pageInfo.pageNow] &&
            pageAttr.backgroundImage[pageInfo.pageNow].resId != ''
        ) {
            Object.assign(paintCanvasStyle, {
                background: `url(${pageAttr.backgroundImage[pageInfo.pageNow].rt_imageUrl})`,
                backgroundSize: `${Number(pageAttr.backgroundImage[pageInfo.pageNow].backgroundSize.width) * canvas.scale}px ${
                    Number(pageAttr.backgroundImage[pageInfo.pageNow].backgroundSize.height) * canvas.scale
                }px`,
            });
        }

        const canvasContentStyleScale: React.CSSProperties = {
            width: canvas.width * canvas.scale + 'px',
            height: canvas.height * canvas.scale + 'px',
            position: 'absolute',
            boxShadow: '1px 1px 15px rgba(0,0,0,.2)',
        };
        const previewPaintCanvasStyle: React.CSSProperties = {
            ...paintCanvasStyle,
            width: canvasContentStyleScale.width,
            height: canvasContentStyleScale.height,
            // transform : `scale(${previewCanvasScale})`,
            transformOrigin: 'left top',
        };
        const thumbnail = preview?.[pageInfo.pageNow]
        let right = '10px';
        if (!canvas.floorCutting) {
            right = this.props.toolPanelWidth + 20 + 'px';
        }
        return (
            <div
                className={["navigator",canvasAddPage && 'navigator-add-page'].join(' ')}
                style={{right}}
                ref={this.navigator}
                // onMouseDown={this.navigatorDownEvent}
                onMouseDown={this.navigatorMouseDown}
            >
                <div className="navigatorWrap">
                    <div className="navigatorContent" ref={this.navigatorContent}>
                        <div
                            ref={this.previewCanvasScaleRef}
                            style={previewPaintCanvasStyle}
                            key={'preview-pages-' + pageInfo.pageNow}
                        >
                            {showPreviewImage && thumbnail?<img src={thumbnail} style={previewPaintCanvasStyle} alt="thumbnail"/>:page.assets.map((asset, index2) => {
                                const assetProps = {
                                    classTag: 'navigatorWrap',
                                    showOnly: true,
                                };

                                const temp = (
                                    <Asset
                                        key={asset.meta.className + '_' + pageInfo.pageNow}
                                        asset={asset}
                                        canvas={canvas}
                                        isAssetActive={false}
                                        isShowOnly={true}
                                        assetProps={assetProps}
                                        parent="navigator"
                                    ></Asset>
                                );

                                return temp;
                            })}
                        </div>
                        <div className="navigatorShowBox" ref={this.navigatorShowBox}></div>
                    </div>
                </div>
            </div>
        );
    }
}
