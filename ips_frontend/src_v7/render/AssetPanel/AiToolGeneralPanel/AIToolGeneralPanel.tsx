import { Component } from 'react';
import React from 'react';
import styles from './scss/index.module.scss';
import { imgHost } from '@src/userComponentV6.0/IPSConfig';
import { Tooltip } from 'antd';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { emitter } from '@src/userComponentV6.0/Emitter';
import RechargeAiPopup from '../RechargeAiPopup';
import { UserLogic } from '@v7_logic/UserLogic';
import { AIAssets } from './components/AIAssets';
import { UploadFile } from './components/upload';
import { ETool } from '@v7_logic/Enum';
import { CanvasPaintedLogic } from '@v7_logic/CanvasPaintedLogic';
import { assetManager } from '@src/userComponentV6.0/AssetManager';
import { CutoutInfo } from '@src/userComponentV6.0/Cutout/Cutout';
import { EventSubscription } from 'fbemitter';
import { PopupLimitBox } from '../MyPanel/components/UploadLimit';
import { storeDecorator } from '@v7_logic/StoreHOC';
import { IStoreState } from '@v7_store/redux/store';
import { IUserInfo } from '@v7_logic/Interface';
export function DisplayAICutoutPanel(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.AI_CUTOUT, nav: ETool.AI_CUTOUT};
}
export function DisplayAIEnhancePanel(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.AI_ENHANCE, nav: ETool.AI_ENHANCE};
}
export function DisplayAIEliminatePanel(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.AI_ELIMINATE, nav: ETool.AI_ELIMINATE};
}
export function DisplayAIEliminateRedrawPanel(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.AI_ELIMINATEREDRAW, nav: ETool.AI_ELIMINATEREDRAW};
}

export function DisplayAIChatPanel(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.AI_CHAT, nav: ETool.AI_CHAT};
}

interface IAIToolGeneralPanelProps {
    toolType?: string;
    user?: IUserInfo;
}
interface IAIToolGeneralPanelState {
    cutoutCount: number;
}
@storeDecorator((state: IStoreState) => {
    return {
        user: state.onCanvasPainted.user,
    };
})
export class AIToolGeneralPanel extends Component<IAIToolGeneralPanelProps, IAIToolGeneralPanelState> {
    useCutoutCountEventEmitter: EventSubscription;
    constructor(props: IAIToolGeneralPanelProps) {
        super(props);
        this.state = {
            cutoutCount: 0,
        };
    }
    componentDidMount(): void {
        this.getCutoutCount();
        this.useCutoutCountEventEmitter = emitter.addListener('useCutoutCount', () => {
            this.setState({
                cutoutCount: this.state.cutoutCount - 1,
            });
        });
    }
    componentWillUnmount(): void {
        this.useCutoutCountEventEmitter.remove();
    }
    // 消费点数
    getPointNum() {
        UserLogic.getUserAIInfo();
    }
    rechargeAI(pointNum: number, fixNum: number, wordsNum: number) {
        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const windowInfo = {
            windowContent: (
                <RechargeAiPopup
                    from="aiEnhanceLimit_ue_edit"
                    pointNum={pointNum}
                    fixNum={fixNum}
                    wordsNum={wordsNum}
                    username={user.userName}
                    avatar={user.avatar}
                    refresh={this.getPointNum.bind(this)}
                />
            ),
            popupWidth: 'auto',
            popupHeight: 'auto',
            style: {
                width: '790px',
                height: '420px',
                background: 'transparent',
                borderRadius: '5px 5px 5px 5px',
                left: '0',
                top: '0',
                right: '0',
                bottom: '0',
                margin: 'auto',
                padding: '0',
            },
            popupTitleBarStyle: {
                width: 0,
                height: 0,
                display: 'none',
            },
        };
        emitter.emit('popupWindow', windowInfo);
    }
    addPoints(pointNum: number, fixNum: number, wordsNum: number) {
        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (user.userName) {
            this.rechargeAI(pointNum, fixNum, wordsNum);
        } else {
            emitter.emit('LoginPanelShow');
        }
        assetManager.setPv_new(8890,{
            additional: {
                s0: this.props.toolType,
            },
        });
        
    }
    renderRest() {
        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const { pointNum, fixNum } = user;
        let rest_text = '点';
        let rest_num: number = pointNum;
        if (pointNum <= 0) {
            rest_text = '张';
            rest_num = fixNum;
        }
        return (
            <>
                共&nbsp;
                <b>{rest_num}</b>
                &nbsp;
                {rest_text}
            </>
        );
    }
    getToolName() {
        let txt = '';
        switch (this.props.toolType) {
            case ETool.AI_CUTOUT:
                txt = '抠图';
                break;
            case ETool.AI_DRAW:
                txt = '素材生成';
                break;
            case ETool.AI_ENHANCE:
                txt = '变清晰';
                break;
            case ETool.AI_ELIMINATE:
                txt = '消除';
                break;
            case ETool.AI_ELIMINATEREDRAW:
                txt = '生成';
                break;
            case ETool.AI_TEXT:
                txt = '文案';
                break;
        }
        return txt;
    }
    /**
     * @description: 获取对应的ai工具名称
     * @return {*}
     */

    onUploadSuccess(imgData) {
        // 正在抠图
        const { toolPanel, work, pageInfo } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const asset = toolPanel.asset;
        const replaceAssetIndex = work.pages[pageInfo.pageNow].assets.indexOf(asset);
        // 添加图片需要构建这样的数据格式
        const { id, picUrl, width, height } = imgData;
        CanvasPaintedLogic.updateAssetInfo({
            index: replaceAssetIndex,
            newPageNow: pageInfo.pageNow,
            info: {
                attribute: {
                    resId: id,
                    picUrl,
                    assetHeight: height,
                    assetWidth: width,
                },
            },
        });
        UserLogic.getUserUploadLimitInfo()
        this.onAssetAddCallback();
    }
    onAssetAddCallback() {
        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        switch (this.props.toolType) {
            case ETool.AI_CUTOUT:
                if(user.bind_phone == 0){
                    emitter.emit('InfoBarPhoneBindPopup');
                    return
                }
                return emitter.emit('startCutoutEmitter');
            case ETool.AI_ENHANCE:
                return emitter.emit('startEnhanceEmitter');
            case ETool.AI_ELIMINATE:
                return emitter.emit('startEliminateEmitter');
            case ETool.AI_ELIMINATEREDRAW:
                return emitter.emit('startEliminateEmitter', true);
        }
        return;
    }
    getCutoutCount() {
        if (this.props.toolType !== ETool.AI_CUTOUT) return;
        assetManager.getCutOutNumInfo().then((data) => {
            data.json().then((resultData) => {
                if (resultData.stat === 1) {
                    this.setState({
                        cutoutCount: resultData.data.num,
                    });
                }
            });
        });
    }
    // 抠图次数限制弹框
    showCutoutPop() {
        const windowInfo = {
            windowContent: <CutoutInfo snum={0} className="cutOutFaile" />,
            popupWidth: 440,
            popupHeight: 420,
            style: {
                padding: 0,
                background: 'none',
            },
            popupTitleBarStyle: {
                display: 'inline-block',
            },
            popupBodyStyle: {
                padding: 0,
            },
        };
        emitter.emit('popupWindow', windowInfo);
    }
    toVip = () => {
        assetManager.setPv_new(7459);
        this.showCutoutPop();
    };
    // 空间限制弹框
    showSpacePop(total: number, limit: number) {
        const windowInfo = {
            windowContent: <PopupLimitBox fromType={this.props.toolType} total={total} limit={limit} pos="11" />,
            popupWidth: 'auto',
            popupHeight: 'auto',
            style: {
                width: '600px',
                height: '220px',
                background: '#fff',
                boxShadow: '0px 4px 14px 0px rgba(31,26,27,0.16)',
                borderRadius: '12px 12px 12px 12px',
                left: '0',
                top: '0',
                right: '0',
                bottom: '0',
                margin: 'auto',
                padding: '24px',
            },
            popupTitleBarStyle: {
                width: 0,
                height: 0,
                display: 'none',
            },
        };
        assetManager.setPv_new(8898, {
            additional: {
                s0: this.props.toolType
            }
        })
        emitter.emit('popupWindow', windowInfo);
    }
    checkUploadLimit() {
        // const [total,limit] = await this.judegePicSpace()
        const { uploadTotal, uploadLimit } = this.props.user;

        const cutoutCount = this.state.cutoutCount;
        const { is_firm_vip } = this.props.user;
        // is_firm_vip 是否是企业vip1是 0不是
        if (is_firm_vip != 1 && uploadTotal >= uploadLimit) {
            this.showSpacePop(uploadTotal, uploadLimit);
            assetManager.setPv_new(8889,{
                additional: {
                    s0: this.props.toolType,
                },
            });
            return false;
        }
        if (this.props.toolType == ETool.AI_CUTOUT && !cutoutCount) {
            assetManager.setPv_new(7517);
            this.showCutoutPop();
            return false;
        }
        return true;
    }
    render() {
        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const { pointNum = 0, fixNum = 0, wordsNum = 0 } = user;
        const { toolType } = this.props;
        return (
            <div className={styles['AIToolGeneralPanel']}>
                <div className={styles['title-wrap']}>
                    <img src={imgHost + `/index_img/editorV7.0/menuIcon-${this.props.toolType}.png`} alt="" />
                    <div className={styles['title']}>AI{this.getToolName()}</div>
                </div>
                <div className={styles['generate-wrap']}>
                    <div
                        className={styles['generate-btn']}
                        onClick={() => {
                            assetManager.setPv_new(8887, {
                                additional: {
                                    s0: this.props.toolType,
                                },
                            });
                        }}
                    >
                        上传图片{this.getToolName()}
                        <span className={styles['tip-icon']}>
                            <i className="iconfont icon-AIshengcheng"></i>1
                        </span>
                        <UploadFile
                            nav={toolType}
                            onUploadSuccess={this.onUploadSuccess.bind(this)}
                            checkUploadLimit={this.checkUploadLimit.bind(this)}
                        ></UploadFile>
                    </div>
                    <div className={styles['info-wrap']}>
                        {toolType === ETool.AI_CUTOUT ? (
                            <>
                                <div className={styles['cutout-info']}>
                                    <span onClick={this.toVip} className={styles['emphasis-text']}>
                                        +增加张数
                                    </span>
                                    <span>
                                        剩余抠图张数：
                                        <span className={styles['emphasis-text']}>{this.state.cutoutCount}</span>
                                    </span>
                                </div>
                            </>
                        ) : (
                            <>
                                <div className={styles['info-num']}>
                                    <i className="iconfont icon-AIshengcheng"></i>
                                    {this.renderRest()}
                                    <span
                                        className={styles['add-num']}
                                        onClick={this.addPoints.bind(this, pointNum, fixNum, wordsNum)}
                                    >
                                        +增加{pointNum > 0 ? '点数' : '张数'}
                                    </span>
                                </div>
                                <div className={styles['info-tip']}>
                                    <Tooltip
                                        trigger={'hover'}
                                        title={() => {
                                            return (
                                                <strong>
                                                    图怪兽AI服务处于前沿探索阶段，功能采用了
                                                    <b style={{ color: '#fff' }}>
                                                        <i>豆包AI作画API</i>
                                                    </b>
                                                    进行实现。用户在使用本产品时，请注意以下事项：
                                                    您应当合法、合规地使用本服务，并承担由此产生的所有责任。
                                                    本服务生成的作品仅供个人学习、交流使用，不可用于商业用途。图怪兽对您的使用不做任何形式的保证且不承担任何责任。
                                                    您应当知悉，AI生成的内容均由人工智能模型生成，其中通过
                                                    <b style={{ color: '#fff' }}>
                                                        <i>豆包AI作画API</i>
                                                    </b>
                                                    获得的数据、信息或内容的准确性、完整性和及时性均由百度提供和控制。我们不对这些信息的任何错误、遗漏、或未及时更新承担责任。
                                                    虽然图怪兽对AI生成内容无法做任何保证，但生成内容服务会消耗算力，因此仍会消耗点数。
                                                </strong>
                                            );
                                        }}
                                    >
                                        <i className="iconfont icon-tishi1"></i>免责声明
                                    </Tooltip>
                                </div>
                            </>
                        )}
                    </div>
                </div>
                <div className={styles['assets-wrap']}>
                    <div className={styles['title']}>选择图片{this.getToolName()}</div>
                    <AIAssets
                        toolType={toolType}
                        toolName={'去' + this.getToolName()}
                        assetItemClick={()=>{
                            this.onAssetAddCallback();
                            assetManager.setPv_new(8888, {
                                additional: {
                                    s0: this.props.toolType,
                                },
                            });
                        }}
                    ></AIAssets>
                </div>
            </div>
        );
    }
}
