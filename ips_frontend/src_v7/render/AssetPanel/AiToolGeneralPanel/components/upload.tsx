import React, { PureComponent } from 'react';
import { assetManager } from '@component/AssetManager';
import { emitter } from '@component/Emitter';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { storeDecorator } from '@v7_logic/StoreHOC';
import { IStoreState } from '@v7_store/redux/store';
import { IUserInfo } from '@v7_logic/Interface';
import { EventSubscription } from 'fbemitter';
import { CanvasPaintedLogic } from '@v7_logic/CanvasPaintedLogic';
import { FILETYPE, FILEADDRESS, UPLOADNAME, UPLOADSIZE, UPLOADSIZENUM, TPropsData } from './uploadData';
import { loginCheckDecorator } from '@v7_logic/Decorators/Method';
import { PopupLimitBox } from '@v7_render/AssetPanel/MyPanel/components/UploadLimit';
import { CutoutInfo, CutoutInfoV2 } from '@src/userComponentV6.0/Cutout/Cutout';
import { message } from 'antd';
interface propsStruct {
    user?: IUserInfo;
    propsData?: TPropsData;
    onChangeKey?: (type: string) => void;
    userInfo?: IUserInfo;
    total?: number;
    limit?: number;
    onUploadSuccess?: (imgDate: Record<string, string>) => void;
    onUploadFail?: (msg: string) => void;
    checkUploadLimit?: () => boolean;
    nav?: string;
    rt_currentNav?: string;
    
}

interface stateStruct {
    uploadType: string;
    cutoutCount: number;
}

const whiteList = ['1', '2', '3', '4', '5'];
const whiteUserList = ['u814xa8t8r', '7190602', '1493596', '2516801', '21245082'];
@storeDecorator((state: IStoreState) => {
    return {
        userInfo: state.onCanvasPainted.user,
        rt_currentNav: state.onCanvasPainted.rt_currentNav,
    };
})
export class UploadFile extends PureComponent<propsStruct, stateStruct> {
    uploadEventEmitter: EventSubscription;
    uploadFileEventEmitter: EventSubscription;
    useCutoutCountEventEmitter: EventSubscription;
    uploadFormRef = React.createRef<HTMLFormElement>();
    uploadInputRef = React.createRef<HTMLInputElement>();
    constructor(props: propsStruct) {
        super(props);
        this.state = {
            uploadType: '',
            cutoutCount: 0,
        };
        this.uploadEventEmitter = emitter.addListener('uploadFileEvent', () => {
            this.uploadFileSelectClick();
        });
        this.uploadFileEventEmitter = emitter.addListener('uploadFileEventEmitter', (e: undefined, files: FileList) => {
            if (this.props.nav && this.props.nav !== this.props.rt_currentNav) {
                return;
            }
            this.uploadFileInputChange(e, files);
        });

        this.useCutoutCountEventEmitter = emitter.addListener('useCutoutCount', () => {
            this.setState({
                cutoutCount: this.state.cutoutCount - 1,
            });
        });
    }

    /**
     * 点击上传，判断用户状态
     */
    @loginCheckDecorator
    uploadFileSelectClick = () => {
        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (user?.bind_phone === 0) {
            emitter.emit('InfoBarPhoneBindPopup');
            return;
        }
        if ((this.props.checkUploadLimit && !this.props.checkUploadLimit()) ) {
            return;
        }
        this.uploadInputRef.current?.click();

        assetManager.setPagePv_new(7453);
    };
    getCutoutCount() {
        assetManager.getCutOutNumInfo().then((data) => {
            data.json().then((resultData) => {
                if (resultData.stat === 1) {
                    this.setState({
                        cutoutCount: resultData.data.num,
                    });
                }
            });
        });
    }
    componentDidMount(): void {
        this.getCutoutCount();
    }

    componentWillUnmount(): void {
        this.uploadEventEmitter?.remove();
        this.uploadFileEventEmitter?.remove();
        this.useCutoutCountEventEmitter?.remove();
    }
    // 抠图次数限制弹框
    showCutoutPop() {
        const windowInfo = {
            windowContent: <CutoutInfo snum={0} className="cutOutFaile" />,
            popupWidth: 440,
            popupHeight: 420,
            style: {
                padding: 0,
                background: 'none',
            },
            popupTitleBarStyle: {
                display: 'inline-block',
            },
            popupBodyStyle: {
                padding: 0,
            },
        };
        emitter.emit('popupWindow', windowInfo);
    }

    // 空间实时判断
    judegePicSpace() {
        return Promise.all([assetManager.getAssetCount()])
            .then((res) => {
                return Promise.all([res[0].json()]);
            })
            .then((res) => {
                return [res[0].data.total, res[0].data.upload_num];
            });
    }


    /**
     * 选择文件
     */
    uploadFileInputChange = (e: React.ChangeEvent, dropFile?: FileList) => {
        if ((this.props.checkUploadLimit && !this.props.checkUploadLimit()) ) {
            return;
        }
        // const [total,limit] = await this.judegePicSpace()
        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const { userId, is_firm_vip } = user;
        const id = String(userId);

        const show =
            (whiteList.includes(String(id.slice(id.length - 1, id.length))) ||
                id === 'u814xa8t8r' ||
                id === '7190602' ||
                id === '1493596' ||
                id === '2516801' ||
                id === '21245082') &&
            is_firm_vip != 1;

        const files = dropFile || (e.currentTarget as HTMLInputElement).files;
        // const { total, limit } = this.props;
        // console.log(total,limit)
        // const uploadLimit = limit - total;

        if (files.length === 0) {
            message.error('请选择文件');
            return false;
        } else if (files.length > 10) {
            message.error('最多可同时上传10个图片');
            return false;
        } else {
            const FILEFORMAT = 'image/png, image/jpeg';
            const fileList = Object.values(files).filter((file) => FILEFORMAT.includes(file.type));
            // if (show) {
            //     fileList = fileList.slice(0, uploadLimit);
            // }
            fileList.forEach((file) => {
                const type = this.onUploadFile(file);
                return type;
            });
        }
        this.uploadInputRef.current.value = '';
    };
    /** 循环处理选择的文件 */
    onUploadFile = (file: File) => {
        const type = 'img';
        if (file.size > UPLOADSIZE[type]) {
            this.uploadFail(`您上传的${UPLOADNAME[type]}文件过大，建议压缩至${UPLOADSIZENUM[type]}MB以内。`);
            return;
        }
        const key = Math.floor(Math.random() * 100000) + '_' + new Date().getTime();
        this.upload(file, type, key);
        return type;
    };

    uploadFail = (msg: string) => {
        message.error(msg);
    };
    // 临时图片加载 保证上传图片过慢时候的用户体验
    showTempImg(file: File) {
        const img = new Image();
        const url = URL.createObjectURL(file);
        const tempId = 'temp_' + new Date().getTime();
        img.onload = () => {
            const element = {
                id: tempId,
                image_url: url,
                width: img.width,
                height: img.height,
                source_width: img.width,
                source_height: img.height,
            };
            emitter.emit('ListAddPic', element);
        };
        img.src = url;
        return tempId;
    }
    upload = async (file: File, type: string, fileKey: string) => {
        // 本地文件临时显示的id
        const tempId = this.showTempImg(file);
        try {
            const cdnInfo = await (await assetManager.getUploadFilename(encodeURIComponent(file.name), FILEADDRESS[type])).json();
            if (cdnInfo.stat === 1) {
                const ossInfo = await this.getOssInfo(type, file, cdnInfo.msg.cdnName);
                if (ossInfo.stat === 1) {
                    const formData = new FormData();
                    formData.append('Signature', ossInfo.msg.signature);
                    formData.append('policy', ossInfo.msg.base64policy);
                    formData.append('callback', ossInfo.msg.base64callback_body);
                    formData.append('OSSAccessKeyId', ossInfo.msg.id);
                    formData.append('key', ossInfo.msg.key);
                    formData.append('file', file);
                    //uploadUserVideoEToCdn 公用一个接口
                    const uploadResult = await assetManager.uploadUserVideoEToCdn(
                        `//${ossInfo.msg.bucket}.${ossInfo.msg.endpoint}`,
                        formData,
                    );

                    if (uploadResult.stat === 1) {
                        assetManager.setPv_new(7514);
                        this.props.onUploadSuccess?.(uploadResult.msg);
                    } else {
                        this.uploadFail(uploadResult.msg || '上传失败');
                    }
                } else {
                    this.uploadFail(ossInfo.msg || '获取上传信息失败');
                }
            } else if (cdnInfo.code === -1001) {
                emitter.emit('LoginPanelShow');
            } else if (cdnInfo.code === -1002) {
                emitter.emit('InfoBarPhoneBindPopup');
            } else {
                this.uploadFail(cdnInfo.msg || '获取上传信息失败');
            }
        } catch (error) {
            this.uploadFail('上传失败');
        }
    };

    getUserAssetList() {
        assetManager.getUserAssetList().then((data) =>
            data.json().then((resultData) => {
                if (resultData.stat == 1) {
                    CanvasPaintedLogic.updateUserAssetList({
                        assetList: resultData.msg,
                    });
                }
            }),
        );
    }

    getOssInfo = async (type: string, file: File, cdnName: string) => {
        const ossInfo = await assetManager
            .getUploadOssForm(file.name, 0, 0, cdnName)
            .then((response) => response.json());
        return ossInfo;
    };

    toVip = () => {
        assetManager.setPv_new(7459);
        // let vipHref = 'https://818ps.com/dash/pay-matting?origin=koutu';
        this.showCutoutPop();
    };

    render(): JSX.Element {
        return (
            <div className="uploadArea">
                <div className="tgs_upload_file" onClick={this.uploadFileSelectClick}>
                    <form className="hiddenUploadForm" ref={this.uploadFormRef}>
                        <input
                            type="file"
                            accept={FILETYPE}
                            ref={this.uploadInputRef}
                            onChange={this.uploadFileInputChange}
                        />
                    </form>
                </div>
            </div>
        );
    }
}
