import React, { CSSProperties, PureComponent } from 'react';
import '../scss/asset.scss';
import LazyLoad from 'react-lazy-load4';
import { assetManager } from '@component/AssetManager';
import { getTime } from '../publicFun';
import { TAsset } from '../type';
import { emitter } from '@component/Emitter';
import ContextApp from '../context';
import { AssetAddListener } from '@v7_logic/AssetAddListener';
import { CanvasPaintedLogic } from '@v7_logic/CanvasPaintedLogic';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { ImageToolLogic } from '@v7_render/AssetToolPanel/Image/ImageToolLogic/ImageToolLogic';
interface propsStruct {
    inputChange?: (v: string) => void;
    inputSearch?: (v: string) => void;
    assetItem?: TAsset;
    height?: number | string;
    type?: string;
    kind?: string; //  用于 埋点区分是外部楼层点击，还是内部查看更多点击
    onClickItem?: (item: any) => void;
    assetListId?: string;
}

interface stateStruct {
    isFav: boolean;
}

export class AssetItem extends PureComponent<propsStruct, stateStruct> {
    constructor(props: propsStruct) {
        super(props);
        this.state = {
            isFav: false,
        };
    }

    componentDidMount(): void {
        const { assetItem } = this.props;
        this.setState({
            isFav: assetItem.is_fav === 1,
        });
    }
    /**
     * 添加元素（点击事件）
     */
    async addAssetHandleClick(e: React.MouseEvent) {
        if (this.props.onClickItem) {
            this.props.onClickItem(this.props.assetItem);
            return;
        }
        await this.addAsset();
        const { getHistoryRecord, searchValue, closePopup } = this.context;
        const { assetItem, kind = 'more', type = 'others', assetListId } = this.props;
        await getHistoryRecord();
        const param: Record<string, string | number> = { i4: assetItem?.type, s0: assetItem.album_type, s1: assetItem.id, kw: searchValue, s2: searchValue, s3: kind, s4: type }
        if (assetListId) {
            param.i0 = Number(assetListId)
        }
        assetManager.setPv_new(7298, { additional: param });
        emitter.emit('popupClose');
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }
    addAsset() {
        return new Promise<boolean>((resolve) => {
            const { assetItem } = this.props;
            const { isToolPanel } = this.context
            const asset = assetItem;
            Object.assign(asset, {
                rt_isNowAdd: true,
            });
            //1-形状线条；2-元素；3-图片；4-表格；5-3D元素；6-图表；33-视频；34-音频；90-AIGC素材; 100相框；流程图-120
            switch (assetItem.album_type) {
                case '1':
                case '124':
                case '132':
                    emitter.emit('ListAddSVG', asset);
                    break;
                case '2':
                case '138':
                case '146':
                case '147':
                case '148':
                case '149':
                case '150':
                case '151':
                    emitter.emit('ListAddElement', asset);
                    break;
                case '3':
                    emitter.emit('ListAddPic', asset);
                    break;
                case '4':
                    const assetData = {
                        id: asset.id,
                        width: asset.width,
                        height: asset.height,
                        meta: {},
                        attribute: {},
                        transform: {},
                    };
                    emitter.emit('ListAddTable', assetData);
                    break;
                case '5':
                    emitter.emit('ListAddElement', asset);
                    break;
                case '6':
                    emitter.emit('ListAddChart', asset);
                    break;
                case '33':
                    this.addVideoE();
                    break;
                case '34':
                    emitter.emit('ListAddElement', asset);
                    break;
                case '90':
                    emitter.emit('ListAddPic', asset);
                    break;
                case '100':
                    if (isToolPanel) {
                        ImageToolLogic.addFrameToImage(asset)
                        assetManager.setPv_new(7673, { additional: { s0: asset.id } });
                    } else {
                        emitter.emit('ListAddFrame', asset);
                    }
                    break;
                case '122':
                    const data = {
                        id: asset.id,
                        width: 400,
                        height: 5,
                        meta: {},
                        attribute: {},
                        transform: {},
                    };
                    emitter.emit('ListAddLine', data);
                    break;
                default:
                    console.log('no event');
            }
            const res = true;
            resolve(res);
        });
    }

    /**
     * 收藏按钮（点击事件）
     */
    async assetInfoCollectionClickEvent(e: React.MouseEvent) {
        // const { isFav } = this.state;
        e.stopPropagation();
        e?.nativeEvent.stopPropagation();
        const { getFavList } = this.context;
        await this.assetFav();
        await getFavList();
    }

    assetFav() {
        return new Promise<boolean>((resolve) => {
            const { myFavoriteAll } = this.context;
            const { assetItem } = this.props;
            let isFav = false;
            if (assetItem.album_type === '4' || assetItem.album_type === '6') {
                isFav = myFavoriteAll.some(
                    (item: TAsset) => item?.id === assetItem?.id && item?.album_type === assetItem?.album_type,
                );
            } else {
                isFav = myFavoriteAll.some((item: TAsset) => item?.id === assetItem?.id);
            }
            if (isFav) {
                assetManager.setPv_new(7305, { additional: { s0: assetItem.album_type, s1: assetItem.id } });
                switch (assetItem.album_type) {
                    case '33':
                        assetManager.delFavTypeAsset(1, assetItem.id);
                        break;
                    case '4':
                    case '6':
                        const type = assetItem.album_type === '4' ? 1 : 2;
                        assetManager.delFavAssetTable(type, assetItem.id);
                        break;
                    default:
                        assetManager.delFavAsset(assetItem.id);
                }
            } else {
                assetManager.setPv_new(7304, { additional: { s0: assetItem.album_type, s1: assetItem.id } });

                switch (assetItem.album_type) {
                    case '33':
                        assetManager.setFavTypeAsset(1, assetItem.id);

                        break;
                    case '4':
                    case '6':
                        const type = assetItem.album_type === '4' ? 1 : 2;
                        assetManager.setFavAssetTable(type, assetItem.id);

                        break;
                    default:
                        assetManager.setFavAsset(assetItem.id);
                }
            }
            this.setState({
                isFav: !isFav,
            });
            const res = true;
            resolve(res);
        });
    }

    addVideoE = async () => {
        try {
            const { assetItem } = this.props;
            if (assetItem) {
                if (Number(assetItem.duration) > 30000) {
                    emitter.emit('PromptBox', {
                        windowContent: (
                            <div style={{ textAlign: 'center', lineHeight: '70px' }}>
                                当前作品下载时间预计超过5分钟，建议裁剪视频片段，缩短画布时长
                            </div>
                        ),
                    });
                    setTimeout(() => {
                        emitter.emit('PromptBoxClose');
                    }, 5000);
                }
                AssetAddListener.addVideoE(
                    {
                        sample: assetItem.sample,
                        width: String(assetItem.width),
                        height: String(assetItem.height),
                        preview: assetItem.frame_file,
                        id: assetItem.id,
                        total_frame: assetItem.total_frame,
                        duration: assetItem.duration,
                    },
                    false,
                );
                CanvasPaintedLogic.updatePageTimeOnUpdateVideoEAsset();
                const res = await assetManager.setOfficialVideoEHistory(assetItem.id);
            }
        } catch (error) {
            console.error('set official videoe history fail: ', error);
        }
    };

    /**
     * 拖拽添加元素
     * @param element
     * @param type
     * @param e
     */
    async dragAddDownEvent(e: React.MouseEvent) {
        const dragEnd = () => {
            getHistoryRecord();
            window.removeEventListener('mouseup', dragEnd);
        };
        window.addEventListener('mouseup', dragEnd, false);
        await this.dragAsset(e);
        const { getHistoryRecord } = this.context;
    }

    async dragAsset(e: React.MouseEvent) {
        return new Promise<boolean>((resolve) => {
            const { assetItem, assetListId } = this.props;
            let type;
            switch (assetItem.album_type) {
                case '1':
                case '132':
                    type = 'SVG';
                    break;
                case '2':
                case '3':
                case '5':
                case '90':
                case '138':
                case '146':
                case '147':
                case '148':
                case '149':
                case '150':
                case '151':
                    type = 'pic';
                    break;
                case '100':
                    type = 'frame';
                    break;
                case '122':
                    type = 'line';
                    break;
                default:
                    type = '';
            }
            if (type) {
                const { searchValue } = this.context;
                emitter.emit('ListDragAddDown', assetItem, type, e);
                const param: Record<string, string | number> = { i4: assetItem?.type, s0: assetItem.album_type, s1: assetItem.id, kw: searchValue }
                if (assetListId) {
                    param.i0 = Number(assetListId)
                }
                assetManager.setPv_new(7307, {
                    additional: param,
                });
            }
            const res = true;
            resolve(res);
        });
    }

    render(): JSX.Element {
        const { assetItem } = this.props;
        // let { isFav } = this.state;
        const height = Number(this.props?.height) || 80;
        let img = assetItem.sample || `//s.tuguaishou.com${assetItem.preview}`;

        let width = 0;
        let assetImgStyle: CSSProperties = {};
        if (assetItem.album_type === '3' || assetItem.album_type === '33' || assetItem.album_type === '90') {
            width = (height * assetItem.width) / assetItem.height;
            if (assetItem.album_type === '33') {
                img = assetItem.preview;
            }
            assetImgStyle = {
                backgroundImage: "url('" + img + "')",
                backgroundSize: `${width}px auto`,
                width: width + 'px',
                height: height + 'px',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center center',
            };
        } else {
            width = height;
            assetImgStyle = {
                backgroundImage: "url('" + img + "')",
                backgroundSize: 'contain',
                width: width + 'px',
                height: height + 'px',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center center',
            };
        }
        const { myFavoriteAll } = this.context;
        let isFav = false;
        if (assetItem.album_type === '4' || assetItem.album_type === '6') {
            isFav = myFavoriteAll.some(
                (item: TAsset) => item?.id === assetItem?.id && item?.album_type === assetItem?.album_type,
            );
        } else {
            isFav = myFavoriteAll.some((item: TAsset) => item?.id === assetItem?.id);
        }
        return (
            <div
                className="assetItemWarp"
                onClick={this.addAssetHandleClick.bind(this)}
                onMouseDown={this.dragAddDownEvent.bind(this)}
                key={assetItem?.album_type + '_' + assetItem.id}
            >
                <div key={assetItem.id} className="assetItem">
                    <LazyLoad className="lazyImg" width={width} height={height} offset={2000} key={assetItem.id}>
                        {assetItem.album_type === '33' ? (
                            <div className="videoBox">
                                {/* <video style={{height: height}} src={assetItem.sample}></video> */}
                                <div className="assetImg" style={assetImgStyle}></div>
                                <span className="videoTime">{getTime(assetItem.duration)}</span>
                            </div>
                        ) : (
                            <div className="assetImg" style={assetImgStyle}></div>
                        )}
                    </LazyLoad>
                        {assetItem.is_ai === 1 ? <img className='aiTag' src='https://js.tuguaishou.com/editor/image/ai/AI.png' /> : ''}
                    <div className="assetInfoArea">
                        <div
                            className={isFav ? 'assetInfoCollection active' : 'assetInfoCollection'}
                            onClick={this.assetInfoCollectionClickEvent.bind(this)}
                        >
                            <i className="iconfont icon-shoucang2"></i>
                        </div>
                    </div>
                </div>
            </div>
        );
    }
}

AssetItem.contextType = ContextApp;
