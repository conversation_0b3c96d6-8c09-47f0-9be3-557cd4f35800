import React, { PureComponent } from 'react';

import { assetManager } from '@component/AssetManager';
import { ETool } from '@v7_logic/Enum';
import { Search } from './components/Search'
import { SearchPage } from './components/SearchPage'
import { FloorContent } from './components/FloorContent'
import { HomePage } from './components/HomePage'
import { FavAndHis } from './components/FavAndHis'
import { TAsset } from './type'
import ContextApp from './context'
import { emitter } from '@component/Emitter';
import { EventSubscription } from 'fbemitter';
import Collection from './components/Collection';
import { storeAdapter } from '@v7_logic/StoreAdapter.bak';
import { IPSConfig } from '@v7_utils/IPSConfig';

export function DisplaySourceMaterialTool(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.SOURCE_MATERIAL, nav: ETool.SOURCE_MATERIAL};
}
interface propsStruct {
    isActive?: boolean;
    pageInfo?:PageInfo[];
    isToolPanel?: boolean
}

interface PageInfo {
    id?: string,
    title?: string,
}

interface ICollectionRequest { //获取素材合集详情的请求参数type
    page: number
    limit: number
    assetId?: string // 素材id
    collectionId?: string // 合集id
}

interface stateStruct {
    inputValue: string;
    searchValue: string;
    inputPlaceholder: string;
    pageInfo: PageInfo[];
    searchPageShow: boolean;
    myFavoriteAll: TAsset[];
    recentlyUseAll: TAsset[];
    favOrHis: string;
    frameKind:string;
    deepest: boolean; //判断当前是否为最深层
    collection: string; // 合集名称
    collectionAssets: TAsset[]; // 合集的元素列表
    pagination: {
        pageNum: number
        pageSize: number
    }
    collectionId: string // 合集id
    assetId: string // 素材id
    hasMore: boolean // 判断是否存在更多
    isToolPanel:boolean
}

export class SourceMaterialPanel extends PureComponent<propsStruct, stateStruct> {
    updateFavListEmitter: EventSubscription;
    updateHistListEmitter: EventSubscription;
    constructor(props: propsStruct) {
        super(props);
        this.state = {
            inputValue: '',
            inputPlaceholder: '素材',
            searchValue: '',
            pageInfo: this.props.pageInfo ?? [],
            searchPageShow: false,
            myFavoriteAll: [],
            recentlyUseAll: [],
            frameKind:'',
            favOrHis: '',
            deepest: false,
            collection: '',
            collectionAssets: [],
            pagination: {
                pageNum: 1,
                pageSize: 30
            },
            collectionId: '',
            assetId: '',
            hasMore: false,
            isToolPanel:this.props.isToolPanel ?? false
        };
        this.updateFavListEmitter = emitter.addListener('updateFavList', (list?: TAsset[]) => {
            if (list?.length) {
                this.setState({ myFavoriteAll: list });
            } else {
                this.getFavList();
            }
        });
        this.updateHistListEmitter = emitter.addListener('updateHisList', () => {
            this.getHistoryRecord();
        });
    }
    componentWillUnmount(): void {
        this.updateFavListEmitter.remove()
        this.updateHistListEmitter.remove()
    }

    componentDidMount(): void {
        this.getFavListInit();
        this.getHistoryRecordInit();
        // 默认搜索url带过来的
        this.useUrlSearch();
    }

    useUrlSearch() { 
        const urlProps = IPSConfig.getProps(true);
        if (urlProps.searchValue) { 
            const searchValue = decodeURIComponent(urlProps.searchValue)
            this.setState({ inputValue: searchValue })
            
            this.inputSearch(searchValue)
        }
        
    }

    inputChange = (v: string) => {
        this.setState({ inputValue: v })
    }

    inputSearch = (v: string) => {
        const { pageInfo, collection } = this.state;
        this.setState({ searchValue: v })
        if (!v) {
            this.setState({ searchPageShow: false })
        } else {
            if (pageInfo.length === 0 || collection) {
                this.setState({ searchPageShow: true })
                assetManager.setPv_new(7299, { additional: { s0: '外部', kw: v} });
            } else {
                assetManager.setPv_new(7299, { additional: { s0: '内部', kw: v, s1: pageInfo[0].id} });
            }
        }
    }

    changePageInfo = (data?: PageInfo[]) => {
        this.setState({ pageInfo: data })
    }
    backPage = () => {
        const { pageInfo, favOrHis, collection } = this.state;
        if (!favOrHis && !collection) {
            if (pageInfo.length > 1) {
                const newPageInfo = pageInfo.splice(0, pageInfo.length - 1)
                this.setState({ pageInfo: newPageInfo })
            } else {
                this.setState({ pageInfo: [] })
            }
        } else if (favOrHis) {
            this.setState({ favOrHis: '' })
        } else if (collection) {
            this.setState({ collection: '' })

        }
        this.setState({ inputValue: '', searchValue: '', inputPlaceholder: '素材', })
    }

    getFavListInit = () => {
        assetManager.getFavList().then((data) => {
            data.json().then((resultData) => {
                if (resultData.code == 1) {
                    emitter.emit('updateCanvasFavList', resultData.data);
                    this.setState({ myFavoriteAll: resultData.data });
                }
                if (resultData.code == 0) {
                    this.setState({ myFavoriteAll: [] });
                }
            });
        });
    }
    getHistoryRecordInit = () => {
        assetManager.getHistoryRecord().then((data) => {
            data.json().then((resultData) => {
                if (resultData.code == 1) {
                    this.setState({ recentlyUseAll: resultData.data })
                }
            });
        });
    }

    getFavList = () => {
        setTimeout(() => {
            assetManager.getFavList().then((data) => {
                data.json().then((resultData) => {
                    if (resultData.code == 1) {
                        // 更新画布右键有收藏列表
                        emitter.emit('updateCanvasFavList', resultData.data);
                        this.setState({ myFavoriteAll: resultData.data });
                    }
                    if (resultData.code == 0) {
                        this.setState({ myFavoriteAll: [] });
                    }
                });
            });
        }, 500);

    }
    getHistoryRecord = () => {

        setTimeout(() => {
            assetManager.getHistoryRecord().then((data) => {
                data.json().then((resultData) => {
                    if (resultData.code == 1) {
                        this.setState({ recentlyUseAll: resultData.data })
                    }
                });
            });
        }, 500);

    }
    changeFrameKind =( kindId:string ) => {
        this.setState( {frameKind:kindId} )
    }
    changeFavOrHis = (id: string) => {
        console.log(id)
        this.setState({ favOrHis: id });
    }
    changePlaceholder = (name: string) => {
        this.setState({ inputPlaceholder: name });
    }
    changeDeepest = (value: boolean) => {
        this.setState({ deepest: value })
    }
    changeCollection = (id: string, type: 'asset' | 'collection') => {
        const { pagination } = this.state
        const params = {
            page: 1,
            limit: pagination.pageSize,
            assetId: '',
            collectionId: '',
        }
        if (type === 'asset') {
            params.assetId = id
            this.setState({
                assetId: id,
                collectionId: ''
            })
        } else {
            params.collectionId = id
            this.setState({
                assetId: '',
                collectionId: id
            })
        }
        this.getCollection(params)
    }
    getCollection = (params: ICollectionRequest, more?: boolean) => {
        const { collectionAssets } = this.state
        assetManager.getCollectionDetail(params).then((res) => {
            if (res.code == 1) {
                const data = res.data.assetList.map((item: TAsset) => {
                    item.album_type = '2'
                    return item
                })
                const list = more ? collectionAssets.concat(data) : data
                this.setState({ collection: res.data.collectionTitle, searchPageShow: false, collectionAssets: list, hasMore: list.length <= res.data.totalCount })
            }
            if (res.code == 0) {
                this.setState({ collectionAssets: [] });
            }
        });
    }
    loadMore = () => {
        const { pagination, collectionId, assetId } = this.state
        this.setState({
            pagination: {
                pageNum: pagination.pageNum + 1,
                pageSize: pagination.pageSize,
            }
        })
        this.getCollection({
            page: pagination.pageNum + 1,
            limit: pagination.pageSize,
            collectionId,
            assetId,
        }, true)
    }

    render(): JSX.Element {
        const { pageInfo, inputValue, searchValue,frameKind, searchPageShow, myFavoriteAll, recentlyUseAll, favOrHis, inputPlaceholder, collection, deepest, collectionAssets, hasMore , isToolPanel } = this.state;
        // const { isDesigner } = storeAdapter.getStore({
        //     store_name: storeAdapter.store_names.paintOnCanvas,
        // });

        const contextValue = {
            inputValue,
            searchValue,
            pageInfo,
            myFavoriteAll,
            recentlyUseAll,
            favOrHis,
            inputPlaceholder,
            inputChange: this.inputChange,
            inputSearch: this.inputSearch,
            frameKind,
            changeFrameKind:this.changeFrameKind,
            changePageInfo: this.changePageInfo,
            changeFavOrHis: this.changeFavOrHis,
            getFavList: this.getFavList,
            getHistoryRecord: this.getHistoryRecord,
            changePlaceholder: this.changePlaceholder,
            deepest: deepest,
            changeDeepest: this.changeDeepest,
            changeCollection: this.changeCollection,
            searchPageShow,
            collection: collection,
            isToolPanel
        }
        const sourceAssetsList = this.state.favOrHis === '我的收藏' ? myFavoriteAll : collection ? collectionAssets : recentlyUseAll;
        return (
            <ContextApp.Provider value={contextValue}>
                <div className="assetMaterialPanel">
                    {(pageInfo.length > 0 || favOrHis || collection) && !isToolPanel && <div className="back_box" onClick={this.backPage}>
                        <i className="iconfont icon-fanhui1"></i>
                        <span>
                            {favOrHis ? favOrHis : collection ? collection : pageInfo[pageInfo.length - 1].title}
                        </span>
                    </div>}
                    {!collection && !favOrHis && pageInfo.length === 0 && <Search key="search" />}
                    {!favOrHis && searchPageShow && <SearchPage key="searchPage" searchValue={searchValue}></SearchPage>}
                    {!collection && !favOrHis && pageInfo.length === 0 && !searchPageShow && <HomePage key="homePage" />}
                    {!collection && !favOrHis && pageInfo.length > 0 && <FloorContent key={pageInfo.length} />}
                    {!collection && favOrHis && <FavAndHis sourceAssetsList={sourceAssetsList} key={favOrHis} />}
                    {collection && !favOrHis && <Collection sourceAssetsList={collectionAssets} key={collection} loadMore={this.loadMore} hasMore={hasMore}></Collection>}
                </div>
            </ContextApp.Provider>
        );
    }
}
