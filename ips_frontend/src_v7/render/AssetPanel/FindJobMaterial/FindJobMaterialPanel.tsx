import { assetManager } from '@src/userComponentV6.0/AssetManager';
import { ETool } from '@v7_logic/Enum';
import { PanelSearch, Tabs } from '@v7_render/Ui';
import React, { PureComponent } from 'react';
// import { VideoEFavor } from './VideoEFavor';
// import { VideoEHistory } from './VideoEHistory';
import { FindJobMaterialList } from './FindJobMaterialList';
import './scss/FindJobMaterialPanel.scss';
import { unionBy } from 'lodash-es';
import { TFindJobMaterialItem } from './FindJobMaterialItem';
import { klona as cloneDeep } from 'klona';
import { storeDecorator } from '@v7_logic/StoreHOC';
import { CommercialUse } from '@v7_render/CommercialUse';
import { IStoreState } from '@v7_store/redux/store';

export function DisplayFindJobMaterialTool(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.FindJobMaterial, nav: ETool.FindJobMaterial};
}

interface IFindJobMaterialPanelProps {
    user?: {
        userId: string;
    };
}

interface IFindJobMaterialPanelState {
    isfetching: boolean;
    activeKey: 'category' | 'history' | 'favor';
    searchText: string;
    categories: {
        id: string;
        name: string;
        kw: string;
        deleted: '0' | '1';
        sort: string;
        type: string;
        asset: {
            tag_id: string;
            asset_id: string;
        }[];
        asset_info: TFindJobMaterialItem[];
    }[];
    searchResults: TFindJobMaterialItem[];
    isSearching: boolean;
    pageIndex: number;
    pageSize: number;
    searchTitle: string;
    searchId: string;
    isSearchList:boolean; //内容是否是搜索列表 1.搜索 0.推荐
}
 
/**
 * 视频素材列表，仅限支持 png 序列
 */
@storeDecorator((state: IStoreState) => {
    return {
        user: state.onCanvasPainted.user,
    };
})
export class FindJobMaterialPanel extends PureComponent<IFindJobMaterialPanelProps, IFindJobMaterialPanelState> {
    targetWidth = 96;

    state: Readonly<IFindJobMaterialPanelState> = {
        isfetching: false,
        activeKey: 'category',
        searchText: '',
        categories: [],
        searchResults: [],
        isSearching: false,
        pageIndex: 1,
        pageSize: 40,
        searchTitle: undefined,
        searchId: undefined,
        isSearchList:false
    };

    onChangeKey = (key: 'category' | 'history' | 'favor') => {
        this.setState({
            activeKey: key,
        });
    };

    onFormSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        e?.nativeEvent.preventDefault();
    };

    onChangeSearchText = (e: React.FormEvent) => {
        const string = (e.currentTarget as HTMLInputElement).value;
        this.setState({
            searchText: string,
        });
    };

    componentDidMount(): void {
        this.getRecommend();
    }

    getRecommend = async () => {
        try {
            if (this.state.searchText?.length === 0) {
                this.setState({
                    isSearching: false,
                    isfetching: true,
                });

                const res = await (await assetManager.getAssetList("", 1, "image_and_bg", 0, 0, 0,{},"招聘元素")).json();
                // if (res.stat === 1) {
                    this.setState({
                        categories: res,
                    });
                // } else {
                    // console.error('get medical material recommend fail: ', res);
                // }
                this.setState({
                    isfetching: false,
                    isSearchList:false
                });
            }
        } catch (error) {
            console.error('get medical material recommend fail: ', error);
        }
    };

    search = async (e?: React.MouseEvent, pageIndex?: number, id?: string) => {
        if (this.state.isfetching) {
            return;
        }
        this.setState({
            isfetching: true,
        });
        try {
            if (this.state.searchText?.length > 0) {
                this.setState({
                    isSearching: true,
                });
                if (pageIndex === 1) {
                    this.setState({
                        pageIndex: 1,
                        searchResults: [],
                    });
                }
                id = id || this.state.searchId;
                const res = await (
                    await assetManager.getAssetList(
                        this.state.searchText,
                        pageIndex || this.state.pageIndex,
                        !id ? 'image_and_bg' : ['1097'].includes(id) ? 'background' : 'image',
                        0,
                        0,
                        0,
                        { tagId: 0 },
                    )
                ).json();
                if (res.stat === -10 && this.state.pageIndex === 1 ) {
                    this.setState({
                        categories:[]
                    })
                    console.error('search medical material fail: ', res);
                } else {
                    if (res?.length > 0) {
                        this.setState({
                            pageIndex: this.state.pageIndex + 1,
                            isSearchList:true
                        });
                        this.calcList(res);
                    }
                }
            }
        } catch (error) {
            console.error('search medical material fail: ', error);
        }
        this.setState({
            isfetching: false,
        });
    };

    searchMore = () => {
        if (this.state.isSearching) {
            this.search();
        }
    };

    updataFavor = (id: string, fav: 0 | 1) => {
        if (this.state.isSearching) {
            const list = cloneDeep(this.state.searchResults);
            const item = list.find((i) => i.id === id);
            if (item) {
                item.isFav = fav;
                this.setState({
                    searchResults: list,
                });
            }
        } else {
            const categories = cloneDeep(this.state.categories);
            for (const i in categories) {
                for (const v of categories[i].asset_info) {
                    if (v.id === id) {
                        v.isFav = fav;
                        break;
                    }
                }
            }
            this.setState({
                categories,
            });
        }
    };

    searchCategory = (kw: string, name: string, id?: string) => {
        this.setState(
            {
                searchText: kw,
                isSearching: true,
                searchTitle: name,
                searchId: id,
            },
            () => {
                this.search(undefined, 1, id);
            },
        );
    };

    searchCategoryBack = () => {
        this.setState(
            {
                searchText: '',
                isSearching: false,
                searchTitle: undefined,
                searchId: undefined,
            },
            () => {
                this.getRecommend();
            },
        );
    };

    clickSearch = () => {
        if (this.state.searchText?.length > 0) {
            this.search(undefined, 1);
        } else {
            this.getRecommend();
        }
        this.setState({
            searchTitle: undefined,
        });
        this.setState({
            searchTitle: undefined,
            searchId: undefined,
        });
        if (this.state.activeKey !== 'category') {
            const tabDom = document.querySelector<HTMLDivElement>('.find-job-material-panel #tgs-tabs-title-category');
            tabDom?.click();
        }
        assetManager.setPv_new(6361, {
            additional: {
                s0: this.state.searchText,
            },
        });
    };

    calcList = (searchResults: TFindJobMaterialItem[]) => {
        // this.setState({
        //     categories:searchResults as any
        // });
        // if (searchResults.length === 0) {
        //     return;
        // }
        const list = unionBy(this.state.searchResults, searchResults, 'id');
        let [c0, c1, c2] = [0, 0, 0];
        for (const i of list) {
            i.rt_displayHeight = (this.targetWidth / Number.parseInt(i.width)) * Number.parseInt(i.height);

            if (c0 <= c1 && c0 <= c2) {
                c0 += i.rt_displayHeight;
                i.rt_column = 0;
            } else if (c1 <= c0 && c1 <= c2) {
                c1 += i.rt_displayHeight;
                i.rt_column = 1;
            } else {
                c2 += i.rt_displayHeight;
                i.rt_column = 2;
            }
        }
        this.setState({
            searchResults: list,
            categories:list as any
        });
    };

    componentDidUpdate(
        prevProps: Readonly<IFindJobMaterialPanelProps>,
        prevState: Readonly<IFindJobMaterialPanelState>,
    ): void {
        if (prevState.activeKey !== 'category' && this.state.activeKey === 'category') {
            this.getRecommend();
        }
        if (
            this.state.activeKey === 'category' &&
            this.props.user.userId &&
            prevProps.user?.userId !== this.props.user.userId
        ) {
            if (this.state.isSearching) {
                this.search(undefined, 1);
            } else {
                this.getRecommend();
            }
        }
    }

    render() {
        return (
            <div className="find-job-material-panel">
                {/* <CommercialUse text="商用会员所有素材可放心商用" /> */}
                {/* <Tabs
                    isActive={false}
                    defaultActive="category"
                    tabs={[
                        {
                            show: true,
                            key: 'category',
                            title: '视频素材',
                            content: ( */}
                <PanelSearch
                    searchText={this.state.searchText}
                    placeholder="搜索元素"
                    onChangeSearchText={this.onChangeSearchText}
                    clickSearch={this.clickSearch}
                />
                <FindJobMaterialList
                    isfetching={this.state.isfetching}
                    searchTitle={this.state.searchTitle}
                    searchResults={this.state.searchResults}
                    isSearching={this.state.isSearching}
                    categories={this.state.categories}
                    searchCategory={this.searchCategory}
                    searchCategoryBack={this.searchCategoryBack}
                    updataFavor={this.updataFavor}
                    searchMore={this.searchMore}
                    isSearchList={this.state.isSearchList}
                />
                {/* ),
                            setPv: {
                                title: {
                                    click: () => assetManager.setPv_new(5523),
                                },
                            },
                        },
                        {
                            show: true,
                            key: 'history',
                            title: '最近使用',
                            content: <VideoEHistory activeKey={this.state.activeKey} />,
                            setPv: {
                                title: {
                                    click: () => assetManager.setPv_new(5524),
                                },
                            },
                        },
                        {
                            show: true,
                            key: 'favor',
                            title: '收藏',
                            content: <VideoEFavor activeKey={this.state.activeKey} />,
                            setPv: {
                                title: {
                                    click: () => assetManager.setPv_new(5525),
                                },
                            },
                        },
                    ]}
                    search={

                    }
                    onChangeKey={this.onChangeKey}
                ></Tabs> */}
            </div>
        );
    }
}
