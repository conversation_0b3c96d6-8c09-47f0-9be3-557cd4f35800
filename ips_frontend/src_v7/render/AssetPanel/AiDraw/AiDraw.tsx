import React, { Component } from 'react';
import ReactDOM from 'react-dom';
import { ETool } from '@v7_logic/Enum';
import { emitter } from '@component/Emitter';
import { assetManager } from '@src/userComponentV6.0/AssetManager';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { CanvasPaintedLogic } from '@v7_logic/CanvasPaintedLogic';
import { defaultMessage, qualityList, formatPreviewSizeMap, formatFieldMap } from './config';
import styles from './index.module.scss';
import {
    getFormatStyle,
    getAiDrawJob,
    checkAiDraw,
    getAiConsumerPoint,
    closeAiDraw,
    getPresetDes,
    getBigPreviewImg,
    checkAiPreview,
    getUaId,
    updateAssetPic,
} from './api';
import {
    IFormatItem,
    IStyleItem,
    MessageItem,
    TQualityItem,
    propsStruct,
    stateStruct,
    IAiPicItem,
    IDesItem,
    IRecommendItem,
} from './type';
import { storeDecorator } from '@v7_logic/StoreHOC';
import { IStoreState } from '@v7_store/redux/store';
import RechargeAiPopup from '../RechargeAiPopup';
import { TgsTextArea } from '@tgs/general_components/src/components';
import { AiTextRefInstance } from '@tgs/general_components/src/type';
import { aiTextIllegalMsg, replaceAiText } from '@tgs/general_components/src/lib';
import classNames from 'classnames';
import { ClickOutside } from '@v7_render/Ui';
import { STATUS } from '../AiText/config';
import { Tooltip } from 'antd';
import { DownloadPopup } from '@src/userComponentV6.0/InfoBar';
import { UserLogic } from '@v7_logic/UserLogic';
import { AIGeneralLogic } from '@v7_logic/AIToolLogic/AIGeneralLogic';
import { PopupLimitBox } from '../MyPanel/components/UploadLimit';

const isMac = /macintosh|mac os x/i.test(navigator.userAgent);
export function DisplayAiDraw(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.AI_DRAW, nav: ETool.AI_DRAW};
}

const MAX_COUNT = 500;

@storeDecorator((state: IStoreState) => {
    return {
        user: state.onCanvasPainted.user,
        menu: state.appStore.menu,
    };
})
export class AiDraw extends Component<propsStruct, stateStruct> {
    desInputRef: React.RefObject<HTMLTextAreaElement>;
    messageBoxRef: React.RefObject<HTMLDivElement>;
    aiTextRef: React.RefObject<AiTextRefInstance>;
    jobId: string;
    pointReqCount: number;
    highDefinitionJob: { id: string };
    previewImageMap: { file: string; width: number; height: number };
    isAiDrawing: boolean;
    previewTaskId: string | number;
    selectedText: string;
    aiStartTime: number;
    aiFirstTime: number;
    aiEndTime: number;
    pollTimer: NodeJS.Timeout;
    stopPoll: boolean;
    stashFirstStyle: string;
    stashTextPrompt: string;
    constructor(props: propsStruct) {
        super(props);
        this.state = {
            activeFormat: {} as IFormatItem,
            activeStyle: {} as IStyleItem,
            activeQuality: qualityList[0],
            activeToggleTab: 'style',
            recommendList: [],
            sceneList: [],
            menuPos: { left: -9999, top: -9999 },
            popContentType: '',
            popHeight: '440px',
            // 版式选择
            format: 'vertical',
            previewPicInfo: {} as IAiPicItem,
            formatList: [],
            // 风格
            sytleList: [],
            // 预设词
            presetDesWords: [],
            previewStatus: false,
            odlLength: 0,
            wordNum: 0,
            message: [],
            inputText: '',
            meetLimit: false,
            inputFocus: false,
            isOverflowStyle: false,
        };
        // 消费点数接口最多请求3次，如果不行，直接判定失败
        this.pointReqCount = 0;

        this.previewImageMap = {} as { file: string; width: number; height: number };

        this.highDefinitionJob = {} as { id: string };

        this.desInputRef = React.createRef();
        this.messageBoxRef = React.createRef();
        this.aiTextRef = React.createRef();
        this.stashTextPrompt = '';
    }
    // 消费点数
    getPointNum() {
        UserLogic.getUserAIInfo();
    }
    getFormatStyle() {
        getFormatStyle().then((res) => {
            const { style, format, recommend = [], scene = [] } = res.data;
            this.setState({
                sytleList: style,
                formatList: format,
                activeStyle: style[0],
                activeFormat: format[0],
                recommendList: recommend,
                sceneList: scene,
            });
            this.stashFirstStyle = style[0].style;
        });
    }
    getPreset() {
        getPresetDes().then((res) => {
            this.setState({
                presetDesWords: res.data.list || [],
            });
        });
    }
    // 聊天记忆功能
    updateStoreMessage() {
        const aiDrawStore = storeAdapter.getStore({ store_name: 'AiDraw' });
        if (!aiDrawStore?.info?.message?.length) return;
        //更新用户信息
        this.setState({
            message: aiDrawStore.info.message,
        });
    }
    getPaperAssetCount() {
        const { work, pageInfo } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const odlLength = work.pages[pageInfo.pageNow || 0].assets.length;
        this.setState({ odlLength });
    }
    reSetPreset = () => {
        this.getPreset();
        assetManager.setPv_new(7423);
    };
    clacPopHeight() {
        const textFitHeight = this.aiTextRef.current.textareaRef.current.offsetHeight - 34;
        if (document.documentElement.clientHeight - 260 - textFitHeight <= 450) {
            this.setState({
                popHeight: `calc(100vh - 310px - ${textFitHeight}px)`,
            });
        } else {
            this.setState({
                popHeight: '440px',
            });
        }
    }
    componentDidMount(): void {
        // 获取风格版式
        this.getFormatStyle();
        // 获取ai可消费点数
        this.getPointNum();
        // 预设词汇
        this.getPreset();
        // 更新聊天记忆
        this.updateStoreMessage();

        this.bindDocumentClick();

        this.clacPopHeight();
    }
    componentWillUnmount(): void {
        // this.stopGenerator()
        // this.highDefinitionJob = ''
        // 关闭ai绘制
        // console.log('close ai draw')
        // this.closeAiDraw()
    }
    // 自动定位到最新一条信息
    autoPostionLastNews() {
        const messageBox = this.messageBoxRef.current;
        messageBox.scrollTop = messageBox.scrollHeight;
    }
    componentDidUpdate(prevProps: Readonly<propsStruct>, prevState: Readonly<stateStruct>): void {
        // 信息自动定位底部功能
        const { message: prevMessage } = prevState;
        const { message: curMessage } = this.state;
        if (!curMessage) return;
        //  && prevMessage[prevMessage.length - 1].content === curMessage[curMessage.length - 1].content
        if (prevMessage.length !== curMessage.length) {
            this.autoPostionLastNews();
        }
    }
    // 更新ai生成图片store相关信息，方便后期同步
    updateAiDrawInfo(newMessage: MessageItem[]) {
        this.setState({
            message: newMessage,
        });
        storeAdapter.dispatch({
            fun_name: 'updateAiDrawInfo',
            store_name: storeAdapter.store_names.AiDraw,
            params: [
                {
                    type: 'updateAiDrawInfo',
                    params: {
                        info: {
                            message: newMessage,
                        },
                    },
                },
            ],
        });
    }
    // ai点数消费
    // consumePoint(fileCount: number) {
    //     const suplusPoint = this.state.pointNum - fileCount;
    //     this.setState({
    //         pointNum: suplusPoint < 0 ? 0 : suplusPoint,
    //     });
    //     if (suplusPoint <= 0) {
    //         assetManager.setPv_new(7477);
    //     }
    // }
    tipsInfo = (msg: string) => {
        // emitter.emit('Message', '解绑成功')

        emitter.emit('PromptBox', {
            windowContent: (
                <div className="errorMsg">
                    {' '}
                    <i className="iconfont icon-shanchuanniu" />
                    {msg}
                </div>
            ),
        });
        window.setTimeout(() => {
            emitter.emit('PromptBoxClose');
        }, 3000);
    };
    updateAiDrawPic(files: IAiPicItem[]) {
        if (!files || !files.length) return;
        const { message } = this.state;
        const newMessage = JSON.parse(JSON.stringify(message));
        const lastMessage = newMessage[newMessage.length - 1];
        const lastContent = lastMessage.content as IAiPicItem[];
        if (lastContent.filter((item) => !item.preview).length === files.length) return;
        const newContent = lastContent.map((item, index) => {
            if (!files[index]) return item;
            return { ...item, ...files[index] };
        });
        lastMessage.content = newContent;
        this.updateAiDrawInfo(newMessage);
    }
    // 结束ai绘制--包括close fail end
    doneAiDraw(params: { status: string; files?: IAiPicItem[]; message?: string }) {
        const { status, message } = params;
        let { files } = params;
        const failTip = message || '生成失败，请重新生成';
        this.jobId = '';
        // 接口返回即消费点数
        // this.consumePoint(files?.length || 0);

        const newMessage = JSON.parse(JSON.stringify(this.state.message));
        const lastMessage = newMessage[newMessage.length - 1];
        const lastContent = lastMessage.content as IAiPicItem[];
        // 看已经生成的图片数量
        const doneFiles = Array.isArray(lastContent) && lastContent.filter((v) => v.preview);
        const fileCount = doneFiles.length;
        files = files || doneFiles;
        let tip = '';
        if (status === 'END') {
            const hasFile = files.length;
            tip = hasFile ? '以下是根据您的需求生成的图片，您可以点击图片加入画布或重新生成' : failTip;
            const newContent = lastContent
                .map((item, index) => {
                    if (!files[index]) return item;
                    return { ...item, ...files[index] };
                })
                .filter((v) => v.preview);
            lastMessage.content = newContent;
            // // 更新文本对象
            if (!hasFile) {
                newMessage[newMessage.length - 2].content = tip;
                newMessage[newMessage.length - 2].type = 'text';
                newMessage.length = newMessage.length - 1;
                newMessage[newMessage.length - 1].status = status;
            } else {
                const hasYellowPic = newContent.some((v) => v.isdel);
                if (hasYellowPic) {
                    newMessage.push({
                        role: 'ai',
                        type: 'text',
                        content: '部分图片涉嫌违规啦，可以尝试 更换关键词重新生成哦~',
                    });
                } else {
                    newMessage.length = newMessage.length - 2;
                    newMessage.push(lastMessage);
                }
            }

            this.updateAiDrawInfo(newMessage);
        } else if (status === 'FAIL') {
            tip = failTip;
            newMessage[newMessage.length - 1].content = tip;
            newMessage[newMessage.length - 1].type = 'text';
            newMessage[newMessage.length - 1].status = status;

            this.updateAiDrawInfo(newMessage);
        } else if (status === 'CLOSE') {
            tip = fileCount
                ? '已停止生成，共成功生成了1张 图片，您可以点击图片加入画 图或重新生成。'
                : '已停止生成，您可以重新输入 描述内容生成图片。';
            if (!fileCount) {
                newMessage.length = newMessage.length - 1;
                newMessage[newMessage.length - 1].content = tip;
                newMessage[newMessage.length - 1].type = 'text';
            } else {
                lastMessage.content = doneFiles;
                newMessage[newMessage.length - 2].content = tip;
                newMessage[newMessage.length - 2].type = 'text';

                const hasYellowPic = doneFiles.some((v) => v.isdel);
                if (hasYellowPic) {
                    newMessage.push({
                        role: 'ai',
                        type: 'text',
                        content: '部分图片涉嫌违规啦，可以尝试 更换关键词重新生成哦~',
                    });
                }
            }
            this.updateAiDrawInfo(newMessage);
        }
        this.getPointNum();
    }
    // 实时监控ai生成图片状态
    checkAiDraw() {
        setTimeout(() => {
            checkAiDraw(this.jobId)
                .then((res) => {
                    const { status, width, height, files, time, message } = res.data as {
                        files: IAiPicItem[];
                        status: string;
                        width: number;
                        height: number;
                        time: number;
                        message: string;
                    };
                    if (files) {
                        if (files.length === 1) this.aiFirstTime = Date.now() - this.aiStartTime;
                        files.forEach((item) => {
                            item.width = width;
                            item.height = height;
                        });
                        const newMessage = JSON.parse(JSON.stringify(this.state.message));
                        newMessage.push({
                            role: 'ai',
                            type: 'image',
                            content: new Array(1).fill({
                                preview: '',
                                isdel: '0',
                                width: 0,
                                height: 0,
                                format: this.state.activeFormat.format,
                                quality: this.state.activeQuality.type,
                            }),
                        });

                        this.updateAiDrawInfo(newMessage);
                    }
                    switch (status) {
                        case 'RUNNING':
                            this.updateAiDrawPic(files);
                            this.checkAiDraw();
                            break;
                        case 'END':
                        case 'CLOSE':
                        case 'FAIL':
                            this.isAiDrawing = false;
                            this.doneAiDraw({ status, files, message });
                            assetManager.setPv_new(7449, {
                                additional: {
                                    i0: this.aiFirstTime,
                                    i1: time,
                                    i2: Date.now() - this.aiStartTime,
                                    s0: status === 'FAIL' ? 'FAIL' : 'SUCCESS',
                                },
                            });

                            break;
                        default: // WATTING
                            this.checkAiDraw();
                    }
                })
                .catch((err) => {
                    this.isAiDrawing = false;
                    this.doneAiDraw({ status: 'FAIL' });
                });
        }, 2000);
    }
    // 让服务器开始生成图片
    startDraw(promt: string) {
        if (this.stopPoll) {
            return;
        }
        this.aiStartTime = Date.now();
        this.isAiDrawing = true;
        const { activeStyle, activeFormat, activeQuality } = this.state;
        getAiDrawJob({
            promt,
            style: activeStyle.style ?? activeStyle.scene,
            format: activeFormat.format,
            definition: activeQuality.value,
            num: 1,
        })
            .then((res) => {
                if (!res.code) {
                    this.isAiDrawing = false;
                    // this.tipsInfo(res.msg);
                    this.updateAiDrawInfo([
                        ...this.state.message,
                        {
                            role: 'ai',
                            type: 'text',
                            status: 'FAIL',
                            content: res.msg,
                        },
                    ]);
                    return;
                }

                switch (res.code) {
                    // 如果包含违禁词，直接隐藏最后一条消息
                    case 10:
                        this.isAiDrawing = false;
                        this.updateAiDrawInfo([
                            ...this.state.message,
                            {
                                role: 'ai',
                                type: 'text',
                                status: 'FAIL',
                                content: res.msg,
                            },
                        ]);
                        return;
                    case 15:
                        // qps限制，需排队等待
                        // this.tipsInfo('AI绘图功能暂不可用，请稍后再试')

                        // if (this.pollTimer) {
                        //     clearTimeout(this.pollTimer);
                        // }
                        // this.pollTimer = setTimeout(() => {
                        //     this.startDraw(promt);
                        // }, 11000);

                        return;
                    case 1:
                        this.updateAiDrawInfo([
                            ...this.state.message,
                            { role: 'user', type: 'text', content: promt },
                            { role: 'ai', type: 'loadding', content: '' },
                        ]);

                        if (res.data.status === 'END' && res.data.files) {
                            const { width, height, files, status } = res.data as {
                                files: IAiPicItem[];
                                status: string;
                                width: number;
                                height: number;
                                time: number;
                            };
                            if (files) {
                                if (files.length === 1) this.aiFirstTime = Date.now() - this.aiStartTime;
                                files.forEach((item) => {
                                    item.width = width;
                                    item.height = height;
                                });
                            }
                            this.isAiDrawing = false;
                            this.doneAiDraw({ status, files });
                            return;
                        } else {
                            this.jobId = res.data.jobId;
                            this.checkAiDraw();
                            this.pollTimer = null;
                            return;
                        }

                    default:
                        if (this.pollTimer) {
                            this.pollTimer = null;
                        }
                        this.isAiDrawing = false;
                        this.tipsInfo(res.msg);
                        return;
                }
            })
            .catch(() => {
                this.isAiDrawing = false;
                assetManager.setPv_new(7449, {
                    additional: {
                        i0: Date.now() - this.aiStartTime,
                        s0: 'FAIL',
                    },
                });
            });
    }

    isNeedFee() {
        const { pointNum = 0, fixNum = 0, wordsNum = 0 } = this.props.user;
        // console.log('draw menukey -- ', this.props.menu.menuKey)
        if (pointNum <= 0) {
            if (this.props.menu.menuKey === 'aiText') {
                // ai文案
                return wordsNum <= 0;
            } else if (this.props.menu.menuKey == 'aiProducts') {
                // ai商品图
                return fixNum <= 0;
            } else if (this.props.menu.menuKey == 'aiDraw') {
                // ai绘图
                return fixNum <= 0;
            } else {
                return true;
            }
        }
        return false;
    }

    tipText() {
        const { pointNum = 0, fixNum = 0, wordsNum = 0 } = this.props.user;
        let tipText = 'AI点数不够';
        if (pointNum <= 0) {
            if (this.props.menu.menuKey === 'aiText') {
                // ai文案
                tipText = 'AI剩余字数不够';
            } else if (this.props.menu.menuKey == 'aiProducts') {
                // ai商品图
                tipText = 'AI剩余绘图张数不够';
            } else if (this.props.menu.menuKey == 'aiDraw') {
                // ai绘图
                tipText = 'AI剩余绘图张数不够';
            }
        }
        return tipText;
    }

    sendUserInfo = () => {
        // console.log(this.props.menu)
        if (!this.props.user.userName) {
            emitter.emit('LoginPanelShow');
            return;
        }
        const { pointNum = 0, fixNum = 0, wordsNum = 0 } = this.props.user;
        if (this.isNeedFee()) {
            // 免费用户
            if (this.props.user.vip <= 1) {
                const windowInfo = {
                    windowContent: (
                        <DownloadPopup
                            isShowCloseInternal={false}
                            key={new Date().getTime()}
                            className="DownloadPopup"
                            showFlag={3}
                            from={'aiDrawLimit_ue_edit'}
                        />
                    ),
                    popupWidth: 400,
                    popupHeight: 320,
                    style: {
                        borderRadius: '15px 15px 0 0',
                    },
                };
                emitter.emit('popupWindow', windowInfo);
                assetManager.setPv_new(8794);
                return;
            } else {
                this.tipsInfo(this.tipText());
                assetManager.setPv_new(7445);
                this.rechargeAI(pointNum, fixNum, wordsNum);
                return;
            }
        }
        if (this.isAiDrawing) {
            this.tipsInfo('正在生成中，请勿频繁操作');
            return;
        }
        // const textarea = this.desInputRef.current
        // if (!textarea.value.trim()) return;
        // 用户需求文案
        // const userNeed = replaceAiText(this.state.inputText || this.stashTextPrompt);
        // if (!userNeed) return this.tipsInfo(aiTextIllegalMsg);
        const userNeed = this.state.inputText || this.stashTextPrompt;
        // textarea.value = ''
        // textarea.style.height = 'auto';

        this.setState({ wordNum: 0, popContentType: '' });
        this.resetInputText();
        if (!this.state.inputText) {
            this.setState({
                inputText: this.stashTextPrompt,
            });
        }
        this.stopPoll = false;
        this.startDraw(userNeed);

        assetManager.setPv_new(7427);
    };
    showPopUp = (type: string) => {
        if (this.state.popContentType == type) {
            return this.setState({ popContentType: '' });
        }
        this.setState({ popContentType: type });
        let pvId = 8758;
        if (type == 'format') {
            pvId = 8761;
        }
        if (type == 'style' && this.state.activeToggleTab == 'scene') {
            pvId = 8759;
        }
        assetManager.setPv_new(pvId);
    };
    // 选择预设的文案
    presetWordToUserNeed = (word: IDesItem) => {
        // const textarea = this.desInputRef.current
        // textarea.value = word.des
        this.aiTextRef.current.manualSetText(word.des);
        const activeStyle = this.state.sytleList.find((v) => v.style === word.style);
        if (activeStyle) {
            this.setState({ activeStyle });
            this.showPopUp('style');
        }
        // this.listenerDesContent()
        assetManager.setPv_new(7424);
    };
    // 推荐画同款
    drawRecommend = (recommend: IRecommendItem) => {
        this.aiTextRef.current.manualSetText(recommend.prompt);
        const activeStyle = [...this.state.sytleList, ...this.state.sceneList].find(
            (v) => v.style == recommend.style || v.scene == recommend.scene,
        );
        const showScene = !recommend.style && recommend.scene;
        const isOverflowStyle = this.state.sytleList.findIndex((v) => v.style === activeStyle?.style) > 2;
        if (activeStyle) {
            this.setState({ activeStyle, isOverflowStyle });
            this.sortStyle(recommend);
            this.setState({ activeToggleTab: showScene ? 'scene' : 'style' });
        }
        assetManager.setPv_new(8757, {
            additional: {
                s0: recommend.id,
            },
        });
    };
    stopPropagation(e: { stopPropagation: () => void }): void {
        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }
    resetInputText = () => {
        this.setState({
            inputText: '',
            meetLimit: false,
        });
        this.aiTextRef.current.textareaRef.current.style.height = '40px';
    };
    // 文本框高度自适应
    listenerDesContent = () => {
        const textarea = this.desInputRef.current;
        textarea.style.height = 'auto';
        textarea.style.height = `${textarea.scrollHeight}px`;
        if (textarea.value.length > MAX_COUNT) {
            textarea.value = textarea.value.substring(0, MAX_COUNT);
        }
        this.setState({
            wordNum: textarea.value.length,
        });

        this.clacPopHeight();
    };

    // 选择样式风格
    selectStyle = (style: IStyleItem) => {
        if (!style) return;
        this.setState({
            activeStyle: style,
        });
        const isOverflowStyle = this.state.sytleList.findIndex((v) => v.style === style.style) > 2;
        this.setState({
            isOverflowStyle,
        });
        assetManager.setPv_new(7428);
    };
    sortStyle = (style: IStyleItem) => {
        // 不排序了
        return;
        if (!style) return;

        const isStyle = style.style;
        const isScene = !isStyle && style.scene;

        if (isStyle) {
            const index = this.state.sytleList.findIndex((v) => v.style === style.style);
            if (index > 2) {
                const curStyle = this.state.sytleList.splice(index, 1);
                const firstStyleIndex = this.state.sytleList.findIndex((v) => v.style === this.stashFirstStyle);
                const fistStyle = this.state.sytleList.splice(firstStyleIndex, 1);
                this.setState({
                    sytleList: [curStyle[0], fistStyle[0], ...this.state.sytleList.slice(0)],
                });
            }
        } else if (isScene) {
            const index = this.state.sceneList.findIndex((v) => v.scene === style.scene);
            if (index > 2) {
                const curStyle = this.state.sceneList.splice(index, 1);
                this.setState({
                    sceneList: [curStyle[0], ...this.state.sceneList],
                });
            }
        }
    };
    selectFormat = (format: IFormatItem) => {
        this.setState({
            activeFormat: format,
        });
        assetManager.setPv_new(7429);
    };
    selectQuality = (quality: TQualityItem) => {
        this.setState({
            activeQuality: quality,
        });
        assetManager.setPv_new(7430);
    };
    stopGenerator = () => {
        // 处理轮询任务的情况
        if (this.pollTimer) {
            this.stopPoll = true;
            this.pollTimer = null;
            this.isAiDrawing = false;
            const newMessage = [...this.state.message];
            const lastMessage = newMessage.pop();
            lastMessage.content = '已停止生成，您可以重新输入 描述内容生成图片。';
            lastMessage.type = 'text';
            lastMessage.role = 'ai';
            newMessage.push(lastMessage);
            this.updateAiDrawInfo(newMessage as MessageItem[]);
            return;
        }
        if (!this.jobId) return;
        closeAiDraw(this.jobId);
    };
    checkPreviewPic(smallPicId: string | number, uaId?: string) {
        return new Promise<{ file: string; width: number; height: number }>((resolve, reject) => {
            if (!this.highDefinitionJob[smallPicId]) {
                reject();
                return;
            }
            const loopCheck = (id: string | number) => {
                // 如果是预览图  画布上是没有当前图片资源的
                const assetIndex = this.state.previewStatus
                    ? 1
                    : this.findAssetIndex([uaId, 'loading_' + id, 'loading_' + uaId]);
                if (!~assetIndex || !this.highDefinitionJob[id]) {
                    reject();
                    return;
                }
                window.setTimeout(() => {
                    checkAiPreview(this.highDefinitionJob[id])
                        .then((res) => {
                            const { status, file, width, height } = res.data;
                            if (status === 'END') {
                                this.previewImageMap[id] = { file, width, height };
                                resolve({ file, width, height });
                            } else if (status === 'FAIL') {
                                reject();
                            } else if (status !== 'CLOSE') {
                                // 点击取消ai文案生成，不再轮询
                                loopCheck(id);
                            }
                        })
                        .catch((err) => {
                            delete this.highDefinitionJob[id];
                            this.tipsInfo('ai解析内容失败');
                            reject();
                        });
                }, 2000);
            };
            loopCheck(smallPicId);
        });
    }
    // 获取高清图
    getHighDefinitionPic(id: string | number, uaId?: string) {
        if (this.previewImageMap[id]) return Promise.resolve(this.previewImageMap[id]);
        return getBigPreviewImg(id).then((res) => {
            this.highDefinitionJob[id] = res.data.jobId;
            return this.checkPreviewPic(id, uaId);
        });
    }
    // 预览
    previewPic = (e: React.MouseEvent<HTMLSpanElement, MouseEvent>, pic: IAiPicItem) => {
        this.stopPropagation(e);
        this.setState({
            previewStatus: true,
            previewPicInfo: pic,
        });
        this.getHighDefinitionPic(pic.id)
            .then((res) => {
                this.setState({
                    previewPicInfo: {
                        ...this.state.previewPicInfo,
                        preview: res.file,
                    },
                });
            })
            .catch(() => {
                // this.tipsInfo('获取高清图失败～')
            });
    };
    checkUploadNum() {
        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const { uploadLimit, uploadTotal, is_firm_vip } = user;

        if (is_firm_vip !=1 && uploadTotal >= uploadLimit) {
            assetManager.setPv_new(8880, { additional: {} });
            const windowInfo = {
                windowContent: (
                    <PopupLimitBox
                        fromType="aiDraw"
                        total={uploadTotal}
                        limit={uploadLimit}
                        pos="11"
                        isSpecialVip={false}
                    />
                ),
                popupWidth: 'auto',
                popupHeight: 'auto',
                style: {
                    width: '600px',
                    height: '220px',
                    background: '#fff',
                    boxShadow: '0px 4px 14px 0px rgba(31,26,27,0.16)',
                    borderRadius: '12px 12px 12px 12px',
                    left: '0',
                    top: '0',
                    right: '0',
                    bottom: '0',
                    margin: 'auto',
                    padding: '24px',
                },
                popupTitleBarStyle: {
                    width: 0,
                    height: 0,
                    display: 'none',
                },
            };
            emitter.emit('popupWindow', windowInfo);
            assetManager.setPv_new(7431, {
                additional: {
                    s1: '11',
                },
            });
            return false;
        }
        return true;
    }
    // 退出预览
    exitPreview = () => {
        delete this.highDefinitionJob[this.previewTaskId];
        this.previewTaskId = '';
        this.setState({
            previewStatus: false,
        });
    };
    // 点击添加
    async addAssetHandleClick(
        e: React.MouseEvent<HTMLDivElement, MouseEvent>,
        pic: IAiPicItem,
        addType?: 'pic' | 'background',
    ) {
        if (!this.checkUploadNum()) return;
        this.addAsset({ e, pic, addType });
    }

    // 拖拽添加
    async dragAddDownEvent(e: React.MouseEvent<HTMLDivElement, MouseEvent>, pic: IAiPicItem) {
        const dragEnd = () => {
            // 需要保证dom渲染完毕

            if (this.previewTaskId === pic.id) {
                this.previewTaskId = '';
                return;
            }
            
            this.updateDefinition(pic);
       

            window.removeEventListener('mouseup', dragEnd);
        };
        window.addEventListener('mouseup', dragEnd, false);
        this.addAsset({ e, pic, isDrag: true });
    }
    addAsset(params: {
        e: React.MouseEvent<HTMLDivElement, MouseEvent>;
        pic: IAiPicItem;
        isDrag?: boolean;
        addType?: 'pic' | 'background';
    }) {
        if (!this.checkUploadNum()) return;
        const { e, pic, isDrag, addType = 'pic' } = params;
        // 添加图片需要构建这样的数据格式
        const { id, preview: image_url, width, height } = pic;
        const element = {
            id: 'loading_' + id,
            image_url,
            width,
            height,
            source_width: width,
            source_height: height,
        };
        if (isDrag) {
            emitter.emit('ListDragAddDown', element, 'pic', e);
            return;
        }
        if (addType == 'background') {
            emitter.emit('ListAddBackground', element, undefined, {
                unrecordable: true,
            });
        } else {
            emitter.emit('ListAddPic', element);
        }

        if (this.previewTaskId === id) {
            this.previewTaskId = '';
            return;
        }
        AIGeneralLogic.AIToolUsedTypeStash('aiDraw');
        this.updateDefinition(pic);
    }
    // 获取高清大图的时候，因为没有closejobapi，所以需要轮询判断是否有这个资源
    findAssetIndex(id: string | number | string[]) {
        const ids = Array.isArray(id) ? id : [id];
        const { work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const pageAssets = work.pages[pageInfo.pageNow || 0].assets;
        return pageAssets.findIndex((item) => ids.includes(item.attribute.resId));
    }
    // 更新高清大图
    // id: string | number
    updateDefinition(pic: IAiPicItem) {
        const { id, preview, width, height } = pic;
        if (!id) return;
        this.previewTaskId = id;
        getUaId(id).then((res) => {
            const uaId = 'UA-' + res.data['ua-id'];
            const data = { asset_id: uaId, preview, height, width };
            const { pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            const assetIndex = this.findAssetIndex('loading_' + id);

            CanvasPaintedLogic.updateAssetInfo({
                index: assetIndex,
                newPageNow: pageInfo.pageNow,
                info: {
                    attribute: {
                        resId: uaId,
                    },
                },
            });
            UserLogic.getUserUploadLimitInfo();
            // ((id, uaId) => {
            //     if (this.previewImageMap[id]) return;
            //     // 自动保存是操作执行完毕1s后开始保存，所以这里延迟1.1s
            //     // 保证在自动保存后，保底要要保存一张图片到服务器
            //     setTimeout(() => {
            //         const assetIndex = this.findAssetIndex(uaId);
            //         CanvasPaintedLogic.updateAssetInfoForVision({
            //             index: assetIndex,
            //             newPageNow: pageInfo.pageNow,
            //             info: {
            //                 attribute: {
            //                     resId: 'loading_' + uaId,
            //                 },
            //             },
            //         });
            //     }, 1100);
            // })(id, uaId);

            // this.getHighDefinitionPic(id, uaId)
            //     .then((resfile) => {
            //         const { file, width, height } = resfile;
            //         const assetIndex = this.findAssetIndex([uaId, 'loading_' + uaId]);
            //         // 更新资源库
            //         updateAssetPic(id, uaId.split('-')[1]);
            //         CanvasPaintedLogic.updateAssetInfo({
            //             index: assetIndex,
            //             newPageNow: pageInfo.pageNow,
            //             info: {
            //                 attribute: {
            //                     resId: data.asset_id,
            //                     picUrl: file,
            //                     assetHeight: Number(height),
            //                     assetWidth: Number(width),
            //                 },
            //             },
            //         });
            //     })
            //     .catch(() => {
            //         // this.tipsInfo('获取高清图失败～')
            //     });
        });
    }
    renderPreviewBox() {
        const { previewPicInfo } = this.state;
        const { quality, format } = previewPicInfo;
        const { width, height } = formatPreviewSizeMap[format][quality];
        return ReactDOM.createPortal(
            <div className="previewBox">
                <div className="previewContent" onClick={this.exitPreview}>
                    <span>退出预览</span>
                    <div
                        className="pic"
                        style={{
                            width: width + 'px',
                            height: height + 'px',
                        }}
                        onClick={this.stopPropagation}
                    >
                        <img src={previewPicInfo.preview} />
                    </div>
                </div>
            </div>,
            document.body,
        );
    }
    bindDocumentClick() {
        document.addEventListener(
            'click',
            () => {
                this.setState({
                    menuPos: {
                        left: -9999,
                        top: -9999,
                    },
                });
            },
            false,
        );
    }
    copyAiText() {
        const text = this.selectedText;
        const elem = document.createElement('textarea');
        elem.value = text;
        document.body.appendChild(elem);
        elem.select();
        document.execCommand('copy');
        document.body.removeChild(elem);

        this.setState({
            menuPos: {
                left: -9999,
                top: -9999,
            },
        });
    }
    rightCopy = (e: React.MouseEvent<HTMLDivElement, MouseEvent>, msg: string) => {
        e.preventDefault();
        this.stopPropagation(e);
        this.selectedText = msg;
        this.setState({
            menuPos: {
                left: e.clientX,
                top: e.clientY - 54,
            },
        });
    };

    rechargeAI(pointNum: number, fixNum: number, wordsNum: number) {
        const windowInfo = {
            windowContent: (
                <RechargeAiPopup
                    from="aiDrawLimit_ue_edit"
                    pointNum={pointNum}
                    fixNum={fixNum}
                    wordsNum={wordsNum}
                    username={this.props.user.userName}
                    avatar={this.props.user.avatar}
                    refresh={this.getPointNum.bind(this)}
                />
            ),
            popupWidth: 'auto',
            popupHeight: 'auto',
            style: {
                width: '790px',
                height: '420px',
                background: 'transparent',
                borderRadius: '5px 5px 5px 5px',
                left: '0',
                top: '0',
                right: '0',
                bottom: '0',
                margin: 'auto',
                padding: '0',
            },
            popupTitleBarStyle: {
                width: 0,
                height: 0,
                display: 'none',
            },
        };
        emitter.emit('popupWindow', windowInfo);
    }

    addPoints(pointNum: number, fixNum: number, wordsNum: number) {
        assetManager.setPv_new(7444);
        if (this.props.user.userName) {
            this.rechargeAI(pointNum, fixNum, wordsNum);
        } else {
            emitter.emit('LoginPanelShow');
        }
    }

    renderRest() {
        const { pointNum = 0, fixNum = 0, wordsNum = 0 } = this.props.user;
        let rest_text = '剩余点数：';
        let rest_num: number = pointNum;
        if (pointNum <= 0) {
            rest_text = '剩余绘图张数：';
            rest_num = fixNum;
        }
        return (
            <>
                {rest_text}
                <b>{rest_num}</b>
            </>
        );
    }

    renderBtnText() {
        const { pointNum = 0 } = this.props.user;
        let rest_text = '增加点数';
        if (pointNum <= 0) {
            rest_text = '增加绘图张数';
        }
        return <>{rest_text}</>;
    }
    getPopupContent(type: string) {
        if (!type) return '';
        const { sytleList, activeStyle, formatList, activeFormat, activeQuality, activeToggleTab, sceneList } =
            this.state;
        let tipText = '风格';
        let showList = [];
        switch (activeToggleTab) {
            case 'style':
                tipText = '风格';
                showList = sytleList;
                break;
            case 'scene':
                tipText = '场景';
                showList = sceneList;
                break;
        }
        switch (type) {
            case 'style':
                return (
                    <div className={styles['style-popup']}>
                        <h3>
                            <span className={styles['title']}>选择{tipText}</span>
                            <span
                                className={styles['close']}
                                onClick={() => {
                                    this.showPopUp('');
                                    this.sortStyle(activeStyle);
                                    assetManager.setPv_new(7426);
                                }}
                            >
                                <i className="iconfont icon-a-guanbi31"></i>
                            </span>
                        </h3>
                        <div className={styles['ideaGroup']}>
                            {showList.map((style) => {
                                return (
                                    <div
                                        className={classNames(
                                            styles['style-item'],
                                            styles[activeStyle.id === style.id ? 'active' : ''],
                                        )}
                                        key={style.id}
                                        onClick={() => {
                                            this.selectStyle(style);
                                        }}
                                    >
                                        <img src={style.preview} className={styles['style-img']} />
                                        <div className={styles['style-name']}>{style.name}</div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                );
            case 'format':
                return (
                    <div className={styles['format-popup']}>
                        <div className={classNames(styles['formatItem'], styles['radio'])}>
                            <div className={styles['format-title']}>比例</div>
                            <div className={styles['radio-wrap']}>
                                {formatList.map((item) => {
                                    return (
                                        <div
                                            key={item.id}
                                            className={classNames(
                                                styles['radio-item'],
                                                styles['radio' + item.id],
                                                styles[activeFormat.id === item.id ? 'active' : ''],
                                            )}
                                            onClick={() => {
                                                this.selectFormat(item);
                                            }}
                                        >
                                            <b>
                                                <i></i>
                                            </b>
                                            <span>{item.name}</span>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>
                        <div className={classNames(styles['formatItem'], styles['quality'])}>
                            <div className={styles['format-title']}>画质</div>
                            <div className={styles['quality-wrap']}>
                                {qualityList.map((item) => {
                                    return (
                                        <div
                                            key={item.name}
                                            className={classNames(
                                                styles['quality-item'],
                                                styles[activeQuality.value === item.value ? 'active' : ''],
                                            )}
                                            onClick={() => {
                                                this.selectQuality(item);
                                            }}
                                        >
                                            {item.name}
                                        </div>
                                    );
                                })}
                            </div>
                        </div>
                    </div>
                );
        }
    }
    render(): JSX.Element {
        const {
            message,
            popContentType,
            sytleList,
            recommendList,
            sceneList,
            activeStyle,
            previewStatus,
            presetDesWords,
            menuPos,
            activeToggleTab,
            inputFocus,
        } = this.state;
        const { pointNum = 0, fixNum = 0, wordsNum = 0 } = this.props.user;
        return (
            <div className={styles['aiDraw']}>
                <div className={styles['copyMenu']} style={{ left: menuPos.left + 'px', top: menuPos.top + 'px' }}>
                    <p
                        onClick={() => {
                            this.copyAiText();
                        }}
                    >
                        <span>复制</span> <span>{isMac ? 'Command' : 'Ctrl'}+c</span>
                    </p>
                </div>
                {previewStatus ? this.renderPreviewBox() : null}
                <div className={styles['aiDraw-title']}>
                    <img
                        src="https://js.tuguaishou.com/editor/image/ai/aiLogoIcon.png"
                        className={styles['logo-pic']}
                    />
                    <img
                        src="https://js.tuguaishou.com/editor/image/ai/aiDrawTitle.png"
                        className={styles['title-pic']}
                    />
                </div>

                <div className={styles['drawContent']}>
                    {(message && message.length) > 0 ? (
                        <div className={styles['messageCutBox']} ref={this.messageBoxRef}>
                            <div className={styles['messageContent']}>
                                {message.map((msg, index) => {
                                    // ai首条消息带icon
                                    const isFirst = !index;
                                    // 预设选项
                                    const isPresets = index === 1;
                                    // ai生成中状态
                                    const isLoadding = msg.type === 'loadding';
                                    // ai生成的图片状态
                                    const isImage = msg.type === 'image';
                                    const formatClass = formatFieldMap[(msg.content[0] as IAiPicItem)?.format ?? '4:3'];
                                    return (
                                        <div
                                            key={index}
                                            className={classNames(
                                                styles['messageItem'],
                                                styles[isImage ? 'imgContainer' : ''],
                                                styles[isFirst ? 'first' : ''],
                                                styles[msg.role],
                                                styles[msg.status],
                                            )}
                                        >
                                            {isImage ? (
                                                <ul className={classNames(styles['aiPicList'], styles[formatClass])}>
                                                    {(msg.content as IAiPicItem[]).map((pic, i) => {
                                                        const { preview: url, isdel } = pic;
                                                        return (
                                                            <>
                                                                {url && !isdel ? (
                                                                    <div className={styles['aiPic']}>
                                                                        {/* <span className='previewBtn' onClick={e => {
                                                                            this.stopPropagation(e)
                                                                            this.previewPic(e, pic)
                                                                        }} onMouseDown={this.stopPropagation}>
                                                                            <i className='iconfont icon-fangda2'></i>
                                                                            <strong>放大预览</strong>
                                                                        </span> */}
                                                                        <div className={styles['preview-box']}>
                                                                            <img
                                                                                src={url}
                                                                                onMouseDown={(e) =>
                                                                                    this.dragAddDownEvent(e, pic)
                                                                                }
                                                                                onClick={(e) =>
                                                                                    this.addAssetHandleClick(e, pic)
                                                                                }
                                                                            />
                                                                            <div className={styles['action-wrap']}>
                                                                                <div
                                                                                    className={classNames(
                                                                                        styles['add-background'],
                                                                                        styles['action-item'],
                                                                                    )}
                                                                                    onClick={(e) => {
                                                                                        this.addAssetHandleClick(
                                                                                            e,
                                                                                            pic,
                                                                                            'background',
                                                                                        );
                                                                                        assetManager.setPv_new(8763);
                                                                                    }}
                                                                                >
                                                                                    设置为背景
                                                                                </div>
                                                                                <div
                                                                                    className={classNames(
                                                                                        styles['add-pic'],
                                                                                        styles['action-item'],
                                                                                    )}
                                                                                    onClick={(e) => {
                                                                                        this.addAssetHandleClick(
                                                                                            e,
                                                                                            pic,
                                                                                        );
                                                                                        assetManager.setPv_new(8764);
                                                                                    }}
                                                                                >
                                                                                    添加到画布
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <Tooltip trigger={'hover'} title={'重新生成'}>
                                                                            <div
                                                                                className={styles['reStart-btn']}
                                                                                onClick={(e) => {
                                                                                    e.stopPropagation();
                                                                                    e.preventDefault();

                                                                                    this.sendUserInfo();
                                                                                    assetManager.setPv_new(8762);
                                                                                }}
                                                                                onMouseDown={(e) => {
                                                                                    e.stopPropagation();
                                                                                    e.preventDefault();
                                                                                }}
                                                                            >
                                                                                <i className="iconfont icon-zhongxinshengcheng1"></i>
                                                                            </div>
                                                                        </Tooltip>
                                                                    </div>
                                                                ) : isdel === 1 ? (
                                                                    <div className={styles['deleted-pic']}>
                                                                        <i className="iconfont icon-tupiantishi"></i>
                                                                    </div>
                                                                ) : (
                                                                    <div className={styles['loading-pic']}>
                                                                        <i className="iconfont icon-AIhuitu2-01"></i>
                                                                    </div>
                                                                )}
                                                            </>
                                                        );
                                                    })}
                                                </ul>
                                            ) : (
                                                <div
                                                    className={styles['message']}
                                                    onContextMenu={(e) => {
                                                        if (msg.role !== 'user') return;
                                                        this.rightCopy(e, msg.content as string);
                                                    }}
                                                >
                                                    {isLoadding ? (
                                                        <div className={styles['loaddingdot']}>
                                                            <span className={styles['loading-dots']}>
                                                                <span></span>
                                                                <span></span>
                                                                <span></span>
                                                            </span>
                                                            <span>{msg.content}</span>
                                                        </div>
                                                    ) : (
                                                        msg.content
                                                    )}

                                                    {/* {isPresets ? (
                                                        <>
                                                            <i
                                                                className="refresh iconfont icon-zhongxin"
                                                                onClick={this.reSetPreset}
                                                            ></i>
                                                            <p className={styles['example']}>
                                                                {presetDesWords.map((word) => {
                                                                    return (
                                                                        <span
                                                                            key={word.title}
                                                                            onClick={() =>
                                                                                this.presetWordToUserNeed(word)
                                                                            }
                                                                        >
                                                                            {word.title}
                                                                        </span>
                                                                    );
                                                                })}
                                                            </p>
                                                        </>
                                                    ) : null} */}
                                                </div>
                                            )}
                                            {/* {isLoadding ? (
                                                <span className={styles['stopBtn']} onClick={this.stopGenerator}>
                                                    <i className="iconfont icon-tingzhi"></i>停止生成
                                                </span>
                                            ) : null} */}
                                        </div>
                                    );
                                })}
                            </div>
                        </div>
                    ) : (
                        <div className={styles['recommend-wrap']}>
                            <div className={styles['recommend-tip']}>
                                <i className="iconfont icon-AIshengcheng"></i>
                                <span>快速生成爆款素材</span>
                            </div>
                            <div className={styles['recommend-content']}>
                                {recommendList && recommendList.length > 0
                                    ? recommendList.map((item) => {
                                          return (
                                              <div
                                                  className={styles['recommend-item']}
                                                  key={item.id}
                                                  onClick={() => {
                                                      this.drawRecommend(item);
                                                  }}
                                              >
                                                  <img src={item.preview} alt="" className={styles['item-preview']} />
                                                  <div className={styles['text-tip']}>{item.name}</div>
                                                  <div className={styles['hover-tip']}>立即生成</div>
                                              </div>
                                          );
                                      })
                                    : ''}
                            </div>
                        </div>
                    )}
                </div>
                <div className={styles['style-toggle-wrap']}>
                    <div className={styles['title-tab']}>
                        <div
                            className={classNames(
                                styles['tab-item'],
                                activeToggleTab == 'style' ? styles['active'] : '',
                            )}
                            onClick={() => {
                                this.setState({ activeToggleTab: 'style' });
                                assetManager.setPv_new(8760);
                            }}
                        >
                            风格
                        </div>
                    </div>
                    <div className={styles['style-wrap']}>
                        {sytleList.slice(0, 3).map((style) => {
                            return (
                                <div
                                    className={classNames(
                                        styles['style-item'],
                                        styles[activeStyle.id === style.id ? 'active' : ''],
                                    )}
                                    key={style.id}
                                    onClick={() => {
                                        this.selectStyle(style);
                                    }}
                                >
                                    <img src={style.preview} className={styles['item-preview']} />
                                    <div className={styles['style-name']}>{style.name}</div>
                                </div>
                            );
                        })}
                        <div
                            className={classNames(
                                styles['style-item'],
                                styles['show-more'],
                                styles[this.state.isOverflowStyle ? 'active' : ''],
                            )}
                            key={'show-more'}
                            onClick={() => {
                                this.showPopUp('style');
                            }}
                        >
                            {this.state.isOverflowStyle && (
                                <>
                                    <img src={activeStyle.preview} className={styles['item-preview']} />
                                </>
                            )}
                            <div className={styles['tip-text']}>更多</div>
                            {/* <span className={styles['tip-text']}>
                                +{sytleList.length - 3}
                            </span>
                            <img
                                src={`https://s.tuguaishou.com/editor/image/ai/${activeToggleTab == 'style' ? 'aiStyleShowMore.png' : 'aiSceneShowMore.png'}`}
                                className={styles['item-preview']}
                            /> */}
                        </div>
                    </div>
                </div>
                <div className={styles['footer']}>
                    <div
                        className={classNames(
                            styles['inputArea'],
                            styles[this.state.meetLimit ? 'meetLimit' : ''],
                            styles[inputFocus ? 'active' : ''],
                        )}
                    >
                        {/* <div className={styles['functions']}>
                                <div
                                    className={classNames(
                                        styles['function'],
                                        styles['idea'],
                                        styles[ideaPopStatus ? 'active' : ''],
                                    )}
                                    onClick={this.stopPropagation}
                                >
                                    <span className={styles['btn']} onClick={() => {
                                        this.toggleIdeaContent()
                                        assetManager.setPv_new(7425)
                                    }}><i className='bulb iconfont icon-chuangyi'></i>创意选项 <i className='arrow iconfont icon-xiangxia1'></i></span>
                                </div>
                            </div> */}
                        {/* <textarea ref={this.desInputRef} onFocus={() => this.toggleIdeaContent(true)} onPaste={this.stopPropagation} onKeyDown={this.stopPropagation} onInput={this.listenerDesContent} placeholder="描述您想要的画面内容..."></textarea> */}
                        <TgsTextArea.AiText
                            value={this.state.inputText}
                            setText={(text) => {
                                const textArea = this.aiTextRef.current.textareaRef;
                                textArea.current.style.height = `${textArea.current.scrollHeight}px`;
                                this.setState({ inputText: text });
                                this.stashTextPrompt = text;
                            }}
                            ref={this.aiTextRef}
                            maxLength={800}
                            showCounter={false}
                            // onFocus={() => this.toggleIdeaContent(true)}
                            onPaste={this.stopPropagation}
                            onKeyDown={(e) => {
                                if (e.keyCode === 13) {
                                    this.sendUserInfo();
                                    e.preventDefault();
                                }
                                this.stopPropagation(e);
                            }}
                            onMeetLimit={(meetLimit) => {
                                this.setState({ meetLimit });
                            }}
                            placeholder="描述你想要生成的画面，角色，风格，场景...."
                            className={classNames(styles['input-wrap'])}
                            tipStyle={{
                                bottom: '-22px',
                                right: '44px',
                            }}
                            onFocus={() => {
                                this.setState({
                                    inputFocus: true,
                                });
                            }}
                            onBlur={() => {
                                this.setState({
                                    inputFocus: false,
                                });
                            }}
                        ></TgsTextArea.AiText>
                        {/* <div className={styles['style-tip']}>
                            {activeStyle.name}
                        </div> */}
                        <div className={styles['tool-wrap']}>
                            <div
                                className={styles['image-quality']}
                                onClick={() => {
                                    this.showPopUp('format');
                                }}
                            >
                                <i className="iconfont icon-shezhixuanxiang-01"></i>
                                <span>画质比例</span>
                            </div>
                            <div className={styles['btnArea']} onClick={this.sendUserInfo}>
                                <i className="iconfont icon-AIshengcheng"></i>
                                <span>
                                    生成
                                    <strong className={styles['poptip']}>
                                        {pointNum > 0 ? '每次消耗1点' : '每次消耗对应绘图张数'}
                                    </strong>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <p className={styles['otherInfo']}>
                    <span className={styles['ai_draw_info']}>
                        <i className="iconfont icon-tishi1"></i>免责声明
                        <strong>
                            图怪兽AI服务处于前沿探索阶段，功能采用了
                            <b style={{ color: '#fff' }}>
                                <i>豆包AI作画API</i>
                            </b>
                            进行实现。用户在使用本产品时，请注意以下事项：
                            您应当合法、合规地使用本服务，并承担由此产生的所有责任。
                            本服务生成的作品仅供个人学习、交流使用，不可用于商业用途。图怪兽对您的使用不做任何形式的保证且不承担任何责任。
                            您应当知悉，AI生成的内容均由人工智能模型生成，其中通过
                            <b style={{ color: '#fff' }}>
                                <i>豆包AI作画API</i>
                            </b>
                            获得的数据、信息或内容的准确性、完整性和及时性均由百度提供和控制。我们不对这些信息的任何错误、遗漏、或未及时更新承担责任。
                            虽然图怪兽对AI生成内容无法做任何保证，但生成内容服务会消耗算力，因此仍会消耗点数。
                        </strong>
                    </span>
                    <span className={styles['ai_draw_coin_box']}>
                        <span
                            className={styles['ai_draw_add_coin']}
                            onClick={this.addPoints.bind(this, pointNum, fixNum, wordsNum)}
                        >
                            {this.renderBtnText()}
                        </span>
                        <span className={styles['ai_draw_info']}>{this.renderRest()}</span>
                    </span>
                </p>
                <ClickOutside
                    onClickOutside={() => {
                        if (!this.state.popContentType) return;
                        this.setState({
                            popContentType: '',
                        });
                        this.sortStyle(activeStyle);
                    }}
                >
                    <div className={classNames(styles['popup-container'], popContentType ? styles['showPop'] : '')}>
                        {this.getPopupContent(popContentType)}
                    </div>
                </ClickOutside>
            </div>
        );
    }
}
