import React, { Component, Ref } from 'react';
import { ETool } from '@v7_logic/Enum';
import { emitter } from '@component/Emitter';
import { EventSubscription } from 'fbemitter';
import { assetManager } from '@src/userComponentV6.0/AssetManager';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { UserUploadBox } from '@src/userComponentV6.0/toolV6.0/UserUploadArea';
import { getAiDrawJob, checkAiDraw, getAiConsumerPoint, closeAiDraw, uploadFile, getCououtResult, getProductScene } from './api'
import { IFormatItem, IStyleItem, IMessageItem, TQualityItem, propsStruct, stateStruct, IAiPicItem, IDesItem, ICateItem, ISceneItem, TProduceItem } from './type';
import { storeDecorator } from '@v7_logic/StoreHOC';
import { IStoreState } from '@v7_store/redux/store';
import RechargeAiPopup from '../RechargeAiPopup';
import { UserInput, PointRule } from './AiComponents'
import { MessageList } from './AiComponents/MessageList';
import { MSG_TYPE } from './constants';
import { ChangePosition } from './AiComponents/ChangePostion';
import { defaultMessage, qualityList, produceNumber, textBtnObj1, textBtnObj2 } from './config'
import './index.scss';
import { PopupLimitBox } from '../MyPanel/components/UploadLimit';

export function DisplayAiProducts(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.AI_PRODUCTS, nav: ETool.AI_PRODUCTS};
}
export interface ICutoutProduct { pic: string; url: string, pos: string; width: number; height: number, scale: number; left: number; top: number; }
@storeDecorator((state: IStoreState) => {
    return {
        user: state.onCanvasPainted.user,
        menu: state.appStore.menu
    };
})
export class AiProducts extends Component<propsStruct, stateStruct> {
    messageBoxRef: React.RefObject<HTMLDivElement>;
    pointRuleRef: React.RefObject<HTMLDivElement>;
    userInputRef: React.RefObject<HTMLDivElement>;
    changePositionEvent: EventSubscription;
    jobId: string;
    pointReqCount: number;
    isAiDrawing: boolean;
    aiStartTime: number;
    aiFirstTime: number;
    aiEndTime: number;
    pollTimer: NodeJS.Timeout | null;
    stopPoll: boolean;
    // 最新的抠完图的商品图--带水印
    cutoutProduct: ICutoutProduct[] = [];
    constructor(props: propsStruct) {
        super(props);
        this.state = {
            activeFormat: {} as IFormatItem,
            activeScene: {} as ISceneItem,
            activeQuality: qualityList[0],
            activeProducer: produceNumber[2] as { value: number; name: string },
            // 商品类型分类
            cateTree: [] as ICateItem[],
            ideaPopStatus: false,
            pointNum: 0,
            fixNum: 0,
            wordsNum: 0,
            // 版式选择
            format: 'vertical',
            formatList: [],
            // 预设词
            presetDesWords: [],
            // 抠完的商品图
            message: [
                ...defaultMessage
            ]
        }
        // 消费点数接口最多请求3次，如果不行，直接判定失败
        this.pointReqCount = 0;


        this.messageBoxRef = React.createRef();

        this.pointRuleRef = React.createRef();
        this.userInputRef = React.createRef();

        this.changePositionEvent = emitter.addListener('showChangePositionComp', (url: string) => {
            const changeProduct = this.cutoutProduct.find(item => item.url === url)
            this.showPositionComp(changeProduct)
        })
    }
    // 消费点数
    getPointNum() {
        this.pointReqCount++;
        if (this.pointReqCount >= 3) return
        getAiConsumerPoint().then(res => {
            this.pointReqCount = 0;
            this.setState({
                pointNum: res?.data?.num,
                fixNum: res?.data?.fix_num,
                wordsNum: res?.data?.word_num
            })
        }).catch(e => {
            this.getPointNum()
        })
    }
    getFormatStyle() {
        getProductScene().then(res => {
            const { cate, example, format } = res.data
            this.setState({
                cateTree: cate,
                presetDesWords: example,
                formatList: format,
                activeFormat: format[2],
            })

        })
    }
    getPreset() {
        // getPresetDes().then(res => {
        //     this.setState({
        //         presetDesWords: res.data.list || []
        //     })
        // })
    }
   
    // 聊天记忆功能
    updateStoreMessage() {
        const aiDrawStore = storeAdapter.getStore({ store_name: 'AiProduct' })
        if (!aiDrawStore?.info?.message?.length) return
        //更新用户信息
        this.setState({
            message: aiDrawStore.info.message
        })
        this.cutoutProduct = aiDrawStore.info.cutoutProduct
    }
    reSetPreset = () => {
        // this.getPreset()
        // assetManager.setPv_new(7423);
    }
    componentDidMount(): void {
        // 获取风格版式
        this.getFormatStyle()
        // 获取ai可消费点数
        this.getPointNum()
        // 预设词汇
        // this.getPreset()
        // 更新聊天记忆
        this.updateStoreMessage()
    }
    componentWillUnmount(): void {
        this.changePositionEvent.remove()
    }
    // 自动定位到最新一条信息
    autoPostionLastNews() {
        const messageBox = this.messageBoxRef.current
        messageBox.scrollTop = messageBox.scrollHeight
    }
    componentDidUpdate(prevProps: Readonly<propsStruct>, prevState: Readonly<stateStruct>): void {
        // 信息自动定位底部功能
        const { message: prevMessage } = prevState
        const { message: curMessage } = this.state
        //  && prevMessage[prevMessage.length - 1].content === curMessage[curMessage.length - 1].content
        if (prevMessage.length !== curMessage.length) {
            setTimeout(() => this.autoPostionLastNews(), 0)
        }
    }
    // 更新ai生成图片store相关信息，方便后期同步
    updateAiDrawInfo(newMessage: IMessageItem[]) {
        this.setState({
            message: newMessage
        })
        storeAdapter.dispatch({
            fun_name: 'updateAiProductInfo',
            store_name: storeAdapter.store_names.AiDraw,
            params: [{
                type: 'updateAiProductInfo',
                params: {
                    info: {
                        message: newMessage,
                        cutoutProduct: this.cutoutProduct
                    }
                }
            }]
        })
    }
    // ai点数消费
    consumePoint(fileCount: number) {
        const suplusPoint = this.state.pointNum - fileCount
        this.setState({
            pointNum: suplusPoint < 0 ? 0 : suplusPoint
        })
        if (suplusPoint <= 0) {
            assetManager.setPv_new(7530);
        }
    }
    tipsInfo = (msg: string) => {
        emitter.emit('PromptBox', {
            windowContent: <div className='errorMsg'> <i className="iconfont icon-shanchuanniu" />{msg}</div>,
        });
        window.setTimeout(() => {
            emitter.emit('PromptBoxClose');
        }, 3000)
    }
    updateAiDrawPic(files: IAiPicItem[]) {
        if (!files || !files.length) return
        const { message } = this.state
        const newMessage = JSON.parse(JSON.stringify(message))
        const lastMessage = newMessage[newMessage.length - 1]
        const lastContent = lastMessage.content as IAiPicItem[]
        if (lastContent.filter(item => !item.preview).length === files.length) return;
        const newContent = lastContent.map((item, index) => {
            if (!files[index]) return item;
            return { ...item, ...files[index] }
        })
        lastMessage.content = newContent
        this.updateAiDrawInfo(newMessage)
    }
    // 结束ai绘制--包括close fail end
    doneAiDraw(status: string, files?: IAiPicItem[]) {
        this.jobId = ''
        // 接口返回即消费点数
        this.consumePoint(files?.length || 0)

        const newMessage = JSON.parse(JSON.stringify(this.state.message)).filter((v: { type: string; }) => ![MSG_TYPE.TYPE_SELECTOR, MSG_TYPE.CUTOUTED].includes(v.type))
        const lastMessage = newMessage[newMessage.length - 1]
        const lastContent = lastMessage.content as IAiPicItem[]
        // 看已经生成的图片数量
        const doneFiles = lastContent.filter(v => v.preview)
        const fileCount = doneFiles.length
        files = files || doneFiles
        let tip = ''
        if (status === 'END') {
            const hasFile = files.length
            tip = hasFile ? '以下是根据您的需求合成的图片，您可以点击图片加入画布' : '生成失败，请重新生成'
            const newContent = lastContent.map((item, index) => {
                if (!files[index]) return item;
                return { ...item, ...files[index] }
            }).filter(v => v.preview)
            lastMessage.content = newContent
            // 更新文本对象
            newMessage[newMessage.length - 2].content = tip
            newMessage[newMessage.length - 2].type = 'text'
            if (!hasFile) {
                newMessage.length = newMessage.length - 1
            } else {
                const hasYellowPic = newContent.some(v => v.isdel)
                if (hasYellowPic) {
                    newMessage.push({ role: 'ai', type: 'text', content: '您上传的图片涉嫌违规，依据 国家相关法律法规，禁止上传 包含色情、违法、侵权等性质 内容，违规内容将会删除处理~' })
                }
            }
            newMessage.push(textBtnObj1, textBtnObj2)

            this.updateAiDrawInfo(newMessage)
        } else if (status === 'FAIL') {
            tip = '生成失败，请重新生成'
            newMessage.length = newMessage.length - 1
            newMessage[newMessage.length - 1].content = tip
            newMessage[newMessage.length - 1].type = 'text'
            this.updateAiDrawInfo(newMessage)
        } else if (status === 'CLOSE') {
            tip = fileCount ? '已停止生成，共成功生成了1张 图片，您可以点击图片加入画 图或重新生成。' : '已停止生成，您可以重新输入 描述内容生成图片。'
            if (!fileCount) {
                newMessage.length = newMessage.length - 1
                newMessage[newMessage.length - 1].content = tip
                newMessage[newMessage.length - 1].type = 'text'
            } else {
                lastMessage.content = doneFiles
                newMessage[newMessage.length - 2].content = tip
                newMessage[newMessage.length - 2].type = 'text'

                const hasYellowPic = doneFiles.some(v => v.isdel)
                if (hasYellowPic) {
                    newMessage.push({ role: 'ai', type: 'text', content: '您上传的图片涉嫌违规，依据 国家相关法律法规，禁止上传 包含色情、违法、侵权等性质 内容，违规内容将会删除处理' })
                }

            }
            this.updateAiDrawInfo(newMessage)
        }
        this.getPointNum();
    }
    // 实时监控ai生成图片状态
    checkAiDraw() {
        setTimeout(() => {
            checkAiDraw(this.jobId).then(res => {
                const { status, width, height, files, time } = res.data as { files: IAiPicItem[], status: string; width: number; height: number; time: number }
                if (files) {
                    if (files.length === 1) this.aiFirstTime = Date.now() - this.aiStartTime
                    files.forEach(item => {
                        item.width = width;
                        item.height = height
                    })
                }
                switch (status) {
                    case 'RUNNING':
                        this.updateAiDrawPic(files)
                        this.checkAiDraw()
                        break;
                    case 'END':
                    case 'CLOSE':
                    case 'FAIL':
                        this.isAiDrawing = false
                        this.doneAiDraw(status, files)
                        assetManager.setPv_new(7531, {
                            additional: {
                                i0: this.aiFirstTime,
                                i1: time,
                                i2: Date.now() - this.aiStartTime,
                                s0: status === 'FAIL' ? 'FAIL' : 'SUCCESS'
                            }
                        });
                        break;
                    default: // WATTING
                        this.checkAiDraw()
                }
            }).catch(err => {
                this.isAiDrawing = false
                this.doneAiDraw('FAIL')
            })
        }, 2000)
    }
    get lastProduct() {
        return this.cutoutProduct[this.cutoutProduct.length - 1]
    }
    // 让服务器开始生成图片
    startDraw(promptOrScene: string | ISceneItem) {
        if(this.stopPoll) return
        this.aiStartTime = Date.now()
        this.isAiDrawing = true
        const { activeFormat, activeQuality, activeProducer, activeScene } = this.state
        const fromClickScene = typeof promptOrScene !== 'string'
        const prompt = fromClickScene ? (promptOrScene as ISceneItem).prompt : promptOrScene
        const generatorParams = {
            promt: prompt,
            style: (fromClickScene ? (promptOrScene as ISceneItem).style : activeScene.style) || 'xieshi',
            format: activeFormat.format,
            definition: activeQuality.value,
            is_synthesis: 1,
            pic: this.lastProduct.pic,
            pos: this.lastProduct.pos,
            num: activeProducer.value,
        }
        getAiDrawJob(generatorParams).then(res => {
            if (!res.code) {
                this.isAiDrawing = false
                this.tipsInfo(res.msg)
                return;
            }
            let newMessage = null
            switch(res.code){
                // 如果包含违禁词，直接隐藏最后一条消息
                case 10:
                    this.isAiDrawing = false
                    this.tipsInfo(res.msg)
                    newMessage = JSON.parse(JSON.stringify(this.state.message))
                    newMessage.length = newMessage.length - 1
                    this.updateAiDrawInfo(newMessage as IMessageItem[])
                    return
                case 15:
                    // this.tipsInfo('AI绘图功能暂不可用，请稍后再试')
                    if(!this.pollTimer) {
                        newMessage = [
                            ...this.state.message,
                            { role: 'ai', type: 'loadding', content: '努力生成中<b>...</b>' },
                        ]
                        this.updateAiDrawInfo(newMessage as IMessageItem[])
                    }
                    if(this.pollTimer){
                        clearTimeout(this.pollTimer)
                    }
                    this.pollTimer = setTimeout(()=>{
                        this.startDraw(promptOrScene)
                    }, 11000)
                    return;
                case 1:
                    this.jobId = res.data.jobId
                    newMessage = [
                        ...this.state.message,
                    ]
                    !this.pollTimer && newMessage.push({ role: 'ai', type: 'loadding', content: '努力生成中<b>...</b>' })
                    newMessage.push({
                        role: 'ai', type: 'image', content: new Array(1).fill({
                            preview: '',
                            isdel: '0',
                            width: 0,
                            height: 0,
                            format: activeFormat.format,
                            quality: activeQuality.type
                        })
                    })
                    this.updateAiDrawInfo(newMessage as IMessageItem[])
                    this.checkAiDraw()
                    this.pollTimer = null
                    return
                default:
                    if(this.pollTimer){
                        newMessage = [
                            ...this.state.message,
                        ]
                        newMessage.pop()
                        this.updateAiDrawInfo(newMessage as IMessageItem[])
                        this.pollTimer = null
                    }
                    this.isAiDrawing = false
                    this.tipsInfo(res.msg)
                    return;
            }
        }).catch(() => {
            this.isAiDrawing = false
            assetManager.setPv_new(7531, {
                additional: {
                    i0: Date.now() - this.aiStartTime,
                    s0: 'FAIL'
                }
            });
        })
    }

    isNeedFee() {
        // console.log('prod menukey -- ', this.props.menu.menuKey)
        if (this.state.pointNum <= 0) {
            if (this.props.menu.menuKey === 'aiText') {
                // ai文案
                return this.state.wordsNum <= 0

            } else if (this.props.menu.menuKey == 'aiProducts') {
                // ai商品图
                return this.state.fixNum <= 0
            } else if (this.props.menu.menuKey == 'aiDraw') {
                // ai绘图
                return this.state.fixNum <= 0
            } else {
                return true
            }
        }
        return false
    }

    sendUserInfo = (text: string) => {
        assetManager.setPv_new(7527)
        if (!this.props.user.userName) {
            emitter.emit('LoginPanelShow');
            return
        }
        if (this.isNeedFee()) {
            this.addPoints()
            return
        }
        // 如果用户没有上传商品图也不能发送
        if (this.cutoutProduct.length === 0) {
            this.tipsInfo('请先上传商品图～')
            return
        }
        // 正在合成状态
        if (this.isAiDrawing) {
             this.tipsInfo('正在生成中，请勿频繁操作')
             return
        };
        // 用户需求文案
        const userNeed = text
        const newMessage = [
            ...this.state.message,
            { role: 'user', type: 'text', content: userNeed }
        ]
        this.setState({ ideaPopStatus: false })
        this.updateAiDrawInfo(newMessage as IMessageItem[])
        this.stopPoll = false
        this.startDraw(userNeed)
        return true
    }

    autoFillText = (text: string) => {
        const userInputComp = this.userInputRef.current as unknown as { setUserNeed: (text: string) => void, listenerDesContent: () => void }
        userInputComp.setUserNeed(text)
        // userInputComp.listenerDesContent()
    }
    // 选择预设的文案
    presetWordToUserNeed = async (word: IDesItem) => {
        assetManager.setPv_new(7519)
        const { width, height } = await this.getImageSize(word.preview)
        const newMessage = [
            ...this.state.message,
            { role: 'ai', type: MSG_TYPE.CUTOUTED, content: word.preview },
            { role: 'ai', type: MSG_TYPE.TYPE_SELECTOR, content: word.cateid }
        ]
        this.updateAiDrawInfo(newMessage as IMessageItem[])
        this.addCutoutPicMessage(word.preview, word.pic, width, height)
        // this.autoFillText(word.des)
    }
    stopPropagation(e: { stopPropagation: () => void; }): void {
        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }
    // 显示预设创意选项
    toggleIdeaContent = (status?: boolean) => {
        const hasStatus = typeof status === 'boolean'
        this.setState({
            ideaPopStatus: hasStatus ? status : !this.state.ideaPopStatus
        })
    }
    selectFormat = (format: IFormatItem) => {
        this.setState({
            activeFormat: format
        })
        assetManager.setPv_new(7532)
    }
    selectQuality = (quality: TQualityItem) => {
        this.setState({
            activeQuality: quality
        })
        assetManager.setPv_new(7533)
    }
    selectProduce = (produce: TProduceItem) => {
        assetManager.setPv_new(7534)
        this.setState({
            activeProducer: produce
        })
    }
    stopGenerator = () => {
         // 处理轮询任务的情况
         if(this.pollTimer){
            this.stopPoll = true
            this.pollTimer = null
            this.isAiDrawing= false
            const newMessage =[...this.state.message]
            const lastMessage = newMessage.pop()
            lastMessage.content = '已停止生成，您可以重新输入 描述内容生成图片。'
            lastMessage.type = 'text'
            lastMessage.role = 'ai'
            newMessage.push(lastMessage)
            this.updateAiDrawInfo(newMessage as IMessageItem[])
            return
        }
        if (!this.jobId) return
        closeAiDraw(this.jobId)
    }
    rechargeAI(pointNum: number, fixNum: number, wordsNum: number) {
        const windowInfo = {
            windowContent: <RechargeAiPopup from='aiProducts' pointNum={pointNum} fixNum={fixNum} wordsNum={wordsNum} username={this.props.user.userName} avatar={this.props.user.avatar} refresh={this.getPointNum.bind(this)} />,
            popupWidth: 'auto',
            popupHeight: 'auto',
            style: {
                width: '790px',
                height: '420px',
                background: 'transparent',
                borderRadius: '5px 5px 5px 5px',
                left: '0',
                top: '0',
                right: '0',
                bottom: '0',
                margin: 'auto',
                padding: '0',
            },
            popupTitleBarStyle: {
                width: 0,
                height: 0,
                display: 'none',
            }
        };
        emitter.emit('popupWindow', windowInfo);
    }
    addPoints(pointNum = 0, fixNum = 0, wordsNum = 0) {
        if (!pointNum) assetManager.setPv_new(7529);
        if (this.props.user.userName) {
            this.rechargeAI(pointNum, fixNum, wordsNum)
        } else {
            emitter.emit('LoginPanelShow');
        }
    }
    // 切换抠图位置
    closePositionModal = () => {
        emitter.emit('popupClose');
    }
    onSubmitPosition = (url: string, posInfo: { scale: number; leftRatio: number; topRatio: number, left: number; top: number }) => {
        const productIndex = this.cutoutProduct.findIndex(v => v.url === url)
        const product = this.cutoutProduct[productIndex]
        product.scale = posInfo.scale
        product.left = posInfo.left
        product.top = posInfo.top
        product.pos = `${posInfo.scale},${posInfo.leftRatio},${posInfo.topRatio}`
        emitter.emit('popupClose');
    }
    // 修改商品图位置
    // size={{
    //     width: product.width,
    //     height: product.height
    // }} url={product.url} 
    showPositionComp = (product: ICutoutProduct) => {
        assetManager.setPv_new(7522)
        const windowInfo = {
            windowContent: <ChangePosition product={product} activeFormat={this.state.activeFormat} onClose={this.closePositionModal} onSubmit={this.onSubmitPosition} />,
            popupWidth: 1000,
            style: {
                padding: 0,
                background: '#fff'
            },
            popupTitleBarStyle: {
                display: "none"
            },
            popupBodyStyle: {
                padding: 0
            }
        };
        emitter.emit('popupWindow', windowInfo);
    }
    // 从商品库里选择商品图
    selectProductPic = (item: any) => {
        emitter.emit("popupClose");
        const url = item.path ? item.path : item.sample
        this.cutoutPic(url.includes('http') ? url : 'https:' + url)
    }
    // 从图库里选择商品图
    onOpenImageLib = (type?: string) => {
        assetManager.setPv_new(type === 'chatting' ? 7521 : 7525)
        if (!this.state.pointNum && !this.state.fixNum) {
            this.addPoints()
            return;
        }
        emitter.emit('UserUploadBoxUploadFile', 'aiProduct', this.selectProductPic)
    }
    // 上传新的商品图
    onUploadProductPic = (type?: string) => {
        assetManager.setPv_new(type === 'chatting' ? 7520 : 7524)
        if (!this.state.pointNum && !this.state.fixNum) {
            this.addPoints()
            return;
        }
        const inp = document.createElement('input')
        inp.type = 'file'
        inp.accept = 'image/*'
        inp.click()
        inp.onchange = (e) => {
            const file = (e.target as HTMLInputElement).files![0]
            this.uploadFile(file)
        }
    }
    getImageSize(url: string) {
        return new Promise<{ width: number; height: number }>((resolve, reject) => {
            const img = new Image();
            img.onload = function () {
                const width = img.width;
                const height = img.height;
                resolve({ width, height })
            };
            img.src = url
        })
    }
    // 添加抠图信息历史记录
    addCutoutPicMessage = async (url: string, pic: string, width: number, height: number) => {

        this.cutoutProduct.push({
            url,
            pic,
            width,
            height,
            left: 0,
            top: 0,
            scale: 1,
            pos: '0,0,0'
        })
    }
    // 抠图
    cutoutPic = (url: string) => {
        let newMessage = [
            ...this.state.message,
            { role: 'ai', type: MSG_TYPE.CUTOUTING, content: url },
        ]
        this.updateAiDrawInfo(newMessage as IMessageItem[])
        getCououtResult(url).then(async res => {
            const { width, height } = await this.getImageSize(url)
            newMessage.pop()
            newMessage = [
                ...newMessage,
                { role: 'ai', type: MSG_TYPE.CUTOUTED, content: res.data.url },
                { role: 'ai', type: MSG_TYPE.TYPE_SELECTOR, content: '' }
            ]
            this.updateAiDrawInfo(newMessage as IMessageItem[])
            this.addCutoutPicMessage(res.data.url, res.data.pic, width, height)

        })
    }
    // 重新选择场景 对最后一张商品图进行合成
    onReselectScene = () => {
        const newMessage = [
            ...this.state.message,
            { role: 'ai', type: MSG_TYPE.TYPE_SELECTOR, content: this.state.activeScene.cateid },
        ]
        this.updateAiDrawInfo(newMessage as IMessageItem[])
    }
    // 上传文件进行抠图
    uploadFile = (file: File) => {
        const newMessage = [
            ...this.state.message,
            { role: 'ai', type: MSG_TYPE.UPLOADING, content: '商品图上传中<b>...</b>' },
        ]
        this.updateAiDrawInfo(newMessage as IMessageItem[])
        uploadFile(file).then(url => {
            newMessage.pop()
            this.updateAiDrawInfo(newMessage as IMessageItem[])
            // 执行抠图动作
            this.cutoutPic(url)
        }).catch((err) => {
            newMessage.pop()
            this.updateAiDrawInfo(newMessage as IMessageItem[])
            if (err.code === -1001) {
                emitter.emit('LoginPanelShow');
            } else if (err.code === -1002) {
                emitter.emit('InfoBarPhoneBindPopup');
            } else {
                this.tipsInfo(err.msg ? err.msg : '文件上传失败')
            }

        })
    }

    // 修改分类场景
    changeScene = (scene: ISceneItem) => {
        this.autoFillText(scene.prompt)
        this.setState({ activeScene: scene })
    }
    // 点击商品场景类型
    onSelectCateHook = () => {
        assetManager.setPv_new(7523)
    }
    // 适应高度
    onAdapterMessageHeight = () => {
        // const pointRef = this.userInputRef.current as unknown as { desInputRef: React.RefObject<HTMLElement> }
        // this.messageBoxRef.current.style.height = `calc(100vh - 330px - ${pointRef.desInputRef.current.offsetHeight - 40}px)`
    }
    render(): JSX.Element {
        const { message, ideaPopStatus, activeProducer, formatList, activeFormat, activeQuality, pointNum, fixNum, wordsNum, cateTree, presetDesWords, activeScene } = this.state
        return (
            <div className="aiProduct">
                <h2>
                    <img src="https://s.tuguaishou.com/editor/image/ai/aiproduct/pic_menu_aiproduct.png" />
                    <span>AI商品图</span>
                </h2>
                <div className="drawContent">
                    <div className="messageCutBox" ref={this.messageBoxRef}>
                        <MessageList
                            message={message}
                            cateTree={cateTree}
                            activeScene={activeScene}
                            presetDesWords={presetDesWords}
                            onSelectCateHook={this.onSelectCateHook}
                            onUploadProductPic={this.onUploadProductPic}
                            onReselectScene={this.onReselectScene}
                            onStopGenerator={this.stopGenerator}
                            onOpenImageLib={this.onOpenImageLib}
                            onPresetWordToUserNeed={this.presetWordToUserNeed}
                            tipsInfo={this.tipsInfo}
                            stopPropagation={this.stopPropagation}
                            onChangeScene={this.changeScene}
                        ></MessageList>
                    </div>
                    <UserInput
                        pointNum={pointNum}
                        consume={activeProducer.value}
                        ref={this.userInputRef}
                        onAdapterMessageHeight={this.onAdapterMessageHeight}
                        onSendUserInfo={this.sendUserInfo}
                        onToggleIdeaContent={this.toggleIdeaContent}
                        tipsInfo={this.tipsInfo}
                    >
                        <div className="functions">
                            <div className={`function upload `}>
                                <span className='btn'><i className='iconfont icon-shangchuan1'></i>上传商品图</span>
                                <div className="ideaPopup">
                                    <div className="popContent">
                                        <ul>
                                            <li onClick={() => {
                                                this.onUploadProductPic()
                                            }}>上传商品图</li>
                                            <li onClick={() => {

                                                this.onOpenImageLib()
                                            }}>我的上传</li>
                                        </ul>
                                        <span className="triangle"></span>
                                    </div>
                                </div>
                            </div>
                            <div className={`function setting ` + (ideaPopStatus ? 'active' : '')} onClick={this.stopPropagation}>
                                <span className='btn' onClick={() => {
                                    this.toggleIdeaContent()
                                    assetManager.setPv_new(7526)
                                }}><i className='iconfont icon-shezhixuanxiang-01'></i>设置 <i className='arrow iconfont icon-xiangxia1'></i></span>
                                <div className="ideaPopup">
                                    <div className="popContent">
                                        <span className='close' onClick={() => {
                                            this.toggleIdeaContent(false)
                                            assetManager.setPv_new(7426)
                                        }}><i className='iconfont icon-a-guanbi31'></i></span>
                                        <div className="ideaGroupCut">
                                            <div className="ideaGroup">
                                                <div className="ideoItem format">
                                                    <h4>选择版式</h4>
                                                    <ul>
                                                        {formatList.map(item => {
                                                            return <li
                                                                key={item.id}
                                                                className={`format${item.id} ${activeFormat.id === item.id ? 'active' : ''}`}
                                                                onClick={() => {
                                                                    this.selectFormat(item)
                                                                }}
                                                            >
                                                                <b><i></i></b>
                                                                <span>{item.name}</span>
                                                            </li>
                                                        })}
                                                    </ul>
                                                </div>
                                                <div className="ideoItem quality">
                                                    <h4>选择画质</h4>
                                                    <ul>
                                                        {qualityList.map(item => {
                                                            return <li
                                                                key={item.name}
                                                                className={`${activeQuality.value === item.value ? 'active' : ''}`}
                                                                onClick={() => {
                                                                    this.selectQuality(item)
                                                                }}
                                                            >{item.name}</li>
                                                        })}
                                                    </ul>
                                                </div>
                                                <div className="ideoItem produce">
                                                    <h4>生成张数</h4>
                                                    <ul>
                                                        {produceNumber.map(item => {
                                                            return <li
                                                                key={item.value}
                                                                className={`${activeProducer.value === item.value ? 'active' : ''}`}
                                                                onClick={() => {
                                                                    this.selectProduce(item)
                                                                }}
                                                            >{item.name}</li>
                                                        })}
                                                    </ul>
                                                </div>

                                            </div>
                                        </div>
                                        <span className="triangle"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </UserInput>
                </div>
                <PointRule from='aiProducts' ref={this.pointRuleRef} pointNum={pointNum} fixNum={fixNum} wordsNum={wordsNum} onAddPoints={(n, fn, wn) => {
                    assetManager.setPv_new(7528)
                    this.addPoints(n, fn, wn)
                }}></PointRule>
            </div>
        )
    }
}
