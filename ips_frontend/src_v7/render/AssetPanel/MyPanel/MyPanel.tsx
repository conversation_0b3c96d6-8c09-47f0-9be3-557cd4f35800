import React, { PureComponent } from 'react';

import { assetManager } from '@component/AssetManager';
import { ETool } from '@v7_logic/Enum';
import { MyDesign } from './components/MyDesign'
import { MyFav } from './components/MyFav'
import { UploadGroup } from './UploadGrout'
import { MyAll } from './components/MyAll'
import { TAsset, TemplateList } from './type'
import { emitter } from '@component/Emitter';
import { EventSubscription } from 'fbemitter';
import { Tabs } from '@v7_render/Ui';
import './scss/index.scss'
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { UserLogic } from '@v7_logic/UserLogic';
import { storeDecorator } from '@v7_logic/StoreHOC';
import { IUserInfo } from '@v7_logic/Interface';
export function DisplayMyTool(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.MYPANEL, nav: ETool.MYPANEL};
}
interface propsStruct {
    isActive?: boolean;
    user?: IUserInfo;
}
type TChangeKey = 'all' | 'design' | 'fav' | 'upload'|'third'
interface stateStruct {
    activeKey: TChangeKey;
    designImgList: TemplateList[];
    designStarList: TemplateList[];
    favTemplateList: TAsset[];
    favMaterialList: TAsset[];
    favSpecialList: TAsset[];
    thirdList:TAsset[];
    maskShow: boolean;
    total: number;
    limit: number;
    hasMore: boolean;
    isSpecialVip: boolean;
}
const mapStateToProps = (state) => {
    return {
        user: state.onCanvasPainted.user,
    };
};

@storeDecorator(mapStateToProps)
export class MyPanel extends PureComponent<propsStruct, stateStruct> {
    changeMyUpload: EventSubscription;
    changeMyDesignListEvent: EventSubscription;
    changeMyFavListEvent: EventSubscription;
    changeUploadMaskShowEvent: EventSubscription;
    deleteUserThirdAssetEvent: EventSubscription;
    
    thirdPage: number = 1;
    constructor(props: propsStruct) {
        super(props);
        this.state = {
            activeKey: 'all',

            maskShow: false,

            designImgList: [],
            designStarList: [],
            favTemplateList: [],
            favMaterialList: [],
            favSpecialList: [],
            thirdList:[],
            total: 0,
            limit: 10,
            hasMore:true,
            isSpecialVip: false,
        };
        this.getUserTemplateList();
        this.myFavForTempl();
        this.getMaterialList();
        this.getSpecialList();
        this.getThirdList()

        this.changeMyUpload = emitter.addListener('changeMyUpload', () => {
            this.onChangeKey('upload')
        });
        this.changeMyDesignListEvent = emitter.addListener('changeMyDesignList', (type: string, item: TAsset) => {
            this.changeMyDesignList(type, item)
        });
        this.changeMyFavListEvent = emitter.addListener('changeMyFavList', (type: string, item: TAsset) => {
            this.changeMyFavList(type, item)
        });
        this.changeUploadMaskShowEvent = emitter.addListener('changeUploadMaskShow', (flag: boolean) => {
            this.setState({ maskShow: flag })
        });

        this.deleteUserThirdAssetEvent = emitter.addListener('deleteUserThirdAsset', (assetItem:TAsset) => {
            this.deleteThirdAsset(assetItem)
        });
    }
    componentWillUnmount(): void {
        this.changeMyDesignListEvent.remove();
        this.changeMyFavListEvent.remove();
        this.changeMyUpload.remove();
        this.changeUploadMaskShowEvent.remove();
    }

    setDesignImgList = (data: TemplateList[]) => {
        this.setState({ designImgList: data })
    }
    setDesignStarList = (data: TemplateList[]) => {
        this.setState({ designStarList: data })
    }
    setFavTemplateList = (data: TAsset[]) => {
        this.setState({ favTemplateList: data })
    }
    setFavMaterialList = (data: TAsset[]) => {
        this.setState({ favMaterialList: data })
    }
    setFavSpecialList = (data: TAsset[]) => {
        this.setState({ favSpecialList: data })
    }

    setThirdList = (data: TAsset[]) => {
        this.setState({ thirdList: data })
    }

    changeMyDesignList = (type: string, asset: TAsset) => {
        const { designImgList, designStarList } = this.state;
        let select,
            newImgData = designImgList,
            newStarData = designStarList;
        switch (type) {
            case 'addStar':
                newImgData = designImgList.map(item => {
                    if (String(item.id) === asset.id) {
                        const newItem = { ...item, is_star_teml: 1 }
                        select = newItem
                        return newItem
                    } else {
                        return item
                    }
                })
                newStarData = [select, ...designStarList]
                break;
            case 'delStar':
                newImgData = designImgList.map(item => {
                    if (String(item.id) === asset.id) {
                        const newItem = { ...item, is_star_teml: 0 }
                        select = newItem
                        return newItem
                    } else {
                        return item
                    }
                })
                newStarData = designStarList.filter(item => String(item.id) !== asset.id)
                break;
            case 'delTemp':
                newImgData = designImgList.filter(item => String(item.id) !== asset.id)
                newStarData = designStarList.filter(item => String(item.id) !== asset.id)
                break;
            case 'changeTitle':
                newImgData = designImgList.map(item => {
                    if (String(item.id) === asset.id) {
                        const newItem = { ...item, title: asset.title }
                        return newItem
                    } else {
                        return item
                    }
                })
                newStarData = designStarList.map(item => {
                    if (String(item.id) === asset.id) {
                        const newItem = { ...item, title: asset.title }
                        return newItem
                    } else {
                        return item
                    }
                })
                break;
        }

        this.setDesignImgList(newImgData);
        this.setDesignStarList(newStarData);

    }

    changeMyFavList = (type: string, asset: TAsset) => {
        const { favTemplateList, favMaterialList, favSpecialList } = this.state;
        switch (type) {
            case 'template':
                const newFavTemplateList = favTemplateList.filter(item => String(item.id) !== asset.id)
                this.setFavTemplateList(newFavTemplateList);
                break;
            case 'material':
                const newFavMaterialList = favMaterialList.filter(item => String(item.id) !== asset.id)
                this.setFavMaterialList(newFavMaterialList);
                break;
            case 'special':
                const newFavSpecialList = favSpecialList.filter(item => String(item.id) !== asset.id)
                this.setFavSpecialList(newFavSpecialList);
                break;
        }
    }

    getUserTemplateList(): void {
        assetManager.myDesignerForTemplate(1).then((data) => {
            data.json().then((resultData) => {
                if (Number(resultData.stat) === 1) {
                    this.setDesignImgList(resultData.data)
                }
            });
        });
    }

    myFavForTempl(): void {
        assetManager.myFavForTempl(1).then((data) => {
            data.json().then((resultData) => {
                if (resultData.code === 1) {
                    this.setFavTemplateList(resultData.data.work)
                }
            });
        });
    }
    getMaterialList = () => {
        assetManager.getFavList(1).then((data) => {
            data.json().then((resultData) => {
                if (Number(resultData.code) === 1) {
                    this.setFavMaterialList(resultData.data)
                }
            });
        });
    };
    getSpecialList = () => {
        assetManager.myFavForAlbum(1).then((data) => {
            data.json().then((resultData) => {
                if (Number(resultData.code) === 1) {
                    this.setFavSpecialList(resultData.data.work)
                }
            });
        });
    };
    // 获取第三方资源上传
    getThirdList = () => {
        if (!this.state.hasMore) return;
        this.setState({
            hasMore:false
        })
        assetManager.getUserAssetList(this.thirdPage,'-1').then((data) => {
            data.json().then((resultData) => {
                if (Number(resultData.stat) === 1) {
                    this.thirdPage++;
                    this.setState({
                        hasMore:!!resultData.msg.length
                    })
                    this.setThirdList([...this.state.thirdList, ...resultData.msg])
                }
            });
        })
    }

    onChangeKey = (key: TChangeKey) => {
        if (key === 'all') { 
            emitter.emit('showMyPanelAllList')
        }
        this.setState({
            activeKey: key,
        });
    };

    updateUploadLimit() {
        UserLogic.getUserUploadLimitInfo()
        this.setState({})
    }
    async checkPersonalVipUserIsSpecial(){
        assetManager.checkUserIsSpecialVip().then((data) => {
             data.json().then((resultData) => {
                 if (resultData.code == 1) {
                     this.setState({ isSpecialVip: resultData.data });
                 }
             });
         });
      }

    componentDidMount(): void {
        this.updateUploadLimit()
        this.checkPersonalVipUserIsSpecial()
        emitter.addListener('updateUploadLimit', () => {
            this.updateUploadLimit()
        })
        emitter.addListener('userSpaceUpgrade',()=>{
            this.updateUploadLimit()
            this.checkPersonalVipUserIsSpecial()
        })
    }

    deleteThirdAsset = (asset:TAsset) => { 
        this.setThirdList(this.state.thirdList.filter(v=>v.id !== asset.id))
    }

    render(): JSX.Element {
        const { uploadLimit, uploadTotal } = this.props.user;
        const { activeKey, designImgList, designStarList, favTemplateList, favMaterialList, favSpecialList, maskShow, isSpecialVip, thirdList } = this.state;
        const propsData = {
            designImgList,
            designStarList,
            setDesignImgList: this.setDesignImgList,
            setDesignStarList: this.setDesignStarList,
            favTemplateList,
            favMaterialList,
            favSpecialList,
            setFavTemplateList: this.setFavTemplateList,
            setFavMaterialList: this.setFavMaterialList,
            setFavSpecialList: this.setFavSpecialList,
            thirdList,
            setThirdList: this.setThirdList,
            getThirdList: this.getThirdList,
        }

        return (
            <div className='my_panel'>
                <Tabs
                    isActive={false}
                    minusLeft={20}
                    defaultActive={activeKey}
                    className='tgs-tabs-line'
                    tabs={[
                        {
                            show: true,
                            key: 'all',
                            title: '全部',
                            content: <MyAll onChangeKey={this.onChangeKey} propsData={propsData} total={uploadTotal} limit={uploadLimit} />,
                            setPv: {
                                title: {
                                    click: () => assetManager.setPv_new(7316),
                                },
                            },
                        },
                        {
                            show: true,
                            key: 'design',
                            title: '设计',
                            content: <MyDesign propsData={propsData} />,
                            setPv: {
                                title: {
                                    click: () => assetManager.setPv_new(7317),
                                },
                            },
                        },
                        {
                            show: true,
                            key: 'fav',
                            title: '收藏',
                            content: <MyFav onChangeKey={this.onChangeKey} propsData={propsData} />,
                            setPv: {
                                title: {
                                    click: () => assetManager.setPv_new(7318),
                                },
                            },
                        },
                        {
                            show: true,
                            key: 'upload',
                            title: '上传',
                            content: <UploadGroup currentNav={'myPanel'} onChangeKey={this.onChangeKey} total={uploadTotal} limit={uploadLimit} isSpecialVip={this.state.isSpecialVip} source='myPanel'/>,
                            setPv: {
                                title: {
                                    click: () => assetManager.setPv_new(7319),
                                },
                            },
                        },
                    ]}
                    onChangeKey={this.onChangeKey}
                ></Tabs>
                <div className={maskShow ? 'my_panel_uploadMask' : 'my_panel_uploadMaskHide'}>
                    <div className='title'>拖拽图片至此处上传</div>
                    <div className='title'>支持批量</div>
                    <div className='title'>拖拽上传暂支持 JPEG/PNG 格式文件</div>
                </div>
            </div>
        )
    }
}
