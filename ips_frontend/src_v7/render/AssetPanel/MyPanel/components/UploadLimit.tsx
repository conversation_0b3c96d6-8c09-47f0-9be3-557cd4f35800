import { assetManager } from '@component/AssetManager';
import { emitter } from "@src/userComponentV6.0/Emitter"
import { storeDecorator } from '@v7_logic/StoreHOC';
import { ErrorBoundaryDecorator } from '@v7_render/ErrorBoundaryHOC';
import { IStoreState } from '@v7_store/redux/store';
import React, { ReactElement, useCallback, PureComponent, useMemo, useRef } from "react"
import { IUserInfo } from '@v7_logic/Interface';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import './scss/uploadLimit.scss';
export const PopupBox = ({ total, limit, pos }: { total: number, limit: number, pos: string }): ReactElement => {

    const isLimit = useMemo(() => {
        return pos ? pos.length > 1 : false
    }, [pos])

    const close = useCallback(() => {
        emitter.emit('popupClose')
        const pageId = isLimit ? 7433 : 7421
        assetManager.setPv_new(pageId, {
            additional: {
                s1: pos
            }
        })
    }, [])

    const ok = useCallback(() => {
        emitter.emit('popupClose')
        const pageId = isLimit ? 7432 : 7463
        assetManager.setPv_new(pageId, {
            additional: {
                s1: pos
            }
        })
        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (user?.userId !== '0') {
            window.open( `https://818ps.com/dash/firm-intro?origin=UploadCapacityUpgrade&route_id=16855116458319&route=&after_route=`,
                '_blank',
            );
        } else {
            window.open('https://818ps.com/dash/firm-intro?origin=UploadCapacityUpgrade&route_id=16855116458319&route=&after_route=');
        }

    }, [])

    return (
        <div>
            <div style={{
                display: 'flex',
                justifyContent: 'space-between'
            }}>
                <div style={{
                    fontSize: '20px',
                    fontWeight: '500',
                    color: '#000',
                    lineHeight: '26px',

                }}>{total >= limit ? '您的上传空间已满' : '上传空间'}</div>
                <div onClick={close}>
                    <i className="iconfont icon-quxiao" style={{
                        color: '#A5A3A4',
                        fontSize: '11px',
                        cursor: 'pointer',
                    }} ></i>
                </div>
            </div>
            <div style={{
                fontSize: ' 16px',
                fontWeight: '400',
                color: '#4C4849',
                lineHeight: '24px',
                margin: '32px 0 44px 0'
            }}>
                {total >= limit ?
                    <>无法上传新素材，请删除旧素材或升级<span style={{ fontWeight: '600', background: 'linear-gradient(90deg, #ef3964 40%, #f9b531 100%)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>{'企业VIP'}</span>享受更多特权</> :
                    <>升级<span style={{ fontWeight: '600', background: 'linear-gradient(90deg, #ef3964 40%, #f9b531 100%)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>{'企业VIP'}</span>，可享更多空间容量和VIP特权</>
                }
            </div>
            <div style={{
                display: 'flex',
                justifyContent: 'end',
            }}>
                <button
                    className='upload_limit_btn_canel'
                    onClick={close}
                >取消</button>
                <button
                    className='upload_limit_btn_ok'
                    onClick={ok}>立即升级</button>
            </div >
            <style>{`
                .upload_limit_btn_canel {
                    width: 80px;
                    height: 40px;
                    border-radius: 24px 24px 24px 24px;
                    border: 1px solid #d2d1d1;
                    cursor: pointer;
                    background-color: #fff;
                    margin-right: 16px;
                    
                }
                .upload_limit_btn_canel:hover {
                    border: 1px solid #ef3964;
                    color: #ef3964;
                }
                .upload_limit_btn_ok {
                    width: 96px;
                    height: 40px;
                    background: linear-gradient(137deg, #ef3964 0%, #ef3964 48%, #e47a19 100%);
                    border-radius: 24px 24px 24px 24px;
                    cursor: pointer;
                    border: 1px solid rgba(0, 0, 0, 0);
                    color: #fff;
                }
                .upload_limit_btn_ok:hover {
                    background: linear-gradient(137deg, #e47a19 0%, #ef3964 48%, #ef3964 100%);
                }
            `}</style>
        </div >
    )
}
export const PopupAgreeUpgrade=()=>{
    const  promptBoxTimeRef=useRef<NodeJS.Timeout>()
    const agreeUpgrade = () => {
        assetManager.upgradePersonalVipUserSpace().then((data)=>{
            data.json().then((resultData) => {
                if (resultData.code !== 1) {
                      // 保存失败提示框
                      const windowInfo = {
                        windowContent: (
                            <div className="userUploadTipArea">
                                <p className="text1">
                                    <i className="iconfont icon-tishi"></i>升级失败
                                </p>
                            </div>
                        ),
                    };
                    emitter.emit('PromptBox', windowInfo);
                    if (promptBoxTimeRef.current ) {
                        clearTimeout(  promptBoxTimeRef.current );
                    }
                    promptBoxTimeRef.current = setTimeout(() => {
                        emitter.emit('PromptBoxClose');
                    }, 3000);
                }else{
                    emitter.emit('userSpaceUpgrade')
                }
            });
        })
        assetManager.setPv_new(7536)
        emitter.emit('popupClose')
    }
    const close= () => {
        emitter.emit('popupClose')
    }
    return (
        <div className='agree-upgrade-container'>
            <div  className='agree-upgrade-container-header' onClick={close}>
                    <i className="iconfont icon-quxiao" style={{
                        color: '#A5A3A4',
                        fontSize: '16px',
                        cursor: 'pointer',
                    }} ></i>
            </div>
            <div className='upgrade-tip'>
                将为您升级到100张图片上传空间
            </div>
            <div className='agree-upgrade-btn' onClick={agreeUpgrade}> 同意升级 </div>
        </div>
    )
}
// isSpecialVip 区分一些vip用户，目前是2023/5/9号前
export const PopupLimitBox = ({ total, limit, pos,fromType, isSpecialVip }: { total: number, limit: number, pos: string,fromType?:string, isSpecialVip?:boolean }): ReactElement => {
    const isLimit = useMemo(() => {
        return pos ? pos.length > 1 : false
    }, [pos])

    const close = useCallback(() => {
        emitter.emit('popupClose')
        const pageId = isLimit ? 7433 : 7421
        assetManager.setPv_new(pageId, {
            additional: {
                s1: pos
            }
        })
    }, [])

    const ok = useCallback(() => {
        emitter.emit('popupClose')
        const pageId = isLimit ? 7432 : 7463
        assetManager.setPv_new(pageId, {
            additional: {
                s1: pos
            }
        })
        const limitForPay = {
            'background-upload':7509,
            'background-replace': 7508,
            'image-doubleClick': 7506,
            'image-replace': 7506,
            'bg-doubleClick': 7506,
            'left-bottom': 7511,
            'left-me': 7510,
            'left-add': 7510,
            'cutout':7516,
            'aiCutout':9066,
        }
        const limitForPayField = {
            // 右侧背景图上传
            'background-upload': 'BackgroundUploadPoint',
            // 右侧背景图替换
            'background-replace': 'BackgroundReplaceUploadPoint',
            // 图片双击
            'image-doubleClick': 'ImageDoubleClickUploadPoint',
            // 右侧图片替换
            'image-replace': 'ImageReplaceUploadPoint',
            // 背景双击
            'bg-doubleClick': 'BackgroundDoubleClickUploadPoint',
            // 左侧底部
            'left-bottom': 'LeftBottomUploadPoint',
            // 左侧我的上传
            'left-me': 'LeftMeUploadPoint',
            // 左侧添加
            'left-add': 'LeftAddUploadPoint',
            // 抠图上传
            'cutout':'CutoutUploadPoint',
            'aiCutout':'AICutoutPoint',
            'aiDraw':'AIDrawPoint',
            'aiEnhance':'AiEnhancePoint',
            'aiEliminate':'AiEliminatePoint',
            'aiEliminateReDraw':'AiEliminateReDrawPoint',
            'aiProduct':'AiProductPoint',
            'aiWordArt':'AiWordArtPoint',
            'aiChat':'AiChatPoint',
        }
        let origin = 'UploadCapacityUpgrade'
        if (fromType) { 
            assetManager.setPv_new(limitForPay[fromType], {
            })
            origin = limitForPayField[fromType]
        }
        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (user?.userId !== '0') {
            window.open(`https://818ps.com/dash/firm-intro?origin=${origin}&route_id=16855116458319&route=&after_route=`,
                '_blank',
            );
        } else {
            window.open(`https://818ps.com/dash/firm-intro?origin=${origin}&route_id=16855116458319&route=&after_route=`);
        }
    }, [])
    const go = () => {
        window.open('https://818ps.com/notice/1/161.html')
    }
    const getFreeExpation=()=>{
        const windowInfo = {
            windowContent: <PopupAgreeUpgrade></PopupAgreeUpgrade>,
            popupWidth: 'auto',
            popupHeight: 'auto',
            style: {
                width: '600px',
                height: '210px',
                background: '#fff',
                boxShadow: '0px 4px 14px 0px rgba(31,26,27,0.16)',
                borderRadius: '12px 12px 12px 12px',
                left: '0',
                top: '0',
                right: '0',
                bottom: '0',
                margin: 'auto',
                padding: '0'
            },
            popupTitleBarStyle: {
                width: 0,
                height: 0,
                display: 'none',
            }
        };
        emitter.emit('popupWindow', windowInfo);
        assetManager.setPv_new(7535, {
            additional: {
                s1: pos
            }
        })
    }
    return (
        <div className={ [isSpecialVip ? 'upload_special_limit-container' :''].join('')}>
            <div style={{
                width: "100%",
                height: '154px',
                background: 'linear-gradient(180deg, rgba(255,65,110,0.12) 0%, rgba(255,255,255,0) 100%)',
                borderRadius: '12px 12px 12px 12px',
                position: 'absolute',
                left: '0',
                top: '0',
                zIndex: '-1'
            }}></div>
            <div style={{
                display: 'flex',
                justifyContent: 'space-between'
            }}>
                <div style={{
                    fontSize: '24px',
                    fontWeight: '500',
                    color: '#1f1a1b',
                    lineHeight: '26px',

                }}>{'您的空间容量已满，继续' + (fromType.includes('ai') ? '使用？' : "上传？")}</div>
                <div onClick={close}>
                    <i className="iconfont icon-quxiao" style={{
                        color: '#A5A3A4',
                        fontSize: '16px',
                        cursor: 'pointer',
                    }} ></i>
                </div>
            </div>
            <div style={{
                fontSize: ' 20px',
                fontWeight: '500',
                color: '#4C4849',
                lineHeight: '32px',
                marginTop: '16px'
            }}>
                {
                    //  <>请删除旧素材或<span style={{ fontWeight: '600', background: 'linear-gradient(90deg, #ef3964 40%, #f9b531 100%)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>{'升级企业VIP，解锁最大50G上传空间'}</span></>
                    <><span style={{ fontWeight: '600', background: 'linear-gradient(90deg, #ef3964 40%, #f9b531 100%)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>{'升级企业VIP，解锁最大50G上传空间'}</span></>
                }
            </div>
            <div className={'upload_limit_tips_box'}>
                <div className='upload_limit_tips' style={{ marginRight: '80px' }}>
                    <div style={{ width: '16px', height: '16px', color: '#fff', borderRadius: '100%', overflow: 'hidden', background: '#EF3964', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <i className='iconfont icon-gou1' style={{ fontSize: '14px' }}></i>
                    </div>
                    <div style={{ fontSize: '16px', color: '#797676', fontWeight: '400', marginLeft: '4px' }}>226万+模板海量下载</div>
                </div>
                <div className='upload_limit_tips'>
                    <div style={{ width: '16px', height: '16px', color: '#fff', borderRadius: '100%', overflow: 'hidden', background: '#EF3964', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <i className='iconfont icon-gou1' style={{ fontSize: '14px' }}></i>
                    </div>
                    <div style={{ fontSize: '16px', color: '#797676', fontWeight: '400', marginLeft: '4px' }}>享企业商用授权，专业版权保障</div>
                </div>
            </div>
            <div className='upload_limit_confirm_btn_box'>
                {/*{isSpecialVip && <button className={['upload_limit_confirm_btn','special'].join(' ')} onClick={getFreeExpation}>领取VIP免费扩容福利</button>}*/}
                <button className='upload_limit_confirm_btn' onClick={ok}>立即升级，继续{fromType.includes('ai') ? '使用' : "上传"}</button>
            </div >
            <div className='upload_limit_confirm_tip_box'>
                {/*<div className='upload_limit_confirm_tip' style={{ lineHeight: '24px', fontWeight: '400', fontSize: '16px', margin: '16px 0 24px 0', cursor: 'pointer' }} onClick={go}>查看上传服务调整公告</div>*/}
            </div>
            {/* <style>{`
                .upload_limit_tips{
                    display: flex;
                    align-items: center;
                    height: 24px;
                }
                .upload_limit_tips_box{
                    display: flex;
                    margin-top: 32px;
                }
                .upload_limit_confirm_btn_box{
                    margin-top: 52px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                }
                .upload_limit_confirm_btn{
                    width: 100%;
                    height: 40px;
                    background: linear-gradient(137deg, #EF3964 0%, #EF3964 48%, #E47A19 100%);
                    border-radius: 24px 24px 24px 24px;
                    color: #fff;
                    border: 1px solid rgba(0, 0, 0, 0);
                    cursor: pointer;
                    font-size: 16px;
                }
                .upload_limit_confirm_btn:hover{
                    background: linear-gradient(137deg, #E47A19 0%, #EF3964 48%, #EF3964 100%);
                }
                .upload_limit_confirm_tip{
                    color: #797676;
                }
                .upload_limit_confirm_tip:hover{
                    color: #1f1a1b;
                }
                .upload_special_box > .upload_limit_confirm_btn {
                    width:40%;
                }
            `}</style> */}
        </div >
    )
}


interface propsStruct {
    user?: IUserInfo;
    total: number
    limit: number
}
@ErrorBoundaryDecorator()
@storeDecorator((state: IStoreState) => {
    return {
        user: state.onCanvasPainted.user,
    };
})

class UploadLimit extends PureComponent<propsStruct> {
    constructor(props: propsStruct) {
        super(props);
        this.state = {
        };
    }

    handleClick() {
        window.open(`https://818ps.com/dash/firm-intro?origin=UploadCapacityUpgrade&route_id=16856907379166&route=&after_route=`, '_blank',);

        // const { user } = storeAdapter.getStore({
        //     store_name: storeAdapter.store_names.paintOnCanvas,
        // });
        // const isGrayscale = checkIsUserGrayScale(user?.userId, true);
        // if (isGrayscale) {
        //     window.open(`https://818ps.com/dash/firm-intro?origin=UploadCapacityUpgrade&route_id=16856907379166&route=&after_route=`, '_blank',);
        // } else { 
        //     const windowInfo = {
        //         windowContent: <PopupBox total={this.props.total} limit={this.props.limit} pos='1' />,
        //         popupWidth: 'auto',
        //         popupHeight: 'auto',
        //         style: {
        //             width: '600px',
        //             height: '164px',
        //             background: '#fff',
        //             boxShadow: '0px 4px 14px 0px rgba(31,26,27,0.16)',
        //             borderRadius: '12px 12px 12px 12px',
        //             left: '0',
        //             top: '0',
        //             right: '0',
        //             bottom: '0',
        //             margin: 'auto',
        //         },
        //         popupTitleBarStyle: {
        //             width: 0,
        //             height: 0,
        //             display: 'none',
        //         }
        //     };
        //     emitter.emit('popupWindow', windowInfo);
        // }
   
        assetManager.setPv_new(7419, {
            additional: {
                s1: "1"
            }
        })
    }

    render(): JSX.Element {
        const { userId, is_firm_vip, uploadAITotal} = this.props.user
        const id = String(userId)
        
        return (
            <>{is_firm_vip != 1 && <div className="upload_limit" style={{ marginTop: '5px' }}>
                <div className="segmentation" />
                <div className="upload_limit_title">
                    <div className="limit_left">{this.props.total < this.props.limit ? '上传空间' : '上传空间已满'}</div>
                    <div className="limit_right" onClick={this.handleClick.bind(this)}>
                        <div className="limit_right_text">
                            <span style={{
                                marginRight: '7px'
                            }}>升级</span>
                        </div>
                        <div className="limit_right_arrow">
                            <i className="iconfont icon-shang1" style={{
                                fontSize: '12px',
                                height: '14px',
                                width: '14px',
                            }}></i>
                        </div>
                    </div>
                </div>
                <div className="data_box">
                    <div className="data">{`${this.props.total}张/${this.props.limit}张`}</div>
                    <div className="progress_box">
                        <div className="progress" style={{ width: `${((this.props.total > this.props.limit ? this.props.limit : this.props.total) / this.props.limit) * 100}%` }}></div>
                    </div>
                    <div className='asset_num_info'>
                        <span className='upload_num_info'>上传图片：{this.props.total - uploadAITotal}张</span>
                        <span className='ai_num_info'>AI生图图片：{uploadAITotal}张</span>
                    </div>
                </div>
            </div>
            }
            </>
        )
    }
}

export default UploadLimit