import React, { useEffect, useRef, useState } from 'react';
import { TgsAiChat } from '@tgs/ai_chat';
import { Modal, Popover, message } from 'antd';
import { emitter } from '@component/Emitter';
import { assetManager } from '@component/AssetManager';
import { CanvasPaintedLogic } from '@v7_logic/CanvasPaintedLogic/CanvasPaintedLogic';
import { AssetLogic, UpdateAsset } from '@v7_logic/AssetLogic';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { IText, IRichText } from '@v7_logic/Interface/Asset';
import { PopupLimitBox } from '../MyPanel/components/UploadLimit';
import { VipRechargeModal } from './components/VipRechargeModal';
import { storeHOC } from '@v7_logic/StoreHOC/storeHOC';
import { createTableAssets } from '@v7_logic/PasteListener/createTableAssets';

interface IEditorController {
    // 文本编辑相关
    enterTextEdit: (asset: any) => void;
    exitTextEdit: (asset: any) => void;
    updateText: (text: string, asset: any) => void;
    
    // 图片裁剪相关
    enterImageClip: (asset: any) => void;
    exitImageClip: (asset: any) => void;
    updateImageClip: (clipData: any) => void;
    
    // 画布控制相关
    blurSelect: () => void;
    updateCanvas: (type: string, data: any) => void;
    
    // 组操作相关
    enterGroupEdit: (groupName: string) => void;
    exitGroupEdit: (groupName: string) => void;
    updateGroupContent: (groupName: string, content: any) => void;

    // 添加图片
    addPic: (asset: any) => void;

    // 添加表格
    addTable: (asset: any) => void;

    // 记录当前编辑器状态
    recordCurrentState: (messageId: string) => void;
}

export const useEditorController = (): IEditorController => {
    const editController = useRef<IEditorController>({
        // 文本编辑相关
        enterTextEdit: (asset) => {
            if (!asset) return;
            const { toolPanel } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            
            AssetLogic.updateGroupTextEditor({
                asset,
                assetIndex: toolPanel.asset_index,
                fun_name: "UPDATE_GROUP_TEXT_EDITOR"
            });
            
            emitter.emit('textEditorLockedEditor');
        },
        
        exitTextEdit: (asset) => {
            if (!asset) return;
            const { toolPanel } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            
            AssetLogic.updateGroupTextEditor({
                asset,
                assetIndex: toolPanel.asset_index,
                fun_name: "EXIT_GROUP_TEXT_EDITOR"
            });
        },
        
        updateText: (text, asset) => {
            if (!asset) return;
            const { toolPanel } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            
            UpdateAsset.updateAssets('UPDATE_TEXT', [{
                index: toolPanel.asset_index,
                changes: {
                    attribute: {
                        text: [text] as (IText[] | string | IRichText)[]
                    }
                }
            }]);
        },
        
        // 图片裁剪相关
        enterImageClip: (asset) => {
            if (!asset?.attribute?.container) return;
            
            AssetLogic.updateAssetContainerIsEdit({
                isEdit: true,
                asset,
                assetIndex: asset.meta.index,
                className: asset.meta.className
            });
            
            assetManager.setPv_new(2904);
        },
        
        exitImageClip: (asset) => {
            if (!asset?.attribute?.container) return;
            
            AssetLogic.updateAssetContainerIsEdit({
                isEdit: false,
                asset,
                assetIndex: asset.meta.index,
                className: asset.meta.className
            });
            
            assetManager.setPv_new(2905);
        },
        
        updateImageClip: (clipData) => {
            const { toolPanel } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            
            UpdateAsset.updateAssets('UPDATE_ASSET_CONTAINER', [{
                index: toolPanel.asset_index,
                changes: {
                    attribute: {
                        container: clipData
                    }
                }
            }]);
        },
        
        // 画布控制相关
        blurSelect: () => {
            CanvasPaintedLogic.updateIsContainerEditor({ 
                isContainerEditor: false, 
                isNotDo: true 
            });
        },
        
        recordCurrentState: (messageId: string) => {
            emitter.emit('autoSaveTempl', '', '', {handleSave: true, message_id: messageId});
        },

        updateCanvas: (type, data) => {
            CanvasPaintedLogic[type](data);
        },
        
        // 组操作相关
        enterGroupEdit: (groupName) => {
            if (!groupName) return;
            emitter.emit('group:enterEdit', { groupName });
        },
        
        exitGroupEdit: (groupName) => {
            if (!groupName) return;
            emitter.emit('group:exitEdit', { groupName });
        },
        
        updateGroupContent: (groupName, content) => {
            if (!groupName) return;
            emitter.emit('group:updateContent', { groupName, content });
        },

        addPic: (asset) => {
            emitter.emit('ListAddPic', asset);
        },

        addTable: (asset) => {
            const tableAsset = createTableAssets(asset)
            emitter.emit('ListAddTable', {
                width: 717,
                height: 400,
                meta: tableAsset.asset.meta,
                attribute: tableAsset.asset.attribute,
                transform: tableAsset.asset.transform,
            });
        }
    });

    return editController.current;
};



const mapStateToProps = (state: any) => {
    return {
        user: state.onCanvasPainted.user,
    };
};
export const AIChat: React.FC = () => {
    const editorController = useEditorController();
    const [generateNum, setGenerateNum] = useState<any>(null);

    const { user } = storeAdapter.getStore({
        store_name: storeAdapter.store_names.paintOnCanvas,
    });

    const limitPopup = () => {
        const windowInfo = {
            windowContent: (
                <PopupLimitBox
                    fromType="aiChat"
                    total={1}
                    limit={1}
                    pos="11"
                    isSpecialVip={false}
                />
            ),
            popupWidth: 'auto',
            popupHeight: 'auto',
            style: {
                width: '600px',
                height: '220px',
                background: '#fff',
                boxShadow: '0px 4px 14px 0px rgba(31,26,27,0.16)',
                borderRadius: '12px 12px 12px 12px',
                left: '0',
                top: '0',
                right: '0',
                bottom: '0',
                margin: 'auto',
                padding: '24px',
            },
            popupTitleBarStyle: {
                width: 0,
                height: 0,
                display: 'none',
            },
        };
        emitter.emit('popupWindow', windowInfo);
    }

    const rechargeModalPopup = (origin: string = '',callback: (isCharge: boolean) => void) => {
        const windowInfo = {
            windowContent: <VipRechargeModal callback={callback} origin={origin} user={user} generateNum={generateNum}/>,
            popupWidth: 'auto',
            popupHeight: 'auto',
            style: {
                width: '770px',
                height: '555px',
                background: '#fff',
                boxShadow: '0px 4px 14px 0px rgba(31,26,27,0.16)',
                borderRadius: '12px 12px 12px 12px',
                left: '0',
                top: '0',
                right: '0',
                bottom: '0',
                margin: 'auto',
                padding: '0',
            },
            popupTitleBarStyle: {
                width: 0,
                height: 0,
                display: 'none',
            },
        };
        emitter.emit('popupWindow', windowInfo);
    }
    const getGenerateNum = async () => {
        const res = await assetManager.getGenerateNum();
        setGenerateNum(res.data);
    };
    useEffect(() => {
        if (user) {
            getGenerateNum();
        }
    }, [user]);

    return (
        <div style={{height:'100%'}}>
            <TgsAiChat
                Modal={Modal}
                Popover={Popover}
                popMessage={message}
                userInfo={user}
                openLoginModel={() => {
                }}
                uploadLimitPopup={limitPopup}
                rechargeModalPopup={rechargeModalPopup}
                editorController={editorController}
                sourceFrom={2} 
                size="small" 
            />
        </div>
    );
};

storeHOC(mapStateToProps, AIChat);

