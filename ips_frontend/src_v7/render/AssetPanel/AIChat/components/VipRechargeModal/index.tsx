import React, { useEffect, useState } from 'react';
import { emitter } from "@src/userComponentV6.0/Emitter"
import RechargeAiPopup from './RechargeAiPopup';

export function VipRechargeModal({ callback, origin, user, generateNum }: { callback: (isCharge: boolean) => void, origin: string, user: any, generateNum: any }) {
    // const { userInfo, isAiVip, generateNum, expireTime} = useSelector((state: any) => {
    //     return state.user;
    // });
    const [number, setNumber] = useState<any>({});

    useEffect(() => {
        setNumber(generateNum);
    }, [generateNum]);



    const unlimited = number?.aiProduceInfo?.ai_design_day_num;
    const showNum = unlimited ? '不限' : number?.total_num;

    const successCallback = async () => {
        callback(true);
    };
    const onCancel = () => {
        emitter.emit('popupClose');
    };
    return (
        <div>
            <div style={{
                position: 'absolute',
                top: '10px',
                right: '10px',
                cursor: 'pointer',
                zIndex: 1000,
            }} onClick={onCancel}>
                <i className="iconfont icon-a-guanbi101"></i>
            </div>
            <RechargeAiPopup
                isAiVip={number?.is_ai_produce_vip}
                produceNum={showNum}
                expireTime={number?.expire_time}
                username={user?.userName}
                avatar={user?.avatar}
                refresh={successCallback}
                onClose={onCancel}
                origin={origin}
            ></RechargeAiPopup>
        </div>
    );
}
