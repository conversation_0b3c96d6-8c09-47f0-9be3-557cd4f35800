$theme-color: #7b5aff;


.recharge_ai_box_aiDesign {
    width: 770px;
    border-radius: 12px;
    overflow: hidden;
    display: flex;
    flex-direction: column;


        .recharge_ai_header {
            width: 100%;
            height: 70px;
            display: flex;
            justify-content: space-between;
        }

        .recharge_uesr_box {
            display: flex;
            margin: 16px 0 16px 0;
            height: 46px;
            .recharge_user_info {
                display: flex;
                flex-direction: column;
                justify-content: space-around;
            }
            .recharge_user_image {
                height: 46px;
                width: 46px;
                border-radius: 100%;
                margin-right: 11px;
            }

            .username {
                font-size: 14px;
                font-weight: 500;
                color: #10255a;
            }

            .remain {
                margin-top: 5px;
                font-size: 12px;
                font-weight: 400;
                color: #10255a;
                display: flex;
                align-items: center;

                &-item {
                    margin-right: 20px;
                }
            }
        }

        .recharge_ai_content {
            flex: 1;
            background: #fff;
            display: flex;
        }

        .recharge_ai_content_left {
            padding: 15px 15px 5px;
            width: 190px;
            background: linear-gradient(137.9deg, #f1f3ff 17.88%, #f7edff 79.03%);
            border-radius: 5px 5px 5px 5px;

            .title_box {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 4px 0 0 0;

                .title_img {
                    width: 150px;
                    height: 26px;
                    margin-bottom: 15px;
                    background-size: 100% 100%;
                }

                .title {
                    font-size: 16px;
                    font-weight: 500;
                    color: #1f1a1b;
                    line-height: 24px;
                }

                .spin {
                    width: 70px;
                    height: 4px;
                    background: $theme-color;
                    border-radius: 2px;
                }
            }

            .content_box {
                margin: 10px 5px;

                .text-box {
                    .text-header {
                        display: flex;
                        align-items: center;
                        padding-bottom: 10px;
                        i {
                            font-size: 18px;
                            color: $theme-color;
                            margin-right: 5px;
                        }
                        .header-title {
                            color: $theme-color;
                        }
                    }
                }

                .ai_recharge_text_item {
                    padding-left: 5px;
                    padding-bottom: 7px;
                    display: flex;

                    .point {
                        width: 4px;
                        height: 4px;
                        background: #7b5aff;
                        border-radius: 100%;
                        margin: 8px 12px 0 0;
                    }

                    .ai_recharge_text {
                        flex: 1;
                        font-size: 12px;
                        font-weight: 400;
                        color: #1f1a1b;
                        line-height: 20px;
                        word-break: keep-all;
                    }

                    .ai_recharge_text_red {
                        font-size: 12px;
                        font-weight: 400;
                        color: #ff3d00;
                        line-height: 20px;
                    }
                }
            }
        }

        .recharge_ai_content_right {
            width: 580px;
            padding: 20px;
            box-sizing: border-box;
        }

        .recharge_ai_price_box {
            display: flex;
            width: 100%;
            height: 190px;
            background: #ffffff;
            justify-content: space-between;
            margin-top: 15px;
        }

        .recharge_ai_pay_box {
            box-sizing: border-box;
            width: 100%;
            height: 220px;
            background: #ffffff;
            border-radius: 6px;
            padding: 20px;
            display: flex;
            margin-top: 20px;
            border: 1px solid #e9e8e8;
        ;

            .recharge_ai_pay_qrcode {
                width: 150px;
                height: 150px;
                border-radius: 0px 0px 0px 0px;
                margin-right: 15px;
                position: relative;
                cursor: pointer;

                .res_mask {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    background: #585858;
                    z-index: 1;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: 400;
                    color: #fff;
                }
            }

            .recharge_ai_pay_message {
                font-size: 12px;
                color: #1f1a1b;
                font-weight: 400;

                .pay_message_tip {
                    display: flex;
                    width: 193px;
                    height: 18px;
                    justify-content: space-between;
                    align-items: center;
                    margin-top: 20px;
                }
            }

            .pay_icon {
                display: inline-block;
                width: 14px;
                height: 14px;
                background-size: 100% 100%;
                margin: 3px;
            }

            .alipay {
                background-image: url('https://s.tuguaishou.com/index_img/editorV7.0/vip_type_8.png');
            }

            .wechat {
                background-image: url('https://s.tuguaishou.com/index_img/editorV7.0/vip_type_7.png');
            }

            .recharge_ai_pay_success {
                height: 100%;
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
                font-size: 12px;
                font-weight: 400;
                color: #1f1a1b;
                line-height: 20px;

                .success_icon {
                    height: 30px;
                    width: 30px;
                    background: #0eb52d;
                    margin-bottom: 7px;
                    color: #fff;
                    border-radius: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }
        }

        .price-text-box {
            width: 5rem;
            height: 2rem;
        }

        .ai_recharge_price {
            width: 260px;
            height: 190px;
            border-radius: 6px;
            border: 1px solid #e9e8e8;
            display: flex;
            flex-direction: column;
            cursor: pointer;
            position: relative;
            margin-right: 16px;
            &:last-child {
                margin-right: 0;
            }

            .point_box {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                color: #1f1a1b;
                border-bottom: 1px solid #e9e8e8;
                padding: 10px 0;

                .title_text {
                    font-size: 14px;
                    font-weight: 500;
                    line-height: 22px;
                }

                .point_num_box {
                    display: flex;
                    align-items: flex-end;

                    .point_num {
                        font-size: 28px;
                        font-weight: 500;
                        line-height: 32px;
                    }

                    .extra {
                        margin: 0 0 7px 5px;
                        width: 60px;
                        height: 20px;
                        background: linear-gradient(137deg, #ef3964 0%, #ef3964 48%, #e47a19 100%);
                        border-radius: 10px 10px 10px 0px;
                        font-size: 12px;
                        font-weight: 500;
                        color: #fff;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                }
            }

            .price_box {
                display: flex;
                align-items: center;
                justify-content: center;
                flex: 1;
                color: #ef3964;
                font-size: 16px;
                font-weight: 500;
                line-height: 24px;
            }

            .ai_recharge_checked_box {
                width: 40px;
                height: 40px;
                position: absolute;
                right: 0;
                bottom: 0;

                .triangle {
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    width: 0;
                    height: 0;
                    border-radius: 6px;
                    border-bottom: 40px solid $theme-color;
                    border-left: 40px solid transparent;
                }

                .icon-duigou {
                    position: absolute;
                    color: #fff;
                    z-index: 1;
                    bottom: 0;
                    right: 0;
                    font-size: 14px;
                }
            }
        }

        .price-text-box {
            width: 50px;
            height: 20px;
            font-weight: 400;
            font-size: 12px;
            text-align: center;
            background-color: #f7f7f7;
            border-radius: 3px 0 5px 0;
            margin-bottom: 11px;
        }

        .price-text-box.price-text-box-checked {
            background-color: #ef3964;
            color: white;
        }

        .price-box {
            margin-bottom: 13px;
            border-radius: 2px 2px 0 0;

            .suggest {
                position: absolute;
                right: 0;
                color: #fff;
                text-align: center;
                font-size: 12px;
                line-height: 30px;
                width: 70px;
                height: 30px;
                border-radius: 0px 6px;
                background: linear-gradient(88deg, rgba(63, 97, 255, 1) 1.03%, rgba(168, 64, 255, 1) 98.37%);
            }
        }

        .price-inner-box {
            padding: 15px;
            &.no-time-limit {
                .price-text-left {
                    .price-text {
                        color: #7b5aff;
                    }
                }
                .price-bottom-box {
                    position: relative;

                    border: 1px solid #ead0ff;
                    background-color: #faf3ff;
                    .price-text-icon {
                        color: #7b5aff;
                        margin-right: 10px;
                        i {
                            margin-right: 3px;
                            color: #7b5aff;
                        }
                    }
                    .price-huobi-color {
                        color: #7b5aff;
                    }
                    .box-line {
                        padding-left: 13px;
                        justify-content: start;
                        .tip-icon {
                            position: absolute;
                            right: 10px;
                            top: 4px;
                            i {
                                font-size: 14px;
                                color: #ead0ff;
                            }
                        }
                    }
                }
            }
        }

        .price-text-right,
        .price-text-left {
            width: 50%;
            color: #1f1a1b;
        }

        .price-text-center {
            display: flex;
            align-items: flex-end;
            margin-top: 20px;
        }

        .price-text {
            font-size: 16px;
        }

        .price-text-color {
            line-height: 1.5;
            font-size: 16px;
            color: $theme-color;
        }

        .price-num {
            font-weight: 700;
            font-size: 18px;
        }

        .price-bottom-box {
            margin-top: 15px;
            width: 216px;
            height: 60px;
            border-radius: 6px;
            border: 1px solid #ffd8bb;
            background-color: #fff7f0;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            .box-line {
                display: flex;
                justify-content: space-around;
                i {
                    color: #ffd8bb;
                }
            }

            .price_icon {
                display: inline-block;
                width: 14px;
                height: 14px;
            }
            .price-huobi-box {
                //文字对齐中线
                line-height: 22px;
            }
            .price-text-icon {
                display: flex;
                line-height: 24px;
                i {
                    font-size: 14px;
                    color: #ff3d00;
                }
            }
        }

        .price-huobi {
            font-size: 12px;
        }

        .price-huobi-color {
            color: #ff3d00;
        }

        .price {
            font-size: 36px;
            font-weight: 600;
            line-height: 36px;
        }

        .price-icon {
            font-size: 14px;
            display: inline-block;
            color: white;
        }

        .ant-checkbox-inner {
            width: 14px !important;
            height: 14px !important;
        }

        .ant-checkbox-inner:after {
            transform: rotate(45deg) scale(1) translate(-60%, -45%) !important;
        }

        .refresh-btn {
            width: 60px;
            height: 30px;
            border-radius: 6px;
            font-size: 14px;
            margin-top: 15px;
            text-align: center;
            line-height: 30px;
            background: $theme-color;
        }
    
}
