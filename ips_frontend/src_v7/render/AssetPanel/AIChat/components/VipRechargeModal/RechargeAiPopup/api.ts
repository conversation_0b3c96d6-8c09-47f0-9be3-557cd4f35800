import { ipsApi } from '@src/userComponentV6.0/IPSConfig';

const options: { credentials: 'include' } = {
  credentials: 'include',
};

export const checkeAIPayStatus = (time: number) => {
  let formData = new FormData();
  formData.append('time', '' + time);
  return fetch(ipsApi('/pay/check-pay'), {
    method: 'POST',
    body: formData,
    ...options,
  }).then(r => r.json());
};

export const getPayQrCode = ({ type, origin = 'ai_design_poster_pay_vip' }: { type: string, origin?:string }) => {
  let formData = new FormData();
  formData.append('type', type);
  formData.append('pay_origin', origin);
  formData.append('classify', '17');
  formData.append('update', '0');

  return fetch(ipsApi('/pay/get-qrcode'), {
    method: 'POST',
    body: formData,
    ...options,
  }).then(r => r.json());
};
