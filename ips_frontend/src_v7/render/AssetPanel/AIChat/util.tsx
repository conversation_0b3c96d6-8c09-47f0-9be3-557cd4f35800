// ✅ 将纯数组数据转成 asset 表格结构
export function createTableAssetFromArray(data: string[][], options?: {
  defaultRowHeight?: number;
  defaultColWidth?: number;
  fontSize?: number;
}): any {
  const rows = data.length;
  const cols = data[0]?.length || 0;

  const rowHeights = Array(rows).fill(options?.defaultRowHeight || 56);
  const colWidths = Array(cols).fill(options?.defaultColWidth || 136);

  const cell: any[][] = [];
  const text: any[][] = [];

  for (let r = 0; r < rows; r++) {
    const cellRow: any[] = [];
    const textRow: any[] = [];

    for (let c = 0; c < cols; c++) {
      cellRow.push({
        lineStyle: ["solid", "solid", "solid", "solid"],
        lineColor: Array(4).fill({ r: 218, g: 218, b: 218, a: 1 }),
        lineWidth: [1, 0, 1, c === cols - 1 ? 1 : 0],
        background: {
          color: { r: 255, g: 255, b: 255, a: 1 }
        }
      });

      textRow.push({
        content: [data[r][c]],
        alpha: 1,
        fontSize: options?.fontSize || 36,
        fontWeight: r === 0 ? 600 : 400,
        textAlign: "center",
        lineHeight: 1.3,
        fontFamily: "fnsyhtRegular",
        color: { r: 51, g: 51, b: 51, a: 1 }
      });
    }

    cell.push(cellRow);
    text.push(textRow);
  }

  return {
    asset: {
      meta: {
        type: "table",
        className: "table",
        index: 1,
        name: "",
        hash: "autogen-" + Date.now().toString(36),
        v: 2,
        deleted: 0,
        rt_rntime: 0,
        rt_page_index: 0,
        rt_page_assets_index: 0
      },
      transform: {
        posX: 0,
        posY: 0,
        rotate: 0,
        horizontalFlip: false,
        verticalFlip: false
      },
      attribute: {
        opacity: 100,
        width: colWidths.reduce((a, b) => a + b, 0),
        height: rowHeights.reduce((a, b) => a + b, 0),
        cell,
        cellSize: {
          row: rowHeights,
          col: colWidths
        },
        text,
        resId: "autogen"
      }
    },
    canvas: {
      width: 1242,
      height: 1660,
      scale: 1,
      x: 0,
      y: 0,
      backgroundColor: { r: 255, g: 255, b: 255, a: 1 },
      showUnit: "px"
    },
    assetProps: {},
    index: 0,
    isMoving: false,
    isActive: true,
    hideDragTable: true
  };
}


// ✅ 从 Excel 或复制的纯文本表格中提取二维数组数据
export function parseClipboardTable(text: string): string[][] {
  return text
    .trim()
    .split(/\r?\n/) // 每一行
    .map(line => line.split(/\t|\s{2,}/).map(cell => cell.trim()));
}

// 用法示例：
// const excelText = `A\tB\tC\n1\t2\t3`;
// const array = parseClipboardTable(excelText);
// const asset = createTableAssetFromArray(array);
