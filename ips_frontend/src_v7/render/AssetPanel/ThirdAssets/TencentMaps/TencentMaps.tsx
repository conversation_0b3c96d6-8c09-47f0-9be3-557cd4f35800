import React, { PureComponent } from 'react';

import { assetManager } from '@component/AssetManager';
import { ETool } from '@v7_logic/Enum';
import { TAsset, TMap } from '../type'
import { EmptyPage } from '../components/EmptyPage'
import { AssetItem } from '../components/AssetItem'
import { SearchBox, Loading } from '@v7_render/Ui';
//@ts-ignore
import InfiniteScroll from 'react-infinite-scroller';

export function DisplayTencentMapsToll(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.TENCENTMAPS, nav: ETool.TENCENTMAPS};
}
interface propsStruct {
    isActive?: boolean;
}

interface stateStruct {
    imgListLoading: boolean;
    imgList: TAsset[];

    resLoading: boolean;
    keyword: string;
    pageIndex: number;
}

export class TencentMaps extends PureComponent<propsStruct, stateStruct> {
    constructor(props: propsStruct) {
        super(props);
        this.state = {
            imgListLoading: true,
            imgList: [],
            keyword: '',

            resLoading: true,
            pageIndex: 0,
        };
        this.getImgList();
    }
    getImgList = () => {
        this.setState({ resLoading: false })
        const { pageIndex, imgList, keyword } = this.state;
        let oldImgList = imgList;
        if (pageIndex === 0) {
            this.setState({ imgListLoading: true });
            oldImgList = []
        }
        const searchKeyword = keyword || '北京天安门'
        assetManager.searchTencentMap(pageIndex + 1, searchKeyword).then((data) => {
            data.json().then((resultData) => {
                if (resultData.code == 1) {
                    if (resultData.data.length === 10) {
                        this.setState({ resLoading: true })
                    }
                    const resData = resultData.data.map((item: TMap) => {
                        return {
                            ...item,
                            // id: item.id,
                            id: 'loading_' + item.id,
                            title: item.title,
                            preview: item.preview,
                            width: item.width|| 1280,
                            height: item.height|| 1280,
                        }
                    })
                    this.setState({ imgList: oldImgList.concat(resData), pageIndex: pageIndex + 1 })
                } else if (resultData.code === 0) {
                    this.setState({ imgList: [] })
                }
                this.setState({ imgListLoading: false })
            });
        });
    }
    onSearch = (value: string) => {
        this.setState({ pageIndex: 0, keyword: value })
        setTimeout(() => {
            this.getImgList();
        }, 0)
    }
    loadMore = () => {
        this.getImgList();
    }

    render(): JSX.Element {
        const { imgListLoading, imgList, resLoading } = this.state;
        return (
            <div className='tencentMaps_Panel'>
                <SearchBox onSearch={this.onSearch} placeholder='搜索地址' />
                <div className='tencentMapsPage'>
                    {imgListLoading ? <Loading /> : (
                        imgList.length > 0 ? (
                            <InfiniteScroll
                                pageStart={0}
                                hasMore={resLoading}
                                loadMore={this.loadMore.bind(this)}
                                threshold={2000}
                                useWindow={false}
                                initialLoad={false}
                            >
                                {imgList.map((item) => {
                                    return (
                                        <div className='mapCard' key={item.id}>
                                            <AssetItem key={item.id} thirdType='map' height={272} assetItem={item}></AssetItem>
                                            <div className='title'>{item.title}</div>
                                            {item.address && <div className='info'>地址：{item.address}</div>}
                                            {item.tel && <div className='info'>电话：{item.tel}</div>}
                                        </div>
                                    )
                                })}
                            </InfiniteScroll>
                        ) : (
                            <EmptyPage />
                        )
                    )}
                </div>
            </div>
        )
    }
}
