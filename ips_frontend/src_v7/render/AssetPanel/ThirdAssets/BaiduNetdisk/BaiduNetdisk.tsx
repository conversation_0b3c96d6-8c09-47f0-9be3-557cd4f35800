import React, { PureComponent } from 'react';
import { assetManager } from '@component/AssetManager';
import { ETool } from '@v7_logic/Enum';
import { ErrorBoundaryDecorator } from '@v7_render/ErrorBoundaryHOC';
import { ThirdLogin } from '../components/ThirdLogin';
import { TAsset, TNetdisk } from '../type'
import { EmptyPage } from '../components/EmptyPage'
import { SearchBox,Loading } from '@v7_render/Ui';
import { AssetList } from '../components/AssetList';
//@ts-ignore
import InfiniteScroll from 'react-infinite-scroller';

import '../scss/thirdAssets.scss'

export function DisplayBaiduNetdiskToll(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.BAIDUNETDISK, nav: ETool.BAIDUNETDISK};
}
interface propsStruct {
    isActive?: boolean;
}

interface stateStruct {
    imgListLoading: boolean;
    loginLoading: boolean;
    isLogin: boolean;
    imgList: TAsset[];

    resLoading: boolean;
    keyword: string;
    pageIndex: number;
}

@ErrorBoundaryDecorator()
export class BaiduNetdisk extends PureComponent<propsStruct, stateStruct> {
    loginTime: NodeJS.Timeout
    constructor(props: propsStruct) {
        super(props);
        this.state = {
            imgListLoading: true,
            loginLoading: true,
            isLogin: false,
            imgList: [],
            keyword: '',

            resLoading: true,
            pageIndex: 0,
        };
        this.onLogin(true);
    }

    onLogin = (init = false) => {
        assetManager.getBaiduNetdiskAuthUrl().then((data) => {
            data.json().then((resultData) => {
                this.setState({ loginLoading: false })
                if (resultData.code == 1 && !init) {
                    const authUrl = resultData.data.authUrl;
                    const windowName = 'BaiduNetDiskAuthorization';
                    const windowOptions = 'toolbar=no, menubar=no, width=800, height=600';
                    const popup = window.open(authUrl, windowName, windowOptions);
                    const left = (screen.width / 2) - (800 / 2);
                    const top = (screen.height / 2) - (600 / 2);
                    popup.moveTo(left, top);
                    popup.resizeTo(800, 600);
                    this.setLoginStatus();
                } else if (resultData.code == 2) {
                    // 已经授权
                    this.setState({ isLogin: true })
                    this.getImgList()
                    if (this.loginTime) {
                        clearInterval(this.loginTime);
                    }
                }
            });
        });
    }
    componentWillUnmount(): void {
        if (this.loginTime) {
            clearInterval(this.loginTime);
        }
    }

    setLoginStatus = () => {
        if (this.loginTime) {
            clearInterval(this.loginTime);
        }
        this.loginTime = setInterval(() => {
            assetManager.getBaiduNetdiskAuthUrl().then((data) => {
                data.json().then((resultData) => {
                    if (resultData.code == 2) {
                        // 已经授权
                        this.setState({ isLogin: true })
                        this.getImgList()
                        if (this.loginTime) {
                            clearInterval(this.loginTime);
                        }
                    }
                });
            });
        }, 1000)
    }


    getImgList = () => {
        this.setState({ resLoading: false })
        const { pageIndex, imgList, keyword } = this.state;
        let oldImgList = imgList;
        if (pageIndex === 0) {
            this.setState({ imgListLoading: true })
            oldImgList = []
        }
        assetManager.getBaiduNetdiskImgList(pageIndex + 1, keyword).then((data) => {
            data.json().then((resultData) => {
                if (resultData.code == 1) {
                    if (resultData.data.length === 20) {
                        this.setState({ resLoading: true })
                    }
                    const support = ['jpeg', '.jpg', '.png', 'JPEG', '.JPG','.PNG']
                    const resData = resultData.data.filter((item: TNetdisk) => {
                        const lastFour = item.filename.slice(-4);
                        const small = item.size < (20 * 1024 * 1024)
                        
                        return support.includes(lastFour) && item.height && item.height && small;
                    })
                    const newResData = resData.map((item: TNetdisk) => {
                        return {
                            ...item,
                            id: item.fs_id,
                            title: item.server_filename,
                            preview: item.thumbs.url3,
                            width: item.width || 1280,
                            height: item.height || 1280,
                        }
                    })
                    this.setState({ imgList: oldImgList.concat(newResData), pageIndex: pageIndex + 1 })
                } else if (resultData.code === 0) {
                    this.setState({ imgList: [] })
                }
                this.setState({ imgListLoading: false })
            });
        });
    }
    onSearch = (value: string) => {
        this.setState({ pageIndex: 0, keyword: value })
        setTimeout(() => {
            this.getImgList();
        }, 0)
    }
    loadMore = () => {
        this.getImgList();
    }

    render(): JSX.Element {
        const { isLogin, loginLoading, imgListLoading, imgList, resLoading } = this.state;

        return (
            <div className='baiduNetdisk_Panel'>
                {loginLoading && <Loading />}
                {!loginLoading && <>
                    {!isLogin ? (
                        <ThirdLogin id='48' onLogin={this.onLogin} />
                    ) : (<>
                        <SearchBox onSearch={this.onSearch} placeholder='搜索图片' />
                        <div className='baiduNetdiskPage'>
                            {imgListLoading ? <Loading /> : (
                                    imgList.length > 0 ? (
                                    <InfiniteScroll
                                        pageStart={0}
                                        hasMore={resLoading}
                                        loadMore={this.loadMore.bind(this)}
                                        threshold={2000}
                                        useWindow={false}
                                        initialLoad={false}
                                    >
                                        <AssetList assetsList={imgList} />
                                    </InfiniteScroll>
                                ) : (
                                    <EmptyPage baidu/>
                                )
                            )}
                        </div>
                    </>
                    )}
                </>}
            </div>
        )
    }
}
