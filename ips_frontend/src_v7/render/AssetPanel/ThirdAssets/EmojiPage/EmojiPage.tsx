import React, { PureComponent } from 'react';
import { assetManager } from '@component/AssetManager';
import { ETool } from '@v7_logic/Enum';
import LazyLoad from 'react-lazy-load4';
import { EmptyPage } from '../components/EmptyPage'
import { emitter } from '@component/Emitter';
import { SearchBox } from '@v7_render/Ui';

import '../scss/emojiPage.scss'

export function DisplayEmojiPageToll(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.EMOJIPAGE, nav: ETool.EMOJIPAGE};
}
interface propsStruct {
    isActive?: boolean;
}

interface emoji {
    id: string;
    group_name: string;
    keyword: string;
    preview: string;
    svg_preview: string;
    preview64?: string;
    rank: string;
}
interface emojiList {
    title: string;
    emojiList: emoji[]
}

interface stateStruct {
    emojiData: emojiList[];
    searchValue: string;
    loading: boolean;
}

export class EmojiPage extends PureComponent<propsStruct, stateStruct> {
    constructor(props: propsStruct) {
        super(props);
        this.state = {
            emojiData: [],
            searchValue: '',
            loading: true,
        };
        this.onSearch('')
    }

    onSearch = (value: string) => {
        this.setState({ searchValue: value, loading: true })
        assetManager.getGoogleEmoji(value).then((data) => {
            data.json().then((resultData) => {
                if (resultData.code == 1) {
                    const arrData = [];
                    for (const key in resultData.data) {
                        if (resultData.data.hasOwnProperty(key)) {
                            arrData.push({ title: key, emojiList: resultData.data[key] });
                        }
                    }
                    this.setState({ emojiData: arrData })
                } else {
                    this.setState({ emojiData: [] })
                }
                this.setState({ loading: false })
            });
        });
    }

    changeData(data: emoji[]) {
        const arr: emoji[][] = [];
        let num = 0;
        for (let i = 0; i < data.length; i++) {
            if (!arr[num] || arr[num].length === 0) {
                arr[num] = [data[i]];
            } else {
                arr[num].push(data[i]);
            }
            if ((i + 1) % 5 == 0) {
                num++;
            }
        }
        return arr;
    }

    addAsset = (emoji: emoji, e: React.MouseEvent) => {
        const asset = {
            ...emoji,
            title: emoji.group_name + emoji.id,
            height: 48,
            width: 48,
            source_width: 48,
            source_height: 48,
            image_url: emoji.preview,
            rt_isNowAdd: true,
        }
        emitter.emit('ListAddElement', asset);
    }

    dragAddDownEvent = (emoji: emoji, e: React.MouseEvent) => {
        const asset = {
            ...emoji,
            title: emoji.group_name + emoji.id,
            height: 48,
            width: 48,
            source_width: 48,
            source_height: 48,
            image_url: emoji.preview,
            rt_isNowAdd: true,
        }
        emitter.emit('ListDragAddDown', asset, 'pic', e);
    }

    render(): JSX.Element {
        const { emojiData, loading } = this.state;

        return (
            <div className='emoji_Panel'>
                {/* <SearchBox placeholder='搜索表情符号' onSearch={this.onSearch} /> */}
                {loading ? (
                    <div></div>
                ) : (
                    emojiData.length > 0 ? (
                        <div className='emojiContent'>
                            {emojiData.map((item, index) => {
                                if (item.emojiList.length > 0) {
                                    const rowList = this.changeData(item.emojiList)
                                    return (<div key={index}>
                                        <div className='title'>{item.title}</div>
                                        <div className='emojiList'>
                                            {rowList.map((row, rowIndex) => {
                                                return (<div className='row' key={rowIndex}>
                                                    {row.map((emoji) => {
                                                        return (<div className='emojiItem'
                                                            key={emoji.id}
                                                            onClick={this.addAsset.bind(this, emoji)}
                                                            onMouseDown={this.dragAddDownEvent.bind(this, emoji)}
                                                        >
                                                            <LazyLoad
                                                                className="lazyImg"
                                                                width={56}
                                                                height={56}
                                                                offset={400}
                                                                key={emoji.id}
                                                            >
                                                                <div className='item'>
                                                                    <img src={emoji.svg_preview} />
                                                                </div>
                                                            </LazyLoad>
                                                        </div>)
                                                    })}
                                                </div>)
                                            })}
                                        </div>
                                    </div>);
                                } else {
                                    <></>
                                }
                            })}
                        </div>
                    ) : (
                        <EmptyPage />
                    )
                )}
            </div>
        )
    }
}
