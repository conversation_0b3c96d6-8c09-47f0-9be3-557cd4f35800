import React, { PureComponent } from 'react'
import './scss/musicTab.scss'
import { MusicList } from './MusicList'
import { MusicHistory } from './MusicHistory'
// import { MusicFav } from './MusicFav'
import { ETool } from '@v7_logic/Enum';
import { Tabs } from '@v7_render/Ui';
import { Dialog } from '@v7_render/Ui/Dialog'
import { assetManager } from '@src/userComponentV6.0/AssetManager'
import { unionBy } from 'lodash-es';
import { IAudio, Imusic, IPageAttr } from '@v7_logic/Interface';
import { AudioLogic } from '@v7_logic/AudioLogic';
import { storeDecorator } from '@v7_logic/StoreHOC';
import { TemplateLogic } from '@v7_logic/TemplateLogic';
import { CommercialUse } from '@v7_render/CommercialUse';

export function DisplayMusicTool(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.MUSIC, nav: ETool.MUSIC};
}

const style = {
    width: "500px",
    background: "linear-gradient(180deg, #FFE6D7 0%, #FFFFFF 100%)",
    borderRadius: "10px"
}

interface IMusicTabProps {
    pageAttr?: IPageAttr;
}

interface ImusicState {
    isActive: boolean;
    activeKey: 'musicList' | 'history' | 'favor';
    visible: boolean;
    payVisble: boolean;
    musicData: Imusic[]
    page: number;
    keyWord: string;
    has_count: number;
    vip_type: number;
}

@storeDecorator((state: { [key: string]: any }) => {
    return {
        pageAttr: state.onCanvasPainted.pageAttr,
    };
})
export class MusicTab extends PureComponent<IMusicTabProps ,ImusicState> {
    addAudio: IAudio; // 缓存选中的音乐数据
    musicRef = React.createRef<MusicList>();
    musicHisRef = React.createRef<MusicHistory>();
    state: Readonly<ImusicState> = {
        isActive: false,
        activeKey: 'musicList',
        visible: false, // 扣费弹框
        payVisble: false, // 充值弹框
        musicData: [],
        page: 1,
        keyWord: '',
        has_count: 0,
        vip_type: 0
    }
    isLoadingMore = false
    isSearch = false

    constructor(props:any) {
      super(props)
      this.checkMusicUserVip()
    }

    async getMusicList(pageIndex?: number):Promise<void> {
        try {
            const { page, keyWord } = this.state;
            const resData = await assetManager.searchMusicData(pageIndex || page, keyWord)
            if (resData.stat === 1) {
                if (this.isSearch) {
                    this.setState({
                        musicData: [],
                    })
                }
                this.isLoadingMore = false
                this.isSearch = false
                this.calcList(resData.msg.data)
            }
        } catch (error) {
            console.error(error)
        }
    }


    calcList = (musicData: Imusic[]) => {
        if (musicData?.length === 0) {
            return;
        }

        const list = unionBy(this.state.musicData, musicData, 'id');

        for (const i of list) {
            i.isFav = i.is_fav;
            const t = Math.ceil(Number.parseFloat(i.total_time) / 1000);
            const s = t % 60;
            const m = Math.floor(t / 60);
            i.total_time_string = `${m < 10 ? '0' + m : m}:${s < 10 ? '0' + s : s}`;
        }
        this.setState({
            musicData: list,
        });
    };

    onScrollFn = (isScroll: boolean, pageIndex: number):void => {
        this.isLoadingMore = isScroll
        this.getMusicList(pageIndex)
    }

    componentDidMount(): void {
        this.getMusicList()
    }

    async checkMusicUserVip(): Promise<void> {
        try {
            const resData = await assetManager.checkMusicUserVip()
            if (resData.stat === 1) {
                this.setState(() => {
                    return {
                        has_count: resData.data.has_count,
                        vip_type: resData.data.vip_type
                    }
                })
            }
        } catch (error) {
            console.error(error)
        }
    }

    onChangeSearchText = (e: React.FormEvent):void => {
        const string = (e.currentTarget as HTMLInputElement).value;
        this.setState({
            keyWord: string,
        });
    }

    clickSearch = (e: React.MouseEvent):void => {
        const { activeKey } = this.state;
        this.isSearch = true
        if (activeKey === 'musicList') {
            this.getMusicList()
        } else if (activeKey === 'history') {
            // console.log(this.state.keyWord, 'history')
        } else {
            // console.log(this.state.keyWord, 'favor')
        }
        assetManager.setPv_new(5571)
        assetManager.setPv_new(7101, {
            additional: {
                s1: '音乐素材',
                s2: this.state.keyWord,
            }
        })
        e.stopPropagation();
        e?.nativeEvent.stopPropagation();
    }

    onChangeKey = (key: 'musicList' | 'history' | 'favor'):void => {
        this.setState({
            activeKey: key,
        });
        if (key === 'musicList') {
            assetManager.setPv_new(5573, {additional: {s0: '1'}})
        } else if (key === 'history') {
            assetManager.setPv_new(5573, {additional: {s0: '2'}})
        }
    };

    onFormSubmit = (e: React.FormEvent):void => {
        e.preventDefault();
        e?.nativeEvent.preventDefault();
    }; 

    handleSubmit = async (isClose: boolean, addAudio?: IAudio): Promise<void> => {
        await this.checkMusicUserVip()
        const {has_count, vip_type} = this.state;

        if (has_count > 0 && vip_type > 1) {
            this.setState({visible: isClose})
            assetManager.setPv_new(5577)
        } else {
            this.setState({payVisble: isClose})
            assetManager.setPv_new(5579)
        }

        if (addAudio) {
            this.addAudio = addAudio;
        }

        if (!isClose && this.addAudio) {
            try {
                const resData = await assetManager.payMusicVip(this.addAudio.resId)
                if (resData.stat === 1) {
                    this.addAudio && AudioLogic.setUserAudio(this.addAudio, false);
                     // eslint-disable-next-line @typescript-eslint/no-unused-vars
                    const res = await assetManager.setMusicHistory(this.addAudio.resId)
                    assetManager.setPv_new(5578, {additional: {s0: '1'}})
                }
            } catch (error) {
                console.error(error)
            }
        }
    }

    closeDialog = (isClose: boolean): void => {
        const {has_count, vip_type} = this.state;
        this.addAudio = undefined
        if (has_count > 0 && vip_type > 1) {
            this.setState({visible: isClose})
            assetManager.setPv_new(5578, {additional: {s0: '2'}})
        } else {
            this.setState({payVisble: isClose})
            assetManager.setPv_new(5580)
        }
    }

    pauseOtherMiuse = (activeKey: 'musicList' | 'history' | 'favor') => {
        if (activeKey === 'musicList') {
            this.musicHisRef.current?.pauseMusic()
        } else if (activeKey === 'history') {
            this.musicRef.current?.pauseMusic()
        }
    }

    skipCompany() {
      assetManager.setPv_new(7742)
      window.open('https://818ps.com/dash/firm-intro?origin=ue-music');
    }
    
    skipPri() {
      assetManager.setPv_new(7741)
      window.open('https://818ps.com/dash/vip-spec-commercial?origin=ue-music');
    }

    render() {
        const { isActive, musicData, visible, payVisble, activeKey, has_count, vip_type } = this.state;
        console.log('has_count', has_count)
        const hasAudio = TemplateLogic.hasAudio(this.props.pageAttr)

        return (
            <div className='official-music-panel'>
                {/* <CommercialUse text="商用会员所有音乐素材可放心商用" /> */}
                <a className='authorization' href="https://houzi8.com/" target="_blank" rel="noreferrer">
                    <img src="//s.tuguaishou.com/img/logo-houziyinyue.svg" />
                    <div className='authorization-text'>猴子音悦，版权好音乐！</div>
                    <i className="iconfont icon-xiangyou1"></i>
                </a>
                {/* <Tabs isActive={isActive} minusLeft={20} defaultActive='musicList' tabs={[{
                    show: true,
                    key: 'musicList',
                    title: '音乐素材',
                    content: <MusicList 
                                ref={this.musicRef}
                                vip_type={vip_type}
                                hasAudio={hasAudio}
                                musicData={ musicData } 
                                isLoadingMore={this.isLoadingMore} 
                                keyWord={this.state.keyWord}
                                openDialogFn={this.handleSubmit}
                                pauseOtherMiuse={this.pauseOtherMiuse}
                                onScrollFn={this.onScrollFn}/>,
                },
                {
                    show: true,
                    key: 'history',
                    title: '最近使用',
                    content: <MusicHistory ref={this.musicHisRef} hasAudio={hasAudio} pauseOtherMiuse={this.pauseOtherMiuse} openDialogFn={this.handleSubmit} activeKey={activeKey} />,
                },
                // {
                //     key: 'favor',
                //     title: '收藏',
                //     content: <MusicFav keyWord={keyWord} activeKey={activeKey} />,
                //     setPv: {
                //         title: {
                //             // hover: () => ,
                //             // click: () => ,
                //         }
                //     }
                // }
                ]}
                search={
                  <>
                    <form className="official-music-panel-search-form" onSubmit={this.onFormSubmit}>
                        <input
                            className="official-music-panel-search-input"
                            type="text"
                            placeholder="在这里搜索音频素材"
                            value={this.state.keyWord}
                            onChange={this.onChangeSearchText}
                        />
                        <button
                            className="official-music-panel-search-button"
                            type="button"
                            onClick={this.clickSearch}
                        >
                            搜索
                        </button>
                    </form>
                    { vip_type == 0 && <div className="vip-info-line">
                      音乐商用请购买对应商用授权
                      <span onClick={this.skipCompany} className="vip-tag">
                        企业商用
                      </span>
                      <span onClick={this.skipPri} className="vip-tag">
                        个人商用
                      </span>
                  </div>}
                  </>
                }
                onChangeKey={this.onChangeKey}>
                </Tabs> */}
                <>
                    <form className="official-music-panel-search-form" onSubmit={this.onFormSubmit}>
                        <input
                            className="official-music-panel-search-input"
                            type="text"
                            placeholder="在这里搜索音频素材"
                            value={this.state.keyWord}
                            onChange={this.onChangeSearchText}
                        />
                        <button
                            className="official-music-panel-search-button"
                            type="button"
                            onClick={this.clickSearch}
                        >
                            搜索
                        </button>
                    </form>
                    { vip_type == 0 && <div className="vip-info-line">
                      音乐商用请购买对应商用授权
                      <span onClick={this.skipCompany} className="vip-tag">
                        企业商用
                      </span>
                      <span onClick={this.skipPri} className="vip-tag">
                        个人商用
                      </span>
                  </div>}
                </>
                <MusicList 
                    ref={this.musicRef}
                    vip_type={vip_type}
                    hasAudio={hasAudio}
                    musicData={ musicData } 
                    isLoadingMore={this.isLoadingMore} 
                    keyWord={this.state.keyWord}
                    openDialogFn={this.handleSubmit}
                    pauseOtherMiuse={this.pauseOtherMiuse}
                    onScrollFn={this.onScrollFn}/>
                {/* 抠次数弹框 */}
                {visible && <Dialog 
                    visible={visible} 
                    inHeadBtn={true} 
                    isHeader={true} 
                    closeDialog={this.closeDialog}
                    handleSubmit={this.handleSubmit}>
                    <div className='pay-container-box'>
                        <div className='pay-box'>
                            <div className='count-text'>今天剩余次数：<span className='pay-count'>{has_count > 200 ? '海量': has_count}</span></div>
                            <div className='count-text'>本次消费次数：<span className='pay-count'>1</span></div>
                        </div>
                    </div>
                </Dialog>}
                {/* 充值弹框 */}
                {payVisble && <Dialog 
                    visible={payVisble} 
                    inHeadBtn={false} 
                    outHeadBtn={true}
                    isHeader={false}
                    isFooter={false}
                    style={style}
                    closeDialog={this.closeDialog}>
                    <div className='pay-content-box'>
                        <div className='pay-content-header'>继续添加VIP音乐？成为音乐VIP吧！</div>
                        <div className='pay-title'>图怪兽·会打字就能用的作图神器</div>
                        <div className='pay-header-detail'>
                            <div className='count-text-vip'>尊享 · <span className='pay-count-text'>最新音乐每月尊享</span></div>
                            <div className='count-text-vip'>精选 · <span className='pay-count-text'>海量音乐全景覆盖</span></div>
                            <div className='count-text-vip'>商用 · <span className='pay-count-text'>热门音乐商用无忧</span></div>
                        </div>
                        <div className='pay-link'><a href="https://818ps.com/dash/pay-music" target='_blank' rel="noreferrer">立即开通，使用VIP音乐</a></div>
                    </div>
                </Dialog>}
            </div>
        )
    }
}
