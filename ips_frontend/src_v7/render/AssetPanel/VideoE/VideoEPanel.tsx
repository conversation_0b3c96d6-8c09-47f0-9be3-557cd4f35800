import { assetManager } from '@src/userComponentV6.0/AssetManager';
import { ETool } from '@v7_logic/Enum';
import { PanelSearch, Tabs } from '@v7_render/Ui';
import React, { PureComponent } from 'react';
import { VideoEFavor } from './VideoEFavor';
import { VideoEHistory } from './VideoEHistory';
import { VideoEList } from './VideoEList';
import './scss/VideoEPanel.scss';
import { unionBy } from 'lodash-es';
import { TOfficialVideoE } from './VideoEItem';
import { klona as cloneDeep } from 'klona';
import { storeDecorator } from '@v7_logic/StoreHOC';
import { CommercialUse } from '@v7_render/CommercialUse';
import { IStoreState } from '@v7_store/redux/store';

export function DisplayVideoETool(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.VIDEOE, nav: ETool.VIDEOE};
}

interface IVideoEPanelProps {
    user?: {
        userId: string;
    };
}

interface IVideoEPanelState {
    activeKey: 'category' | 'history' | 'favor';
    searchText: string;
    categories: {
        id: string;
        name: string;
        kw: string;
        deleted: '0' | '1';
        sort: string;
        type: string;
        asset: {
            tag_id: string;
            asset_id: string;
        }[];
        asset_info: TOfficialVideoE[];
    }[];
    searchResults: TOfficialVideoE[];
    isSearching: boolean;
    pageIndex: number;
    pageSize: number;
}

/**
 * 视频素材列表，仅限支持 png 序列
 */
@storeDecorator((state: IStoreState) => {
    return {
        user: state.onCanvasPainted.user,
    };
})
export class VideoEPanel extends PureComponent<IVideoEPanelProps, IVideoEPanelState> {
    targetWidth = 120;
    isfetching = false;

    state: Readonly<IVideoEPanelState> = {
        activeKey: 'category',
        searchText: '',
        categories: [],
        searchResults: [],
        isSearching: false,
        pageIndex: 1,
        pageSize: 40,
    };

    onChangeKey = (key: 'category' | 'history' | 'favor') => {
        this.setState({
            activeKey: key,
        });
    };

    onFormSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        e?.nativeEvent.preventDefault();
    };

    onChangeSearchText = (e: React.FormEvent) => {
        const string = (e.currentTarget as HTMLInputElement).value;
        this.setState({
            searchText: string,
        });
    };

    componentDidMount(): void {
        this.getRecommend();
    }

    getRecommend = async () => {
        try {
            if (this.state.searchText?.length === 0) {
                this.setState({
                    isSearching: false,
                });
                const res = await assetManager.getOfficialVideoERecommend();
                if (res.stat === 1) {
                    (res.msg as IVideoEPanelState['categories']).forEach((c) => {
                        c.asset_info.forEach((v) => {
                            const t = Math.ceil(Number.parseFloat(v.duration) / 1000);
                            const s = t % 60;
                            const m = Math.floor(t / 60);
                            v.rt_duration_string = `${m < 10 ? '0' + m : m}:${s < 10 ? '0' + s : s}`;
                        });
                    });
                    this.setState({
                        categories: res.msg,
                    });
                } else {
                    console.error('get official videoe recommend fail: ', res);
                }
            }
        } catch (error) {
            console.error('get official videoe recommend fail: ', error);
        }
    };

    search = async (e?: React.MouseEvent, pageIndex?: number) => {
        if (this.isfetching) {
            return;
        }
        this.isfetching = true;
        try {
            if (this.state.searchText?.length > 0) {
                this.setState({
                    isSearching: true,
                });
                if (pageIndex === 1) {
                    this.setState({
                        pageIndex: 1,
                        searchResults: [],
                    });
                }
                const res = await assetManager.searchOfficialVideoE(
                    pageIndex || this.state.pageIndex,
                    this.state.searchText,
                );
                if (res.stat === 1) {
                    if (res.msg?.length > 0) {
                        this.setState({
                            pageIndex: this.state.pageIndex + 1,
                        });
                        this.calcList(res.msg);
                    }
                } else {
                    console.error('search official videoe fail: ', res);
                }
            }
        } catch (error) {
            console.error('search official videoe fail: ', error);
        }
        this.isfetching = false;
    };

    searchMore = () => {
        if (this.state.isSearching) {
            this.search();
        }
    };

    updataFavor = (id: string, fav: 0 | 1) => {
        if (this.state.isSearching) {
            const list = cloneDeep(this.state.searchResults);
            const item = list.find((i) => i.id === id);
            if (item) {
                item.isFav = fav;
                item.is_fav = fav;
                this.setState({
                    searchResults: list,
                });
            }
        } else {
            const categories = cloneDeep(this.state.categories);
            for (const i in categories) {
                for (const v of categories[i].asset_info) {
                    if (v.id === id) {
                        v.isFav = fav;
                        break;
                    }
                }
            }
            this.setState({
                categories,
            });
        }
    };

    searchCategory = (kw: string) => {
        this.setState(
            {
                searchText: kw,
                isSearching: true,
            },
            () => {
                this.search(undefined, 1);
            },
        );
    };

    clickSearch = () => {
        if (this.state.searchText?.length > 0) {
            this.search(undefined, 1);
        } else {
            this.getRecommend();
        }
        if (this.state.activeKey !== 'category') {
            const tabDom = document.querySelector<HTMLDivElement>('.official-videoe-panel #tgs-tabs-title-category');
            tabDom?.click();
        }
        assetManager.setPv_new(5522);
        assetManager.setPv_new(7101, {
            additional: {
                s1: '视频素材',
                s2: this.state.searchText,
            }
        })
    };

    calcList = (searchResults: TOfficialVideoE[]) => {
        if (searchResults.length === 0) {
            return;
        }
        const list = unionBy(this.state.searchResults, searchResults, 'id');
        let [c0, c1] = [0, 0];
        for (const i of list) {
            i.isFav = i.is_fav;
            i.rt_displayHeight = (this.targetWidth / Number.parseInt(i.width)) * Number.parseInt(i.height);
            const t = Math.ceil(Number.parseFloat(i.duration) / 1000);
            const s = t % 60;
            const m = Math.floor(t / 60);
            i.rt_duration_string = `${m < 10 ? '0' + m : m}:${s < 10 ? '0' + s : s}`;
            if (c0 <= c1) {
                c0 += i.rt_displayHeight;
                i.rt_column = 0;
            } else {
                c1 += i.rt_displayHeight;
                i.rt_column = 1;
            }
        }
        this.setState({
            searchResults: list,
        });
    };

    componentDidUpdate(prevProps: Readonly<IVideoEPanelProps>, prevState: Readonly<IVideoEPanelState>): void {
        if (prevState.activeKey !== 'category' && this.state.activeKey === 'category') {
            this.getRecommend();
        }
        if (
            this.state.activeKey === 'category' &&
            this.props.user.userId &&
            prevProps.user?.userId !== this.props.user.userId
        ) {
            if (this.state.isSearching) {
                this.search(undefined, 1);
            } else {
                this.getRecommend();
            }
        }
    }

    render() {
        return (
            <div className="official-videoe-panel">
                {/* <CommercialUse text="商用会员所有视频素材可放心商用" /> */}
                <Tabs
                    isActive={false}
                    minusLeft={20}
                    defaultActive="category"
                    tabs={[
                        {
                            show: true,
                            key: 'category',
                            title: '视频素材',
                            content: (
                                <VideoEList
                                    searchText={this.state.searchText}
                                    searchResults={this.state.searchResults}
                                    isSearching={this.state.isSearching}
                                    categories={this.state.categories}
                                    searchCategory={this.searchCategory}
                                    updataFavor={this.updataFavor}
                                    searchMore={this.searchMore}
                                />
                            ),
                            setPv: {
                                title: {
                                    click: () => assetManager.setPv_new(5523),
                                },
                            },
                        },
                        {
                            show: true,
                            key: 'history',
                            title: '最近使用',
                            content: <VideoEHistory activeKey={this.state.activeKey} />,
                            setPv: {
                                title: {
                                    click: () => assetManager.setPv_new(5524),
                                },
                            },
                        },
                        {
                            show: true,
                            key: 'favor',
                            title: '收藏',
                            content: <VideoEFavor activeKey={this.state.activeKey} />,
                            setPv: {
                                title: {
                                    click: () => assetManager.setPv_new(5525),
                                },
                            },
                        },
                    ]}
                    search={
                        <PanelSearch
                            searchText={this.state.searchText}
                            placeholder="在这里搜索视频素材"
                            onChangeSearchText={this.onChangeSearchText}
                            clickSearch={this.clickSearch}
                        />
                    }
                    onChangeKey={this.onChangeKey}
                ></Tabs>
            </div>
        );
    }
}
