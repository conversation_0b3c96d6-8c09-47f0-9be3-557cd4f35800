import { ETool } from '@v7_logic/Enum';
import React from 'react';
import { tabList, toolConfig } from './config';
import './scss/index.scss';
import { CopywritingBtnFloatPanel } from '@v7_render/CopywritingBtnFloatPanel';
import { assetManager } from '@src/userComponentV6.0/AssetManager';
export function DisplayAddMenuTool(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.ADD_MENU, nav: ETool.ADD_MENU};
}
// interface IAddMenuProps {
//     //
// }
interface IAddMenuState {
    showCommonCopywriting: boolean;
}

export class AddMenu extends React.PureComponent<unknown, IAddMenuState> {
    constructor(props: any) {
        super(props);
        this.state = {
            showCommonCopywriting: false, // 常用文案
        };
    }
    componentDidMount(): void {
        assetManager.setPv_new(8835);
    }

    render() {
        const { showCommonCopywriting } = this.state;
        return (
            <div className="add_menu_toolPanel_container">
                {tabList.map((tabItem) => {
                    return (
                        <div key={tabItem.name + tabItem.id} className="tool_wrap">
                            <div className="tool_title">
                                <span>{tabItem.name}</span>
                                {tabItem.more && (
                                    <span className="item_more" onClick={tabItem.onClickMore}>
                                        查看更多
                                    </span>
                                )}
                            </div>
                            <div className="item_wrap">
                                {toolConfig[tabItem.key].map((toolItem) => {
                                    return toolItem.type == 'obj' ? (
                                        <div
                                            key={toolItem.name + toolItem.id}
                                            className={['tool_item', toolItem.key].join(' ')}
                                            onClick={toolItem.onClick.bind(this)}
                                        >
                                            {toolItem.icon_url && (
                                                <div className="icon">
                                                    <img src={toolItem.icon_url} alt="" />
                                                </div>
                                            )}
                                            {toolItem.icon && (
                                                <div className="icon">
                                                    <span className={`iconfont ${toolItem.icon}`}></span>
                                                </div>
                                            )}
                                            <div className="item_name">{toolItem.name}</div>
                                        </div>
                                    ) : (
                                        toolItem.content
                                    );
                                })}
                            </div>
                        </div>
                    );
                })}
                {showCommonCopywriting && (
                    <CopywritingBtnFloatPanel closeCallBack={() => this.setState({ showCommonCopywriting: false })} />
                )}
            </div>
        );
    }
}
