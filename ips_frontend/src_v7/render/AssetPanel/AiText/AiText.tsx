import React, { Component } from 'react';
import { ETool } from '@v7_logic/Enum';
import { AssetAddListener } from '@v7_logic/AssetAddListener';
import { assetManager } from '@src/userComponentV6.0/AssetManager';
import './index.scss';
import { SceneList, ContentNeed, FooterBtn, CreateText } from './comp'
import { STATUS, textList } from './config'
import { getAiConsumerPoint } from './api';
import { storeDecorator } from '@v7_logic/StoreHOC';
import { IStoreState } from '@v7_store/redux/store';
import { IUserInfo } from '@v7_logic/Interface';
import { IPSConfig } from '@v7_utils/IPSConfig';
interface propsStruct {
    // fontCount:number
    isActive?: boolean;
    user?: IUserInfo;
}

export interface SceneItem {
    title: string;
    img: string;
    tips: string;
    type: string;
}
interface stateStruct {
    status: string;
    // 当前场景下需要文案需求
    sceneText: string;
    // ai生成的文案
    aiText: string;
    limitNum: number;
    activeScene: SceneItem;
    // 剩余可消耗点数
    restPoint: number | string;
    restFix: number | string;
    restWords: number | string
}
export function DisplayAiText(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.AI_TEXT, nav: ETool.AI_TEXT};
}

@storeDecorator((state: IStoreState) => {
    return {
        user: state.onCanvasPainted.user,
    };
})

export class AiText extends Component<propsStruct, stateStruct> {
    apiCount: number;
    aiTextStash:string;
    constructor(props: propsStruct) {
        super(props);
        this.state = {
            status: STATUS.SCENE,
            aiText: '',
            sceneText: '',
            limitNum: 1000,
            activeScene: textList[0],
            restPoint: 0,
            restFix: 0,
            restWords: 0,
        }
        this.apiCount = 0
        this.aiTextStash = "" 
    }
    componentDidMount(): void {
        this.getConsumerPoint()
        this.toUrlScene()
    }
    toUrlScene() {
        const urlProps = IPSConfig.getProps(true);
        if (urlProps?.scene) {
            const scene = textList.find(v => v.type === urlProps.scene)
            this.selScene(scene)
        }
    }
    getConsumerPoint() {
        getAiConsumerPoint().then(res => {
            res.json().then(r => {
                if (typeof r?.data?.num === 'number') {
                    this.setState({
                        restPoint: r?.data?.num,
                        restFix: r?.data?.fix_num,
                        restWords: r?.data?.word_num,
                    })
                } else {
                    this.setState({
                        restPoint: '获取失败，请稍候',
                        restFix: '获取失败，请稍候',
                        restWords: '获取失败，请稍候',
                    })
                    if (this.apiCount >= 3) return;
                    setTimeout(() => {
                        this.apiCount++;
                        this.getConsumerPoint()
                    }, 2000)
                }

            })
        })
    }
    stopPropagation(e: MouseEvent): void {
        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }

    addTextEditorEvent(
        value: string,
    ): void {
        AssetAddListener.addText(
            {
                fontSize: 24,
                fontWeight: 'normal',
                ranking: '0',
                isSmallFontSize: true,
            },
            undefined,
            value,
        );
        return;
    }
    // 消耗点数
    consume = () => {
        const suplusPoint = (this.state.restPoint as number) - 1
        this.setState({
            restPoint: suplusPoint < 0 ? 0 : suplusPoint
        })

        if (suplusPoint <= 0) {
            assetManager.setPv_new(7476);
        }
    }
    // 选择ai场景
    selScene = (scene: SceneItem) => {
        this.setState({
            activeScene: scene,
            status: STATUS.CONTENT
        })
        assetManager.setPv_new(7376);
    }
    // 切换ai场景
    switchScene = () => {
        this.setState({
            status: STATUS.SCENE
        })
        assetManager.setPv_new(7377);
    }
    // 修改场景文案
    changeSceneText = (sceneText: string) => {
        this.setState({
            sceneText
        })
    }
    // 限制ai生成字数
    changeNumLimit = (limitNum: number) => {
        this.setState({
            limitNum
        })
        assetManager.setPv_new(7379);
    }
    // ai文案生成结束
    typingEnd = (aiText: string) => {
        this.setState({
            status: STATUS.GENERATED,
            aiText
        })
        this.aiTextStash = aiText
        this.getConsumerPoint();
    }
    changeAiText = (aiText: string) => {
        console.log(aiText)
        this.setState({
            aiText
        })
        this.aiTextStash = aiText

    }
    // 开始生成ai文案
    startGenerator = () => {
        if (!this.state.sceneText) return;
        this.setState({
            status: STATUS.CREATE
        })
        assetManager.setPv_new(7380);
    }
    // 取消ai文案生成
    cancelGenerator = () => {
        this.setState({
            status: STATUS.CONTENT
        })
        // assetManager.setPv_new(7381);
    }
    // 重新生成ai文案
    reGenerator = () => {
        this.setState({
            aiText: '',
            status: STATUS.CREATE
        })
        assetManager.setPv_new(7383);
    }
    // 使用ai文案
    copyAiText = (val: string) => {
        const text = val || this.state.aiText ||  this.aiTextStash
        const elem = document.createElement('textarea');
        elem.value = text;
        document.body.appendChild(elem);
        elem.select();
        document.execCommand('copy');
        document.body.removeChild(elem);
        // this.addTextEditorEvent(this.state.aiText)
        // assetManager.setPv_new(7382);
    }

    // 返回ai文案需求
    returnAiText = () => {
        this.setState({
            aiText: '',
            status: STATUS.CONTENT
        })
    }

    render(): JSX.Element {
        const { activeScene, status, sceneText, limitNum, aiText, restPoint, restFix, restWords } = this.state;
        const isCreate = [STATUS.CREATE, STATUS.GENERATED].includes(status);
        const showFootBtn = status !== STATUS.SCENE
        const isGenerated = status === STATUS.GENERATED
        return (
            <div className="aiTextWrap">
                {isGenerated ?
                    <h3 className='returnText' onClick={this.returnAiText}><i className='iconfont icon-fanhui1'></i>返回</h3>
                    : <h3>AI文案</h3>
                }
                <div className={'deepseek-tip'}>
                    <img src="https://s.tuguaishou.com/index_img/editorV7.0/deepseek_logo.png" alt="" />
                    <span className={'tip-text'}>全新文案生成</span>
                </div>
                {/* <h3>AI文案</h3>  */}
                {/* ai文案场景选择 */}
                {status === STATUS.SCENE ? <SceneList selScene={this.selScene} /> : null}
                {/* ai文案prompt */}
                {status === STATUS.CONTENT ? <ContentNeed activeScene={activeScene} needValue={sceneText} changeNumLimit={this.changeNumLimit} switchScene={this.switchScene} changeSceneText={this.changeSceneText} limitNum={limitNum} /> : null}
                {/* ai文案生成 */}
                {isCreate ? <CreateText copyAiText={this.copyAiText} consume={this.consume} cancelGenerator={this.cancelGenerator} status={status} isGenerated={isGenerated} needValue={sceneText} typingEnd={this.typingEnd} changeSceneText={this.changeSceneText} activeScene={activeScene} aiText={aiText} limitNum={limitNum} changeAiText={this.changeAiText} /> : null}
                {showFootBtn ? <FooterBtn from='aiText' restPoint={restPoint as number} restFix={restFix as number} restWords={restWords as number} cancelGenerator={this.cancelGenerator} startGenerator={this.startGenerator} status={status} copyAiText={this.copyAiText} reGenerator={this.reGenerator} username={this.props.user.userName} avatar={this.props.user.avatar} refresh={this.getConsumerPoint.bind(this)} /> : null}
            </div>
        )
    }
}
