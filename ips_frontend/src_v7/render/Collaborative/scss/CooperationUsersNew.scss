@import '@editorConfig/index.scss';
.collaborative_wrapper_box {
    margin-right: 8px;
    height: 36px;
    width: 70px;
    position: relative;
    .usersList {
        height: 36px;
        width: 100%;
        display: flex;
        align-items: center;
        position: relative;
        cursor: pointer;
        .userAvatar {
            width: 36px;
            height: 36px;
            line-height: 36px;
            background-color: #EF3964;
            text-align: center;
            font-weight: 500;
            font-size: 20px;
            color: #fff;
            border-radius: 50%;

            img {
                width: 36px;
                height: 36px;
                background: #F7F7F7;
                border-radius: 50%;
            }
            &:nth-child(2) {
                position: absolute;
                left: 17px;
            }
        }
        .avatarList {
            display: flex;
            align-items: center;
            position: relative;
            &:hover {
                .class_popover.collaborativePopover {
                    visibility: visible;
                    z-index: 1;
                    opacity: 1;
                }
            }
        }
        .addMore {
            box-sizing: border-box;
            width: 36px;
            height: 36px;
            position: absolute;
            background: #F7F7F7;
            color: #a5a3a4;
            border: 1px solid #FFF;
            border-radius: 50%;
            right: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            i {
                font-size: 20px;
                position: relative;
                top: -1px;
            }
            &:hover {
                background: #ffecf1;
                color: #ff416e;
            }

            .add_tooltip {
                width: 88px;
                height: 36px;
                line-height: 36px;
                text-align: center;
            }
            &:hover {
                .add_tooltip {
                    display: block;
                }
            }
        }
    }

    .class_popover.collaborativePopover {
        left: 50%;
        transform: translate(-50%);
        width: 220px;
        padding: 15px;
        box-sizing: border-box;

        // visibility: visible;
        // z-index: 1;
        // opacity: 1;
        .tabsBox {
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #e9e8e8;
            .tabItem {
                height: 32px;
                font-weight: 500;
                color: #797676;
                cursor: pointer;
                border-bottom: 2px solid transparent;
            }
            .active {
                color: #1f1a1b;
                border-bottom: 2px solid #ef3964;
            }
            .popoverTitle {
                padding-bottom: 10px;
                color: #1f1a1b;
                font-weight: 500;
            }
        }
        .contentList {
            padding-top: 15px;
            padding-bottom: 5px;
            max-height: 140px;
            overflow: auto;
            .userItem {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
                .mine {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    background-color: #EF3964;
                    color: #fff;
                    line-height: 40px;
                    text-align: center;
                    font-weight: 500;
                    font-size: 15px;
                }
                img {
                    width: 40px;
                    height: 40px;
                    background: #f8f8f8;
                    border-radius: 50%;
                }
                .userName {
                    margin-left: 10px;
                    max-width: 120px;
                    font-size: 14px;
                    font-weight: 400;
                    color: #000000;
                    line-height: 14px;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                }
                i {
                    color: #ef3964;
                    margin-left: 4px;
                }
            }
        }
        .contentAdd {
            padding-top: 11px;
            padding-bottom: 15px;
            .linkTitle {
                font-size: 12px;
                font-weight: 500;
                color: #1f1a1b;
                line-height: 20px;
                margin-bottom: 3px;
            }
            .linkRemark {
                font-size: 12px;
                font-weight: 400;
                color: #797676;
                line-height: 20px;
            }
            .linkInfo {
            }
            .copyLink {
                height: 33px;
                background: #ef3964;
                border-radius: 5px 5px 5px 5px;
                font-size: 12px;
                font-weight: 400;
                color: #ffffff;
                text-align: center;
                line-height: 33px;
            }
        }

        .footer {
            border-top: 1px solid #e9e8e8;
            padding-top: 15px;
            font-size: 12px;
            color: #1f1a1b;

            .remind {
                font-size: 12px;
                font-weight: 400;
                line-height: 20px;
                text-align: center;
            }
            .voiceBtn {
                margin-top: 5px;
                height: 33px;
                background: #ffffff;
                border-radius: 5px 5px 5px 5px;
                border: 1px solid #d2d1d1;
                text-align: center;
                line-height: 33px;
                font-weight: 500;
                cursor: pointer;
                &:hover {
                    color: #ef3964;
                    border: 1px solid #ef3964;
                }
            }
        }
    }
}
