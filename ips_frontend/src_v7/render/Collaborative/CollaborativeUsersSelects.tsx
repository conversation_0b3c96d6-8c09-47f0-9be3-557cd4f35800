import { <PERSON><PERSON><PERSON>elper } from '@v7_logic/AssetHelper';
import { ESelectType } from '@v7_logic/Enum';
import { IAsset } from '@v7_logic/Interface';
import { storeDecorator } from '@v7_logic/StoreHOC';
import { IStoreState } from '@v7_store/redux/store';
import equal from 'fast-deep-equal';
import React, { PureComponent } from 'react';
import { applyToPoint, rotateDEG } from 'transformation-matrix';

import './scss/CollaborativeUsersSelects.scss';

interface IRect {
    left: number;
    top: number;
    width: number;
    height: number;
    transform: string;
}

interface ISelectRect {
    userName: string;
    assets: {
        className: string;
        pageHash: string;
        assetHash: string;
    }[];
    size?: {
        [pageHash: string]: IRect;
    };
}

interface ICollaborativeUsersSelectsProps {
    canvas_dom_info?: IStoreState['paintOnCanvas']['canvas_dom_info'];
    canvas?: IStoreState['paintOnCanvas']['canvas'];
    pageInfo?: IStoreState['paintOnCanvas']['pageInfo'];
    work?: IStoreState['paintOnCanvas']['work'];
    pageAttr?: IStoreState['paintOnCanvas']['pageAttr'];
    toolPanel?: IStoreState['paintOnCanvas']['toolPanel'];
    cooperationUsers?: IStoreState['paintOnCanvas']['cooperationUsers'];
    collaborativeUserMap?: IStoreState['paintOnCanvas']['collaborativeUserMap'];
    renderMode?: 'dom' | 'canvas';
    pagesTop?: number[];
    rt_canvas_render_mode?: '' | 'pull' | 'board';
}

interface ICollaborativeUsersSelectsState {
    userAssetsInfo: Record<string, ISelectRect>;
}

@storeDecorator((state: IStoreState) => {
    return {
        canvas_dom_info: state.onCanvasPainted.canvas_dom_info,
        canvas: state.onCanvasPainted.canvas,
        pageInfo: state.onCanvasPainted.pageInfo,
        work: state.onCanvasPainted.work,
        pageAttr: state.onCanvasPainted.pageAttr,
        toolPanel: state.onCanvasPainted.toolPanel,
        cooperationUsers: state.onCanvasPainted.cooperationUsers,
        collaborativeUserMap: state.onCanvasPainted.collaborativeUserMap,
    };
})
export class CollaborativeUsersSelects extends PureComponent<
    ICollaborativeUsersSelectsProps,
    ICollaborativeUsersSelectsState
> {
    state: ICollaborativeUsersSelectsState = {
        userAssetsInfo: {},
    };

    getCurrentPageHash = (): string => {
        if (!this.props.pageAttr.pageHash) {
            return undefined;
        }
        for (const ph in this.props.pageAttr.pageHash) {
            if (this.props.pageAttr.pageHash[ph] === this.props.pageInfo.pageNow) {
                return ph;
            }
        }
        return undefined;
    };

    calcRotateRect = (size: { x: number; y: number; w: number; h: number }, rotate: number) => {
        const { x, y, w, h } = size;
        const tl = { x, y };
        const tr = { x: x + w, y };
        const bl = { x, y: y + h };
        const br = { x: x + w, y: y + h };
        const matrix = rotateDEG(rotate, x + w / 2, y + h / 2);
        const newTl = applyToPoint(matrix, tl);
        const newTr = applyToPoint(matrix, tr);
        const newBl = applyToPoint(matrix, bl);
        const newBr = applyToPoint(matrix, br);
        const t: number = Math.min(newTl.y, newTr.y, newBl.y, newBr.y);
        const b: number = Math.max(newTl.y, newTr.y, newBl.y, newBr.y);
        const l: number = Math.min(newTl.x, newTr.x, newBl.x, newBr.x);
        const r: number = Math.max(newTl.x, newTr.x, newBl.x, newBr.x);
        return {
            x: l,
            y: t,
            w: r - l,
            h: b - t,
        };
    };

    calcSelectsRect = (
        assets: {
            pageIndex: number;
            index: number;
            asset: IAsset;
            assetHash: string;
            pageHash: string;
        }[],
    ) => {
        const sortedAssets: Record<string, IAsset[]> = {};
        const size: Record<string, IRect> = {};
        const scale = this.props.canvas.scale ?? 1;
        assets.forEach((i) => {
            if (sortedAssets[i.pageHash]) {
                sortedAssets[i.pageHash].push(i.asset);
            } else {
                sortedAssets[i.pageHash] = [i.asset];
            }
        });
        for (const ph in sortedAssets) {
            let t: number;
            let b: number;
            let l: number;
            let r: number;
            let rotate = 0;
            const len = sortedAssets[ph].length;
            sortedAssets[ph].forEach((a) => {
                let x = a.transform.posX;
                let y = a.transform.posY;
                let w = parseFloat(`${a.attribute.width}`);
                let h = parseFloat(`${a.attribute.height}`);
                if (a.attribute.container?.id) {
                    const { width, height } = a.attribute.container;
                    w = parseFloat(`${width}`);
                    h = parseFloat(`${height}`);
                }
                if (a.meta.type === 'text' && this.props.canvas_dom_info?.is_exist) {
                    let dom = document.querySelector<HTMLDivElement>(
                        `.${this.props.canvas_dom_info.class_name} .${a.meta.className}`,
                    );
                    if (dom) {
                        h = dom.clientHeight / scale;
                    }
                    dom = null;
                }
                if (len === 1) {
                    rotate = a.transform.rotate;
                }
                if (len > 1 && a.transform.rotate % 360 !== 0) {
                    ({ x, y, w, h } = this.calcRotateRect({ x, y, w, h }, a.transform.rotate));
                }
                if (typeof a.transform.posX === 'number') {
                    if (typeof l !== 'number') {
                        l = x;
                    } else {
                        l = Math.min(l, x);
                    }
                }
                if (typeof a.transform.posY === 'number') {
                    if (typeof t !== 'number') {
                        t = y;
                    } else {
                        t = Math.min(t, y);
                    }
                }
                if (!Number.isNaN(w)) {
                    if (typeof r !== 'number') {
                        r = x + w;
                    } else {
                        r = Math.max(r, x + w);
                    }
                }
                if (!Number.isNaN(h)) {
                    if (typeof b !== 'number') {
                        b = y + h;
                    } else {
                        b = Math.max(b, y + h);
                    }
                }
            });
            size[ph] = {
                left: l * scale,
                top: t * scale,
                width: (r - l) * scale,
                height: (b - t) * scale,
                transform: `rotate(${rotate}deg)`,
            };
        }
        return size;
    };

    renderSelectsRect = () => {
        const ph = this.getCurrentPageHash();
        const rects = [];
        for (const userId in this.state.userAssetsInfo) {
            if (
                this.props.renderMode === 'canvas' &&
                !this.props.rt_canvas_render_mode &&
                this.state.userAssetsInfo[userId]?.size
            ) {
                for (const key in this.state.userAssetsInfo[userId].size) {
                    rects.push(
                        <div
                            className="user-selects-rect"
                            style={this.state.userAssetsInfo[userId]?.size?.[key]}
                            key={userId + '-' + key}
                        >
                            <div className="user-selects-name">
                                {this.state.userAssetsInfo[userId].userName.slice(0, 1)}
                            </div>
                        </div>,
                    );
                }
            } else if (this.state.userAssetsInfo[userId]?.size?.[ph]) {
                rects.push(
                    <div
                        className="user-selects-rect"
                        style={this.state.userAssetsInfo[userId]?.size?.[ph]}
                        key={userId}
                    >
                        <div className="user-selects-name">
                            {this.state.userAssetsInfo[userId].userName.slice(0, 1)}
                        </div>
                    </div>,
                );
            }
        }
        return rects;
    };

    componentDidUpdate(prevProps: Readonly<ICollaborativeUsersSelectsProps>) {
        // 没有协作用户 清空选中框信息
        if (prevProps.collaborativeUserMap?.length > 0 && this.props.collaborativeUserMap?.length === 0) {
            this.setState({
                userAssetsInfo: {},
            });
        }
        if (this.props.collaborativeUserMap?.length > 0) {
            const { userAssetsInfo } = this.state;
            const newSelectMap: Record<
                string,
                {
                    assets: {
                        assetHash: string;
                        pageHash: string;
                        className: string;
                    }[];
                    userName: string;
                }
            > = {};
            this.props.collaborativeUserMap.forEach((d) => {
                newSelectMap[d.userId] = {
                    assets: d.assets,
                    userName: d.userName,
                };
            });
            const newUserAssetsInfo: typeof userAssetsInfo = {};
            for (const id in userAssetsInfo) {
                if (newSelectMap[id]) {
                    newUserAssetsInfo[id] = userAssetsInfo[id];
                }
            }
            const ph = this.getCurrentPageHash();
            for (const id in newSelectMap) {
                let isCurrentPage = false;
                const len = newSelectMap[id].assets?.length ?? 0;
                for (let i = 0; i < len; i++) {
                    if (newSelectMap[id].assets[i].pageHash === ph) {
                        isCurrentPage = true;
                        break;
                    }
                }
                if (
                    this.props.renderMode === 'canvas' ||
                    isCurrentPage ||
                    !newSelectMap[id].assets ||
                    !newUserAssetsInfo[id]?.assets ||
                    !equal(newSelectMap[id].assets, newUserAssetsInfo[id].assets)
                ) {
                    let size: Record<string, IRect> = {};
                    if (newSelectMap[id].assets) {
                        const assets = AssetHelper.findAllByHash(
                            this.props.work,
                            this.props.pageAttr,
                            newSelectMap[id].assets,
                        );
                        if (assets?.length > 0) {
                            console.log(99999);
                            size = this.calcSelectsRect(assets);
                        }
                        if (this.props.renderMode === 'canvas' && !this.props.rt_canvas_render_mode) {
                            for (const ph in size) {
                                const i = this.props.pageAttr.pageHash[ph];
                                size[ph].top = size[ph].top + this.props.pagesTop[i];
                            }
                        }
                    }
                    newUserAssetsInfo[id] = {
                        userName: newSelectMap[id].userName,
                        assets: newSelectMap[id].assets,
                        size,
                    };
                }
            }
            if (!equal(userAssetsInfo, newUserAssetsInfo)) {
                this.setState({
                    userAssetsInfo: newUserAssetsInfo,
                });
            }
        }
    }

    render() {
        return <div className="collaborative-users-selects">{this.renderSelectsRect()}</div>;
    }
}
