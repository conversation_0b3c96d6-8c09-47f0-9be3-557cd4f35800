import { assetManager } from '@src/userComponentV6.0/AssetManager';
import { ICell } from '@v7_logic/Interface';
import React from 'react';
import { tableAddMaxRowAndMaxCol, deleteRowAndColFn, addRow, addCol } from './utils';

function stopPropagation (e: React.MouseEvent<HTMLElement>) {
    e.stopPropagation();
}
export function CellController(props: {
    cells: ICell[][];
    rowHeight: number[];
    colWidth: number[];
    editingCell: string;
    onCellControllerMouseDown: (e: React.MouseEvent<HTMLElement>) => void;
    onCellControllerDoubleClick: (e: React.MouseEvent<HTMLElement>) => void;
}) {
    let t = 0;
    return (
        <div className="table-cell-controller">
            {props.rowHeight.map((h, r) => {
                if (r > 0) {
                    t += props.rowHeight[r - 1];
                }
                let l = 0;
                return props.colWidth.map((w, c) => {
                    if (c > 0) {
                        l += props.colWidth[c - 1];
                    }
                    let width = w;
                    let height = h;
                    if (props.cells[r]?.[c]?.merge) {
                        // x 是行 y 是列
                        for (let i = c + 1; i <= props.cells[r][c].merge.y; i++) {
                            width += props.colWidth[i];
                        }
                        for (let i = r + 1; i <= props.cells[r][c].merge.x; i++) {
                            height += props.rowHeight[i];
                        }
                    }
                    return (
                        <div
                            className={`table-cell-controller-cell ${props.cells[r]?.[c]?.merge ? 'merge' : ''}`}
                            key={r + '-' + c}
                            style={{
                                left: l,
                                top: t,
                                width,
                                height,
                                pointerEvents: props.editingCell === r + '_' + c ? 'none' : 'initial',
                                display: props.cells[r]?.[c]?.merged ? 'none' : 'block'
                            }}
                            data-row={r}
                            data-col={c}
                            data-cell={r + '_' + c}
                            onMouseDown={props.onCellControllerMouseDown}
                            onDoubleClick={props.onCellControllerDoubleClick}
                        ></div>
                    );
                });
            })}
        </div>
    );
}

export function SelectColAndRowDrag(props: {
    cells: ICell[][];
    rowHeight: number[];
    colWidth: number[];
    scale: number;
    selectSize?: {
        startRow: number;
        startCol: number;
        endRow: number;
        endCol: number;
    }
    dragWidth: number | undefined;
    dragHeight: number | undefined;
    x: string | undefined;
    y: string | undefined
    type: undefined | 'col' | 'row'
}) {
    const { selectSize, colWidth, rowHeight, dragWidth, scale, x, y } = props;
    const totalHeight = props.rowHeight.reduce((old, now) => {
        return old + now
    }, 0)
    const totalWidth = props.colWidth.reduce((old, now) => {
        return old + now
    }, 0)
    const borderWidth = Math.ceil(2 / scale)
    // 计算列偏移量
    let Colx = 0;
    if (selectSize) {
        for(let i = 0; i < selectSize.endCol; i++) {
            Colx += colWidth[i];
        }
    }
    // 计算行的偏移量
    let Rowy = 0;
    if (selectSize) {
        for(let i = 0; i < selectSize.endRow; i++) {
            Rowy += rowHeight[i];
        }
    }
    return (
        <>
            {
                props.type === 'col' &&
                <div className='select-col-drag'
                    style={{
                        width: props.dragWidth,
                        top: -20 / props.scale,
                        left: x ? Number(x) : Colx,
                    }}
                >
                    <div className='drag-top-block' 
                        style={{ width: props.dragWidth, height: 12 / props.scale }}
                    >
                        <i style={{ fontSize: 8 / scale }} className='iconfont icon-tuozhuai' />
                    </div>
                    <div className='drag-col' style={{ height: totalHeight, top: 20 / props.scale, borderWidth}}>
                        {
                            rowHeight.map((item, index) => {
                                return (
                                    <div className='item' key={index} style={{ height: item, borderWidth }} />
                                )
                            })
                        }
                    </div>
                </div>
            }
            {
                props.type === 'row' &&
                <div className='select-row-drag'
                    style={{
                        left: -20 / props.scale,
                        width: 12 / props.scale,
                        top: y ? Number(y) : Rowy
                    }}
                >
                    <div className='drag-top-block' 
                        style={{ width: 12 / props.scale, height: props.dragHeight }}
                    >
                        <i style={{ fontSize: 8 / scale }} className='iconfont icon-tuozhuai' />
                    </div>
                    <div className='drag-row' style={{ width: totalWidth, top: 0, left: 21 / props.scale, borderWidth}}>
                        {
                            colWidth.map((item, index) => {
                                return (
                                    <div className='item' key={index} style={{ width: item, borderWidth }} />
                                )
                            })
                        }
                    </div>
                </div>
            }
        </>
    )
}

export function ShowRestDragLine(props: {
    rowHeight: number[];
    colWidth: number[];
    scale: number;
    nearLineRow: number;
    nearLineCol: number;
    type: undefined | 'col' | 'row'
}) {
    const { nearLineRow, nearLineCol, type } = props;
    const totalHeight = props.rowHeight.reduce((old, now) => {
        return old + now
    }, 0)
    const totalWidth = props.colWidth.reduce((old, now) => {
        return old + now
    }, 0)
    const lineWidth = Math.ceil(2 / props.scale);
    let left = 0;
    let top = 0;
    if (nearLineCol && type === 'col') {
        for(let i = 0; i < nearLineCol; i++) {
            left += props.colWidth[i];
        }
    }
    if (nearLineRow && type === 'row') {
        for(let i = 0; i < nearLineRow; i++) {
            top += props.rowHeight[i];
        }
    }
    return (
        <>
            {
                type === 'col' &&
                <div className='show-rest-drag-line-col' style={{ top: 0, height: totalHeight, left: left + 'px', width: lineWidth }}></div>
            }
            {
                type === 'row' &&
                <div className='show-rest-drag-line-row' style={{ width: totalWidth, top: top + 'px', height: lineWidth }}></div>
            }
        </>
    )
}

export function SelectedTds(props: {
    cells: ICell[][];
    rowHeight: number[];
    colWidth: number[];
    scale: number;
    selectSize?: {
        startRow: number;
        startCol: number;
        endRow: number;
        endCol: number;
    }
    isDragColAndRow: boolean
}) {
    let style: React.CSSProperties = {
        left: 0,
        top: 0,
        width: 0,
        height: 0,
    };
    if (props.selectSize) {
        let l = 0;
        let w = 0;
        let t = 0;
        let h = 0;
        // x 是行 y 是列
        for (let i = 0; i <= props.selectSize.endRow; i++) {
            if (i < props.selectSize.startRow) {
                t += props.rowHeight[i];
            } else {
                h += props.rowHeight[i];
            }
        }
        for (let i = 0; i <= props.selectSize.endCol; i++) {
            if (i < props.selectSize.startCol) {
                l += props.colWidth[i];
            } else {
                w += props.colWidth[i];
            }
        }
        const borderWidth = Math.ceil(1 / props.scale);
        style = {
            left: l,
            top: t,
            width: w + borderWidth,
            height: h + borderWidth,
            visibility: 'visible', 
            border: props.isDragColAndRow  ? 'none' : `${borderWidth}px solid #EF3964`,
            // borderWidth: props.isDragColAndRow ? 'none' : borderWidth,
            opacity: props.isDragColAndRow ? 0.4: 1,
            backgroundColor: props.isDragColAndRow ? '#ffffff' : ''
        };
    }

    return <div className="table-controller-selected-cells" style={style}></div>;
}

export function RowAndColLines(props: {
    rowHeight: number[];
    colWidth: number[];
    cn: string;
    scale: number;
    activeControllerLine: string;
    isDragging: boolean;
    onMouseDown: (e: React.MouseEvent<HTMLElement, MouseEvent>) => void;
    addIconIndex: string;
    cells?: ICell[][];
}) {
    let t = 0;
    // 使用与Table.ts相同的边框宽度计算方式
    // 默认lineWidth为1，如果有cells数据则使用实际值
    const defaultLineWidth = 1;
    const actualLineWidth = props.cells?.[0]?.[0]?.lineWidth?.[0] || defaultLineWidth;
    const borderTopWidth = Math.max(Math.round(actualLineWidth / props.scale), actualLineWidth * 2);
    const rowLineHeight = 4 * borderTopWidth;
    let l = 0;
    const actualLineWidthLeft = props.cells?.[0]?.[0]?.lineWidth?.[3] || defaultLineWidth;
    const borderLeftWidth = Math.max(Math.round(actualLineWidthLeft / props.scale), actualLineWidthLeft * 2);
    const colLineWidth = 4 * borderLeftWidth;
    const { addIconIndex } = props;

    return (
        <>
            <div className="table-controller-row-lines">
                <div
                    className={`table-controller-row-line ${
                        props.activeControllerLine === 'row_-1'
                            ? 'active'
                            : props.activeControllerLine || props.isDragging
                            ? 'disable'
                            : ''
                    }`}
                    key={-1}
                    style={{ top: 0, height: rowLineHeight }}
                    data-type="row"
                    data-rowline={-1}
                    data-scale={props.scale}
                    data-cn={props.cn}
                    data-h={props.rowHeight[0]}
                    onMouseDown={props.onMouseDown}
                >
                    <div className={`table-controller-line ${addIconIndex==='row_-1' ?'show-line':''}`} style={{ borderTopWidth }}></div>
                    {
                        <InsertRow rowHeight={props.rowHeight} scale={props.scale} serial={-1} isDragging={props.isDragging} activeControllerLine={props.activeControllerLine} />
                    }
                </div>
                {props.rowHeight.map((h, r) => {
                    t += h;
                    return (
                        <div
                            className={`table-controller-row-line ${
                                props.activeControllerLine === 'row_' + r
                                    ? 'active'
                                    : props.activeControllerLine || props.isDragging
                                    ? 'disable'
                                    : ''
                            }`}
                            key={r}
                            style={{
                                top: t,
                                height: rowLineHeight,
                            }}
                            data-type="row"
                            data-rowline={r}
                            data-scale={props.scale}
                            data-cn={props.cn}
                            data-h={h}
                            onMouseDown={props.onMouseDown}
                        >
                            <div className={`table-controller-line ${addIconIndex==='row_' + r ?'show-line':''}`} style={{ borderTopWidth }}></div>
                            {
                                <InsertRow rowHeight={props.rowHeight} scale={props.scale} serial={r} isDragging={props.isDragging} activeControllerLine={props.activeControllerLine} />
                            }
                        </div>
                    );
                })}
            </div>
            <div className="table-controller-col-lines">
                <div
                    className={`table-controller-col-line ${
                        props.activeControllerLine === 'col_-1'
                            ? 'active'
                            : props.activeControllerLine || props.isDragging
                            ? 'disable'
                            : ''
                    }`}
                    key={-1}
                    style={{
                        left: 0,
                        width: colLineWidth,
                    }}
                    data-type="col"
                    data-colline={-1}
                    data-scale={props.scale}
                    data-cn={props.cn}
                    data-w={props.colWidth[0]}
                    onMouseDown={props.onMouseDown}
                >
                    <div className={`table-controller-line ${addIconIndex==='col_-1' ?'show-line':''}`} style={{ borderLeftWidth }}></div>
                    <InsertCol colWidth={props.colWidth} scale={props.scale} serial={-1} isDragging={props.isDragging} activeControllerLine={props.activeControllerLine} />
                </div>
                {props.colWidth.map((w, c) => {
                    l += w;

                    return (
                        <div
                            className={`table-controller-col-line ${
                                props.activeControllerLine === 'col_' + c
                                    ? 'active'
                                    : props.activeControllerLine || props.isDragging
                                    ? 'disable'
                                    : ''
                            }`}
                            key={c}
                            style={{
                                left: l,
                                width: colLineWidth,
                            }}
                            data-type="col"
                            data-colline={c}
                            data-scale={props.scale}
                            data-cn={props.cn}
                            data-w={w}
                            onMouseDown={props.onMouseDown}
                        >
                            <div className={`table-controller-line ${addIconIndex==='col_' + c ?'show-line':''}`} style={{ borderLeftWidth }}></div>

                            <InsertCol colWidth={props.colWidth} scale={props.scale} serial={c} isDragging={props.isDragging} activeControllerLine={props.activeControllerLine} />
                        </div>
                    );
                })}
            </div>
        </>
    );
}

export function RowAndColSelect(props: {
    rowHeight: number[];
    colWidth: number[];
    scale: number;
    cells: ICell[][];
    selectSize?: {
        startRow: number;
        startCol: number;
        endRow: number;
        endCol: number;
    };
    activeControllerLine: string;
    isDragging: boolean;
    onSelectorMouseDown: (e: React.MouseEvent<HTMLElement>) => void;
    changeAddIconIndex: (typeIndex: string) => void;
    hoverBgcIndex: string;
    changeHoverBgcIndex: (bgcIndex: string) => void;
}) {
    // 添加鼠标位置跟踪状态
    const [hoverPosition, setHoverPosition] = React.useState<{
        type: 'row' | 'col';
        index: number;
        position: 'left' | 'right' | 'top' | 'bottom' | null;
    }>({ type: 'row', index: -1, position: null });

    let startRow: number = undefined;
    let endRow: number = undefined;
    let startCol: number = undefined;
    let endCol: number = undefined;
    let isRowFull = false;
    let isRowPartial = false;
    let isColFull = false;
    let isColPartial = false;
    // x 是行 y 是列
    if (props.selectSize) {
        startRow = props.selectSize?.startRow;
        endRow = props.selectSize?.endRow;
        startCol = props.selectSize?.startCol;
        endCol = props.selectSize?.endCol;
        isRowPartial = startRow >= 0 && endRow >= 0;
        isColPartial = startCol >= 0 && endCol >= 0;
        if (
            isRowPartial &&
            props.selectSize?.startCol === 0 &&
            props.selectSize?.endCol === props.colWidth.length - 1
        ) {
            isRowFull = true;
        }
        if (
            isColPartial &&
            props.selectSize?.startRow === 0 &&
            props.selectSize?.endRow === props.rowHeight.length - 1
        ) {
            isColFull = true;
        }
    }
    const borderRadius = 2 / props.scale;
    const borderWidth = 1 / props.scale;
    const totalHeight = props.rowHeight.reduce((old, now) => {
        return old + now
    }, 0)
    const totalWidth = props.colWidth.reduce((old, now) => {
        return old + now
    }, 0)

    const transform = `translate(-50%, -50%) scale(${1 / props.scale}) rotate(90deg)`
    const transform1 = `translate(-50%, -50%) scale(${1 / props.scale})`

    // 处理行选择器鼠标移动
    const handleRowMouseMove = (e: React.MouseEvent<HTMLElement>, r: number) => {
        const rect = e.currentTarget.getBoundingClientRect();
        const y = e.clientY - rect.top;
        const halfHeight = rect.height / 2;
        setHoverPosition({
            type: 'row',
            index: r,
            position: y < halfHeight ? 'top' : 'bottom'
        });
    };

    // 处理列选择器鼠标移动
    const handleColMouseMove = (e: React.MouseEvent<HTMLElement>, c: number) => {
        const rect = e.currentTarget.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const halfWidth = rect.width / 2;
        setHoverPosition({
            type: 'col',
            index: c,
            position: x < halfWidth ? 'left' : 'right'
        });
    };

    // 处理鼠标离开
    const handleMouseLeave = () => {
        setHoverPosition({ type: 'row', index: -1, position: null });
    };

    const overHighlightRowAndCol = (e: React.MouseEvent<HTMLElement>) => {
        e.stopPropagation();
        const target = e.target as HTMLElement
        const nextSibling = target.offsetParent.nextSibling as HTMLElement
        if (nextSibling) {
            nextSibling.style.visibility = 'visible'
        }
    }

    const leaveHighlightRowAndCol = (e: React.MouseEvent<HTMLElement>) => {
        e.stopPropagation();
        const target = e.target as HTMLElement
        const nextSibling = target.offsetParent.nextSibling as HTMLElement
        if (nextSibling) {
            nextSibling.style.visibility = 'hidden';
        }
    }

    return (
        <>
            <div
                className={`table-controller-row-select ${
                    props.isDragging || props.activeControllerLine ? 'hide' : ''
                }`}
                style={{
                    left: -12 / props.scale,
                    width: 12 / props.scale,
                }}
            >
                {props.rowHeight.map((height, r) => {
                    const isSelected = startRow <= r && r <= endRow;
                    const isHoverResize = startRow === endRow && isRowFull && isSelected;
                    const showTopAdd = hoverPosition.type === 'row' && hoverPosition.index === r && hoverPosition.position === 'top';
                    const showBottomAdd = hoverPosition.type === 'row' && hoverPosition.index === r && hoverPosition.position === 'bottom';

                    return (
                        <div
                            className={`table-controller-row-selector ${isSelected ? 'active' : ''} ${
                                isRowPartial ? 'partial' : ''
                            } ${isRowFull ? 'full' : ''} ${isHoverResize ? 'hoveResize' : ''}`}
                            style={{
                                height,
                                borderWidth,
                                background: props.hoverBgcIndex === `row_${r}` ? isRowPartial ? '#F588A2' : '#FFDBE3' : '',
                                borderRadius:
                                    r === 0
                                        ? `${borderRadius}px ${borderRadius}px 0 0`
                                        : r === props.rowHeight.length - 1
                                        ? `0 0 ${borderRadius}px ${borderRadius}px`
                                        : 0,
                            }}
                            key={r}
                            data-model="selectRowAndCol"
                            data-type="row"
                            data-row={r}
                            onMouseDown={props.onSelectorMouseDown}
                            onMouseMove={(e) => handleRowMouseMove(e, r)}
                            onMouseEnter={() => {
                                if (!isHoverResize) {
                                    props.changeHoverBgcIndex(`row_${r}`);
                                }
                            }}
                            onMouseLeave={() => {
                                handleMouseLeave();
                                if (!isHoverResize) { 
                                    props.changeHoverBgcIndex('');
                                }
                            }}
                        >
                            {/* 上方添加按钮 */}
                            {showTopAdd && (
                                <div
                                    className='add-icon-box add-icon-box-top'
                                    style={{
                                        position: 'absolute',
                                        left: '50%',
                                        top: 0,
                                        transform: `translateX(-50%) translateY(-50%) scale(${1 / props.scale})`,
                                        cursor: 'pointer',
                                        zIndex: 10
                                    }}
                                    onClick={addRow}
                                    onMouseOver={() => { props.changeAddIconIndex(`row_${r-1}`) }}
                                    onMouseLeave={() => { props.changeAddIconIndex('') }}
                                >
                                    <i className="iconfont icon-tianjia1-copy" data-serial={r-1}></i>
                                </div>
                            )}

                            {/* 下方添加按钮 */}
                            {showBottomAdd && (
                                <div
                                    className='add-icon-box add-icon-box-bottom'
                                    style={{
                                        position: 'absolute',
                                        left: '50%',
                                        bottom: 0,
                                        transform: `translateX(-50%) translateY(50%) scale(${1 / props.scale})`,
                                        cursor: 'pointer',
                                        zIndex: 10
                                    }}
                                    onClick={addRow}
                                    onMouseOver={() => { props.changeAddIconIndex(`row_${r}`) }}
                                    onMouseLeave={() => { props.changeAddIconIndex('') }}
                                >
                                    <i className="iconfont icon-tianjia1-copy" data-serial={r}></i>
                                </div>
                            )}

                            <div className='table-back-box' style={{ width: totalWidth, left: 20 / props.scale }}></div>
                        </div>
                    );
                })}
            </div>
            <div
                className={`table-controller-col-select ${
                    props.isDragging || props.activeControllerLine ? 'hide' : ''
                }`}
                style={{
                    top: -12 / props.scale,
                    height: 12 / props.scale,
                }}
            >
                {props.colWidth.map((width, c) => {
                    const isSelected = startCol <= c && c <= endCol;
                    const isHoverResize = startCol === endCol && isColFull && isSelected;
                    const showLeftAdd = hoverPosition.type === 'col' && hoverPosition.index === c && hoverPosition.position === 'left';
                    const showRightAdd = hoverPosition.type === 'col' && hoverPosition.index === c && hoverPosition.position === 'right';

                    return (
                        <div
                            className={`table-controller-col-selector ${isSelected ? 'active' : ''} ${
                                isColPartial ? 'partial' : ''
                            } ${isColFull ? 'full' : ''} ${isHoverResize ? 'hoveResize' : ''}`}
                            style={{
                                width,
                                borderWidth,
                                background: props.hoverBgcIndex === `col_${c}` ? isColPartial ? '#F588A2' : '#FFDBE3' : '',
                                borderRadius:
                                    c === 0
                                        ? `${borderRadius}px 0 0 ${borderRadius}px`
                                        : c === props.colWidth.length - 1
                                        ? `0 ${borderRadius}px ${borderRadius}px 0`
                                        : 0,
                            }}
                            key={c}
                            data-model="selectRowAndCol"
                            data-type="col"
                            data-col={c}
                            onMouseDown={props.onSelectorMouseDown}
                            onMouseMove={(e) => handleColMouseMove(e, c)}
                            onMouseEnter={() => {
                                if (!isHoverResize) {
                                    props.changeHoverBgcIndex(`col_${c}`); 
                                }
                            }}
                            onMouseLeave={() => {
                                handleMouseLeave();
                                if (!isHoverResize) {
                                    props.changeHoverBgcIndex('');
                                }
                            }}
                        >
                            {/* 左侧添加按钮 */}
                            {showLeftAdd && (
                                <div
                                    className='add-icon-box add-icon-box-left'
                                    style={{
                                        position: 'absolute',
                                        top: '50%',
                                        left: 0,
                                        transform: `translateY(-50%) translateX(-50%) scale(${1 / props.scale})`,
                                        cursor: 'pointer',
                                        zIndex: 10
                                    }}
                                    onClick={addCol}
                                    onMouseOver={() => { props.changeAddIconIndex(`col_${c-1}`) }}
                                    onMouseLeave={() => { props.changeAddIconIndex('') }}
                                >
                                    <i className="iconfont icon-tianjia1-copy" data-serial={c-1}></i>
                                </div>
                            )}

                            {/* 右侧添加按钮 */}
                            {showRightAdd && (
                                <div
                                    className='add-icon-box add-icon-box-right'
                                    style={{
                                        position: 'absolute',
                                        top: '50%',
                                        right: 0,
                                        transform: `translateY(-50%) translateX(50%) scale(${1 / props.scale})`,
                                        cursor: 'pointer',
                                        zIndex: 10
                                    }}
                                    onClick={addCol}
                                    onMouseOver={() => { props.changeAddIconIndex(`col_${c}`) }}
                                    onMouseLeave={() => { props.changeAddIconIndex('') }}
                                >
                                    <i className="iconfont icon-tianjia1-copy" data-serial={c}></i>
                                </div>
                            )}

                            <div className='table-back-box' style={{ height: totalHeight, top: 20 / props.scale}}></div>
                        </div>
                    );
                })}
            </div>
        </>
    );
}

export function MoveController(props: { scale: number, isDragging: boolean, activeControllerLine: string }) {
    return (
        <div
            className={ props.isDragging || props.activeControllerLine ? 'table-controller-move-controller hide' : 'table-controller-move-controller'}
            style={{ top: -72 / props.scale, transform: `scale(${1 / props.scale}) ` }}
            onClick={() => assetManager.setPv_new(7066)}
        >
            <i className="iconfont icon-yidong1"></i>
        </div>
    );
}

export function RightAddCol(props: { scale: number; maxRow: number; maxCol: number, isDragging: boolean, activeControllerLine: string }) {
    return (
        <div
            className={ props.isDragging || props.activeControllerLine ? 'table-controller-right-add-col hide' : 'table-controller-right-add-col'}
            style={{ right: -32 / props.scale, transform: `scale(${1 / props.scale}) ` }}
            data-type="col"
            data-max-row={props.maxRow}
            data-max-col={props.maxCol}
            onClick={tableAddMaxRowAndMaxCol}
        >
            <i className="iconfont icon-tianjia1-copy"></i>
        </div>
    );
}

export function BottomAddRow(props: { scale: number; maxRow: number; maxCol: number, isDragging: boolean, activeControllerLine: string }) {
    return (
        <div
            className={props.isDragging || props.activeControllerLine ? 'table-controller-bottom-add-row hide' : 'table-controller-bottom-add-row'}
            style={{ bottom: -32 / props.scale, transform: `scale(${1 / props.scale}) ` }}
            data-type="row"
            data-max-row={props.maxRow}
            data-max-col={props.maxCol}
            onClick={tableAddMaxRowAndMaxCol}
        >
            <i className="iconfont icon-tianjia1-copy"></i>
        </div>
    );
}

function InsertRow(props: { rowHeight: number[]; scale: number, serial: number, isDragging: boolean, activeControllerLine: string }) {
    const scale = /* Math.min( */props.scale/* , 1) */;
    const transform = `translateY(-50%) translateX(-100%) scale(${1 / scale}) `;
    return (
        <div
            className={'table-controller-insert'}
            style={{
                left: -21 / scale,
                transform
            }}
            onMouseDown={stopPropagation}
        >
            <div onClick={addRow} className={props.isDragging || props.activeControllerLine ? 'hide' : 'table-controller-insert-row'} style={{ top: 0 }} key={-1}>
                <div className="insert-row-placeholder"></div>
                <div className='icon-box' data-serial={props.serial}>
                    <i className="iconfont icon-tianjia1-copy" data-serial={props.serial}></i>
                </div>
            </div>
        </div>
    );
}

function InsertCol(props: { colWidth: number[]; scale: number, serial: number, isDragging: boolean, activeControllerLine: string }) {
    const scale = /* Math.min( */props.scale/* , 1) */;
    const transform = `translate(-50%, -100%) scale(${1 / scale}) `;
    return (
        <div
            className="table-controller-insert"
            style={{
                top: -21 / scale,
                transform
            }}
            onMouseDown={stopPropagation}
        >
            <div onClick={addCol} className={ props.isDragging || props.activeControllerLine ? 'hide' : 'table-controller-insert-col'} style={{ left: 0 }} key={-1}>
                <div className="insert-col-placeholder"></div>
                <div className='icon-box' data-serial={props.serial}>
                    <i className="iconfont icon-tianjia1-copy" data-serial={props.serial}></i>
                </div>
            </div>
        </div>
    );
}
