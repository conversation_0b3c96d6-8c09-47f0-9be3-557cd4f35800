.asset-table2 {
    width: 100%;
    height: 100%;
    position: relative;
    transform-origin: 0 0;

    &:focus {
        outline: none;
    }

    * {
        box-sizing: border-box;
    }

    .table-warper {
        width: 100%;
        height: 100%;
    }

    .table-main {
        width: 100%;
        height: 100%;
        table-layout: fixed;
        box-sizing: border-box;
        border-spacing: 0;
        border-collapse: collapse;

        td {
            overflow: hidden;
        }

        .table-td-container {
            position: relative;
        }

        .table-td-content {
            position: absolute;
            left: 5px;
            top: 5px;
            right: 5px;
            bottom: 5px;
            width: auto;
            height: auto;
            -webkit-user-modify: read-write-plaintext-only;
            word-break: break-all;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;

            &:focus {
                outline: none;
                cursor: text;
            }
        }
    }

    .table-controller {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 2;
        pointer-events: none;

        .table-cell-controller {
            width: 100%;
            height: 100%;
            // position: absolute;
            // left: 0;
            // top: 0;
            // z-index: 2;
            // transform-origin: 0 0;

            .table-cell-controller-cell {
                position: absolute;
                pointer-events: initial;
                z-index: 2;

                &.merge {
                    z-index: 10;
                }
            }
        }

        .table-controller-selected-cells {
            position: absolute;
            visibility: hidden;
            z-index: 5;
            box-sizing: border-box;
        }

        .show-rest-drag-line-col {
            position: absolute;
            z-index: 99999;
            background-color: #EF3964;
            // top: 0;
        }
        .show-rest-drag-line-row {
            z-index: 99999;
            position: absolute;
            background-color: #EF3964;
            left: 0;
        }

        .table-controller-row-lines {
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
            z-index: 3;

            .table-controller-row-line {
                position: absolute;
                left: 0;
                pointer-events: initial;
                cursor: ns-resize;
                width: 100%;
                height: 0;
                transform: translateY(-50%);
                display: flex;
                justify-content: center;
                align-items: center;

                &:hover {
                    .table-controller-line {
                        opacity: 1;
                    }
                }

                &.active {
                    opacity: 1;
                }

                &.disable {
                    pointer-events: none;
                }

                .table-controller-line {
                    width: 100%;
                    height: 0;
                    border-top: 1px solid #EF3964;
                    opacity: 0;
                }
                .show-line{
                    opacity: 1;
                }
                .table-controller-insert {
                    position: absolute;
                    top: 0;
                    width: 16px;
                    height: 16px;
                    top: 50%;
                    transform-origin: 100% 50%;
                    display: none;

                    .hide {
                        opacity: 0;
                    }
    
                    .table-controller-insert-row {
                        // transform-origin: 0% 50%;
                        pointer-events: initial;
                        cursor: initial;
                        width: 16px;
                        height: 16px;
                        cursor: initial;
    
                        &:hover {
                            .insert-row-placeholder {
                                display: none;
                            }
                            .icon-box {
                                display: block;
                                background-color: #EF3964;
                                color: #ffffff;
                            }
                        }
    
                        .insert-row-placeholder {
                            position: absolute;
                            right: 20%;
                            top: 50%;
                            transform: translate(50%, -50%);
                            width: 3px;
                            height: 3px;
                            background: #dddfe4;
                            border-radius: 50%;
                            cursor: initial;
                        }

                        .icon-box {
                            display: none;
                            width: 16px;
                            height: 16px;
                            border-radius: 50%;
                            background-color: #ffffff;
                            color: #EF3964;
                            cursor: pointer;
                            .icon-tianjia1-copy {
                                width: 16px;
                                height: 16px;
                                text-align: center;
                                border-radius: 50%;
                                cursor: pointer;
                                position: absolute;
                                top: 50%;
                                left: 50%;
                                transform: translate(-50%, -50%) scale(0.6);
                            }
                        }
                    }
                }
            }
        }

        .table-controller-col-lines {
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
            z-index: 3;

            .table-controller-col-line {
                position: absolute;
                top: 0;
                pointer-events: initial;
                cursor: ew-resize;
                width: 0;
                height: 100%;
                transform: translateX(-50%);
                display: flex;
                justify-content: center;
                align-items: center;

                &:hover {
                    .table-controller-line {
                        opacity: 1;
                    }
                }

                &.active {
                    opacity: 1;
                }

                &.disable {
                    pointer-events: none;
                }

                .table-controller-line {
                    width: 0;
                    height: 100%;
                    border-left: 1px solid #EF3964;
                    opacity: 0;
                }
                .show-line{
                    opacity: 1;
                }
                .table-controller-insert {
                    width: 16px;
                    height: 16px;
                    position: absolute;
                    left: 50%;
                    transform-origin: 50% 100%;
                    display: none;
                    .hide {
                        opacity: 0;
                    }

                    .table-controller-insert-col {
                        pointer-events: initial;
                        cursor: pointer;
                        width: 16px;
                        height: 16px;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        &:hover {
                            .insert-col-placeholder {
                                display: none;
                            }
                            .icon-box {
                                display: block;
                                background-color: #EF3964;
                                color: #ffffff;
                            }
                        }

                        .insert-col-placeholder {
                            position: absolute;
                            bottom: 20%;
                            left: 50%;
                            transform: translate(-50%, 50%);
                            width: 3px;
                            height: 3px;
                            background: #dddfe4;
                            border-radius: 50%;
                            cursor: initial;
                        }
                        .icon-box {
                            display: none;
                            width: 16px;
                            height: 16px;
                            line-height: 16px;
                            text-align: center;
                            border-radius: 50%;
                            background-color: #ffffff;
                            color: #EF3964;
                            .icon-tianjia1-copy {
                                width: 16px;
                                height: 16px;
                                text-align: center;
                                line-height: 16px;
                                border-radius: 50%;
                                cursor: pointer;
                                position: absolute;
                                // left: 50%;
                                bottom: 50%;
                                transform: translate(-50%, 50%) scale(0.6);
                            }
                        }
                    }
                }
            }
        }

        .table-controller-row-select {
            position: absolute;
            top: 0;
            box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.1);

            &.hide {
                display: none;
            }

            .table-controller-row-selector {
                background: #FDEBF0;
                border: 1px solid #dddfe4;
                border-bottom: none;
                cursor: pointer;
                pointer-events: initial;
                position: relative;

                &:first-of-type {
                    border-radius: 2px 2px 0 0;
                }

                &:last-of-type {
                    border-bottom: 1px solid #dddfe4;
                    border-radius: 0 0 2px 2px;
                }
                &.active {
                    &.partial.full {
                        background: #F588A2;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                }
                &.hoveResize {
                    cursor: move;
                }

                .add-icon-box {
                    width: 16px;
                    height: 16px;
                    border-radius: 50%;
                    background-color: #ffffff;
                    color: #EF3964;
                    border: 1px solid rgba(0,0,0,0.2);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    
                    .icon-tianjia1-copy {
                        width: 16px;
                        height: 16px;
                        text-align: center;
                        border-radius: 50%;
                        transform: scale(0.6);
                    }
                    
                    &:hover {
                        background-color: #EF3964;
                        color: #fff;
                    }
                }

                .delete-row-col-btn {
                    position: absolute;
                    transform: translateY(-50%);
                    transition: all .3s;
                    background-color: #ffffff;
                    box-shadow: 0px 0px 2px 0px rgba(0,0,0,0.2);
                    border-radius: 50%;
                    color: #000;
                    &:hover {
                        .btn {
                            color: #ffffff;
                            background-color: #DA615D;
                        }
                    }
                    .btn {
                        border-radius: 50%;
                        pointer-events: none;
                        height: 100%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        box-shadow: 0px 0px 2px 0px rgba(0,0,0,0.2);
                        i {
                            width: 16px;
                            height: 16px;
                            transform: scale(0.5);
                        }
                    }
                }
                .table-back-box {
                    height: 100%;
                    visibility: hidden;
                    position: absolute;
                    background-color: #DA615D;
                    opacity: 0.25;
                }
            }
        }

        .select-col-drag {
            position: absolute;
            z-index: 99999;
            .drag-top-block {
                background-color: #EF3964;
                border: 1px solid #DDDFE4;
                color: #B8C3FF;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: move;
                pointer-events: auto;
            }
            .drag-col {
                position: absolute;
                width: 100%;
                border-left-style: solid;
                border-color: #EF3964;
                border-right-style: solid;
                .item {
                    border-bottom-style: solid;
                    border-color: #EF3964;
                    &:nth-child(1) {
                        border-top-style: solid;
                    }
                }
            }
        }
        .select-row-drag {
            position: absolute;
            z-index: 99999;
            .drag-top-block {
                background-color: #EF3964;
                border: 1px solid #DDDFE4;
                color: #B8C3FF;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: move;
                pointer-events: auto;
                i {
                    transform: rotate(90deg);
                }
            }
            .drag-row {
                position: absolute;
                height: 100%;
                border-bottom-style: solid;
                border-color: #EF3964;
                border-top-style: solid;
                display: flex;
                .item {
                    border-right-style: solid;
                    border-color: #EF3964;
                    &:nth-child(1) {
                        border-left-style: solid;
                    }
                }
            }
        }

        .table-controller-col-select {
            position: absolute;
            left: 0;
            font-size: 0;
            white-space: nowrap;
            box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.1);
            display: flex;

            &.hide {
                display: none;
            }

            .table-controller-col-selector {
                display: inline-block;
                height: 100%;
                background: #FDEBF0;
                border: 1px solid #dddfe4;
                border-right: none;
                cursor: pointer;
                pointer-events: initial;
                position: relative;

                &:first-of-type {
                    border-radius: 2px 0 0 2px;
                }

                &:last-of-type {
                    border-right: 1px solid #dddfe4;
                    border-radius: 0px 2px 2px 0;
                }

                &.active {

                    &.partial.full {
                        background: #F588A2;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                    &.hoveResize {
                        cursor: move;
                    }
                }
                .add-icon-box {
                    width: 16px;
                    height: 16px;
                    border-radius: 50%;
                    background-color: #ffffff;
                    color: #EF3964;
                    border: 1px solid rgba(0,0,0,0.2);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    
                    .icon-tianjia1-copy {
                        width: 16px;
                        height: 16px;
                        text-align: center;
                        border-radius: 50%;
                        transform: scale(0.6);
                    }
                    
                    &:hover {
                        background-color: #EF3964;
                        color: #fff;
                    }
                }

                .delete-row-col-btn {
                    position: absolute;
                    transform: translateX(-50%);
                    transition: all .3s;
                    background-color: #ffffff;
                    border-radius: 50%;
                    box-shadow: 0px 0px 2px 0px rgba(0,0,0,0.2);
                    color: #000;
                    &:hover {
                        .btn {
                            color: #ffffff;
                            background-color: #DA615D;
                        }
                    }
                    .btn {
                        border-radius: 50%;
                        pointer-events: none;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        box-shadow: 0px 0px 2px 0px rgba(0,0,0,0.2);
                        i {
                            width: 16px;
                            height: 16px;
                            transform: scale(0.5);
                        }
                    }
                }
                .table-back-box {
                    visibility: hidden;
                    width: 100%;
                    position: absolute;
                    background-color: #DA615D;
                    opacity: 0.25;
                }
            }
        }

        .table-controller-move-controller,
        .table-controller-right-add-col,
        .table-controller-bottom-add-row {
            position: absolute;
            width: 22px;
            height: 22px;
            pointer-events: initial;
            color: #586ee0;
            background: #ffffff;
            box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.4);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: move;

            &:hover {
                background: #586ee0;
                color: #fff;
            }

            .icon-yidong1,
            .icon-tianjia1-copy {
                font-size: 16px;
            }
            &.hide {
                display: none;
            }
        }

        .table-controller-move-controller {
            left: 50%;
            margin-left: -11px;
            transform-origin: 50% 0;
        }

        .table-controller-right-add-col {
            top: 50%;
            margin-top: -11px;
            cursor: pointer;
            transform-origin: 100% 50%;
        }

        .table-controller-bottom-add-row {
            left: 50%;
            margin-left: -11px;
            cursor: pointer;
            transform-origin: 50% 100%;
        }
    }
}
