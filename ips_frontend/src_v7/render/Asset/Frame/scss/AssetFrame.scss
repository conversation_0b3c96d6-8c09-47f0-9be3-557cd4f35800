.frame-asset {
    width: 100%;
    height: 100%;
    svg {
        pointer-events: none;
    }
}

.drag-image-tooltip {
    position: absolute;
    background: rgba(31, 26, 27, 0.7);
    width: 116px;
    height: 40px;
    border-radius: 5px;
    padding: 0 18px;
    line-height: 40px;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
    white-space: nowrap;
    box-sizing: border-box;
    top: -40px;
    transform: translate(-50%, -15%);
    .iconfont {
        margin-right: 12px;
        font-size: 16px;
    }
}
.frame-edit-wrap {
    position: absolute;
    // width: 87px;
    height: 42px;
    padding: 0;
    opacity: 1;
    box-sizing: border-box;
    z-index: 100;
    display: flex;
    align-items: center;
    border-radius: 12px;
    border: 0.5px solid #dcdcdc;
    background: #fff;
    /* Shadows/弹窗一级下拉菜单 */
    box-shadow: 0px 4px 14px 0px rgba(31, 26, 27, 0.16);

    &.hide {
        display: none;
    }

    .tooltip {
        border-radius: 8px;
        &.bottom {
            top: 50px !important;
        }
        &.top {
            bottom: 50px !important;
        }
    }

    .frame-delete-popover {
        position: absolute;
        width: auto !important;
        height: 80px !important;
        padding: 14px !important;
        background: #ffffff;
        box-shadow: 0px 1px 6px 0px rgba(0, 0, 0, 0.1);
        border-radius: 5px 5px 5px 5px;
        box-sizing: border-box;
        opacity: 1;
        border: 1px solid #e9e8e8;
        .delete-item {
            white-space: nowrap;
            font-weight: 600;
            cursor: pointer;
            &:hover {
                color: #ef3964;
            }
            &:not(:last-child) {
                margin-bottom: 10px;
            }
        }
    }
    .frame-edit-toolbox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        // width: 87px;
        // height: 40px;
        padding: 0px 10px;
        // box-shadow: 0px 1px 6px 0px rgba(0, 0, 0, 0.1);

        .iconfont {
            cursor: pointer;
            font-size: 20px;
        }
        .splitLine {
            width: 1px;
            height: 18px;
            background: #d2d1d1;
        }
        .textProperty {
            display: flex;
            height: 30px;
            margin: 5px 0;
            justify-content: center;
            align-items: center;
            border-radius: 8px;
            cursor: pointer;
        }
        i {
            display: block;
            width: 30px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            margin: 0 5px;
            &:hover {
                border-radius: 5px;
                background: #f7f7f7;
            }
            &.active {
                color: #ef3964;
            }
        }

        .effect-text {
            display: flex;
            width: 44px;
            height: 30px;
            margin: 5px 8px;
            justify-content: center;
            align-items: center;
            gap: 10px;
            flex-shrink: 0;
            border-radius: 8px;

            cursor: pointer;

            color: #000;
            font-size: 14px;
            font-style: normal;
            font-weight: 500;
            line-height: normal;

            &:hover {
                background: #f7f7f7;
            }
            &.active {
                background-color: #fdebf0;
                color: #ef3964;
            }
        }

        .icon-text {
            display: flex;
            height: 30px;
            padding: 0 8px;
            margin: 5px 8px;
            justify-content: center;
            align-items: center;
            gap: 4px;
            flex-shrink: 0;
            border-radius: 8px;

            cursor: pointer;

            .icon {
                width: 20px;
                height: 20px;
                line-height: 20px;
                margin: 0;
            }

            .text {
                width: 56px;
                height: 20px;
                color: #000;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
            }

            &:hover {
                background: #f7f7f7;
            }
            &.active {
                background-color: #fdebf0;
                color: #ef3964;
            }
        }

        .font-family {
            width: 160px;
            height: 30px;
            margin-left: 8px;
            border-radius: 8px;
            border: 1px solid #e9e8e8;
            background-position: 8px center;
            background-repeat: no-repeat;
            background-size: 90%;

            cursor: pointer;

            &:hover {
                background-color: #f7f7f7;
            }
        }

        .font-size {
            display: inline-flex;
            margin-left: 4px;
            margin-right: 8px;
            height: 20px;
            padding: 5px 8px;
            justify-content: center;
            align-items: center;
            gap: 10px;
            position: relative;

            cursor: pointer;

            border-radius: 8px;
            border: 1px solid #e9e8e8;
            background: #fff;

            &:hover {
                background-color: #f7f7f7;
            }

            .fontsizeListArea {
                padding-top: 8px;
                position: absolute;

                top: 30px;
                left: -5px;

                .fontsizeList {
                    width: 106px;
                    border-radius: 8px;
                    max-height: 280px;
                    background-color: white;

                    overflow-x: hidden;
                    overflow-y: auto;
                    scroll-behavior: smooth;
                    overscroll-behavior: contain;

                    li {
                        padding: 6px 12px;
                        height: 40px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        text-align: center;

                        color: #1f1a1b;
                        font-family: 'PingFang SC';
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: normal;

                        &:hover {
                            background-color: #f7f7f7;
                        }
                    }

                    .active {
                        background-color: #fdebf0;
                        color: #ef3964;
                    }
                }
            }

            .fontSizeSelectInput {
                max-width: 30px;
                text-align: center;
                border: 0;
                background: none;
                outline: 0;
                box-shadow: none;
            }

            i {
                width: 14px;
                height: 14px;
                margin: 0px;
                font-size: 14px;
                line-height: 14px;
            }

            p {
                color: #000;
                font-family: 'PingFang SC';
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
            }
        }

        .font-color {
            display: flex;
            align-items: center;
            border-radius: 8px;
            padding: 5px;
            margin: 5px 8px;

            &:hover {
                background-color: #f7f7f7;
            }
        }

        .font-weight {
            display: flex;
            width: 20px;
            height: 20px;
            justify-content: center;
            align-items: center;
            gap: 10px;
            flex-shrink: 0;
            border-radius: 8px;

            &:hover {
                background: #f7f7f7;
            }
            &.active {
                background-color: #fdebf0;
                color: #ef3964;
            }
        }

        .rotation {
            rotate: -90deg;
        }

        .float-tool {
            position: relative;
            top: 10px;
            left: -65px;
            padding: 5px 8px;
            border-radius: 8px;

            width: max-content;
            display: flex;
            align-items: center;

            background-color: white;
        }

        .float-align-tool {
            position: relative;
            top: 10px;
            left: -5px;
            padding: 5px 8px;
            border-radius: 8px;

            width: max-content;
            display: flex;
            flex-direction: column;
            gap: 4px;

            background-color: white;

            .item {
                display: flex;
                align-items: center;
                cursor: pointer;

                &:hover {
                    background: #f7f7f7;
                }
                &.active {
                    color: #ef3964;
                }
            }
        }

        .float-space-tool {
            position: relative;
            top: 10px;
            left: -120px;
            padding: 5px 8px;
            border-radius: 4px;

            cursor: pointer;

            width: max-content;
            display: flex;
            flex-direction: column;
            align-items: center;

            background-color: white;
        }

        .float-layer-tool {
            position: relative;
            top: 10px;
            left: -5px;
            padding: 5px 8px;
            border-radius: 4px;

            width: max-content;
            display: flex;
            align-items: center;

            background-color: white;

            .layersWrap {
                width: 248px;
                height: 208px;
                background: #ffffff;
                box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.2);
                border-radius: 8px;
                position: absolute;
                margin-top: 15px;
                left: 6px;
                z-index: 20;

                display: flex;

                .layersWrapLeft {
                    width: 80px;
                    margin-right: 20px;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    .layerSlideBar {
                        flex: 1;
                    }
                    .layersWrapBtn {
                        margin: 0 auto;
                        width: 50px;
                        cursor: pointer;
                        text-align: center;
                    }
                    .layersWrapBtnTop {
                        margin-top: 14px;
                        margin-bottom: 6px;
                    }
                    .layersWrapBtnBottom {
                        margin-bottom: 12px;
                    }

                    .SlideBarVertical {
                        width: 2px;
                        height: 120px;
                        margin: 10px auto;
                        position: relative;

                        .slideBarButtonBar {
                            background: rgba(255, 69, 85, 1);
                            border-radius: 1px;
                            width: inherit;
                            height: 0px;
                            position: absolute;
                            bottom: 0;
                        }
                        .slideBarButton {
                            position: absolute;
                            left: -8px;
                            bottom: -6px;
                            width: 18px;
                            height: 18px;
                            background: rgba(255, 69, 85, 1);
                            box-shadow: 0 1px 3px 1px rgba(255, 69, 85, 0.3);
                            border-radius: 50%;
                            cursor: pointer;
                            .sliderBarShowNumber {
                                position: absolute;
                                left: 24px;
                                font-size: 14px;
                                top: -2px;
                            }
                        }
                        .slideBarMask {
                            width: inherit;
                            height: inherit;
                            border-radius: 1px;
                            // background: rgba(255, 69, 85, 0.2);
                            background: rgba(255, 69, 85, 1);
                        }
                        .tabBarbox {
                            width: 5px;
                            position: absolute;
                            top: -1px;
                            left: 8px;
                            z-index: -99;
                        }
                        .tabBarcar {
                            width: 5px;
                            height: 1px;
                            background: rgba(95, 95, 106, 1);
                        }
                    }
                }

                .layersWrapRight {
                    .layersWrapRightTitle {
                        margin-top: 14px;
                        margin-bottom: 6px;
                    }
                    .layersWrapRightContent {
                        width: 120px;
                        height: 138px;
                        box-sizing: border-box;
                        border-radius: 2px;
                        background: #ffffff;
                        border: 1px solid #f4f4f4;
                        padding: 1px;
                        .layersWrapRightItems {
                            width: 116px;
                            height: 134px;
                            .layersWrapRightBtnItemBtn {
                                box-sizing: border-box;
                                padding: 4px;
                                display: inline-block;
                                width: 36px;
                                height: 42px;
                                background: #efefef;
                                cursor: pointer;
                                vertical-align: middle;
                                font-size: 0;
                                margin-right: 4px;
                                margin-bottom: 4px;

                                &:hover {
                                    background: #e2e2e2;
                                }

                                .circle {
                                    margin-top: 13px;
                                    display: inline-block;
                                    background: #202020;
                                    border-radius: 50%;
                                    width: 10px;
                                    height: 10px;
                                }
                                &.t,
                                &.b {
                                    .iconfont {
                                        font-size: 19px;
                                    }
                                }

                                &.t,
                                &.c,
                                &.b {
                                    text-align: center;
                                }
                                &.l,
                                &.c,
                                &.r {
                                    line-height: 34px;
                                }
                                &.rt,
                                &.r,
                                &.rb {
                                    text-align: right;
                                    margin-right: 0;
                                }
                                &.lb,
                                &.b,
                                &.rb {
                                    // padding-top: 22px;
                                    margin-bottom: 0;
                                }
                                &.b {
                                    // padding-top: 20px;
                                }
                            }
                        }
                    }
                }
            }
        }

        .svg-color {
            display: flex;
            align-items: center;
            border-radius: 8px;
            padding: 5px;
            margin: 5px 8px;

            &:hover {
                background-color: #f7f7f7;
            }

            .color-block {
                border: 1px solid rgb(233, 232, 232);
                margin: 0px;
                width: 20px;
                height: 20px;
                border-radius: 2px;

                display: flex;
                justify-content: center;
                align-items: center;
                flex-shrink: 0;
            }
        }

        .table-border {
            height: 20px;
            width: 30px;
            margin: 0 10px;

            .tableBorderStylePanel {
                width: 248px;
                height: 270px;
                border-radius: 8px;
                background: #fff;
                /* Shadows/弹窗一级下拉菜单 */
                box-shadow: 0px 4px 14px 0px rgba(31, 26, 27, 0.16);
                padding: 16px;
                box-sizing: border-box;
                .typeTitle {
                    font-weight: 500;
                    color: #1f1a1b;
                    line-height: 14px;
                    font-size: 14px;
                    margin-bottom: 10px;
                }
                .scopeBox {
                    display: grid;
                    grid-template-columns: repeat(5, 1fr);
                    /* grid-template-rows: repeat(2, 100px); */
                    gap: 7px;
                    margin-bottom: 12px;
                    div {
                        width: 32px;
                        height: 32px;
                        padding: 6px;
                        border-radius: 4px 4px 4px 4px;
                        margin: 0;
                        cursor: pointer;
                        box-sizing: border-box;
                        &:hover {
                            background: #f7f7f7;
                        }
                        &.active {
                            background: #f7f7f7;
                        }
                        img {
                            width: 20px;
                        }
                    }
                    .disableDiv {
                        cursor: no-drop;
                    }
                }
                .colorBox {
                    width: 100%;
                    height: 24px;
                    margin-bottom: 16px;
                    .color {
                        width: 100%;
                        height: 24px;
                        border-radius: 8px;
                        border: 1px solid rgba(0, 0, 0, 0.05);
                        background: var(---, #a5a3a4);
                        cursor: pointer;
                    }
                }
                .typeDegreeBox {
                    width: 100%;
                    height: 32px;
                    display: flex;
                    .type {
                        display: inline-flex;
                        // width: 112px;
                        width: 100%;
                        height: 32px;
                        border-radius: 8px;
                        background: var(---, #e9e8e8);
                        align-items: center;
                        padding: 0 10px;
                        cursor: pointer;
                        position: relative;
                        margin-right: 0;
                        .line {
                            width: 141px;
                            height: 2px;
                            border-bottom: 2px #333333;
                        }
                        .icon-xiala {
                            font-size: 10px;
                            position: absolute;
                            right: 12px;
                        }
                    }
                    .degree {
                        display: inline-flex;
                        width: 72px;
                        height: 32px;
                        background: #f6f7f9;
                        border-radius: 2px 2px 2px 2px;
                        display: inline-flex;
                        align-items: center;
                        padding: 0 12px;
                        cursor: pointer;
                        input {
                            width: 24px;
                            padding: 0 2px;
                            font-size: 12px;
                            margin-left: 8px;
                        }
                        span {
                            margin-left: 12px;
                        }
                        input::-webkit-outer-spin-button,
                        input::-webkit-inner-spin-button {
                            -webkit-appearance: none;
                        }
                    }
                    .typeSelects {
                        padding: 4px 0;
                        position: absolute;
                        width: 216px;
                        top: 36px;
                        left: 0;
                        border-radius: 8px;
                        background: #fff;
                        /* Shadows/二级下拉菜单 */
                        box-shadow: 0px 8px 20px 0px rgba(31, 26, 27, 0.16);
                        .typeItem {
                            height: 32px;
                            display: flex;
                            align-items: center;
                            padding: 0 10px;
                            &:hover {
                                background: var(---, #f7f7f7);
                            }
                            .text {
                                font-size: 14px;
                                color: #000;
                            }
                        }
                        .icon-gou1 {
                            position: absolute;
                            right: 12px;
                            color: #1f1a1b;
                        }
                    }
                }
            }
        }
    }
}

.image-clip-toolbox {
    position: absolute;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    font-weight: 600;
    padding: 0 16px;
    color: #1f1a1b;
    background: #ffffff;
    box-sizing: border-box;
    box-shadow: 0px 1px 6px 0px rgba(0, 0, 0, 0.1);
    border-radius: 5px 5px 5px 5px;
    opacity: 1;
    border: 1px solid #e9e8e8;
    .success {
        position: relative;
        display: flex;
        align-items: center;
        color: #ef3964;
        // width: 50%;
        cursor: pointer;
        &::after {
            content: '';
            display: block;
            position: absolute;
            right: -10px;
            width: 2px;
            height: 20px;
            background-color: #e9e8e8;
        }
    }

    .cancel,
    .replace {
        display: flex;
        align-items: center;
        cursor: pointer;
    }
    .replace {
        position: relative;
        &::after {
            content: '';
            display: block;
            position: absolute;
            right: -10px;
            width: 2px;
            height: 20px;
            background-color: #e9e8e8;
        }
    }
    .text {
        margin-left: 5px;
    }
}

.quick-menu-wrap {
    position: absolute;
    // width: 87px;
    height: 42px;
    padding: 5px 10px;
    box-sizing: border-box;
    z-index: 99;
    display: flex;
    align-items: center;
    border-radius: 45px;
    border: 0.5px solid #dcdcdc;
    background: #fff;
    /* Shadows/弹窗一级下拉菜单 */
    box-shadow: 0px 4px 14px 0px rgba(31, 26, 27, 0.16);
    transform: translateX(-50%);
    .tooltip {
        &.bottom {
            top: 46px !important;
        }
        &.top {
            bottom: 46px !important;
        }
        border-radius: 8px;
    }
    .quickGroupBtn {
        color: #000;
        font-size: 14px;
        font-weight: 600;
        white-space: nowrap;
        cursor: pointer;
        padding: 5px 8px;
        &:hover {
            border-radius: 100px;
            background: var(---, #f7f7f7);
        }
    }
    .imageIcon {
        width: 30px;
        height: 30px;
        padding: 2px;
        box-sizing: border-box;
        &:hover {
            background: #f7f7f7;
            border-radius: 50%;
        }
        img {
            width: 100%;
            height: 100%;
        }
    }

    .tooltip-wrapper {
        margin-right: 8px;
        &:last-child {
            margin-right: 0;
        }
        .iconfont {
            width: 30px;
            height: 30px;
            display: block;
            box-sizing: border-box;
            padding: 5px;
            font-size: 20px;
            cursor: pointer;
            &:hover {
                background: #f7f7f7;
                border-radius: 50%;
            }
        }
    }
    .aiTextOptionWrap {
    }

    .aiTextOption {
        position: relative;
        cursor: pointer;
        .newIcon {
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            top: -12px;
            left: 27px;
            line-height: 12px;
            padding: 2px 4px;
            font-size: 10px;
            font-weight: 600;
            background: var(---, #ef3964);
            color: #fff;
            box-sizing: border-box;
        }
        .option-wrap {
            position: absolute;
            bottom: -165px;
            left: 0;
            // width: 84px;
            // height: 130px;
            border-radius: 8px;
            background: #fff;
            box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.15);
            padding: 5px;
            box-sizing: border-box;

            .option-item {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                width: 90px;
                height: 30px;
                border-radius: 6px;
                box-sizing: border-box;
                font-size: 14px;
                text-wrap: nowrap;
                cursor: pointer;
                .svgIcon {
                    margin-left: 8px;
                    margin-right: 8px;
                    svg {
                        width: 18px;
                        height: 18px;
                    }
                }
                &:hover {
                    background: var(---, #f7f7f7);
                }
                i {
                    font-size: 16px;
                    margin-right: -3px;
                }
            }
        }
    }
    .tableDelPanel {
        width: 126px;
        height: 140px;
        border-radius: 8px;
        background: #fff;
        padding: 10px 0;
        /* Shadows/弹窗一级下拉菜单 */
        box-shadow: 0px 4px 14px 0px rgba(31, 26, 27, 0.16);
        box-sizing: border-box;
        position: absolute;
        top: 45px;
        .item {
            width: 126px;
            height: 40px;
            display: flex;
            align-items: center;
            padding: 9px 12px;
            box-sizing: border-box;
            color: var(---, #1f1a1b);
            font-size: 14px;
            cursor: pointer;
            &:hover {
                background: #f7f7f7;
            }
            span {
                &:first-child {
                    font-size: 20px;
                    margin-right: 8px;
                }
            }
        }
    }
}
