import React, { Component } from 'react'
import WebFont from 'webfontloader';
import equal from 'fast-deep-equal';

import {SelectAsset} from "@v7_logic/AssetLogic";
import {storeAdapter} from '@v7_logic_core/StoreAdapter';
import {IAnyObj, IAsset, ICanvas, IPageInfo, IPageAttr} from '@v7_logic/Interface';
import {TextEditor} from '@v7_render/AssetTextEditor';
import {ImageContainer} from '@v7_render/AssetImage';
import {SVG} from '@v7_render/AssetSVG';
import {Grounp} from '@v7_render/AssetGroup';
import {Container} from '@v7_render/AssetContainer';
import {AssetTable} from '@v7_render/AssetTable';
import {AssetChart, AssetEChart} from '@v7_render/AssetChart';
import {ErrorBoundaryDecorator} from '@v7_render/ErrorBoundaryHOC';
import { PageAnimationContainer, AssetAnimationContainer } from '@v7_render/AssetAnimation';
import { AssetEffect } from '@v7_render/AssetEffect'
import { CanvasPaintedLogic } from '@v7_logic/CanvasPaintedLogic';
import { AssetHelper } from '@v7_logic/AssetHelper';
import { AssetVideoE } from './VideoE';
import { AssetTable2 } from './Table2';
import { AssetQrcode } from './Qrcode';
import { AssetFrame} from './Frame'
// 待替换组件
import { emitter } from '@component/Emitter';
import { getFontNameValueList } from '@component/IPSConfig';
import { isPointInRect } from '@component/Function';
import { assetManager } from '@component/AssetManager';


const defaultFunction = () => {/* 空函数 */ }
const userAgent = navigator.userAgent
const isSafari = userAgent.indexOf("Safari") > -1 && userAgent.indexOf("Chrome") === -1;

interface propsStruct {
    asset: IAsset;
    assetProps?: IAnyObj;
    parent?: string;
    grandparent?: string;
    isHiddenFlag?: boolean;
    isAssetActive?: boolean;
    assetEditFlag?: boolean;
    smallFontKeys?: IAnyObj;
    isShowOnly?: boolean;
    pageInfo?: IPageInfo;
    canvas?: ICanvas;
    index?: number;
    canvasWaterMaskFlag?: boolean;
    pageAttr?: IPageAttr;
    pageIndex?: number;
    rt_animatieTime?: number;
    previewing?: boolean;
    audioMuted?: boolean;
    timeLine?: number;
    rt_previewFrame?: number;
    editRef?: any;
    autoPlay?: boolean;
    hideDragTable?: boolean;
}

interface stateStruct {
    initFont: number;
    mouseOverAssetStyle: IAnyObj;
    loadBigFontEnd: boolean;
}

/**
 * 元素渲染
 */
@ErrorBoundaryDecorator()
class Asset extends Component<propsStruct, stateStruct> {


    mouseOverAssetStyle: IAnyObj;
    lastAssetRecord: IAnyObj;
    lastScaleRecord: number;
    lastToolPanelRecord: IAnyObj;
    timerSendPointRecords: IAnyObj;
    movingAssetCurrent: boolean;
    dragAssetCurrent: boolean;

    ed_selectAssetEvent: {
        (e: React.MouseEvent): void;
    };
    ed_assetStopPropagationEvent: {
        (e: React.MouseEvent): void;
    };
    ed_mouseOverAssetEvent: {
        (): void
    };
    ed_mouseLeaveAssetEvent: {
        (): void
    };
    ed_dragAssetEvent: {
        (e: React.MouseEvent): void
    };
    ed_dragTextAssetEvent: {
        (e: React.MouseEvent): void
    };
    mouseDown = 'empty';

    isUnmount = false;

    constructor(props: propsStruct) {

        super(props)
        this.state = {
            mouseOverAssetStyle: { outline: 'none' },
            initFont: 1,
            loadBigFontEnd: false
        }

        this.mouseOverAssetStyle = {
            // border: '1px dashed rgba(102,102,102,0.5)',
            // outline: '3px solid #FFD1D3',
        }

        // 优化记录
        this.lastAssetRecord = null;
        this.lastScaleRecord = 1;
        this.lastToolPanelRecord = null;
        this.movingAssetCurrent = null;
        this.dragAssetCurrent = null
        // 优化记录
        this.timerSendPointRecords = {};//延时发送的埋点

        if (!this.props.assetProps || !this.props.assetProps.showOnly) {
            this.EventDeclaration();
        }
    }

    /**
    * 初始化事件申明
    */
    EventDeclaration(): void {
        this.ed_selectAssetEvent = this.selectAssetEvent.bind(this);
        this.ed_assetStopPropagationEvent = this.assetStopPropagationEvent.bind(this);
        this.ed_mouseOverAssetEvent = this.mouseOverAssetEvent.bind(this);
        this.ed_mouseLeaveAssetEvent = this.mouseLeaveAssetEvent.bind(this);
        this.ed_dragAssetEvent = this.dragAssetEvent.bind(this);
        this.ed_dragTextAssetEvent = this.dragTextAssetEvent.bind(this);
    }

    shouldComponentUpdate(nextProps: propsStruct, nextState: stateStruct): boolean {
        const { toolPanel, canvas, work, pageInfo, rt_current_is_moving_asset, rt_current_is_drag_asset } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if (this.state.initFont != nextState.initFont) {
            return true;
        }

        if (this.props.canvasWaterMaskFlag != nextProps.canvasWaterMaskFlag) {
            return true;
        }

        const { assetProps = {} } = nextProps;
        const _canvasScale = assetProps.canvasScale || canvas.scale;

        if (_canvasScale != this.lastScaleRecord) {
            this.lastScaleRecord = _canvasScale;
            return true;
        }

        const isNotPreview = !this.props.assetProps || !this.props.assetProps.showOnly

        if (isNotPreview && this.movingAssetCurrent !== rt_current_is_moving_asset) {
            this.movingAssetCurrent = rt_current_is_moving_asset
            return true
        }

        if (isNotPreview && this.dragAssetCurrent !== rt_current_is_drag_asset) {
            this.dragAssetCurrent = rt_current_is_drag_asset
            return true
        }

        if (isNotPreview && nextProps.rt_previewFrame !== this.props.rt_previewFrame) {
            return true;
        }

        /** 解决画布预览元素动效闪烁问题 */
        if (!this.props.asset.attribute?.k?.rt_previewAnimaton?.resId &&
            nextProps.asset.attribute?.k?.rt_previewAnimaton?.resId !== this.props.asset.attribute?.k?.rt_previewAnimaton?.resId) {
            return false
        }


        if (!isNotPreview && nextProps.parent === 'canvasReadOnly') {
            if (nextProps.grandparent === 'onlineDetailLeftPanel' && (toolPanel.asset || toolPanel.assets && toolPanel.assets.length > 0)) {
                return false;
            }
            if (!equal(nextProps.asset, this.props.asset)) {
                return true;
            }

            if (nextProps.rt_animatieTime !== this.props.rt_animatieTime) {
                return true;
            }

            if (
                this.props.asset.meta.type === 'videoE' &&
                (nextProps.previewing !== this.props.previewing || nextProps.audioMuted !== this.props.audioMuted)
            ) {
                return true;
            }

            return false;
        }

        /* 
        1.元素内容是否存在变化
        2.画布scale是否变化
        3.选中元素是否存在变化
        4.文字元素的initFont 是否变化
        */
        if (nextProps.asset.attribute.rt_isDrag) {
            // console.log("Asset time a6: ", new Date().getTime() - start_time);
            return true
        }
        if (nextProps.isHiddenFlag !== this.props.isHiddenFlag
            || nextProps.isAssetActive !== this.props.isAssetActive
            || nextProps.assetEditFlag !== this.props.assetEditFlag
        ) {
            return true;
        }

        if (!equal(nextProps.asset, this.props.asset)) {
            return true;
        }
        if (!equal(nextProps.pageAttr, this.props.pageAttr)) {
            return true;
        }

        if (
            (nextProps.parent === 'canvas' || nextProps.parent === 'editBox') &&
            nextProps.asset.meta.type !== 'group' &&
            nextProps.asset.meta.group &&
            (this.mouseDown === 'empty' || this.mouseDown === 'drag') &&
            !nextProps.assetEditFlag &&
            !this.props.assetEditFlag
        ) {
            if (typeof toolPanel.asset_index === 'number' && toolPanel.asset_index > 0) {
                const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
                if (asset.meta.type === 'group' && nextProps.asset.meta.group === asset.meta.group) {
                    return false;
                }
            } else {
                return true;
            }
        }

        return false;
    }

    componentDidMount(): void {
        const { asset } = this.props;
        if (asset.meta && asset.meta.type === 'text') {
            this.initFontLoader(asset)
        }
    }

    componentWillUnmount(): void {
        this.isUnmount = true;
    }

    /**
     * 初始化加载字体
     */
    initFontLoader(asset: IAsset): void {
        let { smallFontKeys } = this.props;
        const fontNameValueList = getFontNameValueList()

        smallFontKeys = smallFontKeys || JSON.parse(localStorage.local_smallFontKeys);
        let fontFamilyKey = fontNameValueList[asset.attribute.fontFamily]
        fontFamilyKey = fontFamilyKey || fontNameValueList['fnsyhtRegular'];
        // document.getElementsByClassName(className)[0].style.display = 'inline-block'
        if (smallFontKeys && smallFontKeys[asset.attribute.fontFamily]) {
            const _smallKey = fontFamilyKey + '_small';
            this.loadFont(_smallKey, () => {
                !this.isUnmount && this.setState({
                    initFont: 0
                }, () => {
                    this.checkCouldLoadBigFontOrNot(fontFamilyKey);
                })
            })
        } else {
            this.loadFont(fontFamilyKey, () => {
                !this.isUnmount && this.setState({
                    initFont: 0
                })
            })
        }

    }

    loadFont(fontFamilyKey: string, CB: any): void {
        WebFont.load({
            custom: {
                families: [fontFamilyKey]
            },
            timeout: 60000,
            loading: () => undefined,
            active: () => {
                CB && CB();
            },
            inactive: () => {
                CB && CB();
            },
            fontloading: (font, variation) => {
                CB && CB();
            },
            fontactive: (font, variation) => undefined,
            fontinactive: (font, variation) => undefined
        });
    }

    /**
     * 检测大字体是否可以加载
     * @param {*} fontFamilyKey 
     */
    checkCouldLoadBigFontOrNot(fontFamilyKey: string): void {
        const _this = this;
        const checkLoadTimer = setTimeout(() => {
            if (
                window.record_time_info_is_done
            ) {
                _this.loadFont(fontFamilyKey, () => {
                    !_this.isUnmount && _this.setState({
                        loadBigFontEnd: true
                    })
                })
            } else {
                _this.checkCouldLoadBigFontOrNot(fontFamilyKey);
            }
            clearTimeout(checkLoadTimer);
        }, 100);
    }

    /**
     * 改变频繁触发的埋点发送规则
     * @param {*} pageId 
     * @param {*} _this 正确的this对象
     * @param {*} timer 延时多久发送
     * @param {*} otherParams 埋点需要的额外发送参数
     */
    changeFrequentTrigPointRule(pageId = '', _this: IAnyObj, timer = 60 * 1000, otherParams = {}): void {
        if (pageId) {
            if (_this.timerSendPointRecords[pageId]) {
                _this.timerSendPointRecords[pageId]++;
            } else {
                _this.timerSendPointRecords[pageId] = 1;
            }

            if (!_this[`timerSend${pageId}`]) {
                _this[`timerSend${pageId}`] = setTimeout(() => {
                    assetManager.setPv_new(pageId, {
                        additional: {
                            ot: _this.timerSendPointRecords[pageId],
                            ...otherParams
                        }
                    })
                    clearTimeout(_this[`timerSend${pageId}`])
                    _this[`timerSend${pageId}`] = null;
                    _this.timerSendPointRecords[pageId] = 0;
                }, timer);
            }
        }
    }

    selectAssetEvent(e: React.MouseEvent): void {
        const state = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const { canvas, work, pageInfo, rt_currentNav } = state;
        let { toolPanel } = state;

        if (e.ctrlKey || e.shiftKey) {
            const tempIndex = AssetHelper.getIndex(work, pageInfo, { className: this.props.asset.meta.className });
            SelectAsset.selectAssetAdd({
                asset_index: tempIndex
            });
            this.changeFrequentTrigPointRule('2444', this)
            e.stopPropagation();
            e.nativeEvent.stopImmediatePropagation();
            return;
        }

        const oCanvasLayout: HTMLDivElement = document.querySelector('.canvasLayout');
        const oCanvasContent: HTMLDivElement = document.querySelector('.canvasContent');
        const oldSelect: IAsset = toolPanel?.asset

        if (toolPanel.asset && !isPointInRect(e.clientX - oCanvasLayout.offsetLeft - oCanvasContent.offsetLeft, e.clientY - 58 - oCanvasContent.offsetTop, toolPanel.asset, canvas)) {
            SelectAsset.blurAsset();
            CanvasPaintedLogic.updateEditTextFocusStatus(
                {
                    rt_isTextFocus: false,
                    fun_name: 'UPDATE_EDIT_TEXT_FOCUS_STATUS'
                }
            )

        }

        // 同步 store 数据
        toolPanel = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas
        }).toolPanel;
        if (!toolPanel.asset || toolPanel.assets_index.length > 0) {
            if (this.props.asset && this.props.asset.meta && (!this.props.asset.attribute.effectVariant3D || !this.props.asset.attribute.effectVariant3D.resId) && (!this.props.asset.attribute.effectVariant || !this.props.asset.attribute.effectVariant.rt_linearGradient) && this.props.asset.meta.type === "text") {
                emitter.emit('AssetHoverTipsUpdatePos', {
                    top: -1000,
                    left: -1000,
                });
            }

            SelectAsset.selectAsset(
                {
                    asset_index: work.pages[pageInfo.pageNow].assets.indexOf(this.props.asset)
                }
            );


            // if (this.props.asset?.meta?.type !== 'qrcode') { 
            //     emitter.emit('onNavChanged', 'qrcodePanelFn');
            //     emitter.emit('rightLayoutHideClick', '', 'qrcodePanelFn', undefined);
            // }
            // const rightLayout = document.querySelector('.rightLayout') as HTMLDivElement
            // if (this.props.asset?.meta?.type === 'qrcode' && (!oldSelect || !(oldSelect?.meta?.type === 'qrcode')) && (rt_currentNav !== 'qrcodePanelFn' || rightLayout.offsetWidth < 100)) {
            //     emitter.emit('onNavChanged', 'qrcodePanelFn');
            //     emitter.emit('rightLayoutHideClick', '', 'qrcodePanelFn', undefined);
            // } else if (this.props.asset?.meta?.type !== 'qrcode' && rt_currentNav === 'qrcodePanelFn' && rightLayout.offsetWidth > 100) {
            //         // // emitter.emit('onNavChanged', 'qrcodePanelFn');
            //         emitter.emit('rightLayoutHideClick', 'hide');
            // }
            if (e.button === 2) {
                emitter.emit('fontFloatOperation', { status: false });
            } else {
                emitter.emit('fontFloatOperation', { status: true });
            }
        }
        emitter.emit('assetMoveDown', e);
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
    }

    /**
     * 文本元素移动
     * @param e
     */
    dragTextAssetEvent(e: React.MouseEvent): void {
        this.assetStopPropagationEvent(e);
    }

    assetStopPropagationEvent(e: React.MouseEvent): void {
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
    }

    dragAssetEvent(e: React.MouseEvent): void {
        if (e.button !== 2) {
            emitter.emit('fontFloatOperation', { status: true });
        }
        emitter.emit('assetMoveDown', e);
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
    }

    mouseOverAssetEvent(): void {
        this.setState({
            mouseOverAssetStyle: JSON.parse(JSON.stringify(this.mouseOverAssetStyle))
        });
    }

    mouseLeaveAssetEvent(): void {
        this.setState({
            mouseOverAssetStyle: { outline: 'none' }
        });
    }

    render(): JSX.Element {
        const { toolPanel, undo, rt_current_is_moving_asset, rt_current_is_drag_asset, rt_hoverPreview, rt_previewAssetAnimation } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }); // canvasStore.getState().onCanvasPainted,
        const { canvas, canvasWaterMaskFlag, pageAttr } = this.props;
        let { smallFontKeys } = this.props,
            { mouseOverAssetStyle } = this.state,
            assetLabel,
            assetMouseDownEventType,
            asset_extra_class = "";
        const { asset, assetEditFlag, assetProps = {}, pageInfo } = this.props,

            { initFont, loadBigFontEnd } = this.state,
            assetType = asset.meta.type,
            assetStyle: IAnyObj = {},
            assetAttr = asset.attribute,
            assetTransform = asset.transform;

        if ('undefined' !== typeof (asset.adsorptionFlag) && asset.adsorptionFlag == 1) {
            mouseOverAssetStyle = this.mouseOverAssetStyle
        }

        Object.assign(assetStyle, {
            cursor: 'move'
        })
        Object.assign(assetStyle, {
            zIndex: asset.meta.index * 10
        });

        let newAssetProps: IAnyObj = {};
        if (this.props.assetProps) {
            newAssetProps = JSON.parse(JSON.stringify(this.props.assetProps));
        }

        // asset className计算
        let assetClassName = asset.meta.className;
        if (this.props.assetProps && this.props.assetProps.showOnly) {
            assetClassName += "_showOnly" + "_" + this.props.assetProps.currentPageNum + "_" + (this.props.assetProps.classTag || 'common');
            newAssetProps.assetClassName = assetClassName;
        }
        const pageIndex = newAssetProps?.currentPageNum ?? pageInfo?.pageNow

        const _canvasScale = assetProps.canvasScale || canvas?.scale || 1;

        if (assetType === 'text') {
            const fontNameValueList = getFontNameValueList(),
                tempWidth = assetAttr.width > 0 ? assetAttr.width : 0;
            const newfontFamily = fontNameValueList[assetAttr.fontFamily];
            assetLabel = <TextEditor
                // assetLabel = <RichText
                asset={asset}
                // key={undo + '_' + asset.attribute.effect+ '_' + (asset.attribute.effectVariant ? asset.attribute.effectVariant.state : '') + '_' + asset.meta.className } 
                index={this.props.index}
                key={undo + '_' + (asset.attribute.effect ? asset.attribute.effect.split('@')[0] : '') + '_' + '_' + asset.meta.className + "_" + initFont}
                assetEditFlag={assetEditFlag}
                initFont={initFont}
                isAssetActive={this.props.isAssetActive}
                assetProps={newAssetProps}
                canvas={canvas}
                pageIndex={pageIndex}
                isShowOnly={this.props.isShowOnly}
                isHiddenFlag={this.props.isHiddenFlag}
                isMoving={rt_current_is_moving_asset}
                ref={this.props.editRef}
            />

            let fontFamilyKey = '';

            smallFontKeys = smallFontKeys || (localStorage.local_smallFontKeys && JSON.parse(localStorage.local_smallFontKeys));

            if (!assetProps.isUnInit && smallFontKeys && smallFontKeys[assetAttr.fontFamily] && !loadBigFontEnd) {//有小字体 并且 未加载完成大字体
                fontFamilyKey = newfontFamily + '_small' + ',font130';
            } else {
                fontFamilyKey = newfontFamily + ',font130'
            }
            Object.assign(assetStyle, {
                cursor: 'text',
                // fontSize: assetAttr.fontSize + 'px',
                width: tempWidth * _canvasScale + 'px',
                textAlign: assetAttr.textAlign,
                textAlignLast: assetAttr.textAlign === 'disperse' ? 'justify' : '',
                fontStyle: assetAttr.fontStyle,
                textDecoration: assetAttr.textDecoration,
                // fontWeight: assetAttr.fontWeight,
                lineHeight: assetAttr.lineHeight / 10,
                letterSpacing: assetAttr.letterSpacing,
                fontFamily: fontFamilyKey,//newfontFamily+','+
                color: 'rgba(' + assetAttr.color.r + ',' + assetAttr.color.g + ',' + assetAttr.color.b + ',' + assetAttr.color.a + ')',
                wordBreak: 'break-all',
                writingMode: assetAttr.writingMode ? assetAttr.writingMode : "horizontal-tb",
            });
            if (asset.meta.v === 3) {
                // 不设置字体大小
                // assetStyle.fontSize = assetAttr.fontSize + 'px';
            } else {
                assetStyle.fontSize = assetAttr.fontSize + 'px';
            }

            if (assetAttr.fontWeight === "bold") {
                if (asset.attribute.effectVariant && asset.attribute.effectVariant.rt_name && asset.attribute.effectVariant.layers.length > 0) {
                    Object.assign(assetStyle, {
                        fontWeight: "bold"
                    });
                } else {
                    Object.assign(assetStyle, {
                        WebkitTextStroke: "1px " + 'rgba(' + assetAttr.color.r + ',' + assetAttr.color.g + ',' + assetAttr.color.b + ',' + assetAttr.color.a + ')'
                    });
                }
            }
            if (assetAttr.writingMode === "vertical-rl" && !(assetAttr.effectVariant3D && assetAttr.effectVariant3D.resId)) {
                Object.assign(assetStyle, {
                    width: "auto",
                    height: assetAttr.height * _canvasScale + 'px',
                });
            }
            if (initFont) {
                Object.assign({
                    fontFamily: '12px/1.5 Microsoft YaHei,tahoma,arial,Hiragino Sans GB'
                })
            }
            if (!assetEditFlag) {
                Object.assign(assetStyle, {
                    cursor: 'move'
                })
            }
            // 立体特效字dom渲染比canvas渲染偏细，暂时先加粗一下
            if(assetAttr.effect && assetAttr.effect == '25@0'){
                Object.assign(assetStyle, {
                    WebkitTextStroke: "1.6px " + 'transparent'
                });
            }
            // // 3D特效字样式修改 START
            // if(effectVariant3D.resId){
            //     Object.assign(assetStyle, {
            //         width: "auto",
            //     });
            // }
            // // 3D特效字样式修改 END

        } else if (assetType === 'image' || assetType === 'background' || assetType === 'pic') {
            assetLabel = <ImageContainer
                key={'imageContainer_' + asset.meta.className}
                asset={asset}
                index={this.props.index}
                assetProps={{ ...assetProps, assetClassName: assetClassName }}
                isMoving={rt_current_is_moving_asset}
                canvas={canvas}
                pageInfo={pageInfo}
                canvasWaterMaskFlag={canvasWaterMaskFlag}
            />
            const tempContainer = asset.attribute.container ? asset.attribute.container : {};
            Object.assign(assetStyle, {
                width: (tempContainer.id ? tempContainer.width : assetAttr.width) * _canvasScale + 'px',
                height: (tempContainer.id ? tempContainer.height : assetAttr.height) * _canvasScale + 'px',
            });

            if (toolPanel.asset == asset && toolPanel.asset.attribute.container && toolPanel.asset.attribute.container.isEdit == true) {
                Object.assign(assetStyle, {
                    zIndex: 9999999
                })
            }
        } else if (assetType === 'SVG' || assetType === 'flow') {
            assetLabel = <SVG asset={asset} assetProps={newAssetProps} index={this.props.index} isMoving={rt_current_is_moving_asset} />
            Object.assign(assetStyle, {
                width: assetAttr.width * _canvasScale + 'px',
                height: assetAttr.height * _canvasScale + 'px',
            })
        } else if (assetType === 'group') {
            assetLabel = <Grounp asset={asset} assetProps={newAssetProps} index={this.props.index} isMoving={rt_current_is_moving_asset} />
            Object.assign(assetStyle, {
                // width: previewScale ? assetAttr.width * previewScale : assetAttr.width * canvas.scale + 'px',
                width: assetAttr.width * _canvasScale + 'px',
                // height: previewScale ? assetAttr.height * previewScale : assetAttr.height * canvas.scale + 'px',
                height: assetAttr.height * _canvasScale + 'px',
            })
        } else if (assetType === 'container') {
            assetLabel = <Container asset={asset} assetProps={newAssetProps} index={this.props.index} isMoving={rt_current_is_moving_asset} />
            Object.assign(assetStyle, {
                width: assetAttr.width * _canvasScale + 'px',
                height: assetAttr.height * _canvasScale + 'px',
            })
        } else if (assetType === 'table') {
            assetLabel = <AssetTable2 asset={asset} assetProps={newAssetProps} index={this.props.index} canvas={canvas} isShowOnly={this.props.isShowOnly} isMoving={rt_current_is_moving_asset} isActive={toolPanel.asset === asset} hideDragTable={this.props.hideDragTable} />
            // assetLabel = <AssetTable asset={asset} assetProps={newAssetProps} index={this.props.index} canvas = {canvas} isShowOnly = {this.props.isShowOnly} isMoving={rt_current_is_moving_asset}/>
            Object.assign(assetStyle, {
                width: assetAttr.width * _canvasScale + 'px',
                height: assetAttr.height * _canvasScale + 'px'
            })
            if (assetEditFlag) {
                assetStyle.zIndex = 99
            }
        }else if( assetType === 'chart' ){
            if(asset.meta.v === 2) {
                const width = assetAttr.width * _canvasScale, height = assetAttr.height * _canvasScale;
                const pageIndex = newAssetProps?.currentPageNum ?? pageInfo?.pageNow;      
                
                assetLabel = <AssetEChart asset={asset} index={this.props.index} canvas={canvas} isShowOnly={this.props.isShowOnly} assetProps={{ canvasScale: _canvasScale, width, height, currentPageNum: pageIndex }} />
                Object.assign(assetStyle, {
                    width: assetAttr.width * _canvasScale + 'px',
                    height: assetAttr.height * _canvasScale + 'px',
                })
            } else {
                assetLabel = <AssetChart asset={asset} assetProps={newAssetProps} index={this.props.index} canvas = {canvas} isShowOnly = {this.props.isShowOnly} isMoving={rt_current_is_moving_asset}/>
                Object.assign(assetStyle, {
                    width: assetAttr.width * _canvasScale + 'px',
                    height: assetAttr.height * _canvasScale + 'px',
                })
            }
        }
        else if (assetType == 'videoE') {
            assetLabel = <AssetVideoE
                asset={asset}
                assetProps={assetProps}
                scale={_canvasScale}
                rt_animatieTime={this.props.rt_animatieTime}
                previewing={this.props.previewing}
                audioMuted={this.props.audioMuted}
                autoPlay={this.props.autoPlay}
            />;
            Object.assign(assetStyle, {
                width: assetAttr.width * _canvasScale + 'px',
                height: assetAttr.height * _canvasScale + 'px',
            })
            if (toolPanel.asset == asset && toolPanel.asset.attribute.container && toolPanel.asset.attribute.container.isEdit == true) {
                Object.assign(assetStyle, {
                    zIndex: 9999999
                })
            }
        } else if (assetType == 'qrcode') {
            assetLabel = <AssetQrcode
                asset={asset}
                assetProps={assetProps}
                canvas={canvas}
                isShowOnly={this.props.isShowOnly}
                options={asset.attribute.qrcodeInfo}
            />;
            Object.assign(assetStyle, {
                width: assetAttr.width * _canvasScale + 'px',
                height: assetAttr.height * _canvasScale + 'px',
            })
            if (toolPanel.asset == asset && toolPanel.asset.attribute.container && toolPanel.asset.attribute.container.isEdit == true) {
                Object.assign(assetStyle, {
                    zIndex: 9999999
                })
            }
        }else if (assetType == 'frame') {
            Object.assign(assetStyle, {
                width: assetAttr.width * _canvasScale + 'px',
                height: assetAttr.height * _canvasScale + 'px',
            })
            assetLabel = <AssetFrame
                asset={asset}
                assetProps={assetProps}
                canvas={canvas}
                assetIndex={this.props.index}
                isShowOnly={this.props.isShowOnly}
            />;
        }
        // 富文本不设置外层透明度
        if(asset.meta?.v != 3){
            Object.assign(assetStyle, {
                opacity: (assetAttr.opacity >= 0 ? assetAttr.opacity : 100) / 100,
            })
        }
  

        Object.assign(assetStyle, {
            position: 'absolute',
            // top: previewScale ? assetTransform.posY * previewScale : assetTransform.posY * canvas.scale,
            top: assetTransform.posY * _canvasScale,
            // left: previewScale ? assetTransform.posX* previewScale : assetTransform.posX * canvas.scale,
            left: assetTransform.posX * _canvasScale,

        })

        if (assetType === 'text') {
            Object.assign(assetStyle, {
                top: assetTransform.posY * _canvasScale,

            })

        }

        Object.assign(assetStyle, {
            transform: 'rotate(' + assetTransform.rotate + 'deg)',
        })

        if (assetEditFlag || (toolPanel.asset && toolPanel.asset.meta && toolPanel.asset.meta.group && asset.meta.group === toolPanel.asset.meta.group && (toolPanel.asset.groupTextEditAsset === asset || !toolPanel.asset.groupTextEditAsset))) {
            if (this.ed_dragAssetEvent) {
                assetMouseDownEventType = this.ed_dragAssetEvent;
                this.mouseDown = 'drag';
            } else {
                assetMouseDownEventType = defaultFunction;
                this.mouseDown = 'empty';
            }
            asset_extra_class = "isActive";

            if( assetType === 'text' ) {
                if (this.ed_dragTextAssetEvent) {
                    assetMouseDownEventType = this.ed_dragTextAssetEvent;
                    this.mouseDown = 'drag';
                } else {
                    assetMouseDownEventType = defaultFunction;
                    this.mouseDown = 'empty';
                }

                if (mouseOverAssetStyle.border !== 'none' && assetStyle.width === 'auto') {
                    // assetStyle.top -= 3;
                    // assetStyle.left -= 9;
                } else if (mouseOverAssetStyle.border !== 'none' && assetStyle.width !== 'auto') {
                    // assetStyle.top -= 3;
                    // assetStyle.left -= 3;
                }
            } else {
                if (mouseOverAssetStyle.border !== 'none' && assetStyle.width !== 'auto') {
                    // assetStyle.top -= 3;
                    // assetStyle.left -= 3;
                }
            }

            Object.assign(assetStyle, mouseOverAssetStyle)
        } else {
            if (assetType === 'text' && assetStyle.width === 'auto') {

                if (mouseOverAssetStyle.border !== 'none') {
                    // assetStyle.top -= 3;
                    // assetStyle.left -= 9;
                }
            } else if (mouseOverAssetStyle.border !== 'none') {
                // assetStyle.top -= 3;
                // assetStyle.left -= 3;
            }
            Object.assign(assetStyle, mouseOverAssetStyle)

            // if( asset.meta.locked ){
            //     assetClickEventType = this.selectAssetEvent
            // }else {
            //     assetMouseDownEventType = this.selectAssetEvent
            // }
            if (this.ed_selectAssetEvent) {
                assetMouseDownEventType = this.ed_selectAssetEvent;
                this.mouseDown = 'select';
            } else {
                assetMouseDownEventType = defaultFunction;
                this.mouseDown = 'empty';
            }
        }

        if (toolPanel.assets.indexOf(asset) >= 0) {
            Object.assign(assetStyle, this.mouseOverAssetStyle)
            // assetStyle.top -= 1;
            // assetStyle.left -= 1;
        }

        if (this.props.isHiddenFlag) {
            Object.assign(assetStyle, {
                pointerEvents: "none"
            });
        }
        let isBoxEffect = false;
        const borderStyle = {};
        if (assetAttr?.boxEffect?.length) {
            const normalStyle = assetAttr.boxEffect.find((item: any)=> item?.type === 'normal');
            if (normalStyle && normalStyle.border && normalStyle.border.type) {
                const { width = 1, type, color, radius = 0 } = normalStyle.border;
                isBoxEffect = true;
                Object.assign(borderStyle, {
                    width: `100%`,
                    height: `100%`,
                    position: 'absolute',
                    top: -width,
                    left: -width,
                    border: `${width}px ${type} rgba(${color?.r},${color?.g},${color?.b},${color?.a})`,
                    borderRadius: `${radius}px`,
                })
            }
        }

        // const isNotPreview = !this.props.assetProps || !this.props.assetProps.showOnly
        const isNotPreview = (this.props.assetProps?.showOnly && this.props.assetProps.classTag === 'preview_1') || (!this.props.assetProps || !this.props.assetProps.showOnly)
        const isAssetAnimation = (asset.attribute?.k?.i?.resId || asset.attribute?.k?.rt_previewAnimaton) ? true : false
        return (
            <div
                className={"asset " + assetClassName + (this.props.isHiddenFlag ? "_hidden" : "") + " " + asset_extra_class + (asset_extra_class ? " " : "") + ((rt_current_is_drag_asset || rt_current_is_moving_asset) ? 'assetHoverNone' : 'assetHoverVisible')}
                key={assetClassName + '_' + (asset.attribute.container ? asset.attribute.container.id : '')}
                style={assetStyle}
                onMouseDown={assetMouseDownEventType}
                onMouseEnter={this.ed_mouseOverAssetEvent ? this.ed_mouseOverAssetEvent : defaultFunction}
                onMouseLeave={this.ed_mouseLeaveAssetEvent ? this.ed_mouseLeaveAssetEvent : defaultFunction}>
                {isNotPreview && !isAssetAnimation && 'number' === typeof this.props.index ? (
                    <PageAnimationContainer
                        index={this.props.index}
                        assetStyle={assetStyle}
                        asset={asset}
                        scale={_canvasScale}
                        pageIndex={pageIndex}
                        pageAttr={pageAttr}
                        pageInfo={pageInfo}
                        toolPanel={toolPanel}
                        previewOrCanvas={this.props.assetProps?.classTag}
                        canvas={canvas}
                        rt_animatieTime={this.props.rt_animatieTime}
                        rt_previewFrame={this.props.rt_previewFrame}
                        rt_hoverPreview={rt_hoverPreview}
                    >
                        {!isSafari ? (
                            <AssetEffect
                                asset={asset}
                                scale={_canvasScale}
                                pageAttr={pageAttr}
                                pageInfo={pageInfo}
                                canvas={canvas}
                                previewOrCanvas={this.props.assetProps?.classTag}
                                rt_animatieTime={this.props.rt_animatieTime}
                                timeLine={this.props.timeLine}
                            >
                                {assetLabel}
                            </AssetEffect>
                        ) : (
                            assetLabel
                        )}

                    </PageAnimationContainer>
                ) : isNotPreview && isAssetAnimation && 'number' === typeof this.props.index ?
                    (<AssetAnimationContainer
                        index={this.props.index}
                        assetStyle={assetStyle}
                        asset={asset}
                        scale={_canvasScale}
                        pageIndex={pageIndex}
                        pageAttr={pageAttr}
                        pageInfo={pageInfo}
                        toolPanel={toolPanel}
                        previewOrCanvas={this.props.assetProps?.classTag}
                        canvas={canvas}
                        rt_animatieTime={this.props.rt_animatieTime}
                        rt_previewFrame={this.props.rt_previewFrame}
                        rt_previewAssetAnimation={rt_previewAssetAnimation}
                    >
                        {!isSafari ? (
                            <AssetEffect
                                asset={asset}
                                scale={_canvasScale}
                                pageAttr={pageAttr}
                                pageInfo={pageInfo}
                                canvas={canvas}
                                previewOrCanvas={this.props.assetProps?.classTag}
                                rt_animatieTime={this.props.rt_animatieTime}
                                timeLine={this.props.timeLine}
                            >
                                {assetLabel}
                            </AssetEffect>
                        ) : (
                            assetLabel
                        )}
                    </AssetAnimationContainer>)
                    : (
                        assetLabel
                    )}
                    {isBoxEffect && <div className="assetBorder" style={borderStyle}></div>}
            </div>
        );
    }
}

export { Asset };
