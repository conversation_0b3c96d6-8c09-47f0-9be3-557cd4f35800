import React, { Component } from 'react';
import SliderItem from '@src/userComponentV6.0/Slider/SliderItem';
import addEventListener from 'rc-util/lib/Dom/addEventListener';
import { canvasStore } from '@redux/CanvasStore';
import { MoreColorsPanel } from '@src/userComponentV6.0/MoreColorsPanel/MoreColorsPanel';
import MoreColorsPreinstall from '@src/userComponentV6.0/MoreColorsPanel/MoreColorsPreinstall';
import { matchesSelector } from '@src/userComponentV6.0/Function';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { emitter } from '@src/userComponentV6.0/Emitter';
import { AssetLogic, UpdateAsset } from '@v7_logic/AssetLogic';
import { isSameColor } from '@src/userComponentV6.0/MoreColorsPanel/components';
import { hexToRgb } from '@v7_utils/color';
import { klona as cloneDeep } from 'klona';
import './scss/index.scss';
import { assetManager } from '@src/userComponentV6.0/AssetManager';

interface IState {
    colorsShow: boolean | string;
    isFlipMoreList: boolean;
    isColorMorePanel: boolean;
    isStrokeColorMorePanel: boolean;
}
export class FlowEditBlock extends Component<any, IState> {
    clientX: number;
    clientY: number;
    sliderType: string;
    opacity: number;
    opacityMin: number;
    opacityWidth: number;
    opacityMax: number;
    strokeColorSelect = React.createRef<HTMLDivElement>();
    color = React.createRef<HTMLDivElement>();
    floatFuncListener: any;
    sliderDownFontOpacityEvent: any;
    sliderMoveListtener: any;
    sliderUpListtener: any;
    choiceColorClickListtener: any;
    colorsItemClickListtener: any;
    colorShowClickListtener: any;
    typeBtnMoreClickListtener: any;
    updateInitSvgColorListener: any;
    constructor(props) {
        super(props);

        this.state = {
            colorsShow: '',
            isFlipMoreList: false,
            isColorMorePanel: false,
            isStrokeColorMorePanel: false,
        };

        this.sliderDownFontOpacityEvent = this.sliderDownEvent.bind(this, 'opacity');
        this.fontOpacityChangeEvent = this.fontOpacityChangeEvent.bind(this);
        this.horizontalFlipEvent = this.horizontalFlipEvent.bind(this);
        this.verticalFlipEvent = this.verticalFlipEvent.bind(this);
        this.degrees90Left = this.degrees90Left.bind(this);
        this.degrees90Right = this.degrees90Right.bind(this);

        this.opacityMin = 0;
        this.opacityMax = 100;
        this.opacityWidth = 111;
    }

    componentDidMount() {
        this.floatFuncListener = emitter.addListener('openFuncFloat', (type, item) => {
            if (type === 'svg-color') {
                this.setState({
                    isColorMorePanel: !this.state.isColorMorePanel,
                });
            }
        });
        // this.updateInitSvgColorListener = emitter.addListener('updateInitSvgColor', (color) => {
        //     if (color) {
        //         this.setState({})
        //     }
        // });

    }

    componentWillUnmount() {
        this.floatFuncListener?.remove();
        // this.updateInitSvgColorListener?.remove();
    }

    /**
     * 滑动条点击事件
     */
    sliderDownEvent(sliderType, e) {
        const toolPanel = canvasStore.getState().onCanvasPainted.toolPanel;

        this.opacity = toolPanel.asset.attribute.opacity;

        this.clientX = e.clientX;
        this.clientY = e.clientY;
        this.sliderType = sliderType;
        const th = this;
        this.sliderMoveListtener = addEventListener(window, 'mousemove', (e) => {
            th.sliderMoveEvent(e);
        });
        this.sliderUpListtener = addEventListener(window, 'mouseup', (e) => {
            th.sliderUpEvent(e);
        });
    }

    /**
     * 滑动条释放事件
     */
    sliderUpEvent(e) {
        this.sliderMoveListtener.remove();
        this.sliderUpListtener.remove();
    }

    /**
     * 滑动条移动事件
     */
    sliderMoveEvent(e) {
        const props = {};

        switch (this.sliderType) {
            case 'opacity':
                const opacity = this.opacityFormat(
                    this.opacity + ((e.clientX - this.clientX) * this.opacityMax) / this.opacityWidth,
                );

                Object.assign(props, { opacity: opacity });
                const { toolPanel } = storeAdapter.getStore({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                });
                AssetLogic.updateAssetImage({
                    assetIndex: toolPanel.asset_index,
                    // className:toolPanel.asset.meta.className,
                    key: 'opacity',
                    attribute: 'attribute',
                    value: this.opacityMin + ((this.opacityMax - this.opacityMin) * opacity) / this.opacityMax,
                    fun_name: 'UPDATE_OPACITY',
                });
                break;
        }

        this.setState(Object.assign({}, props));
    }

    /**
     * 透明度（滑动范围设置）
     */
    opacityFormat(opacity) {
        opacity = opacity > this.opacityMin ? opacity : this.opacityMin;
        opacity = opacity > this.opacityMax ? this.opacityMax : opacity;

        return opacity;
    }

    /**
     * 文字透明度（输入框）
     */
    fontOpacityChangeEvent(e) {
        const opacity = parseInt(e.target.value) > this.opacityMax ? this.opacityMax : parseInt(e.target.value);

        const { toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        AssetLogic.updateAssetImage({
            assetIndex: toolPanel.asset_index,
            key: 'opacity',
            attribute: 'attribute',
            value: opacity,
            fun_name: 'UPDATE_OPACITY',
        });
    }

    /**
     * 水平翻转
     */
    horizontalFlipEvent(e) {
        const { toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        AssetLogic.updateAssetImage({
            assetIndex: toolPanel.asset_index,
            attribute: 'transform',
            key: 'horizontalFlip',
            value: !toolPanel.asset.transform.horizontalFlip as unknown as number,
            fun_name: 'HORIZONTAL_FLIP',
        });
        // canvasStore.dispatch(paintOnCanvas('HORIZONTAL_FLIP'))
    }

    /**
     * 垂直翻转
     */
    verticalFlipEvent(e) {
        const { toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        AssetLogic.updateAssetImage({
            assetIndex: toolPanel.asset_index,
            attribute: 'transform',
            key: 'verticalFlip',
            value: !toolPanel.asset.transform.verticalFlip as unknown as number,
            fun_name: 'VERTICAL_FLIP',
        });
    }

    /**
     * 向左旋转90度
     */
    degrees90Left(e) {
        AssetLogic.degrees90Left({});
    }

    /**
     * 向右旋转90度
     */
    degrees90Right(e) {
        AssetLogic.degrees90Right({});
    }

    /**
     * 设置颜色// 取色按钮
     */
    colorEvent1(kColor, color, type) {
        this.colorEvent(kColor, color, type);
    }
    /**
     * 设置颜色// 取色板
     */
    colorEvent2(kColor, color, type) {
        this.colorEvent(kColor, color, type);
    }
    /**
     * 设置颜色
     */
    colorEvent(kColor, color, type) {
        const tempColor = {
            r: color.color.r,
            g: color.color.g,
            b: color.color.b,
            a: color.color.a,
        };
        if (type == 'click') {
            AssetLogic.updateAssetSvgColorsInfo({
                color: tempColor,
                kColor: kColor,
            });
        } else {
            AssetLogic.updateAssetSvgColorsInfo({
                color: tempColor,
                kColor: kColor,
            });
        }
        this.setState({});
        // this.forceUpdate();
    }

    updateStrokeColor(color) {
        assetManager.setPv_new(8770)
        const tempColor = {
            r: color.color.r,
            g: color.color.g,
            b: color.color.b,
            a: color.color.a,
        };
        AssetLogic.updateAssetSvgStrokeColor({
            color: tempColor,
        });
        this.setState({});
    }

    /**
     * 替换全部颜色
     */
    replaceAllSVGColorEvent(assets, lastColor, color) {
        // 记录操作，方便undo
        storeAdapter.dispatch({
            store_name: storeAdapter.store_names.paintOnCanvas,
            fun_name: 'UPDATE_SVG_COLORS_INFO',
            params: [
                {
                    type: 'UPDATE_SVG_COLORS_INFO',
                    params: {},
                },
            ],
        });
        const targetsAssets = [];
        for (const asset of assets) {
            const colors = cloneDeep(asset.attribute.colors);
            // 与lastColor相同的colors的key才是kColor
            const kColor = Object.keys(colors).find((key) => {
                if (typeof colors[key] === 'string') {
                    return isSameColor(lastColor, hexToRgb(key));
                } else {
                    return isSameColor(lastColor, colors[key]);
                }
            });

            if (!kColor) continue;

            Object.assign(colors, {
                [kColor]: color,
            });

            targetsAssets.push({
                index: asset.meta.rt_page_assets_index,
                className: asset.meta.className,
                changes: {
                    attribute: {
                        colors,
                    },
                },
            });
        }
        // 批量更新
        UpdateAsset.updateAssets('UPDATE_SVG_COLORS_INFO', targetsAssets, void 0, void 0, true);
        this.setState({});
    }

    // 检查其他相同颜色的元素
    checkLastColorInCavans(color) {
        if (!color) return [];

        const { work } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const res = [];

        // 遍历所有page，寻找指定color的SVG对象，包括在组中的，并返回
        for (let index = 0; index < work.pages.length; index++) {
            const assetsSvg = work.pages[index].assets.filter(
                (item) => item.meta.type === 'SVG' && this.isEqualColor(item, color),
            );
            res.push(...assetsSvg);
        }

        return res;
    }

    // 判断asset中是含指定color
    isEqualColor(asset, color) {
        const colorsArr = [];
        for (const item in asset.attribute.colors) {
            colorsArr.push([item, asset.attribute.colors[item]]);
        }
        for (let index = 0; index < colorsArr.length; index++) {
            const item = colorsArr[index];
            if (index > 0) {
                return;
            }
            let moreColorsPanelColor = item[0];
            if (item[1] != '') {
                moreColorsPanelColor = item[1];
            }
            return isSameColor(
                color,
                typeof moreColorsPanelColor === 'string' ? hexToRgb(moreColorsPanelColor) : moreColorsPanelColor,
            );
        }
    }

    choiceColorClickEvent(type, e) {
        if (this.choiceColorClickListtener) {
            this.choiceColorClickListtener.remove();
        }
        if (type == 'stroke') {
            this.setState({
                isStrokeColorMorePanel: !this.state.isStrokeColorMorePanel,
            });
        } else {
            assetManager.setPv_new(8771)
            this.setState({
                isColorMorePanel: !this.state.isColorMorePanel,
            });
        }
        const th = this;
        this.choiceColorClickListtener = addEventListener(window, 'click', (e) => {
            if (matchesSelector(e.target, '.FlowEditBlock .choiceColor')) {
                return;
            }
            th.setState({
                isColorMorePanel: false,
                colorsShow: false,
                isStrokeColorMorePanel: false,
            });
            th.choiceColorClickListtener.remove();
        });

    }

    /**
     * 颜色防止冒泡
     */
    colorClickEvent(e) {
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
    }

    colorBlockItemClickEvent(color, kColor, e) {
        AssetLogic.updateAssetSvgColorsInfo({
            color: color.rgb,
            kColor: kColor,
        });
        // canvasStore.dispatch(paintOnCanvas('UPDATE_SVG_COLORS_INFO', {color: color.rgb, kColor: kColor}));

        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    /**
     * SVG色块（点击事件）
     * @param kColor
     */
    colorsItemClick(kColor, e) {
        this.setState({
            colorsShow: kColor,
        });

        if (this.colorsItemClickListtener) {
            this.colorsItemClickListtener.remove();
        }
        const th = this;
        this.colorsItemClickListtener = addEventListener(window, 'click', function (e) {
            th.colorsItemClickListtener.remove();

            th.setState({
                colorsShow: '',
            });
        });

        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
    }

    /**
     * 颜色弹窗
     */
    colorShowEvent(kColor, e) {
        const th = this;
        this.setState({
            colorsShow: kColor,
        });

        if (this.colorShowClickListtener) {
            this.colorShowClickListtener.remove();
        }
        this.colorShowClickListtener = addEventListener(window, 'click', function (e) {

            th.colorShowClickListtener.remove();

            const toolPanel = canvasStore.getState().onCanvasPainted.toolPanel;

            if (toolPanel.asset == undefined || toolPanel.asset == '') {
                return false;
            }

            th.setState({
                colorsShow: false,
                isColorMorePanel: false,
            });
            // th.colorShowFlag = 2
        });
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    typeBtnMoreClickEvent(e) {
        this.setState({
            isFlipMoreList: true,
        });

        if (this.typeBtnMoreClickListtener) {
            this.typeBtnMoreClickListtener.remove();
        }
        const th = this;
        this.typeBtnMoreClickListtener = addEventListener(window, 'click', function (e) {
            th.setState({
                isFlipMoreList: false,
            });
            th.typeBtnMoreClickListtener.remove();
        });

        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    stopPropagation(e) {
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    strokeWidthChangeEvent(value) {
        assetManager.setPv_new(8769);
        AssetLogic.updateAssetSvgStrokeInfo({
            width: value,
        });
    }

    render() {
        const { toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }); 
        const { colorsShow, isColorMorePanel, isStrokeColorMorePanel } = this.state;

        const colorsArr = [];
        for (const item in toolPanel.asset.attribute.colors) {
            colorsArr.push([item, toolPanel.asset.attribute.colors[item]]);
        }

        let strokeColor = { r: 0, g: 0, b: 0, a: 0 };
        let strokeWidth = 0;
        if (toolPanel.asset.attribute.stroke?.color) {
            strokeColor = toolPanel.asset.attribute.stroke.color;
            strokeWidth = toolPanel.asset.attribute.stroke.width;
        }
        const strokeColorStr = `rgba(${strokeColor.r}, ${strokeColor.g}, ${strokeColor.b}, ${strokeColor.a})`;
        return (
            <div className="FlowEditBlock">
                <div className="line"></div>
                <div className="svgTitle">形状</div>
                <div className="item">
                    <p className="itemTitle">颜色</p>
                    {colorsArr.map((item, index) => {
                        if (index > 0) {
                            return;
                        }
                        let color = item[0],
                            moreColorsPanelColor = color;
                        if (item[1] != '') {
                            color = 'rgba(' + item[1].r + ',' + item[1].g + ',' + item[1].b + ',' + item[1].a + ')';
                            moreColorsPanelColor = item[1];
                        }
                        return (
                            <div key={'colorsArr_' + index}>
                                <div className="commonSelect" onClick={this.choiceColorClickEvent.bind(this, 'fill')}>
                                    {item[1]?.a === 0 ? (<div className="choiceColor transparent"></div>) : (<div className="choiceColor" style={{ background: color }}></div>)}
                                    <MoreColorsPreinstall
                                        dropdownStyle={{ top: '43px', right: '-1px' }}
                                        btnStyle={{ position: 'relative', float: 'right' }}
                                        onCallback={this.colorEvent1.bind(this, item[0])}
                                        closeColorSelect={() => this.setState({ isColorMorePanel: false })}
                                    />
                                    <div className="colorPanel">
                                        {isColorMorePanel && (
                                            <MoreColorsPanel
                                                color={moreColorsPanelColor}
                                                onChange={this.colorEvent2.bind(this, item[0])}
                                                style={{
                                                    left: '-10px',
                                                    top: '-20px',
                                                    zIndex: 20,
                                                }}
                                                onClose={() => this.setState({ isColorMorePanel: false })}
                                                couldReplaceAllColor={true}
                                                onReplaceColorFunc={this.replaceAllSVGColorEvent.bind(this)}
                                                checkLastColorInCavans={this.checkLastColorInCavans.bind(this)}
                                                setPvReplaceAllColorHover={8321}
                                                setPvReplaceAllColorClick={8322}
                                            />
                                        )}
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>
                <div className="line"></div>
                <div className="svgTitle">描边</div>
                <div className="item ">
                    <p className="itemTitle">颜色</p>
                    <div className="commonSelect" onClick={this.choiceColorClickEvent.bind(this, 'stroke')}>
                    {strokeColor?.a === 0 ? (<div className="choiceColor transparent"></div>) : (<div className="choiceColor" style={{ background: strokeColorStr }}></div>)}
                        <MoreColorsPreinstall
                            dropdownStyle={{ top: '43px', right: '-1px' }}
                            btnStyle={{ position: 'relative', float: 'right' }}
                            onCallback={this.updateStrokeColor.bind(this)}
                            closeColorSelect={() => this.setState({ isStrokeColorMorePanel: false })}
                        />
                        <div className="strokeColorPanel" ref={this.strokeColorSelect}>
                            {isStrokeColorMorePanel && (
                                <MoreColorsPanel
                                    color={strokeColor}
                                    onChange={this.updateStrokeColor.bind(this)}
                                    style={{
                                        left: '-10px',
                                        top: '-20px',
                                        zIndex: 20,
                                    }}
                                    onClose={() => this.setState({ isStrokeColorMorePanel: false })}
                                    couldReplaceAllColor={true}
                                    onReplaceColorFunc={this.replaceAllSVGColorEvent.bind(this)}
                                    checkLastColorInCavans={this.checkLastColorInCavans.bind(this)}
                                    setPvReplaceAllColorHover={8321}
                                    setPvReplaceAllColorClick={8322}
                                />
                            )}
                        </div>
                    </div>
                </div>
                <div className="item">
                    <p className="itemTitle">粗细</p>
                    <SliderItem
                        value={strokeWidth}
                        range={[0, 100]}
                        sliderWidth={120}
                        setPv={{
                            onDragStart: ()=>{AssetLogic.saveAssetSvgStroke()},
                        }}
                        onMouseDown={(e) => {AssetLogic.saveAssetSvgStroke()}}
                        onChange={(value, isStop, unrecordable) => this.strokeWidthChangeEvent(value)}
                        showInput={true}
                        height={42}
                        adjust
                    />
                </div>
                <div className="line"></div>
            </div>
        );
    }
}
