import React, { PureComponent } from 'react';
import { IPageAnimation, IPageAttr, IPageInfo } from '@v7_logic/Interface';
import { storeDecorator } from '@v7_logic/StoreHOC';
import { ETool } from '@v7_logic/Enum';
import { PageAnimation } from '@v7_render/Animation';


interface IPageAnimationProps {
}

interface IPageAnimationState {
}




export function DisplayPageAnimationTool(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.ANIMATION_EFFECT, nav: ETool.ANIMATION_EFFECT};
}

@storeDecorator((state: { [key: string]: any }) => {
    return {
        pageInfo: state.onCanvasPainted.pageInfo,
        pageAttr: state.onCanvasPainted.pageAttr,
    };
})
export class PageAnimationPanel extends PureComponent<IPageAnimationProps, IPageAnimationState> {
    constructor(props: Record<string, never>) {
        super(props);
    }

    render() {
        return (
            <div className="page-animation">
                <PageAnimation isLeftPanel={true} />
            </div>
        );
    }
}
