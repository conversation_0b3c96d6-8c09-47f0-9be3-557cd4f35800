import React, { PureComponent, Suspense } from 'react';

import { storeHOC } from '@v7_logic/StoreHOC';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { FixedWaterMaskButton } from '@v7_render/FixedWaterMaskButton';
import { CanvasToolBar as CanvasToolBarRight } from './CanvasToolBar';
import { SelectAsset } from '@v7_logic/AssetLogic';
import { CanvasPaintedLogic } from '@v7_logic/CanvasPaintedLogic';
import { emitter } from '@component/Emitter';
import { assetManager } from '@component/AssetManager';
import { addEventListener, EventListener } from '@v7_utils/AddEventListener';
import { ReactSortable, Sortable } from 'react-sortablejs';
import { loginCheckDecorator } from '@v7_logic/Decorators/Method';
import { EventSubscription } from 'fbemitter';
import { TemplateCanvasLogic } from '@v7_logic/TemplateCanvasLogic';
import './style/index.scss';
import { IPSConfig } from '@v7_utils/IPSConfig';
import CanvasReadOnly from '@src/userComponentV6.0/canvasReadOnly/canvasReadOnly1';
import { ICanvas, IPage, IPageAttr, IPageInfo, ITemplateInfo, IToolPanel, IUserInfo, IWork } from '@v7_logic/Interface';
import { Tooltip } from 'antd';
import { ClickOutside } from '@v7_render/Ui';
import { checkProhibitedWords } from '@v7_render/InfoBar';
import { PageThumbnail } from '@v7_renderCore/PageThumbnail';

interface propsStruct {
    canvas: ICanvas;
    pageInfo: IPageInfo;
    work: IWork;
    pageAttr: IPageAttr;
    toolPanel: IToolPanel;
    user: IUserInfo;
    info: ITemplateInfo;
    switchRenderMode?: React.ReactNode;
    preview: string[];
    resourcePanelWidth?: number;
    toolPanelWidth?: number;
    rt_canvas_render_mode?: '' | 'pull' | 'board';
}
interface stateStruct {
    isShowMorePage: boolean;
    isMoveState: boolean;
    menuPopoverLeft: number;
    toolTopLeft: number;
    hoverTooltip: number;
    selectActive: number;
    previewPageNow: number;
    floorTemIndex: number;
    preview: {
        big_img: string;
        small_img: string;
    }[];
    work: [];
    pages: IPage[];
    showContentDetail: boolean;
    pageNameEditAble: boolean;
    titleArr: string[]; // 单独控制标题回显
}

class CanvasToolBar extends PureComponent<propsStruct, stateStruct> {
    static displayName = 'CanvasFooterToolBar';

    emitterFooterMorePage: EventSubscription;
    showDetailPromiseResolve: (...args: unknown[]) => void;
    hasShowDetail: boolean;
    constructor(props: propsStruct) {
        super(props);

        this.state = {
            // isShowMorePage: props.preview.length > 1 ? true : false,
            isShowMorePage: false,
            isMoveState: false,
            menuPopoverLeft: 0,
            toolTopLeft: 0,
            hoverTooltip: 0,
            selectActive: -1,
            previewPageNow: 0,
            floorTemIndex: 0,
            preview: [],
            work: [],
            pages: [],
            showContentDetail: false,
            pageNameEditAble: false,
            titleArr: [],
        };
    }

    componentWillUnmount(): void {
        this.emitterFooterMorePage.remove();
    }

    componentDidMount() {
        // setTimeout(() => {
        //     const { work } = storeAdapter.getStore({
        //         store_name: storeAdapter.store_names.paintOnCanvas,
        //     });
        //     const { info } = storeAdapter.getStore({
        //         store_name: storeAdapter.store_names.InfoManage,
        //     });
        //     if (work.pages.length > 1 && info.template_type != '3') {
        //         this.setState({ isShowMorePage: true });
        //         this.calcPullCanvasSize(true);
        //         emitter.emit('setCanvasAddPage', false);
        //     } else {
        //         this.setState({ isShowMorePage: false });
        //         this.calcPullCanvasSize(false);
        //     }
        // }, 500);
        
        this.emitterFooterMorePage = emitter.addListener('emitterFooterMorePage', (type: boolean) => {
            const { rt_editor_type } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            if (rt_editor_type === 'dom') {
                this.setState({ isShowMorePage: type });
                emitter.emit('setCanvasAddPage', !type);
            }
        });
        const wrapDom = document.querySelector('.canvas_footer_tool_bar');
        const scrollDom = document.querySelector('.footerToolContent');
        wrapDom.addEventListener('wheel', (event: WheelEvent) => {
            // @ts-ignore
            if (event?.target?.closest?.('.keyboard-map,.recordListWrap,.feedback-modal-textarea')) {
                return;
            }
            // event.preventDefault();
            event.stopPropagation();
            // scrollDom.scrollLeft += event?.deltaY;
        });
        const {
            picId = 0,
            upicId = 0,
            version_id = 0,
            user_template_team_id = 0,
            paperId = 0,
            share_uid = 0,
        } = IPSConfig.getProps();


        assetManager
            .getPreviewList(
                picId as number,
                upicId as number,
                version_id as number,
                user_template_team_id as number,
                paperId as number,
                share_uid as number,
            )
            .then((res) => res.json())
            .then((data) => {
                const { work } = this.props;
                this.setState({
                    preview: data.url?.preview,
                    work: data.url.work,
                    pages: work.pages,
                });
            })
            .catch((e) => {
                console.error(e);
            });
        // 导航器内容采用异步渲染
        new Promise((res) => {
            this.showDetailPromiseResolve = res;
        }).then(() => {
            this.setState({ showContentDetail: true });
        });
    }

    componentDidUpdate(prevProps: Readonly<propsStruct>, prevState: Readonly<stateStruct>, snapshot?: any): void {
        const {work, pageAttr, info } = this.props;
        const urlProps = IPSConfig.getProps();
        const pageLen = work.pages.length;
        if (prevProps.pageAttr.pageInfo !== pageAttr.pageInfo || pageAttr?.pageInfo?.length && !this.state.titleArr.length) {
            this.setState({
                titleArr: pageAttr.pageInfo?.map((item) => (item?.title !== undefined ? item.title : '')) || [],
            });
        }

        if (
            !info ||
            !info.template_type ||
            // info.template_type == '3' ||
            !this.showDetailPromiseResolve 
            // this.hasShowDetail
        ) {
            return;
        }
        if (info.template_type == 3 && !this.state.isShowMorePage && !this.hasShowDetail && this.state.preview.length > 0) {
            this.showDetailPromiseResolve();
            this.showDetailPromiseResolve = null;
            this.setState({ isShowMorePage: true });
            this.calcPullCanvasSize(true);
            emitter.emit('setCanvasAddPage', false);
            this.hasShowDetail = true;
        } else if (info.template_type !== 3 && !this.hasShowDetail) {
            setTimeout(() => {
                // ppt模板先不dom渲染
                // 空闲时渲染
                if (window.requestIdleCallback) {
                    requestIdleCallback((deadLine) => {
                        if (deadLine.timeRemaining() > 1 && this.showDetailPromiseResolve) {
                            this.showDetailPromiseResolve();
                            this.showDetailPromiseResolve = null;
                        }
                    });
                } else {
                    this.showDetailPromiseResolve();
                    this.showDetailPromiseResolve = null;
                }
            }, pageLen * 500);
            this.hasShowDetail = true;
        }
    }

    calcPullCanvasSize(type: boolean) {
        const { pageAttr, pageInfo } = this.props;
        const pageType = pageAttr.pageInfo?.[pageInfo.pageNow]?.type || '';
        CanvasPaintedLogic.updateCanvasRenderMode({ mode: pageType || (type ? 'pull' : '') });
        TemplateCanvasLogic.canvasResizeByWindow();
    }

    hideClickEvent() {
        const { isShowMorePage } = this.state;
        assetManager.setPv_new(4249);
        // 如果用户立马点了,就立马给他渲染
        if (!isShowMorePage) {
            this.showDetailPromiseResolve && this.showDetailPromiseResolve();
            this.showDetailPromiseResolve = null;
        }
        this.setState({ isShowMorePage: !isShowMorePage });
        this.calcPullCanvasSize(!isShowMorePage);
        emitter.emit('setCanvasAddPage', isShowMorePage);
    }

    /**
     * 点击左侧缩略图事件
     */
    clickLeftPreview(index: number, e: React.MouseEvent) {
        const { pageInfo } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (pageInfo.pageNow == index) {
            return;
        }
        SelectAsset.blurAsset();
        CanvasPaintedLogic.selectPageTemplate({ pageNumber: index });
        CanvasPaintedLogic.mutisizeSetFloorTemplateCurrentindex({
            floorTemplateIndex: index,
            clickTem: 'left' as any,
        });
        this.setState({ floorTemIndex: index });
        e.stopPropagation();
    }

    /**
     * 添加模板缩略图
     */
    clickRightMenuItem = (e: React.MouseEvent) => {
        SelectAsset.blurAsset();
        CanvasPaintedLogic.mutisizeRightmenuItemAddEmptyTemplate();
        this.setState({ menuPopoverLeft: 0, selectActive: -1 });
        assetManager.setPv_new(4250);
        e.stopPropagation();
        e?.nativeEvent.stopPropagation();
    };
    /**
     * 按钮添加模板缩略图
     */
    clickRightBtn = (e: React.MouseEvent) => {
        SelectAsset.blurAsset();
        CanvasPaintedLogic.mutisizeRightmenuItemAddEmptyTemplate();
        const scrollDom = document.querySelector('.footerToolContent');
        setTimeout(() => {
            scrollDom.scrollBy(90, 0);
        }, 0);
        scrollDom.scrollBy(90, 0);
        assetManager.setPv_new(4250);
        emitter.emit('CanvasSizeCloseEmitter');
        e.stopPropagation();
        e?.nativeEvent.stopPropagation();
    };
    /** 删除模板缩略图  */
    copyMenuItem = (e: React.MouseEvent) => {
        SelectAsset.blurAsset();
        CanvasPaintedLogic.mutisizeRightmenuItemCopyEmptyTemplate({ pageNow: this.state.selectActive });
        assetManager.setPv_new(4251);
        emitter.emit('CanvasSizeCloseEmitter');
        this.setState({ menuPopoverLeft: 0, selectActive: -1 });
        e.stopPropagation();
        e?.nativeEvent.stopPropagation();
    };
    /** 删除模板缩略图  */
    deleteMenuItem(e: React.MouseEvent) {
        SelectAsset.blurAsset();
        CanvasPaintedLogic.mutisizeRightmenuItemDeleteEmptyTemplate({ pageNow: this.state.selectActive });
        this.setState({ menuPopoverLeft: 0, selectActive: -1 });
        emitter.emit('CanvasSizeCloseEmitter');
        assetManager.setPv_new(4252);
        e.stopPropagation();
        e?.nativeEvent.stopPropagation();
    }

    /** * 点击右上角菜单*/
    rightTopMenu(index: number, e: React.MouseEvent) {
        const th = this;
        const selectLeft = e.currentTarget.parentElement.offsetLeft;
        const scrollDom = document.querySelector('.footerToolContent');
        const scrollLeft = scrollDom.scrollLeft;
        const left = selectLeft - scrollLeft + 55;
        this.setState({ menuPopoverLeft: left, selectActive: index });
        e.stopPropagation();
        e?.nativeEvent.stopPropagation();
        assetManager.setPv_new(9020);
    }

    hideMenuPopover = () => {
        this.setState({ menuPopoverLeft: 0, selectActive: -1 });
    }
    /** * 移入*/
    // pageItemMove(index: number, e: React.MouseEvent) {
    //     if (!this.state.isMoveState) {
    //         const selectLeft = (e.currentTarget as any).offsetLeft;
    //         const scrollDom = document.querySelector('.footerToolContent');
    //         const scrollLeft = scrollDom.scrollLeft;
    //         const left = selectLeft - scrollLeft + 16;
    //         this.setState({ toolTopLeft: left, hoverTooltip: index + 1 });
    //     }
    //     e.stopPropagation();
    //     e?.nativeEvent.stopPropagation();
    // }
    addAfterPage = (e: React.MouseEvent<HTMLLIElement>) => {
        e.stopPropagation();
        e?.nativeEvent.stopPropagation();
        const { i } = e.currentTarget.dataset;
        SelectAsset.blurAsset();
        CanvasPaintedLogic.mutisizeRightmenuItemAddEmptyTemplate(Number(i));
        assetManager.setPv_new(9042);
    };

    renderFloorsDetail() {
        const { work, pageInfo, pageAttr, rt_canvas_render_mode } = this.props;
        const canvas = rt_canvas_render_mode === 'board' ? {...this.props.canvas, width: 1920, height: 1080} : this.props.canvas;
        const { width, height } = canvas;
        const maxHeight = 62;
        const minWidth = 49;

        let validHeight = 78,
            validWidth = 78
        const currentEditIndex = pageInfo.rt_floor_template_current_index || 0;
        if (canvas.width > canvas.height) {
            validHeight = (validWidth * height) / width;
        } else {
            validWidth = (validHeight * width) / height;
        }

        const ratio_w = validWidth / width,
            ratio_h = validHeight / height;
        let canvasScale = ratio_w > ratio_h ? ratio_h : ratio_w;

        canvasScale = maxHeight / height;
        if (width * canvasScale < minWidth) {
            canvasScale = minWidth / width;
        }

        const canvasContentStyle = {
            width: width,
            height: height,
        };

        const iconStyle = {
            display: 'flex',
        };
        const renderDone = this.state.work.length === this.state.preview.length;
        const scaleCanvas = {
            ...canvas,
            scale: canvasScale,
        }

        const pageHashMap = {};
        if (pageAttr.pageHash) {
            for (const key in pageAttr.pageHash) {
                pageHashMap[pageAttr.pageHash[key]] = key
            }
        }

        return work.pages.map((item, index: number) => {
            const currentPages = work.pages[index];
            const currentTag = currentPages.rt_page_tem_tag;
            const cacheIndex = this.state.pages.findIndex(
                ({ rt_page_tem_tag }) => rt_page_tem_tag && currentTag === rt_page_tem_tag,
            );
            const imgSrc = this.state.preview[cacheIndex]?.small_img;
            return (
                <div className="page-item-container" key={'pages-' + (pageHashMap[index] || currentPages.rt_page_tem_tag || index)}>
                    <div
                        className={`contentPageItem ${index == currentEditIndex ? ' active' : ''}`}
                        // style={{height: Math.round(floorStyle.height) + 14 + 'px'}}
                        onMouseDown={this.clickLeftPreview.bind(this, index)}
                        //    onMouseEnter={this.pageItemMove.bind(this, index)}
                        //    onMouseLeave={() => {
                        //        this.setState({ toolTopLeft: 0, hoverTooltip: 0 });
                        //    }}
                    >
                        <Tooltip placement="top" title={`第${index + 1}页${pageAttr.pageInfo?.[index]?.title ? `-${pageAttr.pageInfo[index].title}` : ''}`}>
                            <div className={`contentPageCanvas ${index == currentEditIndex ? ' active' : ''}`}>
                                <PageThumbnail
                                    key={'PageThumbnail-' + pageHashMap[index]}
                                    canvas={scaleCanvas}
                                    page={item}
                                    pageAttr={pageAttr}
                                    pageIndex={index}
                                    preview={renderDone && imgSrc}
                                    currentEditIndex={currentEditIndex}
                                />
                                {/* {i
                                {/* {imgSrc && renderDone ? (
                                    <img style={{ width: '100%', height: '100%', objectFit: 'contain' }} src={imgSrc} />
                                ) : (
                                    <CanvasReadOnly
                                        currentPage={currentPages}
                                        currentPageNum={index}
                                        canvasStyle={{ scale: canvasScale }}
                                        props={{ canvasContentStyle }}
                                        classNameTag={'onlinedetail_' + index}
                                        currentBlockIndex={index}
                                        parent="onlineDetailLeftPanel1"
                                        canvas={this.props.canvas}
                                        work={this.props.work}
                                    />
                                    
                                )} */}
                                {/* {<img style={{ width: '100%', height: '100%', objectFit: 'contain' }} src={imgSrc} />} */}
                            </div>
                        </Tooltip>
                        <div
                            className="menuIcon"
                            style={this.state.selectActive === index ? iconStyle : {}}
                            onClick={this.rightTopMenu.bind(this, index)}
                        >
                            <i className="iconfont icon-gengduo2"></i>
                        </div>
                        <div className="contentPageNumber">{index + 1}</div>
                    </div>
                    <div className="add-after-page">
                        <Tooltip placement="top" title="添加页面">
                        <i
                            className="iconfont icon-bianjiqidibuduoye-tianjia"
                            data-i={index}
                            onClick={this.addAfterPage}
                        ></i>
                        </Tooltip>
                    </div>
                </div>
            );
        });
    }

    setPages = (pages: any) => {
        // 不能使用库的排序结果，仅用来解决报错
    };
    @loginCheckDecorator
    onSortStart = (e: Sortable.SortableEvent) => {
        this.setState({ toolTopLeft: 0, hoverTooltip: 0, isMoveState: true });
    };

    onSortEnd = (e: Sortable.SortableEvent) => {
        assetManager.setPv_new(9031);
        const { newIndex, oldIndex } = e;
        if (newIndex !== oldIndex) {
            storeAdapter.dispatch({
                fun_name: 'SORT_PAGES',
                store_name: storeAdapter.store_names.paintOnCanvas,
                isAutoSave: true,
                params: [
                    {
                        type: 'sortPages',
                        params: {
                            oldIndex,
                            newIndex,
                        },
                    },
                ],
            });
        }
        this.setState({ toolTopLeft: 0, hoverTooltip: 0, isMoveState: false });
    };
    onSortMove = (e: Sortable.MoveEvent) => {
        this.setState({ toolTopLeft: 0, hoverTooltip: 0 });

        return e.related.className.indexOf('ignore') === -1;
    };
    onPageDragStart(e: any) {
        e.stopPropagation();
    }

    editPageName = () => {
        this.setState({
            pageNameEditAble: true,
        });
        assetManager.setPv_new(9043);
    };

    disableEditPageName = () => {
        this.setState({
            pageNameEditAble: false,
        });
    };

    onPageNameInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
        const title = e.currentTarget.value;
        checkProhibitedWords([title]).then((res) => {
            const { pageAttr, pageInfo } = this.props;
            if (res) {
                CanvasPaintedLogic.updatePageTitle({ title, pageNumer: pageInfo.pageNow });
                // assetManager.setPv_new(7451);
            } else {
                this.setState({
                    titleArr: pageAttr.pageInfo?.map((item) => (item?.title !== undefined ? item.title : '')) || [],
                });
            }
        });
    };

    onPageNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { user, pageInfo } = this.props;
        if (!(Number(user.userId) > 0)) {
            emitter.emit('LoginPanelShow');
            return;
        }
        if (user.bind_phone === 0) {
            emitter.emit('InfoBarPhoneBindPopup');
            return;
        }
        const title = e.currentTarget.value;
        const sliceTitleArr = this.state.titleArr.slice();
        sliceTitleArr[pageInfo.pageNow] = title;
        console.log(55555555, title, sliceTitleArr)
        this.setState({
            titleArr: sliceTitleArr,
        });
    };

    render() {
        const { isShowMorePage, menuPopoverLeft, hoverTooltip, toolTopLeft, showContentDetail, pageNameEditAble, titleArr } =
            this.state;
        const { canvas, work, pageInfo, pageAttr, rt_canvas_render_mode, toolPanel } = this.props;
        const currentEditIndex = pageInfo.rt_floor_template_current_index || 0;
        const tooltipStyle = {
            display: 'block',
            left: toolTopLeft,
        };

        const style = {
            left: 0,
            right: 0,
        };
        if (!canvas.floorCutting) {
            style.left = this.props.resourcePanelWidth - 72;
        } else if (canvas.floorCutting && this.props.resourcePanelWidth < 100) {
            style.left = 100;
        }
        if (!canvas.floorCutting) {
            style.right = this.props.toolPanelWidth;
        }
        if (rt_canvas_render_mode === 'board' && toolPanel.asset_index < 0 && toolPanel.assets_index?.length === 0) {
            style.right = 0;
        }

        return (
            <div className="canvas_footer_tool_bar" style={style}>
                <div className="footerToolTop">
                    <div className="footerToolContainer">
                        <FixedWaterMaskButton />
                        <div className="pageNum" onClick={this.hideClickEvent.bind(this)}>
                            <span>
                                页面{currentEditIndex + 1}/{work.pages.length}
                            </span>
                            <i
                                className={'iconfont icon-xiangyou'}
                                style={{
                                    color: '#202020',
                                    transform: !isShowMorePage ? 'rotate(-90deg)' : 'rotate(90deg)',
                                }}
                            ></i>
                        </div>
                    </div>
                    <div className="footerToolContainer">
                        <CanvasToolBarRight switchRenderMode={this.props.switchRenderMode} />
                    </div>
                </div>
                <div className={`footerToolContent ${isShowMorePage ? '' : 'hide'}`} tabIndex={0}>
                    <div className="contentPageList" onDragStart={this.onPageDragStart}>
                        <ReactSortable
                            className={`sort-pages`}
                            list={this.props.work.pages.map((p, pi) => {
                                return { pi, id: pi };
                            })}
                            setList={this.setPages}
                            animation={150}
                            group="workPages"
                            chosenClass="dragging"
                            onStart={this.onSortStart}
                            onEnd={this.onSortEnd}
                            onMove={this.onSortMove}
                        >
                            <Suspense fallback={<div></div>}>{showContentDetail && this.renderFloorsDetail()}</Suspense>
                        </ReactSortable>
                        <div className="contentPageAddContainer">
                            <Tooltip placement="top" title="添加页面">
                                <div className="contentPageAdd" onClick={this.clickRightBtn.bind(this)}>
                                    <i className="iconfont icon-dilan-xintianjiayemian"></i>
                                </div>
                            </Tooltip>
                        </div>
                    </div>
                    <ClickOutside onClickOutside={this.hideMenuPopover}>

                    <div
                        className="menuPopover"
                        style={{ display: menuPopoverLeft > 0 ? 'block' : 'none', left: menuPopoverLeft }}
                    >
                        <ClickOutside onClickOutside={this.disableEditPageName}>
                            <div className="page-name">
                                <input
                                    className={`page-name-input`}
                                    readOnly={!pageNameEditAble}
                                    type="text"
                                    placeholder='添加页面标题'
                                    value={titleArr?.[pageInfo.pageNow]}
                                    onChange={this.onPageNameChange}
                                    onBlur={this.onPageNameInputBlur}
                                    onClick={this.editPageName}
                                />
                                {!pageNameEditAble && (
                                    <i className="iconfont icon-xinshouye-biaotibianji" onClick={this.editPageName}></i>
                                )}
                            </div>
                        </ClickOutside>
                        <div className="menuList">
                            <div className="menuItem" onClick={this.clickRightMenuItem.bind(this)}>
                                <i className="iconfont icon-dilan-xintianjiayemian"></i>
                                添加页面
                            </div>
                            <div className="menuItem" onClick={this.copyMenuItem.bind(this)}>
                                <i className="iconfont icon-bianjiqi-xinhuabu-fuzhi"></i>
                                复制页面
                            </div>
                            {/* <div className="menuItem">粘贴页面</div> */}
                            <div className="menuItem" onClick={this.deleteMenuItem.bind(this)}>
                                <i className="iconfont icon-bianjiqi-xinhuabu-shanchu"></i>
                                删除页面
                            </div>
                        </div>
                    </div>
                    </ClickOutside>
                    
                    {/* <div style={hoverTooltip > 0 ? tooltipStyle : {}} className="tooltip itemTooltip">
                        第{hoverTooltip}页
                    </div> */}
                </div>
                {/* <div className="hideBtn" onClick={this.hideClickEvent.bind(this)}>
                    <img src="//s.tuguaishou.com/image/editor/background-footer-button.png"></img>
                    <i
                        className={'iconfont icon-xiangyou'}
                        style={{ color: '#202020', transform: !isShowMorePage ? 'rotate(-90deg)' : 'rotate(90deg)' }}
                    ></i>
                    <div className="tooltip pageTooltip">{isShowMorePage ? '隐藏页面' : '显示页面'}</div>
                </div> */}
            </div>
        );
    }
}

const mapStateToProps = (state: { [key: string]: any }) => {
    return {
        canvas: state.onCanvasPainted.canvas,
        pageInfo: state.onCanvasPainted.pageInfo,
        work: state.onCanvasPainted.work,
        pageAttr: state.onCanvasPainted.pageAttr,
        toolPanel: state.onCanvasPainted.toolPanel,
        user: state.onCanvasPainted.user,
        preview: state.onCanvasPainted.preview,
        info: state.infoManageStore.info,
        rt_canvas_render_mode: state.onCanvasPainted.rt_canvas_render_mode,
    };
};
const storeComponent = storeHOC(mapStateToProps, CanvasToolBar);

export { storeComponent as CanvasToolBar };
