import React, { Component, CSSProperties } from 'react';

import { addEventListener, EventListener } from '@v7_utils/AddEventListener';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { EventSubscription } from 'fbemitter';
import { emitter } from '@component/Emitter';

import { assetTemplate } from '@component/AssetMap';
import { assetManager } from '@component/AssetManager';

// import { GroupWordPanel } from '@v7_render/GroupWordPanel';
import { ETool } from '@v7_logic/Enum';
import { TextEditorAddBlock } from '@component/toolV6.0/specificWord/SpecificWord';
import { CopywritingBtnFloatPanel } from '@v7_render/CopywritingBtnFloatPanel';
import { AssetAddListener } from '@v7_logic/AssetAddListener';
import { fitPageDomSIze } from '@v7_logic/AssetAddListener/FitPageDomSize';
import { SearchBox, Scroll } from '@v7_render/Ui';
import { TextGroupSearchPanel, specificWordItem } from './../TextGroupSearchPanel/TextGroupSearchPanel';
import { GroupWord  } from '../GroupWordNew/GroupWordNew';
import {DocGraphicPanel} from '../DocGraphicPanel/DocGraphicPanel'
import './scss/SpecificWordNew.scss';

interface propsStruct {
    // fontCount:number
    isActive?: boolean;
}

interface stateStruct {
    navId: string;
    isCommonCopywritingBtn: boolean;
    classArr: [];
    fontCount?: number;
    specificWordPreviewStyle?:
        | {
              top: number;
              backgroundImage: string;
          }
        | string;
    textGroupTab: 'specialWord' | 'textGroup';
    isSearchPlane: boolean;
    searchValue: string;
    allSpecialFontList: specificWordItem[];
    isDocEditor: boolean;
}

export function DisplaySpecificWordTool(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.SPECIFIC_WORD, nav: ETool.SPECIFIC_WORD};
}

export class SpecificWord extends Component<propsStruct, stateStruct> {
    titleProp: { fontSize: number; fontWeight: string; ranking: string };
    mainBodyProp: { fontSize: string; fontWeight: string; ranking: string };
    addTextEditorTitleEvent: {
        (e: React.MouseEvent<HTMLDivElement, MouseEvent>): void;
    };
    addTextEditorMainBodyEvent: {
        (e: React.MouseEvent<HTMLDivElement, MouseEvent>): void;
    };
    addEventListenerListener: EventListener;
    TextEditorAddBlockAddTextEditorEventEmitter: EventSubscription;
    changeTextGroupTabEventEmitter: EventSubscription;
    scrollBar: any;

    constructor(props: propsStruct) {
        super(props);

        this.titleProp = {
            fontSize: 24,
            fontWeight: 'bold',
            ranking: '0',
        };

        this.mainBodyProp = {
            fontSize: 'small',
            fontWeight: 'normal',
            ranking: '0',
        };
        this.state = {
            navId: '0',
            isCommonCopywritingBtn: false,
            classArr: [],
            textGroupTab: 'textGroup',
            isSearchPlane: false,
            searchValue: '',
            allSpecialFontList: [],
            isDocEditor: false,
        };

        this.addTextEditorTitleEvent = this.addTextEditorEvent.bind(this, this.titleProp, '', 'script');
        this.addTextEditorMainBodyEvent = this.addTextEditorEvent.bind(this, this.mainBodyProp, '', 'text');
        this.getUseingFontCount();
        this.addTextEditorEventEmitter();
        this.addChangeTextGroupTabEmiiter();
        this.getSpecialFontList();
    }

    componentDidMount(): void {
        const { rt_editor_scene } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        this.setState({
            isDocEditor: rt_editor_scene === 'doc'
        })
    }

    getUseingFontCount(): void {
        const th = this;
        assetManager.getUsingFontCount().then((data) => {
            data.json().then((resultData) => {
                if (resultData.stat == 1) {
                    th.setState({
                        fontCount: resultData.count,
                    });
                }
            });
        });
    }

    componentWillUnmount(){
        this.changeTextGroupTabEventEmitter?.remove();
        if( this.TextEditorAddBlockAddTextEditorEventEmitter ){
            this.TextEditorAddBlockAddTextEditorEventEmitter.remove();
        }
    }

    addTextEditorEventEmitter(){

        this.TextEditorAddBlockAddTextEditorEventEmitter = emitter.addListener('TextEditorAddBlockAddTextEditorEventEmitter', (value: string) => {
            console.log('xxxx')
            const assetProps = {
                fontSize: 18,
                fontWeight: 'normal',
                ranking: '1'
            } as const;
            if( value == undefined || value == '' ){
                value = '双击编辑文字'
            }
            this.addTextEditorEvent(assetProps, value);
        });
    }

    addChangeTextGroupTabEmiiter() {
        this.changeTextGroupTabEventEmitter = emitter.addListener('ChangeTextGroupTabEventEmitter', (tab: 'specialWord' | 'textGroup') => {
            // 从添加菜单进入
            assetManager.setPv_new(8186, { additional: { s0: tab, s1: 'addMenu' } });
            this.setState({textGroupTab: tab});
        });
    }

    getSpecialFontList() {
        assetManager.getSpecificWordList().then((data) => {
            data.json().then((resultData) => {
                if (resultData.stat == 1) {
                    this.setState({
                        allSpecialFontList: resultData.msg
                    })
                }
            });
        });
    }

    stopPropagation(e: MouseEvent): void {
        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }

    commonCopywritingBtnClickEvent(e: MouseEvent): void {
        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }); // canvasStore.getState().onCanvasPainted;
        if (!(user.userId > 0)) {
            emitter.emit('LoginPanelShow', 'previewAndShare');
            assetManager.setPv_new(174, {
                additional: {
                    s0: 'previewAndShare',
                },
            });
            return;
        }
        assetManager.setPv_new(7102, {
            additional: {
                s1: 'textGroup',
            },
        });

        assetManager.setPv_new(1993);
        this.setState({
            isCommonCopywritingBtn: true,
        });

        // if (this.addEventListenerListener) {
        //     this.addEventListenerListener.remove();
        // }

        // const th = this;
        // @ts-ignore
        // this.addEventListenerListener = addEventListener(window, 'click', () => {
        //     th.setState({
        //         isCommonCopywritingBtn: false,
        //     });

        //     th.addEventListenerListener.remove();
        // });

        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }

    addTextEditorEvent(
        assetProps: {
            fontSize: number | 'small';
            fontWeight: 'normal' | 'bold';
            ranking: '0' | '1' | '2';
        },
        value: string,
        sign?: string,
        e?: React.MouseEvent,
    ): void {
        AssetAddListener.addText(
            {
                ...assetProps,
                fontSize: 24,
                // 用来替代旧的 临时 fontSize === 'small' 产生的类型校验错误
                isSmallFontSize: assetProps.fontSize === 'small',
            },
            undefined,
            value,
        );
        if (e) {
            e.stopPropagation();
            e.nativeEvent.stopPropagation();
        }
        assetManager.setPv_new(3140, { additional: { s0: sign } });
        assetManager.setPv_new(7102, {
            additional: {
                s1: 'textGroup',
            },
        });
        return;
    }


    // 拖拽字体事件
    dragAddEvent(
        assetProps: {
            [x: string]: any;
            fontSize: number;
            fontWeight: string;
            ranking: string | number;
            isSmallFontSize: boolean;
            fontFamily?: string;
        },
        id: string,
        e: React.MouseEvent,
    ) {
        let value;
        const type = 'text';
        if (value == undefined || value == '') {
            value = '双击编辑文字';
        }
        const asset = JSON.parse(JSON.stringify(assetTemplate.text)),
            { canvas } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            }),
            { recommendFont } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.RecommendManage,
            });
        let recommendFontFlag = 0;

        let width = 200,
            height = 50;

        Object.assign(asset.meta, {
            isEdit: id ? false : true,
            type: 'text',
            addOrigin: id ? 'specificWord' : 'text',
        });
        Object.assign(asset.attribute, {
            text: [value],
            fontSize: assetProps.fontSize,
            fontWeight: assetProps.fontWeight,
            textAlign: 'center',
            width: width,
            opacity: 100,
            lineHeight: 13,
            letterSpacing: 0,
            fontFamily: assetProps.fontFamily??'fnsyhtRegular',
            color: { r: 0, g: 0, b: 0, a: 1 },
            effect: id ? id + '@0' : '',
            effectVariant: {
                state: 'add',
            },
            rt_fontFamily_important: assetProps?.fontFamily
        });

        if (id) {
            asset.attribute.rt_isNowAdd = true;
        } else {
            asset.attribute.writingMode = 'horizontal-tb';
        }

        try {
            if (recommendFont[assetProps.ranking]) {
                width = parseInt(recommendFont[assetProps.ranking]['font_size']) * 9;
                Object.assign(asset.attribute, {
                    fontSize: parseInt(recommendFont[assetProps.ranking]['font_size']),
                    width: width,
                });
                recommendFontFlag = 1;
            }
        } catch (err) {}

        if (!recommendFontFlag) {
            let tempFontSize: number;
            const fontNum = 6;

            if (assetProps.ranking == 2) {
                tempFontSize = (canvas.width * 1) / 3 / fontNum;
            } else if (assetProps.ranking == 1) {
                tempFontSize = (canvas.width * 1) / 2 / fontNum;
            } else {
                tempFontSize = (canvas.width * 2) / 3 / fontNum;
            }

            width = tempFontSize * 9;
            Object.assign(asset.attribute, {
                fontSize: tempFontSize,
                width: width,
            });

            height = tempFontSize;
        }

        Object.assign(asset.transform, {
            posX: ((canvas.width - width) / 2) * canvas.scale,
            posY: ((canvas.height - height) / 2) * canvas.scale,
        });
        const { isDesigner } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if (!id) {
            // 标题 正文
            if (assetProps.isSmallFontSize) {
                asset.attribute.rt_fontSize = 'small';
            }
            if (assetProps.ranking === '0' && !isDesigner) {
                if (assetProps.isSmallFontSize) {
                    asset.meta.isTitle = 1;
                }
            }
        } else {
            // 特效字
            if (!isDesigner) {
                asset.meta.isTitle = 1;
            }
            const textType = assetProps['textType'] ? assetProps['textType'] : 'specificWord';
            // createTime = Math.floor(new Date().getTime() / 1000)
            const { createTime } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });

            if (textType === 'specificWord') {
                asset.attribute = {
                    ...asset.attribute,
                    effect: id + '@0',
                    effectVariant: {
                        state: 'add',
                    },
                };
                assetManager.setPv_new(163, {
                    additional: {
                        s0: 'specificWordId',
                        s1: id,
                        s2: createTime,
                    },
                });
            }
        }

        const paintOnCanvasState = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        // 根据画布调整size
        fitPageDomSIze(paintOnCanvasState, asset);
        emitter.emit('ListDragAddDown', asset, type, e);
    }

    // onMouseOver字体事件
    specificWordPreviewMouseOverEvent(id: any, path: string, e: { target: { offsetTop: number } }) {
        const toolLayout = document.querySelector('.toolLayout') as HTMLElement;
        const rootLayout = document.querySelector('.rightLayout') as HTMLElement;

        if (rootLayout) {
            if (rootLayout.offsetWidth != 360) {
                this.setState({ specificWordPreviewStyle: '' });
                return;
            }
        }
        const maxHeight = toolLayout.offsetHeight;
        const scrollTop = this.scrollBar.scrollTop;

        const offsetTop = e.target.offsetTop - scrollTop - 100;

        let PreviewTop = 0;
        if (offsetTop < 0) {
            PreviewTop = 2;
        } else if (offsetTop + 280 > maxHeight) {
            PreviewTop = maxHeight - 280 - 2;
        } else {
            PreviewTop = offsetTop;
        }
        if (path) {
            path = '//' + path;
        } else {
            path = `//js.tuguaishou.com/specificWordPreview/${id}-big.png`;
        }
        this.setState({
            specificWordPreviewStyle: {
                top: PreviewTop,
                backgroundImage: `url("${path}?t=155555")`,
            },
        });
    }

    specificWordPreviewMouseLeave() {
        this.setState({ specificWordPreviewStyle: '' });
    }
        /**
     * 添加特效字
     * @param item
     * @param e
     */
    addSpecialTextEditorEvent(item: specificWordItem, e: React.MouseEvent): void {
        const specificWordProp = {
            fontSize: 24,
            fontWeight: 'normal',
            ranking: '0',
        } as const;
        const { createTime } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        AssetAddListener.addText(specificWordProp, { id: item.id, textType: 'specificWord' }, undefined);
        e?.stopPropagation();
        e?.nativeEvent.stopPropagation();
        assetManager.setPv_new(163, { additional: { 
            s0: 'specificWordId',
            s1: item.id,
            s2: createTime
        } });
    }

    onSearchAll(value: string) {
        this.setState({
            searchValue: value,
        });
        assetManager.setPv_new(8184, { additional: { s0: value } })
    }

    searchInputFocus() {
        assetManager.setPv_new(8183)
    }
    
    onTabChange(key: string, s1?: string): void {
        assetManager.setPv_new(8186, { additional: { s0: key, s1 } });
        this.setState({
            textGroupTab: key as 'specialWord' | 'textGroup',
        });
    }

   renderTextGroupItem = (worldList: specificWordItem[]) => {
        const filterId = [31, 25, 57, 29]
        const list:specificWordItem[]= [];
        filterId.forEach((id) => {
            const item = worldList.find((item) => Number(item.id) === id);
            if (item) {
                list.push(item);
            }
        });
        return (
            <div className="assetListItemContent">
            <Scroll
                onPrev={() => {
                    assetManager.setPv_new(7301, { additional: { s0: 'l', s1: 'specialWord' } });
                }}
                onNext={() => {
                    assetManager.setPv_new(7301, { additional: { s0: 'r', s1: 'specialWord' } });
                }}
            >
                {list.length > 0 &&
                    list.map((item: specificWordItem) => {
                        return (
                            <div
                                className="listItem assetItem"
                                style={{ overflow: 'hidden' }}
                                key={item.id}
                                onMouseDown={this.dragAddEvent.bind(this,
                                    {
                                        fontSize: 24,
                                        fontWeight: 'normal',
                                        isSmallFontSize: true,
                                        ranking: '0',
                                        textType: 'specificWord',
                                    },
                                    item.id)
                                }
                                onClick={this.addSpecialTextEditorEvent.bind(this, item)}
                            >
                                <div className="elementFg" >
                                    <img src={`//${item.host + item.preview2}?t=155543)`} alt={item.name} />
                                </div>
                            </div>
                        );
                    })}
            </Scroll>
        </div>
        )
    }

    render(): JSX.Element {
        const { navId, fontCount, isCommonCopywritingBtn, specificWordPreviewStyle, textGroupTab, searchValue, allSpecialFontList, isDocEditor } =
            this.state;
        if (isDocEditor) {
            return <DocGraphicPanel wheelAdd={false}></DocGraphicPanel>
        }
        return (
            <>
                <div className="specificWordNew">
                    <div className="textSearchWrap">
                        <SearchBox placeholder="搜索文字" onSearch={this.onSearchAll.bind(this)} searchInputFocus={this.searchInputFocus}/>
                    </div>
                    <div className="textGroupWrap">
                        <div className="textGroupInner">
                            <div className="addTextContainer newAddTextCtn">
                                <div
                                    className="item addText"
                                    onClick={this.addTextEditorTitleEvent}
                                    onMouseDown={this.dragAddEvent.bind(
                                        this,
                                        {
                                            fontSize: 24,
                                            fontWeight: 'bold',
                                            ranking: '0',
                                        },
                                        '',
                                    )}
                                    onMouseOver={this.specificWordPreviewMouseOverEvent.bind(this, '00', '')}
                                    onMouseLeave={this.specificWordPreviewMouseLeave.bind(this)}
                                >
                                    <span className="iconfont icon-zuoce-tianjiabiaoti"></span>
                                    <span>添加标题</span>
                                </div>
                                <div
                                    className="item addMainText"
                                    onClick={this.addTextEditorMainBodyEvent}
                                    onMouseDown={this.dragAddEvent.bind(
                                        this,
                                        {
                                            fontSize: 14,
                                            fontWeight: 'normal',
                                            isSmallFontSize: true,
                                            ranking: '0',
                                        },
                                        '',
                                    )}
                                    onMouseOver={this.specificWordPreviewMouseOverEvent.bind(this, '01', '')}
                                    onMouseLeave={this.specificWordPreviewMouseLeave.bind(this)}
                                >
                                    <span className="iconfont icon-zuoce-tianjiazhengwen"></span>
                                    <span>添加正文</span>
                                </div>
                                {specificWordPreviewStyle && (
                                    <div
                                        className={'specificWordPreviewWindow'}
                                        style={specificWordPreviewStyle as CSSProperties}
                                        onMouseLeave={this.specificWordPreviewMouseLeave.bind(this)}
                                    ></div>
                                )}
                                <div
                                    className="item commonCopywritingBtn"
                                    onClick={this.commonCopywritingBtnClickEvent.bind(this)}
                                >
                                    <span className="iconfont icon-zuoce-changyongwenan"></span>
                                    <span>常用文案</span>
                                </div>
                            </div>

                            {isCommonCopywritingBtn && (
                                <CopywritingBtnFloatPanel
                                    closeCallBack={() => this.setState({ isCommonCopywritingBtn: false })}
                                />
                            )}
                            <div className="textGroupTab">
                                <div
                                    className={textGroupTab === 'textGroup' ? 'item active' : 'item'}
                                    onClick={() => this.onTabChange('textGroup')}
                                >
                                    文字组合
                                </div>
                                <div
                                    className={textGroupTab === 'specialWord' ? 'item active' : 'item'}
                                    onClick={() => this.onTabChange('specialWord')}
                                >
                                    特效字
                                </div>
                            </div>
                            {textGroupTab === 'textGroup' && <GroupWord onTabChange={this.onTabChange.bind(this)} renderSpecialItem={this.renderTextGroupItem(allSpecialFontList)}/>}
                            {textGroupTab !== 'textGroup' &&<TextEditorAddBlock isActive={true} navId={navId} key={'0'} />}
                        </div>
                    </div>
                </div>
                <div className="searchRsWrap">
                    <TextGroupSearchPanel search={searchValue} onTextDragEvent={this.dragAddEvent} allSpecialFontList={allSpecialFontList}></TextGroupSearchPanel>
                </div>
            </>
        );
    }
}
