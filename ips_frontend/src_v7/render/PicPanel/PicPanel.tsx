import React, { Component } from 'react';

import { assetManager } from '@component/AssetManager';
import { MaterialPic } from '@v7_render/MaterialPic';
import {ETool} from '@v7_logic/Enum';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { env } from '@editorConfig/env';
import { isUeTeam } from '@v7_utils/webSource';


export function DisplayPicTool(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.PIC, nav: ETool.PIC};
}
interface propsStruct {
    isGallery?: boolean;
    isActive?: boolean;
}

// eslint-disable-next-line @typescript-eslint/ban-types
export class PicPanel extends Component<propsStruct, {}> {
    stopPropagation(e: MouseEvent): void {
        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }

    onGoCorporateMembers(e: MouseEvent): void {
        assetManager.setPagePv_new(3255);
        const { info } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage,
        });
        if(info?.is_company_temp === 1 || env.teamTemplate || isUeTeam){
            window.open(
                'https://818ps.com/dash/firm-intro?origin=font-authorize-btn&route_id=16328996965437&route=1,&after_route=1',
            );
        }else{
            const {user} = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            assetManager.getUserInfo()
            .then((data) => data.json())
            .then(userInfo => {
                if(user.userId == 0 || userInfo.commercial_type == -1 || userInfo.commercial_type == 0) {
                    window.open(
                        "https://818ps.com/dash/vip-spec-non-commercial?origin=skip_common_vipSpec"
                    )
                } else if(userInfo.commercial_type == 1 || userInfo.commercial_type == 2) {
                    window.open(
                        "https://818ps.com/dash/vip-spec-commercial?classify=1&origin=skip_advanced_vipSpec&route_id=16007403774149&route=1,86&after_route=1_86"
                    )
                }
            })
            // if(user.isJumpCommercial === 1){
            //     window.open(
            //         'https://818ps.com/dash/vip-spec-commercial?classify=1&origin=VipSpec&route_id=16007403774149&route=1,86&after_route=1_86',
            //     );
            // }else{
            //     window.open(
            //         'https://818ps.com/dash/vip-spec?classify=1&origin=VipSpec&route_id=16007403774149&route=1,86&after_route=1_86',
            //     );
            // }
        }
        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }

    onMouseEnterEvent(e: MouseEvent): void {
        assetManager.setPagePv_new(3264);
        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }

    onMouseLeaveEvent(e: MouseEvent): void {
        assetManager.setPagePv_new(3263);
        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }

    render(): JSX.Element {
        const { isDesigner } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }); // canvasStore.getState().onCanvasPainted;
        return (
            <div className={'picPanel' + (this.props.isGallery ? ' child' : '')}>
                {/* {!isDesigner && (
                    <div className="specificWord">
                        <div
                            className="company_use"
                            onClick={this.onGoCorporateMembers.bind(this)}
                            onMouseLeave={this.onMouseLeaveEvent.bind(this)}
                            onMouseEnter={this.onMouseEnterEvent.bind(this)}
                        >
                            <i className="iconfont icon-shang"></i>
                            <span>商用会员所有照片可放心商用</span>
                        </div>
                    </div>
                )} */}

                <MaterialPic />
            </div>
        );
    }
}
