/* eslint-disable prefer-const */
import React, { Component, CSSProperties } from 'react';

import { assetManager } from '@component/AssetManager';
import { emitter } from '@component/Emitter';
// @ts-ignore
import MasonryInfiniteScroller from 'react-masonry-infinite';
// @ts-ignore
import LazyLoad from 'react-lazy-load';
import { IAsset } from '@v7_logic/Interface';
import {ETool} from '@v7_logic/Enum/'
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import {addEventListener, EventListener} from '@v7_utils/AddEventListener';
// import EmojiPanel from '../EmojiPanel/EmojiPanel';

export function DisplayTeamWorkTool(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.TEAM_WORK, nav: ETool.TEAM_WORK};
}

interface propsStruct {
    isActive?: boolean;
}

interface stateStruct {
    userTeamInfo: { id: number; name: string };
    isTeam: boolean;
    teamInfo: { name: string }[];
    showChooseTeam: boolean;
    tabType: number;
    isDropdownBox: boolean;
    assetsList: { id: number; imgUrl: string; width: number; height: number; sample: string }[];
    dropdownBoxStyle: { top: number };
    updateTime: number;
    showChooseProject: boolean;
    chooseTeamIndex: number;
    userProjectInfo: { name: string; id: number };
    pages: number;
    scrollbarHeight: number;
    lastPage: boolean;
    userProject: { name: string }[];
    isShowTeamVip: boolean;
    wheelAdd?: boolean;
}

class TeamWorkPanel extends Component<propsStruct, stateStruct> {
    selectTypeTeamClick: EventListener;
    selectTypeProjectClick: EventListener;
    loadListData_timer: NodeJS.Timeout;
    teamWorkNode: HTMLDivElement;
    constructor(props: propsStruct) {
        super(props);

        this.state = {
            userTeamInfo: { id: 0, name: '' },
            isTeam: false,
            teamInfo: [],
            showChooseTeam: false,
            tabType: 1,
            isDropdownBox: false,
            assetsList: [],
            dropdownBoxStyle: { top: 0 },
            updateTime: new Date().getTime(),
            showChooseProject: false,
            chooseTeamIndex: 0,
            userProjectInfo: { id: 0, name: '' },
            pages: 1,
            scrollbarHeight: 700,
            lastPage: false,
            userProject: [],
            isShowTeamVip: false,
        };
        assetManager.getTeamName().then((data) => {
            data.json().then((resultData) => {
                if (resultData.stat == 1 && resultData.msg.length > 0) {
                    this.setState({
                        teamInfo: resultData.msg,
                        isTeam: true,
                        userTeamInfo: resultData.msg[0],
                    });
                    this.getTeamFloder(resultData.msg[0].id, this.state.tabType);
                }
            });
        });
    }

    componentDidMount(): void {
        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }); // canvasStore.getState().onCanvasPainted;

        const isShowTeamVip =
            user &&
            user.vip &&
            (Number(user.vip) == 47 || Number(user.vip) == 48 || Number(user.vip) == 51 || Number(user.vip) == 52);

        if (!isShowTeamVip) {
            assetManager.setPagePv_new(3237);
        }

        this.setState({
            isShowTeamVip,
        });
    }

    getTeamDetailContent(team_id: string, type: string, folder_id: number, pages: number): void {
        // if(lastPage ) return;
        assetManager.getTeamDetailContent(team_id, type, folder_id, pages).then((data) =>
            data.json().then((resultData) => {
                if (resultData.stat == 1) {
                    if (pages == 1) {
                        this.setState({
                            assetsList: resultData.msg,
                            wheelAdd: true,
                            updateTime: new Date().getTime(),
                        });
                        if (resultData.msg.length < 30) {
                            this.setState({
                                wheelAdd: true,
                                lastPage: true,
                            });
                        }
                        return;
                    } else {
                        const tempObject = this.state.assetsList;
                        if (resultData.msg.length == 0) {
                            this.setState({
                                wheelAdd: true,
                                lastPage: true,
                            });
                            return;
                        }
                        for (const item in resultData) {
                            tempObject.push(resultData[item]);
                        }
                        this.setState({
                            assetsList: tempObject,
                            wheelAdd: true,
                            updateTime: new Date().getTime(),
                        });
                    }
                }
            }),
        );
    }

    getTeamFloder(team_id: number, type: number): void {
        assetManager.getTeamFloder(team_id, type).then((data) => {
            data.json().then((resultData) => {
                if (resultData.stat == 1 && resultData.msg.length > 0) {
                    this.setState(
                        {
                            userProjectInfo: resultData.msg[0],
                            userProject: resultData.msg,
                        },
                        () => {
                            const { userTeamInfo, userProjectInfo } = this.state;
                            this.getTeamDetailContent(userTeamInfo.id.toString(), type.toString(), userProjectInfo.id, 1);
                        },
                    );
                }
            });
        });
    }

    changeTeamType(item: { id: number; name: string }, index: number, e: MouseEvent): void {
        this.setState({
            userTeamInfo: item,
            chooseTeamIndex: index,
            showChooseTeam: false,
            pages: 1,
            lastPage: false,
        });
        this.getTeamFloder(item.id, this.state.tabType);
        assetManager.setPagePv_new(2489);
        e.stopPropagation();
    }

    selectTypeTeam(e: MouseEvent): void {
        if (this.selectTypeTeamClick) {
            this.selectTypeTeamClick.remove();
        }
        this.setState({
            showChooseTeam: true,
            showChooseProject: false,
        });
        const _this = this;
        this.selectTypeTeamClick = addEventListener(window, 'click', function () {
            _this.setState({
                showChooseTeam: false,
                showChooseProject: false,
            });
            _this.selectTypeTeamClick.remove();
        });
        assetManager.setPv_new(2939, { additional: {} });
        e.stopPropagation();
    }

    selectTypeProject(e: MouseEvent): void {
        if (this.selectTypeProjectClick) {
            this.selectTypeProjectClick.remove();
        }
        this.setState({
            showChooseProject: true,
            showChooseTeam: false,
        });
        const _this = this;
        this.selectTypeProjectClick = addEventListener(window, 'click', function () {
            _this.setState({
                showChooseTeam: false,
                showChooseProject: false,
            });
            _this.selectTypeProjectClick.remove();
        });
        e.stopPropagation();
    }

    changeProjectType(item: { id: number; name: string }, e: MouseEvent): void {
        this.setState({
            userProjectInfo: item,
            showChooseProject: false,
            pages: 1,
            lastPage: false,
        });
        this.getTeamDetailContent(this.state.userTeamInfo.id.toString(), this.state.tabType.toString(), item.id, 1);
        assetManager.setPv_new(2941, {
            additional: {
                btyType: this.state.tabType,
            },
        });
        e.stopPropagation();
    }

    /**
     * 选项卡（点击事件）
     * @param e
     */
    materialTabItemClickEvent(tabType: number, e: MouseEvent): void {
        this.setState({
            tabType: tabType,
            pages: 1,
            lastPage: false,
        });
        this.getTeamFloder(this.state.userTeamInfo.id, tabType);
        assetManager.setPv_new(2940, {
            additional: {
                btyType: tabType,
            },
        });
        e.stopPropagation();
        (e as any)?.nativeEvent.stopImmediatePropagation();
    }

    /**
     * 滚动条移动
     **/
    wheelMoveEvent(event: React.ChangeEvent): boolean {
        const { assetsList, lastPage } = this.state;
        if (!(assetsList.length > 0)) {
            return false;
        }
        event.stopPropagation();
        const clientHeight = event.target.clientHeight;
        const scrollHeight = event.target.scrollHeight;
        const scrollTop = event.target.scrollTop;
        const tempNum = 100;
        const isBottom = clientHeight + scrollTop + tempNum >= scrollHeight;

        if (scrollTop > 300) {
            this.setState({
                isDropdownBox: true,
            });
        } else {
            this.setState({
                isDropdownBox: false,
            });
        }
        if (isBottom && !lastPage) {
            if (!this.loadListData_timer) {
                this.setState(
                    {
                        pages: this.state.pages + 1,
                    },
                    () => {
                        // if(this.state.userProjectInfo != ''){
                        //     this.getTeamDetailContent(this.state.userTeamInfo.id, this.state.tabType, this.state.userProjectInfo.id, this.state.pages + 1)
                        // }else{
                        //     this.getTeamDetailContent(this.state.userTeamInfo.id, this.state.tabType, '', this.state.pages + 1)
                        // }
                        this.getTeamFloder(this.state.userTeamInfo.id, this.state.tabType);
                    },
                );
                this.loadListData_timer = setTimeout(() => {
                    clearTimeout(this.loadListData_timer);
                    this.loadListData_timer = null;
                }, 1000);
            }
        }
    }

    windowResize(): void {
        const innerHeight = document.body.clientHeight - 40;
        let scrollbarHeight = innerHeight,
            rightLayoutDom = document.getElementsByClassName('rightLayout');

        if (rightLayoutDom && this.teamWorkNode) {
            scrollbarHeight = (rightLayoutDom[0] as HTMLDivElement).offsetHeight - this.teamWorkNode.getBoundingClientRect().top + 58;
        }

        this.setState({
            scrollbarHeight: scrollbarHeight,
        });
    }

    /**
     * 元素（点击事件）
     * @param asset
     * @param e
     */
    assetItemClickEvent(asset: IAsset, e: MouseEvent): void {
        const { tabType } = this.state;
        if (tabType == 2 || tabType == 3) {
            const element = {
                id: 'UA-' + asset.id,
                image_url: asset.imgUrl_max,
                width: asset.width,
                height: asset.height,
                source_width: asset.width,
                source_height: asset.height,
            };
            emitter.emit('ListAddElement', element);
        } else {
            window.open(`https://818ps.com/team/project?team_id=${this.state.userTeamInfo.id}`);
            // window.open(
            //     `https://818ps.com/team/project-jump?team_id=${this.state.userTeamInfo.id}&utid=${asset.utid}&pram=eyJyb3V0ZV9pZCI6IjE1OTI4MTgxNTY4MTUzIiwicm91dGUiOiIxLWF2YXRhciw3MCwiLCJhZnRlcl9yb3V0ZSI6IjEtYXZhdGFyLDcwIiwicmVmZXJlciI6IiUyRnRlYW0lMkZ0ZWFtLXByb2plY3QlM0Z0ZWFtX2lkJTNEMTIifQ==`,
            // );
            // window.open(
            //     `https://818ps.com/team/team-project-jump?team_id=${this.state.userTeamInfo.id}&utid=${asset.utid}&pram=eyJyb3V0ZV9pZCI6IjE1OTI4MTgxNTY4MTUzIiwicm91dGUiOiIxLWF2YXRhciw3MCwiLCJhZnRlcl9yb3V0ZSI6IjEtYXZhdGFyLDcwIiwicmVmZXJlciI6IiUyRnRlYW0lMkZ0ZWFtLXByb2plY3QlM0Z0ZWFtX2lkJTNEMTIifQ==`,
            // );

            // // window.open(`//ue.818ps.com/?upicId=${asset.utid}&template_type=1&team_id=${this.state.userTeamInfo.id}`);
            // let tempUrl = document.location.href.replace(/upicId=[^&#]*/, '')
            // tempUrl = tempUrl + '&upicId=' + asset.utid
            // tempUrl = tempUrl.replace(/picId=[^&#]*[&| ]/, '')
            // // history.pushState('', document.title, ipsUrl('?upicId=' + result1.info.id))
            // history.pushState('', document.title, tempUrl)
            // // location.replace(tempUrl)

            // assetManager.getTemplate('', asset.utid, this.state.userTeamInfo.id).then((data) => {
            //     data.json().then((resultData) => {
            //         if( resultData.stat != 1 ){
            //             return false;
            //         }

            //         let info = resultData.info,
            //             recommendFont = resultData.recommendFont

            //         // info.title = '你身边的设计怪兽'
            //         let preview = resultData.preview;
            //         resultData = resultData.doc
            //         resultData = templateFormat.getFormat(resultData)

            //         canvasStore.dispatch(paintOnCanvas('UPDATE_CANVAS_MAP_REPLACE', {
            //             picId: '',
            //             canvas: resultData.canvas,
            //             work: resultData.work,
            //             subRecord: {
            //                 origin: this.props.addProps ? this.props.addProps.origin : ''
            //             },
            //             preview:preview
            //         }))
            //         let toolPanel = canvasStore.getState().onCanvasPainted.toolPanel;
            //         Object.assign(toolPanel, {
            //             asset: '',
            //             assets: ''
            //         });
            //         canvasStore.dispatch(infoManage('UPDATE_INFO', info))
            //         emitter.emit('quickEditingUpdateState');

            //         canvasStore.dispatch(paintOnCanvas('UPDATE_COMMON_CANVAS_CENTER'));
            //         emitter.emit('NavLayoutupdateState');
            //         canvasStore.dispatch(recommendManage('UPDATE_RECOMMEND_FONT', {recommendFont: recommendFont}));
            //         // canvasStore.dispatch(paintOnCanvas('UPDATE_SOURCE', {source: parseInt(info.templ_id)}));

            //         // emitter.emit('CanvasContentScale', 0);
            //         emitter.emit('CanvasWindowResize');

            //         // 替换快捷编辑信息
            //         emitter.emit('quickEditingReplaceFavTextEmitter');
            //         canvasStore.dispatch(paintOnCanvas("DEL_GROUP_INFO_ALL"));
            //     });
            // });
            // // window.reload()
        }
        assetManager.setPv_new(2942, {
            additional: {
                btyType: tabType,
            },
        });
        e.stopPropagation();
        (e as any)?.nativeEvent.stopImmediatePropagation();
    }

    stopPropagation(e: MouseEvent): void {
        e.stopPropagation();
        (e as any)?.nativeEvent.stopImmediatePropagation();
    }

    onGoPurchaseClick(e: MouseEvent): void {
        assetManager.setPagePv_new(3205);
        window.open('https://818ps.com/dash/firm-intro?&route_id=15995313144085&route=1&after_route=1&origin=ue_team');
        e.stopPropagation();
        (e as any)?.nativeEvent.stopImmediatePropagation();
    }

    onMouseEnterEvent(e: MouseEvent): void {
        assetManager.setPagePv_new(3206);
        e.stopPropagation();
        (e as any)?.nativeEvent.stopImmediatePropagation();
    }

    onMouseLeaveEvent(e: MouseEvent): void {
        assetManager.setPagePv_new(3207);
        e.stopPropagation();
        (e as any)?.nativeEvent.stopImmediatePropagation();
    }

    render(): JSX.Element {
        const {
            userTeamInfo,
            showChooseTeam,
            teamInfo,
            tabType,
            updateTime,
            assetsList,
            showChooseProject,
            userProjectInfo,
            scrollbarHeight,
            userProject,
            isShowTeamVip,
        } = this.state;
        const { userTeamAssetsList } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }); // canvasStore.getState().onCanvasPainted;
        // @ts-ignore
        // eslint-disable-next-line @typescript-eslint/no-use-before-define
        // const assetList = tabType == 2 ? userTeamAssetsList : assetList;
        const myScrollbar = {
            // paddingLeft: '12px',
            // paddingTop: '15px',
            overflowX: 'hidden',
            overflowY: 'auto',
        } as CSSProperties;
        const sizes = [
            {
                columns: 2,
                gutter: 10,
            },
        ];
        return (
            <div className="TeamWorkPanel">
                {/* <div className="company_use">
                    <i className="iconfont icon-shang"></i>
                    <span>VIP会员所有素材可放心商用</span>
                </div> */}

                {/* {!isShowTeamVip && (
                    <div
                        className="teamworkRecharge"
                        onMouseLeave={this.onMouseLeaveEvent.bind(this)}
                        onMouseEnter={this.onMouseEnterEvent.bind(this)}
                        onClick={this.onGoPurchaseClick.bind(this)}
                    >
                        <div className="teamworkRechargeDesc">企业VIP-有版权,敢商用</div>
                        <div className="teamworkRechargeBtn">立即加入</div>
                    </div>
                )} */}

                <div className="teamworkTeamName">
                    <div className="selectTeam" onClick={this.selectTypeTeam.bind(this)}>
                        <i className="iconfont icon-tupianbianjiqizuoceicon_tuandui"></i>

                        <div className="showTeamName" title={userTeamInfo.name}>
                            {userTeamInfo.name}
                        </div>
                        <i className="iconfont icon-xiala"></i>
                        {showChooseTeam && userTeamInfo && teamInfo.length > 1 && (
                            <div className="selections">
                                {teamInfo.map((item, index) => {
                                    return (
                                        <div
                                            className="option"
                                            title={item.name}
                                            onClick={this.changeTeamType.bind(this, item, index)}
                                            key={index}
                                        >
                                            {item.name}
                                        </div>
                                    );
                                })}
                            </div>
                        )}
                    </div>

                    <div className="selectShowProject">
                        <div
                            className={tabType === 1 ? 'materialTabItem active' : 'materialTabItem'}
                            onClick={this.materialTabItemClickEvent.bind(this, 1)}
                        >
                            模板
                        </div>
                        <div
                            className={tabType === 2 ? 'materialTabItem active' : 'materialTabItem'}
                            onClick={this.materialTabItemClickEvent.bind(this, 2)}
                        >
                            素材
                        </div>
                        <div
                            className={tabType === 3 ? 'materialTabItem active' : 'materialTabItem'}
                            onClick={this.materialTabItemClickEvent.bind(this, 3)}
                        >
                            LOGO
                        </div>
                    </div>

                    <div className="selectTeam selectProject" onClick={this.selectTypeProject.bind(this)}>
                        <i className="iconfont icon-qiyeyunpan icon"></i>
                        <div className="showTeamName" title={userProjectInfo.name}>
                            {userProjectInfo.name != '' ? userProjectInfo.name : '默认项目'}
                        </div>
                        <i className="iconfont icon-xiala"></i>
                        {showChooseProject && userProjectInfo.name != '' && userProject.length > 1 && (
                            <div className="selections">
                                {userProject.map((item, index) => {
                                    return (
                                        <div
                                            className="option"
                                            title={item.name}
                                            onClick={this.changeProjectType.bind(this, item)}
                                            key={index}
                                        >
                                            {item.name}
                                        </div>
                                    );
                                })}
                            </div>
                        )}
                    </div>

                    <div className="listShow">
                        <div
                            className="scroll_content_auto"
                            style={myScrollbar}
                            ref={(node) => (this.teamWorkNode = node)}
                            key={'scrollBar' + '-' + updateTime}
                            onScroll={this.wheelMoveEvent.bind(this)}
                        >
                            <div style={{ display: 'inline-block', width: 'calc(100% - 5px)' }} ref="scrollContent">
                                <div className="assetsList">
                                    {assetsList.length > 0 && (
                                        <MasonryInfiniteScroller
                                            loadMore={undefined}
                                            hasMore={false}
                                            sizes={sizes}
                                            threshold={150}
                                            key={'assetsList' + '-' + updateTime}
                                        >
                                            {assetsList.map((item) => {
                                                const assetImgStyle = {
                                                    background: "url('" + item.imgUrl + "')",
                                                    backgroundSize: '151px auto',
                                                    width: '151px',
                                                    height: (151 * item.height) / item.width + 'px',
                                                };

                                                return (
                                                    <div
                                                        className="assetItem"
                                                        onClick={this.assetItemClickEvent.bind(this, item)}
                                                        key={item.id}
                                                    >
                                                        <LazyLoad
                                                            width={151}
                                                            height={(151 * item.height) / item.width}
                                                            offset={200}
                                                        >
                                                            <div className="assetImg" style={assetImgStyle}></div>
                                                        </LazyLoad>
                                                    </div>
                                                );
                                            })}
                                        </MasonryInfiniteScroller>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }
}

export { TeamWorkPanel };
