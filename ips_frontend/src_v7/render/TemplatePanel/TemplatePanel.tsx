import React, { Component } from 'react';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
import LazyLoad from 'react-lazy-load4';
import { EventSubscription } from 'fbemitter';

import { addEventListener, EventListener } from '@v7_utils/AddEventListener';
import { InfoManageHelper, RecommendManageHelper, SearchHelper } from '@v7_logic/StoreLogic';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { IAnyObj, IAsset, ICanvas, IPageInfo, ITransform, IWork } from '@v7_logic/Interface';
import { ETool } from '@v7_logic/Enum';
import { env } from '@editorConfig/env';
import { ErrorBoundaryDecorator } from '@v7_render/ErrorBoundaryHOC';
import { storeDecorator } from '@v7_logic/StoreHOC';
import { IInfoManageState } from '@v7_store/redux/reducers/InfoManage';
import equal from 'fast-deep-equal';
import Masonry from 'react-masonry-css'

// 待替换组件
import { emitter } from '@component/Emitter';
import { getProps, imgHost } from '@component/IPSConfig';
import { assetManager } from '@component/AssetManager';
import { templateFormat } from '@component/TemplateFormat';
import { BigPopupWindow } from '@component/canvas/BigPopupWindow';
import Select from '@component/Select';
import { CanvasPaintedLogic } from '@v7_logic/CanvasPaintedLogic';
import { EditorLogic } from '@v7_logic/EditorLogic';
import { TemplateCanvasLogic } from '@v7_logic/TemplateCanvasLogic';
import { klona as cloneDeep } from 'klona';
import { AssetQrcodeELogic } from '@v7_logic/AssetQrcodeELogic';
import { IPaintOnCanvasState } from '@v7_store/redux/reducers/onCanvasPaintedReducer';
import TemplateITabs from '@v7_render/AssetToolPanel/components/TemplateITabs';
import { isEcommerce, isEcommerceTeam, isUeAndEcommerce } from '@v7_utils/webSource'
import { AssetHelper } from '@v7_logic/AssetHelper';
import { EffectTemplateItem, EffectTemplateLogic, EffectTemplatePanel } from './EffectTemplatePanel';
import { EffectTemplateFloor } from './EffectTemplatePanel/EffectTemplatePanel';
import PreviewPanel from './PreviewPanel';
import { Loading } from '../Ui/Loading/Loading';
import { Skeleton } from 'antd';
import { applyToPoint, rotateDEG } from 'transformation-matrix';
import { DownloadPopup } from '@src/userComponentV6.0/InfoBar';

const defaultFun = () => {/* do nothing */ }

export function DisplayTemplate(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.TEMPLATE, nav: ETool.TEMPLATE};
}
interface TemplateReplacePopup_propsStruct {
    templateId: string;
    pageIndex: number;
    addProps: IAnyObj;
    serchclickType: boolean;
    rt_canvas_render_mode: IPaintOnCanvasState['rt_canvas_render_mode'];
};

class TemplateReplacePopup extends Component<TemplateReplacePopup_propsStruct> {
    searchInputKeyDownListener: EventListener;
    isSaveing: boolean;
    constructor(props: TemplateReplacePopup_propsStruct) {
        super(props);

        const { work, pageInfo, isEffectTemplate } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }); // canvasStore.getState().onCanvasPainted;
        if (!(work.pages[pageInfo.pageNow].assets.length > 0) || (isEffectTemplate)) {
            this.trueBtnClickEvent();
            emitter.emit('popupClose');
        }
    }

    calcRotateRect = (size: { x: number; y: number; w: number; h: number }, rotate: number) => {
        const { x, y, w, h } = size;
        const tl = { x, y };
        const tr = { x: x + w, y };
        const bl = { x, y: y + h };
        const br = { x: x + w, y: y + h };
        const matrix = rotateDEG(rotate, x + w / 2, y + h / 2);
        const newTl = applyToPoint(matrix, tl);
        const newTr = applyToPoint(matrix, tr);
        const newBl = applyToPoint(matrix, bl);
        const newBr = applyToPoint(matrix, br);
        const t: number = Math.min(newTl.y, newTr.y, newBl.y, newBr.y);
        const b: number = Math.max(newTl.y, newTr.y, newBl.y, newBr.y);
        const l: number = Math.min(newTl.x, newTr.x, newBl.x, newBr.x);
        const r: number = Math.max(newTl.x, newTr.x, newBl.x, newBr.x);
        return {
            x: l,
            y: t,
            w: r - l,
            h: b - t,
        };
    };
        
    calcContentSize(assets: IAsset[]) {
        let left: number;
        let right: number;
        let top: number;
        let bottom: number;
        assets.forEach((asset) => {
            const [ox, oy, ow, oh] = [
                asset.transform.posX,
                asset.transform.posY,
                'container' in asset.attribute
                    ? (asset.attribute.container?.width ?? asset.attribute.width)
                    : asset.attribute.width,
                'container' in asset.attribute
                    ? (asset.attribute.container?.height ?? asset.attribute.height)
                    : asset.attribute.height,
            ];
            const { x, y, w, h } = this.calcRotateRect({ x: ox, y: oy, w: ow, h: oh }, asset.transform.rotate);
            left = left === undefined ? x : Math.min(left, x);
            top = top === undefined ? y : Math.min(top, y);
            right = right === undefined ? x + w : Math.max(right, x + w);
            bottom = bottom === undefined ? y + h : Math.max(bottom, y + h);
        });
        return {
            left: left || 0,
            top: top || 0,
            right: right || 0,
            bottom: bottom || 0,
            width: (right || 0) - (left || 0),
            height: (bottom || 0) - (top || 0),
        };
    }

    async addLimit(picId: string, info) {
        try {
            const data = await assetManager.getDownloadNumInfo(picId)
            const resultData = await data.json();
            if (resultData.stat === 1) {
                if (resultData.msg.checkAuth === 0) {
                        const windowInfo = {
                            windowContent: (
                                <DownloadPopup
                                    isShowCloseInternal={true}
                                    isDownloadZero={true}
                                    key={new Date().getTime()}
                                    className="DownloadPopup"
                                    showFlag={3}
                                    info={info}
                                    actionText={"添加"}
                                />
                            ),
                            popupWidth: 400,
                            popupHeight: 330,
                            style: {
                                padding: '20px',
                                borderRadius: '14px',
                                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            },

                            popupTitleBarStyle: {
                                width: 0,
                                height: 0,
                                display: 'none',
                            },
                            popupBodyStyle: {
                                padding: 0,
                            },
                        };
                        emitter.emit('popupWindow', windowInfo);
                }
                return resultData.msg.checkAuth;
            }
            return 0;
        } catch (error) {
            
        }
    }

    /**
     * 替换页面按钮
     */
    trueBtnClickEvent(): void {
        const { isDesigner, pageInfo, rt_editor_type, work, canvas, isEffectTemplate, user, pageAttr } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas
        });
        const { info: oldInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
            store_name: storeAdapter.store_names.InfoManage
        });

        const { templateId, pageIndex, rt_canvas_render_mode } = this.props;
        const { oldQrcodeReplace } = AssetQrcodeELogic()
        const isBoard = rt_canvas_render_mode === 'board' || pageAttr.pageInfo?.some((p) => p.type === 'board');
        if (parseInt(templateId) > 0) {
            assetManager.getTemplate(templateId, 0, '', '', 0, 'replaceTemplate').then((data) => {
                data.json().then(async (resultData) => {
                    if (resultData.stat != 1) {
                        return false;
                    }
                    if (isBoard) {
                        const checkAuth = await this.addLimit(templateId, resultData.info);
                        if (!checkAuth) {
                            return;
                        }
                    } 
                    /* 图片二维码替换 */
                    resultData = oldQrcodeReplace(resultData)

                    const info = resultData.info,
                        recommendFont = resultData.recommendFont
                    const preview = resultData.preview;
                    resultData = resultData.doc
                    resultData = templateFormat.getFormat(resultData)
                    let replaceCanvas = resultData.canvas

                    // if (newState.isDesigner) {
                    //     assetClass.updateCanvasMap(action)
                    // }  else {
                    //     assetClass.updateCanvaseTemplateMap(action);
                    // }
                    let tempWork = cloneDeep(work);
                    // 如果是效果模板
                    if (isEffectTemplate && !isDesigner) {
                        replaceCanvas = EffectTemplateLogic.handleEffectTemplateReplace({
                            info,
                            doc: resultData,
                            tempWork,
                            canvas: replaceCanvas
                        })
                    } else {
                        if (isDesigner) {
                            tempWork = resultData.work;
                        } else {
                            if (pageIndex > -1) {
                                if (rt_canvas_render_mode === 'board') {
                                    const newAssets = resultData.work.pages[pageIndex].assets
                                    const contentSize = this.calcContentSize(work.pages[pageInfo.pageNow].assets)
                                    const dataSize = this.calcContentSize(newAssets)
                                    // 将 dataSize 对应的数据整体偏移到 contentSize.right 的右边
                                    const offsetX = contentSize.right - dataSize.left + 200;
                                    const offsetY = contentSize.height === 0 ? 0 : contentSize.top + contentSize.height / 2 - dataSize.top - dataSize.height / 2;
                                    const len = tempWork.pages[pageInfo.pageNow].assets.length
                                    newAssets.forEach((a: IAsset, i: number) => {
                                        a.transform.posX += offsetX;
                                        a.transform.posY += offsetY;
                                        a.meta.className = a.meta.type + ++work.nameSalt.salt + (user.userId || '')
                                        a.meta.index = len + i;
                                    })
                                    tempWork.pages[pageInfo.pageNow].assets = tempWork.pages[pageInfo.pageNow].assets.concat(newAssets);
                                    // storeAdapter.dispatch({
                                    //     store_name: storeAdapter.store_names.paintOnCanvas,
                                    //     fun_name: 'BOARD_ADD_TEMPLATE_ASSETS',
                                    //     params: [
                                    //         {
                                    //             type: 'addAssets',
                                    //             params: {
                                    //                 assets: newAssets,
                                    //                 autoSave: undefined,
                                    //             },
                                    //         },
                                    //     ],
                                    //     isAutoSave: true,
                                    // });
                                    // 将新内容居中显示
                                    // TemplateCanvasLogic.setCanvasPositionAndSize({
                                    //     x: -contentSize.right * canvas.scale + 120,
                                    //     y: (contentSize.height === 0 ? 0 : -(dataSize.top + offsetY) * canvas.scale) + 120,
                                    // })
                                    // 白板不更新画布宽高，会影响搜索结果
                                    replaceCanvas.width = canvas.width;
                                    replaceCanvas.height = canvas.height;
                                    replaceCanvas.x = -contentSize.right * canvas.scale + 120 / canvas.scale;
                                    replaceCanvas.y = (contentSize.height === 0 ? 0 : -(dataSize.top + offsetY) * canvas.scale) + 120;
                                } else {
                                    tempWork.pages[pageInfo.pageNow] = cloneDeep(resultData.work.pages[pageIndex]);
                                }
                            } else {
                                tempWork.pages = cloneDeep(resultData.work.pages)
                            }
                        }
                    }
                    CanvasPaintedLogic.replaceCanvasMap({
                        picId: templateId,
                        canvas: replaceCanvas,
                        work: tempWork,
                        preview: preview
                    })
                    const canvasSizeChange =replaceCanvas.width !== canvas.width || replaceCanvas.height !== canvas.height
                    rt_canvas_render_mode !== 'board' && canvasSizeChange && TemplateCanvasLogic.canvasResizeByWindow()
                    // canvasStore.dispatch(paintOnCanvas('UPDATE_CANVAS_MAP_REPLACE', {
                    //     picId: templateId,
                    //     canvas: resultData.canvas,
                    //     work: resultData.work,
                    //     subRecord: {
                    //         origin: this.props.addProps ? this.props.addProps.origin : ''
                    //     },
                    //     preview:preview
                    // }))

                    if (rt_canvas_render_mode !== 'board') {
                        emitter.emit('changeTemplate', resultData.work.pages[pageIndex < 0 ? 0 : pageIndex].assets)
                    }

                    const { toolPanel } = storeAdapter.getStore({
                        store_name: storeAdapter.store_names.paintOnCanvas,
                    });
                    Object.assign(toolPanel, {
                        asset: '',
                        assets: ''
                    });
                    const realOldInfo = oldInfo as any
                    let title = realOldInfo.title
                    if(!realOldInfo.titleEdited) {
                        title = info.title
                    }
                    InfoManageHelper.updateInfo({
                        ...info,
                        title
                    })
                    // canvasStore.dispatch(infoManage('UPDATE_INFO', info))
                    emitter.emit('quickEditingUpdateState');

                    rt_canvas_render_mode !== 'board' && CanvasPaintedLogic.updateCommonCanvasCenter()
                    emitter.emit('NavLayoutupdateState');
                    RecommendManageHelper.updateRecommendFont({ recommendFont: recommendFont })
                    // canvasStore.dispatch(recommendManage('UPDATE_RECOMMEND_FONT', {recommendFont: recommendFont}));
                    // canvasStore.dispatch(paintOnCanvas('UPDATE_SOURCE', {source: parseInt(info.templ_id)}));

                    // emitter.emit('CanvasContentScale', 0);
                    if (rt_editor_type !== 'canvas' || (rt_canvas_render_mode !== 'board' && canvasSizeChange)) {
                        TemplateCanvasLogic.canvasResizeByWindow();
                        emitter.emit('CanvasWindowResize');
                    }
                    // 替换快捷编辑信息
                    emitter.emit('quickEditingReplaceFavTextEmitter');
                    // EditorLogic.delGroupInfoAll()
                    emitter.emit('CanvasSizeCloseEmitter'); // 关掉选择尺寸
                });
            });
            // 点击更换模版时弹出提示窗中的【替换页面】人数和次数
            assetManager.setPv_new(4254)

            if (this.props.serchclickType) {
                assetManager.setPv_new(994);
            } else {
                assetManager.setPv_new(34);
            }
            assetManager.setTemplateClick(templateId);

        }

        emitter.emit('popupClose');

        if (oldInfo.is_second) {
            setTimeout(() => {
                this.saveClickEvent();
                emitter.emit('updateTemplateListData', templateId);
            }, 1000)
        }
    }


    saveClickEvent() {
        // const {user} = canvasStore.getState().onCanvasPainted;

        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }); // canvasStore.getState().onCanvasPainted;
        if (!(parseInt(user.userId) > 0)) {
            emitter.emit('LoginPanelShow', "saveTempl");
            return;
        }
        if (this.isSaveing) {
            return;
        }
        // canvasStore.dispatch(paintOnCanvas('CLICK_SAVE_BTN'));
        /*编辑器判断*/
        const { isDesigner } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }); // canvasStore.getState().onCanvasPainted;

        if (isDesigner) {
            emitter.emit('autoSaveTempl', '', 1, { iszdbc: true });
        } else {
            emitter.emit('autoSaveTempl', '', 1)
        }
    }

    /**
     * 新增页面按钮
     */
    falseBtnClickEvent(): void {
        const { templateId, pageIndex, rt_canvas_render_mode } = this.props;
        const { isEffectTemplate, isDesigner, canvas, pageAttr } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        
        const isBoard = rt_canvas_render_mode === 'board' || pageAttr.pageInfo?.some((p) => p.type === 'board');
        if (parseInt(templateId) > 0) {
            assetManager.getTemplate(templateId, 0, '', '', 0, 'addNew').then((data) => {
                data.json().then(async (resultData) => {
                    if (resultData.stat != 1) {
                        return false;
                    }
                    if (isBoard) {
                        const checkAuth = await this.addLimit(templateId, resultData.info);
                        if (!checkAuth) {
                            return;
                        }
                    } 
                    const info = resultData.info,
                        recommendFont = resultData.recommendFont;
                    const isPPTTemplate = info.template_type === 3;
                    const preview = resultData.preview;
                    resultData = resultData.doc
                    resultData = templateFormat.getFormat(resultData)
                    let tempWork = cloneDeep(resultData.work)
                    if (isEffectTemplate && !isDesigner) {
                        EffectTemplateLogic.replaceEffectTemplate({
                            curCanvas: canvas,
                            replaceAsset: null,
                            tempCurWork: tempWork.pages[pageIndex],
                            tempCanvas: resultData.canvas
                        })
                    }
                    // ppt 获取pageIndex的页面
                    const pptTempWork = cloneDeep(tempWork.pages[pageIndex])
                    if (isPPTTemplate) {
                        tempWork = {...tempWork, pages: [pptTempWork] }
                    }
                    CanvasPaintedLogic.addNewCanvasMap({
                        picId: templateId,
                        canvas: resultData.canvas,
                        work: tempWork,
                        preview: preview,
                    });
                    emitter.emit('changeTemplate', resultData.work.pages[pageIndex].assets)

                    // 更换模板后 显示导航栏侧边栏 关闭模板侧边栏
                    emitter.emit('emitterFooterMorePage', true)

                    const { toolPanel } = storeAdapter.getStore({
                        store_name: storeAdapter.store_names.paintOnCanvas,
                    });
                    Object.assign(toolPanel, {
                        asset: '',
                        assets: ''
                    });
                    InfoManageHelper.updateInfo(info);
                    emitter.emit('NavLayoutupdateState');
                    RecommendManageHelper.updateRecommendFont({ recommendFont: recommendFont })
                    emitter.emit('CanvasWindowResize');

                });
            });
            // 记录新增页面操作，方便undo
            storeAdapter.dispatch({
                store_name: storeAdapter.store_names.paintOnCanvas,
                fun_name: 'ADD_PAGE',
                params: [
                    {
                        type: 'updateCanvas',
                        params: {},
                    },
                ],
            });
            // 点击更换模版时弹出提示窗中的【添加新页面】人数和次数
            assetManager.setPv_new(4253)

            assetManager.setTemplateClick(templateId);

        }
        emitter.emit('popupClose');
        emitter.emit('CanvasSizeCloseEmitter'); // 关掉选择尺寸
    }

    // componentWillMount(){
    //     const {work, pageInfo} = canvasStore.getState().onCanvasPainted;
    //     if( !(work.pages[pageInfo.pageNow].assets.length > 0) ){
    //         this.trueBtnClickEvent();
    //         emitter.emit('popupClose');
    //     }
    // }

    render() {
        const { pageIndex, rt_canvas_render_mode } = this.props
        return (
            <div className="templateReplacePopup">
                {pageIndex > -1 ? <>
                    <div className="templateReplaceTitle">
                        <i className="iconfont icon-bianjiqi-danchuang-xinxitishi"></i>
                        <span>将模板添加为新页面</span>
                    </div>
                    <div className="templateReplaceTip">使用模板后，模板内容将应用至页面</div>
                    <div className="btnArea">
                        <div className="trueBtn" onClick={this.trueBtnClickEvent.bind(this)}>{this.props.rt_canvas_render_mode ? '加入画布' : '替换页面'}</div>
                        <div className="falseBtn" onClick={this.falseBtnClickEvent.bind(this)} >添加新页面</div>
                    </div>
                </> : <>
                    <div className="templateReplaceTitle">
                        <i className="iconfont icon-bianjiqi-danchuang-xinxitishi"></i>
                        <span>用此模板替换当前页面</span></div>
                    <div className="templateReplaceTip">使用模板后，所有当前页面的内容将会被替换</div>
                    <div className="btnArea">
                        <div className="trueBtn" onClick={() => emitter.emit('popupClose')}>取消</div>
                        <div className="falseBtn" onClick={this.trueBtnClickEvent.bind(this)} >替换所有页面</div>
                    </div>
                </>}
            </div>
        );
    }
}

let isInit = false;
let isInitEnd = false;

export const templateTag = {
    4: '免费',
    2: 'VIP',
    3: '个人',
    1: '企业'
}
export const hoverTemplateTag = {
    4: '免费下载',
    2: 'VIP专享',
    3: '个人商用',
    1: '企业商用'
}
export const templateTagStyle = {
    1: { background: 'linear-gradient(132deg, #353533 0%, #1B1D1C 100%)', color: '#ECDA97' },
    2: { background: 'linear-gradient(132deg, #FE7900 0%, #F64500 100%)' },
    3: { background: 'linear-gradient(312deg, #E8BA70 0%, #FCE5B0 100%)', color: '#7A4F05' }
}

const templateClass = {
    template3D: {
        _classId: '1630_0_0_0',
    },
    templateIns: {
        _classId: '10_1720_0_0',
    },
    templateCartoon: {
        _classId: '10_1722_0_0',
    },
    templateCharacters: {
        _classId: '10_1711_0_0',
    },
    templateEstate: {
        _classId: '1747_0_0_0',
    },
    templateBank: {
        _classId: '1750_0_0_0',
    },
    templateBeauty: {
        _classId: '1755_0_0_0',

    },
    templateFinance: {
        _classId: '1763_0_0_0',
    },
} as const;

const templateConf = {
    'template3D': '3D海报',
    'templateIns': '',
    'templateCartoon': '',
    'templateCharacters': '',
    'templateEstate': '地产',
    'templateBank': '银行',
    'templateBeauty': '美业',
    'templateFinance': '理财',
} as const;

interface TemplatePanel_propsStruct {
    isActive?: boolean;
    info?: IInfoManageState['info'];
    canvas?: IPaintOnCanvasState['canvas'];
    type:
    | 'template'
    | 'template3D'
    | 'templateIns'
    | 'templateCartoon'
    | 'templateCharacters'
    | 'templateEstate'
    | 'templateBank'
    | 'templateBeauty'
    | 'templateFinance';
    rt_canvas_render_mode?: IPaintOnCanvasState['rt_canvas_render_mode'];
};
interface TemplatePanel_stateStruct {
    listLoading: boolean,
    assetList: IAsset[],
    wheelAdd: boolean,
    lastPage: boolean,
    templateList: IAnyObj[],
    isDefaultShow: boolean,
    isDropdownBox: boolean,
    inputValue: string,
    placeholder: string,
    classInfo: IAnyObj,
    subClassList: IAnyObj[],//子分类
    templateTab: number,
    formatClassList: IAnyObj[],
    templateListRefresh: boolean,
    formatId: string,
    tabsList: any,
    isShowcreen: boolean,
    classSearchid: string,
    classSearchTitle: string;
    targetTemplate: IAnyObj,
    activeTab: 'default' | 'class_filter',
};

const tgsCanvasLoad = false
@ErrorBoundaryDecorator()
@storeDecorator((state: { infoManageStore: IInfoManageState, onCanvasPainted: IPaintOnCanvasState }) => {
    return {
        info: state.infoManageStore.info,
        canvas: state.onCanvasPainted.canvas,
        rt_canvas_render_mode: state.onCanvasPainted.rt_canvas_render_mode
    };
})
class TemplatePanel extends Component<TemplatePanel_propsStruct, TemplatePanel_stateStruct> {
    updateNum: number;
    serchclickType: boolean;
    seachNowInput: string;
    tagData: IAnyObj[];
    TemplateNode: HTMLDivElement;
    updateTemplateListListenner: EventSubscription;
    addTemplateListListener: EventSubscription;
    moreBtnClickEmitterListener: EventSubscription;
    tgsAssetLoadEventListener: EventSubscription;
    updateTemplateListDataListener: EventSubscription;
    refreshTemplateListDataListener: EventSubscription;
    listMoreBtn = React.createRef<HTMLDivElement>();
    templateFavBtnDom: {
        [key: string]: HTMLDivElement
    } = {};
    firstBlankSource: number;
    moreBtn: number;
    searchInputKeyDownListener: EventListener;
    searchInput = React.createRef<HTMLInputElement>();
    urlParams: any

    constructor(props: TemplatePanel_propsStruct) {
        super(props);
        const templateList: IAnyObj[] = [];
        const { search } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }); // canvasStore.getState().onCanvasPainted;
        this.state = {
            listLoading: false,
            assetList: [],
            wheelAdd: false,
            lastPage: false,
            templateList: templateList,
            isDefaultShow: true,
            isDropdownBox: false,
            inputValue: '',
            placeholder: '搜索模板',
            classInfo: {},//{kid_1:--, kid_2:--}
            subClassList: [],//子分类
            // 模块选择字段
            templateTab: 1,
            // 板式下拉列表
            formatClassList: [],
            templateListRefresh: true,
            formatId: null,
            tabsList: [],
            isShowcreen: false,
            classSearchid: '', //是否tabs通过分类帅选id
            classSearchTitle: '',
            targetTemplate: undefined,
            activeTab: 'default',
        };

        this.searchBtnDownEvent = this.searchBtnDownEvent.bind(this)

        this.updateNum = 1;
        this.wheelMoveEvent = this.wheelMoveEvent.bind(this);

        this.serchclickType = false;
        this.seachNowInput = '';
        this.tagData = [];//分类信息

        this.urlParams = this.urlParametric(window.location.search)
        this.getAllKindInfo();
        this.addTemplateList();
        this.updateTemplateListEmitter();
        // this.updateTemplateList();
        this.templateItemClickEmitter();
        this.moreBtnClickEmitter();
        this.refreshTemplateListData();
    }


    // 获取模板tabs分类信息
    getTemplateTabsFn(): void {
        assetManager.getTemplateTabs().then(data => {
            data.json().then(resultData => {
                if (resultData.stat == 1) {
                    this.setState({
                        tabsList: resultData.data.data
                    })
                }
            });
        })
    }
    /**
     * 获取所有分类信息
     */
    getAllKindInfo(): void {
        const _this = this;
        assetManager.getKindTagAll_v2().then(data => {
            data.json().then(resultData => {
                if (resultData.stat == 1) {
                    _this.tagData = resultData.msg;
                }
            });
        });
    }

    getFormatClass(): void {
        assetManager.getFormatClass().then((data) => {
            data.json().then((resultData) => {
                if (resultData.stat === 1) {
                    const formatClassList: IAnyObj[] = []
                    Object.keys(resultData.data).forEach(key => {
                        formatClassList.push({
                            id: key,
                            label: resultData.data[key]
                        })
                    })
                    this.setState({ formatClassList })
                }
            });
        })
    }

    // 重置分页信息
    resetPageInfo(): void {
        if (this.TemplateNode) {
            this.TemplateNode.scrollTop = 0
        }
        this.setState({
            templateList: [],
            wheelAdd: true,
            lastPage: false,
        })
        SearchHelper.updateTemplatePage({
            search: {
                templatePage: 1,
            },
        });
        // canvasStore.dispatch(searchManage('UPDATE_TEMPLATE_PAGE', {
        //     templatePage: 1
        // }))
    }

    // 用户编辑器模板搜索左侧tabs分类筛选
    onChangeTemplateTabs = (class_id: string, title: string) => {
        // 设置开启tabs搜索
        this.setState({
            classSearchid: class_id,
            classSearchTitle: title,
            isShowcreen: false,
            activeTab: 'class_filter',
        }, () => {
            const params: IAnyObj = {
                kw: title,
                class_id: class_id,
                esType: this.state.templateTab
            }
            this.getNewTemplateList(params)
        })
        assetManager.setPv_new(9045, {
            additional: {s0: class_id, s1: title }
        });
    }
    // 切换tab
    changeTemplateTab(templateTab: number): void {
        // 重复点击过滤 防止点击过快
        if (templateTab === this.state.templateTab || this.state.listLoading) {
            return
        }
        // 切换tab后重置templatePage
        this.resetPageInfo()

        const { formatId } = this.state;
        // 切换tab时清空搜索框
        this.setState({
            inputValue: '',
            formatId: null
        })
        if (templateTab === 2) {
            // （图片编辑器  二次创作）点击高通过率模板按钮埋点
            assetManager.setPv_new(3483);
        }
        this.setState(({
            templateTab
        }))
        const params: IAnyObj = {
            esType: templateTab
        }
        if (formatId && formatId !== 'all') {
            params.formatId = formatId
        }
        this.getNewTemplateList(params)
    }

    handleTemplateSelect(data: IAnyObj): void {
        const { inputValue, templateTab } = this.state;
        this.resetPageInfo()
        this.setState({
            formatId: data.id
        })
        // （图片编辑器  二次创作）版式标签点击次数 埋点
        const params: IAnyObj = { kw: inputValue, esType: templateTab }

        if (data.id !== 'all') {
            assetManager.setPv_new(3484, { additional: { s1: data.id } });
            params.formatId = data.id
        }

        this.getNewTemplateList(params)
    }

    getTemplateNum(): void {
        const th = this;
        const { type } = this.props
        if (type === 'template') {
            // assetManager.getTemplateNum().then(data => {
            //     data.json().then(templateRes => {
            //         if (templateRes.stat == 1) {
                        th.setState({
                            placeholder: `搜索模板`
                        })
            //         }
            //     });
            // });
        } else {
            th.setState({
                placeholder: `搜索${templateConf[type]}相关模板`
            })
        }

    }

    getNewTemplateList(params: IAnyObj = {}): void {
        const { classInfo, classSearchid } = this.state;
        const { page = 1, ratio = 0, formatId, esType, kw = '', class_id = '' } = params

        const { type } = this.props

        // const _classId = `${classInfo['kid_1'] || 0}_${classInfo['kid_2'] || 0}_0`;
        const th = this;

        this.updateNum = this.updateNum + 1;

        const { search } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.Search
        });

        const { info } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage,
        });

        const { isDesigner, canvas } = storeAdapter.getStore({ store_name: storeAdapter.store_names.paintOnCanvas })

        let classId1, _classId: string, classId2;
        let keyWords: string;
        if (!isDesigner) {
            if (type === 'template') {
                classId1 = `${info.class_id[0] || 0}_${info.class_id[1] || 0}_0`
                classId2 = info.class_id.length > 0 ? info.class_id.join('_') : classId1
                _classId = classId2;
                keyWords = kw ? kw : info.search_key_word
            } else if (templateClass[type]) {
                _classId = templateClass[type]._classId;
                keyWords = (kw || kw === '') ? kw : info.search_key_word;
            } else {
                keyWords = kw
            }
        } else {
            keyWords = kw
            if (type === 'template') {
                _classId = `${classInfo['kid_1'] || 0}_${classInfo['kid_2'] || 0}_0`;
            } else if (templateClass[type]) {
                _classId = templateClass[type]._classId;
            }
        }
        let templateK1 = 0;
        let templateK2 = 0;
        if (info.is_second || !kw) {
            templateK1 = info.kid_1;
            templateK2 = info.kid_2;
        }

        if (page === 1) {
            this.setState({
                listLoading: true
            })
        }

        const otherParams: IAnyObj = {
            esType
        }
        if (formatId !== 'all') {
            otherParams.formatId = formatId
        }

        const urlParams = this.urlParams;
        const isNew: boolean = (urlParams && (urlParams.origin == 'customize_index' || urlParams.origin == 'fixed_blank_index' || urlParams.origin == "home_create_quick")) ? true : false;
        const classId = isNew ? (urlParams.class_id): _classId

        if (!!classSearchid) {
            this.getTemplateTabsListFn(page, classSearchid, isNew ? 0 : canvas.width, isNew ? 0 : canvas.height,)
        } else {
            if (type === 'template') {
                (assetManager.getTemplateList(keyWords as any, templateK1, templateK2, page, ratio, 0, 0, 0, '', 0, classId, otherParams as any, isNew ? 0 : canvas.width, isNew ? 0 : canvas.height, info.ratio)
                    .then((data) => {
                        data.json().then((resultData) => {
                            if (typeof resultData.stat != 'undefined' && type === 'template' && (resultData.stat == '-10' || resultData.stat == '-1')) {
                                th.setState({
                                    lastPage: true,
                                })
                                return
                            }
                            let templateList = []
                            if (page == 1) {
                                templateList = resultData.msg.map((item: IAnyObj) => ({ ...item, pageIndex: page, templ_purpose: resultData.templ_purpose[item.id] }))
                            } else {
                                templateList = [...this.state.templateList, ...resultData.msg.map((item: IAnyObj) => ({ ...item, pageIndex: page, templ_purpose: resultData.templ_purpose[item.id] }))]
                            }
                            SearchHelper.updateTemplatePage({
                                search: {
                                    templatePage: search.templatePage + 1,
                                },
                            });
                            // canvasStore.dispatch(searchManage('UPDATE_TEMPLATE_PAGE', {
                            //     templatePage: search.templatePage + 1
                            // }))      
                            th.setState({
                                templateList,
                                wheelAdd: true,
                                lastPage: false,
                            })
                        });
                    }).finally(() => {
                        th.setState({
                            listLoading: false
                        })
                    }))
            } else {
                this.getOtherTemplateList(keyWords, page, isNew ? urlParams.class_id : _classId, templateConf[type], isNew ? 0 : canvas.width, isNew ? 0 : canvas.height,)
            }
        }
    }

    getOtherTemplateList = (keyWords: string, page: number, id: string, conf: string, width?: string, height?: string) => {
        const th = this;
        const { search } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.Search
        });

        this.setState({
            listLoading: true
        })
        assetManager.getOtherTemplateList(keyWords, page, id, width, height, conf)
            .then((data) => {
                data.json().then((resultData) => {
                    if (typeof resultData.stat != 'undefined' && this.props.type !== 'template' && (resultData.stat == '-10' || resultData.stat == '-1')) {
                        th.setState({
                            lastPage: true,
                        })
                    } else {
                        let templateList = []
                        if (page == 1) {
                            templateList = resultData.msg.map((item: IAnyObj) => ({ ...item, pageIndex: page, templ_purpose: resultData.templ_purpose[item.id] }))
                        } else {
                            templateList = [...this.state.templateList, ...resultData.msg.map((item: IAnyObj) => ({ ...item, pageIndex: page, templ_purpose: resultData.templ_purpose[item.id] }))]
                        }
                        SearchHelper.updateTemplatePage({
                            search: {
                                templatePage: search.templatePage + 1,
                            },
                        });
                        th.setState({
                            templateList,
                            wheelAdd: true,
                            lastPage: false,
                        })
                    }

                    //此接口列表数据为空 才返回data空数组 
                    if (resultData.stat == '-1' && resultData.data.length == 0) {
                        th.setState({
                            lastPage: true,
                        })
                        // this.getOtherTemplateList(keyWords, page, id, templateConf[this.props.type])
                    }
                });
            }).finally(() => {
                th.setState({
                    listLoading: false
                })
            })
    }

    getTemplateTabsListFn = (page: number, class_id: string, width?: string, height?: string) => {
        const th = this;
        const { search } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.Search
        });

        this.setState({
            listLoading: true
        })
        assetManager.getTemplateTabsList(page, class_id, width, height)
            .then((data) => {
                data.json().then((resultData) => {
                    if (typeof resultData.stat != 'undefined' && this.props.type !== 'template' && (resultData.stat == '-10' || resultData.stat == '-1')) {
                        th.setState({
                            lastPage: true,
                        })
                    } else {
                        let templateList = []
                        if (page == 1) {
                            templateList = resultData.msg.map((item: IAnyObj) => ({ ...item, pageIndex: page, templ_purpose: resultData.templ_purpose[item.id] }))
                        } else {
                            templateList = [...this.state.templateList, ...resultData.msg.map((item: IAnyObj) => ({ ...item, pageIndex: page, templ_purpose: resultData.templ_purpose[item.id] }))]
                        }
                        SearchHelper.updateTemplatePage({
                            search: {
                                templatePage: search.templatePage + 1,
                            },
                        });
                        th.setState({
                            templateList,
                            wheelAdd: true,
                            lastPage: false,
                        })
                    }

                });
            }).finally(() => {
                th.setState({
                    listLoading: false
                })
            })
    }
    /**
     * 更新模板列表
     */
    updateTemplateListEmitter(): void {

        this.updateTemplateListListenner = emitter.addListener('updateTemplateList', (noEmpty: boolean) => {
            this.updateTemplateList(false, noEmpty);
        })
    }

    updateTemplateList(isInit?: boolean, noEmpty?: boolean): void {
        const { search, isDesigner, canvas, isEffectTemplate } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }); // canvasStore.getState().onCanvasPainted;
        const { info, type } = this.props;
        const oldList = this.state.templateList;
        this.setState({
            wheelAdd: false,
            templateList: noEmpty ? oldList : []
        })

        let class_id: any;
        if (!isDesigner) {
            const { info } = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
                store_name: storeAdapter.store_names.InfoManage,
            });
            if (type === 'template') {
                class_id = `${info.class_id[0] || 0}_${info.class_id[1] || 0}_0`;
                class_id = info.class_id.length > 0 ? info.class_id.join('_') : class_id;
            } else if (templateClass[type]) {
                class_id = templateClass[type]._classId;
            }
            if (!search.templateWord || search.templateWord === 'undefined') {
                search.templateWord = info.search_key_word
            }
        } else {
            if (type === 'template') {
                class_id = '0_0_0'
            } else if (templateClass[type]) {
                class_id = templateClass[type]._classId;
            }
        }

        const otherParams: IAnyObj = {
            formatId: null, esType: null
        }
        const urlParams = this.urlParams;

        const isNew: boolean = (urlParams && (urlParams.origin == 'customize_index' || urlParams.origin == 'fixed_blank_index' || urlParams.origin == 'home_recommend_edit' || urlParams.user_asset_id || urlParams.class_recommend)) ? true : false
        // 非新建，需要获取模版信息才能去请求模版列表,如果id不存在，不请求
        if (!isNew && (urlParams.picId || urlParams.upicId && urlParams.paperId) && !info.id) {
            return;
        }
        if (
            isInit && 
            urlParams.picId !== '5925767' && // 5925767 新建白板专用 id
            !search.templateWord &&
            // (Number.parseInt(urlParams.picId) > 0 || Number.parseInt(urlParams.upicId) > 0)
            !((!urlParams.picId && urlParams.upicId) || (urlParams.picId && urlParams.upicId) || (!urlParams.picId && !urlParams.upicId)) && !urlParams.paperId
        ) {
            return;
        }
        if (isInit && templateConf[type]) {
            search.templateWord = '';
        }
        try {
            // 跳转路径携带kw
            const urlKw = urlParams.keyword ? decodeURI(urlParams.keyword) : '';
            if (isInit && urlKw) {
                search.templateWord = urlKw;
            }
        } catch (e) {
            search.templateWord = '';  // 防止出现乱码的情况，报错。。。。。。。。。。。。。。。。。
        }
        if (urlParams.user_asset_id) {
            search.templateWord = '';
        };
        if ((info.template_type === 3 || location.href.includes('mode=ppt')) && (!class_id || class_id === '0_0_0')) {
            // 主要处理新建进入的 ppt 默认搜索没有 ppt
            class_id = '290'
        }

        this.setState({
            listLoading: true
        })
        if (isEffectTemplate && !isDesigner) {
            assetManager.getEffectTemplateList().then(data => {
                data.json().then(res => {
                    if (res.code == 1) {
                        this.setState({
                            templateList: res.data,
                            wheelAdd: true,
                            lastPage: false,
                            isDefaultShow: true,
                            listLoading: false,
                        })
                    }

                })
            })
        } else {
            (type === 'template' ? assetManager.getTemplateList(search.templateWord, info.kid_1, info.kid_2, 1, search.templateRatioId, 0, 0, 0, '', 0, isNew ? urlParams.class_id : class_id, otherParams as any, isNew ? 0 : canvas.width, isNew ? 0 : canvas.height, info.ratio)
                : assetManager.getOtherTemplateList(search.templateWord, 1, class_id, isNew ? 0 : canvas.width, isNew ? 0 : canvas.height, templateConf[type]))
                .then((data) => {
                    data.json().then((resultData) => {
                        this.updateNum = this.updateNum + 1;
                        if (resultData.stat == 1) {
                            isInitEnd = true;
                            const templateList = resultData.msg;
                            templateList.forEach((item: IAnyObj) => {
                                item.templ_purpose = resultData.templ_purpose[item.id];
                            })
                            this.setState({
                                templateList: templateList,
                                wheelAdd: true,
                                lastPage: false,
                                isDefaultShow: true,
                                listLoading: false,
                            })

                        } else if (resultData.stat == -1) {
                            if (type !== 'template') {
                                this.setState({
                                    inputValue: ''
                                })
                            }
                            if (!info.is_second) {
                                (type === 'template' ? assetManager.getTemplateList(search.templateWord = '', 0, 0, 1, -1, 0, 0, 0, '', 0, isNew ? urlParams.class_id : class_id, otherParams as any, isNew ? 0 : canvas.width, isNew ? 0 : canvas.height, info.ratio)
                                    : assetManager.getOtherTemplateList(search.templateWord = '', 1, isNew ? urlParams.class_id : class_id))
                                    .then((data) => {
                                        data.json().then((resultData) => {
                                            if (resultData.stat == 1) {
                                                const templateList = resultData.msg;
                                                templateList.forEach((item: IAnyObj) => {
                                                    item.templ_purpose = resultData.templ_purpose[item.id];
                                                })
                                                this.setState({
                                                    templateList: templateList,
                                                    wheelAdd: true,
                                                    lastPage: false,
                                                    isDefaultShow: false
                                                })
                                            } else {
                                                this.setState({
                                                    templateList: noEmpty ? oldList : [],
                                                })
                                            }
                                        });
                                    })
                            }
                            // 当前筛选项没有结果获取全部分类结果

                        } else {
                            this.setState({
                                templateList: [],
                                isDefaultShow: true
                            })
                        }
                    });
                });
        }

    }

    /**
     * 添加下一页内容
     */
    addTemplateList(): void {
        this.addTemplateListListener = emitter.addListener('TemplateAddAssetList', () => {
            const { search } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.Search
            });
            const { wheelAdd, lastPage, inputValue, templateTab, formatId } = this.state
            const templatePage = search.templatePage;
            if (wheelAdd && !lastPage) {
                this.setState({
                    wheelAdd: false
                })
                this.getNewTemplateList({
                    kw: inputValue,
                    page: templatePage,
                    formatId: formatId,
                    esType: templateTab
                });
            }
        })

    }

    templateItemClickEmitter(): void {
        const th = this;
        emitter.addListener("templateItemClick", (templateId: string, e: React.MouseEvent, props = {}) => {
            th.templateItemClickEvent(templateId, props, e)
        });
    }

    getWindowInfo({
        templateId,
        pageIndex = 0
    }: {
        templateId: string,
        pageIndex?: number
    }) {
        const props: IAnyObj = getProps();
        return ({
            windowContent: <TemplateReplacePopup serchclickType={this.serchclickType} templateId={templateId} pageIndex={pageIndex} addProps={props.addProps} key={'TemplateReplacePopup-' + templateId} rt_canvas_render_mode={this.props.rt_canvas_render_mode} />,
            popupWidth: 490,
            popupHeight: 218,
            closePopupPvNew: 5232,
            style: {
                padding: 0,
                borderRadius: '8px',
                top: '50%',
                left: '50%',
                marginTop: '-130px',
                marginLeft: '-250px'
            }
        });
    }

    /**
     * 模板列表（点击事件）
     */
    async templateItemClickEvent(templateId: string, props: IAnyObj, e: React.MouseEvent) {
        let class_id: string[] = [];
        let isJumpPage = true;
        assetManager.getClassId(templateId).then((data) => {
            data.json().then(async (resultData) => {
                if (resultData.stat === 1) {
                    class_id = resultData.data
                }

                const props: IAnyObj = getProps();

                /**
                 *  在class_id中包含classIdArr中一种情况不跳转页面或者属于电商二级分类
                 */
                const classIdArr: string[] = ['4', '5', '12', '800', '956', '1025', '1026'];
                class_id.forEach((item: string) => {
                    if (classIdArr.includes(item)) {
                        isJumpPage = false;
                    }
                });
                if (window.location.href.includes('ecommerce.818ps.com') || isEcommerce() || isEcommerceTeam()) {
                    isJumpPage = false;
                }

                if (class_id.indexOf('34') > -1 && !props['isDesigner'] && isJumpPage) {
                    try {
                        const response = await assetManager.getTemplate(templateId, 0);
                        const resultData = await response.json();
                        if (resultData.stat != 1) {
                            return false;
                        }
                        const floorCutting = resultData.doc.canvas.floorCutting;
                        if (floorCutting && floorCutting.length > 0 && floorCutting.length !== resultData.doc.work.length) {
                            window.open(`//ecommerce.818ps.com/v1/?picId=${templateId}`);
                            return;
                        } else {
                            // window.open(`//ue.818ps.com/?picId=${templateId}`);
                            // return;
                        }
                    } catch (error) {
                        console.error(error);
                    }
                }
                
                    const { info } = storeAdapter.getStore({
                        store_name: storeAdapter.store_names.InfoManage,
                    });

                    if (info.is_second) {
                        const newInfo = Object.assign({}, info);
                        newInfo.old_templ_id = templateId;
                        InfoManageHelper.updateInfo(newInfo);
                    }

                    if (props.firstBlankSource == 1) {
                        assetManager.setPagePv_new(74, '', 0);
                    }
                    if (this.state.templateTab === 1) {
                        //（图片编辑器  二次创作）点击全部模板下子模板 埋点
                        assetManager.setPv_new(3486, {
                            additional: {
                                s0: templateId
                            }
                        });
                    }
                    if (this.state.templateTab === 2) {
                        //图片编辑器  二次创作）高通过率下模板点击次数 埋点
                        const additional: IAnyObj = {
                            s0: templateId,
                        }
                        // formatId!=='all' 不触发埋点
                        if (this.state.formatId !== 'all') {
                            additional.s1 = this.state.formatId
                        }
                        assetManager.setPv_new(3485, { additional });
                    }
                    assetManager.setPv_new(178, {
                        additional: {
                            templID: templateId
                        }
                    });
                    emitter.emit('popupWindow', this.getWindowInfo({
                        templateId
                    }));
            })


            // 筛选出来的模板
            if (!!this.state.classSearchid) {
                assetManager.setPv_new(7190, {
                    additional: {
                        s0: templateId,
                    }
                })
            }
        })

        assetManager.setPv_new(5231)
        const { inputValue } = this.state;
        assetManager.setPv_new(7102, {
            additional: {
                s1: '模板',
                s2: inputValue,
            }
        })
    }

    updateTemplateListData(): void {
        const th = this;
        this.updateTemplateListDataListener = emitter.addListener("updateTemplateListData", (templateId: number) => {
            const { templateList } = th.state;
            const newTemplateList = Object.assign([], templateList);
            for (let i = 0; i < newTemplateList.length; i++) {
                if (newTemplateList[i].id == templateId) {
                    newTemplateList[i].hasUsed = 1;
                    break;
                }
            }
            th.setState({
                templateList: newTemplateList
            })
        })
    }

    refreshTemplateListData(): void {
        const th = this;
        this.refreshTemplateListDataListener = emitter.addListener("refreshTemplateListData", (templateId: string) => {
            const { templateList } = th.state;
            const newTemplateList = Object.assign([], templateList);
            for (let i = 0; i < newTemplateList.length; i++) {
                if (newTemplateList[i].id == templateId) {
                    newTemplateList[i].hasUsed = 0;
                    break;
                }
            }
            th.setState({
                templateList: newTemplateList
            })
        });
    }

    /**
     * 滚动条移动
     **/
    wheelMoveEvent(event: React.UIEvent): void {
        const { templateList } = this.state
        if (!(templateList.length > 0)) {
            return;
        }
        event.stopPropagation();
        const clientHeight = (event.target as HTMLDivElement).clientHeight;
        const scrollHeight = (event.target as HTMLDivElement).scrollHeight;
        const scrollTop = (event.target as HTMLDivElement).scrollTop;
        const isBottom = clientHeight + scrollTop === scrollHeight;

        if (isBottom) {
            emitter.emit('TemplateAddAssetList')
        }

        if (this.listMoreBtn.current && this.listMoreBtn.current.style) {
            if (scrollTop > 100) {
                this.listMoreBtn.current.style.display = 'block';
            } else {
                this.listMoreBtn.current.style.display = 'none';
            }
        }



        if (scrollTop > 300) {
            this.setState({
                isDropdownBox: true,
            });
        } else {
            this.setState({
                isDropdownBox: false,
            });
        }
    }

    /**
     * 收藏模板
     * @param assetId
     * @param e
     */
    assetFavBtnClickEvent(assetId: string, asset: IAsset, e: React.MouseEvent): void {
        const assetFavBtnDom = this.templateFavBtnDom[assetId],
            className = assetFavBtnDom.getAttribute('class');
        if (className == 'templateFavBtn') {
            assetFavBtnDom.setAttribute('class', 'templateFavBtn active');
            assetManager.setFavTemplate(assetId).then((data) => {
                data.json().then((resultData) => {
                    if (resultData.stat != 1) {
                        assetFavBtnDom.setAttribute('class', 'templateFavBtn');
                        return false;
                    }
                    asset.isFav = 1;
                    emitter.emit('updateFavTemplateList');
                });
            });
            // 筛选出来的模板
            if (!!this.state.classSearchid) {
                assetManager.setPv_new(7188, {
                    additional: {
                        s0: assetId,
                    }
                })
            } else {
                assetManager.setPv_new(6350, {
                    additional: {
                        i0: 1,
                    }
                })
            }

        } else {
            assetFavBtnDom.setAttribute('class', 'templateFavBtn');
            assetManager.delFavTemplate(assetId).then((data) => {
                data.json().then((resultData) => {
                    if (resultData.stat != 1) {
                        assetFavBtnDom.setAttribute('class', 'templateFavBtn active');
                        return false;
                    }
                    asset.isFav = 0;
                    emitter.emit('updateFavTemplateList');
                });
            });
            assetManager.setPv_new(6350, {
                additional: {
                    i0: 0,
                }
            })
        }

        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
    }

    /**
     * 更多背景点击事件（全局声明）
     */
    moreBtnClickEmitter(): void {
        const th = this;

        this.moreBtnClickEmitterListener = emitter.addListener('templatePanelMoreBtnClick', (kw: string, props: IAnyObj = {}) => {
            if (props.firstBlankSource == 1) {
                this.firstBlankSource = 1;
            } else {
                this.firstBlankSource = 0;
            }
            if (props.serch) {
                this.serchclickType = true;
                this.seachNowInput = props.seachNowInput;
            } else {
                this.serchclickType = false;
                this.seachNowInput = ''
            }
            th.moreBtnClickEvent(kw, props.fromType || '');
        });
    }

    /**
     * 更多模板点击事件
     * @param e
     */
    moreBtnClickEvent(kw: string, type = ''): void {
        this.moreBtn = this.moreBtn >= 0 ? this.moreBtn + 1 : 0;
        const w = 1500,
            h = 940

        const windowInfo: IAnyObj = {
            windowContent: <BigPopupWindow fromWhere={{ fromType: type }} width={w} height={h} kw={kw} type="template" seachNowInput={this.seachNowInput} serchclickType={this.serchclickType} firstBlankSource={this.firstBlankSource} key={'moreBtn' + this.moreBtn + '-' + this.firstBlankSource} />,
            style: {
                padding: 0,
                borderRadius: '4px',
                top: '20px',
                left: '90px',
                bottom: '20px',
                right: '90px'
            },
            popupTitleBarStyle: {
                height: 0
            },
            popupBodyStyle: {
                padding: 0
            }
        };

        emitter.emit('popupWindow', windowInfo);
        if (this.firstBlankSource == 1) {
            this.firstBlankSource = 0;
        }
        assetManager.setPv_new(6347);
    }

    /**
     * 搜索按钮点击事件
     */
    searchBtnDownEvent(): void {

        const { inputValue, formatId, templateTab } = this.state;
        this.setState({
            classSearchid: '',
            isShowcreen: false
        })
        this.resetPageInfo()
        const params: IAnyObj = {
            kw: inputValue,
            esType: templateTab
        }
        if (templateTab === 2) {
            params.formatId = formatId
        }

        this.getNewTemplateList(params)
        assetManager.setPv_new(6349);
        assetManager.setPv_new(7101, {
            additional: {
                s1: '模板',
                s2: inputValue,
            }
        })
    }

    clearSearchInput = () => {
        this.setState({
            inputValue: '',
        })
    }

    /**
     * 搜索框获取焦点
     */
    searchInputFocus(): void {
        assetManager.setPv_new(6348);
        this.searchInputKeyDownListener = addEventListener(window, 'keydown', (e: KeyboardEvent) => {

            if (e.keyCode == 13) {
                this.searchBtnDownEvent()
                this.searchInput.current.blur();
            }
        })
    }

    /**
     * 点击搜索按钮
     */
    onSearchBtnClickEvent(e: React.MouseEvent): void {
        this.searchBtnDownEvent();

        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    /**
     * 搜索框失去焦点
     */
    searchInputBlur(): void {
        this.searchInputKeyDownListener.remove()
    }

    onChange(event: React.KeyboardEvent): void {
        this.setState({
            inputValue: (event.target as HTMLInputElement).value,
            classSearchid: ''
        }
        );
    }

    componentWillUnmount(): void {
        this.updateTemplateListListenner && this.updateTemplateListListenner.remove();
        this.moreBtnClickEmitterListener && this.moreBtnClickEmitterListener.remove();
        this.tgsAssetLoadEventListener && this.tgsAssetLoadEventListener.remove();
        this.updateTemplateListDataListener && this.updateTemplateListDataListener.remove();
        this.addTemplateListListener && this.addTemplateListListener.remove();
        this.refreshTemplateListDataListener && this.refreshTemplateListDataListener.remove();
        this.searchInputKeyDownListener && this.searchInputKeyDownListener.remove();
    }

    componentDidMount(): void {
        this.updateTemplateList(true);
        this.updateTemplateListData();
        this.getFormatClass();
        this.getTemplateNum();
        this.getTemplateTabsFn()
    }

    componentDidUpdate(prevProps: Readonly<TemplatePanel_propsStruct>): void {
        if (!isInit && !equal(prevProps.info, this.props.info) && this.props.info?.search_key_word && !isInitEnd) {
            isInit = true;
            setTimeout(() => {
                this.updateTemplateList(true);
            }, 500);
        }
        if (!isInit && (prevProps.canvas.width !== this.props.canvas?.width || prevProps.canvas.height !== this.props.canvas?.height)) {
            isInit = true;
            setTimeout(() => {
                this.updateTemplateList(true);
            }, 500);
        }
    }

    /**
     * 处理url参数
     */
    urlParametric(url: string) {
        const obj = {},
            params = url.substr(1);
        const parr = params.split('&');
        for (const i of parr) {
            const arr = i.split('=');
            obj[arr[0]] = arr[1];
            obj[arr[0]] = arr[1];
            obj[arr[0]] = arr[1];
        }
        return obj
    }

    onWheelEvent(): void {
        this.setState({});
    }

    onClickCancelFreedEvent(id: string): void {
        const th = this;
        assetManager.cancelFreed(id).then((data) => {
            data.json().then((resultData) => {
                if (resultData.stat === 1) {
                    const { templateList } = th.state;
                    const newTemplateList = Object.assign([], templateList);
                    for (let i = 0; i < newTemplateList.length; i++) {
                        if (newTemplateList[i].hasUsed && Number(newTemplateList[i].id) === Number(id)) {
                            newTemplateList[i].hasUsed = 0;
                            break;
                        }
                    }
                    th.setState({
                        templateList: newTemplateList
                    })
                }
            });

        })
    }

    onGoCorporateMembers(e: React.MouseEvent): void {
        assetManager.setPagePv_new(3252);
        const { info } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage,
        });
        if ((info && info.is_company_temp === 1) || env.teamTemplate) {
            window.open(
                'https://818ps.com/dash/firm-intro?origin=font-authorize-btn&route_id=16328996965437&route=1,&after_route=1',
            );
        } else {
            window.open('https://818ps.com/dash/vip-spec?classify=1&origin=VipSpec&route_id=16007403774149&route=1,86&after_route=1_86')
        }
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    onMouseEnterEvent(e: React.MouseEvent): void {
        assetManager.setPagePv_new(3257);
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    onMouseLeaveEvent(e: React.MouseEvent): void {
        assetManager.setPagePv_new(3258);
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    onClickDefaultList = () => {
        this.setState({
            classSearchid: '',
            classSearchTitle: '',
            isShowcreen: false,
            templateTab: 1,
            formatId: 'all',
            activeTab: 'default',
        }, () => {
            this.updateTemplateList(true);
        })
        assetManager.setPv_new(9044);
    }

    render(): JSX.Element {
        const { templateList, formatId, formatClassList, isDefaultShow, isDropdownBox, classInfo, templateTab, tabsList, isShowcreen, listLoading, activeTab } = this.state;
        const { inputValue, placeholder, targetTemplate, classSearchTitle } = this.state;
        const { search } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.Search
        }); // canvasStore.getState().searchManageRedux;
        const { canvas, isDesigner, isEffectTemplate } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas
        })
        const { isActive, info, type } = this.props;
        const props = getProps();
        let column = 1

        let ratio = -1,
            threshold = 312;
        if (templateList.length > 0) {
            const tempNum = templateList[0].width / templateList[0].height;
            ratio = tempNum >= 1 ? 1 : 2;
        }

        if (ratio == 2 || (!isDesigner && (['ecommerce', 'ecommerceteam'].includes(env.editor) || isEcommerce() || isEcommerceTeam())&& canvas.width === canvas.height)) {
            threshold = 147;
            column = 2
        }
        //默认筛选条件下无结果提示
        let isDefaultShowContent = <p>当前筛选条件下没有搜索结果<br />以下是全部的结果</p>;
        if (!isDefaultShow && search.templateK1 == 0 && search.templateK2 == 0 && search.templateRatioId != -1) {
            if (search.templateRatioId == 0) {
                isDefaultShowContent = <p>当前版式<p className="text1">{'"方图"'}</p>没有匹配结果<br />以下结果包含所有版式</p>;
            } else if (search.templateRatioId == 1) {
                isDefaultShowContent = <p>当前版式<p className="text1">{'"横图"'}</p>没有匹配结果<br />以下结果包含所有版式</p>;
            } else if (search.templateRatioId == 2) {
                isDefaultShowContent = <p>当前版式<p className="text1">{'"竖图"'}</p>没有匹配结果<br />以下结果包含所有版式</p>;
            }
        }

        if (templateList.length >= 1 && (templateList[0].width == 1242 || templateList[0].width == 2480)) {
            column = 2
            threshold = 147
        }

        return (
            <div className="templatePanel">
                {(props['is_second']) &&
                    <div className="company_use"
                        onClick={props['isDesigner'] ? undefined : this.onGoCorporateMembers.bind(this)}
                        onMouseLeave={this.onMouseLeaveEvent.bind(this)} onMouseEnter={this.onMouseEnterEvent.bind(this)}>

                        {
                            props['is_second'] &&
                            <span>点击一个模板开始二次创作吧！</span>

                            // <div>
                            //     <i className="iconfont icon-shang"></i>
                            //     <span>商用会员所有模板可放心商用</span>
                            // </div>
                        }

                    </div>
                }
                {(props['isDesigner'] && props['is_second']) &&
                    <div className="template_type_tab">
                        <div className={`template_type_item ${templateTab === 1 ? 'active' : ''}`} onClick={() => this.changeTemplateTab(1)}>
                            全部模板
                        </div>
                        <div className={`template_type_item ${templateTab === 2 ? 'active' : ''}`} onClick={() => this.changeTemplateTab(2)}>
                            高通过率模板
                            <img className='template_new_icon' src={imgHost + '/site/editor/newIconfont.svg'} alt="new" />
                        </div>
                    </div>
                }

                {false && isDropdownBox && <div className="dropdownBox">
                    <div className="dropDownBoxInputArea">
                        <input type="text" onChange={this.onChange.bind(this)} onFocus={this.searchInputFocus.bind(this)} onBlur={this.searchInputBlur.bind(this)} placeholder={placeholder} value={inputValue} />
                        <i className="icon iconfont icon-sousuo" onMouseDown={this.searchBtnDownEvent}></i>
                    </div>
                </div>}
                {
                    !isEffectTemplate && <div className='class_list_search'>
                        <div className={'searchBar isSecondSearchBar'}>
                            <input
                                className="searchInput"
                                ref={this.searchInput}
                                placeholder={placeholder}
                                value={inputValue}
                                onChange={this.onChange.bind(this)}
                                onFocus={this.searchInputFocus.bind(this)}
                                onBlur={this.searchInputBlur.bind(this)}
                                type="text" />
                            <i className="icon iconfont icon-sousuoxiao" onMouseDown={this.searchBtnDownEvent} />
                            {inputValue && <i className="icon iconfont icon-shanchu1" onClick={this.clearSearchInput}></i>}
                        </div>
                        {/* <div className="searchBtn" onClick={this.onSearchBtnClickEvent.bind(this)}>
                                搜索
                          </div> */}
                        <div className='class_filter' onMouseLeave={() => {
                            this.setState({
                                isShowcreen: false,
                            })
                        }}>
                            <div className={`default_list ${activeTab === 'default' ? 'active' : ''}`} onClick={this.onClickDefaultList}>推荐模版</div>
                            <div className={`class_filter_btn ${activeTab === 'class_filter' ? 'active' : ''}`}
                                onClick={() => assetManager.setPv_new(7183)}
                                onMouseEnter={() => {
                                    this.setState({
                                        isShowcreen: true
                                    })
                                    assetManager.setPv_new(7182)
                                }}
                            >   
                                {classSearchTitle || '分类'}
                                <i className={`iconfont icon-dilan-jiantou ${isShowcreen ? 'active' : ''}`}></i>
                            </div>
                            {isShowcreen &&
                                <div className="class_filter_wrapper">
                                    <div className="class_filter_context" onMouseLeave={() => {
                                        this.setState({
                                            isShowcreen: false,
                                        })
                                    }}>
                                        {tabsList.length > 0 && <TemplateITabs items={tabsList} onClick={this.onChangeTemplateTabs}></TemplateITabs>}
                                    </div>
                                </div>
                            }

                        </div>
                    </div>
                }



                {(props['isDesigner'] && props['is_second']) && templateTab === 2 &&
                    <div className="template_select">
                        <Select
                            needAll={true}
                            placeholder='版式'
                            value={formatId}
                            onChange={(e: React.KeyboardEvent) => this.handleTemplateSelect(e)}
                            options={formatClassList} />
                    </div>
                }

                {
                    isEffectTemplate ? <EffectTemplatePanel templateWrap={templateList as EffectTemplateFloor[]} templateClick={this.templateItemClickEvent.bind(this)} favClick={this.assetFavBtnClickEvent.bind(this)}></EffectTemplatePanel> :
                        <div
                            className="scroll_content_auto" ref={node => this.TemplateNode = node} key={search.templateWord + '-' + search.templateRatioId} onScroll={this.wheelMoveEvent}
                        >
                            {listLoading &&
                                <div className="templateList">
                                <Masonry
                                    breakpointCols={2}
                                    className="my-masonry-grid"
                                    columnClassName="my-masonry-grid_column"
                                    key="loading-template-list"
                                >
                                    {new Array(20).fill(1).map((item, index) => {
                                        return (
                                            <div className="templateItem" key={index}>
                                                <Skeleton.Node active style={{
                                                    width: 146,
                                                    height: 200,
                                                }} />
                                            </div>
                                        )
                                    })}
                                </Masonry>
                            </div>}

                            <div>
                                {/* {!isDefaultShow && <div className="isDefaultShowContent">{isDefaultShowContent}</div>} */}
                                <div className="templateList" key={templateList.length + '_' + this.updateNum}>
                                    {templateList.length > 0 ? (
                                        <Masonry
                                            breakpointCols={column}
                                            className="my-masonry-grid"
                                            columnClassName="my-masonry-grid_column"
                                            // loadMore={defaultFun}
                                            // hasMore={false}
                                            // sizes={sizes}
                                            // className="templateList-scroller"
                                            // threshold={threshold}
                                            key={
                                                'templateItemArea' +
                                                '-' +
                                                this.updateNum +
                                                isActive +
                                                '-' +
                                                '-' +
                                                isDefaultShow
                                            }
                                        >
                                            {templateList.map((templ, index) => {
                                                const multiPage = templ.pages > 1
                                                return (
                                                    (
                                                        <div
                                                            className="templateItem"
                                                            id={'templateItem_' + index}
                                                            key={`${templ.pageIndex}-${templ.id}`}
                                                            style={{
                                                                width: threshold + 'px',
                                                                height: (threshold * templ.height) / templ.width + 'px',
                                                                minHeight: 60,
                                                            }}
                                                        >
                                                            <LazyLoad
                                                                height={(threshold * templ.height) / templ.width + 'px'}
                                                                key={templ.id}
                                                                width={threshold}
                                                                offset={200}
                                                            >
                                                                <img
                                                                    style={{
                                                                        height: (threshold * templ.height) / templ.width + 'px',
                                                                        width: '100%',
                                                                    }}
                                                                    id={'templateItemImg_' + index}
                                                                    src={templ.host + templ.imgUrl}
                                                                />
                                                            </LazyLoad>
                                                            {/* {templ.templ_purpose > 0 && (
                                                            <div>
                                                                <div style={templateTagStyle[templ.templ_purpose]} className="templateTag">{templateTag[templ.templ_purpose]}</div>
                                                                <div style={templateTagStyle[templ.templ_purpose]} className="templateTag hoverTemplateTag">{hoverTemplateTag[templ.templ_purpose]}</div>
                                                            </div>
                                                        )} */}
                                                            {templ.templ_purpose > 0 && templ.templ_purpose < 4 && (
                                                                    <div className="templateVipTag">
                                                                        <img src={imgHost + '/platform/templ_vip_icon.png'} className='templateVipIcon' alt="" />
                                                                        <span className='templateVipText'>{hoverTemplateTag[templ.templ_purpose]}</span>
                                                                    </div>
                                                            )}
                                                            <div className="elementHover"></div>
                                                            <div
                                                                className="elementInfo"
                                                                onClick={multiPage ?
                                                                    () => this.setState({ targetTemplate: templ }) :
                                                                    this.templateItemClickEvent.bind(this, templ.id, {})
                                                                }
                                                            >
                                                                <div className="elementInfoTitle">{templ.title}</div>
                                                                <div
                                                                    className={
                                                                        templ.isFav ? 'templateFavBtn active' : 'templateFavBtn'
                                                                    }
                                                                    // ref={'templateFavBtn' + templ.id}
                                                                    ref={(ref) => {
                                                                        Object.assign(this.templateFavBtnDom, {
                                                                            [templ.id]: ref,
                                                                        });
                                                                    }}
                                                                    onClick={this.assetFavBtnClickEvent.bind(
                                                                        this,
                                                                        templ.id,
                                                                        templ,
                                                                    )}
                                                                    onMouseEnter={() => {
                                                                        assetManager.setPv_new(7189, {
                                                                            additional: {
                                                                                s0: templ.id,
                                                                            }
                                                                        })
                                                                    }}
                                                                >
                                                                    <i className="iconfont icon-shoucang1"></i>
                                                                </div>
                                                                {templ.template_type === '3' && <div className='template-page-count'>
                                                                    第 1/{templ.pages} 页
                                                                </div>}
                                                            </div>

                                                            {info.is_second ? (
                                                                <div>
                                                                    <div
                                                                        className="occupiedTemplate"
                                                                        onClick={multiPage ?
                                                                            () => this.setState({ targetTemplate: templ }) :
                                                                            this.templateItemClickEvent.bind(this, templ.id)
                                                                        }
                                                                    >
                                                                        点击使用
                                                                    </div>
                                                                    {/* {
                                                                            !templ.hasUsed ? (
                                                                            <div className="occupiedTemplate" onClick={ this.templateItemClickEvent.bind(this, templ.id)}>
                                                                                占用模板
                                                                            </div>
                                                                        ):(
                                                                            <div className="cancelOccupyTemplate" onClick={this.onClickCancelFreedEvent.bind(this,templ.id)}>
                                                                                取消占用
                                                                            </div>
                                                                            )
                                                                        } */}
                                                                </div>
                                                            ) : (
                                                                <div
                                                                    className={'addItemBtn'}
                                                                    onClick={multiPage ?
                                                                        () => this.setState({ targetTemplate: templ }) :
                                                                        this.templateItemClickEvent.bind(this, templ.id, {})
                                                                    }
                                                                    onMouseEnter={() => {
                                                                        assetManager.setPv_new(7191, {
                                                                            additional: {
                                                                                s0: templ.id,
                                                                            }
                                                                        })
                                                                    }}
                                                                    style={
                                                                        classInfo['kid_2'] == 19 ||
                                                                            (info.class_id &&
                                                                                info.class_id.filter((v) => Number.parseInt(v) == 19)
                                                                                    .length > 0)
                                                                            ? {
                                                                                bottom: '2px',
                                                                            }
                                                                            : {}
                                                                    }
                                                                >
                                                                    立即更换
                                                                </div>
                                                            )}
                                                            {templ.is_ai === 1 ? <img className='aiTemplTag' src='https://js.tuguaishou.com/editor/image/ai/AI.png' /> : ''}
                                                        </div>
                                                    )

                                                );
                                            })}
                                        </Masonry>
                                    ) : (
                                        templateList.length <= 0 && !listLoading && (
                                            <div
                                                style={{
                                                    textAlign: 'center',
                                                    paddingTop: 15,
                                                    margin: '0 auto',
                                                    width: '100%',
                                                    height: '100%',
                                                }}
                                            >
                                                {!isInitEnd ? (
                                                    <div className='loader_wrapper'>
                                                        <div className="loader_cartoon"></div>
                                                        <div style={{ fontSize: 13, color: '#a9a9a9' }}>玩命加载中...</div>
                                                    </div>
                                                ) : (
                                                    <>
                                                        <img
                                                            style={{ marginBottom: 12 }}
                                                            src="//s.tuguaishou.com/image/editor/group-word-empty.png"
                                                        />
                                                        <div style={{ fontSize: 13, color: '#a9a9a9' }}>
                                                            结果为空，换个试试吧~~
                                                        </div>
                                                    </>
                                                )}
                                            </div>
                                        )
                                    )}
                                </div>
                                {
                                    info.is_second && (templateList && templateList.length === 0) ? (
                                        <div className="isDefaultShowContent">当前筛选条件下没有搜索结果</div>
                                    ) : null
                                }
                                {
                                    (!info.is_second && this.props.type === 'template') ? (<div className="listMoreBtn" ref={this.listMoreBtn} onClick={this.moreBtnClickEvent.bind(this, '', 'template-panel')}>更多模板</div>) : null
                                }

                                {/* <div className="listMoreBtn" ref="listMoreBtn" onClick={this.moreBtnClickEvent.bind(this, '')}>更多模板</div> */}

                            </div>
                        </div>

                }
                {
                    targetTemplate && <PreviewPanel data={targetTemplate} getWindowInfo={this.getWindowInfo.bind(this)} onClose={() => this.setState({ targetTemplate: undefined })} />
                }
            </div>
        )
    }
}


export default class OccupationTips extends Component {
    constructor(props: IAnyObj) {
        super(props);
        this.state = {}

    }

    render(): JSX.Element {
        return (
            <div className="occupationTips">使用本模板之前，请先取消已占用模版后才可以选用模板才行哦！</div>
        )
    }
}


export { TemplatePanel }
