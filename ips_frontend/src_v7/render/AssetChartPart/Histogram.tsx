import React, {Component} from 'react';
// import * as d3 from "d3";

import {IAnyObj, IAsset} from '@v7_logic/Interface';

// 待替换组件
import {emitter} from '@component/Emitter';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { CanvasPaintedLogic } from '@v7_logic/CanvasPaintedLogic';

let d3

interface propsStruct {
    assetProps?: {
        canvasScale?: number;
        assetClassName?: string;
        currentPageNum?: number;
        isPreview?: boolean;
    };
    asset?: IAsset;
};
interface stateStruct {};

class Histogram extends Component<propsStruct, stateStruct>{
    constructor(props: propsStruct){
        super(props);
    }
    
    shouldComponentUpdate(){
        const {rt_current_is_moving_asset} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }); // canvasStore.getState().onCanvasPainted;
        if(rt_current_is_moving_asset){//拖拽元素时不重新渲染
            return false;
        }else{
            return true;
        }
    }

    /**
     * 绘制柱形图
     * @param {*} type 
     */
    async drawHistogram(type = 0){
        let {asset, assetProps} = this.props,
            {canvas} = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            }); // canvasStore.getState().onCanvasPainted;
        let attribute = asset.attribute,
            chartDrawInfo = attribute.chartDrawInfo,
            chartRule = attribute.chartRule;
        let canvasScale = assetProps.canvasScale > 0 ? assetProps.canvasScale : canvas.scale;
        let th = this;

        /* 初始变量定义 START */
        let WHMin = attribute.width > attribute.height ? attribute.height : attribute.width;    // 最小边计算
        let isLeftBar = chartRule.isLeftBar;
        
        let fontSize = WHMin * chartDrawInfo.fontSize,    // 文字大小
            fontColor = "rgba(" + chartDrawInfo.fontColor.r + "," + chartDrawInfo.fontColor.g + "," + chartDrawInfo.fontColor.b + "," + chartDrawInfo.fontColor.a + ")",    // 文字颜色
            axisXTextY = WHMin * chartDrawInfo.axisXTextY,
            axisYTextX = WHMin * chartDrawInfo.axisYTextX,
            axisYX = attribute.width * chartDrawInfo.axisPosX,    //Y坐标轴X坐标
            axisYY = attribute.height * chartDrawInfo.axisPosY,    //Y坐标轴Y坐标
            axisXX = attribute.width * chartDrawInfo.axisPosX,
            axisXY = attribute.height * (chartDrawInfo.axisPosY + chartDrawInfo.axisYHeight),
            brokenLineWidth = WHMin * chartDrawInfo.brokenLineWidth,
            axisXWidth = attribute.width * chartDrawInfo.axisXWidth,
            axisYHeight = attribute.height * chartDrawInfo.axisYHeight,
            _chartBgcolor = attribute.chartBgcolor || {r:255,g:255,b:255,a:1},
            _coordinateText = attribute.coordinateText || {} ,
            labelColor = _coordinateText.color || {r:51,g:51,b:51,a:1} ,
            labelSize = _coordinateText.size ,
            legendInfo = attribute.legendBoxInfo ;
        let axisYStrokeDasharray = "none",
            brokenLineFill = "none";
        if( chartDrawInfo.axisYStrokeDasharray ){
            axisYStrokeDasharray = "";
            chartDrawInfo.axisYStrokeDasharray.map((item: number, index: number) => {
                if( index > 0 ){
                    axisYStrokeDasharray += ",";
                }
                axisYStrokeDasharray += attribute.width * item;
            });
        }
        /* 初始变量定义 END */

        // 获取当前显示的className
        let assetClassName = asset.meta.className;
        if( assetProps.assetClassName ){
            assetClassName = assetProps.assetClassName;
        }
        if (!d3) {
            d3 = await import('d3');
        }
        //清除svg
        d3.select("." + assetClassName).select(".drawSVGArea")
            .select('svg').remove();
        //创建svg
        let svg = d3.select("." + assetClassName).select(".drawSVGArea")
            .append('svg')
            .attr('width', asset.attribute.width)
            .attr('height', asset.attribute.height)
            .attr("transform-origin", "0 0")
            .attr("transform", "scale(" + canvasScale + ")");
            // .style("background-color","none");
        // if(_chartBgcolor.a === 1){
        //     svg.style("background",`rgba(${_chartBgcolor.r},${_chartBgcolor.g},${_chartBgcolor.b},1)`);
        // }else{
        //     svg.style("background","none");
        // }
        svg.style("background",`rgba(${_chartBgcolor.r},${_chartBgcolor.g},${_chartBgcolor.b},${_chartBgcolor.a})`);
            
        //模拟数据:
        let dataX: (number[] | number)[] = [],
            dataY: (number[] | number)[] = [],
            dataZ: string[] = [],
            dataYSortMap: IAnyObj[] = [];
        // 用户数据
        if( attribute.userData ){
            attribute.userData.map((item, index) => {
                if( attribute.chartRule && attribute.chartRule.isFirstTitle ){
                    // 第1行是标题
                    if( index == 0 ){
                        // 获取第一行字段
                        dataZ = [...item];
                        dataZ = dataZ.slice(1, dataZ.length);
                        dataZ.map((subItem, subIndex) => {
                            dataY.push([]);
                        });
                    }else{
                        // 获取详细数据
                        dataZ.map((subItem, subIndex) => {
                              // @ts-ignore
                            dataY[subIndex].push(item[subIndex + 1]);
                        });
                        // @ts-ignore
                        dataX.push(item[0]);
                    }
                }else{
                    if( index == 0 ){
                        dataY.push([]);
                    }
                    // 第1行不是是标题
                    // @ts-ignore
                    dataY[0].push(item[1]);
                    // @ts-ignore
                    dataX.push(item[0]);
                }
            });
            dataY.map((item, index) => {
                dataYSortMap.push({
                    index: index,
                    value: item
                });
            });
            if( dataZ.length == 0 ){
                dataZ = ["单数据"]
            }
        }
        if( isLeftBar ){
            let tempData = dataY;
            dataY = dataX;
            dataX = tempData;
            //构建y轴的比例尺
            let ydomain = dataY.map(function(d,i){return d+'_'+i})
            let y = d3.scaleBand()
                .domain(ydomain).range([0, axisYHeight])
            let axisY = d3.axisLeft(y);
            axisY.tickPadding(([-axisYTextX] as any));
            let axisYG = svg.append('g')
                .attr('class','axisY')
                .attr('transform','translate('
                    + axisYX
                    + ',' + axisYY
                    + ')')
                .call(axisY as any);
            // 修改左侧y轴颜色
            axisYG.select('path')
             .attr('stroke',`rgba(${labelColor.r},${labelColor.g},${labelColor.b},${labelColor.a})`)
            // Y轴样式修改
            axisYG.selectAll('g.tick')
                .selectAll('text')
                .attr("color", `rgba(${labelColor.r},${labelColor.g},${labelColor.b},${labelColor.a})`)
                .attr("font-size", labelSize)
                // .attr("color", fontColor)
                // .attr("font-size", fontSize)
                .text((d: string, i:number)=>{
                    return d.split('_')[0];
                })
            //构建x轴比例尺
            let xmax = 0;
            dataX.map((item: number[], index) => {
                let tempItem = [xmax, ...item];
                xmax = d3.max(tempItem,function(d){return d}) //为了增大图表y的上限
            });
            xmax *= 1.2;
            let x = d3.scaleLinear()
                .domain([0, xmax])
                .range([0, axisXWidth]);

            let axisX = d3.axisBottom(x)
            axisX.tickPadding(([axisXTextY] as any));
            let axisXG = svg.append('g')
                .attr('class','axisX')
                .attr('transform','translate('
                    + axisXX
                    + ',' + axisXY
                    + ')')
                .call(axisX as any);
            // 修改底部x轴颜色
            axisXG.select('path')
                .attr('stroke',`rgba(${labelColor.r},${labelColor.g},${labelColor.b},${labelColor.a})`)
            //更改折线图x轴下方标尺小间隔线的颜色
            axisXG.selectAll('line')
                .attr('stroke',`rgba(${labelColor.r},${labelColor.g},${labelColor.b},${labelColor.a})`)

            axisXG.selectAll('g.tick')
                .attr('class','axisXRod')
                .selectAll('line')
                .attr('y1', -axisYHeight)
                .attr('y2', -attribute.height * chartDrawInfo.rodBottomOffset)
                .attr('stroke-dasharray', axisYStrokeDasharray)
                .attr("stroke", `rgba(${labelColor.r},${labelColor.g},${labelColor.b},${labelColor.a})`);
            // X轴样式修改
            axisXG.selectAll('g.tick')
                .selectAll('text')
                .attr("color", `rgba(${labelColor.r},${labelColor.g},${labelColor.b},${labelColor.a})`)
                .attr("font-size", labelSize)
                // .attr("color", labelColor)
                // .attr("font-size", fontSize)

            axisXG.selectAll('.axisXRod')
                    .selectAll('text')
                    .attr("font-size", labelSize)
                    .attr("color", `rgba(${labelColor.r},${labelColor.g},${labelColor.b},${labelColor.a})`)
            /* ========>> 绘制bar START <<======== */
            let xBandwidth = y.bandwidth() * 0.9,
                xBandGap = y.bandwidth() * 0.05,
                barGap = 0.0034 * asset.attribute.width,
                barWidth = (xBandwidth + barGap) / dataZ.length;
            for(let j = 0; j < dataZ.length; j++ ){
                let bar = svg.append('g')
                    .attr('transform','translate('
                    + axisYX
                    + ',' + axisYY
                    + ')');
                (bar.selectAll('rect')
                    .data(dataY)
                    .enter()
                    .append('rect')
                    .attr('y', function(d, i){return y(d+'_'+i) + barWidth * j + xBandGap})
                    .attr('x', function(d, i){return 0})
                    .attr('height', barWidth - barGap)
                    .attr('width', function(d, i){return x(dataX[j][i])})
                    .attr("fill", (d, i) => {
                        let index = j;
                        if( dataZ.length == 1 ){
                            index = i;
                        }
                        let tempBrokenLineColor = "rgba(0,0,0,1)";
                        tempBrokenLineColor = this.getColorDomain({
                            index: index,
                            colorTable: attribute.colorTable,
                            areaAlpha: attribute.chartDrawInfo.areaAlpha ? attribute.chartDrawInfo.areaAlpha : 1
                        });
                        return tempBrokenLineColor;
                    }) as IAnyObj).transition()
            }
            /* ========>> 绘制bar END <<======== */

        }else{
            //构建y轴的比例尺
            let ymax = 0;
            dataY.map((item: number[], index) => {
                let tempItem = [ymax, ...item];
                ymax = d3.max(tempItem,function(d){return d}) //为了增大图表y的上限
            });
            ymax *= 1.2;
            let y=d3.scaleLinear()
                .domain([0,ymax]).range([axisYHeight, 0])
            let axisY = d3.axisLeft(y);
            axisY.tickPadding(([-axisYTextX] as any));
            let axisYG = svg.append('g')
                .attr('class','axisY')
                .attr('transform','translate('
                    + axisYX
                    + ',' + axisYY
                    + ')')
                .call(axisY as any);
            // 修改左侧y轴颜色
            axisYG.select('path')
                .attr('stroke',`rgba(${labelColor.r},${labelColor.g},${labelColor.b},${labelColor.a})`)
            // Y轴样式修改
            axisYG.selectAll('g.tick')
                .selectAll('text')
                .attr("color", `rgba(${labelColor.r},${labelColor.g},${labelColor.b},${labelColor.a})`)
                .attr("font-size", labelSize)
                // .attr("color", fontColor)
                // .attr("font-size", fontSize)
            axisYG.selectAll('g.tick')
                .selectAll('line')
                .attr('x1', attribute.width * chartDrawInfo.rodLeftOffset)
                .attr('x2', axisXWidth)
                .attr('stroke-dasharray', axisYStrokeDasharray)
                .attr("stroke", `rgba(${labelColor.r},${labelColor.g},${labelColor.b},${labelColor.a})`);
                // .attr("stroke", () => {return "#ccc"});
            // 关闭底部横杆
            axisYG.selectAll('g.tick').filter(":nth-child(2)")
                .selectAll('line')
                .attr('x1', attribute.width * chartDrawInfo.rodLeftOffset)
                .attr('x2', 0)
                .attr('stroke-dasharray', "none")
                .attr("stroke", `rgba(${labelColor.r},${labelColor.g},${labelColor.b},${labelColor.a})`)
            //构建x轴比例尺
            let ydomain = dataX.map(function(d,i){return d +'_'+ i})//添加下标来使每项数据唯一
            //['制造','外包','金融','咨询']
            let x = d3.scaleBand()
                .domain(ydomain)
                .range([0, axisXWidth])

            let axisX = d3.axisBottom(x)
            axisX.tickPadding(([axisXTextY] as any));
            let axisXG = svg.append('g')
                .attr('class','axisX')
                .attr('transform','translate('
                    + axisXX
                    + ',' + axisXY
                    + ')')
                .call(axisX as any);
            // 修改底部x轴颜色
            axisXG.select('path')
                .attr('stroke',`rgba(${labelColor.r},${labelColor.g},${labelColor.b},${labelColor.a})`)
            //更改折线图x轴下方标尺小间隔线的颜色
            axisXG.selectAll('line')
                .attr('stroke',`rgba(${labelColor.r},${labelColor.g},${labelColor.b},${labelColor.a})`)
            // X轴样式修改
            axisXG.selectAll('g.tick')
                .selectAll('text')
                .attr("color", `rgba(${labelColor.r},${labelColor.g},${labelColor.b},${labelColor.a})`)
                .attr("font-size", labelSize)
                // .attr("color", fontColor)
                // .attr("font-size", fontSize)
                .text((d: string, i: number)=>{
                        return d.split('_')[0];//渲染x轴显示时 去除唯一性下标
                    }
                )
            /* ========>> 绘制bar START <<======== */
            let left = x.step()
            let xBandwidth = x.bandwidth() * 0.9,
                xBandGap = x.bandwidth() * 0.05,
                barGap = 0.0034 * asset.attribute.width,
                barWidth = (xBandwidth + barGap) / dataZ.length;
            for(let j = 0; j < dataZ.length; j++ ){
                let bar  = svg.append('g')
                    .attr('transform','translate('
                    + axisYX
                    + ',' + axisYY
                    + ')')
                bar.selectAll('rect')
                    .data(dataX)
                    .enter()
                    .append('rect')
                    .attr('x', function(d, i){return x(d+'_'+i) + barWidth * j + xBandGap})//x() = > 获取每项数据追加下标
                    .attr('y', function(d, i){return y(dataY?.[j]?.[i])})
                    .attr('width', (barWidth - barGap))
                    .attr('height', function(d, i){return axisYHeight - y(dataY?.[j]?.[i])})
                    // .attr("fill", (d) => {return "#66ccff"})
                    .attr("fill", (d, i) => {
                        let index = j;
                        if( dataZ.length == 1 ){
                            index = i;
                        }
                        let tempBrokenLineColor = "rgba(0,0,0,1)";
                        tempBrokenLineColor = this.getColorDomain({
                            index: index,
                            colorTable: attribute.colorTable,
                            areaAlpha: attribute.chartDrawInfo.areaAlpha ? attribute.chartDrawInfo.areaAlpha : 1
                        });
                        return tempBrokenLineColor;
                    })
            }
            /* ========>> 绘制bar END <<======== */
        }
        if( dataZ.length > 1 && legendInfo.show){
            /* ========>> 绘制legend START <<======== */
            let legendG = svg.append('g')
                .attr('class','legendBox')
                .attr('transform','translate('
                    + axisXX
                    + ',' + (axisXY + 50)
                    + ')');
            let legend = legendG.selectAll(".legend")
                .data(dataYSortMap)
                .enter()
                .append("g")
                .attr("class", "legend")
                
            legend.append("rect")
                .attr("x", 0)
                .attr("width", WHMin * 0.03)
                .attr("height", WHMin * 0.03)
                .attr("fill", (d, i) => {
                    let tempBrokenLineColor = "rgba(0,0,0,1)";
                    tempBrokenLineColor = this.getColorDomain({
                        index: i,
                        colorTable: attribute.colorTable,
                        areaAlpha: attribute.chartDrawInfo.areaAlpha ? attribute.chartDrawInfo.areaAlpha : 1
                    });
                    return tempBrokenLineColor;
                })

            legend.append("text")
                .attr("x", WHMin * 0.036)
                .attr("y", WHMin * 0.015)
                .attr("dy", ".35em")
                .style("text-anchor", "begin")
                .style("font-size", legendInfo.fontSize + "px")
                .attr("fill", `rgba(${legendInfo.fontColor[0].r},${legendInfo.fontColor[0].g},${legendInfo.fontColor[0].b},${legendInfo.fontColor[0].a})`)
                // .style("font-size", labelSize + "px")
                // .style("font-size", fontSize + "px")
                .text((d, i) => {
                    return dataZ[i];
                });

            // 获取所有legend元素
            let legends = legend.nodes();

            // legend单元素定位
            let legendWidthSum = 0;
            legendG.selectAll(".legend")
                .data(legends)
                .attr("transform", (d, i) => {
                    let tempWidth = d.getBBox().width;
                    let returnStr = "translate(" + (legendWidthSum + 20 * i) + ", 10)";
                    legendWidthSum += tempWidth;
                    return returnStr;
                });

            // legend区域居中
            let legendTopValue = 0 ;
            if(legendInfo.position == 'bottom'){
                legendTopValue = (axisXY + (attribute.height * 0.07)) ;
            }
            legendG.attr('transform','translate('
                + (axisXX + ((axisXWidth - legendG.node().getBBox().width) / 2))
                + ',' + legendTopValue
                + ')');
            /* ========>> 绘制legend START <<======== */
        }
    }

    /**
     * 获取指定颜色表中的颜色
     */
    getColorDomain(props: IAnyObj){
        let index = props.index ? props.index : 0,
            colorTable = props.colorTable;
        let tempBrokenLineColor = "rgba(0,0,0,1)",
            nowColorTableNum = 0;
        let tempAreaAlpha = props.areaAlpha ? props.areaAlpha : 1;
        if( colorTable && colorTable.length > 0 ){
            nowColorTableNum = index % colorTable.length;
            tempBrokenLineColor = "rgba(" + colorTable[nowColorTableNum].r + "," + colorTable[nowColorTableNum].g + "," + colorTable[nowColorTableNum].b + "," + (colorTable[nowColorTableNum].a * tempAreaAlpha) + ")";
        }

        return tempBrokenLineColor;
    }

    componentDidMount(){
        this.componentDidUpdate();
    }

    componentDidUpdate(){
        // 绘制图表
        this.drawHistogram();

        let {asset, assetProps} = this.props;
        if( !asset.rt_shotIsComplete ){
            // canvasStore.dispatch(paintOnCanvas('UPDATE_SHOTISCOMPLETE', {
            //     asset: asset,
            //     pageNum: assetProps.currentPageNum
            // }));
            CanvasPaintedLogic.updateShotIsComplete(
                {
                    asset,
                    // eslint-disable-next-line react/prop-types
                    pageIndex: assetProps.currentPageNum
                }
            )
            emitter.emit("SimpleCanvasShowUpdateState");
        }
    }

    render(){
        const {assetProps , asset} = this.props;
        let {canvas} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }); // canvasStore.getState().onCanvasPainted;
        let canvasScale = assetProps.canvasScale > 0 ? assetProps.canvasScale : canvas.scale;

        let areaStyle = {};
        if(assetProps.isPreview){
            areaStyle = {height:asset.attribute.height * canvasScale}
        }
        return (
            <div className="drawSVGArea" style={areaStyle}>

            </div>
        );
    }
}

export {Histogram};