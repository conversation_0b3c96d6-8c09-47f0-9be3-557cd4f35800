import { Mo<PERSON>, Tooltip, Checkbox } from 'antd';
import React, { PureComponent, useEffect, useState } from 'react';
import './scss/VipRecharge.scss';
import { Icon1, Icon2, TitleIcon, vipIconList } from './icon';
import { storeDecorator } from '@v7_logic/StoreHOC';
import { IStoreState } from '@v7_store/redux/store';
import { assetManager } from '@src/userComponentV6.0/AssetManager';
import { imgHost } from '@src/userComponentV6.0/IPSConfig';
import { TgsModal } from '@tgs/general_components/src/components';
import { PayResult } from './PayResult';
import { ITemplateInfo } from '@v7_logic/Interface';
import PublicTransfer from './PublicTransfer';

interface IVipType {
    vip_type: string;
    name: string;
    short_name: string;
    price: string;
    origin_price: string;
    period: string;
    period_num: string;
    amount_max: string;
    amount_max_pic: string;
    amount_max_gif: string;
    admission: string;
    level: string;
    amount_max_other: string;
    active_frequency: string;
    classify_sub: string;
    edit_num: string;
    give_away: string;
    give_kt: string;
    gift_info: string;
    give_draw: string;
    give_word: string;
    is_firm: string;
    give_template_precis: string;
    tuijian: boolean;
    xianliang: boolean;
    time: string;
    icon_index: number;
    vip_is_show: number;
    original_price_s: string;
    expiration_time_s: string;
    price_orange_s: {
        price: string;
        unit: string;
    };
    rights_s1: Array<{
        text: string;
        heightLight: number;
    }>;
    rights_s2: {
        bar_s1: {
            name: string;
            content: {
                hint_s: string;
                information: Array<{
                    hint_s: string;
                    information: Array<{
                        text: string;
                        heightLight: number;
                    }>;
                }>;
            };
        };
        bar_s2: {
            name: string;
            content: {
                information: Array<{
                    text: string;
                    heightLight: number;
                }>;
            };
        };
    };
    xianliang_s?: string;
    slogn_s?: string;
    amount_max_str?: string;
    cunch?: string;
    level_name?: string;
}

interface IClassifyInfo {
    code: string;
    name: string;
    authority: string;
    expire_name: string;
    vip_type_name: string;
    amount_left: string;
    amount_max: string;
    userInfoVipName: string;
    unit: string;
}

interface ISlogn {
    date: string;
    slogn: string;
    start_time: number;
    template_sum: number;
    template_sum1: number;
    limit_count: number;
    max_limit_count: number;
    limit_date: string;
    limit_count_b: number;
    max_limit_count_b: number;
    limit_date_b: string;
    limit_count_b_2: number;
}

interface IVipDesc {
    tishi: {
        show: number;
        download_num: string;
        authority: string;
        less_day: number;
    };
}

interface ILifelong {
    info: string;
}

interface IModel {
    uid: number;
    username: string;
    vip_type: number;
    vip_type_pic: number;
    vip_type_gif: number;
}

interface ISponsor {
    user_name: string;
}

interface IVipInfo {
    classify: number;
    vip_type: IVipType[];
    classify_info: IClassifyInfo;
    total: Record<string, string>;
    slogn: ISlogn;
    saveOrigin: boolean;
    ab_test: number;
    lottery_list: any[];
    model: IModel;
    sponsor: ISponsor;
    'vip-desc': IVipDesc;
    lifelong: ILifelong;
    vip_list: IVipType[];
}

function periodToDate(period: string) {
    switch (period) {
        case '3天':
            return '(3天)';
        case '31天':
            return '(1个月)';
        case '3个月':
        case '季':
            return '(3个月)';
        case '终身':
            return '(终身)';
        default:
            return '(12个月)';
    }
}

let gettingQrcode = false;

interface IVipRechargeContentProps {
    hide: () => void;
    user?: IStoreState['onCanvasPainted']['user'];
    vipTemplateNumMap?: IStoreState['InfoManage']['vipTemplateNumMap'];
    vipInfo?: IVipInfo;
    activeVipType: string;
    setActiveVipType: (type: string) => void;
    info: ITemplateInfo;
}

interface IVipRechargeContentState {
    checked: boolean;
    qrcode?: {
        unit_price: null;
        vipUpdateLock: false;
        needDeductOrder: false;
        wechatQrcode: string;
        alipayIframe: string;
        originVipType: string;
        originExpire: number;
        needRefund: false;
        unifyPayQrcode: string;
        vip_info: IVipInfo;
        price: number;
        origin_price: number;
        old_origin_price: number;
        vip_deduction_price: number;
        vip_deduction_price_match_old: number;
        vip_period: number;
        activity: string;
        pay_ways: {
            ali: {
                stat: 'success';
                cost: 2;
            };
        };
        lottery: null;
        group_work_lottery: null;
        vip_count: number;
        is_reduce_10: number;
        uin_id: string;
        pop_tencent_flag: number;
        has_pay_firm_vip: number;
    };
    payTimeout: boolean;
    payResult?: 'success' | 'fail';
    activeVipTypeInfo: IVipType | undefined;
    showPublicTransfer: boolean;
    showTecentPop: boolean
}

@storeDecorator((state: { [key: string]: any }) => {
    return {
        user: state.onCanvasPainted.user,
        vipTemplateNumMap: state.infoManageStore.vipTemplateNumMap,
    };
})
class VipRechargeContent extends PureComponent<IVipRechargeContentProps, IVipRechargeContentState> {
    state: IVipRechargeContentState = {
        checked: true,
        qrcode: undefined,
        payTimeout: false,
        payResult: undefined,
        activeVipTypeInfo: undefined,
        showPublicTransfer: false,
        showTecentPop: true,
    };

    time: number;
    payStatusTimer: number;
    key: 'vip_type' | 'vip_list' = 'vip_type';

    componentDidMount(): void {
        if (Number(this.props.user.userId) > 0) {
            this.getPayQrcode();
        }
        this.key = this.props.info.is_company_temp === 1 ? 'vip_list' : 'vip_type';
    }

    componentWillUnmount(): void {
        clearInterval(this.payStatusTimer);
    }

    componentDidUpdate(
        prevProps: Readonly<IVipRechargeContentProps>,
        prevState: Readonly<IVipRechargeContentState>,
        snapshot?: any,
    ): void {
        if (Number(this.props.user.userId) > 0 && !this.state.qrcode) {
            this.getPayQrcode();
        }
        if (!this.state.activeVipTypeInfo && this.props.activeVipType && this.props.vipInfo?.[this.key]) {
            const activeVipTypeInfo = this.props.vipInfo?.[this.key]?.find(
                (item) => item.vip_type === this.props.activeVipType,
            );
            if (activeVipTypeInfo) {
                this.setState({
                    activeVipTypeInfo,
                });
            }
        }
    }

    onChangeCheckBox = (e: any) => {
        this.setState({
            checked: e.target.checked,
        });
    };

    contactClick = (e: React.MouseEvent) => {
        window.open(
            'https://818ps.com/apiv2/udesk',
            '_blank',
            'height=544, width=644,toolbar=no,scrollbars=no,menubar=no,status=no',
        );
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    };

    selectVipType = (e: React.MouseEvent<HTMLDivElement>) => {
        const { vipType } = e.currentTarget.dataset;
        this.props.setActiveVipType(vipType);
        assetManager.setPv_new(9004, { additional: { s0: vipType, i0: this.props.info?.is_company_temp } });
        const activeVipTypeInfo = this.props.vipInfo?.[this.key]?.find((item) => item.vip_type === vipType);
        if (activeVipTypeInfo) {
            this.setState({
                activeVipTypeInfo,
                qrcode: undefined,
            });
        }
        this.getPayQrcode(vipType);
    };

    checkPay = () => {
        assetManager.checkeAIPayStatus(this.time).then((res) => {
            if (res.stat == 1) {
                clearInterval(this.payStatusTimer);
                this.setState({
                    payResult: 'success',
                });
                assetManager.setPv_new(9005, { additional: { i0: this.props.info?.is_company_temp } });
            }
        });
    };

    getPayQrcode = async (vipType?: string) => {
        try {
            if (gettingQrcode || !this.props.activeVipType) {
                return;
            }
            gettingQrcode = true;
            // this.setState({
            //     qrcode: undefined,
            //     payTimeout: false,
            //     payResult: undefined,
            // });
            const res = await assetManager.getVipPayQrcode(
                vipType || this.props.activeVipType,
                'ueEditor',
                this.props.vipInfo?.classify || '1',
                false,
            );
            if ((res.uin_id && res.unifyPayQrcode) || res.pop_tencent_flag) {
                this.setState({
                    qrcode: res,
                    payTimeout: false,
                });
                if (res.uin_id && res.unifyPayQrcode) {
                    this.time = Number(new Date()) / 1000;
                    clearInterval(this.payStatusTimer);
                    this.payStatusTimer = window.setInterval(this.checkPay, 4000);
                    const timer = window.setTimeout(() => {
                        this?.setState?.({
                            payTimeout: true,
                        });
                        clearTimeout(timer);
                    }, 1000 * 180);
                }
            }
        } catch (error) {
            console.error(error);
        } finally {
            gettingQrcode = false;
        }
    };

    refreshQrcode = () => {
        if (this.state.payTimeout) {
            this.getPayQrcode();
        }
    };

    onTencentSubmit = (validateStatus: number) => {
        assetManager.setPv_new(7853, {
            additional: { s0: this.state.qrcode?.pop_tencent_flag, s1: 'editor', i0: this.props.info.is_company_temp },
        });
        switch (validateStatus) {
            case -1:
            case 0:
                // 验证失败
                assetManager.setPv_new(7858, {
                    additional: {
                        s0: this.state.qrcode?.pop_tencent_flag,
                        s1: 'editor',
                        i0: this.props.info.is_company_temp,
                    },
                });
                return;
            case -2:
                // 达到限制
                assetManager.setPv_new(7858, {
                    additional: {
                        s0: this.state.qrcode?.pop_tencent_flag,
                        s1: 'editor',
                        i0: this.props.info.is_company_temp,
                    },
                });
                this.props.hide();
                return;
            case 1:
                // 验证成功
                this.getPayQrcode();
                this.setState({
                    showTecentPop: false,
                });
                assetManager.setPv_new(7854, {
                    additional: {
                        s0: this.state.qrcode?.pop_tencent_flag,
                        s1: 'editor',
                        i0: this.props.info.is_company_temp,
                    },
                });
        }
    };

    checkThreeableAuth = async (data: {
        name: string;
        mobile: string;
        unique_id: string;
        card_id: string;
        bank_card: string;
    }) => {
        return await assetManager.tencentChenckThreeableAuth(data);
    };

    getUniqueId = async ({ bank_card = '' }) => {
        return await assetManager.getUniqueId(this.props.user?.tel, bank_card);
    };

    onClose = () => {
        assetManager.setPv_new(9003, { additional: { i0: this.props.info?.is_company_temp } });
        this.props.hide();
    };
    /**
     * 关闭腾讯验证弹窗
     */
    setTencentPopVisible = (value = false) =>{
        this.setState({
            showTecentPop: value
        })
    }

    setPublicTransfer = (show: boolean) => {
        this.setState({
            showPublicTransfer: show,
        });
    };

    render() {
        const { user, vipInfo, activeVipType, hide, info, vipTemplateNumMap } = this.props;
        const {showTecentPop, payResult, qrcode} = this.state;

        if (payResult) {
            return <PayResult result={payResult} hide={hide} info={info} />;
        }
        let tencentModal = null, pop_tencent_flag = true;
        switch (qrcode?.pop_tencent_flag) {
            // 充值受限 联系客服
            case -1:
                tencentModal = <TgsModal.ContactServiceAgent onClose={hide}></TgsModal.ContactServiceAgent>;
                break;
            // 三要素认证;
            case 2:
                tencentModal = 
                    <TgsModal.TgsLeftTencentValidateBox
                        phoneNum={user?.tel}
                        submitValidate={this.checkThreeableAuth}
                        onSubmit={this.onTencentSubmit}
                        getUniqueId={this.getUniqueId}
                        validateElementNums={3}
                        closeable={true}
                        onClose={this.setTencentPopVisible.bind(this, false)}
                    ></TgsModal.TgsLeftTencentValidateBox>
                ;
                break;
            // 四要素认证
            case 3:
                tencentModal = 
                    <TgsModal.TgsLeftTencentValidateBox
                        phoneNum={user?.tel}
                        submitValidate={this.checkThreeableAuth}
                        onSubmit={this.onTencentSubmit}
                        getUniqueId={this.getUniqueId}
                        validateElementNums={4}
                        closeable={true}
                        onClose={this.setTencentPopVisible.bind(this, false)}
                    ></TgsModal.TgsLeftTencentValidateBox>
                    break;
            default:
                pop_tencent_flag =  false;
                break;
        }
        const tempNum =
            info.is_company_temp !== 1 ? vipTemplateNumMap?.personal_commercial : vipTemplateNumMap?.company;
        return (
            <>
                {/**腾讯认证 */}
                {showTecentPop && pop_tencent_flag && (
                    <div className="pop_tencent_flag_mask">
                        {/* <span className="iconfont icon-quxiao close"  onClick={()=>{
                            this.setTencentPopVisible(false)
                        }}></span> */}
                        {tencentModal}
                    </div>
                )}

                {/**对公转账 */}
                {this.state.showPublicTransfer && (
                    <PublicTransfer
                        selectedVipType={this.state.activeVipTypeInfo}
                        setPublicTransfer={() => {
                            this.setPublicTransfer(false);
                        }}
                        qrCodeData={this.state.qrcode}
                    />
                )}
                <div className="vip_recharge_new">
                    <div className="vip_recharge_top">
                        <div className="vip_recharge_header">
                            <span className="vip_recharge_title">继续下载？成为VIP吧！</span>
                            <span className="vip_recharge_download" onClick={this.onClose}>
                                放弃下载
                            </span>
                        </div>

                        <div className="vip_recharge_features">
                            <div className="feature_item">
                                <span className="feature_icon">高清</span>
                                <span className="feature_text">极速下载 无水印原图</span>
                            </div>
                            <div className="feature_item">
                                <span className="feature_icon">{info.is_company_temp === 1 ? '正版' : '商用'}</span>
                                <span className="feature_text">内容原创设计 免费下载</span>
                            </div>
                            <div className="feature_item">
                                <span className="feature_icon">海量</span>
                                <span className="feature_text">{tempNum}万+ 模版任意使用</span>
                            </div>
                            <div className="feature_item">
                                <span className="feature_icon">实用</span>
                                <span className="feature_text">在线编辑，云端存储</span>
                            </div>
                        </div>
                    </div>
                    {info.is_company_temp === 1 ? (
                        <div className="vip_recharge_company">
                            <div className="vip_recharge_right">
                                <div className="vip_recharge_user">
                                    <img className="vip_recharge_image" src={user.avatar}></img>
                                    <span className="vip_recharge_username">{user.userName}</span>
                                </div>
                                <div className="vip_recharge_vip_text">
                                    <span>请选择 </span>
                                    <span className="vip_color">企业商用VIP</span>
                                    <span> 会员服务</span>
                                </div>
                                <div className="vip_recharge_vip_list">
                                    {vipInfo?.vip_list?.map((item, index) => {
                                        return (
                                            <div
                                                className={`vip_recharge_vip_list_item ${item.vip_type === activeVipType ? 'active' : ''}`}
                                                key={item.vip_type}
                                                data-vip-type={item.vip_type}
                                                onClick={this.selectVipType}
                                            >
                                                {Number(item.vip_type) === 243 && <div className="firstChoice"></div>}
                                                {Number(item.vip_type) === 278 && <div className="suggestion"></div>}
                                                <div className="vip_recharge_vip_name">
                                                    <span className="vip_recharge_name_text">{item.name}</span>
                                                </div>
                                                <div className="vip_recharge_discount">
                                                    {Number(item.origin_price) > 0 && (
                                                        <>
                                                            <span className="vip_recharge_original_price">
                                                                {item.origin_price}
                                                            </span>
                                                            <Tooltip
                                                                title={() => (
                                                                    <div style={{ color: '#1F1A1B', fontSize: 12 }}>
                                                                        {vipInfo?.slogn.date}
                                                                        <br />
                                                                        {'恢复标准价'}
                                                                    </div>
                                                                )}
                                                                color="#fff"
                                                                placement="right"
                                                            >
                                                                <i className="iconfont icon-tishi1"></i>
                                                            </Tooltip>
                                                        </>
                                                    )}
                                                </div>
                                                <div className="vip_recharge_vip_price">
                                                    <span className="vip_recharge_price_number">{item.price}</span>
                                                    <span className="vip_recharge_price_unit">{item.period}</span>
                                                </div>
                                                <div className="level_name">
                                                    <span className="txt">{item.level_name}</span>
                                                    <span>
                                                        （
                                                        <span style={{ color: `rgba(245, 35, 3, 1)`, fontWeight: 600 }}>
                                                            {item.amount_max_str}
                                                        </span>
                                                        /天下载）
                                                    </span>
                                                </div>
                                                {Number(item.vip_type) === 243 ? (
                                                    <div className="admission">
                                                        <span className="highColor">{item.cunch}</span>储存空间 &nbsp;
                                                        <span className="highColor">{item.admission}人</span>
                                                        使用/限购1次
                                                    </div>
                                                ) : (
                                                    <div className="admission">
                                                        <span className="highColor">{item.cunch}</span>储存空间 &nbsp;
                                                        <span className="highColor">{item.admission}个</span>下载账号
                                                    </div>
                                                )}
                                            </div>
                                        );
                                    })}
                                </div>
                                <div className="qr_code_header">
                                    <span>扫码立即开通，继续下载</span>
                                </div>
                                <div className={`vip_recharge_pay_box ${this.state.checked ? '' : 'hide'}`}>
                                    <div className="vip_recharge_pay_qrcode">
                                        {this.state.checked && this.state.qrcode?.unifyPayQrcode && (
                                            <img
                                                className="vip_recharge_qrcode_image"
                                                src={this.state.qrcode?.unifyPayQrcode}
                                            />
                                        )}
                                        {this.state.checked && this.state.payTimeout && (
                                            <div className="vip_recharge_qrcode_timeout" onClick={this.refreshQrcode}>
                                                二维码已过期
                                                <br />
                                                点击刷新
                                            </div>
                                        )}
                                    </div>
                                    <div
                                        className="vip_recharge_public_transfer"
                                        onClick={() => {
                                            assetManager.setPv_new(4692, { additional: { s0: 'ueEidtor' } });
                                            this.setPublicTransfer(true);
                                        }}
                                    >
                                        <div className="logo"></div>
                                        <div className="for-public-txt">对公转账</div>
                                    </div>
                                    <div className="vip_recharge_pay_tip">
                                        <img
                                            className="vip_recharge_wechat"
                                            src={`${imgHost}/index_img/editorV7.0/vip_type_7.png`}
                                        />
                                        <img
                                            className="vip_recharge_alipay"
                                            src={`${imgHost}/index_img/editorV7.0/vip_type_8.png`}
                                        />
                                        <span>微信/支付宝扫码支付</span>
                                    </div>
                                </div>

                                <div className="vip_recharge_agreement">
                                    <div className="vip_recharge_license">
                                        <Checkbox
                                            checked={this.state.checked}
                                            onChange={this.onChangeCheckBox}
                                        ></Checkbox>
                                        <span className="vip_recharge_license_text">
                                            我已阅读并同意
                                            <a href="https://818ps.com/page/15" target="_blank" rel="noreferrer">
                                                《VRF (VIP Royalty-Free ) 企业会员商业使用授权协议》。
                                            </a>
                                        </span>
                                    </div>
                                    <div className="vip_recharge_agreement_tip">
                                        <span>所有充值均可享受速开发票服务，详情请</span>
                                        <span className="vip_recharge_contact" onClick={this.contactClick}>
                                            联系客服
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div className="bonus_features">
                                <div className="bonus_text">赠送VIP增值特权：</div>
                                <div className="bonus_items">
                                    {/* 去掉赠字 */}
                                    <div className="bonus_item">
                                        {this.state.activeVipTypeInfo?.gift_info?.slice(1)}
                                    </div>
                                    <div className="bonus_item">
                                        {this.state.activeVipTypeInfo?.give_kt}次智能抠图次数
                                    </div>
                                    <div className="bonus_item">
                                        {' '}
                                        · {this.state.activeVipTypeInfo?.give_draw}张AI绘图
                                    </div>
                                    <div className="bonus_item">
                                        {' '}
                                        · {this.state.activeVipTypeInfo?.give_word}字AI写作
                                    </div>
                                    <div className="bonus_item">
                                        · {this.state.activeVipTypeInfo?.give_template_precis}次智能设计次数
                                    </div>
                                    <Tooltip title={() => <div style={{ fontSize: 12 }}>增值权益VIP过期后失效</div>}>
                                        <i className="iconfont icon-wenhao" style={{ fontSize: 12 }}></i>
                                    </Tooltip>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <>
                            <div className="vip_recharge_right">
                                <div className="vip_recharge_user">
                                    <img className="vip_recharge_image" src={user.avatar}></img>
                                    <span className="vip_recharge_username">{user.userName}</span>
                                </div>
                                <div className="vip_recharge_vip_text">
                                    <span>请选择 </span>
                                    <span className="vip_color">个人商用VIP</span>
                                    <span> 会员服务</span>
                                </div>
                                <div className="vip_recharge_vip_list">
                                    {vipInfo?.vip_type
                                        ?.filter((item) => item.vip_is_show)
                                        .map((item, index) => {
                                            return (
                                                <div
                                                    className={`vip_recharge_vip_list_item ${item.vip_type === activeVipType ? 'active' : ''}`}
                                                    key={item.vip_type}
                                                    data-vip-type={item.vip_type}
                                                    onClick={this.selectVipType}
                                                >
                                                    <div className="vip_recharge_vip_name">
                                                        <img
                                                            className="vip_recharge_vip_icon"
                                                            src={vipIconList[item.icon_index]?.url}
                                                        />
                                                        <span className="vip_recharge_name_text">
                                                            {item.short_name}
                                                        </span>
                                                    </div>
                                                    <div className="vip_recharge_discount">
                                                        {Number(item.origin_price) > 0 && (
                                                            <>
                                                                <span className="vip_recharge_original_price">
                                                                    {item.original_price_s}
                                                                </span>
                                                                <Tooltip
                                                                    title={() => (
                                                                        <div style={{ color: '#1F1A1B', fontSize: 12 }}>
                                                                            {item.expiration_time_s}
                                                                        </div>
                                                                    )}
                                                                    color="#fff"
                                                                >
                                                                    <i className="iconfont icon-tishi1"></i>
                                                                </Tooltip>
                                                                <div className="vip_recharge_discount_text">
                                                                    {item.slogn_s}
                                                                </div>
                                                            </>
                                                        )}
                                                    </div>
                                                    <div className="vip_recharge_vip_price">
                                                        <span className="vip_recharge_price_number">
                                                            {item.price_orange_s?.price}
                                                        </span>
                                                        <span className="vip_recharge_price_unit">
                                                            {item.price_orange_s?.unit}
                                                        </span>
                                                    </div>
                                                    <div className="vip_recharge_download_text">
                                                        {item.rights_s1?.map((t, i) => {
                                                            return (
                                                                <span
                                                                    key={i}
                                                                    className={`${t.heightLight ? 'vip_recharge_download_text_heightLight' : ''}`}
                                                                >
                                                                    {t.text}
                                                                </span>
                                                            );
                                                        })}
                                                    </div>
                                                    <span className="vip_recharge_edit_text">编辑次数海量</span>
                                                </div>
                                            );
                                        })}
                                </div>
                                <div className="qr_code_header">
                                    <span>扫码立即开通，继续下载</span>
                                </div>
                                {!!this.state.qrcode?.vip_deduction_price && (
                                    <div className="vip_recharge_current_price_label">
                                        (实付
                                        <span className="origin_price">{this.state.qrcode?.price}</span>
                                        元，升级VIP可抵扣
                                        <span className="deduction_price">
                                            {this.state.qrcode?.vip_deduction_price}
                                        </span>
                                        元)
                                    </div>
                                )}

                                <div className={`vip_recharge_pay_qrcode ${this.state.checked ? '' : 'hide'}`}>
                                    {this.state.checked && this.state.qrcode?.unifyPayQrcode && (
                                        <img
                                            className="vip_recharge_qrcode_image"
                                            src={this.state.qrcode?.unifyPayQrcode}
                                        />
                                    )}
                                    {this.state.checked && (qrcode?.pop_tencent_flag === 2 || qrcode?.pop_tencent_flag === 3) && (
                                        <div className="vip_recharge_auth_box">
                                            为了您的账号安全
                                            <br />
                                            请先进行安全验证
                                            <div className="vip_recharge_auth_btn"  onClick={()=>this.setTencentPopVisible(true)}>
                                                <span className='icon'>
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="11" height="11" viewBox="0 0 11 11" fill="none">
                                                        <path d="M1.375 2.12117L5.50197 0.916748L9.625 2.12117V4.59114C9.625 7.18725 7.96359 9.49203 5.5006 10.3127C3.03694 9.49205 1.375 7.18675 1.375 4.58999V2.12117Z" fill="white" stroke="white" stroke-width="0.6875" stroke-linejoin="round"/>
                                                        <path d="M3.4375 5.27083L5.04167 6.875L7.79167 4.125" stroke="#43C93E" stroke-width="0.6875" stroke-linecap="round" stroke-linejoin="round"/>
                                                    </svg>
                                                </span>
                                                <span>立即验证</span>
                                            </div>
                                        </div>
                                    )}
                                    {this.state.checked && this.state.payTimeout && (
                                        <div className="vip_recharge_qrcode_timeout" onClick={this.refreshQrcode}>
                                            二维码已过期
                                            <br />
                                            点击刷新
                                        </div>
                                    )}
                                </div>
                                <div className="vip_recharge_pay_tip">
                                    <img
                                        className="vip_recharge_wechat"
                                        src={`${imgHost}/index_img/editorV7.0/vip_type_7.png`}
                                    />
                                    <img
                                        className="vip_recharge_alipay"
                                        src={`${imgHost}/index_img/editorV7.0/vip_type_8.png`}
                                    />
                                    <span>微信/支付宝扫码支付</span>
                                </div>
                                <div className="vip_recharge_agreement">
                                    <div className="vip_recharge_license">
                                        <Checkbox
                                            checked={this.state.checked}
                                            onChange={this.onChangeCheckBox}
                                        ></Checkbox>
                                        <span className="vip_recharge_license_text">
                                            我已阅读并同意
                                            <a href="https://818ps.com/page/5" target="_blank" rel="noreferrer">
                                                《VRF (VIP Royalty-Free ) 个人会员商业使用授权协议》。
                                            </a>
                                        </span>
                                    </div>
                                    <div className="vip_recharge_agreement_tip">
                                        <span>所有充值均可享受速开发票服务，详情请</span>
                                        <span className="vip_recharge_contact" onClick={this.contactClick}>
                                            联系客服
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div className="bonus_features">
                                <div className="bonus_text">赠送VIP增值特权：</div>
                                <div className="bonus_items">
                                    {this.state.activeVipTypeInfo?.rights_s2?.bar_s1?.content.information.map(
                                        (t, i) => {
                                            return (
                                                <div className="bonus_item" key={i}>
                                                    {t.information.map((t1, i1) => {
                                                        return (
                                                            <span
                                                                key={i1}
                                                                className={`vip_recharge_text_14 ${
                                                                    t1.heightLight
                                                                        ? 'vip_recharge_rights_text_heightLight'
                                                                        : ''
                                                                }`}
                                                            >
                                                                {t1.text}
                                                            </span>
                                                        );
                                                    })}
                                                </div>
                                            );
                                        },
                                    )}
                                    <Tooltip title={() => <div style={{ fontSize: 12 }}>增值权益VIP过期后失效</div>}>
                                        <i className="iconfont icon-wenhao" style={{ fontSize: 12 }}></i>
                                    </Tooltip>
                                </div>
                            </div>
                        </>
                    )}
                </div>
            </>
        );
    }
}

interface IVipRechargeProps {
    open: boolean;
    hide: () => void;
    info: ITemplateInfo;
}

export function VipRecharge(props: IVipRechargeProps) {
    const [vipInfo, setvipInfo] = useState<IVipInfo>();
    const [activeVipType, setActiveVipType] = useState('');

    useEffect(() => {
        if (props.open && !vipInfo) {
            try {
                if (props.info.is_company_temp === 1) {
                    assetManager.getFirmVipList().then((res) => {
                        if (res?.code === 10000) {
                            console.log(res.data.vip_list);
                            if (res.data.vip_list?.find((i) => i.vip_type === '243')) {
                                setActiveVipType('243');
                            }
                            setvipInfo(res.data);
                        }
                    });
                } else {
                    assetManager.getPersionCommercialVipList().then((res) => {
                        if (res?.code === 1) {
                            res.data.vip_type?.reverse();
                            if (res.data.vip_type?.find((i) => i.vip_type === '299')) {
                                setActiveVipType('299');
                            }
                            setvipInfo(res.data);
                        }
                    });
                }
            } catch (error) {}
        }
        if (props.open) {
            if (vipInfo?.vip_type) {
                const activeVipTypeInfo = vipInfo?.vip_type.find((item) => item.vip_type === '299');
                if (activeVipTypeInfo) {
                    setActiveVipType(activeVipTypeInfo.vip_type);
                }
            }
            assetManager.setPv_new(9002, {
                additional: {
                    i2: 1,
                    i0: props.info.is_company_temp,
                    i4: 1,
                },
            });
        }
    }, [props.open]);

    return (
        <Modal
            open={props.open}
            centered
            footer={false}
            destroyOnClose
            modalRender={() => (
                <div style={{ pointerEvents: 'initial' }}>
                    <VipRechargeContent
                        vipInfo={vipInfo}
                        activeVipType={activeVipType}
                        setActiveVipType={setActiveVipType}
                        hide={props.hide}
                        info={props.info}
                    />
                </div>
            )}
            width="1062"
        />
    );
}
