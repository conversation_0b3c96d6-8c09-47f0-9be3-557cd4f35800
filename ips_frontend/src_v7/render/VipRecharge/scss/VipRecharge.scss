.vip_recharge_new {
    display: flex;
    border-radius: 24px;
    overflow: hidden;
    contain: layout;
    pointer-events: initial;
    isolation: isolate;

    .vip_recharge_left {
        width: 260px;
        height: 630px;
        position: relative;
        overflow: hidden;

        .vip_recharge_left_bg {
            position: absolute;
            z-index: 1;
            top: 0;
            left: 0;
            right: 0;
        }

        .vip_recharge_left_content {
            position: relative;
            z-index: 2;
            flex-direction: column;
            align-items: flex-start;
            width: 260px;
            height: 630px;
            display: flex;
            --text-color: #f9dfa8;
            color: var(--text-color);

            .vip_recharge_left_title {
                margin-top: 34px;
                margin-left: 16px;
                font-size: 24px;
                font-weight: 500;
                line-height: 32px;
            }
            .vip_recharge_left_subtitle {
                margin-top: 10px;
                margin-left: 16px;
                font-size: 14px;
                font-weight: 400;
                line-height: 22px;
                opacity: 0.8;
            }
            .vip_recharge_left_line {
                background-color: var(--text-color);
                width: 49px;
                height: 3px;
                margin-top: 4px;
                margin-left: 14px;
            }
            .vip_recharge_left_title2 {
                margin-top: 55px;
                margin-left: 17px;
                font-size: 20px;
                font-weight: 500;
                line-height: 28px;
            }
            .vip_recharge_left_list {
                width: 228px;
                height: 294px;
                margin-top: 18px;
                margin-left: 16px;

                .vip_recharge_left_list_item {
                    margin-bottom: 12px;
                    height: 22px;
                    font-size: 14px;
                    font-weight: 500;
                    line-height: 22px;
                    display: flex;
                    align-items: center;
                    position: relative;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    .vip_recharge_left_icon {
                        width: 12px;
                        height: 12px;
                    }
                    .vip_recharge_left_list_text {
                        z-index: 0;
                        margin-top: 0;
                        margin-left: 6px;
                        position: relative;
                    }

                    .iconfont {
                        margin-left: 7px;
                        cursor: pointer;
                    }
                }
            }
            .vip_recharge_left_bottom {
                align-items: center;
                height: 20px;
                margin-top: 84px;
                margin-left: 16px;
                font-size: 12px;
                font-weight: 400;
                line-height: 20px;
                display: flex;
                .vip_recharge_left_bottom_line {
                    background-color: var(--text-color);
                    width: 1px;
                    height: 14px;
                    margin-top: 3px;
                    margin-right: 4px;
                    margin-left: 3px;
                }
                .vip_recharge_left_union {
                    width: 43px;
                    height: 16px;
                }
            }
        }
    }

    .vip_recharge_right {
        isolation: isolate;
        background-color: #fff;
        width: 802px;
        height: 630px;
        position: relative;
        overflow: hidden;

        .vip_recharge_right_header {
            background: linear-gradient(89.57deg, #ffd867 0.02%, #ffefbf 60.2%, #ffd867 98.92%);
            flex-direction: row;
            align-items: center;
            width: 802px;
            height: 60px;
            display: flex;

            .vip_recharge_right_header_title {
                align-items: center;
                height: 28px;
                margin-left: 309px;
                display: flex;
                position: relative;

                .vip_recharge_text {
                    color: #7a4f05;
                    font-size: 20px;
                    font-weight: 500;
                    line-height: 28px;
                    position: relative;
                    margin-left: 8px;
                }
            }
            .icon-a-guanbi101 {
                width: 18px;
                height: 18px;
                font-size: 18px;
                line-height: 18px;
                margin-left: 279px;
                display: block;
                cursor: pointer;
            }
        }
        .vip_recharge_user {
            width: 760px;
            height: 28px;
            margin-top: 12px;
            margin-left: 19px;
            display: flex;
            align-items: center;
            .vip_recharge_image {
                box-sizing: border-box;
                border: 1px solid #e9e8e8;
                border-radius: 50%;
                width: 28px;
                height: 28px;
            }
            .vip_recharge_username {
                color: #000;
                margin-left: 8px;
                font-size: 12px;
                font-weight: 400;
                line-height: 20px;
            }
            .vip_recharge_vip_text {
                text-align: right;
                margin-top: 4px;
                margin-left: 505px;
                line-height: 20px;
                color: #000;
                font-size: 12px;
                font-weight: 400;
                line-height: 20px;

                .vip_color {
                    color: #c70;
                    font-weight: 500;
                }
            }
        }
        .vip_recharge_vip_list {
            align-items: center;
            width: 765px;
            height: 151px;
            margin-top: 16px;
            margin-left: 14px;
            display: flex;
            justify-content: space-between;
            position: relative;

            .vip_recharge_vip_list_item {
                box-sizing: border-box;
                background-color: #fff;
                border: 1px solid var(---, #e9e8e8);
                border-radius: 12px;
                flex-direction: column;
                align-items: center;
                width: 180px;
                height: 151px;
                display: flex;
                position: relative;
                overflow: hidden;
                cursor: pointer;

                &.active {
                    background-color: #fffcf8;
                    border: 2px solid #e9ce8a;
                }
                .vip_recharge_vip_name {
                    width: 155px;
                    height: 22px;
                    margin-top: 9px;
                    padding: 0 10px;
                    display: flex;
                    justify-content: center;

                    .vip_recharge_name_text {
                        color: #1f1a1b;
                        white-space: nowrap;
                        width: max-content;
                        font-size: 14px;
                        font-weight: 500;
                        line-height: 22px;
                    }

                    .vip_recharge_vip_icon {
                        width: 24px;
                        height: 18.24px;
                        flex-shrink: 0;
                        margin-right: 4px;
                    }
                }
                .vip_recharge_discount {
                    height: 20px;
                    margin-top: 4px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    position: relative;

                    .icon-tishi1 {
                        font-size: 12px;
                        line-height: 14px;
                        margin-left: 2px;
                        margin-right: 5px;
                        cursor: pointer;
                        color: red;
                    }

                    .vip_recharge_discount_text {
                        background-color: red;
                        border-radius: 10px;
                        width: 50px;
                        height: 14px;
                        color: #fff;
                        font-size: 12px;
                        font-weight: 600;
                        line-height: 14px;
                        text-align: center;
                    }
                    .vip_recharge_original_price {
                        white-space: nowrap;
                        color: #797676;
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 20px;
                        text-decoration: line-through;
                    }
                }
                .vip_recharge_vip_price {
                    margin-bottom: 6px;
                    text-align: center;
                    color: #cc7700;
                    .vip_recharge_price_number {
                        font-size: 30px;
                        font-weight: 500;
                        line-height: 28px;
                    }
                    .vip_recharge_price_unit {
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 20px;
                    }
                }
                .vip_recharge_download_text {
                    text-align: center;
                    line-height: 20px;
                    position: relative;
                    color: #666;
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 20px;

                    .vip_recharge_download_text_heightLight {
                        color: #c70;
                        font-size: 14px;
                        font-weight: 500;
                        line-height: 22px;
                    }
                }
                .vip_recharge_edit_text {
                    text-align: center;
                    color: #666;
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 20px;
                }
            }
        }

        .vip_recharge_rights {
            margin-top: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #c70;
            font-size: 12px;
            font-weight: 500;
            line-height: 20px;

            .vip_recharge_rights_text {
                padding: 1px 10px;
                margin-left: 10px;
                font-size: 12px;
                font-weight: 400;
                line-height: 20px;
                border-radius: 5px;
                border: 1px solid #c70;
                background: #fffaf3;

                &:last-of-type {
                    margin-right: 10px;
                }
            }
            .vip_recharge_rights_text_heightLight {
                color: red;
                margin: 0 4px;
            }
        }
        .vip_recharge_current_price {
            height: 22px;
            margin-top: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            .vip_recharge_current_price_label {
                white-space: pre;
                font-size: 14px;
                font-weight: 400;
                line-height: 22px;
                color: #4c4849;

                .vip_recharge_current_price_price {
                    color: #1f1a1b;
                }
            }
            .vip_recharge_desc {
                width: 180px;
                height: 22px;
                margin-left: 6px;
                color: #c70;
                font-size: 14px;
                font-weight: 500;
                line-height: 22px;
            }
        }
        .vip_recharge_pay_box {
            position: relative;
            &.hide {
                opacity: 0;
            }

            .vip_recharge_public_transfer {
                display: flex;
                align-items: center;
                flex-flow: column;
                width: 53px;
                height: 150px;
                padding-top: 8px;
                background: #ffffff;
                border-radius: 4px;
                border: 1px solid #dde2ff;
                font-size: 16px;
                font-weight: 700;
                color: #25325e;
                line-height: 58px;
                position: relative;
                justify-content: space-around;
                cursor: pointer;
                margin-left: 133px;
                position: absolute;
                left: 50%;
                top: 0;
                .for-public-txt {
                    writing-mode: vertical-rl;
                    text-orientation: mixed;
                    margin-top: 8px;
                    font-size: 16px;
                    line-height: 22px;
                    font-weight: 700;
                    letter-spacing: 0.6em;
                }
                .logo {
                    background: url(//js.tuguaishou.com/image/firm/firm-pay/for-pubilic-icon2x.png) no-repeat center;
                    background-size: contain;
                    width: 18px;
                    height: 18px;
                    display: inline-block;
                }
            }
        }
        .vip_recharge_pay_qrcode {
            box-sizing: border-box;
            border: 1px solid #e9e8e8;
            background-color: #fff;
            border-radius: 8px;
            width: 122px;
            height: 122px;
            padding: 4px;
            margin: 22px auto;
            overflow: hidden;
            position: relative;

            &.hide {
                opacity: 0;
            }

            .vip_recharge_qrcode_image {
                display: block;
                object-fit: cover;
                width: 112px;
                height: 112px;
            }
            .vip_recharge_qrcode_timeout {
                position: absolute;
                z-index: 2;
                top: 4px;
                bottom: 4px;
                left: 4px;
                right: 4px;
                background: rgba(255, 255, 255, 0.97);
                text-align: center;
                padding-top: 55px;
                cursor: pointer;
            }
            .vip_recharge_auth_box {
                position: absolute;
                z-index: 2;
                top: 4px;
                bottom: 4px;
                left: 4px;
                right: 4px;
                background: rgba(255, 255, 255, 0.97);
                text-align: center;
                padding-top: 26px;
                color: #363233;
                cursor: pointer;
                .vip_recharge_auth_btn {
                    margin: 20px auto 0 auto;
                    display: flex;
                    width: 115px;
                    height: 30px;
                    padding: 7px 27px 6px 27.5px;
                    justify-content: center;
                    align-items: center;
                    flex-shrink: 0;
                    border-radius: 100px;
                    background: var(---, #0EB52D);
                    color: #fff;
                    box-sizing: border-box;
                    white-space: nowrap;
                    font-size: 12px;
                    .icon {
                        width: 11px;
                        height: 11px;
                        display: block;
                        margin-right: 2px;
                        svg {
                            width: 11px;
                            height: 11px;
                            display: block;
                        }
                    }
                }
            }
        }
        .vip_recharge_pay_tip {
            text-align: center;
            color: #000;
            height: 22px;
            margin-left: 306px;
            font-size: 14px;
            font-weight: 500;
            line-height: 22px;
            display: flex;
            align-items: center;
            .vip_recharge_wechat {
                width: 21px;
                height: 18px;
            }
            .vip_recharge_alipay {
                width: 20px;
                height: 20px;
                margin-left: 8px;
                margin-right: 9px;
            }
        }

        .vip_recharge_agreement {
            flex-direction: column;
            align-items: center;
            margin-top: 20px;
            display: flex;

            .vip_recharge_license {
                width: fit-content;
                height: 17px;
                display: flex;
                align-items: center;
                position: relative;

                .ant-checkbox-checked .ant-checkbox-inner {
                    background-color: #cc7700 !important;
                    border-color: #cc7700 !important;
                }

                .ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover .ant-checkbox-inner {
                    border-color: #cc7700 !important;
                }

                .vip_recharge_license_text {
                    color: #797676;
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 17px;
                    margin-left: 8px;
                    position: relative;

                    a,
                    a:hover,
                    a:active {
                        color: #797676;
                    }
                }
            }
            .vip_recharge_agreement_tip {
                text-align: center;
                margin-top: 8px;
                line-height: 17px;
                color: #797676;
                font-size: 12px;
                font-weight: 400;
                line-height: 17px;

                .vip_recharge_contact {
                    text-decoration: underline;
                }
            }
        }
    }
}

.vip_recharge_new {
    background: linear-gradient(180deg, #ffe7d9 0%, #fff 14.56%);
    border-radius: 12px;
    padding: 10px 0;
    width: 980px;
    min-width: 800px;
    margin: 0 auto;
    height: 720px;
    display: block;
    overflow: hidden;
    box-sizing: border-box;
    .vip_recharge_top {
        height: 83px;

        .vip_recharge_header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 9px;
            position: relative;

            .vip_recharge_title {
                color: #f64216;
                font-size: 25px;
                font-weight: 900;
                margin: auto;
            }

            .vip_recharge_download {
                position: absolute;
                right: 14px;
                top: 13px;
                color: #7a4f05;
                cursor: pointer;
                font-size: 12px;
                opacity: 0.78;
            }
        }

        .vip_recharge_features {
            display: flex;
            justify-content: space-between;
            padding: 0 55px;
            .feature_item {
                display: flex;
                align-items: center;
                gap: 8px;

                .feature_icon {
                    color: #fff;
                    font-size: 15px;
                    font-weight: 600;
                    width: 40px;
                    height: 40px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 32px;
                    background: linear-gradient(180deg, #feb81a 0%, #ff961b 100%);
                    box-shadow: 0px 4px 8px 0px rgba(255, 156, 27, 0.5);
                }

                .feature_text {
                    color: var(---, #1f1a1b);
                    font-size: 15px;
                    font-weight: 500;
                }
            }
        }
    }

    .vip_recharge_right {
        padding: 17px 44px;
        width: 100%;
        box-sizing: border-box;
        margin-top: 22px;
        border-radius: 24px;
        background: #fff;
        box-shadow: 0px -1px 5.2px 0px rgba(246, 66, 22, 0.08);
        .vip_recharge_user {
            margin-left: 0;
            margin-top: 0;
            .vip_recharge_username {
                font-size: 16px;
            }
        }
        .vip_recharge_vip_list {
            width: 100%;
            margin-left: 0;
            justify-content: center;
            .vip_recharge_vip_list_item {
                width: 200px;
                height: 163px;
                margin-left: 30px;
                &:first-child {
                    margin-left: 0;
                }
                &.active {
                    .vip_recharge_download_text,
                    .vip_recharge_edit_text {
                        color: #1f1a1b;
                    }
                }
                .vip_recharge_vip_name {
                    .vip_recharge_name_text {
                        font-size: 16px;
                    }
                }
                .vip_recharge_vip_price {
                    height: 37px;
                    padding-top: 5px;
                    .vip_recharge_price_unit {
                        font-size: 14px;
                    }
                }
                .vip_recharge_download_text,
                .vip_recharge_edit_text {
                    font-size: 15px;
                    .vip_recharge_download_text_heightLight {
                        font-size: 16px;
                        padding: 5px;
                    }
                }
                .vip_recharge_edit_text {
                    margin-top: 4px;
                }
            }
        }

        .vip_recharge_vip_text {
            margin-top: 8px;
            font-size: 16px;
            font-weight: 500;
            .vip_color {
                color: var(---, #c70);
            }
        }

        .qr_code_header {
            background: linear-gradient(90deg, #ff5926 0%, #ff764e 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 3px;
            margin-top: 19px;
            font-size: 24px;
            font-weight: 500;
            text-align: center;
        }
        .vip_recharge_current_price_label {
            color: var(---, #797676);
            text-align: center;
            font-size: 12px;
            font-weight: 400;
            width: 100%;
            .delete_line {
                text-decoration: line-through;
            }
        }
        .vip_recharge_pay_qrcode {
            width: 161px;
            height: 161px;
            margin-top: 7px;
            margin-bottom: 16px;
            .vip_recharge_qrcode_image {
                width: 151px;
                height: 151px;
            }
        }
        .vip_recharge_pay_tip {
            margin: 0 auto;
            justify-content: center;
        }
        .vip_recharge_agreement {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: row;
            .vip_recharge_agreement_tip {
                margin-top: 0;
            }
        }

        .vip_user_info {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 24px;

            .user_avatar {
                width: 40px;
                height: 40px;
                border-radius: 50%;
            }

            .user_name {
                font-size: 16px;
                color: #333;
            }
        }

        .qr_code_section {
            text-align: center;
            margin-bottom: 24px;

            .qr_code_container {
                position: relative;
                width: 200px;
                height: 200px;
                margin: 0 auto;
                margin-bottom: 16px;

                .qr_code {
                    width: 100%;
                    height: 100%;
                }

                .timeout_overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(255, 255, 255, 0.9);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    text-align: center;
                    font-size: 14px;
                    color: #666;
                    cursor: pointer;
                }
            }

            .payment_methods {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 12px;

                .payment_icon {
                    width: 24px;
                    height: 24px;
                }

                span {
                    font-size: 14px;
                    color: #666;
                }
            }
        }
    }

    .agreement_section {
        margin-bottom: 24px;
        text-align: center;

        .ant-checkbox-wrapper {
            font-size: 14px;
            color: #666;

            a {
                color: #ff6b00;
            }
        }
    }

    .bonus_features {
        position: absolute;
        bottom: 0;
        border-radius: 0px 0px 12px 12px;
        background: #fffcf8;
        height: 36px;
        color: #cc7700;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        font-size: 13px;

        .bonus_items {
            display: flex;
            align-items: center;
            gap: 10px;
            .bonus_item {
            }
        }
    }
    .vip_recharge_company {
        .vip_recharge_right {
            .vip_recharge_vip_list_item {
                &.active {
                    border-radius: 12px;
                    border: 2px solid #b6c1fd;
                    background: linear-gradient(92deg, #fff 2.15%, #eeeefe 37.29%, #ebeeff 98.53%);
                    .level_name {
                        .txt {
                            color: #46558d;
                        }
                    }
                }
                .firstChoice {
                    background: url(//js.tuguaishou.com/image/firm/firm-pay/<EMAIL>);
                    background-size: 281px 91px;
                    width: 63px;
                    height: 66px;
                    background-position: -9px -14px;
                    left: -2px;
                    top: -2px;
                    position: absolute;
                }
                .suggestion {
                    background: url(//js.tuguaishou.com/image/firm/firm-pay/<EMAIL>);
                    background-size: 281px 91px;
                    width: 63px;
                    height: 66px;
                    background-position: -85px -16px;
                    left: -2px;
                    top: -2px;
                    position: absolute;
                }
            }
            .vip_color {
                color: var(---, #46558d);
            }
            .vip_recharge_vip_price {
                height: 30px !important;
                .vip_recharge_price_number {
                    font-size: 26px !important;
                    color: #E37300;
                }
            }

            .level_name {
                color: #46558d;
                font-size: 16px;
                .txt {
                    color: #e37300;
                }
            }
            .admission {
                color: #46558d;
                font-size: 14px;
                margin-top: 8px;
                .highColor {
                    font-weight: 600;
                    color: #e37300;
                }
            }
            .vip_recharge_agreement {
                .ant-checkbox-checked .ant-checkbox-inner {
                    background-color: #46558d !important;
                    border-color: #46558d !important;
                }
            }
        }
        .bonus_features {
            background: #f2f4ff;
            color: #46558d;
        }
    }
}
.pop_tencent_flag_mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.45);
    z-index: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 12px;

    .close {
        position: absolute;
        top: 120px;
        right: 90px;
        color: #4C4849;
        cursor: pointer;
        z-index: 2;
    }
}