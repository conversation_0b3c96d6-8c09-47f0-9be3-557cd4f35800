import React, { Component, CSSProperties } from 'react';

import { EventListener } from '@v7_utils/AddEventListener';
import { assetManager } from '@component/AssetManager';
import { emitter } from '@component/Emitter';
import { getProps } from '@component/IPSConfig';
import { ETool } from '@v7_logic/Enum';
import { BackgroundColorArea } from './DefaultColorPanel';
import { EventSubscription } from 'fbemitter';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { SearchHelper } from '@v7_logic/StoreLogic';
import { SearchBox } from '@v7_render/Ui';
import { AssetInfo, AssetItem } from './types';
import { SrollBackgroundPanel } from './ScrollPanel';
import { BackgroundList } from './BackgroundList';
import { IAsset } from '@v7_logic/Interface';
import ColorSelector from '@v7_render/Ui/ColorSelector';
import { CanvasPaintedLogic } from '@v7_logic/CanvasPaintedLogic';
import { ClickOutside } from '@v7_render/Ui';
import { storeDecorator } from '@v7_logic/StoreHOC';
import './scss/NavBackground.scss';
import { IStoreState } from '@v7_store/redux/store';

export function DisplayBackgroundTool(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.BACKGROUND, nav: ETool.BACKGROUND};
}

interface propsStruct {
    active?: boolean;
    isGallery?: boolean;
    pageAttr?: IStoreState['paintOnCanvas']['pageAttr'];
    pageNow?: number;
    scrollbarHeight?: number;
    addBackgroundColorEvent?: (color: string) => void;
    addBackgroundEvent?: (background: any) => void;
}
type recommendData = { keyword: string; id: number };

interface stateStruct {
    needRefresh: boolean;
    assetList: AssetInfo[];
    ratio?: {
        name: string;
    };
    wheelAdd: boolean;
    lastPage: boolean;
    scrollbarHeight: number;
    showType: string;
    inputValue: string;
    showRecommendWords: boolean;
    search: string;
    isDropdownBox: boolean;
    dropdownBoxStyle: { top: string };
    recommandWords: recommendData[];
    ratioList: { id: string; name: string }[];
    assetNewList: AssetItem[];
    showAssetList: string;
    showItemName: string;
    recentList: AssetInfo[];
    showColorPicker: boolean;
}

@storeDecorator((state: IStoreState) => {
    return {
        pageAttr: state.onCanvasPainted.pageAttr,
        pageNow: state.onCanvasPainted.pageInfo?.pageNow || 0,
    };
})
export class BackgroundPanel extends Component<propsStruct, stateStruct> {
    loadListData_timer: ReturnType<typeof setTimeout>;
    moreBtn: number;
    searchInputKeyDownListener: EventListener;
    backgroundNode: HTMLDivElement;
    chanageBackgroundFromRightListener: EventSubscription;
    updateCollectionBackgroundListener: EventSubscription;
    searchInput = React.createRef<HTMLInputElement>();
    firstPost: boolean;

    // 标识listMoreBtn是否显示
    // isShowListMoreBtn: boolean;
    // setScrollTopTimer: NodeJS.Timeout;

    constructor(props: propsStruct) {
        super(props);
        const initAssetList: AssetInfo[] = [];
        // const { search } = storeAdapter.getStore({
        //     store_name: storeAdapter.store_names.paintOnCanvas,
        // });
        this.state = {
            needRefresh: true,
            assetList: initAssetList,
            wheelAdd: false,
            lastPage: false,
            scrollbarHeight: 620,
            showType: '',
            inputValue: '',
            search: '',
            isDropdownBox: false,
            dropdownBoxStyle: { top: '0' },
            recommandWords: [],
            ratioList: [
                {
                    id: '-1',
                    name: '全部',
                },
                {
                    id: '-10',
                    name: '精品',
                },
                {
                    id: '1',
                    name: '横',
                },
                {
                    id: '2',
                    name: '竖',
                },
                {
                    id: '0',
                    name: '方',
                },
            ],
            assetNewList: [],
            showAssetList: 'displayList',
            showItemName: '',
            recentList: [],
            showRecommendWords: false,
            showColorPicker: false,
            // isLoading: false,
        };

        const url_props = getProps();
        if (url_props['isDesigner']) {
            // 获取设计师推荐元素列表 START
            this.getDesignerRecommendAssetList();
            // 获取设计师推荐元素列表 END
        } else {
            // 获取用户推荐元素列表 START
            this.getRecommendAssetList();
            // 获取用户推荐元素列表 END
        }

        this.addAssetList();
        this.updateLoadAssetList();
        // this.chanageBackgroundFromRight();

        this.wheelMoveEvent = this.wheelMoveEvent.bind(this);
        this.searchBtnDownEvent = this.searchBtnDownEvent.bind(this);

        // this.moreBtnClickEmitter();
        this.updateCollectionBackgroundEmitter();
    }

    // 获取设计师推荐元素列表
    getDesignerRecommendAssetList(): void {
        assetManager.getDesignerRecommendAssetList(4).then((data) => {
            data.json().then((resultData) => {
                if (resultData.stat == 1) {
                    this.setState({
                        assetNewList: resultData.data,
                    });
                }
            });
        });
    }

    // 获取用户推荐元素列表
    getRecommendAssetList(): void {
        console.log('xxx')
        assetManager.getRecommendAssetList(4).then((data) => {
            data.json().then((resultData) => {
                if (resultData.stat == 1) {
                    this.setState({
                        assetNewList: (resultData.msg as any[]).filter((i: any) => i.asset_info?.length > 0),
                    });
                }
            });
        });
    }

    /**
     * 更新元素列表
     */
    updateLoadAssetList(): void {
        emitter.addListener('BackgroundUpdateLoadAssetList', () => {
            this.getAssetBackgroundList();
        });
    }
    /**
     * 更新来自右方点击的背景
     */
    chanageBackgroundFromRight(): void {
        this.chanageBackgroundFromRightListener = emitter.addListener('chanageBackgroundFromRight', () => {
            this.setState({});
        });
    }
    getAssetBackgroundList = async () => {
        const { search } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.Search,
        }); // canvasStore.getState().searchManageRedux;
        this.setState({
            wheelAdd: false,
            assetList: [],
            isDropdownBox: false,
        });
        try {
            const res = await assetManager.getAssetList(
                search.backgroundWord,
                search.backgroundPage - 1,
                'background',
                0,
                0,
                0,
                {
                    sceneId: search.backgroundScene,
                    styleId: search.backgroundStyle,
                    ratioId: search.backgroundRatio,
                },
            );
            const resData = await res.json();
            this.setState({
                assetList: resData,
            });
            this.setState({
                wheelAdd: true,
                lastPage: false,
            });
        } catch (error) {
            console.error(error);
        }
    };

    /**
     * 添加下一页内容
     */
    addAssetList(): void {
        const th = this;

        emitter.addListener('BackgroundAddAssetList', () => {
            const searchStoreSearch = storeAdapter.getStore({
                store_name: storeAdapter.store_names.Search,
            }).search;
            const { wheelAdd, lastPage } = this.state;
            if (wheelAdd && !lastPage) {
                th.setState({
                    wheelAdd: false,
                });
                assetManager
                    .getAssetList(
                        searchStoreSearch.backgroundWord,
                        searchStoreSearch.backgroundPage,
                        'background',
                        searchStoreSearch.backgroundK1,
                        searchStoreSearch.backgroundK2,
                        searchStoreSearch.backgroundK3,
                        {
                            sceneId: searchStoreSearch.backgroundScene,
                            styleId: searchStoreSearch.backgroundStyle,
                            ratioId: searchStoreSearch.backgroundRatio,
                        },
                    )
                    .then((data) => {
                        data.json().then((resultData) => {
                            SearchHelper.updateBackgroundPage({
                                search: {
                                    backgroundPage: searchStoreSearch.backgroundPage + 1,
                                },
                            });
                            th.setState({
                                wheelAdd: true,
                            });
                            if (typeof resultData.stat != 'undefined' && resultData.stat == '-10') {
                                this.setState({
                                    lastPage: true,
                                });
                                return;
                            }
                            const tempObject = th.state.assetList;
                            for (const item in resultData) {
                                tempObject.push(resultData[item]);
                            }
                            th.setState({
                                assetList: tempObject,
                            });
                        });
                    });
            }
        });
    }

    componentDidUpdate(nextProps: Readonly<propsStruct>): boolean {
        const isActive = this.props.active;
        const { search } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }); // canvasStore.getState().onCanvasPainted;
        const searchStoreSearch = storeAdapter.getStore({
            store_name: storeAdapter.store_names.Search,
        }).search; // canvasStore.getState().searchManageRedux.search;

        if (isActive === true && this.state.needRefresh === true) {
            const th = this;
            if (this.firstPost) return;
            this.firstPost = true;
            //切换到该面板显示的时候，并且有必要刷新数据的时候，载入数据
            assetManager
                .getAssetList(
                    search.backgroundWord,
                    searchStoreSearch.backgroundPage - 1,
                    'background',
                    search.backgroundK1,
                    search.backgroundK2,
                    search.backgroundK3,
                    {
                        sceneId: search.backgroundScene,
                        styleId: search.backgroundStyle,
                        ratioId: search.backgroundRatio,
                    },
                )
                .then(function (response) {
                    response.json().then(function (newAssetList) {
                        th.setState({ needRefresh: false, assetList: newAssetList });
                        th.firstPost = false;
                        th.setState({
                            wheelAdd: true,
                        });
                    });
                });
        }
        return true;
    }

    /**
     * 滚动条移动
     **/
    wheelMoveEvent(event: React.WheelEvent): boolean {
        const { assetList, showAssetList } = this.state;
        if (!(assetList.length > 0)) {
            return false;
        }
        event.stopPropagation();
        // @ts-ignore
        const clientHeight = event.target.clientHeight;
        // @ts-ignore
        const scrollHeight = event.target.scrollHeight;
        // @ts-ignore
        const scrollTop = event.target.scrollTop;
        const tempNum = 100;
        const isBottom = clientHeight + scrollTop + tempNum >= scrollHeight;

        if (isBottom) {
            if (!this.loadListData_timer) {
                const _this = this;
                emitter.emit('BackgroundAddAssetList');
                this.loadListData_timer = setTimeout(() => {
                    clearTimeout(_this.loadListData_timer);
                    _this.loadListData_timer = null;
                }, 1000);
            }
        }

        // @ts-ignore
        // eslint-disable-next-line react/no-string-refs
        // if (this.refs.listMoreBtn && this.refs.listMoreBtn.style) {
        //     if (scrollTop > 150) {
        //         // @ts-ignore
        //         // eslint-disable-next-line react/no-string-refs
        //         this.refs.listMoreBtn.style.display = 'block';
        //         if (!this.isShowListMoreBtn) {
        //             assetManager.setPv_new(8340, { additional: {} });
        //         }
        //     } else {
        //         // @ts-ignore
        //         // eslint-disable-next-line react/no-string-refs
        //         this.refs.listMoreBtn.style.display = 'none';
        //     }
        // }

        // if (scrollTop > 300) {
        //     this.setState({
        //         isDropdownBox: true,
        //     });
        // } else {
        //     this.setState({
        //         isDropdownBox: false,
        //     });
        // }

        // if (this.setScrollTopTimer) {
        //     clearTimeout(this.setScrollTopTimer);
        // }
        // // 记录滚动高度
        // if (showAssetList != 'displayList') {
        //     OnWheelPaneLogic.setScrollTop({ gallery: { twoType: scrollTop } });
        // }
        // if (showAssetList == 'displayList') {
        //     OnWheelPaneLogic.setScrollTop({ gallery: { oneType: scrollTop } });
        // }
    }
    /**
     * 搜索按钮点击事件
     */
    searchBtnDownEvent(): void {
        const searchInputDom = this.searchInput.current;

        assetManager.setPv_new(2463, { additional: {} });
        assetManager.setPv_new(7101, {
            additional: {
                s1: '背景',
                s2: searchInputDom.value,
            },
        });

        SearchHelper.updateBackgroundWord({
            search: {
                backgroundWord: searchInputDom.value,
            },
        });
        if (this.state.inputValue != '') {
            this.setState({
                showAssetList: 'search',
            });
        } else {
            this.setState({
                showAssetList: 'displayList',
            });
        }
        this.getAssetBackgroundList();

        // emitter.emit('BackgroundUpdateLoadAssetList');

        assetManager.assetSearchStat(searchInputDom.value, 0);
    }

    /**
     * 搜索框获取焦点
     */
    searchInputFocus(): void {
        this.setState({ showRecommendWords: true });
    }

    /**
     * 搜索框失去焦点
     */
    searchInputBlur(): void {
        this.searchInputKeyDownListener?.remove();
        this.setState({ showRecommendWords: false });
    }

    onChange(value: string): void {
        // @ts-ignore
        this.setState({ inputValue: value });
        SearchHelper.updateBackgroundWord({
            search: {
                backgroundWord: value,
            },
        });

        if (value != '') {
            this.setState({
                showAssetList: 'search',
                showRecommendWords: false,
            });
        } else {
            this.setState({
                showAssetList: 'displayList',
                showRecommendWords: false,
            });
        }
        this.getAssetBackgroundList();
        assetManager.assetSearchStat(value, 0);
        assetManager.setPv_new(2463, { additional: {} });
        assetManager.setPv_new(7101, {
            additional: {
                s1: '背景',
                s2: this.state.inputValue,
            },
        });
    }
    /**
     * 点击搜索推荐词
     */
    onClickRecommandWord = (rw: string, e: MouseEvent): void => {
        e.stopPropagation();
        this.setState(
            {
                showAssetList: 'allShow',
                showItemName: rw,
                inputValue: rw,
                showRecommendWords: false,
            },
            () => {
                SearchHelper.updateBackgroundWord({
                    search: {
                        backgroundWord: rw,
                    },
                });

                // emitter.emit('BackgroundUpdateLoadAssetList');
                this.getAssetBackgroundList();
                assetManager.assetSearchStat(rw, 0);
            },
        );
        assetManager.setPagePv_new(3612, '', '', '', rw);
        assetManager.setPv_new(7101, {
            additional: {
                s1: '背景',
                s2: rw,
            },
        });
    };

    componentWillUnmount(): void {
        // this.chanageBackgroundFromRightListener?.remove();
        this.updateCollectionBackgroundListener?.remove();
        SearchHelper.updateBackgroundWord({
            search: {
                backgroundWord: '',
            },
        });
    }

    componentDidMount(): void {
        // this.windowResize(this, document.body.clientHeight - 40);
        this.setState({
            dropdownBoxStyle: {
                top: this.backgroundNode.offsetTop + 58 + 'px',
            },
        });
        this.updateRecentBackgroundList();
        this.getRecommandWords();
        setTimeout(() => {
            emitter.emit('materialAssetDidMount');
        }, 0);
    }

    windowResize(th: any, innerHeight: number): void {
        if (th.props.scrollbarHeight) {
            th.setState({
                scrollbarHeight: th.props.scrollbarHeight,
            });
            return;
        }
        let scrollbarHeight = innerHeight,
            // eslint-disable-next-line prefer-const
            rightLayoutDom = document.getElementsByClassName('rightLayout');
        // backgroundMaterial

        if (rightLayoutDom) {
            // @ts-ignore
            scrollbarHeight = rightLayoutDom[0].offsetHeight - this.backgroundNode.getBoundingClientRect().top + 58;
        }

        th.setState({
            scrollbarHeight: scrollbarHeight,
        });
    }

    //1:素材;2:照片;3:背景;4:文案大全
    // 获取推荐词
    getRecommandWords(): void {
        const th = this;
        assetManager.getRecommendWords(3).then((data) => {
            data.json().then((resultData) => {
                if (resultData.stat == 1) {
                    th.setState({
                        recommandWords: resultData.data,
                    });
                }
            });
        });
    }
    /**
     * 贴纸列表点击全部（点击事件）
     */
    assetItemAllClickEvent(keyword: string, name: string): void {
        SearchHelper.updateBackgroundWord({
            search: {
                backgroundWord: keyword,
            },
        });
        this.setState(
            {
                showAssetList: 'allShow',
                inputValue: keyword,
                showItemName: name,
            },
            () => {
                this.getAssetBackgroundList();
            },
        );
        assetManager.setPv_new(8365, {
            additional: {
                s0: name,
                s1: keyword,
            },
        });
    }
    /**
     * 最近使用 查看更多
     */
    showRecentMore(name: string): void {
        this.setState({
            showAssetList: 'allShow',
            showItemName: name,
            showType: 'recent',
        });
        assetManager.setPv_new(8365, {
            additional: {
                s0: name,
            },
        });
    }
    /**
     * 元素返回全部（点击事件）
     */
    getBackIteemsToAll(): void {
        this.setState({
            showAssetList: 'displayList',
            inputValue: '',
            showType: '',
        });
    }

    /**
     * 添加背景---不搜索，不限时全部的状态下
     */
    addBackgroundEvent(background: AssetInfo, floor?: AssetItem): void {
        if (this.props.addBackgroundEvent) {
            this.props.addBackgroundEvent(background);
            return;
        }
        const { search } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.Search,
        });
        Object.assign(background, {
            rt_searchWord: search.backgroundWord,
            rt_isNowAdd: true,
            rt_ratioId: search.backgroundRatio,
        });
        emitter.emit('ListAddBackground', background);
        assetManager.assetClickWSearch(0, background.id);
        assetManager.setPv_new(2464, { additional: {i4: background?.type} });
        assetManager.setPv_new(7102, {
            additional: {
                i4: background?.type,
                s1: '背景',
                s2: search.backgroundWord,
            },
        });
        // 添加背景到画布---新增埋点-针对aigc
        assetManager.setPv_new(7363, {
            additional: {
                i0: background?.type,
                // 楼层id
                i1: floor?.id,
                // 背景素材id
                i2: background.id,
                // 背景图分类id
                i3: background.kid_1,
                // 是否收藏0 1
                i4: background.isFav ?? 0,
                // 搜索词
                i5: search.backgroundWord,
            },
        });
        this.updateRecentUseList(background);
    }
    /**
     * 更新最近使用列表
     */
    updateRecentUseList(asset: AssetInfo): void {
        const { recentList } = this.state;
        const newRecentList = recentList.filter((item) => item.id !== asset.id);
        newRecentList.unshift(asset);
        this.setState({
            recentList: newRecentList,
        });
    }

    updateCollectionBackgroundEmitter(): void {
        this.updateCollectionBackgroundListener = emitter.addListener('updateCollectionBackground', () => {
            const { isDesigner } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            if (isDesigner) {
                this.getDesignerRecommendAssetList();
            } else {
                this.getRecommendAssetList();
            }
        });
    }
    /**
     * 更新最近使用背景列表
     */
    updateRecentBackgroundList() {
        const th = this;
        assetManager.getAssetHistoryRecord(2).then((data) => {
            data.json().then((resultData) => {
                if (resultData.stat == 1) {
                    th.setState({
                        recentList: resultData.msg,
                    });
                }
            });
        });
    }
    // 收藏
    assetInfoItemCollectionClickEvent(iIndex: number, index: number, e: React.MouseEvent): void {
        const { assetNewList, recentList } = this.state;

        const { isDesigner } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const selectId = assetNewList[iIndex].asset_info[index].id;
        if (assetNewList[iIndex].asset_info[index].isFav === 1) {
            assetNewList[iIndex].asset_info[index] = Object.assign(assetNewList[iIndex].asset_info[index], {
                isFav: 0,
            });
            assetManager.delFavAsset(selectId).then((data) => {
                data.json().then((resultData) => {
                    if (resultData.stat != 1) {
                        assetNewList[iIndex].asset_info[index] = Object.assign(assetNewList[iIndex].asset_info[index], {
                            isFav: 1,
                        });
                    }
                    const recentIndex = recentList.findIndex((item) => item.id === selectId);
                    if (recentIndex !== -1) {
                        recentList[recentIndex] = Object.assign(recentList[recentIndex], { isFav: 0 });
                    }
                });
            });
        } else {
            assetNewList[iIndex].asset_info[index] = Object.assign(assetNewList[iIndex].asset_info[index], {
                isFav: 1,
            });
            assetManager.setFavAsset(selectId).then((data) => {
                data.json().then((resultData) => {
                    if (resultData.stat != 1) {
                        assetNewList[iIndex].asset_info[index] = Object.assign(assetNewList[iIndex].asset_info[index], {
                            isFav: 0,
                        });
                    }
                    const recentIndex = recentList.findIndex((item) => item.id === selectId);
                    if (recentIndex !== -1) {
                        recentList[recentIndex] = Object.assign(recentList[recentIndex], { isFav: 1 });
                    }
                });
            });
        }
        if (!isDesigner) {
            assetManager.setPv_new(4603, { additional: { s0: 'edit_recommend' } });
        }

        this.setState({
            recentList: recentList,
            assetNewList: assetNewList,
        });
        e.stopPropagation();
        e?.nativeEvent.stopPropagation();
    }
    // 收藏最近使用
    assetInfoItemCollectionRecentEvent(iIndex: number, index: number, e: React.MouseEvent): void {
        const { recentList, assetNewList } = this.state;

        const { isDesigner } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const selectId = recentList[index].id;
        if (recentList[index].isFav === 1) {
            recentList[index] = Object.assign(recentList[index], {
                isFav: 0,
            });
            assetManager.delFavAsset(selectId).then((data) => {
                data.json().then((resultData) => {
                    if (resultData.stat != 1) {
                        recentList[index] = Object.assign(recentList[index], {
                            isFav: 1,
                        });
                    }
                    for (let i = 0; i < assetNewList.length; i++) {
                        for (let j = 0; j < assetNewList[i].asset_info.length; j++) {
                            if (assetNewList[i].asset_info[j].id === selectId) {
                                assetNewList[i].asset_info[j] = Object.assign(assetNewList[i].asset_info[j], {
                                    isFav: 0,
                                });
                                break;
                            }
                        }
                    }
                });
            });
        } else {
            recentList[index] = Object.assign(recentList[index], {
                isFav: 1,
            });
            assetManager.setFavAsset(selectId).then((data) => {
                data.json().then((resultData) => {
                    if (resultData.stat != 1) {
                        recentList[index] = Object.assign(recentList[index], {
                            isFav: 0,
                        });
                    }
                    for (let i = 0; i < assetNewList.length; i++) {
                        for (let j = 0; j < assetNewList[i].asset_info.length; j++) {
                            if (assetNewList[i].asset_info[j].id === selectId) {
                                assetNewList[i].asset_info[j] = Object.assign(assetNewList[i].asset_info[j], {
                                    isFav: 1,
                                });
                                break;
                            }
                        }
                    }
                });
            });
        }
        if (!isDesigner) {
            assetManager.setPv_new(4603, { additional: { s0: 'recent' } });
        }

        this.setState({
            recentList: recentList,
            assetNewList: assetNewList,
        });
        e.stopPropagation();
        e?.nativeEvent.stopPropagation();
    }
    /* 处理从搜索以及查看全部的收藏 */
    handRecentAndAssetFav(assetId: string, isFav: 0 | 1, e: MouseEvent) {
        if (!assetId) return;
        const { recentList, assetNewList } = this.state;
        const updateObj: Pick<stateStruct, 'assetNewList' | 'recentList'> = { recentList: [], assetNewList: [] };
        for (let i = 0; i < assetNewList.length; i++) {
            for (let j = 0; j < assetNewList[i].asset_info.length; j++) {
                if (assetNewList[i]?.asset_info[j]?.id === assetId) {
                    assetNewList[i].asset_info[j] = Object.assign(assetNewList[i].asset_info[j], {
                        isFav: isFav,
                    });
                    updateObj.assetNewList = assetNewList;
                    break;
                }
            }
        }
        const recentIndex = recentList.findIndex((item) => item.id === assetId);
        if (recentIndex !== -1) {
            recentList[recentIndex] = Object.assign(recentList[recentIndex], { isFav: isFav });
            updateObj.recentList = recentList;
        }
        if (updateObj.recentList.length || updateObj.assetNewList.length) {
            this.setState(updateObj);
        }
        e.stopPropagation();
    }
    /**
     * 设置颜色
     */
    colorEvent(color: any, isOpacity = false) {
        CanvasPaintedLogic.updateBackGroundColor({
            backgroundColor: color.rgb,
            isOpacity,
            fun_name: 'UPDATE_BACKGROUNDCOLOR',
        });
        emitter.emit('CanvasContentSCTS');
        this.setState({});
    }
    /**
     * 自定义面板设置颜色
     */
    customColorEvent(color: any, isOpacity = false) {
        this.colorEvent(color, isOpacity);
        assetManager.setPv_new(182, {
            additional: {
                s0: 'top',
                s1: 'custom',
            }
        });
    }
    /**
     * 显示隐藏颜色选择器
     */
    swtichColorSelector(value: boolean, e: MouseEvent) {
        if (value === undefined) {
            value = !this.state.showColorPicker;
        }
        if (value) {
            assetManager.setPv_new(7469, {
                additional: {
                    s0: 'background',
                    s1: 'top'
                }
            });
        }
        this.setState({
            showColorPicker: value,
        });
        e?.stopPropagation();
    }

    render(): JSX.Element {
        const {
            wheelAdd,
            showType,
            recommandWords,
            assetNewList,
            showAssetList,
            showItemName,
            recentList,
            assetList,
            showColorPicker,
        } = this.state;
        const {pageAttr, pageNow = 0} = this.props;
        const { showRecommendWords } = this.state;
        const { work, rt_canvas_render_mode } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const backgroundAsset = work.pages[pageNow].assets.find(
            (item: IAsset) => item.meta.type === 'background',
        );
        let currentColor = pageAttr.backgroundColor[pageNow];
        if (!currentColor) {
            currentColor = work.pages[pageNow].backgroundColor || { r: 255, g: 255, b: 255, a: 1 };
        }
        const isOpacity = pageAttr?.backgroundOpacity?.[pageNow];
        const isBackgroundImage = pageAttr.backgroundImage[pageNow];
        const { search } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.Search,
        });

        const myScrollbar = {
            // height: scrollbarHeight,
            overflowX: 'hidden',
            boxSizing: 'border-box',
        } as CSSProperties;

        return (
            <div className={'elementPanel navBackground'}>
                <div className="search">
                    <SearchBox
                        defaultValue={'搜索背景'}
                        onSearch={this.onChange.bind(this)}
                        searchInputFocus={this.searchInputFocus.bind(this)}
                        searchInputBlur={this.searchInputBlur.bind(this)}
                    />
                </div>
                {showRecommendWords && recommandWords.length > 0 && (
                    <div className="recommandWordWrap">
                        <div className="title">推荐搜索</div>
                        <div className="content">
                            {recommandWords.map((item: recommendData) => {
                                return (
                                    <div
                                        className="item"
                                        key={item.id}
                                        onMouseDown={this.onClickRecommandWord.bind(this, item.keyword)}
                                    >
                                        {item.keyword}
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                )}
                <div
                    className="scroll_content_auto"
                    style={myScrollbar}
                    ref={(node) => (this.backgroundNode = node)}
                    key={search.backgroundWord + '-' + search.backgroundStyle + '-' + search.backgroundRatio}
                    onScroll={this.wheelMoveEvent.bind(this)}
                >
                    {recentList.length > 0 && <div>
                        <div className='tab-item'>全部</div>
                        <div className='tab-item' onClick={this.showRecentMore.bind(this, '最近使用')}>最近使用</div>
                    </div>}
                    {rt_canvas_render_mode !== 'board' && <div className="section">
                        <div className="assetListItemDesc">
                            <div className="assetListItemTitle">背景颜色</div>
                            <div
                                className="currentColorBox"
                                onMouseDown={this.swtichColorSelector.bind(this, undefined)}
                            >
                                {isBackgroundImage && isBackgroundImage.resId ? (
                                    <span
                                        className="colorSpan"
                                        style={{ backgroundImage: `url(${isBackgroundImage.rt_imageUrl})` }}
                                    ></span>
                                ) : isOpacity ? (
                                    <span className="opactiySpan"></span>
                                ) : (
                                    <span
                                        className="colorSpan"
                                        style={{
                                            backgroundColor: `rgba(${
                                                currentColor?.r +
                                                ',' +
                                                currentColor?.g +
                                                ',' +
                                                currentColor?.b +
                                                ',' +
                                                currentColor?.a
                                            } )`,
                                        }}
                                    ></span>
                                )}
                                <span className="moreColor"></span>
                            </div>
                        </div>
                        {showColorPicker && (
                            <ClickOutside onClickOutside={this.swtichColorSelector.bind(this, false)}>
                                <ColorSelector
                                    onChange={this.customColorEvent.bind(this)}
                                    onClose={this.swtichColorSelector.bind(this, false)}
                                    style={{ left: '180px', top: '118px', position: 'fixed', zIndex: 9999 }}
                                    initPanelColor={currentColor as any}
                                />
                            </ClickOutside>
                        )}
                        <BackgroundColorArea addBackgroundColorEvent={this.colorEvent.bind(this)} backgroundColor={currentColor} isOpacity={isOpacity}/>
                    </div>}
                    {/* {recentList.length > 0 && showAssetList == 'displayList' && (
                        <div className="section">
                            <div className="assetListItemDesc">
                                <div className="assetListItemTitle">最近使用</div>
                                <div className="assetListItemMore" onClick={this.showRecentMore.bind(this, '最近使用')}>
                                    查看更多
                                </div>
                            </div>
                            <SrollBackgroundPanel
                                type={'recent'}
                                list={recentList}
                                listIndex={0}
                                itemWidth={98}
                                maxHeight={156}
                                bakcgroundAssetId={backgroundAsset?.attribute?.resId}
                                addElementEvent={this.addBackgroundEvent.bind(this)}
                                onHandleFavEvent={this.assetInfoItemCollectionRecentEvent.bind(this)}
                            />
                        </div>
                    )} */}

                    {showAssetList == 'displayList' &&
                        this.state.assetList.length > 0 &&
                        assetNewList.map((item, iIndex) => {
                            return (
                                <div className="section" key={item.id}>
                                    <div className="assetListItemDesc">
                                        <div className="assetListItemTitle">{item.name}</div>
                                        <div
                                            className="assetListItemMore"
                                            onClick={this.assetItemAllClickEvent.bind(this, item.kw, item.name)}
                                        >
                                            查看更多
                                        </div>
                                    </div>
                                    <SrollBackgroundPanel
                                        type={showType}
                                        list={item.asset_info}
                                        floor={item}
                                        listIndex={iIndex}
                                        itemWidth={98}
                                        maxHeight={156}
                                        onHandleFavEvent={this.assetInfoItemCollectionClickEvent.bind(this)}
                                        addElementEvent={this.addBackgroundEvent.bind(this)}
                                    />
                                </div>
                            );
                        })}
                </div>

                {showAssetList == 'search' && (
                    <div className="backgroundListWrap">
                        <div className="assetsItems">
                            <BackgroundList
                                type={'search'}
                                wheelAdd={wheelAdd}
                                columns={3}
                                itemWidth={98}
                                addBackgroundEvent={this.addBackgroundEvent.bind(this)}
                                assetList={this.state.assetList}
                                onHandleFavEvent={this.handRecentAndAssetFav.bind(this)}
                            />
                        </div>
                    </div>
                )}

                {showAssetList == 'allShow' && (
                    <div className="backgroundListWrap">
                        <div className="assetsItems">
                            <div className="assetsItemsTitle" onClick={this.getBackIteemsToAll.bind(this)}>
                                <i className="iconfont icon-zuo"></i> {showItemName}
                            </div>
                            <BackgroundList
                                type={showType}
                                wheelAdd={wheelAdd}
                                columns={3}
                                itemWidth={98}
                                addBackgroundEvent={this.addBackgroundEvent.bind(this)}
                                onHandleFavEvent={this.handRecentAndAssetFav.bind(this)}
                                assetList={showType === 'recent' ? this.state.recentList : this.state.assetList}
                            />
                        </div>
                    </div>
                )}
            </div>
        );
    }
}
