@import '@scss/clientV6.0/Base.scss';
.tgs_headerBar {
    height: 100%;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;

    * {
        pointer-events: initial;
    }

    .headerLeft {
        display: flex;
        align-items: center;
        pointer-events: initial;
        padding: 6px 0 6px 12px;
        background: rgba(255, 255, 255, 0.0627);
        backdrop-filter: blur(4px);
        border-radius: 12px;

        .divider {
            width: 1px;
            height: 22px;
            background: #e9e8e8;
            margin-right: 6px;
        }

        .step {
            position: relative;
            .stepTooltip {
                width: 108px;
                height: 36px;
                line-height: 36px;
                text-align: center;
            }
            i {
                font-size: 22px;
                margin: 0;
            }
            &:hover {
                .stepTooltip {
                    display: block;
                }
            }
        }
        .noStep {
            i {
                color: #979797;
            }
            .stepTooltip {
                display: none;
            }
            &:hover {
                cursor: default;
                background: none;
                .stepTooltip {
                    display: none;
                }
                i {
                    color: #979797;
                }
            }
        }
        .saveBtn {
            color: #a5a3a4;
            position: relative;
            padding: 0 12px;
            margin-right: 4px;
            &:hover {
                color: #1f1a1b;
                .saveTooltip {
                    display: block;
                }
            }
            .saveTooltip {
                // background: #333;
                // border-radius: 6px;
                height: max-content;
                // margin-top: 14px;
                text-align: center;
                width: max-content;
                // position: relative;
                // display: block;
                padding: 15px 15px;
                > div {
                }
            }
            i {
                font-size: 22px;
                color: #1f1a1b;
                // position: relative;
                // top: 2px;
            }
            span {
                margin-left: 3px;
            }
            .autoSaveTipsTxt1 {
                color: #fff;
                font-size: 14px;
                line-height: 20px;
            }
            .autoSaveTipsTxt2 {
                color: #b1b2b7;
                font-size: 12px;
                line-height: 17px;
                margin-top: 5px;
            }
        }
        .saveFlag {
            color: $primaryColorV6_2;
            i {
                color: #666;
            }
        }
        .fileBtn {
            position: relative;
            .filePopover {
                left: 0px;

                visibility: visible;
                z-index: 1;
                opacity: 1;
            }
            &:hover {
                .filePopover {
                    visibility: visible;
                    z-index: 1;
                    opacity: 1;
                }
            }
        }

        .headerNameBox {
            position: relative;
            margin-right: 12px;
            width: 180px;
            .templateName {
                margin-right: 0;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                max-width: 448px;
                background: none;
                opacity: 0;
                pointer-events: none;

                @media screen and (max-width: 1500px) {
                    box-sizing: border-box;
                    max-width: 180px;
                }
            }
            input {
                position: absolute;
                width: 100%;
                top: 0;
                right: 0;
                outline: none;
                border-radius: 8px;
                opacity: 1;
                // border: 1px solid #e9e8e8;
                background: none;
                border: none;
                box-sizing: border-box;
                height: 39px;
                line-height: 38px;
                padding: 0 12px;
                font-weight: 500;
                color: #1f1a1b;
                font-size: 14px;
                // visibility: hidden;
                // opacity: 0;
                visibility: visible;
                opacity: 1;
                border-bottom: 1px dashed transparent;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;

                &:focus {
                    border-bottom: 1px dashed #797676;
                }
                &:hover {
                    border-bottom: 1px dashed #797676;
                }
            }
            .inputShow {
                visibility: visible;
                opacity: 1;
            }
        }
    }
    .headerRight {
        display: flex;
        align-items: center;
        pointer-events: initial;
        padding: 6px 12px 6px 16px;
        background: rgba(255, 255, 255, 0.0627);
        backdrop-filter: blur(4px);
        border-radius: 12px;

        .copyrightBtn {
            position: relative;
            &:hover {
                .businese_hover_tips {
                    visibility: visible;
                    z-index: 1;
                    opacity: 1;
                }
            }

            .icon-bianjiqixin-dingbudaohang-banquan {
                font-size: 20px;
            }
        }
        .rightDownloadBtn {
            // margin-left: 10px;
            height: 38px;
            line-height: 38px;
            position: relative;
            display: inline-block;

            .downloadBtn {
                background: #ff416e;
                border-radius: 5px;
                box-shadow: none;
                height: 38px;
                line-height: 38px;
                padding: 0;
                width: 130px;
                color: #fff;
                cursor: pointer;
                font-size: 14px;
                box-shadow: none;
                position: relative;
            }
            .isTeamDownlaod {
                text-align: left;
            }
            .downloadBox { 
                border-radius: 5px;
                height: 38px;
                line-height: 38px;
                padding-left: 12px;
                width: calc(100% - 12px);
                left: 0;
                position: absolute;
                &:hover {
                    background: #e63b63;
                }
            }
            .noShowArrow {
                text-align: center;
                width: 100%;
                padding-left: 0;
            }
            .downloadTemplTipAreaNew {
                background: #ff416e;
                border-radius: 0 8px 8px 0;
                height: 38px;
                line-height: 38px;
                width: 38px;
                border-left: 1px solid #dd1b2b;
                color: #ff4555;
                display: inline-block;
                font-size: 12px;
                position: absolute;
                right: 0;
                text-align: center;
                top: 0;
                i {
                    font-size: 16px;
                    margin-right: 0;
                    color: #fff;
                }
                &:hover {
                    background: #e63b63;
                }
            }
            .downloadTemplTipAreaNewV2 {
                background: #ff416e;
                border-radius: 0 5px 5px 0;
                height: 38px;
                line-height: 38px;
                width: 38px;
                color: #ff4555;
                display: inline-block;
                font-size: 12px;
                position: absolute;
                right: 0;
                text-align: center;
                top: 0;
                .downloadTempIcon {
                    height: 38px;
                    border-left: 1px solid #d7335a;
                    > i {
                        font-size: 16px;
                        margin-right: 0;
                        color: #fff;
                    }
                }
                &:hover .downloadTempIcon {
                    border-radius: 5px 0 0 5px;
                    border-right: 1px solid #d7335a;
                    transform: rotate(180deg);
                }
                &:hover {
                    background: #e63b63;
                }
                .downloadTempPanel {
                    display: none;
                    border-top: 8px solid transparent;
                    cursor: auto;
                    position: absolute;
                    right: 0;
                    top: 38px;
                }
                &:hover .downloadTempPanel,
                .downloadTempPanel:hover {
                    display: block;
                }
            }
            .triangleDropDown {
                position: absolute;
                width: 198px;
                background: #ffffff;
                box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.12);
                border-radius: 4px;
                border: 1px solid #d6dbe1;
                top: 58px;
                right: 0;
                color: #202020;
                padding: 6px 0;
                min-height: 0;
                top: 48px;
                .item {
                    width: 198px;
                    height: 38px;
                    padding-left: 44px;
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;
                    color: #202020;
                    width: calc(100% - 44px);
                    &:hover {
                        background: #eceef5;
                    }
                }
            }
            .online_detail_download_type {
                position: absolute;
                width: 246px;
                height: 127px;
                background: rgba(255, 255, 255, 1);
                box-shadow: 1px 1px 10px 0px rgba(205, 195, 195, 1);
                border-radius: 6px;
                right: 5px;
                padding: 0 20px;
                color: #202020;
                line-height: 42px;
                cursor: default;
                display: none;
                .type_title {
                    font-size: 14px;
                    font-weight: 600;
                }
                .download_type_item {
                    line-height: 30px;
                    cursor: pointer;
                    .cus_raido {
                        display: inline-block;
                        width: 20px;
                        height: 20px;
                        background: rgba(255, 255, 255, 1);
                        border: 1px solid rgba(219, 223, 229, 1);
                        border-radius: 50%;
                        vertical-align: middle;
                        position: relative;
                        cursor: pointer;
                        &.check {
                            &::after {
                                content: '';
                                position: absolute;
                                width: 14px;
                                height: 14px;
                                border-radius: 50%;
                                background: #ff4555;
                                position: absolute;
                                left: 50%;
                                top: 50%;
                                transform: translate(-50%, -50%);
                            }
                        }
                    }
                    .download_type_txt {
                        display: inline-block;
                        vertical-align: middle;
                        font-size: 14px;
                    }
                }
            }
            .online_detail_download_type {
                padding-top: 12px;
                top: 50px;
                &::after {
                    content: '';
                    width: 100%;
                    height: 10px;
                    position: absolute;
                    top: -10px;
                }
            }
            &:hover {
                .online_detail_download_type {
                    display: block;
                }
                color: #661216;
            }
        }
        .headerBtnItem {
            margin-right: 8px;
        }
    }
    .topRightCommandArea {
        display: none;
    }
    .headerBtnItem {
        align-items: center;
        cursor: pointer;
        height: 38px;
        line-height: 38px;
        padding: 0 8px;
        margin-right: 8px;
        border-radius: 8px;
        color: #1f1a1b;
        font-size: 14px;
        font-weight: 500;
        display: flex;
        align-items: center;
        white-space: nowrap;

        & > i {
            font-size: 14px;
            margin-right: 6px;
        }
        &:hover {
            background: #fff;
        }

        &.userShare {
            box-sizing: border-box;
            border: 1px solid #fff;
        }
    }
    .class_tooltip {
        position: absolute;
        top: 50px;
        display: none;
        transform: translate(-50%);
        left: 50%;
        background: #333;
        border-radius: 4px;
        color: #fff;
        font-size: 12px;

        &::after {
            content: '';
            border-color: #333 transparent;
            border-style: dashed dashed solid;
            border-width: 0 8px 8px;
            font-size: 0;
            left: 50%;
            line-height: 0;
            top: -8px;
            position: absolute;
            -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
        }
    }
    .class_popover {
        visibility: hidden;
        opacity: 0;
        z-index: -99;
        transition: all 0.3s ease-in-out;
        background: #ffffff;
        box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.1);
        border-radius: 5px 5px 5px 5px;
        border: 1px solid #e9e8e8;
        font-size: 14px;
        color: #1f1a1b;
        top: 42px;
        position: absolute;
        &::after {
            content: '';
            width: 100%;
            height: 10px;
            position: absolute;
            top: -10px;
            left: 0;
        }
    }
    .businese_hover_tips {
        width: 328px;
        height: 208px;
        text-align: center;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0px 2px 6px 1px rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        font-size: 14px;
        font-weight: 400;
        color: rgba(32, 32, 32, 1);
        position: absolute;
        right: -50px;
        top: 42px;
        visibility: hidden;
        opacity: 0;
        transition: all 0.3s ease-in-out;
        z-index: -99999;
        .right_txt {
            font-size: 14px;
            color: #666;
            padding: 18px 0;
        }
        .right_icons {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 30px;
            .right_item {
                text-align: center;
                line-height: 24px;
                span {
                    display: inline-block;
                    &.item_txt {
                        font-size: 14px;
                        color: #202020;
                    }
                    &.icon_area {
                        width: 32px;
                        height: 32px;
                        border-radius: 100%;
                        text-align: center;
                        line-height: 32px;
                        background: #f2f3f7;
                        .iconfont {
                            margin: 0;
                        }
                    }
                }
            }
        }
        .right_footer {
            width: 288px;
            height: 38px;
            line-height: 16px;
            position: relative;
            margin-top: 25px;
            padding: 0 20px;

            .right_btn {
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, #ffe9c6 0%, #ffd48f 100%);
                border-radius: 4px 4px 4px 4px;
                opacity: 1;
                font-size: 14px;
                border: none;
                color: #84561f;
                display: block;
                line-height: 38px;
                font-weight: 500;
            }
            .btn_tip {
                position: absolute;
                right: 20px;
                top: -14px;
                width: 105px;
                height: 22px;
                background: #fc422d;
                border-radius: 0px 8px 0px 8px;
                opacity: 1;
                font-size: 12px;
                color: #ffffff;
                line-height: 20px;
            }
        }

        i {
            color: #00a82d;
        }
    }

    // .collaborative-share-container {
    //     margin: 0px;
    //     height: 40px;
    //     line-height: 40px;
    //     width: 84px;
    //     .collaborative-share-icon {
    //         margin-right: 0px;
    //     }
    // }

    .vipExtensionArea {
        height: 38px;
        line-height: 38px;
        margin-right: 12px;
        .vipExtension {
            height: 38px;
            line-height: 38px;
            margin-left: 0 !important;
            .abTestVipSlideshow {
                height: 38px;
                top: 0;
                line-height: 38px;
                width: 140px;
                .userVip {
                    // border-radius: 5px;
                    // height: 40px;
                    img {
                        margin-right: 6px;
                    }
                }
                li {
                    width: 140px;
                    border-radius: 5px;
                    height: 38px;
                }
                // .flashSales {
                //     height: 40px;
                //     border-radius: 5px;
                // }
                // .userVip {
                //     height: 40px;
                //     border-radius: 5px;
                // }
            }
        }
    }
    .store_our_website {
        width: 500px;
        height: 358px;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0px 11px 10px 0px rgba(0, 0, 0, 0.04);
        border-radius: 4px;
        position: absolute;
        right: 167px;
        top: 75px;
        text-align: center;
        .store_us_top {
            width: 500px;
            height: 246px;
            background: linear-gradient(-57deg, rgba(254, 67, 80, 1) 0%, rgba(255, 85, 132, 1) 100%);
            border-radius: 4px 4px 0px 0px;
            .ctrl_code {
                font-size: 46px;
                line-height: 46px;
                font-weight: bold;
                color: rgba(247, 247, 247, 1);
                padding: 35px 0 25px;
            }
            .ctrl_txt {
                font-size: 40px;
                font-weight: bold;
                color: rgba(247, 247, 247, 1);
                line-height: 40px;
            }
            .border_line {
                width: 420px;
                height: 1px;
                background: rgba(255, 255, 255, 1);
                margin: 27px 0 15px;
                display: inline-block;
            }
            .warm_tips {
                font-size: 26px;
                font-weight: 500;
                color: rgba(247, 247, 247, 1);
            }
        }
        .store_us_bottom {
            .confirm_btn {
                display: inline-block;
                width: 198px;
                height: 56px;
                background: linear-gradient(-57deg, rgba(254, 67, 80, 1) 0%, rgba(255, 85, 132, 1) 100%);
                border-radius: 28px;
                line-height: 56px;
                margin: 16px 0 11px;
                font-size: 26px;
                color: #fff;
                font-weight: 500;
                cursor: pointer;
            }
            .confirm_second {
                font-size: 14px;
                color: #666;
            }
        }
    }
}

// 448

@media screen and (max-width: 1200px) {
    .tgs_headerBar {
        .headerLeft {
            .saveBtn {
                span {
                    display: none;
                }
            }
            .headerNameBox {
                display: none;
            }
        }
        .headerRight {
            .copyrightBtn {
                span {
                    display: none;
                }
            }
        }
    }
} /*宽度小于1300px时 */

// 周一吐槽大会活动按钮 END
.vipExtensionArea {
    height: 100%;
    line-height: $infoHeight;
    margin-right: 10px;
    position: relative;

    .vipExtension {
        display: inline-block;
        // width: 95px;
        // width: 126px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        color: #fff;
        font-size: 14px;
        // background: $primaryColorV6_2;
        // border: 1px solid #ff4555;
        // border-radius: 4px;
        & > span:first-child {
            display: inline-block;
            background-color: #ff4555;
            color: #fff;
            padding: 0 10px;
            border-radius: 4px 0 0 4px;
        }
        & > span:nth-child(2) {
            display: inline-block;
            background-color: #feecec;
            color: #f37f6d;
            padding: 0 6px;
            line-height: 28px;
            border: 1px solid #ff4555;
            border-radius: 0 4px 4px 0;
        }

        .abTestVip {
            width: 142px;
            height: 58px;

            position: relative;
            /* top: -15px; */
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0px;

            font-size: 16px;
            font-weight: 600;
            color: #ff4555;
            line-height: 22px;

            box-shadow: 0px 3px 4px -1px #e0e0e0;
            // z-index: -1;

            // background: linear-gradient(135deg, #FF5584 0%, #FE4350 100%);
            .giveJurisdiction {
                margin-left: 5px;

                width: 50px;
                height: 18px;
                background: #ff4555;
                border-radius: 2px;
                opacity: 0.88;

                font-size: 12px;
                font-weight: 500;
                color: #ffffff;
                line-height: 16px;
            }
        }

        &.newVipStyle {
            background: transparent;
            color: #666;
            height: 58px;
            line-height: 58px;
            border-radius: 0;

            &:hover {
                background: $primaryColorV6_2;
                color: #fff;
            }
        }
    }

    .nav-pulldown-bg {
        position: relative;
        transition: all 0.3s;
        visibility: hidden;
        opacity: 0;
        width: 300px;
        box-sizing: border-box;
        padding-top: 12px;
        top: 54px;
        pointer-events: none;
        .nav-pulldown {
            width: 100%;
            padding: 20px;
            background: rgba(255, 250, 244, 1);
            border: 1px solid #ededed;
            border-radius: 4px;
            position: relative;
            color: $fontColorLight;
            &:before {
                display: block;
                content: '';
                position: absolute;
                border-right: 1px solid #ededed;
                border-bottom: 1px solid #ededed;
                width: 18px;
                height: 18px;
                transform: rotate(-135deg);
                left: 50%;
                top: -10px;
                background: #f8e0c7;
                margin-left: -9px;
                z-index: -1;
            }
        }
        .VIP-privilege-intr {
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            padding: 0;
            z-index: 9;
            width: 500px;
            height: 380px;
            text-align: center;
            border-radius: 10px;
            box-shadow: 0px 0px 4px 1px rgba(232, 232, 232, 0.5);
            & .vip-title {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 90px;
                background: url(//s.tuguaishou.com/index_img/editorV7.0/nav_pulldown_title.png) no-repeat 0px 0px;
                background-size: contain;

                &.vip-title .vip-title-sOne {
                    width: 100%;
                    font-size: 24px;
                    font-weight: 600;
                    margin-bottom: 10px;
                    line-height: 24px;
                    background: linear-gradient(130deg, #582500 0%, #c98442 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                }
                &.vip-title .vip-title-sTwo {
                    width: 100%;
                    font-size: 16px;
                    font-weight: 400;
                    color: #581800;
                    line-height: 16px;
                }
            }
            & .vip-intro {
                height: 290px;
                display: flex;
                flex-direction: column;
            }
            & .vip-intro .vip-intro-introduce {
                display: flex;
                justify-content: space-between;
                padding: 30px 25px 19px 25px;
                box-sizing: border-box;
            }
            & .vip-intro .vip-intro-introduce .vip-icon-intro {
                width: 115px;
                height: 120px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: space-around;
                font-size: 14px;
                font-weight: 400;
                color: #202020;
            }
            & .vip-intro .vip-intro-introduce .vip-icon-intro a {
                display: block;
                width: 100%;
            }

            & .vip-intro .vip-intro-introduce .vip-icon-intro .iconfont {
                font-size: 30px;
                color: #ffffff;
                border-radius: 50px;
                display: block;
                width: 50px;
                height: 50px;
                margin: 0 auto;
                text-align: center;
                line-height: 50px;
                margin-top: 6px;
                background: linear-gradient(143deg, #e3b686 0%, #d5945d 100%);
            }
            & .vip-intro .vip-intro-introduce .vip-icon-intro .privilege {
                width: 100%;
                text-align: center;
                font-size: 14px;
                font-weight: 600;
                color: #581800;
                line-height: 14px;
            }
            & .vip-intro .vip-intro-introduce .vip-icon-intro .purple {
                width: 100%;
                font-size: 14px;
                font-weight: 400;
                color: #581800;
                line-height: 14px;
            }
            & .vip-intro .vip-intro-details {
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            & .vip-intro .vip-intro-details .know-more {
                flex: 1;
                height: 20px;
            }
            & .vip-intro .vip-intro-details .know-more a {
                color: rgba(41, 15, 0, 0.7);
                text-decoration: underline;
                font-size: 12px;
            }

            & .vip-intro .vip-intro-details .at-once-understand {
                height: 20px;
                line-height: 0px;
                color: rgba(41, 15, 0, 0.7);
                text-decoration: underline;
                font-size: 12px;
            }
            & .vip-intro .vip-intro-details .at-once-join {
                width: 100%;
                display: block;
                width: 260px;
                height: 50px;
                background-color: #5b2e04;
                border-radius: 25px 25px 25px 25px;
                text-align: center;
                line-height: 50px;
                font-size: 16px;
                font-weight: 600;
                color: #ffffff;
            }
            & .vip-intro .vip-intro-details .vip-intro-details-text {
                width: 100%;
                flex: 1;
                font-size: 12px;
                font-weight: 400;
                color: rgba(41, 15, 0, 0.8);
            }
            & .vip-intro .vip-intro-details .vip-intro-details-text a {
                margin-left: 5px;
                font-size: 12px;
                text-decoration: underline;
                color: rgba(41, 15, 0, 0.8);
            }

            & .vip-intro .vip-intro-details .vip-intro-details-join {
                line-height: 40px;
                height: 40px;
                color: #581800;
                text-align: center;
                font-size: 12px;
                font-weight: 400;
            }

            .privilege-intr-jiaru {
                margin: 20px auto;
                display: block;
                width: 182px;
                height: 38px;
                border-radius: 4px;
                line-height: 38px;
                color: #ffffff;
                letter-spacing: 1px;
                font-size: 16px;
                background: #ff4555;
                filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fd7147', endColorstr='#f65e30', GradientType=0);
            }
            .privilege-intr-jiaru:hover {
                opacity: 0.8;
            }
        }
    }

    .nav-pulldown-area {
        visibility: hidden;
        position: absolute;
        left: 50%;
        transform: translate(-50%);
        top: 40px;
        padding-top: 6px;
        width: max-content;
        height: max-content;
    }

    .nav-pulldown-bgNew {
        display: none;
        width: 478px;
        // height: 342px;
        height: 300px;
        // background: rgba(255, 255, 255, 1);
        box-shadow: 0px 2px 4px 0px rgba(227, 227, 227, 0.5);
        border-radius: 10px;
        position: absolute;
        top: 58px;
        left: 50%;
        line-height: 24px;
        -webkit-transform: translate(-50%, 0);
        -moz-transform: translate(-50%, 0);
        -ms-transform: translate(-50%, 0);
        -o-transform: translate(-50%, 0);
        transform: translate(-50%, 0);
        overflow: hidden;
        text-align: center;
        // background: rgba(249, 247, 243, 1);
        background: rgba(255, 254, 252, 1);

        .banner-top {
            width: 100%;
            height: 78px;
            background: url(//js.tuguaishou.com/start-design/other/css_sprites1.png) -5px -5px;
            margin-bottom: 17px;
        }
        .banner-top-v2 {
            width: 100%;
            height: 90px;
            line-height: 90px;
            // height: 78px;
            // line-height: 78px;
            // background: url(//s.tuguaishou.com/index_img/editorV7.0/vip-top-banner.png);
            background: url(//s.tuguaishou.com/index_img/editorV7.0/vip-top-copy-v11.png);
            margin-bottom: 23px;
            font-size: 24px;
            font-weight: 500;
            color: rgba(255, 255, 255, 1);
            position: relative;

            .top-v2-tips {
                font-weight: 500;
                color: rgba(201, 135, 34, 1);
                color: rgba(255, 161, 4, 1);
                display: inline-block;
                position: absolute;
                height: 90px;
                line-height: 90px;
                top: 0;
                left: 54px;
                font-size: 18px;
                font-size: 20px;
            }
            .top-v2-btn {
                width: 166px;
                height: 34px;
                line-height: 34px;
                border-radius: 6px;
                display: inline-block;
                position: absolute;
                top: 28px;
                right: 35px;
                font-size: 16px;
                // color: rgba(1, 1, 1, 1);
                color: rgba(255, 255, 255, 1);
                background: rgba(231, 194, 128, 1);
                background: rgba(253, 181, 7, 1);
            }
        }
        .sub-title {
            font-size: 14px;
            color: rgba(32, 32, 32, 1);
            line-height: 20px;
            position: relative;

            &::before {
                display: block;
                content: '';
                position: absolute;
                width: 48px;
                height: 1px;
                border-bottom: 1px solid #202020;
                background: #ffffff;
                left: -64px;
                top: 9px;
            }
            &::after {
                display: block;
                content: '';
                position: absolute;
                width: 48px;
                height: 1px;
                border-bottom: 1px solid #202020;
                background: #ffffff;
                right: -64px;
                top: 9px;
            }
        }
        .intro {
            font-size: 16px;
            font-weight: 600;
            color: rgba(32, 32, 32, 1);
            line-height: 22px;
            display: inline-block;
            width: 40%;
            margin-top: 44px;
            &:nth-child(-n + 6) {
                margin-top: 24px;
            }
            .iconfont {
                color: #d09324;
                font-size: 24px;
                margin-right: 12px;
                display: inline-block;
                vertical-align: middle;
                font-weight: 500;
            }
            span {
                display: inline-block;
                vertical-align: middle;
                font-weight: 500;
            }
            .cloud-saved {
                position: relative;
                left: -4px;
            }
            & > a {
                display: block;
                color: #202020;
            }

            .logo-img {
                width: 40px;
                height: 40px;
                display: inline-block;
                vertical-align: -14px;
                margin-right: 20px;
                background-image: url(//js.tuguaishou.com/start-design/other/css_sprites1.png);

                &.logo-img1 {
                    background-position: -105px -93px;
                }
                &.logo-img2 {
                    background-position: -5px -93px;
                }
                &.logo-img3 {
                    background-position: -55px -93px;
                }
                &.logo-img4 {
                    background-position: -155px -93px;
                }
            }
        }
        .go-spec-btn {
            display: block;
            width: 280px;
            height: 49px;
            background: linear-gradient(90deg, rgba(242, 219, 187, 1) 0%, rgba(234, 192, 115, 1) 100%);
            border-radius: 25px;
            line-height: 49px;
            text-align: center;
            font-size: 18px;
            font-weight: 500;
            color: rgba(32, 32, 32, 1);
            margin: 27px auto;

            &:hover {
                color: #fff;
            }
        }
    }

    &:hover {
        .nav-pulldown-bgNew {
            display: block;
        }
        .nav-pulldown-bg {
            visibility: visible;
            opacity: 1;
            top: 0px;
            pointer-events: unset;
        }
        .nav-pulldown-area {
            visibility: visible;
        }
    }
    .abTestVipSlideshow {
        display: block;
        width: 138px;
        height: 30px;
        border-radius: 4px;
        color: #581800;
        // color: $primaryFontColor6;
        overflow: hidden;
        position: relative;
        top: 10px;
        font-weight: 600;
        z-index: 999;
        cursor: pointer;
        .SlideBox {
            width: 300%;
            position: absolute;
            left: 0;
            top: 0;
            li {
                list-style: none;
                float: left;
                width: 138px;
                height: 30px;
                background: $slideBoxBGColor;
                border-radius: 4px;
                &.flashSales {
                    font-size: 12px;
                }
                &.userVip {
                    img {
                        width: 20px;
                        height: 20px;
                        position: relative;
                        top: 4px;
                    }
                }
                span {
                    color: red;
                }
            }
        }
    }
}
// .vipExtensionArea {
//     height: 48px;
//     line-height: 40px;
//     margin-right: 20px;
//     .vipExtension {
//         height: 40px;
//         line-height: 40px;
//         .abTestVipSlideshow {
//             height: 40px;
//             top: 0;
//             line-height: 40px;
//             .userVip {
//                 border-radius: 8px;
//                 height: 40px;
//                 img {
//                     margin-right: 4px;
//                 }
//             }
//             .flashSales {
//                 height: 40px;
//                 border-radius: 8px;
//             }
//             .userVip {
//                 height: 40px;
//                 border-radius: 8px;
//             }
//         }
//     }
// }
