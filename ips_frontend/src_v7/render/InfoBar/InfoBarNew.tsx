import React from 'react';
// @ts-ignore
import MasonryInfiniteScroller from 'react-masonry-infinite';
import { emitter } from '@component/Emitter';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { assetManager } from '@component/AssetManager';
import { IPSConfig } from '@v7_utils/IPSConfig';
import { getProps, ipsApi } from '@component/IPSConfig';

import { InfoBarComponent } from './InfoBar';
import { TgsManageHelper } from '@v7_logic/StoreLogic';
import { ErrorBoundaryDecorator } from '@v7_render/ErrorBoundaryHOC';
import { CooperationUsersNew } from '@v7_render/Collaborative';
import { env } from '@editorConfig/env';
import { InfoBarMenuNew } from './InfoBarMenuNew';
import { storeDecorator } from '@v7_logic/StoreHOC';
import { RatifyOption } from '@v7_render/RatifyPopUp';
import { InfoManageHelper } from '@v7_logic/StoreLogic';

import { RedoAndUndo } from '@v7_store/logic/onCanvasPaintedReducer';
import { UserProfile, DesignerTaskKeyWords } from '@component/InfoBar';

import { VipAd } from './VipAd';
import './scss/InfoBarNew.scss';
import { isEcommerceTeam, isUeTeam } from '@v7_utils/webSource';
import PublishPanel from './PublishPanel';
import Qrcode from './PublishPanel/Qrcode';
import { PublishOptions, ShareOptions, ShareType } from './PublishPanel/const';
import { SaveTeamPopup } from './Popup/SaveTeamPopup';
import { canvasStore } from '@v7_store/redux/store';
import { DocHashLogic } from '@v7_logic/DocHash';
import { baseTools } from '@v7_utils/BaseTools';
import { VipRecharge } from '@v7_render/VipRecharge';
import { TgsModal } from '@tgs/general_components/src/components';
import { message } from 'antd';
const systemType = baseTools.isSystem();
const isMac = systemType === 'mac';
const ctrlText = isMac ? '⌘' : 'Ctrl'


let PromptBoxTimer: NodeJS.Timeout;
export const checkProhibitedWords = (words: string[]) => {
    return new Promise((resolve) => {
        assetManager.checkProhibitedWords(words).then((res) => {
            if (res.stat == 1) {
                resolve(true);
            } else {
                const windowContentStr = <div className="errorMsg">含有敏感词汇，修改失败，请重新输入!</div>;
                // 保存成功提示框
                const windowInfo = {
                    windowContent: windowContentStr,
                };
                emitter.emit('PromptBox', windowInfo);
                if (PromptBoxTimer) {
                    clearTimeout(PromptBoxTimer);
                }
                PromptBoxTimer = setTimeout(() => {
                    emitter.emit('PromptBoxClose');
                }, 3000);
                resolve(false);
            }
        });
    });
};

@ErrorBoundaryDecorator()
@storeDecorator((state: { [key: string]: any }) => {
    return {
        user: state.onCanvasPainted.user,
        work: state.onCanvasPainted.work,
        info: state.infoManageStore.info,
    };
})
export class InfoBarNew extends InfoBarComponent {
    update_info_title = React.createRef<HTMLInputElement>();
    PromptBoxTimer: NodeJS.Timeout;
    constructor(props: any) {
        super(props);
    }

    updateInfoTitleChangeEvent() {
        const { info } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage,
        });
        const infoTitle = this.update_info_title.current.value;
    }

    /**
     * 修改标题- 失去焦点(保存)
     */
    updateInfoTitleBlurEvent(e: any) {
        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        let infoTitle = this.update_info_title.current.value;
        if (infoTitle.length > 50) {
            infoTitle = infoTitle.substr(0, 50);
        }
        if (!(user.userId > 0)) {
            emitter.emit('LoginPanelShow');
            return;
        }
        if (user.bind_phone === 0) {
            emitter.emit('InfoBarPhoneBindPopup');
            return;
        }
        checkProhibitedWords([infoTitle]).then((result) => {
            if (result) {
                InfoManageHelper.updateInfoTitle(infoTitle);
                emitter.emit('autoSaveTempl');
                assetManager.setPv_new(1021, { additional: {} });
            }
        });
    }

    uotitleFocusEvent() {
        assetManager.setPv_new(1021, { additional: {} });
    }
    onKeyEnterSearch(e: React.KeyboardEvent<HTMLInputElement>) {
        if (e.key === 'Enter') {
            this.updateInfoTitleBlurEvent(e);
            this.update_info_title.current.blur();
        }
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }
    getShareUrl() {
        const { rt_first_save_recorder } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const urlProps = getProps();
        assetManager
            .getEditShare(
                urlProps.upicId,
                rt_first_save_recorder.main || urlProps.upicId
                    ? 'template_id'
                    : urlProps && urlProps.paperId
                    ? 'paper_id'
                    : urlProps && urlProps.assetId
                    ? 'asset_id'
                    : 'template_id_n',
                false,
            )
            .then((data) => {
                data.json().then((url) => {
                    if (url.stat === 1) {
                        const {
                            share_id: shareId,
                            share_type: shareType,
                            template_id: templateId,
                            version_id: versionId
                        } = url.data
                        this.setState({
                            shareUrl: `https://818ps.com/share?shareId=${shareId}&shareType=${shareType}&templateId=${templateId}&versionId=${versionId}`
                        });
                    }
                });
            });
    }

    hideVipRecharge = () => {
        this.setState({ showVipRecharge: false });
    }

    aiSave = () => {
        if (!this.props.info?.title || !this.props.info?.description) {
            message.error('请先提交设计');
            return;
        }
        const urlProps = IPSConfig.getProps(true);
        if (urlProps.picId) {
            // emitter.emit('autoSaveTempl', '', 0, {handleSave: true, ai_marking: 1, old_templ_id: urlProps.picId});
            const loading = message.loading('处理中，请稍后...', 0);
            emitter.emit('convertCanvasPageToImage', {format:'jpeg',quality:1,index:0, handleSave: true, callback: (urlPath) => {
                assetManager.aiMariking(urlProps.picId, urlPath).then((res) => {
                    loading();
                    if (res.code !== 10000) {
                        message.error(res.message);
                    }
                });
            }});
        } else {
            message.info('请先保存设计');
        }
    }

    componentDidUpdate(): Promise<void> {
        return new Promise(() => {
            const { info } = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
                store_name: storeAdapter.store_names.InfoManage,
            });
            if(this.update_info_title.current != document.activeElement) {
                this.update_info_title.current.value = info.title;
            }
        });
    }
    render() {
        const {
            saveTemplTip,
            filesaveTemplTip,
            notLoginSaveActionCount,
            isTeam,
            isShowTeam,
            isShowClickTeam,
            online_detail_download_type,
            showStoreOurWebsite,
            websiteLastSecond,
            isVideo,
            isUpicIdOwner,
            versionList,
            saveType,
            filePopoverShow,
            filePopoverFocused,
            sharePlaneType,
        } = this.state;
        let { saveText } = this.state;
        //编辑器判断
        const { isDesigner, GroupWordUser, isDocDesigner, rt_is_online_detail_page, canvas, user, work } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const { info, vipTemplateNumMap:{ newVipModelVersion = 0} } = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
            store_name: storeAdapter.store_names.InfoManage,
        });

        //info.is_second二次编辑使用

        let saveClickEvent = this.saveClickEvent as React.MouseEventHandler<HTMLDivElement>;
        const props = getProps();
        if (saveText === '保存中...') {
            saveClickEvent = undefined;
        }
        saveText = '保存';
        if (saveText === '保存' && (env.teamTemplate || isUeTeam)) {
            saveText = '保存到团队设计';
        }

        const firmVipHref2 = ipsApi('/dash/firm-intro?'),
            firmVipHrefOriginDown = 'company_spec_hover',
            vipHrefOriginDown = 'editordownbtnSpec',
            vipHrefOriginDown2 = 'csyEditBtn_editor';
        let vipHref2 = ipsApi('/dash/vip-spec?classify=1');
        if (info.is_company_temp === 2) {
            vipHref2 = ipsApi('/dash/vip-spec-commercial?classify=1');
        }   
        if(newVipModelVersion){
            vipHref2 = ipsApi('/dash/vip-spec-commercial-new?classify=1');
        }
        // 我的设计 超链接
        let more_user_templ_url = props['isDesigner'] ? ipsApi('/dash/dsrtaskall') : ipsApi('/dash/userdsn');
        if (env.teamTemplate || isUeTeam) {
            const { team_id = 0 } = getProps();
            more_user_templ_url = `//team.818ps.com/team/design?team_id=${team_id}&uid=${user.userId}`;
        }
        const urlProps = IPSConfig.getProps();
        const ecommerceTeam = isEcommerceTeam();

        const isUe = !(
            env.editor === 'ecommerce' ||
            env.editor === 'ecommerceteam' ||
            /* env.editor === 'ueteam' */ isUeTeam || ecommerceTeam
        );
        const version = 'b';
        const isSmart = env.editor === 'smart';
        const showCollaborative =
            !isDesigner &&
            !urlProps.isClientSide &&
            !(
                window.navigator.userAgent.toLowerCase().indexOf('mac') > -1 &&
                window.navigator.userAgent.toLowerCase().indexOf('electron/') > -1
            ) &&
            !(env.editor === 'ecommerceteam' || isSmart );
        const isFirm = info?.is_company_temp === 1 || env.teamTemplate || ecommerceTeam;

        return (
            <div className="tgs_headerBar">
                <div className="headerLeft">
                    <div
                        className="headerBtnItem"
                        onMouseEnter={this.goHomeHoverEvent.bind(this)}
                        onClick={this.goHomeClickEvent.bind(this)}
                    >
                        <i className="iconfont icon-fanhui1"></i>
                        <span>首页</span>
                    </div>
                    <div
                        className="headerBtnItem fileBtn"
                        onFocus={() => {
                            this.setState({ filePopoverFocused: true });
                        }}
                        onBlur={() => {
                            this.setState({ filePopoverFocused: false });
                        }}
                        onMouseOver={() => {
                            this.setState({ filePopoverShow: true });
                            assetManager.setPv_new(3691);
                        }}
                        onMouseLeave={() => {
                            this.setState({ filePopoverShow: false });
                        }}
                        onClick={() => {
                            assetManager.setPv_new(3692);
                        }}
                    >
                        <span>文件</span>
                        {(filePopoverShow || filePopoverFocused) && (
                            <div className="class_popover filePopover">
                                <InfoBarMenuNew
                                    isDesigner={isDesigner}
                                    isUpicIdOwner={isUpicIdOwner}
                                    isUe={isUe}
                                    saveTemplTip={saveTemplTip}
                                    info={info}
                                    notLoginSaveActionCount={notLoginSaveActionCount}
                                    saveType={saveType}
                                    GroupWordUser={GroupWordUser}
                                    teamTemplate={env.teamTemplate || isUeTeam}
                                    filesaveTemplTip={filesaveTemplTip}
                                    user={user}
                                    more_user_templ_url={more_user_templ_url}
                                    isTeam={isTeam}
                                    versionList={versionList}
                                    event={{
                                        filesaveClickEvent: this.filesaveClickEvent.bind(this),
                                        changeVersion: this.changeVersion.bind(this),
                                        createCopys: this.createCopys.bind(this),
                                        beforeDownloadClickEvent: this.beforeDownloadClickEvent.bind(this),
                                        myDesignBtnUnloginClick: this.myDesignBtnUnloginClick.bind(this),
                                        myDesignClickEvent: this.myDesignClickEvent.bind(this),
                                        showTeamSavePop: this.showTeamSavePop.bind(this),
                                        newTemplateBtnClickEvent: this.newTemplateBtnClickEvent.bind(this),
                                        clickShareButton: this.clickShareButton.bind(this),
                                        uploadGroupWordClickEvent: this.uploadGroupWordClickEvent.bind(this),
                                        delGroupInfoClickEvent: this.delGroupInfoClickEvent.bind(this),
                                        uploadClickEvent: this.uploadClickEvent.bind(this),
                                        saveClickEvent: this.saveClickEvent.bind(this),
                                        onMouseEnterSave: this.onMouseEnterSave.bind(this),
                                        clickshowTeamSaveBtn: this.clickshowTeamSaveBtn.bind(this),
                                    }}
                                />
                            </div>
                        )}
                    </div>
                    <div
                        className={RedoAndUndo.getPastLength() > 0 ? 'headerBtnItem step' : 'headerBtnItem step noStep'}
                        onClick={this.undoClickEvent.bind(this)}
                    >
                        <i className="iconfont icon-newbianjiqi-daohangshangyibu"></i>
                        <div className="class_tooltip stepTooltip">上一步({ctrlText}+Z)</div>
                    </div>
                    <div className='divider'></div>
                    <div
                        className={
                            RedoAndUndo.getFutureLength() > 0 ? 'headerBtnItem step' : 'headerBtnItem step noStep'
                        }
                        onClick={this.redoClickEvent.bind(this)}
                    >
                        <i className="iconfont icon-newbianjiqi-daohangxiayibu"></i>
                        <div className="class_tooltip stepTooltip">下一步({ctrlText}+Y)</div>
                    </div>
                    <div
                        className={
                            saveText === '保存中...' || saveText === '保存失败，重试'
                                ? 'headerBtnItem saveBtn saveFlag'
                                : 'headerBtnItem saveBtn'
                        }
                    >
                        <div onClick={saveClickEvent} onMouseEnter={this.onMouseEnterSave}>
                            {Number.parseInt(user.userId) <= 0 && this.state.notLoginSaveActionCount >= 1 ? (
                                <>
                                    <i className="iconfont icon-newbianjiqi-daohang-weibaocun"></i>
                                    {/* <span>编辑未保存</span> */}
                                </>
                            ) : (
                                <>
                                    {saveType === 'noSave' && (
                                        <>
                                            <i className="iconfont icon-newbianjiqi-daohang-weibaocun"></i>
                                            {/* <span>保存</span> */}
                                        </>
                                    )}
                                    {saveType === 'saving' && (
                                        <>
                                            <i className="iconfont icon-newbianjiqi-daohang-baocunzhong"></i>
                                            {/* <span>保存中...</span> */}
                                        </>
                                    )}
                                    {saveType === 'saved' && (
                                        <>
                                            <i className="iconfont icon-newbianjiqi-daohang-baocunchenggong"></i>
                                            {/* <span>{saveTemplTip}</span> */}
                                        </>
                                    )}
                                    {saveType === 'saveFail' && (
                                        <>
                                            <i className="iconfont icon-newbianjiqi-daohang-baocunshibai"></i>
                                            {/* <span>保存失败</span> */}
                                        </>
                                    )}
                                </>
                            )}
                        </div>
                        <div className="class_tooltip saveTooltip">
                            <div
                                className="autoSaveTipsTxt1"
                                style={env.teamTemplate || isUeTeam ? { padding: '15px 28px', whiteSpace: 'normal' } : {}}
                            >
                                {Number.parseInt(user.userId) <= 0 && this.state.notLoginSaveActionCount >= 1 ? (
                                    <>
                                        <span>编辑未保存</span>
                                    </>
                                ) : (
                                    <>
                                        {saveType === 'noSave' && (
                                            <>
                                                <span>保存</span>
                                            </>
                                        )}
                                        {saveType === 'saving' && (
                                            <>
                                                <span>保存中...</span>
                                            </>
                                        )}
                                        {saveType === 'saved' && (
                                            <>
                                                <span>{saveTemplTip}</span>
                                                {Number(urlProps.paperId) > 0 && !(Number(urlProps.upicId) > 0) && !isDesigner
                                                ? '已自动保存至草稿箱'
                                                : env.teamTemplate || isUeTeam
                                                ? `，所有编辑会自动保存到团队设计(${ctrlText}+S)`
                                                : `，所有编辑会自动保存(${ctrlText}+S)`}
                                                {!env.teamTemplate && !isUeTeam && (
                                                    <div className="autoSaveTipsTxt2">点击可手动保存至云端工作台</div>
                                                )}
                                            </>
                                        )}
                                        {saveType === 'saveFail' && (
                                            <>
                                                <span>保存失败</span>
                                            </>
                                        )}
                                    </>
                                )}
                            </div>
                        </div>
                    </div>

                    <div className="headerNameBox">
                        <div className="headerBtnItem templateName">{info.title || '未命名设计'}</div>
                        <input
                            type="text"
                            placeholder="未命名设计"
                            className="inputShow"
                            onChange={this.updateInfoTitleChangeEvent.bind(this)}
                            ref={this.update_info_title}
                            onFocus={this.uotitleFocusEvent.bind(this)}
                            onBlur={this.updateInfoTitleBlurEvent.bind(this)}
                            onKeyDown={this.onKeyEnterSearch.bind(this)}
                        />
                    </div>
                </div>
                <div className="headerRight">

                    {!isDesigner && (
                        <div
                            className={'headerBtnItem copyrightBtn'}
                            onMouseEnter={() => {
                                this.businessUseBtnHoverTime = new Date().getTime();
                                const _this = this;
                                this.businessUseBtnTimer = setTimeout(() => {
                                    assetManager.setPv_new(2022);
                                    clearTimeout(_this.businessUseBtnTimer);
                                }, 1000);
                            }}
                            onMouseLeave={() => {
                                const nowTime = new Date().getTime(),
                                    gapTime = this.businessUseBtnHoverTime ? nowTime - this.businessUseBtnHoverTime : 1;
                                // @ts-ignore
                                clearTimeout(this.businessUseBtnTimer);
                                if (gapTime >= 1000) {
                                    assetManager.setPv_new(2023, { additional: { i0: gapTime } });
                                }
                            }}
                            data-from="copyright"
                            onClick={this.businessBtnGotoRight.bind(
                                this,
                                'btn',
                                vipHref2 + '&origin=' + vipHrefOriginDown2 + 'BusBtn',
                            )}
                        >
                            <i className="iconfont icon-bianjiqixin-dingbudaohang-banquan" />
                            <span>企业会员无版权风险</span>
                            <div
                                className="businese_hover_tips"
                                onMouseEnter={() => {
                                    // @ts-ignore
                                    this.businessUseBtnTipsHoverTime = new Date().getTime();
                                    const _this = this;
                                    this.businessUseBtnTipsTimer = setTimeout(() => {
                                        assetManager.setPv_new(2036);
                                        clearTimeout(_this.businessUseBtnTipsTimer);
                                    }, 1000);
                                }}
                                onMouseLeave={() => {
                                    const nowTime = new Date().getTime(),
                                        gapTime = this.businessUseBtnTipsHoverTime
                                            ? // @ts-ignore
                                              nowTime - this.businessUseBtnTipsHoverTime
                                            : 1;
                                    clearTimeout(this.businessUseBtnTipsTimer);
                                    if (gapTime >= 1000) {
                                        assetManager.setPv_new(2037, { additional: { i0: gapTime } });
                                    }
                                }}
                                data-from="copyright-tip"
                                onClick={this.businessBtnGotoRight.bind(
                                    this,
                                    'tips',
                                    vipHref2 + '&origin=' + vipHrefOriginDown2 + 'BusTips',
                                )}
                            >
                                <div className="right_txt">当前模板无版权风险，企业会员用户可免费商用</div>
                                <div className="right_icons">
                                    <div className="right_item">
                                        <span className="icon_area">
                                            <i className="iconfont icon-ziti"></i>
                                        </span>
                                        <span className="item_txt">字体可商用</span>
                                    </div>
                                    <div className="right_item">
                                        <span className="icon_area">
                                            <i className="iconfont icon-tiezhi"></i>
                                        </span>
                                        <span className="item_txt">贴纸可商用</span>
                                    </div>
                                    <div className="right_item">
                                        <span className="icon_area">
                                            <i className="iconfont icon-zhaopian"></i>
                                        </span>
                                        <span className="item_txt">照片可商用</span>
                                    </div>
                                </div>
                                <div className="right_footer">
                                    <a
                                        className="right_btn"
                                        onMouseEnter={(e) => {
                                            assetManager.setPv_new(6878);
                                            e.stopPropagation();
                                        }}
                                        onClick={(e) => {
                                            assetManager.setPv_new(6879);
                                            window.open(
                                                'https://818ps.com/dash/firm-intro?origin=BusinessVipFirmPicDetail&route_id=16635734493682&route=1,187_2&after_route=1_187_2',
                                            );
                                            e.stopPropagation();
                                        }}
                                    >
                                        立即获取商用授权
                                    </a>
                                    <span className="btn_tip">企业发票报销无忧</span>
                                </div>
                            </div>
                        </div>
                    )}

                    {!isDesigner && !GroupWordUser && (
                        <VipAd
                            vipHref2={vipHref2}
                            vipHrefOriginDown2={vipHrefOriginDown2}
                            firmVipHref2={firmVipHref2}
                            firmVipHrefOriginDown={firmVipHrefOriginDown}
                            vipHrefOriginDown={vipHrefOriginDown}
                            version={version}
                            teamTemplate={env.teamTemplate}
                            isFirm={isFirm}
                            info={info}
                            user={user}
                            is_person_business_vip={this.state.is_person_business_vip}
                            event={{
                                vipExtensionAreaMouseenterEvent: this.vipExtensionAreaMouseenterEvent.bind(this),
                                vipExtensionAreaMouseleaveEvent: this.vipExtensionAreaMouseleaveEvent.bind(this),
                                businessBtnGotoRight: this.businessBtnGotoRight.bind(this),
                                vipSlideBoxMouseleaveEvent: this.vipSlideBoxMouseleaveEvent.bind(this),
                                vipSlideBoxMouseenterEvent: this.vipSlideBoxMouseenterEvent.bind(this),
                            }}
                        />
                    )}

                    {showCollaborative && (
                        <CooperationUsersNew
                            isDesigner={isDesigner}
                            clickShareButton={this.collaborativeLinkPopup.bind(this, '')}
                            hideNoRisk={this.hideNoRisk}
                        />
                    )}
                    {!isDesigner && Number(info.template_type) !== 3 && (
                        <div
                            className={'headerBtnItem userShare'}
                            onClick={this.clickShareButton.bind(this, false)}
                            onMouseEnter={this.onShareMouseEnterClickEvent.bind(this, 4285)}
                            // onMouseEnter={this.clickShareButton.bind(this, false)}
                            //onMouseEnter={this.onShareMouseEnterClickEvent.bind(this, 4285)}
                        >
                            <span>分享</span>
                        </div>
                    )}
                    {
                        // 设计师展示任务关键词
                        isDesigner && <DesignerTaskKeyWords />
                    }
                    {GroupWordUser && (
                        <div className="headerBtnItem" onClick={this.delGroupInfoClickEvent.bind(this)}>
                            清除组合
                        </div>
                    )}

                    {!isDesigner && Number(info.template_type) === 3 && (
                        <div
                            className={'headerBtnItem'}
                            onMouseEnter={this.onPreViewMouseEnterClickEvent.bind(this)}
                            onMouseLeave={this.onPreviewMouseLeaveClickEvent.bind(this)}
                            onClick={this.yulanClickEvent.bind(this, 'preview')}
                        >
                            演示
                        </div>
                    )}

                    {/* {!isDesigner ? (
                        <div
                            className={'headerBtnItem'}
                            onMouseEnter={this.onPreViewMouseEnterClickEvent.bind(this)}
                            onMouseLeave={this.onPreviewMouseLeaveClickEvent.bind(this)}
                            onClick={this.yulanClickEvent.bind(this, 'preview')}
                        >
                            {Number(info.template_type) === 3 ? '演示' : '预览'}
                        </div>
                    ) : (
                        <></>
                        // <div className="headerBtnItem" onClick={this.yulanClickEvent.bind(this, 'preview')}>
                        //     预览
                        // </div>
                    )} */}

                    {process.env.NODE_ENV === 'development' && <div className="headerBtnItem" onClick={this.yulanClickEvent.bind(this, 'preview')}>
                        预览
                    </div>}

                    {GroupWordUser && (
                        <div className="headerBtnItem" onClick={this.uploadGroupWordClickEvent.bind(this)}>
                            <i className="iconfont icon-shangchuan"></i>
                            提交组合字
                        </div>
                    )}
                    {isDesigner && (
                        isDocDesigner ? (
                            <div className="headerBtnItem" onClick={this.uploadDocClickEvent.bind(this)}>
                                <i className="iconfont icon-shangchuan"></i>
                                提交图文
                            </div>
                        ) : (
                            <>
                            <div className="headerBtnItem" onClick={this.uploadClickEvent.bind(this)}>
                                <i className="iconfont icon-shangchuan"></i>
                                提交
                            </div>
                            </>
                        )
                    )}


                    {!isDesigner && !GroupWordUser && (
                        <div className="rightDownloadBtn">
                            <div
                                className={'downloadBtn isTeamDownlaod'}
                                onMouseEnter={this.onDownloadMouseEnterClickEvent.bind(this)}
                                onMouseLeave={this.onDownloadMouseLeaveClickEvent.bind(this)}
                                ref="newDownloadStyle"
                                onClick={this.beforeDownloadClickEvent.bind(this, 'download')}
                            >
                                <div
                                    className={[
                                        'downloadBox',
                                        Number.parseInt(user.userId) > 0 &&
                                        (isTeam || isUe || showCollaborative) &&
                                        !isUeTeam &&
                                        env.editor !== 'ecommerceteam' && !ecommerceTeam
                                            ? 'showArrow'
                                            : 'noShowArrow',
                                    ].join(' ')}
                                >
                                    无水印下载
                                </div>
                                {Number.parseInt(user.userId) > 0 &&
                                    (isTeam || isUe || showCollaborative) &&
                                    !env.teamTemplate &&
                                    !isUeTeam && (
                                        <div style={{ display: 'flex' }}>
                                            {/* 暂时屏蔽新版面板 
                                            <div className="downloadTemplTipAreaNewV2">
                                                <div className="downloadTempIcon">
                                                    <i
                                                        className="iconfont icon-xiala1"
                                                        style={env.teamTemplate || isUeTeam ? { color: '#581900' } : {}}
                                                    ></i>
                                                </div>
                                                <div className="downloadTempPanel">
                                                    <PublishPanel
                                                        approval={!isDesigner && isUpicIdOwner && isUe}
                                                        team={isTeam && !isUeTeam}
                                                        ecommerce={isUe}
                                                        onPublish={this.onPublish.bind(this)}
                                                    />
                                                </div>
                                            </div> */}

                                            <div
                      className="downloadTemplTipAreaNew"
                      onClick={this.clickTeamFunct.bind(this)}
                      onMouseEnter={this.onTeamMouseEnterClickEvent.bind(this)}
                      onMouseLeave={this.onTeamMouseLeaveClickEvent.bind(this)}
                    >
                      <i className="iconfont icon-xiala" style={env.teamTemplate || isUeTeam ? { color: '#581900' } : {}}></i>
                    </div>

                    {
                      (isShowTeam || isShowClickTeam) &&
                      <div
                        className={'triangleDropDown'}
                        onMouseEnter={this.enterShowTeam.bind(this)}
                        onMouseLeave={this.leaveShowTeam.bind(this)}
                      >

                        {
                          !isDesigner && isUpicIdOwner && isUe &&
                          <div className="item"
                            onClick={(e) => {
                              this.stopPropagation(e);
                              this.clickShareButton(true);
                              this.setState({
                                isShowTeam: false,
                              });
                            }}
                            onMouseEnter={() => {
                              this.setState({
                                isShowTeam: true,
                              });
                              this.onShareMouseEnterClickEvent(5135);
                            }}
                          >
                            <i className="iconfont icon-a-shenpi1" style={{ color: '#202020' }}></i>审批
                          </div>
                        }

                        {
                          isTeam && !isUeTeam && <div
                            className="item"
                            onClick={this.showTeamSavePop.bind(this, 'download')}
                            onMouseEnter={this.enterSaveTeam.bind(this)}
                            onMouseLeave={this.leaveSaveTeam.bind(this)}
                          >
                            {/* jyjin 保存到团队 */}
                            <i className="iconfont icon-huabanfuben" style={{ color: '#202020' }}></i>保存到团队
                          </div>
                        }

                        {
                          isUe && <div
                            className="item"
                            onClick={this.showWeChatSavePop.bind(this, '')}
                            onMouseEnter={this.handleMouseEnterLimitSetPv.bind(this, 5139)}
                            onMouseLeave={this.handleMouseLeaveLimitSetPv}
                          >
                            <i className="iconfont icon-weixin" style={{ color: '#202020' }}></i>保存至公众号
                          </div>
                        }

                        {
                          isUe && <div
                            className="item"
                            onClick={this.showPopup.bind(this, 'weibo','')}
                            onMouseEnter={this.handleMouseEnterLimitSetPv.bind(this, 5266)}
                            onMouseLeave={this.handleMouseLeaveLimitSetPv}
                          >
                            <i className="iconfont icon-weibo1" style={{ color: '#202020', fontSize: '14px' }}></i>发布到微博
                          </div>
                        }

                        {
                        //   isUe && <div
                        //     className="item"
                        //     onClick={this.showPopup.bind(this, 'tikTok', '')}
                        //     onMouseEnter={this.handleMouseEnterLimitSetPv.bind(this, 5381)}
                        //     onMouseLeave={this.handleMouseLeaveLimitSetPv}
                        //   >
                        //     <i className="iconfont icon-douyins" style={{ color: '#202020' }}></i>发布到抖音
                        //   </div>
                        }
                        {
                        }
                      </div>
                    }
                                        </div>
                                    )}
                                {sharePlaneType === 'team' && (
                                    <div
                                        style={{ position: 'fixed', left: 0, width: '100vw', top: 0, height: '100vh' }}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            this.setState({ sharePlaneType: 'default' });
                                        }}
                                    >
                                        <SaveTeamPopup
                                            style={{ lineHeight: '19px', position: 'absolute', right: 8, top: 57 }}
                                            saveType={''}
                                            backFunction={() => this.setState({ sharePlaneType: 'default' })}
                                            confirmCallback={this.saveClickEvent.bind(this)}
                                            teamSelectCallback={this.popupTeamSelectCallback.bind(this)}
                                        />
                                    </div>
                                )}

                                {sharePlaneType === 'qrcode' && (
                                    <Qrcode
                                        url={this.state.shareUrl}
                                        close={() => this.setState({ sharePlaneType: 'default' })}
                                    />
                                )}

                                {rt_is_online_detail_page && !isVideo && (
                                    <div
                                        ref={(ref) => (this.onDetailDownType = ref)}
                                        className="online_detail_download_type"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                        }}
                                    >
                                        <div className="type_title">下载方式</div>
                                        <div
                                            className="download_type_item"
                                            onClick={() => {
                                                this.setState({ online_detail_download_type: 1 });
                                                TgsManageHelper.tgsSetOnlineDetailDownloadType({
                                                    online_detail_download_type: 1,
                                                });
                                            }}
                                        >
                                            <span
                                                className={`cus_raido${
                                                    online_detail_download_type == 1 ? ' check' : ''
                                                }`}
                                            ></span>
                                            <span className="download_type_txt">页面切片下载</span>
                                        </div>
                                        <div
                                            className="download_type_item"
                                            onClick={() => {
                                                this.setState({ online_detail_download_type: 2 });
                                                TgsManageHelper.tgsSetOnlineDetailDownloadType({
                                                    online_detail_download_type: 2,
                                                });
                                            }}
                                        >
                                            <span
                                                className={`cus_raido${
                                                    online_detail_download_type == 2 ? ' check' : ''
                                                }`}
                                            ></span>
                                            <span className="download_type_txt">完整切片下载</span>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>

                {showStoreOurWebsite && (
                    <div className="store_our_website">
                        <div className="store_us_top">
                            <div className="ctrl_code">ctrl+D</div>
                            <div className="ctrl_txt">收藏图怪兽</div>
                            <div className="border_line"></div>
                            <div className="warm_tips">做图 不迷路</div>
                        </div>
                        <div className="store_us_bottom">
                            <div
                                className="confirm_btn"
                                onClick={() => {
                                    assetManager.setPv_new(2584);
                                    clearTimeout(this.websiteLastSecondTimer);
                                    this.setState({
                                        showStoreOurWebsite: false,
                                        websiteLastSecond: 5,
                                    });
                                }}
                            >
                                朕知道了
                            </div>
                            <div className="confirm_second">弹窗{websiteLastSecond}秒后将自动关闭</div>
                        </div>
                    </div>
                )}

                <VipRecharge open={this.state.showVipRecharge} hide={this.hideVipRecharge} info={info} />
                {/* <TgsModal.SatisfactionSurvey 
                        visible={this.state.showSurveyModal}
                        onClose={this.hideSurveyModal} 
                        onSubmit={(data) => {
                            assetManager.setPv_new(9040);
                            assetManager.submitSatisfactionSurvey(data)
                        }}
                    >
                    </TgsModal.SatisfactionSurvey> */}

            </div>
        );
    }
}
