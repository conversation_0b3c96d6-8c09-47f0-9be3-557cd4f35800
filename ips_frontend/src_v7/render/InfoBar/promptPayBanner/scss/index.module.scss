.promoptTipBox {
    position: absolute;
    top: -50px;
    left: 0;
    display: flex;
    align-items: center;
    width: 100%;
    height: 40px;
    border-radius: 12px;
    border: 1px solid #fee7b0;
    background: #fcf8ea;
    margin-bottom: 10px;
    min-width: 800px;
    display: flex;
    justify-content: space-between;
    cursor: pointer;
    &.firm {
        border: 1px solid #8ea0e1;
        background: #f6f7ff;
        .btn {
            color: #fff;
            background: linear-gradient(269deg, #344475 -4.56%, #707dc2 38.69%, #545792 99.24%), #fee7b0;
        }
        .main {
            .title-box {
                color: #46558d;
            }
        }
    }
    ul,
    li {
        list-style: none;
    }
    .close {
        position: absolute;
        right: 8px;
        width: 25px;
        height: 25px;
        top: 50%;
        z-index: 10;
        cursor: pointer;
        color: #a5a3a4;
        transform: translateY(-50%);
    }
    .main {
        position: relative;
        margin-left: 24px;
        white-space: nowrap;
        .title-box {
            color: #794f22;
            display: flex;

            .title {
                font-size: 14px;
                display: flex;
                align-items: center;
                justify-content: center;
                .vip_has_day {
                    display: block;
                }
            }
            .title-tip {
                font-size: 14px;
            }
        }
        .payDesc {
            color: #f50000;
            font-size: 15px;
            font-weight: 400;
        }
        .vipName {
            font-weight: 500;
            margin: 0 4px;
        }
    }

    .btn {
        margin-right: 16px;
        white-space: nowrap;
        cursor: pointer;
        margin-left: 16px;
        color: #794f22;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 300px;
        background: #fee7b0;
        font-size: 14px;
        padding: 8px 28px;
        box-sizing: border-box;
        background: linear-gradient(180deg, #fee7b0 0%, #e7c469 100%);
    }
    .reduce-10 {
        width: 99px;
        height: 28px;
        position: absolute;
        right: 0;
        top: -28px;
        z-index: 1;
        color: var(---, #ef3964);
        border-radius: 12px 12px 0px 12px;
        background: var(---, #fdebf0);
        font-size: 14px;
        line-height: 26px;
        text-align: center;
    }
}
