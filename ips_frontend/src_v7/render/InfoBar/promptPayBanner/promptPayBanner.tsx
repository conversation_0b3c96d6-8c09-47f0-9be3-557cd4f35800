/**
 *
 * 促付费横幅
 */
import React, { useEffect, useState } from 'react';
import styles from './scss/index.module.scss';
import { message } from 'antd';
import { assetManager } from '@component/AssetManager';
import classNames from 'classnames';

interface PayInfo {
    background: string;
    href: string;
    is_firm_vip: boolean;
    is_gr_vip: boolean;
    is_grsy_vip: boolean;
    type: 0 | 1 | 2; // 1快要过期2已经过期
    vip_has_day: number;
    vip_name: string;
    is_reduce_10: number;
}

const source = 'UeEditor';

export const PromptPayBanner = () => {
    const [open, setOpen] = useState(false);
    const [payInfo, setPayInfo] = useState<PayInfo>(null);

    const whenShow = () => {
        assetManager.getPromptUserPayInfo({ source }).then((res) => {
            if (res.code == 10000) {
                const info = res?.data;
                setPayInfo(info);
                // 只有临期才展示
                if (info?.type === 1) {
                    const pid = 3142;
                    assetManager.setPv_new(pid, {
                        additional: {
                            s1: source,
                            i0: info?.is_firm_vip ? 1 : 0,
                            i1: info?.is_gr_vip ? 1 : 0,
                            i2: info?.is_grsy_vip ? 1 : 0,
                        },
                    });
                    setOpen(true);
                }
            } else if (res.stat == -1001) {
                message.warning(res.msg);
            }
        });
    };

    useEffect(() => {
        whenShow();
        // getIsShow('prompt_user_pay', whenShow, whenHide);
    }, []);

    const getTitleTxt = (overtime: boolean, notOvertime: boolean) => {
        const isFirmVip = payInfo?.is_firm_vip;
        const isGrVip = payInfo?.is_gr_vip;
        const isGrsyVip = payInfo?.is_grsy_vip;
        let titleTxt = '',
            tipTxt = '';
        if (overtime) {
            if (isFirmVip) {
                titleTxt = '，已失去全模版+可商用等特权。';
                tipTxt = '为确保会员权益正常使用，请及时续费>';
            } else if (isGrVip) {
                titleTxt = '，已失去海量模版使用、云端存储等10+项特权。';
                tipTxt = '现在续费恢复会员特权>';
            } else if (isGrsyVip) {
                titleTxt = '，已失去海量模版使用、云端存储等10+项特权。';
                tipTxt = '现在续费恢复会员特权>';
            }
        } else if (notOvertime) {
            if (isFirmVip) {
                titleTxt = `全模版+可商用等特权。`;
                tipTxt = '为确保会员权益正常使用，请及时续费>';
            } else if (isGrsyVip) {
                titleTxt = `特权。`;
                tipTxt = '为确保会员权益正常使用，请及时续费>';
            } else if (isGrVip) {
                titleTxt = `特权。`;
                tipTxt = '为确保会员权益正常使用，请及时续费>';
            }
        }

        return {
            titleTxt,
            tipTxt,
        };
    };

    const overtime = payInfo?.type === 2;
    const notOvertime = payInfo?.type === 1;
    if (!payInfo || !open) {
        return null;
    }
    const { is_firm_vip, vip_has_day, is_reduce_10, vip_name } = payInfo;
    const { tipTxt, titleTxt } = getTitleTxt(overtime, notOvertime);
    const dataS0 = overtime ? 'vipalreadyend' : 'vipend';
    const datai0 = is_firm_vip ? 1 : 0;
    const datai1 = payInfo?.is_gr_vip ? 1 : 0;
    const datai2 = payInfo?.is_grsy_vip ? 1 : 0;
    const params = {
        additional: {
            s0: dataS0,
            s1: source,
            i0: datai0,
            i1: datai1,
            i2: datai2,
        },
    };
    return (
        <div
            className={classNames(styles.promoptTipBox, is_firm_vip ? styles['firm'] : '')}
            onMouseEnter={() => {
                assetManager.setPv_new(3181, params);
            }}
            onClick={() => {
                assetManager.setPv_new(3124, params);
                window.open(payInfo?.href, '_blank');
            }}
        >
            <div className={styles.main}>
                <div className={styles['title-box']}>
                    {overtime && (
                        <div className={styles['title']}>
                            您的<span className={styles.vipName}>{vip_name}</span>已过期
                            {/* <span>{vip_has_day}</span>天 */}
                            {titleTxt}
                        </div>
                    )}
                    {notOvertime && (
                        <div className={styles['title']}>
                            还有 <span className={styles.vip_has_day}>{vip_has_day}</span>
                            天即将失去
                            <span className={styles.vipName}>{vip_name}</span>
                            {titleTxt}
                        </div>
                    )}
                    <div className={styles['title-tip']}>{tipTxt}</div>
                </div>
            </div>
            <div className={styles.btn}>立即续费</div>
            {is_reduce_10 > 0 && <div className={styles['reduce-10']}>续费立减10元</div>}
        </div>
    );
};
