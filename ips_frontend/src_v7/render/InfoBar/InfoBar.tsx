import React, { Component, UIEventHandler } from 'react';
import { klona as cloneDeep } from 'klona';
// import md5 from 'js-md5';
// import sortKeys from 'sort-keys';

import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { assetManager } from '@component/AssetManager';
import { emitter } from '@component/Emitter';
import { IPSConfig } from '@v7_utils/IPSConfig';
import { getProps, ipsApi, getFontNameList } from '@component/IPSConfig';


import { PreviewAndShare } from '@component/PreviewAndShare/PreviewAndShare';
import { PreviewCanvas } from '@component/PreviewAndShare/PreviewCanvas';
import { WeChatOpenPlatform } from '@component/WeChatOpenPlatform/WeChatOpenPlatform';
import { PhoneBind } from '@component/PhoneBind/PhoneBind';
import {templateFormat} from '../../../src/userComponentV6.0/TemplateFormat';
import {TgsRequestManager } from "../../../../ips_component_publish/tgs-component/src/index";

import { urlEncode, checkoutAssetIsInArea } from '@component/Function';
// @ts-ignore
import { QRCodeCanvas } from 'qrcode.react';
import { EventListener } from '@v7_utils/AddEventListener';
import { UserCheck } from '@component/UserCheck/UserCheck';
import { Text3DLocalGenerate } from '@component/Text3DLocalGenerate/Text3DLocalGenerate';
import { tempIdBuilder } from '@component/tool/template';
import { BigPopupWindow } from '@component/canvas/BigPopupWindow';
import { IAsset, IDoc, IPageAttr, ITemplateInfo, IAnyObj, IRichText } from '@v7_logic/Interface';
import { EventSubscription } from 'fbemitter';
import { addEventListener } from '@v7_utils/AddEventListener';
import { storeDecorator } from '@v7_logic/StoreHOC';
import {IRatifyStatusPage,IApprovalData} from '@v7_logic/Interface'; 
import {RatifyPopUp,RatifyStatusPage,RatifyOption} from '@v7_render/RatifyPopUp'; 

import { SelectAsset } from '@v7_logic/AssetLogic';
import { AnimationEffect } from '@v7_logic/AnimationEffect'

import './scss/InfoBar.scss';
import './scss/LogoDetail.scss'

import {
    UploadGroupWord,
    DownloadPopup,
    SaveTeamTemplePopup,
    ForceRefreshModal,
    DownloadBootPage,
    UserProfile,
    DesignerTaskKeyWords,
    UploadPopup,
    UploadDocPanel,
    DownloadBootPageNew,
} from '@component/InfoBar';
import { SaveWeChatPublic } from '@v7_render/SaveWeChatPublic';
import { CanvasPaintedLogic } from '@v7_logic/CanvasPaintedLogic';
import { TemplateHistory } from '@v7_logic/TemplateHistoryLogic';
import { InfoManageHelper } from '@v7_logic/StoreLogic/InfoManageHelper';
import { TgsManageHelper } from '@v7_logic/StoreLogic';
import { EditorLogic } from '@v7_logic/EditorLogic';
// import { RelineAndTemplate } from '@src/userComponentV6.0/canvas/RelineAndTemplate';
import { ErrorBoundaryDecorator } from '../ErrorBoundaryHOC';
import { OperationRecord } from '@v7_logic/OperationRecord';
import { TemplateLogic } from '@v7_logic/TemplateLogic';
import { showCollaborativeLimit, CooperationUsers } from '@v7_render/Collaborative';
import { ShareLinkPopup } from '@src/userComponentV6.0/ShareLinkPopup/ShareLinkPopup';
import { ClickOutside } from '@v7_render/Ui';
import { DocHashLogic } from '@v7_logic/DocHash';
import { RedoAndUndo } from '@v7_store/logic/onCanvasPaintedReducer';

import { SaveToWeibo } from  '@v7_render/SaveToWeibo';
import { SaveToTikTok } from '@v7_render/SaveToTikTok';

import { env } from '@editorConfig/env';

import { VipAd } from './VipAd';
import loaclGenerate from './localGenerate';
import { isEcommerce, isEcommerceTeam, isUeTeam } from '@v7_utils/webSource';
import { checkIsKickOut, checkIsUserGrayScale } from '@v7_utils/user';
import {SaveTeamPopup} from './Popup/SaveTeamPopup';
import { baseTipInfo } from '@v7_render/TipInfo';
import { isAIDesign, isAIDesignPicDrawTemp } from '@v7_utils/estimate';
const TGS_SET_ONLINE_DETAIL_DOWNLOAD_TYPE = 'TGS_SET_ONLINE_DETAIL_DOWNLOAD_TYPE'; //电商详情下载类型

let downloadMoreActionsMouseEnterTime = 0;
let downloadDropDownTimer: ReturnType<typeof setTimeout>

interface propsStruct {
    isInfoBarABTestNewStyle?: HTMLDivElement;
    origin?: string;
    shareUrl?: string; //分享链接
    type?: string;
    prefix?: string;
    info?: ITemplateInfo,
}

interface TemplateList {
    id: number;
    width: number;
    height: number;
    preview: string;
    small: string;
    isDesigner: boolean;
    title: string;
}

interface stateStruct {
    saveText: string;
    saveBtnTipText: string;
    saveBtnTipColor: string;
    saveBtnTipShow: number;
    saveTime: string;
    saveTemplTip: JSX.Element | string;
    saveType: string;
    filesaveTemplTip: JSX.Element | string;
    userTemplateList: TemplateList[];
    isNotLoginSaveTipShow: boolean | number;
    snum: number | string;
    newYearActivity: number;
    vipType: number;
    is_vip_templ: boolean;
    teamInfo: [];
    isTeam: boolean;
    isShowTeam: boolean;
    isShowTeamArea: boolean;
    isShowClickTeam: boolean;
    userTeamInfo: string;
    userProjectInfo: string;
    chooseTeamIndex: number;
    showChooseTeam: boolean;
    showChooseProject: boolean;
    online_detail_download_type: 0 | 1 | 2; //电商详情下载类型
    showStoreOurWebsite: boolean; //显示收藏网站弹窗
    websiteLastSecond: number; //显示收藏网站弹窗倒计时
    picId: string;
    isFileTemplTipAreaShow?: boolean;
    fileType: string;
    dpi: number;
    isOpenDownload: boolean;
    isPrint: boolean;
    isloading: boolean;
    isVideo: boolean;
    newPulldownStyle: React.CSSProperties;
    pulldownStyle: React.CSSProperties;
    ratifyStatus?: IRatifyStatusPage;
    isHiddenNoRisk: boolean;
    hasCheckedUpicIdOwner: boolean;
    isUpicIdOwner: boolean;
    versionList:ITempelateVersion[];
    showNotLoginTip: boolean;
    notLoginSaveActionCount: number;
    disableNotLoginTip: boolean;
    is_personal_com_vip: boolean;
    governmentTempl: boolean;
    is_old_vip: boolean;
    is_person_business_vip: boolean;
    filePopoverShow: boolean;
    filePopoverFocused: boolean; //菜单聚焦状态
    show_but: number,
    copy_success: boolean; // 分享链接复制成功
    sharePlaneType: string;
    isShowFriendBtn: boolean;
    shareUrl?: string;
    is_ppt_vip?:number
    unit?:number;
    showVipRecharge: boolean;
    showSurveyModal: boolean;
    isSendSurveyQuery: boolean;
}

export interface ITempelateVersion {
    id: string;
    created_at: string;
    preview: string;
    title: string;
    version: string;
    updated_at: string;
}

export class InfoBarComponent extends Component<propsStruct, stateStruct> {
    saveBtnTipColor1: string;
    saveBtnTipColor2: string;
    isSaveing: boolean;
    selectTeamId:string;
    selectTeamName:string;
    lastEditTime: number;
    isFirstEditSave: boolean;
    nowInput: string;
    interval: number;
    saveKeyDownEventEmitter: EventSubscription;
    autoSaveTemplListenerEmitter: EventSubscription;
    updateFileTemplTipEmitter: EventSubscription;
    bindWeChatPlatformEmitter: EventSubscription;
    saveOperationsEmitter: EventSubscription;
    getDownloadNumInfoEmitter: EventSubscription;
    websiteLastSecondTimer: NodeJS.Timeout;
    timer1: NodeJS.Timeout;
    timer2: NodeJS.Timeout;
    timer3: NodeJS.Timeout;
    timer4: NodeJS.Timeout;
    phoneBindEmitter: EventSubscription;
    businessUseBtnTipsHoverTime: NodeJS.Timeout;
    InfoBarclearIntervalEmitter: EventListener;
    InfoBarUpdateState: EventSubscription;
    moreBtn: number;
    showStoreOurWebsiteDialogEmitter: EventSubscription;
    getPicIdEmitter: EventSubscription;
    popupClose: EventSubscription;
    tucaoActivitiesAreaTimeout: NodeJS.Timeout;
    vipExtensionTimeout: NodeJS.Timeout;
    businessUseBtnTipsTimer: NodeJS.Timeout;
    downloadParams: { origin: string };
    download_params: { from: string };
    intervalDownloadStart: NodeJS.Timeout;
    uploadWebChatPlatformEmitter: EventSubscription;
    save_set_interval_listen: NodeJS.Timeout;
    newFontImageAll: any;
    businessUseBtnHoverTime: number;
    businessUseBtnTimer: NodeJS.Timeout;
    downloadFileEmitteEmitter: EventSubscription;
    infoBarDownloadEmitter: EventSubscription;
    satisfactionSurveyEmitter: EventSubscription;
    reloadLogin = false;
    PromptBoxTime: number;
    PromptBoxTimes: NodeJS.Timeout;
    fontNameValueList: any;
    fontNameList: any;
    zhFontDate: string[];
    vipFont: string[];
    downloadTimeLock: boolean;
    downloadTimeLockTimer: any;
    vipSlideBoxTimeout: NodeJS.Timeout;
    eleInners: any;
    liLists: any;
    liWidth: number;
    index: number;
    wheelPlayTimer: NodeJS.Timeout;
    shareTimers: NodeJS.Timeout;
    cancelShareEmitter: EventSubscription;
    onDetailDownType: HTMLDivElement;
    beginRatifyEmitter:EventSubscription;
    isShowWeibo: boolean;
    editShareLogLock:boolean;
    closeToolTipKnow: EventSubscription;
    PromptBoxTime1:NodeJS.Timeout;
    shareTimer:NodeJS.Timeout;
    timerLogo: NodeJS.Timeout;
    constructor(props: propsStruct) {
        super(props);

        this.saveBtnTipColor1 = '#c0c0c0';
        this.saveBtnTipColor2 = '#f9a49a';

        this.isSaveing = false;
        this.selectTeamId = '';
        this.selectTeamName = '';
        this.lastEditTime = 0;
        this.isFirstEditSave = true;

        this.nowInput = '';

        this.fontNameValueList = {};
        this.fontNameList = getFontNameList();
        Object.keys(this.fontNameList).map((key) => {
            Object.assign(this.fontNameValueList, { [this.fontNameList[key]]: key });
        });

        // 魂網字体检测
        this.newFontImageAll = {};

        this.zhFontDate = [
        //     'font207',
        //     'font202',
        //     'font208',
        //     'font209',
        //     'font206',
        //     'font210',
        //     'font204',
        //     'font205',
        //     'font211',
        //     'font212',
        //     'font213',
        //     'font214',
        //     'font215',
        //     'font216',
        //     'font217',
        //     'font218',
        //     'font219',
        //     'font220',
        //     'font221',
        //     'font222',
        //     'font223',
        //     'font224',
        //     'font225',
        //     'font226',
        //     'font227',
        //     'font228',
        //     'font229',
        //     'font230',

        //     'font231',
        //     'font232',
        //     'font233',
        //     'font234',
        //     'font235',
        //     'font236',
        //     'font237',
        //     'font238',
        //     'font239',
        //     'font240',
        //     'font241',
        //     'font242',
        //     'font243',
        //     'font244',
        //     'font245',
        //     'font246',
        //     'font247',
        //     'font248',
        //     'font249',
        //     'font250',
        //     'font251',
        //     'font252',
        //     'font253',
        //     'font254',

        //     'font255',
        //     'font256',
        //     'font257',

        //     'font258',
        //     'font259',
        //     'font260',
        //     'font261',
        //     'font262',
        //     'font263',
        //     'font264',
        //     'font265',
        //     'font266',
        //     'font267',
        //     'font268',
        //     'font269',
        //     'font270',
        //     'font271',
        //     'font272',
        //     'font273',
        //     'font274',
        //     'font275',
        //     'font276',
        //     'font277',
        //     'font280',
        ];

        this.vipFont = [
            'zh2hllcht',
            'zh3hyxht',
            'zh49hxyxs',
            'zh71hysjs',
            'zh96hhxss',
            'zh111hjbzpt',
            'zh48btymhss',
            'zh67gyxs',
            'zh185zybs',
            'zh450fcpyt',
        ];

        this.state = {
            saveText: '保存',
            saveBtnTipText: '保存中',
            saveBtnTipColor: this.saveBtnTipColor1,
            saveBtnTipShow: 0,
            saveTime: '',
            saveTemplTip: '',
            saveType: 'noSave',
            filesaveTemplTip: undefined,
            userTemplateList: [],
            isNotLoginSaveTipShow: false,
            snum: null,
            newYearActivity: 0,
            vipType: null,
            is_vip_templ: null,
            teamInfo: [],
            isTeam: false,
            isShowTeam: false,
            isShowTeamArea: false,
            isShowClickTeam: false,
            userTeamInfo: '',
            userProjectInfo: '',
            chooseTeamIndex: 0,
            showChooseTeam: false,
            showChooseProject: false,
            online_detail_download_type: 1, //电商详情下载类型
            showStoreOurWebsite: false, //显示收藏网站弹窗
            websiteLastSecond: 5, //显示收藏网站弹窗倒计时
            picId: '',
            fileType: 'jpg',
            dpi: 150,
            isOpenDownload: true,
            isPrint: false,
            isloading: false,
            isVideo: false,
            newPulldownStyle: {},
            pulldownStyle: {},
            isHiddenNoRisk: false,
            hasCheckedUpicIdOwner: false,
            isUpicIdOwner: false,
            versionList:[],
            showNotLoginTip: false,
            notLoginSaveActionCount: 0,
            disableNotLoginTip: false,
            is_old_vip: null,
            is_personal_com_vip: null,
            governmentTempl: null,
            is_person_business_vip: undefined,
            filePopoverShow: false,
            filePopoverFocused: false,
            show_but: 1,
            copy_success: false,
            sharePlaneType: 'default',
            isShowFriendBtn: false,
            shareUrl: undefined,
            is_ppt_vip:0,
            unit:0,
            showVipRecharge: false,
            showSurveyModal: false,// 满意度调查
            isSendSurveyQuery: false, // 是否发送满意度调查
        };

        TgsManageHelper.tgsSetOnlineDetailDownloadType({
            online_detail_download_type: this.state.online_detail_download_type as 0 | 1 | 2,
        });

        this.interval = 0;
        this.uploadClickEvent = this.uploadClickEvent.bind(this);
        this.saveClickEvent = this.saveClickEvent.bind(this);
        this.yulanClickEvent = this.yulanClickEvent.bind(this);

        this.autoSaveTemplListener();
        this.downloadEmitter();
        this.updateState();
        this.clearIntervalEmitter();
        this.saveKeyDownEmitter();
        this.saveOperationEmitter();
        this.bindWeChatOpenPlatformEmitter();
        this.uploadWebChatOpenPlatformEmitter();
        this.phoneBindPopupEmitter();
        this.updateFileTemplTipAreaEmitter();
        this.download2Emitter();
        this.downloadFileEmitter();
        this.getTeamDetailInfomation();
        this.getUserTemplateList();
        this.getDownloadNumInfoListener();
        // this.setZhFontDate();
        this.cancelShare();
        this.getApprovalShareList();
        this.beginRatify()
        this.setZhFontDate()
        this.cancelShare()
        this.getUserTemplateVersionslList()
        this.isShowWeibo = false;
        this.closeToolTipNoLoginTip();
        this.addSatisfactionSurvey()
    }

    /**
     * 获取模板版本列表信息
     */
    getUserTemplateVersionslList(){
        if(Number.parseInt(IPSConfig.getProps().upicId) > 0){
            assetManager.getUserTemplateVersionslList().then((data) => {
                data.json().then((resultData) => {
                    resultData.data?.sort?.((a: any, b: any) => b.version - a.version);
                    this.setState({
                        versionList : resultData.data
                    })
                    let version = 0
                    const {version_id} = IPSConfig.getProps(true)
                    if( !version_id){
                        version = 1
                    }else{
                        for (const versionItem of resultData.data){
                            if( versionItem.id === version_id ){
                                version = versionItem.version
                                break
                            }
                        }
                    }
                    storeAdapter.dispatch({
                        store_name: storeAdapter.store_names.paintOnCanvas,
                        fun_name: 'UPDATE_VERSION',
                        params: [
                            {
                                type: 'updateVersion',
                                params: {
                                    version,
                                },
                            },
                        ],
                    });
                });
            });
        }
    }

    /**
     * 获取审核状态弹框列表信息
     */
    getApprovalShareList():void{
        assetManager.getApprovalShareList().then((data) => {
            data.json().then((resultData) => {
                // console.log("resultData",resultData)
                this.setState({
                    ratifyStatus : resultData.data
                })
            });
        });
    }

    /**
     * 开始审批
     */
     beginRatify():void{
        this.beginRatifyEmitter = emitter.addListener('beginRatify', (props = {}) => {
            this.clickShareButton(true)
        })
    }

    /**
     * 取消分享
     */
    cancelShare(): void {
       this.cancelShareEmitter = emitter.addListener('cancelShare', (props = {}) => {
           this.shareTimers && clearInterval(this.shareTimers)
       });
    }

    getUserTemplateList(): void {
        const props = getProps() as unknown as { isDesigner: number };
        if (props.isDesigner) {
            this.getDesignerTemplateList();
        } else {
            assetManager.getUserTempls().then((data) => {
                data.json().then((resultData) => {
                    if (Number(resultData.stat) === 1) {
                        this.setState({
                            userTemplateList: resultData.msg,
                        });
                    }
                });
            });
        }
    }

    /**
     * 设置字魂字体
     */
    setZhFontDate(): void{
        TgsRequestManager.getFontsShowInfomation().then(data => {
            data.json().then(res => {
                if(res.stat == 1){
                    res.msg.forEach((item: { list: any[]; }) => {
                        item.list.forEach(v => {
                            if(v.is_zihun == '1'){
                                this.zhFontDate.push(v.font_id)
                            }
                        })
                    });
                }
            })
        })
    }

    /**
     * 监听下载模板次数调用
     */
    getDownloadNumInfoListener(): void {
        this.getDownloadNumInfoEmitter = emitter.addListener('getDownloadNumInfo', (prop: boolean) => {
            this.getDownloadNum(prop);
        });
    }

    /**
     * 获取设计师模板列表
     */
    getDesignerTemplateList(): void {
        assetManager.getDesignerTemplList().then((data) => {
            data.json().then((resultData) => {
                if (Number(resultData.stat) === 1) {
                    const data: TemplateList[] = [];
                    resultData.data.map((item: TemplateList) => {
                        data.push({
                            id: item.id,
                            width: item.width,
                            height: item.height,
                            preview: item.preview,
                            small: item.preview,
                            isDesigner: true,
                            title: item.title,
                        });
                    });

                    this.setState({
                        userTemplateList: data,
                    });
                }
            });
        });
    }

    updateFileTemplTipAreaEmitter(): void {
        this.updateFileTemplTipEmitter = emitter.addListener(
            'InfoBarUpdateFileTemplTipAreaEmitter',
            (flag: boolean) => {
                this.setState({
                    isFileTemplTipAreaShow: flag,
                });
            },
        );
    }

    getTeamDetailInfomation(): void {
        assetManager.getTeamDetailInfomation().then((data) => {
            data.json().then((resultData) => {
                if (Number(resultData.stat) === 1 && resultData.msg.length > 0) {
                    this.setState({
                        teamInfo: resultData.msg,
                        isTeam: true, // 有团队
                        userTeamInfo: resultData.msg[0],
                    });
                    if (resultData.msg[0].folder.length > 0) {
                        this.setState({
                            userProjectInfo: resultData.msg[0].folder[0],
                        });
                    }
                }
            });
        });
    }

    clickTeamFunct(e: MouseEvent): void {
        // const { info } = storeAdapter.getStore({
        //     store_name: storeAdapter.store_names.InfoManage,
        // });
        // const { class_id = [] } = info || {};
        // const s0 = class_id.includes('1') ? '公众号首图' : class_id.includes('7') ? '公众号次图' : '';
        assetManager.setPv_new(5138);

        this.setState({
            // isShowTeam: !this.state.isShowTeam,
            isShowTeam: false,
            isShowClickTeam: !this.state.isShowClickTeam,
        });
        assetManager.setPv_new(3874, { additional: {} });
        e.stopPropagation();
    }

    changeProjectType(item: string, e: MouseEvent): void {
        this.setState({
            userProjectInfo: item,
            showChooseProject: false,
        });
        assetManager.setPagePv_new(2490);
        e.stopPropagation();
    }

    /**
     * 绑定微信公众号成功之后
     */
    bindWeChatOpenPlatformEmitter(): void {
        this.bindWeChatPlatformEmitter = emitter.addListener('InfoBarBindWeChatOpenPlatform', (props = {}) => {
            this.showWeChatOpenPlatform(props);
        });
    }

    onMouseEnterSave = () => {
        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (Number.parseInt(user.userId) <= 0 && this.state.notLoginSaveActionCount >= 1) {
            assetManager.setPv_new(6852)
        }
    }
    /**
     * jyjin 保存按钮点击事件
     */
    saveClickEvent(e: React.MouseEvent<HTMLDivElement, MouseEvent>, template_save_type?: string): void {
        checkIsKickOut().then(() => {

        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (!(user.userId > 0)) {
            emitter.emit('LoginPanelShow', 'saveTempl');
            assetManager.setPv_new(174, {
                additional: {
                    s0: 'saveTempl',
                },
            });
            assetManager.setPv_new(4572, {additional: {
                s0: 'saveTempl'
            }})
            if (this.state.notLoginSaveActionCount >= 1) {
                assetManager.setPv_new(6853)
            }
            return;
        }
        if (this.isSaveing && template_save_type != 'share') {
            return;
        }
        /*编辑器判断*/
        const { isDesigner } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const urlProps = IPSConfig.getProps();
        if (isDesigner) {
            emitter.emit('autoSaveTempl', '', 0, { iszdbc: true });
        } else if(template_save_type == 'share'){
            assetManager.copyUserTempltoTeam({
                team_id: this.selectTeamId,
                id:urlProps.upicId,
                source_type: isUeTeam ? 2 : 1,
            }).then(res=>{
                res.json().then((resultData) => {
                    if(resultData.stat == 1){
                        const jump = this.selectTeamId 
                        ?
                         `https://818ps.com/home/<USER>/design?team_id=${ this.selectTeamId || getProps().team_id }`
                          : "//818ps.com/home/<USER>";
                        // 保存成功提示框
                        baseTipInfo.success(`成功分享到${this.selectTeamName}的团队设计`,{
                            customContent: <a href={jump} target='_blank' rel="noreferrer" style={{
                                color: '#0EB52D',
                                marginLeft:'8px',
                                fontWeight:'600',
                            }}>
                                查看
                            </a>
                        })
                        emitter.emit('popupClose');
                        this.selectTeamId = ''
                        if (this.PromptBoxTime) {
                            clearTimeout(this.PromptBoxTime);
                        }
                    }
                })
            })
        } else {
                // emitter.emit('autoSaveTempl', '', 1, {handleSave: true});
                let time: NodeJS.Timeout = null;
                emitter.emit('autoSaveTempl', '', 1, {handleSave: true, template_save_type: template_save_type || '', saveToTeam: true}, false,(flag:boolean)=>{
                    if(flag){
                        if(time){
                            clearTimeout(time);
                        }
                        time = setTimeout(()=>{
                            if(!urlProps.version_id){
                                const tempUrl = document.location.href + "&version_id=0";
                                history.pushState('', document.title, tempUrl);
                            }
                            // this.getUserTemplateVersionslList();
                        },100)
                        // 兼容弹出框保存团队设计的情况，保存完毕关闭弹窗
                        emitter.emit('popupClose');
                        
                        this.isShowSurveyModal()
                    }
                });
        }
        assetManager.setPv_new(2470, { additional: {} });

        e?.stopPropagation();
        e?.nativeEvent.stopPropagation();

        }, () => {
            return
        })
    }
    /**
     * 保存按钮点击事件
     */
    filesaveClickEvent(template_save_type:string,e: React.MouseEvent): void {
        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (!(user.userId > 0)) {
            emitter.emit('LoginPanelShow', 'saveTempl');
            return;
        }
        if (this.isSaveing) {
            return;
        }
        /*编辑器判断*/
        const { isDesigner } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        const urlProps = IPSConfig.getProps();

        if (isDesigner) {
            emitter.emit('autoSaveTempl', '', 1, { iszdbc: true, template_save_type:template_save_type});
        } else {
            // emitter.emit('autoSaveTempl', '', 1, {handleSave: true}, true);
            if(urlProps?.upicId || template_save_type != 'versions'){
                emitter.emit('autoSaveTempl', '', 1, {handleSave: true, template_save_type:template_save_type});
            } else {
                let time: NodeJS.Timeout = null;
                emitter.emit('autoSaveTempl', '', 1, {handleSave: true, template_save_type:template_save_type}, true,(flag:boolean)=>{
                    if(flag){
                        if(time){
                            clearTimeout(time);
                        }
                        time = setTimeout(()=>{
                            if(!urlProps.version_id){
                                const tempUrl = document.location.href + "&version_id=0";
                                history.pushState('', document.title, tempUrl);
                            }
                            this.getUserTemplateVersionslList();
                            emitter.emit('autoSaveTempl', '', 1, {handleSave: true, template_save_type:template_save_type});
                        },100)
                    }
                });
            }
            // emitter.emit('autoSaveTempl', '', 1, {handleSave: true, template_save_type:template_save_type});
        }
        
        if( template_save_type === "versions"){
            assetManager.setPv_new(5606)
        }else{
            assetManager.setPv_new(3688, { additional: {} });

        }
        e.stopPropagation();
        e?.nativeEvent.stopPropagation();
    }
    /**
     * 保存按钮点击事件
     */
    saveErrorClickEvent(e: React.MouseEvent): void {
        this.isSaveing = false;
        /*编辑器判断*/
        const { isDesigner } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if (isDesigner) {
            emitter.emit('autoSaveTempl', '', 1, { iszdbc: true });
        } else {
            emitter.emit('autoSaveTempl', '', 1);
        }

        e.stopPropagation();
        e?.nativeEvent.stopPropagation();
    }

    /**
     * 快捷键保存
     */
    saveKeyDownEmitter(): void {
        // eslint-disable-next-line @typescript-eslint/ban-types
        this.saveKeyDownEventEmitter = emitter.addListener(
            'autoSaveKeyDownTempl',
            (callback: Function, isTip: boolean) => {
                const { user } = storeAdapter.getStore({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                });
                if (!(user.userId > 0)) {
                    emitter.emit('LoginPanelShow');
                    return;
                }
                const { saveText } = this.state;
                if (saveText === '保存中...') {
                    return;
                }
                /*编辑器判断*/
                const { isDesigner } = storeAdapter.getStore({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                });

                const urlProps = IPSConfig.getProps();

                if (isDesigner) {
                    emitter.emit('autoSaveTempl', callback, isTip, { iszdbc: true });
                } else {
                    // emitter.emit('autoSaveTempl', callback, isTip, {handleSave: true});
                    let time: NodeJS.Timeout = null;
                    emitter.emit('autoSaveTempl', callback, isTip, {handleSave: true}, false,(flag:boolean)=>{
                        if(flag){
                            if(time){
                                clearTimeout(time);
                            }
                            time = setTimeout(()=>{
                                if(!urlProps.version_id){
                                    const tempUrl = document.location.href + "&version_id=0";
                                    history.pushState('', document.title, tempUrl);
                                }
                                // this.getUserTemplateVersionslList();
                            },100)
                            this.isShowSurveyModal()
                        }
                    });
                }
            },
        );
    }

    /**
     * 触发满意度调查问卷
     */
    addSatisfactionSurvey(): void {
        this.satisfactionSurveyEmitter = emitter.addListener(
            'satisfactionSurvey',
            ()=>{
                this.isShowSurveyModal()
            })
    }

    /**
     * 自动保存
     */
    saveOperationEmitter(): void {
        // let number = 0;
        let retryEditTime: NodeJS.Timeout;
        this.saveOperationsEmitter = emitter.addListener('autoSaveOperationTempl', (status: string) => {
            const th = this;
            //处理用户有效编辑时长
            function saveEditTime(flag:number) {
                retryEditTime && clearTimeout(retryEditTime);
                retryEditTime = setTimeout(() => {
                    let editTime;
                    if (!th.lastEditTime) {
                        //第一下编辑
                        editTime = 1
                    } else {
                        //后续编辑
                        const time = Math.round((new Date().getTime() - th.lastEditTime ) / 1000);
                        if (time > 30 || time < 0) {
                            editTime = 1;
                        } else {
                            editTime = time;
                        }
                    }
                    th.lastEditTime = new Date().getTime();
                    assetManager.setPv_new(7011, {
                        additional: {
                            i0: editTime,
                            i1: flag,
                        },
                    })
                }, 1000);
            }
            // 记录用户有效编辑模板次数
            if (th.isFirstEditSave) {
                assetManager.setPv_new(7015);
                th.isFirstEditSave = false;
            }

            if (status === 'notLogin') {
                /*   this.state.isNotLoginSaveTipShow !== 0 && this.setState({
                    isNotLoginSaveTipShow:true
                })*/
                // if(!this.state.showNotLoginTip){
                //     number++;
                // }
                // if(number === 5 && !this.state.showNotLoginTip){
                //     number = 0;
                //     this.setState({
                //         showNotLoginTip: true
                //     },()=>{
                //         assetManager.setPv_new(5704);
                //     })
                // }
                saveEditTime(2);

                this.setState({
                    notLoginSaveActionCount: this.state.notLoginSaveActionCount + 1,
                }, () => {
                    if (this.state.notLoginSaveActionCount >= 3) {
                        this.setState({
                            showNotLoginTip: true,
                        })
                        assetManager.setPv_new(6859);
                    }
                })
            } else {
                saveEditTime(1);

                this.isSaveing = false;
                emitter.emit('autoSaveTempl', '', 0, { iszdbc: true });
                if(this.state.showNotLoginTip){
                    this.setState({
                        showNotLoginTip: false
                    })
                }
            }
        });
    }

    /**
     * 保存成功提示我的设计（点击事件）
     * @param e
     */
    saveTipAreaClickEvent(teamId:string,e:React.MouseEvent,): void {
        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (checkIsUserGrayScale(user.userId) && teamId) {
            e.preventDefault();
            window.open(
                `https://818ps.com/home/<USER>/design?team_id=${teamId}&origin=${isUeTeam ? 'ueTeam' : env.editor}`,
                '__blank',
            );
        }
        assetManager.setPv_new('128');
    }

    // 满意度调查
    isShowSurveyModal() {
        if (!this.state.isSendSurveyQuery) {
            return;
        }
        // 编辑时常是否大于1分钟
        const { createTime } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const nowTime = new Date().getTime() / 1000;
        if (createTime && (nowTime - createTime) > 60) {
            assetManager.isOpenSatisfactionSurvey().then((data) => {
                // 用户满意度调查弹窗
                if (data?.data?.status === 1) {
                    assetManager.setPagePv_new(9039)
                    this.setState({
                        showSurveyModal: true
                    })
                } else if (data?.data?.status > 1) {
                    this.setState({
                        isSendSurveyQuery: false
                    })
                }
            })
        }
    }
    
    hideSurveyModal = () => {
        assetManager.setPagePv_new(9041)
        assetManager.closeSatisfactionSurvey();
        this.setState({ showSurveyModal: false });
    }
    //切换版本
    changeVersion(version_id:number){
        const reg = /&version_id=[^&#]*/
        let tempUrl
        if(reg.test(document.location.href)){
            tempUrl = document.location.href.replace(/&version_id=[^&#]*/, '');
        }else{
            tempUrl = document.location.href.replace(/version_id=[^&#]*/, '');
        }
        tempUrl = tempUrl + "&version_id=" + version_id
        window.open(tempUrl)
        assetManager.setPv_new(5603)
    }

    /**
     * jyjin自动保存模版
     */
    autoSaveTemplListener(): void {
        const urlProps = getProps() as unknown as { GroupWordUser: number };
        if (Number(urlProps.GroupWordUser) === 1) {
            return;
        }
        let failCount = 0;
        let retrySaveTimer: NodeJS.Timeout;
        let checkHash = '';
        let autoSaveCount = 0;
        this.autoSaveTemplListenerEmitter = emitter.addListener(
            'autoSaveTempl',
            (
                callback: React.FocusEvent,
                isTip: boolean,
                propst: {
                    startSaveTime?: number;
                    switchName?: string;
                    newpicId?: number;
                    iszdbc?: string;
                    onlineDetailSinle?: boolean;
                    reloadLogin?: boolean;
                    handleSave?: boolean;
                    callbackProps?:any;
                    template_save_type?: string; //是否保存为新版本（加入这行注释时，版本和审阅功能捆绑）
                    saveToTeam?: boolean; // 标记为保存到团队模版时调用；这个为true, 不用干掉template_save_type的参数
                    saveSuccessCb?:()=>void;
                    message_id?: string;
                },
                isShare?: boolean,
                returnSaveResult?: Function // 返回保存成功或失败  boolean
            ) => {
                if (failCount !== 0) {
                    failCount = 0;
                    retrySaveTimer && clearTimeout(retrySaveTimer);
                    checkHash = '';
                }
                if (this.isSaveing) {
                    return;
                } else {
                    this.isSaveing = true;
                    // if( this.isSaveingTimeout ){
                    //     clearTimeout(this.isSaveingTimeout);
                    // }
                    // this.isSaveingTimeout = setTimeout(() => {
                    //     this.isSaveing = false
                    // }, 1000)
                }
                const {
                    page_map,
                    canvas,
                    work,
                    pageInfo,
                    pageAttr,
                    rt_mutisize_current_selected_tid,
                    rt_isMutiSizeTemplate,
                    rt_mutisize_current_selected_kidInfo,
                    isDesigner,
                    rt_mutisize_subtemplates,
                    rt_is_online_detail_page,
                } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                });
                propst = propst || {};
                const { operationRecord } = storeAdapter.getStore<typeof storeAdapter.store_names.operationRecordRedux>(
                    {
                        store_name: storeAdapter.store_names.operationRecordRedux,
                    },
                );
                const { info } = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
                    store_name: storeAdapter.store_names.InfoManage,
                });

                let reqArray: ITemplateInfo & {
                    picId?: string;
                    doc?: IDoc;
                    isSingleFloor?: boolean;
                } = Object.assign({}, info, {page_map});
                 // 替换成在保存团队弹出框中选择的团队id
                if( this.selectTeamId ) {
                    reqArray['team_id'] = this.selectTeamId
                }
                let { saveTime, saveTemplTip, filesaveTemplTip, saveType } = this.state;

                // const urlProps = getProps();
                const urlProps = IPSConfig.getProps();
                if (autoSaveCount >= 5 && urlProps.paperId) {
                    propst.handleSave = true;
                }
                autoSaveCount++;
                // let k1 = info.k1 > 0 ? info.k1 : urlProps['k1'];
                // let k2 = info.k2 > 0 ? info.k2 : urlProps['k2'];
                // let k3 = info.k3 > 0 ? info.k3 : urlProps['k3'];
                const flag = work.pages.every((item) => item.assets.length === 0);
                if (flag) {
                    this.setState({
                        saveBtnTipShow: 1,
                        saveBtnTipText: '不能保存空模板',
                        saveBtnTipColor: this.saveBtnTipColor2,
                    });
                    return false;
                }

                let picId = urlProps['picId'];

                if (Number(urlProps['assetId']) > 0 && !(Number(urlProps['picId']) > 0)) {
                    picId = `${OperationRecord.source}`;
                }
                let newId = urlProps['upicId'];
                // 对切换账号模板处理
                if (propst && propst.switchName) {
                    newId = '';
                    picId = `${propst.newpicId}`;
                }
                if (rt_isMutiSizeTemplate && !info.dont_show_more_size && isDesigner) {
                    // picId = rt_mutisize_current_selected_tid || '';
                    // newId = rt_mutisize_current_selected_tid || '';
                    reqArray.kid_1 = rt_mutisize_current_selected_kidInfo.kid_1 || reqArray.kid_1;
                    reqArray.kid_2 = rt_mutisize_current_selected_kidInfo.kid_2 || reqArray.kid_2;
                }
                let again_login = 0;
                if (propst?.reloadLogin) {
                    this.reloadLogin = propst.reloadLogin;
                    again_login = 1;
                }
                const editTime = this.calcEditTime();
                Object.assign(reqArray, {
                    id: newId || 0,
                    doc: { canvas: canvas, work: work, pageAttr: pageAttr },
                    title: info.title,
                    picId: picId,
                    st: propst && propst.iszdbc ? 'zdbc' : '',
                    asset_id: urlProps['assetId'],
                    width: canvas.width,
                    height: canvas.height,
                    again_login: again_login,
                    paperId: urlProps.paperId || 0,
                    template_save_type:propst.template_save_type || undefined, //保存为新版本,
                    version_id: !propst.template_save_type && getProps().version_id ? getProps().version_id : undefined,
                    edit_time: editTime
                });
                if(isAIDesignPicDrawTemp()){
                    const primaryPicAsset = this.getPirmaryAsset();
                    if(primaryPicAsset){
                    reqArray['design_main_image_id'] = primaryPicAsset.attribute.resId;
                    }
                }
                if(!urlProps.upicId && !propst.saveToTeam && !(env.teamTemplate || isUeTeam)){
                    Object.assign(reqArray,{
                        template_save_type: undefined,
                    })
                }

                if (isDesigner && urlProps.template_type) {
                    reqArray.template_type = parseInt(urlProps.template_type) as typeof reqArray.template_type;
                }

                this.setState({
                    saveText: '保存中...',
                    saveBtnTipShow: 1,
                    saveBtnTipText: '保存中...',
                    // saveTemplTip: '正在保存中...'
                    filesaveTemplTip: (
                        <span>
                            保存中
                            <span style={{ animation: 'webRoll 1.5s infinite ease-in-out', display: 'inline-block' }}>
                                ...
                            </span>
                        </span>
                    ),
                    saveTemplTip: (
                        <p>
                            保存中
                            <span style={{ animation: 'webRoll 1.5s infinite ease-in-out', display: 'inline-block' }}>
                                ...
                            </span>
                        </p>
                    ),
                    saveType: 'saving',
                });

                // 电商详情单个模板保存
                if (rt_is_online_detail_page && isDesigner) {
                    const onlineDetailSinle = propst.onlineDetailSinle || false;
                    if (onlineDetailSinle) {
                        const tempCanvas = cloneDeep(canvas),
                            tempWork = cloneDeep(work);

                        const { floorCutting = [] } = tempCanvas;
                        const { online_detail_single_floor_uploaded_ids } = storeAdapter.getStore<
                            typeof storeAdapter.store_names.paintOnCanvas
                        >({
                            store_name: storeAdapter.store_names.paintOnCanvas,
                        });
                        const { rt_online_detail_current_index = 0 } = pageInfo;
                        const curFloorHeight = floorCutting[rt_online_detail_current_index].height;

                        let limitTop = 0,
                            limitBottom = 0; //top : 上界限 , bottom 下界限
                        floorCutting.map((v, i) => {
                            if (i <= rt_online_detail_current_index) {
                                limitBottom += v.height;
                            }
                        });
                        limitTop = limitBottom - curFloorHeight;

                        // 过滤 和 重置work中元素位置
                        tempWork.pages.forEach((page, pageNum) => {
                            // eslint-disable-next-line @typescript-eslint/ban-types
                            const filterAssets: IAsset[] = [];
                            page.assets.forEach((asset: IAsset) => {
                                if (checkoutAssetIsInArea(limitTop, limitBottom, asset)) {
                                    const tempAsset = cloneDeep(asset);
                                    tempAsset.transform.posY -= limitTop;
                                    filterAssets.push(tempAsset);
                                }
                            });
                            tempWork.pages[pageNum].assets = filterAssets;
                        });
                        if (tempWork.pages[0].assets.length === 0) {
                            this.isSaveing = false;
                            this.setState({
                                saveBtnTipShow: 0,
                                saveTemplTip: undefined,
                                filesaveTemplTip: undefined,
                            });
                            return;
                        }
                        // 过滤 和 重置work中元素位置 end

                        tempCanvas.floorCutting = undefined;
                        tempCanvas.height = curFloorHeight;
                        const _singleFLoorId = online_detail_single_floor_uploaded_ids[
                            rt_online_detail_current_index
                        ] || {
                            id: '',
                            isUpload: false,
                        };
                        // 覆盖原模板数据
                        reqArray = {
                            ...reqArray,
                            id: _singleFLoorId.id, //单个楼层提交 如果保存具体楼层的ID  判断提交后 不能再次提交 ，
                            picId: _singleFLoorId.id,
                            doc: {
                                ...reqArray.doc,
                                canvas: tempCanvas,
                                work: tempWork,
                            },
                            height: curFloorHeight,
                            isSingleFloor: true,
                        };
                        // 覆盖原模板数据
                       
                    }
                }

                let { handleSave = false } = propst;
                if (propst.callbackProps) {
                    handleSave = true;
                }

                // 储存当前编辑的templateName
                const rt_firstSaveRecorder = `${rt_mutisize_current_selected_tid || 'main'}`;
                const th = this;
                if(this.selectTeamId){
                    reqArray.id = '0'
                }
                function customSaveTemplate(params: { id: string; isSingleFloor: boolean; doc: { work: { pages: { assets: { attribute: {resId:string} }[] }[]} }, st?: 'zdbc'}) {
                    const isAutoSave = params.st === 'zdbc';
                    params.doc.work.pages.forEach(page => { 
                        page.assets.forEach(asset => {
                            const resId = asset.attribute.resId;
                            if (!resId || typeof resId !== 'string') return;
                            if (resId.includes('_')){
                                asset.attribute.resId = resId.split('_')[1]
                            }
                        })
                    });
                    assetManager
                        .saveTemplate({ ...params, rt_firstSaveRecorder }, handleSave, propst.message_id)
                        .then(async (data) => {
                            await data.json().then((result1: any) => {
                                const urlProps = IPSConfig.getProps();
                                if (this.intervalDownloadStart) {
                                    clearInterval(this.intervalDownloadStart);
                                }
                                // const props = getProps() as unknown as { picId: number; upicId: number };
                                const props = IPSConfig.getProps();
                                let saveBtnTipText: string;
                                let saveBtnTipColor: string;
                                let saveText = '保存';

                                if (Number(result1.stat) === 1 && (result1.info.id > 0 || result1.info.paperId > 0)) {
                                    if((env.teamTemplate || isUeTeam) && (propst.template_save_type == 'versions') && +urlProps.upicId) {//团队版成功另存新版本
                                        result1.info.version_id = result1.info.id;
                                    }
                                    if (th.reloadLogin && Number(props.upicId) > 0 && props.upicId !== result1.info.id) {
                                        window.location.reload();
                                        return;
                                    }
                                    th.isSaveing = false;
                                    saveBtnTipText = '保存成功';
                                    saveBtnTipColor = this.saveBtnTipColor1;
                                    const nowDate = new Date(),
                                        hDate = ('0' + nowDate.getHours()).slice(-2),
                                        mDate = ('0' + nowDate.getMinutes()).slice(-2);
                                    // saveTime = hDate + ':' + mDate + ':' + sDate;
                                    saveTime = hDate + ':' + mDate;

                                     /*编辑器判断*/
                                    const { isDesigner } = storeAdapter.getStore({
                                        store_name: storeAdapter.store_names.paintOnCanvas,
                                    });

                                    saveTemplTip = props.paperId ?
                                    (Number(props.paperId) > 0 && !(Number(props.upicId) > 0) && !isDesigner) ?
                                        (isTip ? saveTime + '已保存' : saveTime + '存至草稿') : saveTime + '已保存' : 
                                        (Number(props.upicId) > 0 ? saveTime + '已保存' : saveTime + '存至草稿' );
                                    if(propst.template_save_type === 'versions' || handleSave || env.teamTemplate || isUeTeam){
                                        saveTemplTip = saveTime + '已保存';
                                    }
                                    filesaveTemplTip = saveTime + '已保存';
                                    saveType='saved'
                                    this.getUserTemplateVersionslList()
                                    

                                    CanvasPaintedLogic.firstSaveRecorder({
                                        templateName: rt_firstSaveRecorder,
                                        templateId: result1.info.id,
                                    });
                                    if (isTip) {
                                        /*编辑器判断*/
                                        const { isDesigner } = storeAdapter.getStore({
                                            store_name: storeAdapter.store_names.paintOnCanvas,
                                        });
                                        let windowContentStr: JSX.Element;
                                        
                                        const jump = (env.teamTemplate || isUeTeam) 
                                        ?
                                         `https://team.818ps.com/team/design?team_id=${ this.selectTeamId || getProps().team_id }`
                                          : "//818ps.com/home/<USER>";
                                        // console.log('jyjin jump -- ',  getProps().team_id)
                                        windowContentStr = (
                                            <div className="saveTipArea">
                                                <p className="ptext1">
                                                    <i className="iconfont icon-chenggong saveSuccess"></i>
                                                    成功上传到云端：
                                                    <a
                                                        className="ptext2"
                                                        onClick={() => {
                                                            this.beforeDownloadClickEvent('download', 'file');
                                                            assetManager.setPv_new(3693);
                                                        }}
                                                        onMouseEnter={this.handleMouseEnterLimitSetPv.bind(this, 6066)}
                                                        onMouseLeave={this.handleMouseLeaveLimitSetPv}
                                                    >
                                                        下载到本地
                                                    </a>{' '}
                                                    或{' '}
                                                    <a
                                                        target="_blank"
                                                        // href={(env.teamTemplate || isUeTeam) ? `https://team.818ps.com/team/design?team_id=${ this.selectTeamId || getProps().team_id }` : "//818ps.com/dash/userdsn"}
                                                        href={jump}
                                                        className="ptext2"
                                                        onClick={this.saveTipAreaClickEvent.bind(this,this.selectTeamId || getProps().team_id)}
                                                        rel="noreferrer"
                                                        onMouseEnter={this.handleMouseEnterLimitSetPv.bind(this, 6067)}
                                                        onMouseLeave={this.handleMouseLeaveLimitSetPv}
                                                    >
                                                        {
                                                            (env.teamTemplate || isUeTeam || this.selectTeamId) ?
                                                            "去团队设计"
                                                            :
                                                            "去我的设计"
                                                        }
                                                        {/* 去我的设计 */}
                                                    </a>{' '}
                                                    查看
                                                </p>
                                            </div>
                                        );

                                        if (isDesigner) {
                                            windowContentStr = (
                                                <div className="saveTipArea">
                                                    <p className="ptext1">
                                                        <i className="iconfont icon-chenggong saveSuccess"></i>
                                                        已成功保存到：
                                                        <a
                                                            target="_blank"
                                                            href="//818ps.com/dash/dsrwork"
                                                            className="ptext2"
                                                            rel="noreferrer"
                                                        >
                                                            我的设计
                                                        </a>
                                                    </p>
                                                </div>
                                            );
                                        }

                                        // 保存成功提示框
                                        const windowInfo = {
                                            windowContent: windowContentStr,
                                        };
                                        !isShare && emitter.emit('PromptBox', windowInfo);
                                        //  // 清除选中的teamid
                                        th.selectTeamId = ''
                                        if (this.PromptBoxTime) {
                                            clearTimeout(this.PromptBoxTime);
                                        }
                                        this.PromptBoxTime = setTimeout(() => { 
                                            emitter.emit('PromptBoxClose');
                                        }, 3000);

                                        //如果存了新版本 保存之后跳转至新版本页面
                                        if(result1?.info?.version_id  && propst.template_save_type === "versions" ){
                                            const reg = /&version_id=[^&#]*/
                                            let tempUrl
                                            if(reg.test(document.location.href)){
                                                tempUrl = document.location.href.replace(/&version_id=[^&#]*/, '');
                                            }else{
                                                tempUrl = document.location.href.replace(/version_id=[^&#]*/, '');
                                            }
                                            // tempUrl = tempUrl.replace(/uupicId/, 'upicId');
                                            tempUrl = tempUrl + "&version_id=" + result1.info.version_id;
                                            window.open(tempUrl)

                                            if(!urlProps.version_id && !(Number(urlProps.version_id) > 0)){
                                                const hrefUrl = document.location.href + '&version_id=0';
                                                history.pushState('', document.title, hrefUrl);
                                            }
                                            // this.changeVersion(result1?.info?.version_id)
                                        } else if(result1?.info) {
                                            if(returnSaveResult){
                                                returnSaveResult(true)
                                            }
                                        }
                                        
                                    }
                                    propst.saveSuccessCb?.()
                                          // 针对效果模板做单独的保存处理
                                    if(urlProps.user_asset_id_2){
                                        const { upicId ,version_id, user_template_team_id = 0 } = getProps();
                                        if(result1.info.id > 0 && !upicId){
                                            assetManager.saveUserTemplateKind({
                                                paperId: 0,
                                                version_id,
                                                upicId:result1.info.id,
                                                user_template_team_id,
                                                kid_1: 488
                                            })
                                        }else if(result1.info.paperId > 0){
                                            assetManager.saveUserTemplateKind({
                                                paperId:result1.info.paperId,
                                                version_id,
                                                upicId:0,
                                                user_template_team_id,
                                                kid_1: 488
                                            })
                                        }
                                    }    
                                } else if(Number(result1.stat) === -5) {
                                    if(propst.template_save_type === 'versions'){
                                        assetManager.setPv_new(5731)
                                        if (this.PromptBoxTime) {
                                            clearTimeout(this.PromptBoxTime);
                                        }
                                        this.PromptBoxTime = setTimeout(() => {
                                            emitter.emit('PromptBoxClose');
                                        }, 3000);
                                        const windowContent: JSX.Element = (
                                            <div className="saveTipArea">
                                                <p className="ptext1">
                                                    <i className="iconfont icon-tishi1 saveFailed"></i>
                                                    当前最多创建5个版本
                                                </p>
                                            </div>
                                        );
                                        emitter.emit('PromptBox', {windowContent});
                                        const nowDate = new Date(),
                                        hDate = ('0' + nowDate.getHours()).slice(-2),
                                        mDate = ('0' + nowDate.getMinutes()).slice(-2);
                                        // saveTime = hDate + ':' + mDate + ':' + sDate;
                                        saveTime = hDate + ':' + mDate;
                                        saveTemplTip = saveTime + '已保存';
                                        saveType = 'saved';
                                        filesaveTemplTip = saveTime + '已保存';
                                    }
                                } else {
                                    if(returnSaveResult){
                                        returnSaveResult(false)
                                    }
                                    // if (result1.stat == '-12' && (!(result1.uid > 0) || (user.pre_userId > 0 && user.pre_userId != result1.uid))) {
                                    if (result1.stat === '-14') {
                                        const is_login_out = true;
                                        // if (user.userId != result1.uid) {
                                        //     is_login_out = true;
                                        // }
                                        UserCheck.openTip({
                                            is_login_out: is_login_out,
                                        });
                                        isTip = false;
                                    }
                                    saveText = '保存失败，重试';
                                    saveBtnTipText = '保存失败（' + result1.stat + '）';
                                    saveBtnTipColor = this.saveBtnTipColor2;
                                    saveTemplTip = (
                                        <p style={(env.teamTemplate || isUeTeam) ? {color: '#efc885'} : { color: '#f80606' }}>
                                            保存失败，
                                            <p
                                                style={{
                                                    display: 'inline',
                                                    textDecoration: 'underline',
                                                    cursor: 'pointer',
                                                }}
                                                onClick={this.saveErrorClickEvent.bind(this)}
                                            >
                                                点击保存
                                            </p>
                                        </p>
                                    );
                                    saveType = 'saveFail'
                                    filesaveTemplTip = '保存失败';

                                    if (isTip && !isAutoSave && handleSave) {
                                        // 保存失败提示框
                                        const windowInfo = {
                                            windowContent: (
                                                <div className="saveTipArea">
                                                    <p className="ptext1">
                                                        <i className="iconfont icon-fail saveFailed"></i>保存失败，
                                                        <a
                                                            // target="_blank"
                                                            // href="//wpa.qq.com/msgrd?v=3&uin=739714379&site=qq&menu=yes"
                                                            href="javascript:void(0)"
                                                            onClick={(e) => {
                                                                window.open('https://818ps.com/apiv2/udesk'); //, '_blank', 'height=544, width=644,toolbar=no,scrollbars=no,menubar=no,status=no'
                                                                assetManager.setPv_new(2265);
                                                                e.stopPropagation();
                                                                e.nativeEvent.stopPropagation();
                                                            }}
                                                            // href="//1870046.s2.udesk.cn/im_client/?web_plugin_id=14749&nonce=prm0u9xoklr5939xlz2ccd28h2mucnde&timestamp=1576208507794&web_token=3252489&c_name=3252489&c_cn_ip=************&c_desc=9&signature=D4C4234626236AD6EFB7702107444683D35804E3"
                                                            className="ptext3"
                                                        >
                                                            联系在线客服
                                                        </a>
                                                    </p>
                                                </div>
                                            ),
                                        };
                                        emitter.emit('PromptBox', windowInfo);
                                        if (this.PromptBoxTime) {
                                            clearTimeout(this.PromptBoxTime);
                                        }
                                        this.PromptBoxTime = setTimeout(() => {
                                            emitter.emit('PromptBoxClose');
                                        }, 3000);
                                    }
                                    assetManager.setPv_new(906);
                                    const { isDesigner } = storeAdapter.getStore({
                                        store_name: storeAdapter.store_names.paintOnCanvas,
                                    });
                                    if (!isDesigner && failCount < 3) {
                                        failCount++;
                                        retrySaveTimer && clearTimeout(retrySaveTimer);
                                        assetManager.setPv_new(4539, {additional: {
                                            s0: params.id,
                                            s1: '保存失败重发保存',
                                            s2: result1.msg,
                                            // s3: result1.stat,
                                            s3: checkHash + '  ' + result1.hashDoc,
                                            s4: result1.stat,
                                        }});
                                        retrySaveTimer = setTimeout(() => {
                                            customSaveTemplate.call(this, params);
                                        }, 1000);
                                    } else {
                                        failCount = 0;
                                        retrySaveTimer && clearTimeout(retrySaveTimer);
                                        checkHash = '';
                                    }
        
                                }

                                // let {isDesigner} = canvasStore.getState().onCanvasPainted
                                const { isDesigner } = storeAdapter.getStore({
                                    store_name: storeAdapter.store_names.paintOnCanvas,
                                });
                                /*编辑器判断*/
                                let mutisize_tempKey = '';
                                for (let i = 0, len = rt_mutisize_subtemplates.length; i < len; ++i) {
                                    if (
                                        rt_mutisize_current_selected_tid ===
                                        tempIdBuilder(rt_mutisize_subtemplates[i], i)
                                    ) {
                                        mutisize_tempKey = rt_mutisize_subtemplates[i].link_tid;
                                        break;
                                    }
                                }
                                if ((!(Number(props.upicId) > 0) && !isDesigner) || (propst && propst.switchName)) {
                                    if (Number(props.picId) > 0 && result1.info.id > 0) {
                                        const tempPicId = props.picId,
                                            tempUpicId = result1.info.id;
                                        setTimeout(() => {
                                            assetManager.copyPreview(tempUpicId, tempPicId);
                                        }, 100);
                                    }
                                    let { handleSave = false } = propst;
                                    if (propst.callbackProps) {
                                        handleSave = true;
                                    }
                                    let tempUrl = document.location.href.replace(/upicId=[^&#]*/, '');
                                        tempUrl = document.location.href.replace(/&paperId=[^&#]*/, '');
                                    const {upicId} = IPSConfig.getProps();
                                    if (!handleSave) {
                                        Object.assign(props, {
                                            paperId: result1.info.paperId
                                        });
                                        tempUrl = tempUrl + '&paperId=' + result1.info.paperId;
                                    } else {
                                        if(!upicId){
                                            Object.assign(props, {
                                                upicId: result1.info.id
                                            });
                                            tempUrl = document.location.href.replace(/&paperId=[^&#]*/, '');
                                            tempUrl = tempUrl + '&upicId=' + result1.info.id;
                                        }
                                    }
                                    if (!(Number(props.picId) > 0) && !(Number(props.paperId) > 0)) {
                                        tempUrl = tempUrl.replace(/picId=[^&#]*[&| ]/, '');
                                    } else if (Number(props.picId) > 0 && (!(Number(props.paperId) > 0) || !(Number(props.upicId) > 0))) {
                                        if (Number(props.upicId) === 0) {
                                            tempUrl = tempUrl.replace(/upicId=[^&#]*[&| ]/, '');
                                        }
                                    }
                                    if(env.teamTemplate || isUeTeam){
                                        const {info} = storeAdapter.getStore({
                                            store_name: storeAdapter.store_names.InfoManage,
                                        });
                                        // 团队版需要添加‘用户团队个人模板id’
                                        if(/user_template_team_id/.test(tempUrl)){
                                            tempUrl = tempUrl.replace(/user_template_team_id=[^&#]*/, `user_template_team_id=${result1.info.id}`);
                                            tempUrl = tempUrl.replace(/upicId=[^&#]*/, `upicId=${result1.info.id}`);
                                        }else{
                                            tempUrl = tempUrl + '&user_template_team_id=' + result1.info.id;
                                        }
                                        const infos = {
                                            ...info,
                                            id: result1.info.id
                                        }
                                        InfoManageHelper.updateInfo(infos);
                                    }
                                    Object.assign(props, {
                                        upicId: result1.info.id,
                                    });
                                    // let tempUrl = document.location.href.replace(/upicId=[^&#]*/, '');
                                    // tempUrl = tempUrl + '&upicId=' + result1.info.id;
                                    // tempUrl = tempUrl.replace(/picId=[^&#]*[&| ]/, '');
                                    history.pushState('', document.title, tempUrl);
                                } else if (!(Number(props.picId) > 0) && isDesigner) {
                                    Object.assign(props, {
                                        picId: result1.info.id,
                                    });
                                    let tempUrl = document.location.href.replace(/picId=[^&#]*[&| ]/, '');
                                    tempUrl = tempUrl + '&picId=' + result1.info.id;
                                    tempUrl = tempUrl.replace(/upicId=[^&#]*[&| ]/, '');
                                    // history.pushState('', document.title, ipsUrl('?upicId=' + result1.info.id))
                                    history.pushState('', document.title, tempUrl);
                                }

                                if (
                                    isDesigner &&
                                    Number(result1.stat) === 1 &&
                                    result1.info.id > 0 &&
                                    !mutisize_tempKey &&
                                    rt_isMutiSizeTemplate
                                ) {
                                    // 多尺寸处理
                                    // canvasStore.dispatch(paintOnCanvas('MUTISIZE_SET_CURRENT_SELECTED_TID', {
                                    //     tid: result1.info.id,
                                    // }))
                                    let sonWidth = canvas.width,
                                        sonHeight = canvas.height,
                                        parentId = props.picId;

                                    params['mutisize_bindinfo'] &&
                                        // @ts-ignore
                                        params['mutisize_bindinfo'].map((v) => {
                                            if (v.is_main && v.link_tid != '') {
                                                parentId = v.link_tid;
                                            }
                                        });
                                    for (let i = 0, len = rt_mutisize_subtemplates.length; i < len; ++i) {
                                        if (
                                            rt_mutisize_current_selected_tid ===
                                            tempIdBuilder(rt_mutisize_subtemplates[i], i)
                                        ) {
                                            rt_mutisize_subtemplates[i].tid = props.picId;
                                            rt_mutisize_subtemplates[i].link_tid = result1.info.id;
                                            sonWidth = rt_mutisize_subtemplates[i].width;
                                            sonHeight = rt_mutisize_subtemplates[i].height;
                                            break;
                                        }
                                    }
                                    if (!mutisize_tempKey && parentId != result1.info.id) {
                                        //关联主模板
                                        assetManager
                                            .saveOnlineTemplateSonToParent({
                                                parentId: parentId,
                                                sonId: result1.info.id, // /api/templ 保存后返回
                                                width: sonWidth,
                                                height: sonHeight,
                                            })
                                            .then();
                                    }
                                    this.isSaveing = false;
                                    CanvasPaintedLogic.mutisizeSaveSubTemplateInfo(rt_mutisize_subtemplates);

                                    // 将模板设为已创建
                                    CanvasPaintedLogic.mutisizeSetCurrentStatusUpdate({
                                        created: true,
                                    });
                                    // 多尺寸处理end
                                }

                                /* 对电商详情单个楼层保存提交处理 */
                                if (
                                    rt_is_online_detail_page &&
                                    params.isSingleFloor &&
                                    isDesigner &&
                                    Number(result1.stat) === 1 &&
                                    result1.info.id > 0
                                ) {
                                    const { online_detail_single_floor_uploaded_ids } = storeAdapter.getStore({
                                        store_name: storeAdapter.store_names.paintOnCanvas,
                                    });
                                    const { rt_online_detail_current_index = 0 } = pageInfo;

                                    if (
                                        online_detail_single_floor_uploaded_ids[rt_online_detail_current_index].id !==
                                        result1.info.id
                                    ) {
                                        assetManager
                                            .setOnlineDetailEachFloorInfo({
                                                tid: result1.info.id,
                                                ptid: props.picId,
                                                part: rt_online_detail_current_index + 1,
                                            })
                                            .then((data) => {
                                                // data.json().then(() => {});
                                            });

                                        const floorUploadStatus = [...online_detail_single_floor_uploaded_ids];
                                        floorUploadStatus[rt_online_detail_current_index].id = result1.info.id;

                                        //  TODO  \电商详情页
                                        // canvasStore.dispatch(
                                        //     paintOnCanvas('MUTISIZE_SET_DETAIL_FLOOR_UPLOAD_STATUS', {
                                        //         eachFloorUploadStatus: floorUploadStatus,
                                        //     }),
                                        // );
                                    }
                                }
                                /* 对电商详情单个楼层保存提交处理 end  */

                                //  // 对切换账号模板处理
                                // if (propst.switchName) {
                                //     let tempUrl = document.location.href.replace(/upicId=[^&]*/, ' ')
                                //         history.pushState('', document.title, tempUrl)
                                // }

                                /*延迟显示结果 START*/
                                if (this.isSaveingTimeout2) {
                                    clearTimeout(this.isSaveingTimeout2);
                                }
                                this.isSaveingTimeout2 = setTimeout(() => {
                                    th.setState({
                                        saveText: saveText,
                                        saveBtnTipText: saveBtnTipText,
                                        saveBtnTipColor: saveBtnTipColor,
                                        saveTime: saveTime,
                                        saveTemplTip: saveTemplTip,
                                        saveType: saveType,
                                        filesaveTemplTip: filesaveTemplTip,
                                    });
                                    this.isSaveing = false;
                                }, 2000);
                                /*延迟显示结果 END*/

                                if (typeof callback === 'function') {
                                    // @ts-ignore
                                    callback(propst.callbackProps, this);

                                    const saveTime = (new Date().getTime() - propst.startSaveTime) / 1000;
                                    InfoManageHelper.addSaveTime(saveTime);
                                    // canvasStore.dispatch(infoManage('ADD_SAVE_TIME', { saveTime }));
                                }
                                if(!isDesigner){
                                    emitter.emit('convertCanvasPageToImage', {format:'jpeg',quality:1,index:0, handleSave})
                                }
                            });
                        })
                        .catch((error) => {
                            console.error(error)
                            this.isSaveing = false;
                            if (isTip) {
                                const windowInfo = {
                                    windowContent: (
                                        <div className="saveTipArea">
                                            <p className="ptext1">
                                                <i className="iconfont icon-fail saveFailed"></i>保存失败，
                                                <a
                                                    href="javascript:void(0)"
                                                    onClick={(e) => {
                                                        window.open('https://818ps.com/apiv2/udesk');
                                                        assetManager.setPv_new(2265);
                                                        e.stopPropagation();
                                                        e.nativeEvent.stopPropagation();
                                                    }}
                                                    className="ptext3"
                                                >
                                                    联系在线客服
                                                </a>
                                            </p>
                                        </div>
                                    ),
                                };
                                emitter.emit('PromptBox', windowInfo);
                                if (this.PromptBoxTime) {
                                    clearTimeout(this.PromptBoxTime);
                                }
                                this.PromptBoxTime = setTimeout(() => {
                                    emitter.emit('PromptBoxClose');
                                }, 3000);
                            }
                            this.isSaveingTimeout2 = setTimeout(() => {
                                th.setState({
                                    saveText: '保存失败',
                                    saveBtnTipText: '保存失败',
                                    saveBtnTipColor: this.saveBtnTipColor2,
                                    saveTime: saveTime,
                                    saveTemplTip: <p style={(env.teamTemplate || isUeTeam)? {color: '#efc885'} : { color: '#f80606' }}>保存失败</p>,
                                    saveType: 'saveFail',
                                    filesaveTemplTip: <span style={(env.teamTemplate || isUeTeam)? {color: '#efc885'} : { color: '#f80606' }}>保存失败</span>,
                                });
                                this.isSaveing = false;
                            }, 2000);
                            assetManager.setPv_new(3064, { additional: { s0: error } });
                            emitter.emit('DownloadPopupUS', 20);
                            if (!isDesigner && failCount < 3) {
                                failCount++;
                                retrySaveTimer && clearTimeout(retrySaveTimer);
                                assetManager.setPv_new(4539, {additional: {
                                    s0: params.id,
                                    s1: '保存失败重发保存',
                                    s2: error,
                                    s3: checkHash
                                }});
                                retrySaveTimer = setTimeout(() => {
                                    customSaveTemplate.call(this, params);
                                }, 1000);
                            } else {
                                failCount = 0;
                                retrySaveTimer && clearTimeout(retrySaveTimer);
                                checkHash = '';
                            }
        
                        });
                }

                // const sortedDoc = sortKeys(templateFormat.saveFormat(reqArray.doc), { deep: true })
                // checkHash = md5(JSON.stringify(sortedDoc));


                if (rt_isMutiSizeTemplate && reqArray.picId) {
                    assetManager.getOnlineTemplateDetail({ tid: reqArray.picId }).then((data) => {
                        data.json().then((res) => {
                            if (res) {
                                if (Number(res.stat) === 0) {
                                    customSaveTemplate.call(this, reqArray);
                                } else {
                                    reqArray['mutisize_bindinfo'] = res;
                                    customSaveTemplate.call(this, reqArray);
                                }
                            }
                        });
                    });
                } else {
                    customSaveTemplate.call(this, reqArray);
                }
                let pageAllTime = 0
                if (pageAttr.pageInfo?.length > 0) {
                    pageAllTime = pageAttr.pageInfo.reduce((t, v) => {
                        return t + v.pageTime
                    }, 0)
                }
                const pageCount = work.pages.length
                const pageAnimationIds = this.countPageAnimation(pageAttr);
                if (pageAnimationIds.length > 0) {
                    assetManager.setPv_new(4691, {
                        additional: {
                            s0: this.countPageAnimation(pageAttr),
                            s1: pageAllTime,
                            s2: pageCount
                        },
                    })
                }

                // let {isDesigner} = canvasStore.getState().onCanvasPainted
                /*保存用户操作记录START*/
                /*编辑器判断*/
                if (!isDesigner) {
                    assetManager.setPushOperationRecord();
                    // assetManager.setPushOperationRecordNew();
                } else {
                    assetManager.setPushOperationRecordDesigner();
                }
                /*保存用户操作记录END*/

                /*保存用户停留时间START*/
                /*编辑器判断*/
                /*保存用户停留时间END*/
                
            },
        );
    }
    // 模板主图
    getPirmaryAsset() {
            const { work, pageInfo } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            const curPage = pageInfo.pageNow;
    
            const primaryAsset = work.pages[curPage].assets.find((asset) => {
                return (
                ['image', 'pic', 'background'].includes(asset.meta.type) &&
                    (asset.meta.rt_tag_type == 'primary_img' ||
                        asset.meta.type == 'background')
                );
            });
            return primaryAsset;
    }
    calcEditTime(){
        const curTime = Date.now()
        let editTime = Math.round((curTime - (this.lastEditTime ?? 0)) / 1000)
        this.lastEditTime = curTime
        if (editTime > 30 || editTime < 0 || !this.lastEditTime) {
            editTime = 1;
        } 
        return editTime
    }
    /**
     * 保存团队弹窗选择团队的callback
     * @param team_id 选择的teamid
     */
    popupTeamSelectCallback(team_id:string,team_name = ''):void{
        this.selectTeamId=team_id
        this.selectTeamName = team_name
    }
    /**
     * 数组去再重取前三
     * @param e 
     * @returns 
     */
     checkWorkDeal(arr: any){
        let string ='', newArr: any = []
        for(let i=0,len=arr.length;i<len;i++){
            if(newArr.indexOf(arr[i]) === -1){
                newArr.push(arr[i]);
            }
        }
        newArr.forEach((v: string, i: number) => {
            if(i < 3){    
                if(i == newArr.length - 1){
                    string +=v
                }else{
                    string +=v + ","
                }
            }
        });

        return string
     }

    /**
     * 上传模版
     */
    uploadClickEvent(e: UIEventHandler<HTMLDivElement>): void {
        const tempProps = getProps();
        const { canvas, rt_is_online_detail_page } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (rt_is_online_detail_page) {
            const { info } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.InfoManage,
            });
            if (
                canvas.height < 7990 &&
                ([176, 177].includes(Number(tempProps['k2'])) || [176, 177].includes(Number(info['kid_2'])))
            ) {
                emitter.emit('online_detail_upload_feedbacks', { isRight: false, text: '总尺寸需大于等于7990px' });
                return;
            }
        }
        emitter.emit(
            'autoSaveTempl',
            () => {
                assetManager.getCheckWork(tempProps['picId']).then(data => {
                    data.json().then(res =>{
                        let windowInfo = {}
                        if(res.code == 0 || isAIDesign()){
                            windowInfo = {
                                windowContent: <UploadPopup />,
                                popupWidth: 640,
                                popupHeight: 800,
                                style: {
                                   "overflowY":"scroll"
                                },
                            };
                            emitter.emit('popupWindow', windowInfo) 
                            return
                        }else if(res.code == -2){
                              // 敏感词提示框
                              windowInfo = {
                                windowContent: (
                                    <div className="saveTipArea">
                                        <p className="ptext1">
                                            <i className="iconfont icon-fail saveFailed"></i>
                                            模板内含有违规信息'{this.checkWorkDeal(res.data[0])}'等，请删除或修改后重新提交～
                                        </p>
                                    </div>
                                ),
                            };
                            emitter.emit('PromptBox', windowInfo);
                            if (this.PromptBoxTime) {
                                clearTimeout(this.PromptBoxTime);
                             }
                            setTimeout(() => {
                                emitter.emit('PromptBoxClose');
                            }, 3000);
                        }
                    })
                })
            },
            '',
            { iszdbc: true },
        );
    }

    /**
     * 上传组文字
     * @param e
     */
    uploadGroupWordClickEvent(e: MouseEvent): void {
        const windowInfo = {
            windowContent: <UploadGroupWord key={new Date().getTime()} />,
            popupWidth: 'auto',
            popupHeight: 'auto',
            style: {
                borderRadius: '0',
                backgroundColor: 'rgba(255, 255, 255, 0)',
                top: 0,
                left: 0,
                bottom: 0,
                right: 0,
                padding: 0,
            },
            popupTitleBarStyle: {
                width: 0,
                height: 0,
                display: 'none',
            },
        };

        emitter.emit('popupWindow', windowInfo);
    }


    /**
     * 上传文档图文组合
     * @param e
     */
    uploadDocClickEvent(e: MouseEvent): void {
        const windowInfo = {
            windowContent: <UploadDocPanel key={new Date().getTime()} />,
            popupWidth: 'auto',
            popupHeight: 'auto',
            style: {
                borderRadius: '0',
                backgroundColor: 'rgba(255, 255, 255, 0)',
                top: 0,
                left: 0,
                bottom: 0,
                right: 0,
                padding: 0,
            },
            popupTitleBarStyle: {
                width: 0,
                height: 0,
                display: 'none',
            },
        };

        emitter.emit('popupWindow', windowInfo);
    }

    /**
     * 预览
     */
    yulanClickEvent(origin: string, e: MouseEvent): void {
        const { user, toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const { info } = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
            store_name: storeAdapter.store_names.InfoManage,
          });
        if (!(user.userId > 0)) {
            emitter.emit('LoginPanelShow', 'previewAndShare');
            assetManager.setPv_new(174, {
                additional: {
                    s0: 'previewAndShare',
                },
            });
            return;
        }

        if (!(!toolPanel.asset && toolPanel.asset === '')) {
            SelectAsset.blurAsset();
        }
        // ppt进入演示 图片进入预览
        const element = document.documentElement; 
        if (element?.requestFullscreen && Number(info.template_type) === 3) {
            element?.requestFullscreen();
            const windowInfo = {
                windowContent: (
                    <PreviewCanvas
                        key={PreviewAndShare + '_' + new Date().getTime()}
                        origin={origin}
                        isLocalCanvas={true}
                        governmentTempl={this.state.governmentTempl}
                    />
                ),
                popupWidth: '100%',
                popupHeight: '100%',
                style: {
                    borderRadius: '0',
                    backgroundColor: 'rgba(255, 255, 255, 0)',
                    top: 0,
                    left: 0,
                    bottom: 0,
                    right: 0,
                    padding: 0,
                },
                popupTitleBarStyle: {
                    width: 0,
                    height: 0,
                    display: 'none',
                },
            };
            emitter.emit('popupWindow', windowInfo);
        } else {
            const windowInfo = {
                windowContent: <PreviewAndShare key={PreviewAndShare + '_' + new Date().getTime()} origin={origin}
                                    isLocalCanvas={true}
                                />,
                popupWidth: 'auto',
                popupHeight: 'auto',
                style: {
                    borderRadius: '0',
                    backgroundColor: 'rgba(255, 255, 255, 0)',
                    top: 0,
                    left: 0,
                    bottom: 0,
                    right: 0,
                    padding: 0,
                },
                popupTitleBarStyle: {
                    width: 0,
                    height: 0,
                    display: 'none',
                }
    
            };
            emitter.emit('popupWindow', windowInfo);
        }

        if (origin === 'share') {
            assetManager.setPv_new(197);
        } else {
            assetManager.setPv_new(97);
        }

        switch (origin) {
            case 'preview':
                assetManager.setPv_new(302);
                break;
            case 'share':
                assetManager.setPv_new(305);
                break;
        }

        // emitter.emit('autoSaveTempl',(res)=>{
        //     let windowInfo = {
        //         windowContent: <PreviewAndShare key={PreviewAndShare + '_' + new Date().getTime()} origin={origin} />,
        //         popupWidth: 'auto',
        //         popupHeight: 'auto',
        //         style: {
        //             borderRadius: '0',
        //             backgroundColor: 'rgba(255, 255, 255, 0)',
        //             top: 0,
        //             left: 0,
        //             bottom: 0,
        //             right: 0,
        //             padding: 0,
        //         },
        //         popupTitleBarStyle: {
        //             width: 0,
        //             height: 0,
        //             display: 'none',
        //         }
        //         // popupWidth: 1140,
        //         // popupHeight: 660,
        //         // style: {
        //         //     padding: 0,
        //         //     borderRadius: '4px',
        //         //     backgroundColor: 'rgba(255, 255, 255, 1)'
        //         // }
        //     };

        //     emitter.emit('popupWindow', windowInfo);

        //     if( origin == 'share' ){
        //         assetManager.setPv_new(197);
        //     }else{
        //         assetManager.setPv_new(97);
        //     }

        //     switch (origin){
        //         case 'preview':
        //             assetManager.setPv_new(302);
        //             break;
        //         case 'share':
        //             assetManager.setPv_new(305);
        //             break;
        //     }

        //     if( e ){
        //         e.stopPropagation();
        //         // e.nativeEvent.stopPropagation();
        //     }
        //     return ;
        // },'',{iszdbc:true});

        // let {isDesigner} = canvasStore.getState().onCanvasPainted;
        // if( !isDesigner ){

        // }

        // let props = getProps(),
        //     jumpUrl = '';

        /*编辑器判断*/

        // if( isDesigner ){
        //     jumpUrl = '//' + downloadHostName + '/render/showpreview?tid=' + props.picId;
        //     window.open(jumpUrl);
        // }else {
        //     if (props.upicId > 0) {
        //         jumpUrl = '//' + downloadHostName + '/render/showupreview?tid=' + props.upicId;
        //         window.open(jumpUrl);
        //     } else if (props.picId > 0) {
        //         jumpUrl = '//' + downloadHostName + '/render/showpreview?tid=' + props.picId + '&f=1';
        //         window.open(jumpUrl);
        //     }
        // }
    }

    
    clearIntervalEmitter(): void {
        // @ts-ignore
        this.InfoBarclearIntervalEmitter = emitter.addListener('InfoBarclearInterval', () => {
            const newState = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            clearInterval(newState.tempDownloadInterval);
        });
    }

    downloadFileEmitter(): void {
        this.downloadFileEmitteEmitter = emitter.addListener(
            'downloadFileEmitter',
            (props?: {
                appId: string;
                isPhoneBindSuccess: boolean;
                isPhoneBindClose: boolean;
                origin: string;
                isWeChatOpenPlatform: boolean;
                downloadPicProps: {
                    online_detail_download_type: number;
                    origin: string;
                    dpi: number;
                    isPrint: boolean;
                    ticket: string;
                    rendstr: string;
                    force: number;
                };
            }) => {
                this.downloadFile(props);
            },
        );
    }

    // 检测敏感词
    judgText(is_ratify:boolean) {
        const props = getProps();
        const utid = props['upicId'];
        assetManager.getUsersTemplateInfo({ utid }).then((data) => {
            data.json().then((res) => {
                const { stat = '', msg = '' } = res;
                if (Number(stat) === 1 && msg != null && JSON.stringify(msg) !== '{}') {
                    this.checkProhibiteWord(is_ratify,msg.title, msg.desc);
                } else {
                    this.checkProhibiteWord(is_ratify);
                }
            });
        });
    }

    checkProhibiteWord(is_ratify:boolean,title?: string, desc?: string): void {
        const { work } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const text = [];
        const excludeSpecial = function (str: string) {
            const pattern = /[`~!@#$^&*()=|{}':;',\\\[\]\.<>\/?~！@#￥……&*（）——|{}【】'；：""'。，、？\s]/g;
            const formatStr = str.replace(pattern, '');
            return formatStr;
        };

        if (title) {
            const titleFormat = excludeSpecial(title);
            text.push(titleFormat);
        }

        if (desc) {
            const descFormat = excludeSpecial(desc);
            text.push(descFormat);
        }
        try {
            if (work && work.pages.length > 0) {
                const pages = work.pages;

                if (work.meta && work.meta.title) {
                    const s1 = excludeSpecial(work.meta.title);
                    text.push(s1);
                }

                for (let i = 0; i < pages.length; i++) {
                    if (pages[i].assets) {
                        for (let t = 0; t < pages[i].assets.length; t++) {
                            if (
                                pages[i].assets[t].meta &&
                                pages[i].assets[t].meta.type === 'text' &&
                                pages[i].assets[t].attribute.text.length > 0
                            ) {
                                const textList = pages[i].assets[t].attribute.text;
                                if (pages[i].assets[t].meta.v === 3 ) {
                                    for(let s = 0; s < textList.length; s++){
                                        const s3 = excludeSpecial((textList[s] as IRichText).text);
                                        text.push(s3);
                                    }
                                } else {
                                    for (let s = 0; s < pages[i].assets[t].attribute.text.length; s++) {
                                        const s3 = excludeSpecial(pages[i].assets[t].attribute.text[s] as string);
                                        text.push(s3);
                                    }
                                }

                            }
                            continue;
                        }
                    }
                    continue;
                }
            }

            if (text.length === 0) {
                this.shareLinkPopup(is_ratify);
                return;
            }
            assetManager.judgText(text).then((data) => {
                data.json().then((resultData) => {
                    if (Number(resultData.stat) === 1) {
                        this.shareLinkPopup(is_ratify);
                    } else {
                        if (resultData.msg === '存在违禁词') {
                            this.prohibiteWordPopup(resultData.data);
                        } else {
                            this.shareLinkPopup(is_ratify);
                        }
                    }
                });
            });
        } catch (error) {
            console.log(error);
        }
    }

    /**
     *** 弹出违禁词弹框
     */
    prohibiteWordPopup(prohibiteWords:Array<any>): void {
        let prohibite_string = ""
        prohibiteWords[0].forEach(function (element:string) {
            prohibite_string = prohibite_string + '"' + element + '"、';
        })
        prohibite_string = prohibite_string.substring(0, prohibite_string.length - 1)

        const windowInfo = {
            windowContent: (
                <div className="violateRegulations">
                    <p className="remindText">
                         <i className="iconfont icon-zhuyiuyi1 remindIcon"></i>分享内容涉嫌违规词{prohibite_string}，请删除后重新分享～
                    </p>
                </div>
            ),
        };
        emitter.emit('PromptBox', windowInfo);
        if (this.PromptBoxTime) {
            clearTimeout(this.PromptBoxTime);
        }
        this.PromptBoxTime = window.setTimeout(() => {
            emitter.emit('PromptBoxClose');
        }, 3000);
    }
    /**
     *** 弹出分享链接弹框
     */
    shareLinkPopup(is_ratify?:boolean): void | boolean {
        const { info } = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
            store_name: storeAdapter.store_names.InfoManage,
        });
        const { user, rt_first_save_recorder, work} = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const urlProps = getProps();
        let temple_id = ''
        if (rt_first_save_recorder && rt_first_save_recorder.main) {
            temple_id = rt_first_save_recorder.main
        }else if (urlProps && urlProps.assetId) {
            temple_id = urlProps.assetId
        } else if (info) {
            temple_id = info.id
        }

        // 判断是否为空模板
        const flag = work.pages.every((item) => item.assets.length === 0);
        if (flag) {  
            const sharePup = {
                windowContent: (
                    <DownloadPopup
                        key={new Date().getTime()}
                        className="DownloadPopup"
                        showFlag= {572}
                    />
                ),
                popupWidth: 348,
                popupHeight: 244,
                background: '#000000',
                style: {
                    borderRadius: '4px',
                    backgroundColor: 'rgba(255, 255, 255, 1)',
                    boxShadow: '0px 0px 8px 0px rgba(0, 0, 0, 0.12), 0px 0px 2px 0px rgba(0, 0, 0, 0.12)',
                    position: 'absolute',
                    top: '30%',
                    padding: 0,
                    left: '40%',
                },
                popupTitleBarStyle: {
                    width: 0,
                    height: 0,
                    display: 'none',
                },
                popupBodyStyle: {
                    padding: 0,
                },
            }
            emitter.emit('popupWindow', sharePup);
            emitter.emit('DownloadPopupUS', 572);
            return false;
        }
        if(urlProps.upicId){
            emitter.emit('autoSaveTempl', '', 0, {handleSave: false}, true);
            this.getEditShare(is_ratify)
           
        } else {
            emitter.emit('autoSaveTempl', '', 1, {handleSave: true}, true,(flag:boolean)=>{
                if(flag){
                    setTimeout(() => {
                        this.getEditShare(is_ratify)
                    }, 200);
                }
            });
        }

        // assetManager.setPagePv_new(4286);
            if (rt_first_save_recorder.main || info.last_templ_id || urlProps.upicId) {
                //保存过
                assetManager.setPv_new(4286, {
                    additional: {
                        s0: "is_changed",
                    }
                })
            } else {
                //没保存过
                assetManager.setPv_new(4286, {
                    additional: {
                        s0: "new_templete",
                    }
                })
            }
    }

    getEditShare(is_ratify?:boolean){
        const  {info}  = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
            store_name: storeAdapter.store_names.InfoManage,
        });
        const {rt_first_save_recorder} = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const urlProps = getProps();
        assetManager.getEditShare(
            urlProps.upicId, 
            (rt_first_save_recorder.main || urlProps.upicId )? 'template_id' : ((urlProps && urlProps.paperId) ? 'paper_id' : (urlProps && urlProps.assetId ? 'asset_id' : 'template_id_n')) , 
            is_ratify
        ).then((data) => {
            data.json().then(url => {
                if(url.stat === 1){
                    // 分享、审阅的进度条
                    // const sharePup = {
                    //     windowContent: (
                    //         <DownloadPopup
                    //             isDownloadZero={true}
                    //             key={new Date().getTime()}
                    //             className="DownloadPopup"
                    //             showFlag= {571}
                    //             isRatify={is_ratify}
                    //         />
                    //     ),
                    //     popupWidth: 600,
                    //     popupHeight: 250,
                    //     background: '#000000',
                    //     style: {
                    //         borderRadius: '4px',
                    //         backgroundColor: 'rgba(255, 255, 255, 1)',
                    //         'boxShadow': '0px 0px 8px 0px rgba(0, 0, 0, 0.12), 0px 0px 2px 0px rgba(0, 0, 0, 0.12)',
                    //         position: 'absolute',
                    //         top: '30%',
                    //         padding: 0,
                    //         left: '30%',
                    //     },
                    //     popupTitleBarStyle: {
                    //         width: 0,
                    //         height: 0,
                    //         display: 'none',
                    //     },
                    //     popupBodyStyle: {
                    //         padding: 0,
                    //     },
                    // }
                    // emitter.emit('popupWindow', sharePup);

                    if(is_ratify){
                        assetManager.setPv_new(5169)
                    }
                    emitter.emit('DownloadPopupUS', 571);
                    // this.editShareLog(url.data.share_id,is_ratify)
                    this.editShareLog(url,is_ratify)
                    clearTimeout(this.shareTimer)

                } else if (url.stat === -5) {
                    assetManager.setPv_new(5731);
                    emitter.emit('popupClose');
                    emitter.emit("cancelShare");
                    if (this.PromptBoxTimes) {
                        clearTimeout(this.PromptBoxTimes);
                    }
                    this.PromptBoxTimes = setTimeout(() => {
                        emitter.emit('PromptBoxClose');
                    }, 3000);
                    const windowContent: JSX.Element = (
                        <div className="saveTipArea">
                            <p className="ptext1">
                                <i className="iconfont icon-tishi1 saveFailed" style={{'marginTop': '10px'}}></i>
                                当前审批已达上限，无法重新发起审批
                                <div className="ptextNext">(提示：审批会自动创建版本) </div>
                            </p>
                        </div>
                    );
                    emitter.emit('PromptBox', {windowContent,popupHeight:90});
                }else if(url.stat === -1){
                    this.shareTimer = setTimeout(() => {
                        this.getEditShare(is_ratify)
                    }, 200);
                }
            })
        })
    }


    // editShareLog(url:any,share_id:string,is_ratify?:boolean){
    editShareLog(url:any,is_ratify?:boolean){

        const { info } = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
            store_name: storeAdapter.store_names.InfoManage,
        });
        const { user, rt_first_save_recorder, work} = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        // const urlProps = getProps();
        // let temple_id = ''
        // if (rt_first_save_recorder && rt_first_save_recorder.main) {
        //     temple_id = rt_first_save_recorder.main
        // }else if (urlProps && urlProps.assetId) {
        //     temple_id = urlProps.assetId
        // } else if (info) {
        //     temple_id = info.id
        // }
        // let sharelogNum = 0
        // this.shareTimers = setInterval(() => {
        //     if(urlProps.upicId){
        //         if(this.editShareLogLock){
        //             return
        //         }
        //         this.editShareLogLock = true
        //         assetManager.editShareLog(urlProps.upicId, (rt_first_save_recorder.main || urlProps.upicId )? 'template_id' : ((urlProps && urlProps.paperId) ? 'paper_id' : (urlProps && urlProps.assetId ? 'asset_id' : 'template_id_n')) ,share_id, is_ratify).then((data) => {
        //                 data.json().then((url) => {
                            // if(sharelogNum >= 7){
                            //     sharelogNum += 0.1
                            // }else{
                            //      sharelogNum++
                            // }
                            
                            // if(url.stat == 1){
                            //     emitter.emit('updateShareProgressLineUp',  sharelogNum*10, 1000)
                            // }
                            // if(url.stat == 1 && !url.id ){
                            //     this.editShareLogLock = false
                            // }
                            // if(url.stat == 1 && url.id){

                                // clearInterval(this.shareTimers);
                                // emitter.emit('updateShareProgressLineUp', 100, 500)
                                // setTimeout(() => {
                                    // this.editShareLogLock = false
                            if(url.stat == 1){
                                    if(is_ratify){

                                        //@ts-ignore
                                        info.approve_status = "0"
                                        //@ts-ignore
                                        info.approval_url = url.url
                                        //@ts-ignore
                                        InfoManageHelper.updateInfo(info)
                                        const reg = /&version_id=[^&#]*/
                                        let tempUrl
                                        if(reg.test(document.location.href)){
                                            tempUrl = document.location.href.replace(/&version_id=[^&#]*/, '');
                                        }else{
                                            tempUrl = document.location.href.replace(/version_id=[^&#]*/, '');
                                        }
                                        // let tempUrl = document.location.href.replace(/&version_id=[^&#]*/, '');
                                        tempUrl = tempUrl + "&version_id=" + url.version_id
                                        history.pushState('', document.title, tempUrl);

                                        this.forceUpdate()
                                        const windowInfo = {
                                            windowContent: (
                                                <RatifyPopUp
                                                    isDownloadZero={true}
                                                    key={new Date().getTime()}
                                                    RatifyUrl={url.url}
                                                    QRcode={url.data.xcx_qrcode}
                                                    className="RatifyPopUp"
                                                    showFlag={58}
                                                    avatar={user.avatar}
                                                    userName = {user.username}
                                                />
                                            ),
                                            popupWidth: 368,
                                            popupHeight: 422,
                                            background: 'none',
                                            style: {
                                                borderRadius: '4px',
                                                boxShadow: '0px 0px 8px 0px rgba(0, 0, 0, 0.12), 0px 0px 2px 0px rgba(0, 0, 0, 0.12)',
                                                position: 'absolute',
                                                top: 60,
                                                padding: 0,
                                                left: '62%',
                                                // transform: 'translateX(-50%)',
                                            },
    
                                            popupTitleBarStyle: {
                                                width: 0,
                                                height: 0,
                                                display: 'none',
                                            },
                                            popupBodyStyle: {
                                                padding: 0,
                                            },
                                            // popupTitleBarStyle：{
                                            //     display:none
                                            // }
                                        };
                                        emitter.emit('popupWindow', windowInfo);
                                        emitter.emit('hideSharePopup')
                                    }else{
                                        const windowInfo = {
                                            windowContent: (
                                                <DownloadPopup
                                                    isDownloadZero={true}
                                                    key={new Date().getTime()}
                                                    shareUrl={url.url}
                                                    prefix = {url.prefix}
                                                    className="DownloadPopup"
                                                    showFlag={57}
                                                />
                                            ),
                                            popupWidth: 423,
                                            // popupHeight: 170,
                                            background: 'none',
                                            style: {
                                                borderRadius: '12px',
                                                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                                boxShadow: '0px 0px 8px 0px rgba(0, 0, 0, 0.12), 0px 0px 2px 0px rgba(0, 0, 0, 0.12)',
                                                position: 'absolute',
                                                top: 57,
                                                padding: 0,
                                                left: 'calc(100% - 430px)',
                                                // transform: 'translateX(-50%)',
                                            },
    
                                            popupTitleBarStyle: {
                                                width: 0,
                                                height: 0,
                                                display: 'none',
                                            },
                                            popupBodyStyle: {
                                                padding: 0,
                                            },
                                            // popupTitleBarStyle：{
                                            //     display:none
                                            // }
                                        };
                                        emitter.emit('popupWindow', windowInfo);
                                        emitter.emit('DownloadPopupUS', 57);  
                                        emitter.emit('hideSharePopup')
                                    }
                                    
                                // } ,500)

                            // }else if(url.stat == -1){
                            // this.editShareLogLock = false
                            }else {

                                const sharePup = {
                                    windowContent: (
                                        <DownloadPopup
                                            key={new Date().getTime()}
                                            className="DownloadPopup"
                                            showFlag= {572}
                                            shareType = 'error'
                                        />
                                    ),
                                    popupWidth: 348,
                                    popupHeight: 244,
                                    background: '#000000',
                                    style: {
                                        borderRadius: '4px',
                                        backgroundColor: 'rgba(255, 255, 255, 1)',
                                        'boxShadow': '0px 0px 8px 0px rgba(0, 0, 0, 0.12), 0px 0px 2px 0px rgba(0, 0, 0, 0.12)',
                                        position: 'absolute',
                                        top: '30%',
                                        padding: 0,
                                        left: '40%',
                                    },
                                    popupTitleBarStyle: {
                                        width: 0,
                                        height: 0,
                                        display: 'none',
                                    },
                                    popupBodyStyle: {
                                        padding: 0,
                                    },
                                }
                                clearInterval(this.shareTimers);
                                emitter.emit('popupWindow', sharePup);
                                emitter.emit('DownloadPopupUS', 572);
                                emitter.emit('updateShareProgressLineUp', 100, 500)
                            }
        //                 })
        //         }).catch(()=>{
        //             //@ts-ignore
        //             this.editShareLogLock = false
        //         })
        //     }else{
        //         clearInterval(this.shareTimers);
        //     }
        // },1000)
    }

    /**
     **** 分享链接弹窗点击
     */

    clickShareButton(is_ratify?:boolean): void {
        const { info } = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
            store_name: storeAdapter.store_names.InfoManage,
        });
        const { user, rt_first_save_recorder } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (!(Number(user.userId) > 0)) {
            emitter.emit('LoginPanelShow', 'saveTempl');
            return;
        }
        DocHashLogic.addPageHash();
        if (!user.bind_phone) {
            emitter.emit('InfoBarPhoneBindPopup');
            const urlProps = getProps();
            if (rt_first_save_recorder.main || info.last_templ_id || urlProps.upicId) {
                //保存过
                if(!is_ratify){
                    assetManager.setPv_new(4286, {
                        additional: {
                            s0: "is_changed",
                        }
                    })
                }
            } else {
                //没保存过
                if(!is_ratify){
                    assetManager.setPv_new(4286, {
                        additional: {
                            s0: "new_templete",
                        }
                    })
                }
            }
        } else {
            this.judgText(is_ratify);
        }
        if(is_ratify){
            assetManager.setPv_new(5136)
        }
    }

    onShareMouseEnterClickEvent(pv_num:number): void{
        assetManager.setPv_new(pv_num);
    }

    downloadFile = async (
        props?: {
            appId: string;
            isPhoneBindSuccess: boolean;
            isPhoneBindClose: boolean;
            origin: string;
            isWeChatOpenPlatform: boolean;
            downloadPicProps: {
                fileType?: string,
                online_detail_download_type: number;
                origin: string;
                dpi: number;
                isPrint: boolean;
                ticket: string;
                rendstr: string;
                force: number;
                isVideo?: boolean;
                isGif?: boolean;
                pickPages?: number[];
            };
        },
        _this?: this,
    ) => {
        if (!_this) {
            _this = this;
        }
        const urlProps = getProps() as unknown as { upicId: number, picId: string, ai_template_type: string, subOrigin: string };
        if (Number(urlProps['DownloadPopupUS']) === 6) {
            emitter.emit('DownloadPopupUS', 6);
            return;
        }
        if (Number(urlProps['DownloadPopupUS']) === 3) {
            emitter.emit('PopupUpdateWindowStyle', {
                width: 400,
                height: 320,
            });
            emitter.emit('DownloadPopupUS', 3);
            return;
        }
        const newState = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        // canvasStore.dispatch(paintOnCanvas('UPDATE_ISDOWNLOADFLAG', {isDownloadFlag: 1}))
        if (Number(newState.isDownloadFlag) !== 1) {
            return;
        }
        const downloadFileProps = props;
        // 保存微信公共号添加默认数据
        if ('appId' in downloadFileProps) {
            Object.assign(downloadFileProps, {
                downloadPicProps: { force: 0, fileType: 'png', dpi: '150' },
            });
        }
        // 去掉绑定手机验证

        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        downloadFileProps.downloadPicProps ? (downloadFileProps.downloadPicProps.force = 1) : '';

        // 判断是否有水印
        // downloadFileProps.downloadPicProps.mark = !document.querySelector('.assetWaterMark')?0:1;

        /* 电商详情 */
        if (newState.rt_is_online_detail_page) {
            const { online_detail_download_type } = _this.state;
            props.downloadPicProps['online_detail_download_type'] = online_detail_download_type;
        }
        /* 电商详情 */
        const startDownPicTime = new Date().getTime();
        let videoTotalTime = 0;
        // @ts-ignore
        let downLoadFun = (p: typeof props) => assetManager.downloadPic(p.downloadPicProps)
        if (_this.state.isVideo || props.downloadPicProps.isVideo || props.downloadPicProps.isGif) {
            // @ts-ignore
            downLoadFun = (p: typeof props) => assetManager.downloadVideo(p.downloadPicProps)
            if (Array.isArray(newState.pageAttr.pageInfo)) {
                for(let i =0, len = newState.work.pages.length; i < len; i++) {
                    if (newState.pageAttr?.pageInfo?.[i]?.pageTime) {
                        videoTotalTime += newState.pageAttr?.pageInfo?.[i]?.pageTime;
                    } else {
                        videoTotalTime += 5000;
                    }
                }
            }
        }
        const {emphaticMarkIds, designerEmpharticMarkIds, effectEmphaticMarkIds,effectDesignerEmpharticMarkIds} = this.getEmphaticMarkIds();
        const aiToolUsedType = this.getAiToolUsedType()
        const downloadSuccessAdditional = {
            s0: this.hasPageAnimation(newState.pageAttr) ? '1' : '0', // 是否有页面动画
            s1: this.state.isVideo || props.downloadPicProps.isVideo ? '1' : '0', // 是否是 mp4 格式
            s2: this.countPageAnimation(newState.pageAttr), // 页面动画的 id 组成
            s3: this.hasAudio(newState.pageAttr) ? '1' : '0', // 是否有音频
            s4: this.hasVideoE() ? '1' : '0', // 是否有 videoe
            i0: this.hasEffect() ? 1 : 0, // 是否有元素特效
            i1: this.hasAssetAnimation() ? 1 : 0, // 是否有 元素动效
            i2: props.downloadPicProps.isGif ? 1 : 0, // 是否是 gif 格式
            i3: this.getStyles().join(',') // 是否用了风格
        }

        // 判断是否可以本地生成
        const isCanvasDownload = false;
        let isCanvasDownFailed = false;
        // 是否使用老下载
        const isOldDownload = true;
        const resolution = newState.canvas.width * newState.canvas.height;
        const maxResolution = 2000 * 2000;
        const isClose = false;
        const isLocalDownload = true;

        // 本地化生成 START
        // 下载到手机走线上下载
        // if (props.origin !== 'phone' && resolution <= maxResolution && false) {
            
        //     try {
        //         // 本地下载初始化
        //         const download_start_time = new Date().valueOf();
        //         loaclGenerate.init({
        //             canvas: newState.canvas,
        //             pageAttr: newState.pageAttr,
        //             work: newState.work
        //         }, props.downloadPicProps);
        //         // ue，ueteam可以本地化下载
        //         const isDownloadEditor = ['ue', 'ueteam'].indexOf(env.editor) > -1? true: false;
                
        //         const userId = String(newState.user.userId);
        //         const lastNum = userId.charAt(userId.length - 1);
        //         // 用户id 位数为1 本地化下载
        //         let isUserDown = ['1', '2', '3'].indexOf(lastNum) > -1 ? true: false; 
        //         const whiteIds = [
        //             '16',
        //             '9667287',
        //             '2516801',
        //             '12',
        //             '20310934'
        //         ];
        //         isUserDown = whiteIds.indexOf(userId) > -1 ? true: isUserDown; 

        //         // 判断当前模板中是否有中文中括号，并且是竖版渲染 START
        //         let isBracketsZH = false;
        //         try {
        //             for(let j=0; j < newState.work.pages.length; j++) {
        //                 let tempAsset;
        //                 for(let i=0; i < newState.work.pages[j].assets.length; i++) {
        //                     tempAsset = newState.work.pages[j].assets[i];
        //                     // 存在表走线上下载
        //                     if(tempAsset.meta.type === 'table') {
        //                         isBracketsZH = true;
        //                     }

        //                     // 文本中存在中文中括号走线上下载
        //                     if(tempAsset.meta.type === 'text' && tempAsset.attribute.writingMode === 'vertical-rl') {
        //                         if(tempAsset.meta.v === undefined) {
        //                             tempAsset.attribute.text.map((item: string) => {
        //                                 if(item.indexOf('【') != -1 || item.indexOf('】') != -1) {
        //                                     isBracketsZH = true;
        //                                 }
        //                             });
        //                         } else if(tempAsset.meta.v == '3') {
        //                             tempAsset.attribute.text.map((item: IRichText) => {
        //                                 if(item.text.indexOf('【') != -1 || item.text.indexOf('】') != -1) {
        //                                     isBracketsZH = true;
        //                                 }
        //                             });
        //                         }
        //                     }

        //                     if(isBracketsZH) {
        //                         break;
        //                     }
        //                 }

        //                 if(isBracketsZH) {
        //                     break;
        //                 }
        //             }

        //             if(isBracketsZH) {
        //                 isLocalDownload = false;
        //             }
        //         } catch (error) {
        //             console.error(error);            
        //         }
        //         // 判断当前模板中是否有中文中括号，并且是竖版渲染 END

        //         if (isLocalDownload && loaclGenerate.canDown && isDownloadEditor && isUserDown) {
        //             isCanvasDownload = true;
        //             // assetManager.setPv_new(6978); //开始生成埋点

        //             // 绘制canvas图像
        //             const startFlag = await loaclGenerate.start();
        //             // return ;

        //             // 手动关闭窗口，不走老下载
        //             if (startFlag === 3) {
        //                 isOldDownload = false;
        //                 isClose = true;
        //                 // 本地化生成过程中关闭生成埋点
        //                 assetManager.setPv_new(7034);
        //             }

        //             // 绘制 canvas 图像成功后
        //             if (startFlag === 1) {
        //                 // 本地化生成canvas合成图片完成
        //                 assetManager.setPv_new(7035);
        //                 // 下载图像
        //                 const saveImgFlag = await loaclGenerate.saveImg();
        //                 if (saveImgFlag === 1) {
        //                     isOldDownload = false;
        //                 } else if (saveImgFlag === 3) {
        //                     // 授权失败结束下载
        //                     // return ;
        //                 }

        //                 // isCanvasDownload = await loaclGenerate.saveImg() as boolean;
        //                 // if(isCanvasDownload) {
        //                 if(saveImgFlag === 1) {
        //                     // 下载成功需要触发的埋点
        //                     const { info } = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
        //                         store_name: storeAdapter.store_names.InfoManage,
        //                     });
        //                     const {startDownloadTime } = storeAdapter.getStore({
        //                         store_name: storeAdapter.store_names.paintOnCanvas,
        //                     });
        //                     const download_time =
        //                     (new Date().valueOf() - download_start_time) / 1000;
        //                     const pvClassIds = info?.class_id ? info.class_id.filter((i) => i).map((s) => `a-${s}`).join(',') : '';
        //                     assetManager.setPagePv_new(
        //                         1856,
        //                         'user',
        //                         urlProps.upicId > 0 ? urlProps.upicId : 0,
        //                         Math.round(download_time * 1000),
        //                         '',
        //                         {
        //                             s0: pvClassIds,
        //                             i2: info?.is_company_temp?.toString()
        //                         },
        //                     );
        //                     assetManager.setPv_new(275, {
        //                         additional: {
        //                             s0: props.downloadPicProps.isPrint,
        //                             s1: props.downloadPicProps.dpi,
        //                             s2: props.downloadPicProps.origin,
        //                         },
        //                     });

        //                     assetManager.setPv_new(4697, {
        //                         additional: downloadSuccessAdditional,
        //                     })
        //                     // 使用了风格下载
        //                     if(downloadSuccessAdditional.i3){
        //                         assetManager.setPv_new(7739,{
        //                             additional: downloadSuccessAdditional,
        //                         })
        //                     }
        //                     TemplateLogic.checkImageAssetTag(82);
        //                     assetManager.setDownloadPv_new(
        //                         93,
        //                         (new Date().getTime() - startDownloadTime) / 1000,
        //                         // { i4: _dl_id },
        //                     );
        //                     assetManager.setDownloadPv_new(
        //                         95,
        //                         download_time / 1000,
        //                         {
        //                             // i4: _dl_id,
        //                             s0: info.downloadType,
        //                             s1: info.downPicTime,
        //                             s2: info.saveTime,
        //                             s3: _this.state.isVideo? 'video' : 'image'
        //                         },
        //                     );
        //                     if (downloadFileProps.isPhoneBindClose) {
        //                         assetManager.setPv_new(130);
        //                     } else if (downloadFileProps.isPhoneBindSuccess) {
        //                         assetManager.setPv_new(131);
        //                     } else {
        //                         assetManager.setPv_new(132);
        //                     }
        //                     //downLoadFun和downloadFlagCheck接口调用，是为了修复用户本地化下载没有下载记录，后期优化时需删除。
        //                     //downloadFlagCheck的接口参数‘localization’为1表示该接口时本地化下载时调用，默认没有该参数，后续优化可以删除。
        //                     const downLoadFunRes = await (await downLoadFun(props)).json();
        //                     if (Number(downLoadFunRes.stat) === 1) {
        //                         assetManager.downloadFlagCheck({ localization: 1, jobId: downLoadFunRes.job, isVideo: _this.state.isVideo || props.downloadPicProps.isVideo || props.downloadPicProps.isGif })
        //                     }
        //                 }
        //             }
        //         }
        //     } catch (error) {
        //         assetManager.setPv_new(7033, {additional: {
        //             s0 : error,
        //         }});
        //     }
        // }

        // if (!isCanvasDownload && !isClose) {
        // 是否要走老的下载
        if (isOldDownload) {
            if (isCanvasDownload) {
                // 使用本地化生成后没有成功，使用线上下载 埋点
                assetManager.setPv_new(7056);
                isCanvasDownFailed = true;
            }

            const startDownloadApiTime = new Date().getTime();
            const pageNum = newState.work.pages.filter((item) => item.assets.length != 0).length;
            downLoadFun(props)
                .then(function (data) {
                    // {
                    //     /*assetManager.downloadPPT(props.downloadPicProps).then(function(data){*/
                    // }
                    const download_start_time = new Date().valueOf();
                    data.json().then(function (resultData) {
                        // 统计接口耗时
                        assetManager.setPv_new(8353, {
                            additional: {
                                i0: new Date().getTime() - startDownloadApiTime,
                                i1: 1,
                                i2: Number(resultData.stat),
                                i4: pageNum,
                            }
                        })
                        try {
                            if (resultData['is_use_crop_asset']) {
                                const { canvas } = storeAdapter.getStore<
                                    typeof storeAdapter.store_names.paintOnCanvas
                                >({
                                    store_name: storeAdapter.store_names.paintOnCanvas,
                                });
                                assetManager.setPv_new(7099, {
                                    additional: {
                                        i0: canvas.width,
                                        i1: canvas.height
                                    },
                                });
                            }
                        } catch (error) {
                            console.error(error);
                        }

                        const downPicTime = (new Date().getTime() - startDownPicTime) / 1000;
                        InfoManageHelper.addDownloadPicTime(downPicTime);
                        // canvasStore.dispatch(infoManage('ADD_DOWNLOAD_PIC_TIME', { downPicTime }));
                        // 下载限制 START
                        // @ts-ignore
                        window.callback = (res) => {
                            // res（用户主动关闭验证码）= {ret: 2, ticket: null}
                            // res（验证成功） = {ret: 0, ticket: "String", randstr: "String"}
                            if (Number(res.ret) === 0) {
                                props.downloadPicProps.ticket = res.ticket;
                                props.downloadPicProps.rendstr = res.randstr;
                                // emitter.emit('DownloadPopupUS', 3);
                                emitter.emit('downloadFileEmitter', { ...props });
                            } else if (Number(res.ret) === 2) {
                                emitter.emit('popupClose', false);
                                return;
                            }
                        };
    
                        if (Number(resultData.stat) === -100 || Number(resultData.stat) === -101) {
                            // 下载过于频繁触发创蓝图形验证码

                            // 本地生成失败，并且线上下载触发创蓝图形验证码
                            if (isCanvasDownFailed) {
                                assetManager.setPv_new(7093);
                            }

                            if (Number(resultData.stat) === -101) {
                                alert('验证码验证失败');
                            }
                            const checkBtn = document.createElement('button');
                            checkBtn.id = 'CaptchaEl';
                            checkBtn.type = 'button';
                            checkBtn.setAttribute('data-appid', '2032266202');
                            checkBtn.setAttribute('data-cbfn', 'callback');
    
                            const captcha1 = new Captcha(checkBtn);
                            captcha1.show();
                            return;
                        }
                        // 下载限制 END
    
                        const _dl_id = resultData.dl_id || '';
                        if (Number(resultData.stat) === 0) {

                            try {
                                // 本地生成失败，并且线上下载触发下载限制
                                if (isCanvasDownFailed) {
                                    assetManager.setPv_new(7094);
                                }
                            } catch (error) {
                                console.error(error);
                            }

                            // canvasStore.dispatch(paintOnCanvas('F', { isDownloadFlag: 2 }));
                            const show_share_popupMap: { set_time?: string; click_close_count?: number } = {};
                            const dateTime = new Date();
                            const load_share_popup_number = JSON.parse(localStorage.getItem('ue_show_share_popup_number'));
                            show_share_popupMap.set_time =
                                dateTime.getDate().toString() +
                                dateTime.getMonth().toString() +
                                dateTime.getFullYear().toString();
                            const many_times_download_flag = Boolean(
                                load_share_popup_number &&
                                    show_share_popupMap.set_time === load_share_popup_number.set_time,
                            );
                            // if (load_share_popup_number && load_share_popup_number.click_close_count > 1) {
                            if (false) {
                                // emitter.emit('popupClose')
                                // 第二次及之后的下载受限弹框的弹出
                                emitter.emit(
                                    'PopupUpdateWindowStyle',
                                    {
                                        width: 370,
                                        height: 440,
                                        padding: 0,
                                        background: '#FFFFFF',
                                        'boxShadow':
                                            '0px 0px 8px 0px rgba(0, 0, 0, 0.12), 0px 0px 2px 0px rgba(0, 0, 0, 0.12)',
                                        borderRadius: '8px',
                                    },
                                    true,
                                );
                                emitter.emit('DownloadPopupUS', 56);
                                show_share_popupMap.click_close_count = ++load_share_popup_number.click_close_count;
                                emitter.emit('closePopupBtn', false);
                            } else {
                                emitter.emit(
                                    'PopupUpdateWindowStyle',
                                    {
                                        width: 400,
                                        height: 320,
                                    },
                                    true,
                                );
                                if (many_times_download_flag) {
                                    show_share_popupMap.click_close_count = ++load_share_popup_number.click_close_count;
                                } else {
                                    show_share_popupMap.click_close_count = 1;
                                }
                                emitter.emit('PopupUpdateWindowClass', {
                                    windowContent: (
                                        <DownloadPopup key={new Date().getTime()} className="DownloadLimitPopup" />
                                    ),
                                });
                                emitter.emit(
                                    'DownloadPopupUS',
                                    3,
                                    undefined,
                                    load_share_popup_number ? load_share_popup_number.click_close_count : 1,
                                );
                            }
                            localStorage.setItem('ue_show_share_popup_number', JSON.stringify(show_share_popupMap));
                            // emitter.emit('DownloadPopupUS', 3);
                            const { info } = storeAdapter.getStore({
                                store_name: storeAdapter.store_names.InfoManage,
                            });
                            assetManager.setPagePv_new( info?.is_company_temp === 1 ? 4736 : 1857, '', getProps().upicId > 0 ? getProps().upicId : 0, '', '', {
                                i0: info?.is_company_temp,
                                s0: load_share_popup_number ? load_share_popup_number.click_close_count : 1,
                                i2: 2,
                                i1: _this.state.isVideo ? 1 : 0
                            });
                            emitter.emit('pageDouble', 2);
                        } else if (Number(resultData.stat) === 1) {
                            const jobId = resultData['job'];
                            CanvasPaintedLogic.updateJobid({
                                jobId,
                            });
                            assetManager.setPagePv_new(91, '', 0);
                            let intervalTime = 0,
                                intervalTimeMax = 60;
                            let pdTime = 0;
                            const {canvas, startDownloadTime } = storeAdapter.getStore({
                                store_name: storeAdapter.store_names.paintOnCanvas,
                            });
                            const maxEdgePx: number = Math.max(canvas.width, canvas.height); // 用大边做判断
                            if (maxEdgePx >= 6000 && maxEdgePx < 8000) {
                                intervalTimeMax = 120;
                            }
                            if (maxEdgePx >= 8000) {
                                intervalTimeMax = 240;
                            }
                            if (canvas.width * canvas.height > 2560 * 1440 && intervalTimeMax < 180) {
                                // 大于 2k 分辨率 延长超时时间
                                intervalTimeMax = 180;
                            }
                            if (canvas.width * canvas.height > 3840 * 2160 && intervalTimeMax < 240) {
                                // 大于 4k 分辨率 延长超时时间
                                intervalTimeMax = 240;
                            }
                            if (newState.tempDownloadInterval) {
                                clearInterval(newState.tempDownloadInterval);
                            }
                            if (newState.work.pages.length > 3) {
                                //多页情况
                                intervalTimeMax = 240;
                            }
                            if (_this.state.isVideo || props.downloadPicProps.isVideo || props.downloadPicProps.isGif) {
                                intervalTimeMax = videoTotalTime > 35 * 1000 ? 14 * 60 : 6 * 60;
                            }
    
                            let lineUpFlag = 0;
                            const generateFlag = 0;
                            newState.tempDownloadInterval = setInterval(function () {
                                intervalTime += 1;
                                if (intervalTime >= intervalTimeMax) {
                                    const _urlProps = getProps() as unknown as { upicId: number };
                                    CanvasPaintedLogic.updateIsDownloadFlag({
                                        isDownloadFlag: 3,
                                    });
                                    // newState.isDownloadFlag = 3;
                                    clearInterval(newState.tempDownloadInterval);
                                    emitter.emit('DownloadPopupUS', 6);
                                    assetManager.setPv_new(879, {
                                        additional: {
                                            user_templ_id: _urlProps.upicId,
                                            s0: _this.state.isVideo ? 'video' : 'image',
                                            i4: pageNum,
                                        },
                                    });
                                    if (isCanvasDownFailed) {
                                        // 本地生成失败，使用线上下载超时
                                        assetManager.setPv_new(7071);
                                    }
                                    return;
                                }
                                if (Number(newState.isDownloadFlag) !== 1) {
                                    clearInterval(newState.tempDownloadInterval);
                                    return;
                                }

                                assetManager.downloadFlagCheck({ jobId: jobId, isVideo: _this.state.isVideo || props.downloadPicProps.isVideo || props.downloadPicProps.isGif}).then((data) => {
                                    data.json().then((resultData) => {
                                        if (Number(resultData.stat) === 1) {
                                            CanvasPaintedLogic.updateSuccessImgPath({

                                                successImgPath: resultData.path,
                                            });
                                            if (Number(resultData.job) === 1) {
                                                CanvasPaintedLogic.updateIsDownloadFlag({
                                                    isDownloadFlag: 2,
                                                });
                                                // 本地生成失败，并且线上下载成功发送的埋点
                                                if (isCanvasDownFailed) {
                                                    assetManager.setPv_new(7014);
                                                }
                                                clearInterval(newState.tempDownloadInterval);
                                                let waitTime = 0;
                                                const transitionTime = 300;
                                                if (!lineUpFlag) {
                                                    waitTime = transitionTime;
                                                    /*结束进度条 START*/
                                                    emitter.emit('DownloadPopupUpdateProgressLineUp', 100, transitionTime);
                                                    /*结束进度条 END*/
                                                }
    
                                                setTimeout(() => {
                                                    if (!generateFlag) {
                                                        emitter.emit('DownloadPopupUS', 10);
                                                        waitTime = transitionTime;
                                                        setTimeout(() => {
                                                            /*结束进度条 START*/
                                                            emitter.emit(
                                                                'DownloadPopupUpdateProgressGenerate',
                                                                100,
                                                                transitionTime,
                                                            );
                                                            /*结束进度条 END*/
                                                        }, 100);
                                                    }
                                                }, waitTime);
                                                setTimeout(() => {
                                                    // newState.isDownloadFlag = 2;
    
                                                    const download_time =
                                                        (new Date().valueOf() - download_start_time) / 1000;
                                                    assetManager.setPagePv_new(
                                                        77,
                                                        '',
                                                        urlProps.upicId > 0 ? urlProps.upicId : 0,
                                                        download_time,
                                                        '',
                                                        {},
                                                    );
                                                    const { info } = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
                                                        store_name: storeAdapter.store_names.InfoManage,
                                                    });
                                                 
                                                     const pvClassIds = info?.class_id ? info.class_id.filter((i) => i).map((s) => `a-${s}`).join(',') : '';
                                                    assetManager.setPagePv_new(
                                                        1856,
                                                        'user',
                                                        urlProps.upicId > 0 ? urlProps.upicId : 0,
                                                        Math.round(download_time * 1000),
                                                        '',
                                                        {
                                                            s0: pvClassIds,
                                                            i2: info?.is_company_temp?.toString(),
                                                            i3: urlProps.ai_template_type,
                                                            ti: urlProps.picId ?? '',
                                                        },
                                                    );
                                                    assetManager.setPv_new(275, {
                                                        additional: {
                                                            s0: props.downloadPicProps.isPrint,
                                                            s1: props.downloadPicProps.dpi,
                                                            s2: props.downloadPicProps.origin,
                                                        },
                                                    });
                                                       // 使用了风格下载
                                                    if(downloadSuccessAdditional.i3){
                                                        assetManager.setPv_new(7739,{
                                                            additional: downloadSuccessAdditional,
                                                        })
                                                    }
                                                    if(emphaticMarkIds.length || designerEmpharticMarkIds.length){
                                                        assetManager.setPv_new(8691, {
                                                            additional:{
                                                                s0: emphaticMarkIds.join(','),
                                                                s1: designerEmpharticMarkIds.join(','),
                                                            }
                                                        })
                                                    } 
                                                    if(effectEmphaticMarkIds.length || effectDesignerEmpharticMarkIds.length){
                                                        assetManager.setPv_new(8702, {
                                                            additional:{
                                                                s0: effectEmphaticMarkIds.join(','),
                                                                s1: effectDesignerEmpharticMarkIds.join(','),
                                                            }
                                                        })
                                                    }
                                                    if(aiToolUsedType && aiToolUsedType.length){
                                                        aiToolUsedType.forEach((toolType)=>{
                                                            assetManager.setPv_new(8858, {
                                                                additional:{
                                                                    s0: toolType,
                                                                    s2: urlProps.subOrigin,
                                                                }
                                                            })
                                                        })
                                                    }
                                                    assetManager.setPv_new(4697, {
                                                        additional: downloadSuccessAdditional,
                                                    })
                                                    
                                                    TemplateLogic.checkImageAssetTag(82)
    
                                                    if (props.origin == 'phone') {
                                                        emitter.emit('popupClose', false);
                                                        const windowInfo = {
                                                            windowContent: (
                                                                <div>
                                                                    <QRCodeCanvas
                                                                        className="qrCodeCanvas"
                                                                        value={
                                                                            'https://818ps.com/site/phone-download?path=' +
                                                                            urlEncode(resultData.path)
                                                                        }
                                                                        size={240}
                                                                    />
                                                                    <p
                                                                        style={{
                                                                            textAlign: 'center',
                                                                            marginTop: '10px',
                                                                            fontSize: '16px',
                                                                            color: '#333',
                                                                        }}
                                                                    >
                                                                        使用手机扫一扫，扫码下载
                                                                    </p>
                                                                </div>
                                                            ),
                                                            popupWidth: '260px',
                                                            popupHeight: '280px',
                                                            style: {
                                                                borderRadius: '2px',
                                                                backgroundColor: 'rgba(255, 255, 255, 1)',
                                                                top: '50%',
                                                                left: '50%',
                                                                transform: 'translate(-50%,-50%)',
                                                            },
                                                            popupTitleBarStyle: {
                                                                width: 0,
                                                                height: 0,
                                                            },
                                                            popupBodyStyle: {}
                                                        };
    
                                                        emitter.emit('popupWindow', windowInfo);
                                                    } else {
                                                        const {
                                                            user: { vip },
                                                        } = storeAdapter.getStore({
                                                            store_name: storeAdapter.store_names.paintOnCanvas,
                                                        });
    
                                                        // const isDownloadRange = days>=90 && Number(downloadNum)>=5 && (Number(userPay53Count)+Number(userPayCount))<=0 && vip<=1;
    
                                                        // const isSingleDownloadSuccess = days>=90 && vip<=1;
    
                                                        const isShow = vip <= 1;
    
                                                        const windowInfo = {
                                                            windowContent: (
                                                                <DownloadPopup
                                                                    key={new Date().getTime()}
                                                                    isSingleDownloadSuccess={isShow}
                                                                    className="DownloadPopup"
                                                                    showFlag={2}
                                                                    newUserDlNum={resultData.new_user_dl_num}
                                                                />
                                                            ),
                                                            popupWidth: 760,
                                                            popupHeight: 400,
                                                            style: {
                                                                borderRadius: '5px',
                                                                backgroundColor: 'rgba(255, 255, 255, 1)',
                                                            },
                                                            isDownLoadSuccess: true,
                                                            isUnVip: isShow,
    
                                                            // popupTitleBarStyle:{
                                                            //     display:"none"
                                                            // }
                                                        };
    
                                                        if (isShow) {
                                                            windowInfo.popupWidth = 658;
                                                            windowInfo.popupHeight = 388;
                                                        }
    
                                                        emitter.emit('popupWindow', windowInfo);
                                                        emitter.emit('DownloadPopupUS', 2);
                                                        const { work } = storeAdapter.getStore<
                                                            typeof storeAdapter.store_names.paintOnCanvas
                                                        >({
                                                            store_name: storeAdapter.store_names.paintOnCanvas,
                                                        });
                                                        const pageNum = work.pages.filter(
                                                            (item) => item.assets.length !== 0,
                                                        );
                                                        if (pageNum.length > 1) {
                                                            assetManager.setPv_new(4311);
                                                        }
                                                        emitter.emit('closePopupBtn');
                                                        if (!(props && props.isWeChatOpenPlatform)) {
                                                            // if ((window.navigator.mimeTypes[40] || !window.navigator.mimeTypes.length)) {
                                                            // 360 特供
                                                            // window.open(resultData.path);
                                                            //模板下载给个文件名
                                                            fetch(resultData.path).then((res) =>
                                                              res.blob().then((blob) => {
                                                                const {fileType, pickPages} = props.downloadPicProps;
                                                                
                                                                let suffix = fileType;
                                                                if(fileType == "ppt") {
                                                                    suffix = "pptx";
                                                                } else {
                                                                    const path: string = resultData.path;
                                                                    const index = path.lastIndexOf(".");
                                                                    if(index) {
                                                                        suffix = path.substring(index + 1, path.length)
                                                                    }
                                                                }
                                                                const a = document.createElement('a');
                                                                const url = window.URL.createObjectURL(blob);
                                                                a.href = url;
                                                                a.download = `${resultData.title}.${suffix}`;
                                                                a.click();
                                                                window.URL.revokeObjectURL(url)
                                                                assetManager.setPv_new(8356, {
                                                                    additional: {
                                                                        i0: blob.size,
                                                                        i4: pageNum.length,
                                                                    }
                                                                })
                                                              }),
                                                            );
                                                            
                                                            // } else {
                                                            //     windowDeforeunload.close();
                                                            //     window.location.href = resultData.path;
                                                            //     setTimeout(function(){
                                                            //         windowDeforeunload.open();
                                                            //     }, 1000);
                                                            //     console.log("下载完成");
                                                            // }
                                                        }
                                                    }

                                                    assetManager.setDownloadPv_new(
                                                        93,
                                                        (new Date().getTime() - startDownloadTime) / 1000,
                                                        { i4: _dl_id },
                                                    );

                                                    // 去除摄图伪 4K 参数 START
                                                    let is4K = 1;
                                                    try {
                                                        const { work, canvas} = storeAdapter.getStore<
                                                            typeof storeAdapter.store_names.paintOnCanvas
                                                        >({
                                                            store_name: storeAdapter.store_names.paintOnCanvas,
                                                        });
    
                                                        if (canvas.width * canvas.height > 4096 * 2160) {
                                                            is4K = 2;
                                                        }
                                                    } catch (error) {
                                                        console.error(error);
                                                    }
                                                    // 去除摄图伪 4K 参数 END
    
                                                    assetManager.setDownloadPv_new(
                                                        95,
                                                        (new Date().getTime() - startDownloadTime) / 1000 - pdTime,
                                                        {
                                                            i2: is4K,
                                                            i4: _dl_id,
                                                            s0: info.downloadType,
                                                            s1: info.downPicTime,
                                                            s2: info.saveTime,
                                                            s3: _this.state.isVideo? 'video' : 'image'
                                                        },
                                                    );
    
                                                    /*启动微信开放平台 START*/
                                                    if (props && props.isWeChatOpenPlatform) {
                                                        emitter.emit('InfoBarBindWeChatOpenPlatform', {
                                                            uploadFileUrl: resultData.path,
                                                            appId: props.appId,
                                                        });
                                                        // emitter.emit("WeChatOpenPlatformUploadImageWechat", resultData.path);
                                                    }
                                                    /*启动微信开发平台 END*/

                                                    // // 切图模板对比图生成 STRAT
                                                    // try {
                                                    //     if (!props.downloadPicProps.isVideo && !props.downloadPicProps.isGif) {
                                                    //         if (canvas.width * canvas.height > 4096*2160) {
                                                    //             assetManager.asyncQietu({
                                                    //                 id: urlProps.upicId,
                                                    //                 dl_id: _dl_id
                                                    //             });
                                                    //         }
                                                    //     }    
                                                    // } catch (error) {
                                                    //     console.log(error);                                                    
                                                    // }
                                                    // // 切图模板对比图生成 END
                                                    // // 切图模板对比图生成 STRAT
                                                    // try {
                                                    //     if (!props.downloadPicProps.isVideo && !props.downloadPicProps.isGif) {
                                                    //         if (canvas.width * canvas.height < 1000*1000) {
                                                    //             assetManager.asyncLocal({
                                                    //                 id: urlProps.upicId,
                                                    //                 dl_id: _dl_id
                                                    //             });
                                                    //         }
                                                    //     }    
                                                    // } catch (error) {
                                                    //     console.log(error);                                                    
                                                    // }
                                                    // // 切图模板对比图生成 END
                                                }, waitTime + 500);
                                                if (downloadFileProps.isPhoneBindClose) {
                                                    assetManager.setPv_new(130);
                                                } else if (downloadFileProps.isPhoneBindSuccess) {
                                                    assetManager.setPv_new(131);
                                                } else {
                                                    assetManager.setPv_new(132);
                                                }
                                            } else if (Number(resultData.job) === 100) {
                                                emitter.emit('DownloadProgressPageNum', {
                                                    pages: resultData.pages,
                                                    success_page: resultData.success_page
                                                });
                                                
                                                if (!lineUpFlag) {
                                                    lineUpFlag = 1;
                                                    const transitionTime = 300;
                                                    /*结束进度条 START*/
                                                    emitter.emit('DownloadPopupUpdateProgressLineUp', 100, transitionTime);
                                                    /*结束进度条 END*/
    
                                                    if (pdTime === 0) {
                                                        pdTime = (new Date().getTime() - startDownloadTime) / 1000;
                                                        assetManager.setDownloadPv_new(94, pdTime, {s0: _this.state.isVideo ? 'video' : 'image'});
                                                    }
    
                                                    setTimeout(() => {
                                                        emitter.emit('DownloadPopupUS', 10);
                                                        setTimeout(() => {
                                                            emitter.emit('DownloadPopupRunProgressGenerate', 7000, 100);
                                                        }, 100);
                                                    }, transitionTime);
                                                }
                                                emitter.emit('DownloadPopupRunProgressGenerateTime', intervalTime); //记录下载花费时间
                                            }
                                        } else if (resultData.stat === 0) {
                                            clearInterval(newState.tempDownloadInterval);
                                            // canvasStore.dispatch(
                                            //     paintOnCanvas('UPDATE_ISDOWNLOADFLAG', { isDownloadFlag: 2 }),
                                            // );
    
                                            CanvasPaintedLogic.updateIsDownloadFlag({
                                                isDownloadFlag: 2,
                                            });
                                            emitter.emit(
                                                'PopupUpdateWindowStyle',   
                                                {
                                                    width: 400,
                                                    height: 320,
                                                },
                                                true,
                                            );
                                            emitter.emit('DownloadPopupUS', 3);
                                            const { info } = storeAdapter.getStore({
                                                store_name: storeAdapter.store_names.InfoManage,
                                            });
                                            // assetManager.setPagePv_new(
                                            //     info?.is_company_temp === 1 ? 4736 :1857,
                                            //     '', 
                                            //     // @ts-ignore
                                            //     getProps().upicId > 0 ? getProps().upicId : 0,
                                            // );
                                            assetManager.setPv_new(
                                                1857,
                                                '', 
                                                // @ts-ignore
                                                getProps().upicId > 0 ? getProps().upicId : 0,
                                                {
                                                    additional:{
                                                        i0:info?.is_company_temp
                                                    }
                                                }
                                            );
                                        }
                                        if (resultData.job < 0) {
                                            clearInterval(newState.tempDownloadInterval);
                                            assetManager.setPv_new(9018, {
                                                additional: {
                                                    i0: resultData.job,
                                                }
                                            });
                                        }
                                    });
                                });
                            }, 1000);
                            // @ts-ignore
                            // eslint-disable-next-line @typescript-eslint/no-unused-expressions
                            newState.tempDownloadInterval;
                            CanvasPaintedLogic.updateTempDownloadInterval({
                                tempDownloadInterval: newState.tempDownloadInterval,
                            });
    
                            // emitter.emit('DownloadPopupUS', 2)
                            // // this.newTab.location.href = resultData.msg
                            // let tempFun = window.onbeforeunload
                            // window.onbeforeunload = function(e){
                            //
                            // }
                            // window.location.href = resultData.msg
                            // setTimeout(function(){
                            //     window.onbeforeunload = tempFun
                            // }, 1000)
                        } else if (Number(resultData.stat) === -10086) {

                            try {
                                // 本地生成失败，并且线上下载触发手机绑定
                                if (isCanvasDownFailed) {
                                    assetManager.setPv_new(7095);
                                }
                            } catch (error) {
                                console.error(error);
                            }

                            // emitter.emit('InfoBarPhoneBindPopup');
                            CanvasPaintedLogic.updateIsDownloadFlag({
                                isDownloadFlag: 2,
                            });
                            emitter.emit('DownloadPopupUS', 5);
                            assetManager.setPv_new(110);
                            emitter.emit('InfoBarPhoneBindPopup', { ...props });
                        } else if (Number(resultData.stat) === -21) {

                            try {
                                // 本地生成失败，并且线上下载触发限制下载（短时间内多次下载）
                                if (isCanvasDownFailed) {
                                    assetManager.setPv_new(7096);
                                }
                            } catch (error) {
                                console.error(error);
                            }

                            const _urlProps = getProps() as unknown as { upicId: number };
                            CanvasPaintedLogic.updateIsDownloadFlag({
                                isDownloadFlag: 2,
                            });
                            emitter.emit('DownloadPopupUS', 21);
                            assetManager.setPv_new(880, {
                                additional: {
                                    user_templ_id: _urlProps.upicId,
                                },
                            });
                        } else {
                            CanvasPaintedLogic.updateIsDownloadFlag({
                                isDownloadFlag: 2,
                            });
                            emitter.emit('DownloadPopupUS', 4);
                            // assetManager.setPv_new(2569);

                            // 去除摄图伪 4K 参数 START
                            let is4K = 1;
                            try {
                                const { work, canvas} = storeAdapter.getStore<
                                    typeof storeAdapter.store_names.paintOnCanvas
                                >({
                                    store_name: storeAdapter.store_names.paintOnCanvas,
                                });

                                if (canvas.width * canvas.height > 4096 * 2160) {
                                    is4K = 2;
                                }

                            } catch (error) {
                                
                            }
                            // 去除摄图伪 4K 参数 END

                            assetManager.setPv_new(2569,
                                {
                                    additional:{
                                        i2: is4K,
                                    }
                                }
                            );

                            if (isCanvasDownFailed) {
                                // 本地生成失败，使用线上下载进入排队时失败
                                assetManager.setPv_new(7072);
                            }

                        }
                        emitter.emit('getDownloadNumInfo');
                    });
                }, () => {
                    // 统计接口耗时
                    assetManager.setPv_new(8353, {
                        additional: {
                            i0: new Date().getTime() - startDownloadApiTime,
                            i1: 0,
                            i4: pageNum,
                        }
                    })
                })
                .catch(() => {
                    emitter.emit('DownloadPopupUS', 30);
                });
        }
        

        const { isEdit } = storeAdapter.getStore<typeof storeAdapter.store_names.operationRecordRedux>({
            store_name: storeAdapter.store_names.operationRecordRedux,
        });
        // urlProps = getProps();
        const { info } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage,
        });
        if (isEdit) {
            assetManager.setPagePv_new(71, '', urlProps.upicId > 0 ? urlProps.upicId : 0,0,'',
            {
                i2:info?.is_company_temp?.toString()
            });
        } else {
            assetManager.setPagePv_new(72, '', urlProps.upicId > 0 ? urlProps.upicId : 0,0,'',
            {
                i2:info?.is_company_temp?.toString()
            });
        }
    }

    phoneBindPopupEmitter(): void {
        const th = this;
        this.phoneBindEmitter = emitter.addListener('InfoBarPhoneBindPopup', (props: { setPvType?: string }) => {
            th.phoneBindPopup(props);
            assetManager.setPv_new(2017);
            assetManager.setPv_new(4181, {
                additional: {
                    s0: props ? props : 'none',
                },
            });
            if (props?.setPvType === 'LoginShow') {
                assetManager.setPv_new(4147);
            }
            // assetManager.getLoginValidateImg()
            // th.tempTimer = setTimeout(() => {
            //     th.phoneBindPopup(props);
            //     clearTimeout(th.tempTimer)
            // }, 0);
        });
    }
    // @ts-ignore
    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-typespopupWrapper
    phoneBindPopup(props): void {
        emitter.emit('popupClose');

        const windowInfo = {
            windowContent: (
                <PhoneBind
                    key={'PhoneBind_' + new Date().getTime()}
                    origin={props}
                    {...props}
                    randomKeys={'PhoneBind_' + new Date().getTime()}
                />
            ),
            popupWidth: 'auto',
            popupHeight: 'auto',
            style: {
                borderRadius: '0',
                backgroundColor: 'rgba(255, 255, 255, 0)',
                top: 0,
                left: 0,
                bottom: 0,
                right: 0,
                padding: 0,
            },
            popupTitleBarStyle: {
                width: 0,
                height: 0,
                display: 'none',
            },
        };

        emitter.emit('popupWindow', windowInfo);
    }

    downloadEmitter(): void {
        emitter.addListener('InfoBarDownload', (props: {
            callbackProps?: {
                downloadPicProps?: {
                    dpi: number;
                    fileType: string;
                    force: number;
                    isPrint: boolean;
                    isVideo?: boolean;
                    isGif?: boolean;
                    pickPages?: number[];
                    origin: string;
                },
                origin?: string;
                from?: string;
            }
        } = {}) => {
            // jyjin 图片下载看这里
            CanvasPaintedLogic.updateStartDownloadTime({});
            CanvasPaintedLogic.updateIsDownloadFlag({
                isDownloadFlag: 1,
            });

            const { isVideo } = this.state

            if (props?.callbackProps?.downloadPicProps?.isVideo) {
                this.setState({isVideo: props.callbackProps.downloadPicProps.isVideo})
            } else if (!props?.callbackProps?.downloadPicProps?.isVideo){
                // this.setState({isVideo: true})
                this.setState({isVideo: props.callbackProps.downloadPicProps.isVideo})
            } else if (isVideo && !props?.callbackProps?.downloadPicProps?.isVideo){
                this.setState({isVideo: true})
            } else {
                this.setState({isVideo: false})
            }
            const windowInfo = {
                windowContent: <DownloadPopup key={new Date().getTime()} className="DownloadPopup" fileType = {props?.callbackProps?.downloadPicProps.fileType} />,
                popupWidth: 460,
                popupHeight: 220,
                style: {
                    borderRadius: props?.callbackProps?.from === 'fromPreview' ? '14px' : '4px',
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                },
            };
            emitter.emit('popupWindow', windowInfo);
            emitter.emit('DownloadPopupUS', 10);
            let intervalTime = 1;
            if (this.intervalDownloadStart) {
                clearInterval(this.intervalDownloadStart);
            }
            this.intervalDownloadStart = setInterval(function () {
                intervalTime += 1;
                if (intervalTime >= 20) {
                    CanvasPaintedLogic.updateIsDownloadFlag({
                        isDownloadFlag: 3,
                    });
                    // newState.isDownloadFlag = 3;
                    clearInterval(this.intervalDownloadStart);
                    emitter.emit('DownloadPopupUS', 6);
                    return;
                }
            }, 3000);
            this.isSaveing = false;

            let { work } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            const save_fun = () => {
                const is_loaded = true;
                ({ work } = storeAdapter.getStore({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                }));
                // for (let j = 0; j < work.pages.length; j++) {
                //     for (let i = 0; i < work.pages[j].assets.length; i++) {
                //         const temp_asset = work.pages[j].assets[i];
                //         if (temp_asset.meta.type === 'text' && temp_asset.attribute.effectVariant3D && temp_asset.attribute.rt_renderImageLoading !== false) {
                //             is_loaded = false;
                //         }
                //     }
                // }
                if (is_loaded) {
                    emitter.emit('autoSaveTempl', this.downloadFile, 0, {
                        // @ts-ignore
                        callbackProps: props.callbackProps,
                        startSaveTime: new Date().getTime(),
                    });
                } else {
                    setTimeout(save_fun, 100);
                }
            };

            if (this.save_set_interval_listen) {
                clearTimeout(this.save_set_interval_listen);
            }
            emitter.emit('DownloadPopupRunProgressLineUp', 5000, 100);
            // 本地生成 3D文本 用户服务端合成成品
            // 3d字功能暂时吓掉了
            // Text3DLocalGenerate.localGenerate(work.pages);
            this.save_set_interval_listen = setTimeout(save_fun, 100);
        });
    }

    download2Emitter(): void {
        // @ts-ignore
        this.infoBarDownloadEmitter = emitter.addListener('InfoBarDownloadEmitter', (params = {}) => {
            this.beforeDownloadClickEvent('download');
            // th.downloadParams = params;
            // th.getDownloadNumInfo("download")
            // const {snum} = params;
            // th.download(snum);
        });
    }

    /**
     * 用户下载
     */
    download(snumPreShare?: number, e?: any): void {
        // canvasStore.dispatch(paintOnCanvas('UPDATE_OPERATION_RECORD_ISDOWNLOAD')); // 对应 store 废弃
        // canvasStore.dispatch(paintOnCanvas('UPDATE_STARTDOWNLOADTIME'))
        // emitter.emit('InfoBarDownload')
        // DownloadBootPage

        const {
            user: { vip },
        } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }); // canvasStore.getState().onCanvasPainted;

        // const isDownloadRange = days>=90 && Number(downloadNum)>=5 && (Number(userPay53Count)+Number(userPayCount))<=0 && vip<=1 && snumPreShare !=0;

        // const isSingleRecharge = days>=90 && vip<=1 && snumPreShare !=0;

        const isShowDownloadPop = vip <= 1 && snumPreShare != 0;
        const windowInfo = {
            windowContent: (
                <DownloadBootPage
                    from={typeof this.download_params === 'object' ? this.download_params.from : ''}
                    key={PreviewAndShare + '_' + new Date().getTime()}
                    fontImageAll={this.newFontImageAll}
                    origin={this.downloadParams ? this.downloadParams.origin : ''}
                    isSingleRecharge={isShowDownloadPop}
                    snum={this.state.snum}
                    newYearActivity={this.state.newYearActivity}
                    is_vip_templ={this.state.is_vip_templ}
                    vipType={this.state.vipType}
                    is_personal_com_vip={this.state.is_personal_com_vip}
                    is_old_vip={this.state.is_old_vip}
                    governmentTempl={this.state.governmentTempl}
                    show_but={this.state.show_but}
                    is_ppt_vip={this.state.is_ppt_vip}
                    unit = {this.state.unit}
                />
            ),
            popupWidth: 'auto',
            popupHeight: 'auto',
            style: {
                borderRadius: '0',
                backgroundColor: 'rgba(255, 255, 255, 0)',
                top: 0,
                left: 0,
                bottom: 0,
                right: 0,
                padding: 0,
            },
            popupTitleBarStyle: {
                width: 0,
                height: 0,
                display: 'none',
            },
        };

        // 分类id为132的采用新的下载弹窗
        // const {
        //     info: { class_id },
        // } = storeAdapter.getStore({
        //     store_name: storeAdapter.store_names.InfoManage,
        // });

        // if (class_id && class_id.length) {
        //     const hasId = class_id.indexOf('132') > -1;
        //     if (hasId) {
        //         windowInfo = {
        //             windowContent: (
        //                 <DownloadBootPageNew
        //                     from={typeof this.download_params === 'object' ? this.download_params.from : ''}
        //                     key={PreviewAndShare + '_' + new Date().getTime()}
        //                     fontImageAll={this.newFontImageAll}
        //                     origin={this.downloadParams ? this.downloadParams.origin : ''}
        //                     isSingleRecharge={isShowDownloadPop}
        //                     snum={this.state.snum}
        //                     newYearActivity={this.state.newYearActivity}
        //                     is_vip_templ={this.state.is_vip_templ}
        //                     vipType={this.state.vipType}
        //                 />
        //             ),
        //             popupWidth: 'auto',
        //             popupHeight: 'auto',
        //             style: {
        //                 borderRadius: '0',
        //                 backgroundColor: 'rgba(255, 255, 255, 0)',
        //                 top: 0,
        //                 left: 0,
        //                 bottom: 0,
        //                 right: 0,
        //                 padding: 0,
        //             },
        //             popupTitleBarStyle: {
        //                 width: 0,
        //                 height: 0,
        //                 display: 'none',
        //             },
        //         };
        //     }
        // }
        emitter.emit('popupWindow', windowInfo);

        if (e) {
            e.stopPropagation();
            e.nativeEvent.stopPropagation();
        }
    }

    /**
     * 自定义画布大小
     */
    sizeAreaClickEvent(): void {
        /*编辑器判断*/
        const { isDesigner } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if (!isDesigner) {
            emitter.emit('CanvasSizeShowEmitter');
        }
    }

    /**
     * 跳转首页面(悬停事件)
     */
    goHomeHoverEvent(e: MouseEvent): void {
        assetManager.setPv_new(7139, { additional: {} });
    }

    /**
     * 跳转首页面(点击事件) 
     */
    goHomeClickEvent(e: MouseEvent): void {
        assetManager.setPv_new(7140, { additional: {} });

        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        let target = '_self';
        if (/isClientSide/.test(location.href) && /mac/i.test(navigator.userAgent)) {
            target = '_blank';
        }
        if (user?.userId !== '0') {
            if (env.editor === "ueteam" || env.editor === "ecommerceteam" || isUeTeam || isEcommerceTeam()) {
                let origin = env.editor
                if(isUeTeam) origin = 'ueteam'
                if(isEcommerceTeam()) origin = 'ecommerce'
                if(isEcommerceTeam()) origin = 'ecommerceteam'
                window.open(`https://818ps.com/home?team_id=${getProps().team_id}&origin=${ origin }`, target)
            } else {
                window.open('https://818ps.com/home?team_id=0', target)
            }
        } else {
            window.open('https://818ps.com/', target)
        }
    }

    /**
     * 撤销(点击事件)
     */
    undoClickEvent(e: MouseEvent): void {
        TemplateHistory.undo();
        assetManager.setPv_new(2468, { additional: {} });
    }

    /**
     * 重做(点击事件)
     */
    redoClickEvent(e: MouseEvent): void {
        TemplateHistory.redo();
        assetManager.setPv_new(2469, { additional: {} });
    }

    /**
     * 全站搜索回车监听
     */
    globalSearchInputKeyDownEvent(e: KeyboardEvent): void {
        if (e.keyCode == 13) {
            this.globalSearchBtn();
        }
    }

    /**
     * 全站搜索按钮
     */
    globalSearchBtn(e?: MouseEvent): void {
        /*编辑器判断*/
        const { isDesigner } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (isDesigner) {
            // @ts-ignore
            // eslint-disable-next-line react/no-string-refs
            emitter.emit('BackgroundPanelMoreBtnClick', this.refs.globalSearchInput.value, e);
        } else {
            // @ts-ignore
            // eslint-disable-next-line react/no-string-refs
            emitter.emit('templatePanelMoreBtnClick', this.refs.globalSearchInput.value, e);
            // canvasStore.dispatch(paintOnCanvas('SITE_SEARCH_TEMPL', {}));
            // eslint-disable-next-line react/no-string-refs
            // @ts-ignore
            assetManager.setPagePv_new(78, '', '', '', this.refs.globalSearchInput.value);
        }
    }

    /**
     * 保存到公众号(emitter)
     * @param e
     */
    uploadWebChatOpenPlatformEmitter(): void {
        const th = this;
        this.uploadWebChatPlatformEmitter = emitter.addListener('InfoBarUploadWebChatOpenPlatform', (appId: string) => {
            // canvasStore.dispatch(paintOnCanvas('UPDATE_STARTDOWNLOADTIME'))
            // canvasStore.dispatch(paintOnCanvas('UPDATE_ISDOWNLOADFLAG', {isDownloadFlag: 1}))

            let intervalTime = 1;
            if (th.intervalDownloadStart) {
                clearInterval(th.intervalDownloadStart);
            }
            th.intervalDownloadStart = setInterval(function () {
                intervalTime += 1;
                if (intervalTime >= 20) {
                    CanvasPaintedLogic.updateIsDownloadFlag({
                        isDownloadFlag: 3,
                    });
                    // canvasStore.dispatch(paintOnCanvas('UPDATE_ISDOWNLOADFLAG', { isDownloadFlag: 3 }));
                    // newState.isDownloadFlag = 3;
                    clearInterval(th.intervalDownloadStart);
                    emitter.emit('DownloadPopupUS', 6);
                    return;
                }
            }, 3000);
            th.isSaveing = false;
            emitter.emit('autoSaveTempl', th.downloadFile, 0, {
                callbackProps: {
                    isWeChatOpenPlatform: true,
                    appId: appId,
                },
            });
        });
    }

    /**
     * 保存到公众号
     * @param e
     */
    uploadWebChatOpenPlatformClickEvent(e?: MouseEvent): void {
        assetManager.setPv_new(109);
        emitter.emit('InfoBarBindWeChatOpenPlatform', '');

        if (e) {
            e.stopPropagation();
            (e as any)?.nativeEvent.stopPropagation();
        }
    }

    showWeChatOpenPlatform(props: any): void {
        const windowInfo = {
            windowContent: (
                <WeChatOpenPlatform
                    key={'WeChatOpenPlatform_' + new Date().getTime()}
                    {...props}
                    snum={this.state.snum}
                />
            ),
            popupWidth: 'auto',
            popupHeight: 'auto',
            style: {
                borderRadius: '0',
                backgroundColor: 'rgba(255, 255, 255, 0)',
                top: 0,
                left: 0,
                bottom: 0,
                right: 0,
                padding: 0,
            },
            popupTitleBarStyle: {
                width: 0,
                height: 0,
                display: 'none',
            },
        };

        emitter.emit('popupWindow', windowInfo);
    }

    // 无水印下载
    beforeDownloadClickEvent(type: string, from?: string, e?: React.MouseEvent): void {
        // 触发充值弹框 。。。。。 测试测试
        // let windowInfo = {
        //     windowContent: <RechargeVipPopup />,
        //     popupWidth: 860,
        //     popupHeight: 608,
        //     style: {
        //         borderRadius: '12px',
        //         backgroundColor: '#FDFDFD',
        //         padding: 0,
        //     },
        //     popupBodyStyle: {
        //         padding: 0,
        //     },
        //     popupTitleBarStyle: {
        //         height: 0,
        //         display: 'none'
        //     }
        // };

        // emitter.emit('popupWindow', windowInfo);
        emitter.emit('closePopupBtn', false);
        const rechargePop = false; // 充值弹框

        const { user, work, doc_createTime } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if( user.bind_phone == 0 ){
          return emitter.emit('InfoBarPhoneBindPopup');
        }
        const pageNum = work.pages.filter((item) => item.assets.length != 0).length;
        // 电商编辑器-批量生成商品图-无水印点击人数
        assetManager.setPv_new(6973, {
            additional: {
                s0: pageNum
            }
        });
        // 未登录用户同样触发下载埋点
        const { info } = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
            store_name: storeAdapter.store_names.InfoManage,
        });
        const pvClassIds = info?.class_id ? info.class_id.filter((i) => i).map((s) => `a-${s}`).join(',') : '';
        const editTime =  Math.round((new Date().getTime() - doc_createTime) / 1000);
        assetManager.setPv_new(2414, {
            additional: {
                s0: pvClassIds,
                i0: editTime,
            },
        })
        
        if (!(Number(user.userId) > 0)) {
            switch (type) {
                case 'download':
                    if (from === 'file') {
                    }
                    emitter.emit('LoginPanelShow', 'download');
                    assetManager.setPv_new(174, {
                        additional: {
                            s0: 'download',
                        },
                    });
                    break;
                case 'weChat':
                    emitter.emit('LoginPanelShow', 'saveWebChatOpen');
                    assetManager.setPv_new(174, {
                        additional: {
                            s0: 'saveWebChatOpen',
                        },
                    });
                    break;
            }
            assetManager.setPv_new(165, {additional: {i2:info?.is_company_temp, i4: pageNum}});
            return;
        }
        CanvasPaintedLogic.updateIsDownloadFlag({
            isDownloadFlag: 0,
        });
        const urlProps: IAnyObj = IPSConfig.getProps();

        this.getDownloadNumInfo(type, rechargePop, false, {
            from: from,
        });
        
        // this.getDownloadNumInfo(type,isAb);
        assetManager.setPv_new(165, {additional: {i2:info?.is_company_temp, i4: pageNum}});

        assetManager.setPv_new(4181);

        e?.stopPropagation();
        e?.nativeEvent.stopPropagation();
    }

    getDownloadNumInfo(type: string, rechargePop: boolean, isCloseinternal: boolean, params: { from: string }): void {
        this.downloadParams = { origin: type };
        const windowInfo = {
            windowContent: (
                <DownloadPopup
                    isShowCloseInternal={true}
                    key={new Date().getTime()}
                    className="DownloadPopup"
                    showFlag={0}
                    from={params.from}
                />
            ),
            popupWidth: 460,
            popupHeight: 190,
            style: {
                borderRadius: '8px',
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                left: '100%',
                top: '255px',
                transform: 'translate(-100%, -55%)',
                width: '308px',
                height: '315px',
            },
        };

        if (params.from === 'file') {
            windowInfo.style = Object.assign(windowInfo.style, {
                ...windowInfo.style,
                left: '50%',
                top: '50%',
                transform: 'translate(-50%, -50%)',
            });
            // @ts-ignore
            windowInfo.popupTitleBarStyle = {
                display: 'none',
            };
        }

        emitter.emit('popupWindow', windowInfo);
        const urlProps: IAnyObj = IPSConfig.getProps();
        if(!urlProps.upicId){
            emitter.emit('autoSaveTempl', '', 0, { handleSave: true, saveSuccessCb:()=>{
                this.requestDownloadNumInfo(type, rechargePop,isCloseinternal,params)
             }});
        }else{
            this.requestDownloadNumInfo(type, rechargePop,isCloseinternal,params)
        }
       
    }
    requestDownloadNumInfo(type: string, rechargePop: boolean, isCloseinternal: boolean, params: { from: string }){
        const th = this;
        assetManager.getDownloadNumInfo().then((data) => {
            data.json().then((resultData) => {
                if (resultData.stat === 1) {
                    const { snum, is_company_temp, is_over_five_times } = resultData.msg;
                    //(snum === 0 || is_over_five_times)
                    if ((snum === 0 || is_over_five_times) && is_company_temp !== 1) {
                        this.setState({
                            showVipRecharge: true,
                        })
                        assetManager.setPv_new(9027, {additional: {
                            i4: is_over_five_times,
                        }});
                        emitter.emit('popupClose');
                        return;
                    }
                    const urlProps: IAnyObj = IPSConfig.getProps();
                    assetManager.companyTemplateDl(urlProps.upicId).then(data=>{
                        // 分类id不等于132的或者id=132且是vip且下载次数为0，弹出居中弹窗
                        // const { info: { class_id } } = canvasStore.getState().infoManageRedux;
                        // let { vipType, snum } = resultData.msg;
                        // if((class_id.indexOf('132') === -1 && snum === 0) || (class_id.indexOf('132') > -1 && vipType > 1 && snum === 0)){

                        // 下载次数为零或者没有企业模板权限都弹出居中弹窗
                        const { user } = storeAdapter.getStore({
                            store_name: storeAdapter.store_names.paintOnCanvas,
                        });
                   
                        const enterpriseVip = !(user?.commercialType === 1 || user?.commercialType === 2 || user?.commercialType == 3 || user?.is_firm_vip === 1 );
                        // if (snum === 0 || (info?.is_company_temp === 1 && user?.commercialType === 0 && data.code !== 1 && enterpriseVip)) {

                        if (snum === 0 ) {
                            InfoManageHelper.updateInfoCompanyTemp(is_company_temp)
                            // @ts-ignore
                            const show_share_popup: { set_time: string; click_close_count: number } = new Object();
                            const date = new Date();
                            const load_share_popup = JSON.parse(localStorage.getItem('ue_show_share_popup'));
                            show_share_popup.set_time =
                                date.getDate().toString() + date.getMonth().toString() + date.getFullYear().toString();
                            const many_times_download_flag = Boolean(
                                load_share_popup && show_share_popup.set_time === load_share_popup.set_time,
                            );
                            const windowInfo = {
                                windowContent: (
                                    <DownloadPopup
                                        from={params.from}
                                        isShowCloseInternal={true}
                                        isDownloadZero={true}
                                        key={new Date().getTime()}
                                        className="DownloadPopup"
                                        showFlag={0}
                                        is_over_five_times={is_over_five_times}
                                        // editorRecharge={()=> {
                                        //     this.setState({
                                        //         showVipRecharge: true,
                                        //     }, () => {
                                        //         setTimeout(() => {
                                        //             emitter.emit('popupClose');
                                        //         }, 120);
                                        //     })
                                        // }}
                                    />
                                ),
                                popupWidth: 460,
                                popupHeight: 220,
                                style: {
                                    borderRadius: '4px',
                                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                },

                                popupTitleBarStyle: {
                                    width: 0,
                                    height: 0,
                                    display: 'none',
                                },
                                popupBodyStyle: {
                                    padding: 0,
                                },
                                // popupTitleBarStyle：{
                                //     display:none
                                // }
                            };
                            emitter.emit('popupWindow', windowInfo);
                            if(params.from === 'saveWeChatPublic') {
                                // 保存至公众号限制弹窗展示埋点
                                assetManager.setPv_new(5585);
                            } else if (params.from === 'saveToWeibo') {
                                // 发布到微博限制弹窗展示埋点
                                assetManager.setPv_new(5586);
                            } else if (params.from === 'saveToTikTok') {
                                // 发布到抖音限制弹窗展示埋点
                                assetManager.setPv_new(5587);
                            }
                            // const {user:{vip,created,userPayCount}} = canvasStore.getState().onCanvasPainted;
                            // const  registeredDate = new Date(created).getTime();
                            // const currentDate = new Date().getTime();
                            // const days = Math.floor((currentDate-registeredDate) / (24 * 60 * 60 * 1000));
                            // days>=90 && vip<=1
                            // 注册时间90天且非VIP
                            // 下线
                            // const isShow  = false;
                            if (false) {
                                // let windowInfo = {
                                //             windowContent: <DownloadBootPage key={PreviewAndShare + '_' + new Date().getTime()} fontImageAll={this.newFontImageAll} origin={this.downloadParams ? this.downloadParams.origin : ''} isSingleRecharge={true} snum={this.state.snum} newYearActivity={this.state.newYearActivity} />,
                                //             popupWidth: 'auto',
                                //             popupHeight: 'auto',
                                //             style: {
                                //                 borderRadius: '0',
                                //                 backgroundColor: 'rgba(255, 255, 255, 0)',
                                //                 top: 0,
                                //                 left: 0,
                                //                 bottom: 0,
                                //                 right: 0,
                                //                 padding: 0,
                                //             },
                                //             popupTitleBarStyle: {
                                //                 width: 0,
                                //                 height: 0,
                                //                 display: 'none',
                                //             }
                                // };
                                // emitter.emit('popupWindow', windowInfo);
                                // return;
                            } else {
                                // if (many_times_download_flag && load_share_popup.click_close_count > 1) {
                                if (false) {
                                    // 第二次及之后的下载受限弹框的弹出
                                    emitter.emit(
                                        'PopupUpdateWindowStyle',
                                        {
                                            width: 370,
                                            height: 420,
                                            padding: 0,
                                            background: '#FFFFFF',
                                            'box-shadow':
                                                '0px 0px 8px 0px rgba(0, 0, 0, 0.12), 0px 0px 2px 0px rgba(0, 0, 0, 0.12)',
                                            borderRadius: '8px',
                                        },
                                        true,
                                    );
                                    emitter.emit('DownloadPopupUS', 56);
                                    show_share_popup.click_close_count = ++load_share_popup.click_close_count;
                                } else {
                                    // 第一次下载受限弹框的弹出
                                    // const { info } = storeAdapter.getStore({
                                    //     store_name: storeAdapter.store_names.InfoManage,
                                    // });
                                    // 活动入口展现埋点
                                    assetManager.setPv_new(7930,{additional: {
                                        i4: is_over_five_times
                                    }});
                                    emitter.emit(
                                        'PopupUpdateWindowStyle',
                                        {
                                            width: 400,
                                            height: 330,
                                            padding: '20px',
                                            borderRadius: is_company_temp === 1 ? '14px' : '14px',
                                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                        },
                                        true,
                                    );

                                    if (many_times_download_flag) {
                                        show_share_popup.click_close_count = load_share_popup.click_close_count++;
                                    } else {
                                        show_share_popup.click_close_count = 0;
                                    }
                                    emitter.emit(
                                        'DownloadPopupUS',
                                        3,
                                        rechargePop,
                                        load_share_popup ? load_share_popup.click_close_count : 1,
                                    );
                                }
                                // const { info } = storeAdapter.getStore({
                                //     store_name: storeAdapter.store_names.InfoManage,
                                // });
                                // assetManager.setPagePv_new(
                                //     info?.is_company_temp === 1 ? 4736 :1857,
                                //     '',
                                //     getProps().upicId > 0 ? getProps().upicId : 0,
                                //     '',
                                //     '',
                                //     { s0: load_share_popup ? load_share_popup.click_close_count : 1, i2: 1 },
                                // );
                                assetManager.setPv_new(
                                    1857,
                                    // '', 
                                    // @ts-ignore
                                    // getProps().upicId > 0 ? getProps().upicId : 0,
                                    {
                                        additional:{
                                            s0: load_share_popup ? load_share_popup.click_close_count : 1,
                                            i2: 1,
                                            i0: is_company_temp,
                                            i4: is_over_five_times,
                                        }
                                    }
                                );
                                emitter.emit('pageDouble', 1);
                                localStorage.setItem('ue_show_share_popup', JSON.stringify(show_share_popup));
                                return;
                            }
                        }

                        // if (resultData.msg.isBindPhone == false) {
                        //     emitter.emit('InfoBarPhoneBindPopup', { origin: 'download' });
                        //     return;
                        // }

                        switch (type) {
                            case 'download':
                                th.setState(
                                    {
                                        snum: resultData.msg.snum,
                                        newYearActivity: resultData.msg.newYearActivity,
                                        is_vip_templ: resultData.msg.is_vip_templ,
                                        vipType: resultData.msg.vipType,
                                        is_old_vip: resultData.msg.is_old_vip,
                                        is_personal_com_vip: resultData.msg.is_personal_com_vip,
                                        governmentTempl: resultData.msg.governmentTempl,
                                        show_but: resultData.msg.show_but,
                                        unit: resultData.msg.unit,
                                        is_ppt_vip: resultData.msg.is_ppt_vip

                                    },
                                    () => {
                                        this.download_params = {
                                            from: params.from,
                                        };
                                        th.download();
                                    },
                                );
                                break;
                            case 'weChat':
                                th.setState(
                                    {
                                        snum: resultData.msg.snum,
                                        newYearActivity: resultData.msg.newYearActivity,
                                        is_vip_templ: resultData.msg.is_vip_templ,
                                        vipType: resultData.msg.vipType,
                                    },
                                    () => {
                                        th.uploadWebChatOpenPlatformClickEvent();
                                    },
                                );
                                break;
                        }
                    });
                    
                }
            });
        });
    }
    clickshowTeamSaveBtn(from: string, e: MouseEvent): void{
        if(env.teamTemplate || isUeTeam){
            assetManager.getTeamDetailInfomation().then(data => {
                data.json().then(resultData => {
                    if( resultData.stat == 1 && resultData.msg.length === 0){
                        // 没有团队提示框
                        const windowInfo = {
                            windowContent: (
                                <div className="saveTipArea">
                                    <p className="ptext1">当前模板不是您的作品，暂不能保存</p>
                                </div>
                            ),
                        };
                        emitter.emit('PromptBox', windowInfo);
                        if (this.PromptBoxTime1) {
                            clearTimeout(this.PromptBoxTime1);
                        }
                        this.PromptBoxTime1 = setTimeout(() => {
                            emitter.emit('PromptBoxClose');
                        }, 3000)
                        return
                    }
                    this.showTeamSavePop(from, e,'template')
                });
            });
        }else{
            this.showTeamSavePop(from, e,'template')
        }
    }
    /**
     * 
     * @param from 
     * @param e 
     * @param saveType 区分保存到团队设计和保存到案例库 teamDesign为团队设计
     */
    showTeamSavePop(from: string, e: MouseEvent, saveType = 'teamDesign'): void {
        // emitter.emit('autoSaveTempl', '', '', {handleSave: true});
        let style;
        if (from === 'file') {
            assetManager.setPv_new(3720);
            style = {
                borderRadius: '4px',
                left: '50%',
                top: '50%',
                transform: 'translate(-50%, -50%);',
                width: '369px',
                height: '401px',
                marginLeft: '-185px',
                marginTop: '-200px',
                padding: '0',
            };
        } else {
            style = {
                borderRadius: '4px',
                right: '0',
                top: '64px',
                // transform: 'translate(-50%, -50%);',
                width: '369px',
                height: '401px',
                padding: '0',
                left: 'auto',
            };
        }
        if(env.teamTemplate || isUeTeam){
            style = {
                borderRadius: '4px',
                top: '64px',
                // transform: 'translate(-50%, -50%);',
                width: '369px',
                height: '401px',
                padding: '0',
            };
        }
        if(saveType === 'teamDesign'){
            style.height= "300px"
        }
        const windowInfo = {
            windowContent: <SaveTeamTemplePopup saveType={saveType} confirmCallback={(e:React.MouseEvent<HTMLDivElement, MouseEvent>) => this.saveClickEvent(e, 'save_team')} teamSelectCallback={this.popupTeamSelectCallback.bind(this)}/>,
            popupWidth: 460,
            popupHeight: 190,
            popupTitleBarStyle: {
                display: 'none',
            },
            popupBodyStyle: {
                padding: 0,
            },
            style: style,
        };
        emitter.emit('popupWindow', windowInfo);
        const { info } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage,
        });
        const { class_id = [] } = info || {};
        if(class_id.includes('1') || class_id.includes('7')){
            this.setState({
                isShowTeam: false,
                isShowClickTeam: false,
            })
        }
        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
        if (from === 'download') {
            assetManager.setPv_new(6575);
        }
    }

    tucaoActivitiesAreaMouseenterEvent(e: MouseEvent): void {
        this.tucaoActivitiesAreaTimeout = setTimeout(() => {
            assetManager.setPv_new(651);
        }, 500);

        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }

    vipExtensionAreaMouseenterEvent(e: React.MouseEvent): void {
        this.vipExtensionTimeout = setTimeout(() => {
            if (this.state.is_person_business_vip) {
                assetManager.setPv_new(6652);
            } else {
                assetManager.setPv_new(103);
            }
        }, 500);
        this.closeBy()
        e.stopPropagation();
        e?.nativeEvent?.stopPropagation();
    }

    vipExtensionAreaMouseleaveEvent(e:React.MouseEvent): void {
        if (this.vipExtensionTimeout) {
            clearTimeout(this.vipExtensionTimeout);
        } 
        const {info} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage,
        }); 
        //接着定时器
        if(info?.is_company_temp === 0 && this.state.is_person_business_vip === false ){
            this.openThemBy()
        }
        e.stopPropagation();
        e?.nativeEvent?.stopPropagation();
    }

    /**
     * 鼠标进入导航vip
     * @param e 
     * @param type 所选类型 
     */
    vipSlideBoxMouseenterEvent(type: string): void{
        const {info} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage,
        }); 
        this.vipSlideBoxTimeout = setTimeout(() => {
            if(info?.is_company_temp === 1){
                assetManager.setPv_new(4724, {additional: {s0: type}});
            }else{
                assetManager.setPv_new(4584, {additional: {s0: type}});

            }
        }, 500);
    }

    /**
     * 鼠标离开导航vip
     * @param e 
     */
    vipSlideBoxMouseleaveEvent(e: React.MouseEvent): void{
        if( this.vipSlideBoxTimeout ){
            clearTimeout(this.vipSlideBoxTimeout);
        }
    }

    kownBtnClickEvent(e: MouseEvent): void {
        this.setState({
            isFileTemplTipAreaShow: false,
        });

        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }

    updateState(): void {
        const th = this;
        this.InfoBarUpdateState = emitter.addListener('InfoBarUpdateState', () => {
            th.setState({});
            // th.forceUpdate();
        });
    }

    myDesignClickEvent(from: string, e: React.MouseEvent): void {
        if (from === 'file') {
            assetManager.setPv_new(3719, {
                additional: {},
            });
        } else {
            assetManager.setPv_new(189, {
                additional: {},
            });
        }

        e?.stopPropagation();
        e?.nativeEvent.stopPropagation();
    }

    delGroupInfoClickEvent(e: MouseEvent): void {
        EditorLogic.delGroupInfoAll();
        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }
    serchtemplateBtnClickEvent(): void {
        // 编辑器点击搜索
        emitter.emit('templatePanelMoreBtnClick', '', { serch: true, seachNowInput: this.nowInput });
        assetManager.setPv_new(992, {
            additional: {},
        });
        this.nowInput = '';
        // @ts-ignore
        this.setState();
    }

    newTemplateBtnClickEvent(params: { type: string } = { type: undefined }, e: MouseEvent): void {
        // let {canvas , user } = storeAdapter.getStore({
        //     store_name: storeAdapter.store_names.pa,
        // });
        // let {info} = storeAdapter.getStore({
        //     store_name: storeAdapter.store_names.InfoManage,
        // }); // c
        // let urlProps = getProps() ,
        // _upicId = urlProps['upicId'] || info.id ,
        // _picId = urlProps['picId'] || info.last_templ_id;
        /* if(user.userId <= 0){ // 未登录
            window.open("//ue.818ps.com/?notShowBigTemplate=1&w=" + canvas.width + "&h=" + canvas.height + `&picId=${_picId}&preK1=${info.kid_1}`);
        }else{
            assetManager.copyTemplate(_upicId).then((res)=>{
                res.json().then(data=>{
                    const { stat , msg } = data;
                    if(stat == 1){
                        assetManager.setPv_new(1973,{additional:{templ_id:msg.id}})
                        window.open("//ue.818ps.com/?notShowBigTemplate=1&w=" + canvas.width + "&h=" + canvas.height + `&upicId=${msg.id}&picId=${_picId}&preK1=${info.kid_1}`);
                    }
                })
            })
        } */
        // window.open("//ue.818ps.com/?notShowBigTemplate=1&w=" + canvas.width + "&h=" + canvas.height );
        // if (params.type === 'main') {
        if (params.type === 'container') {
            assetManager.setPv_new(6307, {
                additional: {},
            });
        } else {
            assetManager.setPv_new(2973, {
                additional: {},
            });
        }
        // } else {
        //     assetManager.setPv_new(231, {
        //         additional: {},
        //     });
        // }

        // emitter.emit('templatePanelMoreBtnClick','', {serch: false,fromType : 'new'});

        // @ts-ignore
        this.moreBtnClickEvent('', { serch: false, fromType: 'new' });
        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }

    moreBtnClickEvent(kw: string, type = ''): void {
        this.moreBtn = this.moreBtn >= 0 ? this.moreBtn + 1 : 0;
        const w = 1500,
            h = 940;

        const windowInfo = {
            windowContent: (
                <BigPopupWindow
                    fromWhere={type}
                    width={w}
                    height={h}
                    kw={kw}
                    type="template"
                    key={'moreBtn' + this.moreBtn + '-'}
                />
            ),
            style: {
                padding: 0,
                borderRadius: '4px',
                top: '20px',
                left: '90px',
                bottom: '20px',
                right: '90px',
            },
            popupTitleBarStyle: {
                height: 0,
            },
            popupBodyStyle: {
                padding: 0,
            },
        };
        emitter.emit('popupWindow', windowInfo);
    }

    /**
     * 创建副本
     * @param e
     */
    createCopyTemplateClickEvent(): void {
        const props = getProps();
        if (props['upicId'] > 0) window.open(`https://818ps.com/api/copy-temple?upicid=` + props['upicId']);
        assetManager.setPv_new(2974, {
            additional: {},
        });
        return;
    }

    topAreaMoreBtnClickEvent(e: React.MouseEvent): void {
        assetManager.setPv_new(199, {
            additional: {},
        });

        e.stopPropagation();
        e?.nativeEvent.stopPropagation();
    }

    notLoginSaveTipClickEvent(e: React.MouseEvent): void {
        this.setState({ isNotLoginSaveTipShow: 0 });

        e.stopPropagation();
        e?.nativeEvent.stopPropagation();
    }

    tucaoActivitiesBtn2ClickEvent(): void {
        assetManager.setPv_new(649);
    }

    userTemplateItemClickEvent(e: React.MouseEvent): void {
        assetManager.setPv_new(210, {
            additional: {},
        });

        e.stopPropagation();
        e?.nativeEvent.stopPropagation();
    }

    componentDidMount(): void {
        /*        setTimeout(()=>{
            let {user} = canvasStore.getState().onCanvasPainted;
            if(user.userId<=0){
                this.setState({isNotLoginSaveTipShow:true});
            }
        },30000)*/
        if(window.location.href.includes('uploadWeChatPublic')){
            if(window.location.href.includes('new_bind=1')){
                assetManager.setPv_new(5565)
            } else {
                assetManager.setPv_new(5566)
            }
            emitter.emit('popupWindow',this.returnPopupWindowContent());
        }

        if(window.location.href.includes('saveToWeibo')){
            if(window.location.href.includes('new_bind=1')){
                assetManager.setPv_new(5567)
            } else {
                assetManager.setPv_new(5568)
            }
            emitter.emit('popupWindow',this.returnSavePop('weibo'));
        }

        if(window.location.href.includes('saveToTikTok')){
            if(window.location.href.includes('new_bind=1')){
                assetManager.setPv_new(5569)
            } else {
                assetManager.setPv_new(5570)
            }
            emitter.emit('popupWindow',this.returnSavePop('tikTok'));
        }
        // @ts-ignore
        addEventListener(window, 'click', () => {
            this.setState({
                isShowTeam: false,
                isShowTeamArea: false,
                isShowClickTeam: false
            });
        });
        const th = this;

        this.showStoreOurWebsiteDialogEmitter = emitter.addListener('ShowStoreOurWebsiteDialog', () => {
            assetManager.setPv_new(2583);
            th.setState(
                {
                    showStoreOurWebsite: true,
                },
                th.websiteLastSecondTimeout,
            );
        });
        this.getPicIdEmitter = emitter.addListener('getPicId', (picId: string) => {
            th.setState({
                picId: picId,
            });
        });
        this.popupClose = emitter.addListener('popupForceRefresh_modal', () => {
            const windowInfo = {
                windowContent: <ForceRefreshModal key={'_' + new Date().getTime()} />,
                popupWidth: 348,
                popupHeight: 244,
                style: {
                    borderRadius: 8,
                    backgroundColor: 'rgba(255, 255, 255, 1)',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%,-50%)',
                    padding: 0,
                },
                popupBodyStyle: {
                    padding: 0,
                },
                popupTitleBarStyle: {
                    width: 0,
                    height: 0,
                    display: 'none',
                },
            };
            UserCheck.closeCheck();
            emitter.emit('popupWindow', windowInfo);
        });

        setTimeout(() => {
            this.loadFonts();
        }, 300);

        try {
            this.eleInners = document.querySelector('.SlideBox');
            this.liLists = this.eleInners.querySelectorAll('.SlideBox li');
            this.liWidth = this.liLists[0].offsetWidth;
            this.index = 0;

            const {info} = storeAdapter.getStore({
                store_name: storeAdapter.store_names.InfoManage,
            }); 
            //接着定时器
            if(info?.is_company_temp === 0 && this.state.is_person_business_vip === false){
                this.wheelPlayTimer = setInterval(()=>{
                    this.autoplay(this.eleInners, this.liLists, this.liWidth)
                },2000)
            }
        } catch (error) {
            
        }
        
        this.getIsPersonBusinessVip()
    }

    /** 
     *动画
     * @param obj 元素
     * @param targetPlace 元素偏移量
     */
     animate(obj: any, targetPlace: number): void{
        clearInterval(obj.timer);
        obj.timer = setInterval(() => {
            //判断移动的位置是向左移动还是向右移动
            const speed = obj.offsetLeft > targetPlace ? -15 : 15;
            const result = targetPlace - obj.offsetLeft;
            //只要移动的差值大于speed，那么就一直让obj.style.left 改变
            if (Math.abs(result) > Math.abs(speed)) {
                obj.style.left = obj.offsetLeft + speed + 'px'
            } else {
                //否则如果已经移动的，obj.offsetleft与要移动的位置十分接近了，
                obj.style.left = targetPlace + 'px';
                clearInterval(obj.timer);
            }
        }, 20)
    }

    /** 
     *自动播放
     * @param eleInners 偏移元素
     * @param liLists 轮播元素列表
     * @param liWidth 每一个轮播元素的宽度
     */
    autoplay(eleInners: any, liLists: any, liWidth: number): void{
        this.index++;
        if (this.index > liLists.length - 1) {
            eleInners.style.left = 0;//赶快跳回去
            this.index = 1;//找到第二张图片
        }
        this.animate(eleInners, -this.index * liWidth);
    } 

    /** 
     *开启轮播
     */
    openThemBy(): void{
        this.wheelPlayTimer = setInterval(()=>{
            this.autoplay(this.eleInners, this.liLists, this.liWidth)
        },2000)
    }

    /**
     * 关闭轮播
     */
    closeBy(): void{
        if(this.wheelPlayTimer){
            clearInterval(this.wheelPlayTimer)
        }
    }

    /**
     * 网站收藏弹窗倒计时
     */
    websiteLastSecondTimeout(): void {
        const th = this,
            second = this.state.websiteLastSecond;
        if (second === 0) {
            th.setState({
                showStoreOurWebsite: false,
                websiteLastSecond: 5,
            });
            clearTimeout(this.websiteLastSecondTimer);
            return;
        }
        this.websiteLastSecondTimer = setTimeout(() => {
            th.setState(
                {
                    websiteLastSecond: second - 1,
                },
                th.websiteLastSecondTimeout,
            );
        }, 1000);
    }

    componentWillUnmount(): void {
        this.InfoBarUpdateState.remove();
        this.InfoBarclearIntervalEmitter.remove();
        this.saveKeyDownEventEmitter.remove();
        this.autoSaveTemplListenerEmitter.remove();
        this.saveOperationsEmitter.remove();
        this.bindWeChatPlatformEmitter.remove();
        this.uploadWebChatPlatformEmitter.remove();
        this.phoneBindEmitter.remove();
        this.updateFileTemplTipEmitter.remove();
        this.infoBarDownloadEmitter.remove();
        this.downloadFileEmitteEmitter.remove();
        this.cancelShareEmitter.remove();
        this.closeToolTipKnow.remove();
        this.satisfactionSurveyEmitter?.remove();
    }

    /**
     * 搜索回车监听
     */
    keyWordKeyDownEvent(e: KeyboardEvent): void {
        if (e.keyCode == 13) {
            this.serchtemplateBtnClickEvent();
        }
    }
    /**
     * 搜索（输入框）
     */
    hueInputChangeEvent(e: React.ChangeEvent<HTMLInputElement>): void {
        this.nowInput = e.target.value;
        this.setState({});
    }
    /**
     * 搜索（输入框）
     */
    inputFocusEvent(type: string, e: React.ChangeEvent<HTMLInputElement>): void {
        // @ts-ignore
        this.nowInput = e.target.value;
        this.setState({});
        assetManager.setPv_new(1020, {
            additional: {},
        });
    }
    /**
     * 搜索（输入框）
     */
    inputBlurEvent(e: React.ChangeEvent<HTMLInputElement>): void {
        this.setState({});
    }

    /**
     * 我的设计处 悬浮框未登录时点击
     */
    myDesignUnloginClick(type: string, e: React.MouseEvent): void {
        emitter.emit('LoginPanelShow', 'myDesign', () => {
            window.open(ipsApi('/dash/userdsn'));
        });

        assetManager.setPv_new(type === 'btn' ? 1969 : 1970);

        e.stopPropagation();
        e?.nativeEvent.stopPropagation();
    }
    /**
     * 我的设计处 未登录时点击
     */
    myDesignBtnUnloginClick(type: string, e: MouseEvent): void {
        emitter.emit('LoginPanelShow', 'myDesign', () => {
            window.open(ipsApi('/dash/userdsn'));
        });

        assetManager.setPv_new(189, {
            additional: {},
        });
    }

    /**
     *
     * @param {*} type 类型
     * @param {*} url 跳转的URL
     * @param {*} e
     */
    businessBtnGotoRight(type = '', url = '', e: React.MouseEvent<HTMLElement>): void {
        const { from } = e.currentTarget.dataset
        const { info,vipTemplateNumMap: {newVipModelVersion = 0} } = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
            store_name: storeAdapter.store_names.InfoManage,
        });
        let jumpUrl = ''
        if (from === 'vipad') {
            if (this.state.is_person_business_vip) {
                jumpUrl = 'https://818ps.com/dash/firm-intro?origin=ue-vipad-infobar-vip-hover-enreprise-new';
            } else if (info?.is_company_temp === 1 || env.teamTemplate || isUeTeam) {
                jumpUrl =
                    'https://818ps.com/dash/firm-intro?origin=ue-vipad-temp-company-use&route_id=16328996965437&route=1,&after_route=1';
            } else if (info?.is_company_temp === 2) {
                // 'https://818ps.com/dash/firm-intro?origin=ue-vipad-temp-commercial-use&route_id=16328996965437&route=1,&after_route=1',
                jumpUrl = newVipModelVersion
                    ? 'https://818ps.com/dash/vip-spec-commercial-new?origin=ue-vipad-temp-commercial-use&classify=1'
                    : 'https://818ps.com/dash/vip-spec-commercial?origin=ue-vipad-temp-commercial-use&classify=1';
            } else {
                jumpUrl = newVipModelVersion
                    ? 'https://818ps.com/dash/vip-spec-commercial-new?origin=ue-vipad-temp-commercial-use&classify=1'
                    : 'https://818ps.com/dash/vip-spec-non-commercial?classify=1&origin=ue-vipad-not-type-1-and-2&route_id=16007403774149&route=1,86&after_route=1_86';
            }
            window.open(jumpUrl);
        }
        if (from === 'copyright' || from === 'copyright-tip') {
            if (this.state.is_person_business_vip) {
                window.open('https://818ps.com/dash/firm-intro?origin=ue-copyright-infobar-copyright-hover')
            } else if (info?.is_company_temp === 1 || env.teamTemplate || isUeTeam) {
                window.open(
                    'https://818ps.com/dash/firm-intro?origin=ue-copyright-temp-company-use&route_id=16328996965437&route=1,&after_route=1',
                );
            } else if (info?.is_company_temp === 2) {
                window.open(
                    'https://818ps.com/dash/firm-intro?origin=ue-copyright-temp-commercial-use&route_id=16328996965437&route=1,&after_route=1',
                );
            } else {
                window.open(
                    "https://818ps.com/dash/firm-intro?origin=ue-copyright-not-type-1-and-2"
                )
            }
        }
        assetManager.setPv_new(type == 'btn' ? 2025 : 2026);
        e.stopPropagation();
    }

    onDownloadMouseEnterClickEvent(e: React.MouseEvent): void {
        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const {info} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage,
        }); 
        if (!this.timer1) {
            this.timer1 = setTimeout(() => {
                assetManager.setPv_new(3868, {
                    additional: {},
                });
            }, 1000);
        }
        this.loadFonts();
        const urlProps = IPSConfig.getProps();
        if( urlProps && urlProps.w && urlProps.h ){
            this.getDownloadNum(true)
        }
        if((this.state.snum ==="海量" || Number(this.state.snum) > 0) &&  this.state.vipType <= 1 && !this.state.is_vip_templ){
            assetManager.setPv_new( info?.is_company_temp === 1 ? 4727 : 3134, {additional: {}});
        }
        if (/downloadTemplTipAreaNew/.test((e?.target as HTMLElement)?.className)) {
            this.onDetailDownType && ((this.onDetailDownType as HTMLDivElement).style.display = 'none')
        } else {
            this.onDetailDownType && ((this.onDetailDownType as HTMLDivElement).style.display = 'block')
        }
    }

    onDownloadMouseLeaveClickEvent(e:any): void {
        if (this.timer1) {
            this.timer1 = undefined;
        }
        this.onDetailDownType && ((this.onDetailDownType as HTMLDivElement).style.display = 'none')
    }

    onPreViewMouseEnterClickEvent(): void {
        if (!this.timer2) {
            this.timer2 = setTimeout(() => {
                assetManager.setPv_new(3869, {
                    additional: {},
                });
            }, 1000);
        }
    }

    onPreviewMouseLeaveClickEvent(): void {
        if (this.timer2) {
            this.timer2 = undefined;
        }
    }

    onTeamMouseEnterClickEvent(e:any): void {
        const pullDownDom = document.getElementById('download-pulldown-bg');
        const abTestStyleSwitchC = document.getElementById('abTestStyleSwitchC');
        const isInfoBarABTestStyle = document.getElementById('isInfoBarABTestStyle');

        if (pullDownDom) {
            pullDownDom.style.visibility = 'hidden';
        }
        if (abTestStyleSwitchC) {
            abTestStyleSwitchC.style.visibility = 'hidden';
        }
        if (isInfoBarABTestStyle) {
            isInfoBarABTestStyle.style.visibility = 'hidden';
        }
        this.onDetailDownType && ((this.onDetailDownType as HTMLDivElement).style.display = 'none')
        if (!this.timer3) {
            this.timer3 = setTimeout(() => {
                assetManager.setPv_new(3875, {
                    additional: {},
                });
            }, 1000);
        }
        if(e.target.className.includes('downloadTemplTipAreaNew')){
            this.setState({
                isShowTeam: true
            })
            assetManager.setPv_new(5137);
        }
        clearTimeout(downloadDropDownTimer);
    }

    onTeamMouseLeaveClickEvent(e:any): void {
        if (this.timer3) {
            this.timer3 = undefined;
        }
        const pullDownDom = document.getElementById('download-pulldown-bg');
        const abTestStyleSwitchC = document.getElementById('abTestStyleSwitchC');
        const isInfoBarABTestStyle = document.getElementById('isInfoBarABTestStyle');

        if (pullDownDom) {
            pullDownDom.style.visibility = null;
        }
        if (abTestStyleSwitchC) {
            abTestStyleSwitchC.style.visibility = null;
        }
        if (isInfoBarABTestStyle) {
            isInfoBarABTestStyle.style.visibility = null;
        }

        downloadDropDownTimer = setTimeout(() => {
            this.setState({
                isShowTeam: false,
            })
            clearTimeout(downloadDropDownTimer);
        }, 300);
    }

    /**
     * 阻止冒泡
     * @param {*} e
     */
    stopPropagation(e: MouseEvent | React.MouseEvent): void {
        e.stopPropagation();
    }

    /**
     * 获取下载悬浮框用户选择
     */
    getDownloadUserSection() {
        const selection_obj = JSON.parse(localStorage.getItem('ue_downloadSelection')) || {};
        if (selection_obj.quality) {
            this.setState({
                dpi: selection_obj.quality,
            });
        }
        if (selection_obj.fileType) {
            this.setState({
                fileType: selection_obj.fileType,
            });
        }
        if (selection_obj.fileType) {
            this.setState({
                isPrint: selection_obj.isPrint,
            });
        }
        // selection_obj.fileType = fileType
        // localStorage.setItem("ue_downloadSelection", JSON.stringify(selection_obj));
    }

    /**
     * 加载下载次数
     */
    getDownloadNum(isLogin: boolean): void {
        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if ((!user || user.userId === 0 || this.state.isloading) && !isLogin) {
            return;
        }
        this.setState({
            isloading: true,
        });
        this.loadFonts();
        assetManager.getDownloadNumInfo().then((data) => {
            data.json().then((resultData) => {
                if (resultData.stat === 1) {
                    this.setState({
                        snum: resultData.msg.snum,
                        newYearActivity: resultData.msg.newYearActivity,
                        is_vip_templ: resultData.msg.is_vip_templ,
                        vipType: resultData.msg.vipType,
                        governmentTempl: resultData.msg.governmentTempl,
                        is_old_vip: resultData.msg.is_old_vip,
                        is_personal_com_vip: resultData.msg.is_personal_com_vip,
                        is_ppt_vip: resultData.msg.is_ppt_vip
                    });
                }
                this.setState({
                    isloading: false,
                });
            });
        });
    }

    loadFonts(): void {
        const { user, work, pageInfo } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            }),
            page = work.pages[pageInfo.pageNow];
        // 下载处理魂网字体数量
        const currNewFont = [];
        let cueeNewImage = 0;
        const vipFont = [];
        // 全部的page数据
        for (const pagesDate of work.pages) {
            for (const item of pagesDate.assets) {
                if (
                    item.meta.type == 'text' &&
                    this.zhFontDate.indexOf(this.fontNameValueList[item.attribute.fontFamily]) > -1
                ) {
                    // 只处理魂网字体
                    if (currNewFont.indexOf(this.fontNameValueList[item.attribute.fontFamily]) == -1) {
                        currNewFont.push(this.fontNameValueList[item.attribute.fontFamily]);
                    }
                }
                if (item.meta.type == 'text' && this.vipFont.indexOf(item.attribute.fontFamily) > -1) {
                    vipFont.push(item.attribute.fontFamily);
                }
                // 下载处理摄图网图片数量
                if (item.attribute.owner && item.attribute.owner == '-1') {
                    // 'owner==-1 为摄图网版权图片'
                    cueeNewImage += 1;
                }
            }
        }
        this.newFontImageAll = {
            currNewFont: currNewFont,
            cueeNewImage: cueeNewImage,
            vipFont: vipFont,
        };
    }

    /**
     * 下载文件类型选择(点击事件)
     * @param fileType
     * @param e
     */
    fileTypeItemClickEvent(fileType: string, e: React.MouseEvent): void {
        if (fileType == 'ppt') {
            this.setState({
                fileType: fileType,
                isPrint: false,
                isVideo: false,
            });
        } else {
            this.setState({
                fileType: fileType,
                isVideo: false,
            });
        }

        if (fileType === 'pdf') {
            assetManager.setPv_new(3234, { additional: {} });
        }

        assetManager.setPv_new(3863, { additional: { s0: fileType } });

        const selection_obj = JSON.parse(localStorage.getItem('ue_downloadSelection')) || {};
        selection_obj.fileType = fileType;
        localStorage.setItem('ue_downloadSelection', JSON.stringify(selection_obj));

        e.stopPropagation();
        e?.nativeEvent.stopPropagation();
    }

    enterSaveTeam(e: React.MouseEvent): void {
        const pullDownDom = document.getElementById('download-pulldown-bg');
        const abTestStyleSwitchC = document.getElementById('abTestStyleSwitchC');
        const isInfoBarABTestStyle = document.getElementById('isInfoBarABTestStyle');

        if (pullDownDom) {
            pullDownDom.style.visibility = 'hidden';
        }
        if (abTestStyleSwitchC) {
            abTestStyleSwitchC.style.visibility = 'hidden';
        }
        if (isInfoBarABTestStyle) {
            isInfoBarABTestStyle.style.visibility = 'hidden';
        }
        e.stopPropagation();
        e?.nativeEvent.stopPropagation();
        assetManager.setPv_new(6574);
    }

    leaveSaveTeam(noStop: boolean,e: React.MouseEvent): void {
        const pullDownDom = document.getElementById('download-pulldown-bg');
        const abTestStyleSwitchC = document.getElementById('abTestStyleSwitchC');
        const isInfoBarABTestStyle = document.getElementById('isInfoBarABTestStyle');

        if (pullDownDom) {
            pullDownDom.style.visibility = null;
        }
        if (abTestStyleSwitchC) {
            abTestStyleSwitchC.style.visibility = null;
        }
        if (isInfoBarABTestStyle) {
            isInfoBarABTestStyle.style.visibility = null;
        }
        // this.setState({
        //     isShowTeam: false,
        // })
        if (!noStop) {
            e.stopPropagation();
            e?.nativeEvent.stopPropagation();
        }
    }

    printControlClickEvent(e: React.MouseEvent): void {
        const { isPrint } = this.state;
        if (!isPrint) {
            assetManager.setPv_new(269, { additional: {} });
        }
        this.setState({
            isPrint: !isPrint,
        });

        const selection_obj = JSON.parse(localStorage.getItem('ue_downloadSelection')) || {};
        selection_obj.isPrint = !isPrint;
        localStorage.setItem('ue_downloadSelection', JSON.stringify(selection_obj));

        e.stopPropagation();
        e?.nativeEvent.stopPropagation();
    }

    /**
     * 开始下载(调接口)
     * @param e
     */
    beginDownload(origin = '', e: React.MouseEvent): void {
        emitter.emit('closePopupBtn', true);
        const { work, createTime } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const pageNum = work.pages.filter((item: { assets: string | any[] }) => item.assets.length != 0);
        if (pageNum.length > 1) {            
            assetManager.setPv_new(4310);
        }
        assetManager.setPv_new(3149, { additional: { s0: 'downloadBtn' } });
        // canvasStore.dispatch(infoManage('ADD_DOWNLOAD_TYPE', {fileType:this.state.fileType}));
        InfoManageHelper.addDownloadType(this.state.fileType);
        emitter.emit('InfoBarDownload', {
            callbackProps: {
                downloadPicProps: {
                    fileType: this.state.fileType,
                    isPrint: this.state.isPrint,
                    dpi: this.state.dpi,
                    origin: this.props.origin,
                },
                origin: origin,
            },
        });

        // 新增主要用来区分点击手机下载还是点击按钮下载
        if (origin == 'phone') {
            assetManager.setPv_new(3866, {
                additional: {},
            });
        } else {
            assetManager.setPv_new(3867, {
                additional: {},
            });
        }

        // let {work,createTime} = canvasStore.getState().onCanvasPainted;
        let assetIDs = '';
        const specificWordIdArr: string[] = [],
            specificWordIdKwArr: string[] = [],
            specificWordIdArrNowAdd: string[] = [],
            specificWordIdArrNowKwAdd: string[] = [],
            word3DArr: string[] = [],
            word3DArrNowAdd: string[] = [],
            copyWritingID: string[] = [],
            copyWritingIDNowAdd: string[] = [],
            groupWordID: string[] = [],
            groupWordIDNowAdd: string[] = [],
            SVGID: string[] = [],
            SVGIDNowAdd: string[] = [],
            wordArtArr: string[] = [],
            wordArtArrNowAdd: string[] = [],
            emojiArr: string[] = [],
            emojiArrNowAdd: string[] = [],
            pngArr: string[] = [],
            pngArrNowAdd: string[] = [],
            backgroundArr: string[] = [],
            backgroundArrNowAdd: string[] = [],
            picArr: string[] = [],
            picArrNowAdd: string[] = [],
            photographyArr: string[] = [],
            photographyNowAdd: string[] = [],
            personArr: string[] = [],
            personNowAdd: string[] = [],
            uploadFileArr: string[] = [],
            uploadFileArrNowAdd: any = [],
            fliterArr: string[] = [],
            fliterArrNowAdd: string[] = [],
            imageEffectsArr: number[] = [],
            imageEffectsArrNowAdd: number[] = [],
            frameArr: string[] = [],
            frameArrNowAdd: string[] = [],
            containerArr: string[] = [],
            containerArrNowAdd: string[] = [];

        const groupWordNameArr: string[] = [];
        const tabIdArr: string[] = [];

        work.pages.map((item: { assets: any[] }) => {
            item.assets.map((subItem) => {
                if (subItem.meta.groupWordID) {
                    groupWordNameArr.push(subItem.meta.group);
                }
            });
        });
        work.pages.map(
            (item: {
                assets: ({
                    meta: {
                        group: any;
                        groupWordID: any;
                        addOrigin: string;
                        type: string;
                        rt_isNowAdd: any;
                        rt_searchWord: string;
                        rt_tagId: any;
                        rt_ratioId: any;
                        origin: string;
                    };
                    attribute: {
                        effect: string;
                        fontFamily: any;
                        rt_isNowAdd: any;
                        tabId: any;
                        effectVariant3D: { resId: any };
                        resId: string;
                        filters: { resId: any };
                        imageEffects: { resId: number };
                    };
                }& IAsset)[];
            }) => {
                item.assets.map(
                    (subItem) => {
                        if (groupWordNameArr.includes(subItem.meta.group) && !subItem.meta.groupWordID) return;

                        if (subItem.attribute.effect) {
                            specificWordIdArr.push(subItem.attribute.effect.split('@')[0]);
                            specificWordIdKwArr.push(subItem.attribute.fontFamily);
                            if (subItem.attribute.rt_isNowAdd) {
                                specificWordIdArrNowAdd.push(subItem.attribute.effect.split('@')[0]);
                                specificWordIdArrNowKwAdd.push(subItem.attribute.fontFamily);
                            }
                        }
                        if (subItem.attribute.tabId) {
                            tabIdArr.push(subItem.attribute.tabId);
                        }
                        if (subItem.attribute.effectVariant3D && subItem.attribute.effectVariant3D.resId) {
                            word3DArr.push(subItem.attribute.effectVariant3D.resId);
                            specificWordIdKwArr.push(subItem.attribute.fontFamily);
                            if (subItem.attribute.rt_isNowAdd) {
                                word3DArrNowAdd.push(subItem.attribute.effectVariant3D.resId);
                                specificWordIdArrNowKwAdd.push(subItem.attribute.fontFamily);
                            }
                        }
                        if (subItem.meta.groupWordID && subItem.meta.addOrigin === 'Copywriting') {
                            copyWritingID.push(subItem.meta.groupWordID);
                            if (subItem.attribute.rt_isNowAdd) {
                                copyWritingIDNowAdd.push(subItem.meta.groupWordID);
                            }
                        }
                        if (subItem.meta.groupWordID && !subItem.meta.addOrigin) {
                            groupWordID.push(subItem.meta.groupWordID);
                            if (subItem.attribute.rt_isNowAdd) {
                                groupWordIDNowAdd.push(subItem.meta.groupWordID);
                            }
                        }
                        if (subItem.meta.type == 'SVG') {
                            SVGID.push(subItem.attribute.resId);

                            if (subItem.attribute.rt_isNowAdd) {
                                SVGIDNowAdd.push(subItem.attribute.resId);
                            }
                        }
                        if (subItem.meta.addOrigin == 'wordArt') {
                            wordArtArr.push(
                                JSON.stringify({
                                    id: subItem.attribute.resId,
                                }),
                            );
                            if (subItem.meta.rt_isNowAdd) {
                                wordArtArrNowAdd.push(
                                    JSON.stringify({
                                        id: subItem.attribute.resId,
                                        searchWord: encodeURI(subItem.meta.rt_searchWord),
                                    }),
                                );
                            }
                        }
                        if (subItem.meta.addOrigin == 'emoji') {
                            emojiArr.push(
                                JSON.stringify({
                                    id: subItem.attribute.resId,
                                }),
                            );
                            if (subItem.meta.rt_isNowAdd) {
                                emojiArrNowAdd.push(
                                    JSON.stringify({
                                        id: subItem.attribute.resId,
                                        searchWord: encodeURI(subItem.meta.rt_searchWord),
                                    }),
                                );
                            }
                        }

                        if (subItem.meta.addOrigin == 'png') {
                            pngArr.push(
                                JSON.stringify({
                                    id: subItem.attribute.resId,
                                }),
                            );
                            if (subItem.meta.rt_isNowAdd) {
                                pngArrNowAdd.push(
                                    JSON.stringify({
                                        id: subItem.attribute.resId,
                                        searchWord: encodeURI(subItem.meta.rt_searchWord),
                                        tagId: subItem.meta.rt_tagId,
                                    }),
                                );
                            }
                        }

                        if (subItem.meta.addOrigin == 'photography') {
                            photographyArr.push(
                                JSON.stringify({
                                    id: subItem.attribute.resId,
                                }),
                            );
                            if (subItem.meta.rt_isNowAdd) {
                                photographyNowAdd.push(
                                    JSON.stringify({
                                        id: subItem.attribute.resId,
                                        searchWord: encodeURI(subItem.meta.rt_searchWord),
                                        tagId: subItem.meta.rt_tagId,
                                    }),
                                );
                            }
                        }
                        if (subItem.meta.addOrigin == 'person') {
                            personArr.push(
                                JSON.stringify({
                                    id: subItem.attribute.resId,
                                }),
                            );
                            if (subItem.meta.rt_isNowAdd) {
                                personNowAdd.push(
                                    JSON.stringify({
                                        id: subItem.attribute.resId,
                                        searchWord: encodeURI(subItem.meta.rt_searchWord),
                                        tagId: subItem.meta.rt_tagId,
                                    }),
                                );
                            }
                        }
                        if (subItem.meta.type == 'background') {
                            backgroundArr.push(
                                JSON.stringify({
                                    id: subItem.attribute.resId,
                                }),
                            );
                            if (subItem.meta.rt_isNowAdd) {
                                backgroundArrNowAdd.push(
                                    JSON.stringify({
                                        id: subItem.attribute.resId,
                                        searchWord: encodeURI(subItem.meta.rt_searchWord),
                                        ratioId: subItem.meta.rt_ratioId,
                                    }),
                                );
                            }
                        }

                        if (
                            subItem.meta.type == 'background' ||
                            subItem.meta.type == 'pic' ||
                            subItem.meta.type == 'image'
                        ) {
                            // console.log("subItem",subItem);
                            const filterResId = subItem.attribute.filters.resId;
                            if (filterResId) {
                                fliterArr.push(filterResId);
                                if (subItem.attribute.rt_isNewFilters) {
                                    fliterArrNowAdd.push(filterResId);
                                }
                            }
                            if (subItem.attribute.imageEffects && subItem.attribute.imageEffects.resId > 0) {
                                imageEffectsArr.push(subItem.attribute.imageEffects.resId);
                                if (subItem.attribute.rt_isNewImageEffect) {
                                    imageEffectsArrNowAdd.push(subItem.attribute.imageEffects.resId)
                                }
                            }
                            const containerId = subItem.attribute.container?.id;
                            if (containerId) {
                                containerArr.push(containerId);
                                if (subItem.attribute.rt_isNewContainer) {
                                    containerArrNowAdd.push(containerId);
                                }
                            }
                        }

                        if (
                            subItem.meta.type == 'pic' &&
                            subItem.attribute.resId &&
                            subItem.attribute.resId.toString().split('-')[0] != 'UA'
                        ) {
                            picArr.push(
                                JSON.stringify({
                                    id: subItem.attribute.resId,
                                }),
                            );
                            if (subItem.meta.rt_isNowAdd) {
                                picArrNowAdd.push(
                                    JSON.stringify({
                                        id: subItem.attribute.resId,
                                        searchWord: encodeURI(subItem.meta.rt_searchWord),
                                        tagId: subItem.meta.rt_tagId,
                                    }),
                                );
                            }
                        }
                        if (subItem.attribute.resId && subItem.attribute.resId.toString().split('-')[0] == 'UA') {
                            uploadFileArr.push(
                                JSON.stringify({
                                    id: subItem.attribute.resId,
                                }),
                            );
                        }

                        if (
                            (subItem.meta.type == 'image' ||
                                subItem.meta.type == 'background' ||
                                subItem.meta.type == 'pic') &&
                            subItem.meta.origin != 'default' &&
                            !subItem.meta.group
                        ) {
                            if (assetIDs === '') {
                                assetIDs = subItem.attribute.resId;
                            } else {
                                assetIDs += ',' + subItem.attribute.resId;
                            }
                        }
                        if (subItem.meta.type == 'frame') {
                            frameArr.push(subItem.attribute.resId);
                            if (subItem.meta.rt_isNowAdd) {
                                frameArrNowAdd.push(subItem.attribute.resId);
                            }
                        }
                    },
                );
            },
        );

        const props = getProps();
        if (uploadFileArr.length > 0) {
            assetManager.setPv_new(289, {
                additional: {
                    groupWordID: uploadFileArr.join(','),
                    option: JSON.stringify({ upicId: props['upicId'] }),
                },
            });
        }
        if (picArr.length > 0) {
            assetManager.setPv_new(287, {
                additional: {
                    groupWordID: picArr.join(','),
                    option: JSON.stringify({ upicId: props['upicId'] }),
                },
            });
        }
        if (picArrNowAdd.length > 0) {
            assetManager.setPv_new(288, {
                additional: {
                    groupWordID: picArrNowAdd.join(','),
                    option: JSON.stringify({ upicId: props['upicId'] }),
                },
            });
        }
        if (backgroundArr.length > 0) {
            assetManager.setPv_new(284, {
                additional: {
                    groupWordID: backgroundArr.join(','),
                    option: JSON.stringify({ upicId: props['upicId'] }),
                },
            });
        }
        if (backgroundArrNowAdd.length > 0) {
            assetManager.setPv_new(285, {
                additional: {
                    groupWordID: backgroundArrNowAdd.join(','),
                    option: JSON.stringify({ upicId: props['upicId'] }),
                },
            });
        }
        if (pngArr.length > 0) {
            assetManager.setPv_new(281, {
                additional: {
                    groupWordID: pngArr.join(','),
                    option: JSON.stringify({ upicId: props['upicId'] }),
                },
            });
        }
        if (pngArrNowAdd.length > 0) {
            assetManager.setPv_new(282, {
                additional: {
                    groupWordID: pngArrNowAdd.join(','),
                    option: JSON.stringify({ upicId: props['upicId'] }),
                },
            });
        }
        if (specificWordIdArr.length > 0) {
            assetManager.setPv_new(164, {
                additional: {
                    s0: 'specificWordId',
                    s1: specificWordIdArr.join(','),
                    s2: createTime,
                    s3: specificWordIdKwArr.join(','),
                    s4: tabIdArr.join(','),
                    option: JSON.stringify({ upicId: props['upicId'] }),
                },
            });
        }
        if (specificWordIdArrNowAdd.length > 0) {
            assetManager.setPv_new(221, {
                additional: {
                    s0: 'specificWordId',
                    s1: specificWordIdArrNowAdd.join(','),
                    s3: specificWordIdArrNowKwAdd.join(','),
                    s2: createTime,
                    user_templ_id: props['upicId'],
                },
            });
        }
        if (word3DArr.length > 0) {
            assetManager.setPv_new(170, {
                additional: {
                    s0: '3DWordId',
                    s1: word3DArr.join(','),
                    s2: createTime,
                    s3: specificWordIdKwArr.join(','),
                    option: JSON.stringify({ upicId: props['upicId'] }),
                },
            });
        }
        if (word3DArrNowAdd.length > 0) {
            assetManager.setPv_new(222, {
                additional: {
                    s0: '3DWordId',
                    s1: word3DArrNowAdd.join(','),
                    s2: createTime,
                    s3: specificWordIdArrNowKwAdd.join(','),
                    user_templ_id: props['upicId'],
                },
            });
        }
        if (copyWritingID.length > 0) {
            assetManager.setPv_new(170, {
                additional: {
                    s0: 'copywriting',
                    s1: copyWritingID.join(','),
                    s2: createTime,
                    option: JSON.stringify({ upicId: props['upicId'] }),
                },
            });
        }
        if (copyWritingIDNowAdd.length > 0) {
            assetManager.setPv_new(222, {
                additional: {
                    s0: 'copywriting',
                    s1: copyWritingIDNowAdd.join(','),
                    s2: createTime,
                    user_templ_id: props['upicId'],
                },
            });
        }
        if (groupWordID.length > 0) {
            assetManager.setPv_new(170, {
                additional: {
                    s0: 'groupWordID',
                    s1: groupWordID.join(','),
                    s2: createTime,
                    option: JSON.stringify({ upicId: props['upicId'] }),
                },
            });
        }
        if (groupWordIDNowAdd.length > 0) {
            assetManager.setPv_new(222, {
                additional: {
                    s0: 'groupWordID',
                    s1: groupWordIDNowAdd.join(','),
                    s2: createTime,
                    user_templ_id: props['upicId'],
                },
            });
        }
        if (SVGID.length > 0) {
            assetManager.setPv_new(232, {
                additional: {
                    s0: SVGID.join(','),
                    ut: props['upicId'],
                },
            });
        }
        if (SVGIDNowAdd.length > 0) {
            assetManager.setPv_new(233, {
                additional: {
                    s0: SVGIDNowAdd.join(','),
                    user_templ_id: props['upicId'],
                },
            });
        }
        if (wordArtArr.length > 0) {
            assetManager.setPv_new(272, {
                additional: {
                    s0: wordArtArr.join(','),
                    ut: props['upicId'],
                },
            });
        }
        if (wordArtArrNowAdd.length > 0) {
            assetManager.setPv_new(273, {
                additional: {
                    s0: wordArtArrNowAdd.join(','),
                    ut: props['upicId'],
                },
            });
        }
        if (photographyArr.length > 0) {
            assetManager.setPv_new(427, {
                additional: {
                    s0: photographyArr.join(','),
                    ut: props['upicId'],
                },
            });
        }
        if (photographyNowAdd.length > 0) {
            assetManager.setPv_new(428, {
                additional: {
                    groupWordID: photographyNowAdd.join(','),
                    option: JSON.stringify({ upicId: props['upicId'] }),
                },
            });
        }
        if (personArr.length > 0) {
            assetManager.setPv_new(431, {
                additional: {
                    groupWordID: personArr.join(','),
                    option: JSON.stringify({ upicId: props['upicId'] }),
                },
            });
        }
        if (personNowAdd.length > 0) {
            assetManager.setPv_new(432, {
                additional: {
                    groupWordID: personNowAdd.join(','),
                    option: JSON.stringify({ upicId: props['upicId'] }),
                },
            });
        }

        if (emojiArr.length > 0) {
            assetManager.setPv_new(341, {
                additional: {
                    groupWordID: emojiArr.join(','),
                    option: JSON.stringify({ upicId: props['upicId'] }),
                },
            });
        }

        if (emojiArrNowAdd.length > 0) {
            assetManager.setPv_new(342, {
                additional: {
                    groupWordID: emojiArrNowAdd.join(','),
                    option: JSON.stringify({ upicId: props['upicId'] }),
                },
            });
        }

        if (assetIDs) {
            assetManager.setPv_new(177, {
                additional: {
                    assetIDs: assetIDs,
                },
            });
        }

        if (fliterArr.length > 0) {
            assetManager.setPv_new(3350, {
                additional: {
                    s0: fliterArr.join(','),
                    ut: props['upicId'],
                },
            });
        }
        if (fliterArrNowAdd.length > 0) {
            assetManager.setPv_new(8722, {additional: {
                s0: fliterArrNowAdd.join(','),
                ut: props['upicId'],
            }});
        }
        if (imageEffectsArr.length > 0) {
            assetManager.setPv_new(3756, {
                additional: {
                    s0: imageEffectsArr.join(','),
                    ut: props['upicId'],
                },
            });
        }
        if (imageEffectsArrNowAdd.length > 0) {
            assetManager.setPv_new(8723, {additional: {
                s0: imageEffectsArrNowAdd.join(','),
                ut: props['upicId'],
            }});
        }
        if (containerArr.length > 0) {
            assetManager.setPv_new(8703, {
                additional: {
                    s0: containerArr.join(','),
                    ut: props['upicId'],
                },
            })
        }
        if (containerArrNowAdd.length > 0) {
            assetManager.setPv_new(8704, {
                additional: {
                    s0: containerArrNowAdd.join(','),
                    ut: props['upicId'],
                },
            })
        }
        if (frameArr.length > 0) {
            assetManager.setPv_new(8724, {
                additional: {
                    s0: frameArr.join(','),
                    ut: props['upicId'],
                }
            })
        }
        if (frameArrNowAdd.length > 0) {
            assetManager.setPv_new(8725, {
                additional: {
                    s0: frameArrNowAdd.join(','),
                    ut: props['upicId'],
                }
            })
        }

        e.stopPropagation();
        e?.nativeEvent.stopPropagation();
    }

    hasVideoType = (pageAttr: IPageAttr) => {
        return TemplateLogic.hasVideoType(pageAttr);
    }

    hasPageAnimation = (pageAttr: IPageAttr) => {
        return TemplateLogic.hasPageAnimation(pageAttr);
    }
    
    hasAudio = (pageAttr: IPageAttr) => {
        return TemplateLogic.hasAudio(pageAttr);
    }

    hasVideoE = () => {
        return TemplateLogic.hasVideoE();
    }

    hasEffect = () => {
        return TemplateLogic.hasEffect()
    }

    hasAssetAnimation = () => {
        return TemplateLogic.hasAssetEffect()
    }
    getStyles = () =>{
        return TemplateLogic.getPageStyles()
    }
    getEmphaticMarkIds = () =>{
        return TemplateLogic.getEmphaticMarkIds()
    }
    getAiToolUsedType = ()=>{
        return TemplateLogic.getAiToolUsedType()
    }
    /** 将页面动画 id 统计成字符串 */
    countPageAnimation = (pageAttr: IPageAttr) => {
        return TemplateLogic.countPageAnimation(pageAttr);
    }

    async componentDidUpdate() {
        const { pageAttr, cooperationUsers, user } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (this.state.isVideo && !this.hasVideoType(pageAttr)) {
            this.setState({
                isVideo: false,
            })
        }
        if (!this.state.isHiddenNoRisk && (IPSConfig.upicIdIsCollaboratived || cooperationUsers.length > 1)) {
            this.hideNoRisk();
        }
        if (!this.state.hasCheckedUpicIdOwner && Number(user.userId) > 0) {
            const props = IPSConfig.getProps();
            if (props.upicId && props.share_uid && props.share_id) {
                this.setState({
                    hasCheckedUpicIdOwner: true,
                });
                const result = await TemplateLogic.checkIsUpicIdOwner(props.upicId);
                if (result) {
                    this.setState({
                        isUpicIdOwner: true,
                    })
                }
            } else {
                this.setState({
                    hasCheckedUpicIdOwner: true,
                    isUpicIdOwner: true,
                });
            }
        }

        if (this.state.is_person_business_vip && this.wheelPlayTimer) {
            this.closeBy()
        }
    }

    createCopys = () => {
      
        const { user, work} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const { info } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage,
        });
        const { class_id = [] } = info || {};
        const urlProps = IPSConfig.getProps();
        let upicId = undefined , paperId = undefined;
        let redirectUrl;
        const flag = work.pages.every((item: any) => item.assets.length === 0);

        
        assetManager.setPv_new(3689);
        if (!(user.userId > 0)) {
            emitter.emit('LoginPanelShow', 'createCopys');
            assetManager.setPv_new(4572, {additional: {
                s0: 'createCopys'
            }})
            return;
        }
        if (urlProps.upicId) {
            upicId = urlProps.upicId;
        } else if(urlProps.paperId) {
            paperId = urlProps.paperId
        }

        if(flag && !upicId && !paperId){
            const creatPup = {
                windowContent: (
                    <DownloadPopup
                        key={new Date().getTime()}
                        className="DownloadPopup"
                        showFlag= {573}
                    />
                ),
                popupWidth: 348,
                popupHeight: 244,
                background: '#000000',
                style: {
                    borderRadius: '4px',
                    backgroundColor: 'rgba(255, 255, 255, 1)',
                    'boxShadow': '0px 0px 8px 0px rgba(0, 0, 0, 0.12), 0px 0px 2px 0px rgba(0, 0, 0, 0.12)',
                    position: 'absolute',
                    top: '30%',
                    padding: 0,
                    left: '40%',
                },
                popupTitleBarStyle: {
                    width: 0,
                    height: 0,
                    display: 'none',
                },
                popupBodyStyle: {
                    padding: 0,
                },
            }
            emitter.emit('popupWindow', creatPup);
            return false;
        }
        if(!upicId && !paperId){
            // 如果是新的模板执行自动保存再创建
            emitter.emit('autoSaveTempl', this.createCopys, 0, {iszdbc: true});
        }

        let query = upicId ? `upicId=${upicId}`  : paperId ? `paperId=${paperId}` : '' ;

        if(env.teamTemplate || isUeTeam || isEcommerceTeam()){
            query = `uttid=${urlProps.user_template_team_id}`
        }
        assetManager.createCopyLog(query).then(data => {
            data.json().then(res => {
                if(res.stat == 1){
                    if(env.teamTemplate || isUeTeam || isEcommerceTeam()){
                        const creat_copy_id = `upicId=${res.msg.uttid}`;
                        if (class_id.indexOf('34') > -1) {
                            redirectUrl = '//ue.818ps.com/?' + `user_template_team_id=${res.msg.uttid}&` + creat_copy_id + '&team_id=' + getProps().team_id + '&origin=creat_copy' + '&pram=' + urlProps.pram;
                        } else {
                            redirectUrl = '//ue.818ps.com/?' + `user_template_team_id=${res.msg.uttid}&` + creat_copy_id + '&team_id=' + getProps().team_id + '&origin=creat_copy' + '&pram=' + urlProps.pram;
                        }
                        window.open(redirectUrl)
                    } else {
                        const creat_copy_id = res.msg.upicId ? `upicId=${res.msg.upicId}`  : res.msg.paperId ? `paperId=${res.msg.paperId}` : '' ;
                        if (class_id.indexOf('34') > -1) {
                            redirectUrl = '//ue.818ps.com/?' + `picId=${res.msg.picId}&` + creat_copy_id + '&origin=creat_copy' + '&pram=' + urlProps.pram + '&ecommerce=1';
                        } else {
                            redirectUrl = '//ue.818ps.com/?' + `picId=${res.msg.picId}&` + creat_copy_id + '&origin=creat_copy' + '&pram=' + urlProps.pram;
                        }
                        window.open(redirectUrl)
                    }
                }
            })
        })
    }

    enterShowTeam = (e:React.MouseEvent) => {
        this.onDetailDownType && ((this.onDetailDownType as HTMLDivElement).style.display = 'none')
        this.setState({
            isShowTeam: true,
        })
        if(e){
            e.stopPropagation();
        }
        clearTimeout(downloadDropDownTimer);
    }

    leaveShowTeam = (e:React.MouseEvent) => {
        this.setState({
            isShowTeam: false,
        })
        // if(e){
        //     e.stopPropagation();
        // }
    }

    /*保存至公众号START*/
    showWeChatSavePop = (from: boolean, e:React.MouseEvent) => {
        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (!(user.userId > 0)) {
            emitter.emit('LoginPanelShow', 'download');
            return;
        }
        emitter.emit('autoSaveTempl', '', '', {handleSave: true});
        emitter.emit('popupWindow',this.returnPopupWindowContent());
        assetManager.setPv_new(5140, {additional: {s0: from}});
        this.setState({
            isShowTeam: false,
            isShowClickTeam: false,
        })
        if(e){
            e.stopPropagation();
            (e as any)?.nativeEvent.stopPropagation();
        }
    }

    returnPopupWindowContent = () => {
        const { work } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        // work.pages.length
        return {
            windowContent: (
               <SaveWeChatPublic
                    workPagesLength={1}
                    getDownloadNumInfo={this.getDownloadNumInfo.bind(this,'download', false, false,{from:'saveWeChatPublic'})}
               />
            ),
            popupWidth: 'auto',
            popupHeight: 'auto',
            background: '#000000',
            style: {
                borderRadius: '8px',
                backgroundColor: 'rgba(255, 255, 255, 1)',
                boxShadow: '0px 0px 8px 0px rgba(0, 0, 0, 0.12), 0px 0px 2px 0px rgba(0, 0, 0, 0.12)',
                position: 'absolute',
                padding: 0,
                left: '40%',
                top: '30%'
            },
            popupTitleBarStyle: {
                width: 0,
                height: 0,
                display: 'none',
            },
            popupBodyStyle: {
                padding: 0,
            },
        };
        
    }

    saveWechatEnterSaveTeam = (e:React.MouseEvent):void => {
        this.setState({
            isShowTeam: true
        })
        assetManager.setPv_new(5139);
        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }
    /*保存至公众号END*/

    /*发布到微博/抖音START*/
    showPopup = (type = 'weibo', from = '', e:React.MouseEvent) => {
        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (!(user.userId > 0)) {
            emitter.emit('LoginPanelShow', 'download');
            return;
        }
        emitter.emit('autoSaveTempl', '', '', {handleSave: true});

        emitter.emit('popupWindow',this.returnSavePop(type));
        const setPvNew = type === 'weibo' ? 5267 : 5382;
        assetManager.setPv_new(setPvNew, {additional: {s0: from}});
        this.setState({
            isShowTeam: false,
            isShowClickTeam: false,
        })
        e.stopPropagation(); 
    }

    returnSavePop = (type = 'weibo') => {
        const { work } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        // work.pages.length
        // 当js设置popupWindow当top和left的时候 重新打开popupWindow 并没有刷新样式
        const popupWindow = document.getElementsByClassName('popupWindow')[0] as HTMLElement;
        if(popupWindow){
            popupWindow.style.top = '60px';
            popupWindow.style.right = '10px';
        }

        const Component = type === 'weibo' ? SaveToWeibo : SaveToTikTok;
        const from = type === 'weibo' ? 'saveToWeibo' : 'saveToTikTok';
        return {
            windowContent: (
               <Component
                    workPagesLength={type === 'weibo' ? 1 : work.pages.length}
                    getDownloadNumInfo={this.getDownloadNumInfo.bind(this,'download', false, false,{from})}
               />
            ),
            popupWidth: 'auto',
            popupHeight: 'auto',
            clickMask: false,
            background:'transparent',
            style: {
                borderRadius: '8px',
                backgroundColor: 'rgba(255, 255, 255, 1)',
                boxShadow: '0px 0px 8px 0px rgba(0, 0, 0, 0.12), 0px 0px 2px 0px rgba(0, 0, 0, 0.12)',
                position: 'absolute',
                padding: 0,
                top: '60px',
                right:'10px',
                left:'auto',
                overflow: 'hidden'
            },
            popupTitleBarStyle: {
                width: 0,
                height: 0,
                display: 'none',
            },
            popupBodyStyle: {
                padding: 0,
            },
        } 
    }

    mouseEnterItme = (type = 'weibo', e:React.MouseEvent) => {
        this.setState({
            isShowTeam: true
        })
        const setPvNew = type === 'weibo' ? 5266 : 5381;
        assetManager.setPv_new(setPvNew);
        e.stopPropagation();
    }
    /*发布到微博/抖音END*/

    /*
     * 弹出分享链接弹框
     */
    collaborativeLinkPopup = (from?: string, e?: React.MouseEvent) => {
        e?.stopPropagation();
        this.setState({
            isShowTeam: false,
            isShowClickTeam: false,
        })
        assetManager.getUserInfo().then((data) => {
            data.json().then((userInfo) => {
                if (!userInfo.id) {
                    emitter.emit('LoginPanelShow');
                    return;
                }
                if (userInfo.bind_phone && userInfo.bind_phone === 1) {
                    DocHashLogic.addPageHash(false);
                    emitter.emit('autoSaveTempl', '', 0, { handleSave: true });
                    const urlProps = getProps();

                    if(urlProps.upicId){
                        this.getPopupCollaborativeWindow(urlProps.upicId);
                    }
            
                    if(!urlProps.upicId){
            
                        let intervalId: number = null;
                        let retryNumber = 0
                        const getUpicId = () => {
                            const urlPropsTime = getProps();
                            retryNumber = retryNumber+1;
                            if(urlPropsTime.upicId){
                                clearInterval(intervalId)
                                this.getPopupCollaborativeWindow(urlPropsTime.upicId);
                            }
                            if(retryNumber>10){
                                clearInterval(intervalId)
                            }
                        }
                        intervalId = window.setInterval(getUpicId,1000)
                    }
                } else {
                    emitter.emit('InfoBarPhoneBindPopup', 'downloadToPhone');
                }
            });
        });
        assetManager.setPv_new(5311, {
            additional: {
                s0: from,
            },
        });
    }

    getPopupCollaborativeWindow(upicId: string){
        let jumpType:string = window.location.href.includes('ue.818ps.com') ? 'ue' : 'ecommerce';
        if(/* env.editor === "ueteam" || */ isUeTeam ){
            jumpType = "ueteam"
        }
        assetManager.getCooperationShareUrl(upicId).then(data => {
            data.json().then(result => {
                if (result.stat === 1) {
                    IPSConfig.upicIdIsCollaboratived = true;
                    emitter.emit('onUpicIdIsCollaborativedChange');
                    this.hideNoRisk();
                    const windowInfo = {
                        windowContent: (
                            <ClickOutside onClickOutside={() => emitter.emit('popupClose')}>
                                <ShareLinkPopup
                                    title="邀请其他人一起完成作品设计"
                                    detail="点击链接即可加入编辑完成设计"
                                    prepText={result.prefix}
                                    shareUrl={/* env.editor === "ueteam" */ result.url}
                                    type="collaborative"
                                />
                            </ClickOutside>
                        ),
                        popupWidth: 423,
                        // popupHeight: 170,
                        background: 'none',
                        style: {
                            borderRadius: '12px',
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            boxShadow: '0px 0px 8px 0px rgba(0, 0, 0, 0.12), 0px 0px 2px 0px rgba(0, 0, 0, 0.12)',
                            position: 'absolute',
                            top: 57,
                            padding: 0,
                            left: 'calc(100% - 430px)',
                        },
                        popupTitleBarStyle: {
                            width: 0,
                            height: 0,
                            display: 'none',
                        },
                        popupBodyStyle: {
                            padding: 0,
                        },
                    };
                    emitter.emit('popupWindow', windowInfo);
                } else if (result.stat === -2) {
                    assetManager.setPv_new(5725);
                    showCollaborativeLimit('work')
                } else {
                    emitter.emit('PromptBox', {
                        windowContent: <div style={{ textAlign: 'center', lineHeight: '70px' }}>{result.msg}</div>,
                    });
                    setTimeout(() => {
                        emitter.emit('PromptBoxClose');
                    }, 3000);
                }
            })
        });
    }  

    hideNoRisk = () => {
        this.setState({
            isHiddenNoRisk: true
        })
    }


    closeToolTipNoLoginTip = (): void => {
        this.closeToolTipKnow = emitter.addListener('closeToolTipKnow',(status = false)=>{
            this.setState({
                showNotLoginTip: status
            })
        })
    }

    // 悬停1s限制埋点: 移入
    handleMouseEnterLimitSetPv = (pvNum: number, e: React.MouseEvent): void => {
        e?.stopPropagation();
        e?.nativeEvent.stopPropagation();
        if (this.timer4) {
            clearTimeout(this.timer4);
            this.timer4 = null;
        }
        this.setState({
            isShowTeam: true,
        })
        this.timer4 = setTimeout(() => {
            assetManager.setPv_new(pvNum);
        }, 1000);
    }
    // 悬停1s限制埋点: 移出
    handleMouseLeaveLimitSetPv = (e: React.MouseEvent): void => {
        // e?.stopPropagation();
        // e?.nativeEvent.stopPropagation();
        if (this.timer4) {
            clearTimeout(this.timer4);
            this.timer4 = null;
        }
        // this.setState({
        //     isShowTeam: false,
        // })
    }

    async getIsPersonBusinessVip() {
        try {
            const res = await assetManager.getIsPersonBusinessVip()
            if (res.code === 0) {
                this.setState({
                    is_person_business_vip: res.data.is_person_business_vip,
                })
            }
        } catch (error) {
            console.error(error)
        }
    }
}

@ErrorBoundaryDecorator()
@storeDecorator((state: { [key: string]: any }) => {
    return {
      user: state.onCanvasPainted.user,
      work: state.onCanvasPainted.work,
    };
  })
export class InfoBar extends InfoBarComponent {}