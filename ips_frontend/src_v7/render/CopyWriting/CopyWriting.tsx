import React, { Component } from 'react';
// @ts-ignore
import addEventListener from 'rc-util/lib/Dom/addEventListener';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { canvasStore } from '@redux/CanvasStore';
import { paintOnCanvas } from '@component/canvas/CanvasRedux';
import { emitter } from '@component/Emitter';

import { assetTemplate } from '@component/AssetMap';
import { assetManager } from '@component/AssetManager';
import { IAsset } from '@v7_logic/Interface';
import {ETool} from '@v7_logic/Enum';
import { CopyWritingPanel } from '@v7_render/CopyWritingPanel';
import { CopywritingBtnFloatPanel } from '@v7_render/CopywritingBtnFloatPanel';
import { EventSubscription } from 'fbemitter';
// import { CopyWritingPanel } from '../CopyWritingPanel/CopyWritingPanel'
import { AssetAddListener } from '@v7_logic/AssetAddListener';
import { env } from '@editorConfig/env';
import { isUeTeam } from '@v7_utils/webSource';

export function DisplayCopywritingTool(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.COPYWRITING, nav: ETool.COPYWRITING};
}

interface propsStruct {
    // fontCount:number
    isActive?: boolean;
}

interface stateStruct {
    isCommonCopywritingBtn?: boolean;
    navType?: string;
    fontCount?: number;
}

export class CopyWriting extends Component<propsStruct, stateStruct> {
    titleProp: { fontSize: number; fontWeight: string; ranking: string };
    mainBodyProp: { fontSize: string; fontWeight: string; ranking: string };
    addTextEditorTitleEvent: {
        (e: React.MouseEventHandler<HTMLDivElement>): void;
    };
    addTextEditorMainBodyEvent: {
        (e: React.MouseEvent<HTMLDivElement, MouseEvent>): void;
    };
    addEventListenerListener: EventSubscription;

    constructor(props: propsStruct) {
        super(props);

        this.titleProp = {
            fontSize: 24,
            fontWeight: 'bold',
            ranking: '0',
        };

        this.mainBodyProp = {
            fontSize: 'small',
            fontWeight: 'normal',
            ranking: '0',
        };

        this.state = {
            navType: 'specilWord',
            isCommonCopywritingBtn: false,
        };

        this.addTextEditorTitleEvent = this.addTextEditorEvent.bind(this, this.titleProp, '', 'script');
        this.addTextEditorMainBodyEvent = this.addTextEditorEvent.bind(this, this.mainBodyProp, '', 'text');
        this.getUseingFontCount();
    }

    stopPropagation(e: MouseEvent): void {
        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }

    getUseingFontCount(): void {
        const th = this;
        assetManager.getUsingFontCount().then((data) => {
            data.json().then((resultData) => {
                if (resultData.stat == 1) {
                    th.setState({
                        fontCount: resultData.count,
                    });
                }
            });
        });
    }

    commonCopywritingBtnClickEvent(e: MouseEvent): void {
        const { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }); // canvasStore.getState().onCanvasPainted;
        if (!(user.userId > 0)) {
            emitter.emit('LoginPanelShow', 'previewAndShare');
            assetManager.setPv_new(174, {
                additional: {
                    s0: 'previewAndShare',
                },
            });
            return;
        }
        assetManager.setPv_new(7102, {
            additional: {
                s1: '文案大全',
            }
        });

        assetManager.setPv_new(3542);
        this.setState({
            isCommonCopywritingBtn: true,
        });

        if (this.addEventListenerListener) {
            this.addEventListenerListener.remove();
        }

        const th = this;
        this.addEventListenerListener = addEventListener(window, 'click', () => {
            th.setState({
                isCommonCopywritingBtn: false,
            });

            th.addEventListenerListener.remove();
        });

        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }

    addTextEditorEvent(assetProps: {
        fontSize: number | 'small',
        fontWeight: 'normal'| 'bold',
        ranking: '0' | '1' | '2',
    }, value: string, sign: string, type:any, e: React.MouseEvent): void {
        AssetAddListener.addText({
            ...assetProps,
            fontSize: 24,
            // 用来替代旧的 临时 fontSize === 'small' 产生的类型校验错误
            isSmallFontSize: assetProps.fontSize === 'small',
        }, undefined, value, type);
        if (e) {
            e.stopPropagation();
            e.nativeEvent.stopPropagation();
        }
        assetManager.setPv_new(7102, {
            additional: {
                s1: '文案大全',
            }
        });
        assetManager.setPv_new(3542, { additional: { s0: sign } });
        return;

        // if (value == undefined || value == '') {
        //     value = '双击编辑文字';
        // }
        // const asset = JSON.parse(JSON.stringify(assetTemplate.text)),
        //     { canvas } = storeAdapter.getStore({
        //         store_name: storeAdapter.store_names.paintOnCanvas,
        //     }), // canvasStore.getState().onCanvasPainted,
        //     { recommendFont } = storeAdapter.getStore({
        //         store_name: storeAdapter.store_names.RecommendManage,
        //     }); // canvasStore.getState().recommendManageRedux;
        // let recommendFontFlag = 0;

        // let width = 200,
        //     height = 50;

        // Object.assign(asset.meta, {
        //     type: 'text',
        //     addOrigin: 'text',
        // });
        // Object.assign(asset.attribute, {
        //     text: [value],
        //     fontSize: assetProps.fontSize,
        //     fontWeight: assetProps.fontWeight,
        //     textAlign: 'center',
        //     width: width,
        //     opacity: 100,
        //     lineHeight: 13,
        //     letterSpacing: 0,
        //     fontFamily: 'fnsyhtRegular',
        //     color: { r: 74, g: 74, b: 74, a: 1 },
        //     writingMode: 'horizontal-tb',
        // });

        // try {
        //     if (recommendFont[assetProps.ranking]) {
        //         width = parseInt(recommendFont[assetProps.ranking]['font_size']) * 9;
        //         Object.assign(asset.attribute, {
        //             fontSize: parseInt(recommendFont[assetProps.ranking]['font_size']),
        //             width: width,
        //         });
        //         recommendFontFlag = 1;
        //     }
        // } catch (err) {}

        // if (!recommendFontFlag) {
        //     let tempFontSize;
        //     const fontNum = 6;

        //     if (assetProps.ranking === '2') {
        //         tempFontSize = (canvas.width * 1) / 3 / fontNum;
        //     } else if (assetProps.ranking === '1') {
        //         tempFontSize = (canvas.width * 1) / 2 / fontNum;
        //     } else {
        //         tempFontSize = (canvas.width * 2) / 3 / fontNum;
        //     }

        //     width = tempFontSize * 9;
        //     Object.assign(asset.attribute, {
        //         fontSize: tempFontSize,
        //         width: width,
        //     });

        //     height = tempFontSize;
        // }

        // Object.assign(asset.transform, {
        //     posX: ((canvas.width - width) / 2) * canvas.scale,
        //     posY: ((canvas.height - height) / 2) * canvas.scale,
        // });

        // if (assetProps.fontSize === 'small') {
        //     Object.assign(asset.attribute, {
        //         fontSize: 'small',
        //     });
        // }
        // const { isDesigner } = storeAdapter.getStore({
        //     store_name: storeAdapter.store_names.paintOnCanvas,
        // }); // canvasStore.getState().onCanvasPainted;
        // if (assetProps.ranking === '0' && !isDesigner) {
        //     if (assetProps.fontSize !== 'small') {
        //         Object.assign(asset.meta, {
        //             isTitle: 1,
        //         });
        //     }
        // }

        // assetManager.setPv_new(2461, { additional: {} });

        // canvasStore.dispatch(paintOnCanvas('ADD_TEXTEDITOR', asset));
        // canvasStore.dispatch(paintOnCanvas('SELECT_ASSET_NEW'));
        // if (e) {
        //     e.stopPropagation();
        //     (e as any)?.nativeEvent.stopPropagation();
        // }
    }

    onGoCorporateMembers(e: MouseEvent): void {
        assetManager.setPagePv_new(3253);
        const { info } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage,
        });
        if(info?.is_company_temp === 1 || env.teamTemplate || isUeTeam ){
            window.open(
                'https://818ps.com/dash/firm-intro?origin=font-authorize-btn&route_id=16328996965437&route=1,&after_route=1',
            );
        }else{
            const {user} = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            assetManager.getUserInfo()
            .then((data) => data.json())
            .then(userInfo => {
                if(user.userId == 0 || userInfo.commercial_type == -1 || userInfo.commercial_type == 0) {
                    window.open(
                        "https://818ps.com/dash/vip-spec-non-commercial?origin=skip_common_vipSpec"
                    )
                } else if(userInfo.commercial_type == 1 || userInfo.commercial_type == 2) {
                    window.open(
                        "https://818ps.com/dash/vip-spec-commercial?classify=1&origin=skip_advanced_vipSpec&route_id=16007403774149&route=1,86&after_route=1_86"
                    )
                }
            })
            // if(user.isJumpCommercial === 1){
            //     window.open(
            //         'https://818ps.com/dash/vip-spec-commercial?classify=1&origin=VipSpec&route_id=16007403774149&route=1,86&after_route=1_86',
            //     );
            // }else{
            //     window.open(
            //         'https://818ps.com/dash/vip-spec?classify=1&origin=VipSpec&route_id=16007403774149&route=1,86&after_route=1_86',
            //     );
            // }
        }
        
        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }

    onMouseEnterEvent(e: MouseEvent): void {
        assetManager.setPagePv_new(3260);
        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }

    onMouseLeaveEvent(e: MouseEvent): void {
        assetManager.setPagePv_new(3259);
        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }

    render(): JSX.Element {
        const { isCommonCopywritingBtn, fontCount } = this.state;
        const { isDesigner } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }); // canvasStore.getState().onCanvasPainted;
        
        return (
            <div className="specificWord">
                {/* {!isDesigner && (
                    <div
                        className="company_use"
                        onClick={this.onGoCorporateMembers.bind(this)}
                        onMouseLeave={this.onMouseLeaveEvent.bind(this)}
                        onMouseEnter={this.onMouseEnterEvent.bind(this)}
                    >
                        <i className="iconfont icon-shang"></i>
                        <span>商用会员含{fontCount}套正版字体商用授权</span>
                    </div>
                )} */}

                <div className="addTextContainer">
                    <div className="addText" onClick={this.addTextEditorTitleEvent.bind(this)}>
                        <span>添加标题</span>
                    </div>
                    <div className="addMainText" onClick={this.addTextEditorMainBodyEvent.bind(this)}>
                        <span>添加正文</span>
                    </div>
                </div>

                <div className="commonCopywritingBtn" onClick={this.commonCopywritingBtnClickEvent.bind(this)}>
                    添加常用文案
                </div>
                {isCommonCopywritingBtn && (
                    <CopywritingBtnFloatPanel closeCallBack={() => this.setState({ isCommonCopywritingBtn: false })} />
                )}

                <CopyWritingPanel
                    fromCopyWriting={true}
                    setPv={{
                        onSearch: () => assetManager.setPv_new(3543),
                        onClickContent: (assetId: number, keyword:string) => {
                            assetManager.setPv_new(3544, {
                                additional: {
                                    s0: 'copywriting',
                                    s1: assetId,
                                },
                            });
                            assetManager.setPv_new(7102, {
                                additional: {
                                    s1: '文案大全',
                                    s2: keyword,
                                }
                            })
                        }

                    }}
                />
            </div>
        );
    }
}
