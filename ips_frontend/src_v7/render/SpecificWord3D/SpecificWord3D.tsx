import React, { Component, CSSProperties } from 'react';
// import { addEventListener } from '@v7_utils/AddEventListener';
// @ts-ignore
import LazyLoad from 'react-lazy-load';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { AssetAddListener } from '@v7_logic/AssetAddListener';
import { IEffectVariant3D } from '@v7_logic/Interface';
import { ETool } from '@v7_logic/Enum';

import { emitter } from '@component/Emitter';

import { assetTemplate } from '@component/AssetMap';
import { assetManager } from '@component/AssetManager';

export function DisplaySpecificWord3DTool(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.SPECIFIC_WORD_3D, nav: ETool.SPECIFIC_WORD_3D};
}

interface propsStruct {
    isActive?: boolean;
}

interface stateStruct {
    specificWordList: {
        resId: any;
        fontFamily: string;
        effectVariant3D: any;
    }[];
    updateTime: number;
    scrollbarHeight: number;
    specificWordPreviewStyle: CSSProperties | '';
}

export class SpecificWord3D extends Component<propsStruct, stateStruct> {
    titleProp: { fontSize: number; fontWeight: string; ranking: string };
    subTitleProp: { fontSize: number; fontWeight: string; ranking: string };
    textProp: { fontSize: number; fontWeight: string; ranking: string };
    mainBodyProp: { fontSize: string; fontWeight: string; ranking: string };
    addTextEditorSubtitleEvent: any;
    addTextEditorTitleEvent: any;
    addTextEditorMainBodyEvent: any;
    addTextEditorTextEvent: any;
    addEventListenerListener: any;
    scrollBar: any;
    constructor(props: propsStruct) {
        super(props);
        this.titleProp = {
            fontSize: 24,
            fontWeight: 'bold',
            ranking: '0',
        };

        this.subTitleProp = {
            fontSize: 18,
            fontWeight: 'normal',
            ranking: '1',
        };

        this.textProp = {
            fontSize: 14,
            fontWeight: 'normal',
            ranking: '2',
        };

        this.mainBodyProp = {
            fontSize: 'small',
            fontWeight: 'normal',
            ranking: '0',
        };

        this.state = {
            // 旁门 pmzdbtt
            // 54 zh54hxh
            // 100 zh100hffxft
            // 思源 fnsyhtHeavy
            specificWordList: [
                {
                    resId: 49,
                    fontFamily: 'zh59hcch',
                    effectVariant3D: {
                        resId: 49,
                        rotation: {
                            x: 0.122,
                            y: 0.03,
                        },
                        depth: 70,
                        bevel: {
                            thickness: 1,
                            size: 5,
                            segments: 8,
                        },
                        materials: {
                            front: {
                                type: 'env_map',
                                metalness: 0.847,
                                roughness: 0,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 218, g: 147, b: 0 },
                            },
                            side: {
                                type: 'env_map',
                                metalness: 0.423,
                                roughness: 0,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 255, g: 207, b: 31 },
                            },
                        },
                        isUnDirectionalLight: true,
                    },
                },
                {
                    resId: 2,
                    fontFamily: 'zh100hffxft',
                    effectVariant3D: {
                        resId: 2,
                        rotation: {
                            x: 0.06,
                            y: -0.56,
                        },
                        depth: 80,
                        materials: {
                            front: {
                                color: { r: 255, g: 161, b: 0, a: 1 },
                            },
                            side: {
                                color: { r: 149, g: 86, b: 86, a: 1 },
                            },
                        },
                    },
                },
                {
                    resId: 55,
                    fontFamily: 'zh95hsks',
                    effectVariant3D: {
                        resId: 55,
                        rotation: {
                            x: 0.042,
                            y: -0.25,
                        },
                        depth: 64,
                        bevel: {
                            thickness: 1,
                            size: 2,
                            segments: 8,
                        },
                        materials: {
                            front: {
                                type: 'env_map',
                                metalness: 0.247,
                                roughness: 0,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 233, g: 169, b: 148 },
                            },
                            side: {
                                type: 'env_map',
                                metalness: 0.423,
                                roughness: 0,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 199, g: 144, b: 129 },
                            },
                        },
                        isUnDirectionalLight: true,
                    },
                },
                {
                    resId: 12,
                    fontFamily: 'zh152hjjcjh',
                    effectVariant3D: {
                        resId: 12,
                        rotation: {
                            x: -0.12,
                            y: -0.83,
                        },
                        depth: 100,
                        materials: {
                            front: {
                                color: { r: 255, g: 255, b: 255, a: 1 },
                            },
                            side: {
                                color: { r: 0, g: 0, b: 0, a: 1 },
                            },
                        },
                    },
                },
                {
                    resId: 1,
                    fontFamily: 'pmzdbtt',
                    effectVariant3D: {
                        resId: 1,
                        rotation: {
                            x: 0.12,
                            y: 0.78,
                        },
                        depth: 80,
                        materials: {
                            front: {
                                color: { r: 44, g: 253, b: 254, a: 1 },
                            },
                            side: {
                                color: { r: 195, g: 56, b: 251, a: 1 },
                            },
                        },
                    },
                },
                {
                    resId: 53,
                    fontFamily: 'zh58hczh',
                    effectVariant3D: {
                        resId: 53,
                        rotation: {
                            x: -0.12,
                            y: 0.017,
                        },
                        depth: 64,
                        bevel: {
                            thickness: 1,
                            size: 2,
                            segments: 8,
                        },
                        materials: {
                            front: {
                                type: 'env_map',
                                metalness: 0.447,
                                roughness: 0,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 255, g: 228, b: 127 },
                            },
                            side: {
                                type: 'env_map',
                                metalness: 0.87,
                                roughness: 0,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 205, g: 179, b: 39 },
                            },
                        },
                        isUnDirectionalLight: true,
                    },
                },

                {
                    resId: 46,
                    fontFamily: 'zh95hsks',
                    effectVariant3D: {
                        resId: 46,
                        rotation: {
                            x: -0.061,
                            y: -0.52,
                        },
                        depth: 29,
                        bevel: {
                            thickness: 1,
                            size: 5,
                            segments: 8,
                        },
                        materials: {
                            front: {
                                type: 'env_map',
                                metalness: 0.376,
                                roughness: 0,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 255, g: 200, b: 107 },
                            },
                            side: {
                                type: 'env_map',
                                metalness: 0.752,
                                roughness: 0,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 255, g: 200, b: 107 },
                            },
                        },
                        isUnDirectionalLight: true,
                    },
                },
                {
                    resId: 56,
                    fontFamily: 'zh95hsks',
                    effectVariant3D: {
                        resId: 56,
                        rotation: {
                            x: -0.061,
                            y: -0.52,
                        },
                        depth: 29,
                        bevel: {
                            thickness: 1,
                            size: 2,
                            segments: 8,
                        },
                        materials: {
                            front: {
                                type: 'env_map',
                                metalness: 0.5294,
                                roughness: 0.0988,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 255, g: 255, b: 255 },
                            },
                            side: {
                                type: 'env_map',
                                metalness: 0.752,
                                roughness: 0.1764,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 200, g: 200, b: 200 },
                            },
                        },
                        isUnDirectionalLight: true,
                    },
                },

                {
                    resId: 50,
                    fontFamily: 'zh161hjhczpt',
                    effectVariant3D: {
                        resId: 50,
                        rotation: {
                            x: 0.52,
                            y: -0.026,
                        },
                        depth: 74,
                        bevel: {
                            thickness: 1,
                            size: 5,
                            segments: 8,
                        },
                        materials: {
                            front: {
                                type: 'env_map',
                                metalness: 0.364,
                                roughness: 0.352,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 63, g: 63, b: 63 },
                            },
                            side: {
                                type: 'env_map',
                                metalness: 0.352,
                                roughness: 0,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 198, g: 198, b: 198 },
                            },
                        },
                        isUnDirectionalLight: true,
                    },
                },
                {
                    resId: 4,
                    fontFamily: 'zh100hffxft',
                    effectVariant3D: {
                        resId: 4,
                        rotation: {
                            x: 0.06,
                            y: -0.56,
                        },
                        depth: 80,
                        materials: {
                            front: {
                                color: { r: 255, g: 51, b: 54, a: 1 },
                            },
                            side: {
                                color: { r: 255, g: 150, b: 150, a: 1 },
                            },
                        },
                    },
                },
                {
                    resId: 16,
                    fontFamily: 'zh109hfgxzt',
                    effectVariant3D: {
                        resId: 16,
                        rotation: {
                            x: -0.32,
                            y: 0.01,
                        },
                        depth: 100,
                        materials: {
                            front: {
                                color: { r: 0, g: 0, b: 0, a: 1 },
                            },
                            side: {
                                color: { r: 220, g: 20, b: 20, a: 1 },
                            },
                        },
                    },
                },
                {
                    resId: 17,
                    fontFamily: 'zh199hmqcct',
                    effectVariant3D: {
                        resId: 17,
                        rotation: {
                            x: -0.66,
                            y: 0.01,
                        },
                        depth: 100,
                        materials: {
                            front: {
                                color: { r: 0, g: 27, b: 25, a: 1 },
                            },
                            side: {
                                color: { r: 139, g: 255, b: 0, a: 1 },
                            },
                        },
                    },
                },

                {
                    resId: 48,
                    fontFamily: 'pmzdbtt',
                    effectVariant3D: {
                        resId: 48,
                        rotation: {
                            x: 0.02,
                            y: 0.47,
                        },
                        depth: 12,
                        bevel: {
                            thickness: 1,
                            size: 5,
                            segments: 8,
                        },
                        materials: {
                            front: {
                                type: 'env_map',
                                metalness: 0.435,
                                roughness: 0.129,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 0, g: 144, b: 255 },
                            },
                            side: {
                                type: 'env_map',
                                metalness: 0.635,
                                roughness: 0.176,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 0, g: 181, b: 255 },
                            },
                        },
                        isUnDirectionalLight: true,
                    },
                },
                {
                    resId: 30,
                    fontFamily: 'fnParalinesRegular',
                    effectVariant3D: {
                        resId: 30,
                        rotation: {
                            x: -0.22,
                            y: -0.59,
                        },
                        depth: 100,
                        materials: {
                            front: {
                                color: { r: 255, g: 255, b: 255, a: 1 },
                            },
                            side: {
                                color: { r: 0, g: 0, b: 0, a: 1 },
                            },
                        },
                    },
                },
                {
                    resId: 22,
                    fontFamily: 'zh57hcxh',
                    effectVariant3D: {
                        resId: 22,
                        rotation: {
                            x: 0.4,
                            y: 0.01,
                        },
                        depth: 100,
                        materials: {
                            front: {
                                color: { r: 255, g: 56, b: 83 },
                            },
                            side: {
                                type: 'gradient_color',
                                color: { r: 255, g: 56, b: 83 },
                                color_end: { r: 255, g: 255, b: 255 },
                                angle: 180,
                                center: 0.5,
                            },
                        },
                    },
                },
                {
                    resId: 13,
                    fontFamily: 'zh41hcxt',
                    effectVariant3D: {
                        resId: 13,
                        rotation: {
                            x: -0.2,
                            y: -0.65,
                        },
                        depth: 100,
                        materials: {
                            front: {
                                color: { r: 0, g: 10, b: 5, a: 1 },
                            },
                            side: {
                                color: { r: 17, g: 189, b: 137, a: 1 },
                            },
                        },
                    },
                },
                {
                    resId: 52,
                    fontFamily: 'zh164hfyh',
                    effectVariant3D: {
                        resId: 52,
                        rotation: {
                            x: 0.06,
                            y: 0.54,
                        },
                        depth: 100,
                        bevel: {
                            thickness: 1,
                            size: 5,
                            segments: 8,
                        },
                        materials: {
                            front: {
                                type: 'env_map',
                                metalness: 0.741,
                                roughness: 0.164,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 255, g: 255, b: 255 },
                            },
                            side: {
                                type: 'env_map',
                                metalness: 1,
                                roughness: 0.258,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 53, g: 28, b: 28 },
                            },
                        },
                        isUnDirectionalLight: true,
                    },
                },
                {
                    resId: 3,
                    fontFamily: 'zh100hffxft',
                    effectVariant3D: {
                        resId: 3,
                        rotation: {
                            x: 0.12,
                            y: 0.78,
                        },
                        depth: 80,
                        materials: {
                            front: {
                                color: { r: 255, g: 142, b: 193, a: 1 },
                            },
                            side: {
                                color: { r: 78, g: 55, b: 203, a: 1 },
                            },
                        },
                    },
                },
                {
                    resId: 5,
                    fontFamily: 'zh100hffxft',
                    effectVariant3D: {
                        resId: 5,
                        rotation: {
                            x: 0.12,
                            y: 0.78,
                        },
                        depth: 80,
                        materials: {
                            front: {
                                color: { r: 24, g: 232, b: 129, a: 1 },
                            },
                            side: {
                                color: { r: 26, g: 95, b: 171, a: 1 },
                            },
                        },
                    },
                },
                {
                    resId: 29,
                    fontFamily: 'zh39fzklt',
                    effectVariant3D: {
                        resId: 29,
                        rotation: {
                            x: -0.04,
                            y: -0.05,
                        },
                        depth: 100,
                        materials: {
                            front: {
                                color: { r: 211, g: 75, b: 75, a: 1 },
                            },
                            side: {
                                color: { r: 252, g: 186, b: 186, a: 1 },
                            },
                        },
                    },
                },
                {
                    resId: 8,
                    fontFamily: 'zh100hffxft',
                    effectVariant3D: {
                        resId: 8,
                        rotation: {
                            x: 0.12,
                            y: 0.78,
                        },
                        depth: 80,
                        materials: {
                            front: {
                                type: 'texture',
                                color: { r: 255, g: 251, b: 239 },
                                color_end: { r: 245, g: 166, b: 16 },
                                texture: '//s.tuguaishou.com/image/editor/7.png',
                                repeat: 0.01,
                            },
                            side: {
                                type: 'texture',
                                color: { r: 255, g: 251, b: 239 },
                                color_end: { r: 245, g: 166, b: 16 },
                                texture: '//s.tuguaishou.com/image/editor/5.png',
                                repeat: 0.01,
                            },
                        },
                    },
                },

                {
                    resId: 45,
                    fontFamily: 'zh109hfgxzt',
                    effectVariant3D: {
                        resId: 45,
                        rotation: {
                            x: -0.079,
                            y: -0.58,
                        },
                        depth: 100,
                        bevel: {
                            thickness: 1,
                            size: 1.4,
                            segments: 8,
                        },
                        materials: {
                            front: {
                                type: 'env_map',
                                metalness: 0.258,
                                roughness: 0,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 96, g: 18, b: 0 },
                            },
                            side: {
                                type: 'env_map',
                                metalness: 0,
                                roughness: 0,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 174, g: 1, b: 1 },
                            },
                        },
                        isUnDirectionalLight: true,
                    },
                },
                {
                    resId: 23,
                    fontFamily: 'zh164hfyh',
                    effectVariant3D: {
                        resId: 23,
                        rotation: {
                            x: 0.28,
                            y: 0.83,
                        },
                        depth: 100,
                        materials: {
                            front: {
                                color: { r: 0, g: 234, b: 255 },
                            },
                            side: {
                                type: 'gradient_color',
                                color: { r: 15, g: 103, b: 255 },
                                color_end: { r: 219, g: 0, b: 217 },
                                angle: 270,
                                center: 0.5,
                            },
                        },
                    },
                },
                {
                    resId: 11,
                    fontFamily: 'zh100hffxft',
                    effectVariant3D: {
                        resId: 11,
                        rotation: {
                            x: 0.12,
                            y: 0.78,
                        },
                        depth: 80,
                        materials: {
                            front: {
                                type: 'gradient_color',
                                color: { r: 255, g: 251, b: 239 },
                                color_end: { r: 245, g: 166, b: 16 },
                                angle: 180,
                                center: 0.5,
                            },
                            side: {
                                type: 'gradient_color',
                                color: { r: 255, g: 111, b: 216 },
                                color_end: { r: 56, g: 19, b: 194 },
                                angle: 180,
                                center: 0.5,
                            },
                        },
                    },
                },
                {
                    resId: 9,
                    fontFamily: 'zh100hffxft',
                    effectVariant3D: {
                        resId: 9,
                        rotation: {
                            x: 0.12,
                            y: 0.78,
                        },
                        depth: 80,
                        materials: {
                            front: {
                                type: 'texture',
                                color: { r: 255, g: 251, b: 239 },
                                color_end: { r: 245, g: 166, b: 16 },
                                texture: '//s.tuguaishou.com/image/editor/4.png',
                                repeat: 0.01,
                            },
                            side: {
                                type: 'texture',
                                color: { r: 255, g: 251, b: 239 },
                                color_end: { r: 245, g: 166, b: 16 },
                                texture: '//s.tuguaishou.com/image/editor/5.png',
                                repeat: 0.01,
                            },
                        },
                    },
                },
                {
                    resId: 6,
                    fontFamily: 'zh100hffxft',
                    effectVariant3D: {
                        resId: 6,
                        rotation: {
                            x: 0.06,
                            y: -0.56,
                        },
                        depth: 80,
                        materials: {
                            front: {
                                type: 'texture',
                                color: { r: 255, g: 251, b: 239 },
                                color_end: { r: 245, g: 166, b: 16 },
                                texture: '//s.tuguaishou.com/image/editor/2.png',
                                repeat: 0.01,
                            },
                            side: {
                                type: 'texture',
                                color: { r: 255, g: 251, b: 239 },
                                color_end: { r: 245, g: 166, b: 16 },
                                texture: '//s.tuguaishou.com/image/editor/4.png',
                                repeat: 0.01,
                            },
                        },
                    },
                },
                {
                    resId: 54,
                    fontFamily: 'zh59hcch',
                    effectVariant3D: {
                        resId: 54,
                        rotation: {
                            x: 0.12,
                            y: 6.19,
                        },
                        depth: 62,
                        bevel: {
                            thickness: 1,
                            size: 7,
                            segments: 8,
                        },
                        materials: {
                            front: {
                                type: 'env_map',
                                metalness: 0.247,
                                roughness: 0,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 255, g: 230, b: 191 },
                            },
                            side: {
                                type: 'env_map',
                                metalness: 0.576,
                                roughness: 0,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 175, g: 160, b: 115 },
                            },
                        },
                        isUnDirectionalLight: true,
                    },
                },
                {
                    resId: 14,
                    fontFamily: 'zh179hzkbbt',
                    effectVariant3D: {
                        resId: 14,
                        rotation: {
                            x: 0.86,
                            y: 0.01,
                        },
                        depth: 100,
                        materials: {
                            front: {
                                color: { r: 255, g: 255, b: 255, a: 1 },
                            },
                            side: {
                                color: { r: 78, g: 0, b: 224, a: 1 },
                            },
                        },
                    },
                },
                {
                    resId: 18,
                    fontFamily: 'zh155hfqt',
                    effectVariant3D: {
                        resId: 18,
                        rotation: {
                            x: -0.02,
                            y: 0.05,
                        },
                        depth: 100,
                        materials: {
                            front: {
                                color: { r: 97, g: 13, b: 255, a: 1 },
                            },
                            side: {
                                color: { r: 15, g: 255, b: 254, a: 1 },
                            },
                        },
                    },
                },
                {
                    resId: 7,
                    fontFamily: 'zh100hffxft',
                    effectVariant3D: {
                        resId: 7,
                        rotation: {
                            x: 0.06,
                            y: -0.56,
                        },
                        depth: 80,
                        materials: {
                            front: {
                                type: 'gradient_color',
                                color: { r: 255, g: 111, b: 216 },
                                color_end: { r: 56, g: 19, b: 194 },
                                angle: 180,
                                center: 0.5,
                            },
                            side: {
                                type: 'gradient_color',
                                color: { r: 239, g: 240, b: 255 },
                                color_end: { r: 16, g: 101, b: 245 },
                                angle: 180,
                                center: 0.5,
                            },
                        },
                    },
                },
                {
                    resId: 10,
                    fontFamily: 'zh100hffxft',
                    effectVariant3D: {
                        resId: 10,
                        rotation: {
                            x: 0.06,
                            y: -0.56,
                        },
                        depth: 80,
                        materials: {
                            front: {
                                type: 'gradient_color',
                                color: { r: 255, g: 239, b: 251 },
                                color_end: { r: 255, g: 156, b: 163 },
                                angle: 180,
                                center: 0.5,
                            },
                            side: {
                                type: 'gradient_color',
                                color: { r: 255, g: 255, b: 255 },
                                color_end: { r: 0, g: 0, b: 0 },
                                angle: 180,
                                center: 0.5,
                            },
                        },
                    },
                },

                {
                    resId: 47,
                    fontFamily: 'zh59hcch',
                    effectVariant3D: {
                        resId: 47,
                        rotation: {
                            x: 0.258,
                            y: 0.5,
                        },
                        depth: 100,
                        bevel: {
                            thickness: 1,
                            size: 3,
                            segments: 8,
                        },
                        materials: {
                            front: {
                                type: 'env_map',
                                metalness: 1,
                                roughness: 0.235,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 95, g: 0, b: 0 },
                            },
                            side: {
                                type: 'env_map',
                                metalness: 1,
                                roughness: 0.188,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 121, g: 0, b: 0 },
                            },
                        },
                        isUnDirectionalLight: true,
                    },
                },
                {
                    resId: 31,
                    fontFamily: 'fnPostNoBillsColomboExtraBold',
                    effectVariant3D: {
                        resId: 31,
                        rotation: {
                            x: 0.1,
                            y: 0.73,
                        },
                        depth: 100,
                        materials: {
                            front: {
                                color: { r: 89, g: 255, b: 254, a: 1 },
                            },
                            side: {
                                color: { r: 94, g: 70, b: 255, a: 1 },
                            },
                        },
                    },
                },
                {
                    resId: 15,
                    fontFamily: 'zh44hkxyh',
                    effectVariant3D: {
                        resId: 15,
                        rotation: {
                            x: -0.18,
                            y: -0.43,
                        },
                        depth: 100,
                        materials: {
                            front: {
                                color: { r: 255, g: 254, b: 52, a: 1 },
                            },
                            side: {
                                color: { r: 230, g: 180, b: 5, a: 1 },
                            },
                        },
                    },
                },
                {
                    resId: 51,
                    fontFamily: 'zh184hyycyh',
                    effectVariant3D: {
                        resId: 51,
                        rotation: {
                            x: 0.1,
                            y: -0.48,
                        },
                        depth: 100,
                        bevel: {
                            thickness: 1,
                            size: 2,
                            segments: 8,
                        },
                        materials: {
                            front: {
                                type: 'env_map',
                                metalness: 0.223,
                                roughness: 0.352,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 122, g: 212, b: 149 },
                            },
                            side: {
                                type: 'env_map',
                                metalness: 0.517,
                                roughness: 0.047,
                                envMapInfo: {
                                    envMapId: 3,
                                    rt_envMap: {
                                        pz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nz: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        nx: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        px: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        py: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                        ny: 'https://s.tuguaishou.com/3D/env/env_8.png',
                                    },
                                },
                                color: { r: 12, g: 67, b: 5 },
                            },
                        },
                        isUnDirectionalLight: true,
                    },
                },
                {
                    resId: 21,
                    fontFamily: 'zh44hkxyh',
                    effectVariant3D: {
                        resId: 21,
                        rotation: {
                            x: 0.1,
                            y: 0.79,
                        },
                        depth: 100,
                        materials: {
                            front: {
                                color: { r: 132, g: 67, b: 255, a: 1 },
                            },
                            side: {
                                type: 'gradient_color',
                                color: { r: 132, g: 67, b: 255 },
                                color_end: { r: 255, g: 255, b: 255 },
                                angle: 180,
                                center: 0.5,
                            },
                        },
                    },
                },
            ],
            updateTime: new Date().getTime(),
            scrollbarHeight: 590,
            specificWordPreviewStyle: '',
        };

        assetManager.getSpecificWordList().then((data) => {
            data.json().then((resultData) => {
                if (resultData.stat == 1) {
                    this.setState({
                        // specificWordList: resultData.msg,
                        updateTime: new Date().getTime(),
                    });
                }
            });
        });
    }

    // 拖拽字体事件
    dragAddEvent(
        assetProps: { fontSize: number; fontWeight: string; ranking: string | number },
        id: number,
        e: MouseEvent,
    ): void {
        assetProps = {
            fontSize: 24,
            fontWeight: 'normal',
            ranking: '0',
        };
        let value;
        const type = 'text';
        if (value == undefined || value == '') {
            value = '双击编辑文字';
        }
        const asset = JSON.parse(JSON.stringify(assetTemplate.text));
        const { canvas } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }); // canvasStore.getState().onCanvasPainted;
        const { recommendFont } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.RecommendManage,
        }); // canvasStore.getState().recommendManageRedux;
        let recommendFontFlag = 0;

        let width = 200,
            height = 50;

        Object.assign(asset.meta, {
            type: 'text',
            addOrigin: 'specificWord',
        });
        Object.assign(asset.attribute, {
            text: [value],
            fontSize: assetProps.fontSize,
            fontWeight: assetProps.fontWeight,
            textAlign: 'center',
            width: width,
            opacity: 100,
            lineHeight: 13,
            letterSpacing: 0,
            fontFamily: 'fnsyhtRegular',
            color: { r: 74, g: 74, b: 74, a: 1 },
            effect: id ? id + '@0' : '',
            effectVariant: {
                state: 'add',
            },
        });
        try {
            if (recommendFont[assetProps.ranking]) {
                width = parseInt(recommendFont[assetProps.ranking]['font_size']) * 9;
                Object.assign(asset.attribute, {
                    fontSize: parseInt(recommendFont[assetProps.ranking]['font_size']),
                    width: width,
                });
                recommendFontFlag = 1;
            }
        } catch (err) {}

        if (!recommendFontFlag) {
            let tempFontSize,
                // eslint-disable-next-line prefer-const
                fontNum = 6;

            if (assetProps.ranking == 2) {
                tempFontSize = (canvas.width * 1) / 3 / fontNum;
            } else if (assetProps.ranking == 1) {
                tempFontSize = (canvas.width * 1) / 2 / fontNum;
            } else {
                tempFontSize = (canvas.width * 2) / 3 / fontNum;
            }
            // @ts-ignore
            tempFontSize = parseInt(tempFontSize);
            width = tempFontSize * 9;
            Object.assign(asset.attribute, {
                fontSize: tempFontSize,
                width: width,
            });

            height = tempFontSize;
        }

        Object.assign(asset.transform, {
            posX: ((canvas.width - width) / 2) * canvas.scale,
            posY: ((canvas.height - height) / 2) * canvas.scale,
        });
        emitter.emit('ListDragAddDown', asset, type, e);
    }

    /**
     * 添加特效字效果
     */
    specificWordItemClickEvent(
        param: {
            [x: string]: any;
            id: number;
            fontFamily: string;
            textType?: '3D' | 'specificWord';
            effectVariant3D?: IEffectVariant3D;
        },
        e: MouseEvent,
    ): void {
        AssetAddListener.addText(
            {
                fontSize: 24,
                fontWeight: 'normal',
                ranking: '0',
            },
            {
                ...param,
                fontFamily: this.state.specificWordList.find((v) => v.resId == param.id).fontFamily,
                effectVariant3D: this.state.specificWordList.find((v) => v.resId == param.id).effectVariant3D, // 在使用时会 cloneDeep
            },
        );
        e.stopPropagation();
        (e as any)?.nativeEvent.stopImmediatePropagation();
        return;
    }

    windowResize(th: this, innerHeight: number): void {
        let scrollbarHeight = innerHeight,
            // eslint-disable-next-line prefer-const
            rightLayoutDom = document.getElementsByClassName('rightLayout');

        if (rightLayoutDom) {
            // @ts-ignore
            scrollbarHeight = rightLayoutDom[0].offsetHeight - 120;
        }

        th.setState({
            scrollbarHeight: scrollbarHeight,
        });
    }

    // onMouseOver字体事件
    specificWordPreviewMouseOverEvent(id: number, e: React.ChangeEvent<HTMLInputElement>): void {
        const toolLayout: HTMLDivElement = document.querySelector('.toolLayout');
        const rootLayout: HTMLDivElement = document.querySelector('.rightLayout');

        if (rootLayout) {
            if (rootLayout.offsetWidth != 432) {
                this.setState({ specificWordPreviewStyle: '' });
                return;
            }
        }
        const maxHeight = toolLayout.offsetHeight;

        const scrollTop = this.scrollBar.scrollTop;

        const offsetTop = e.target.offsetTop - scrollTop - 100;

        let PreviewTop = 0;
        if (offsetTop < 0) {
            PreviewTop = 2;
        } else if (offsetTop + 280 > maxHeight) {
            PreviewTop = maxHeight - 280 - 2;
        } else {
            PreviewTop = offsetTop;
        }
        this.setState({
            specificWordPreviewStyle: {
                top: PreviewTop,
                backgroundImage: `url("https://s.tuguaishou.com/specificWordPreview/3D-${id}.png?t=1555603")`,
            },
        });
    }

    specificWordPreviewMouseLeave(): void {
        this.setState({ specificWordPreviewStyle: '' });
    }

    stopPropagation(e: MouseEvent): void {
        e.stopPropagation();
        (e as any)?.nativeEvent.stopImmediatePropagation();
    }

    componentDidMount(): void {
        this.windowResize(this, document.body.clientHeight - 50);
    }

    render(): JSX.Element {
        const { specificWordList, updateTime, scrollbarHeight, specificWordPreviewStyle } = this.state;

        return (
            <div className="specificWord3D">
                <div className="addTemplateText">
                    <div className="specificWordAddArea" onWheel={this.stopPropagation.bind(this)}>
                        <div
                            className="specificWordAddAreaContent"
                            ref={(node) => {
                                this.scrollBar = node;
                            }}
                            key={'specificWordList-' + updateTime}
                        >
                            <div style={{ display: 'inline-block' }}>
                                {specificWordList.map((v) => (
                                    <div
                                        key={v.resId}
                                        className="specificWordItem"
                                        onMouseOver={this.specificWordPreviewMouseOverEvent.bind(this, v.resId)}
                                        // onMouseDown={this.dragAddEvent.bind(this, this.titleProp,'')}
                                        onMouseLeave={this.specificWordPreviewMouseLeave.bind(this)}
                                        onClick={this.specificWordItemClickEvent.bind(this, {
                                            id: v.resId,
                                            textType: '3D',
                                            fontFamily: v.fontFamily,
                                        })}
                                    >
                                        <LazyLoad offset={200}>
                                            <img
                                                src={
                                                    'https://js.tuguaishou.com/specificWordPreview/3D-' +
                                                    v.resId +
                                                    '.png?t=155560'
                                                }
                                                alt={v.resId}
                                            />
                                        </LazyLoad>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>

                {specificWordPreviewStyle && (
                    <div
                        className={'specificWordPreviewWindow'}
                        style={specificWordPreviewStyle}
                        onMouseLeave={this.specificWordPreviewMouseLeave.bind(this)}
                    ></div>
                )}
            </div>
        );
    }
}
