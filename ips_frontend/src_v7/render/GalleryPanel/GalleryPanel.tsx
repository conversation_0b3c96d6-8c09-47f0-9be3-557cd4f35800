import React, { Component } from 'react';

// import {GroupWordPanel} from '../GroupWordPanel/GroupWordPanel';
// import {ArtWordPanel} from '../ArtWordPanel/ArtWordPanel';
// import {PngPanel} from '../PngPanel/PngPanel';
// import {PicPanel} from '../PicPanel/PicPanel';
// import {PhotographyPanel} from '../PhotographyPanel/PhotographyPanel';
// import {PersonPanel} from '../PersonPanel/PersonPanel';
// import {BackgroundPanel} from '../Background';
import { assetManager } from '@component/AssetManager';
import { GroupWordPanel } from '@v7_render/GroupWordPanel';
import { ArtWordPanel } from '@v7_render/ArtWordPanel';
import { PngPanel } from '@v7_render/PngPanel';
import { PicPanel } from '@v7_render/PicPanel';
import { BackgroundPanel } from '@v7_render/BackgroundPanel';
import { EmojiPanel } from '@v7_render/EmojiPanel';
import { PersonPanel } from '@v7_render/PersonPanel';
import { PhotographyPanel } from '@v7_render/PhotographyPanel';
import { ETool } from '@v7_logic/Enum';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { env } from '@editorConfig/env';
import { isUeTeam } from '@v7_utils/webSource';



export function DisplayGalleryTool(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.GALLERY, nav: ETool.GALLERY};
}
interface propsStruct {
    isGallery?: boolean;
    isActive?: boolean;
}

interface stateStruct {
    navType: string;
}

export class GalleryPanel extends Component<propsStruct, stateStruct> {
    galleryNav: { iconClass: string; name: string; navType: string }[];
    constructor(props: propsStruct) {
        super(props);

        this.state = {
            navType: 'background',
        };

        this.galleryNav = [
            // {iconClass: 'icon icon-groupWord', name: '文案排版', navType: 'groupWord'},
            // {iconClass: 'icon icon-png', name: '贴纸', navType: 'png'},
            // {iconClass: 'icon icon-pic', name: '图片', navType: 'pic'},
            { iconClass: 'icon icon-background', name: '背景', navType: 'background' },
            // {iconClass: 'icon icon-photography', name: '摄影', navType: 'photography'},
            // {iconClass: 'icon icon-person', name: '人物', navType: 'person'},
            // {iconClass: 'icon icon-artWord', name: '艺术字', navType: 'artWord'},
            // {iconClass: 'icon icon-artWord', name: '表情包', navType: 'emoji'},
        ];
    }

    galleryNavItemClickEvent(type: string, e: MouseEvent): void {
        const { navType } = this.state;
        if (type != navType) {
            this.setState({
                navType: type,
            });
        }
        assetManager.setPv_new(
            type == 'background'
                ? 2073
                : type == 'photography'
                ? 2074
                : type == 'person'
                ? 2075
                : type == 'artWord'
                ? 2076
                : type == 'emoji'
                ? 339
                : 2045,
        );
        assetManager.setPv_new(2045);
        if (e) {
            this.stopPropagation(e);
        }
    }

    stopPropagation(e: MouseEvent): void {
        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }
    onGoCorporateMembers(e: MouseEvent): void {
        assetManager.setPagePv_new(3254);
        const { info } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.InfoManage,
        });
        if(info?.is_company_temp === 1 || env.teamTemplate || isUeTeam ){
            window.open(
                'https://818ps.com/dash/firm-intro?origin=font-authorize-btn&route_id=16328996965437&route=1,&after_route=1',
            );
        }else{
            const {user} = storeAdapter.getStore({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            assetManager.getUserInfo()
            .then((data) => data.json())
            .then(userInfo => {
                if(user.userId == 0 || userInfo.commercial_type == -1 || userInfo.commercial_type == 0) {
                    window.open(
                        "https://818ps.com/dash/vip-spec-non-commercial?origin=skip_common_vipSpec"
                    )
                } else if(userInfo.commercial_type == 1 || userInfo.commercial_type == 2) {
                    window.open(
                        "https://818ps.com/dash/vip-spec-commercial?classify=1&origin=skip_advanced_vipSpec&route_id=16007403774149&route=1,86&after_route=1_86"
                    )
                }
            })
            // if(user.isJumpCommercial === 1){
            //     window.open(
            //         'https://818ps.com/dash/vip-spec-commercial?classify=1&origin=VipSpec&route_id=16007403774149&route=1,86&after_route=1_86',
            //     );
            // }else{
            //     window.open(
            //         'https://818ps.com/dash/vip-spec?classify=1&origin=VipSpec&route_id=16007403774149&route=1,86&after_route=1_86',
            //     );
            // }
        }
        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }

    onMouseEnterEvent(e: MouseEvent): void {
        assetManager.setPagePv_new(3262);
        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }

    onMouseLeaveEvent(e: MouseEvent): void {
        assetManager.setPagePv_new(3261);
        e.stopPropagation();
        (e as any)?.nativeEvent.stopPropagation();
    }
    render(): JSX.Element {
        const { navType } = this.state;

        return (
            <div className="galleryPanel">
                {/* <div className='galleryNav' style={{paddingTop: navType == 'groupWord' ? '16px' : ''}}> */}
                {/* <div className='galleryNav' style={{paddingTop: '16px'}}>
                    {
                        this.galleryNav.map((item, index) => {
                            return <div
                                className={classNames('galleryNavItem', item.navType, {active: navType == item.navType})}
                                key={index}
                                onClick={this.galleryNavItemClickEvent.bind(this, item.navType)}
                            >{item.name}</div>
                        })
                    }
                </div> */}
                {/* <div
                    className="company_use"
                    onClick={this.onGoCorporateMembers.bind(this)}
                    onMouseLeave={this.onMouseLeaveEvent.bind(this)}
                    onMouseEnter={this.onMouseEnterEvent.bind(this)}
                >
                    <i className="iconfont icon-shang"></i>
                    <span>商用会员所有背景可放心商用</span>
                </div> */}

                {navType == 'groupWord' && <GroupWordPanel isActive={true} />}

                {navType == 'artWord' && <ArtWordPanel isActive={true} isGallery={true} />}
                {navType == 'png' && <PngPanel isActive={true} isGallery={true} />}
                {navType == 'pic' && <PicPanel isActive={true} isGallery={true} />}
                {navType == 'background' && <BackgroundPanel active={true} isGallery={true} />}
                {navType == 'emoji' && <EmojiPanel active={true} />}
                {navType == 'person' && <PersonPanel active={true} />}
                {navType == 'photography' && <PhotographyPanel active={true} />}
            </div>
        );
    }
}
