import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { ToolCateGoryItem } from '../type';
import { AssetLogic } from '@v7_logic/AssetLogic';
import { AssetHelper } from '@v7_logic/AssetHelper';
import { cloneDeep, debounce } from 'lodash-es';
import { assetManager } from '@src/userComponentV6.0/AssetManager';
import { UpdateAsset } from '@v7_logic/AssetLogic';
import { emitter } from '@src/userComponentV6.0/Emitter';
import { IAsset } from '@v7_logic/Interface';
import { TAsset } from '@v7_render/AssetPanel/Material/type';
import { EventSubscription } from 'fbemitter';
import { GroupAndMultipleSelectLogic } from '@v7_logic/GroupAndMultipleSelectLogic';
import { AIGeneralLogic } from '@v7_logic/AIToolLogic/AIGeneralLogic';

export class ImageToolLogic {
    public static frameInitEndListener: EventSubscription;
    public static addFrameTimer: NodeJS.Timeout;
    public static changeImageOpacity(value: number) {
        const { toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        AssetLogic.updateAssetImage({
            assetIndex: toolPanel.asset_index,
            attribute: 'attribute',
            key: 'opacity',
            value: value,
            // className:toolPanel.asset.meta.className,
            fun_name: 'UPDATE_OPACITY',
        });
    }
    // ················裁剪事件start··················//
    /**
     * 裁剪框点击事件
     */
    public static containerItemClick(item?: ToolCateGoryItem, isMarked?: boolean) {
        const { toolPanel, work, pageInfo, canvas, pageAttr, rt_canvas_render_mode } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (toolPanel.asset.attribute.container) {
            if (!toolPanel.asset.attribute.container.isEdit) {
                AssetLogic.updateAssetContainerIsEdit({
                    isEdit: true,
                    asset: toolPanel.asset,
                    assetIndex: toolPanel.asset_index,
                    className: toolPanel.asset.meta.className,
                });
            }
        }
        if (!item) return;
        let element = {
            id: item.id,
            source_key: item.source_key,
            width: Number(item.width),
            height: Number(item.height),
            source_width: item.width,
            source_height: item.height,
            picUrl: 'loading',
            posX: 0,
            posY: 0,
            isEdit: true,
            markedReplace: isMarked,
        };

        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        if (!asset) {
            return;
        }
        const isEdit = JSON.stringify(asset.attribute.container && asset.attribute.container.isEdit);
        const tempAsset = cloneDeep(asset);
        if (isEdit) {
            tempAsset.transform.posX = tempAsset.transform.posX + tempAsset.attribute.container.posX;
            tempAsset.transform.posY = tempAsset.transform.posY + tempAsset.attribute.container.posY;
        }
        let skewX = 0;
        let skewY = 0;
        const pageType = pageAttr.pageInfo?.[pageInfo.pageNow]?.type;
        if (pageType !== 'board') {
            if (tempAsset.attribute.width + tempAsset.transform.posX <= canvas.width) {
                if (tempAsset.transform.posX < 0) {
                    skewX = -tempAsset.transform.posX;
                    tempAsset.attribute.width = tempAsset.attribute.width + tempAsset.transform.posX;
                }
            } else {
                if (tempAsset.transform.posX >= 0) {
                    tempAsset.attribute.width = canvas.width - tempAsset.transform.posX;
                } else {
                    skewX = -tempAsset.transform.posX;
                    tempAsset.attribute.width = canvas.width;
                }
            }

            if (tempAsset.attribute.height + tempAsset.transform.posY <= canvas.height) {
                if (tempAsset.transform.posY < 0) {
                    tempAsset.attribute.height = tempAsset.attribute.height + tempAsset.transform.posY;
                    skewY = -tempAsset.transform.posY;
                }
            } else {
                if (tempAsset.transform.posY >= 0) {
                    tempAsset.attribute.height = canvas.height - tempAsset.transform.posY;
                } else {
                    skewY = -tempAsset.transform.posY;
                    tempAsset.attribute.height = canvas.height;
                }
            }
        }

        let tempWidth = tempAsset.attribute.width,
            tempHeight = (tempWidth * Number(element.source_height)) / Number(element.source_width),
            posXWidth,
            posYHeight;

        if (asset.attribute.container?.id) {
            tempWidth = tempAsset.attribute.container.viewBoxWidth || tempWidth;
            tempHeight = (tempWidth * Number(element.source_height)) / Number(element.source_width);

            posXWidth = Math.abs(asset.attribute.container.posX);
            posYHeight = Math.abs(asset.attribute.container.posY);
        } else if (!(asset.attribute.container && asset.attribute.container.isEdit)) {
            if (tempWidth > tempAsset.attribute.height) {
                tempWidth = tempAsset.attribute.height;
                tempHeight = (tempWidth * Number(element.source_height)) / Number(element.source_width);
            }
            posXWidth = (tempAsset.attribute.width - tempWidth) / 2 + skewX;
            posYHeight = (tempAsset.attribute.height - tempHeight) / 2 + skewY;
        } else {
            tempWidth = tempAsset.attribute.container.viewBoxWidth || tempWidth;
            tempHeight = (tempWidth * Number(element.source_height)) / Number(element.source_width);

            posXWidth = Math.abs(asset.attribute.container.posX);
            posYHeight = Math.abs(asset.attribute.container.posY);
        }

        element = Object.assign(element, {
            width: tempWidth,
            height: tempHeight,
            posX: -posXWidth,
            posY: -posYHeight,
            originContainer: {
                isHave: !!tempAsset.attribute.container,
                data: tempAsset.attribute.container,
            },
        });
        assetManager.setPv_new(4152, {
            additional: {
                s0: item.id,
            },
        });

        AssetLogic.updateAssetContainer({
            container: element,
            asset: toolPanel.asset,
            assetIndex: toolPanel.asset_index,
        });
        if (!tempAsset.attribute.container?.id) {
            UpdateAsset.updateAssets('UPDATE_ASSET', [
                {
                    index: toolPanel.asset_index,
                    changes: {
                        transform: {
                            posX: parseFloat(String(asset.transform.posX)) + posXWidth,
                            posY: parseFloat(String(asset.transform.posY)) + posYHeight,
                        },
                    },
                },
            ]);
        }

        // this.removeImageEffect(true);
    }
    public static containerRemove() {
        const { toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        assetManager.setPv_new(2266);
        if (toolPanel.asset != '' && toolPanel.asset.attribute.container && toolPanel.asset.attribute.container.id) {
            // this.props.deleteAssetContainer();
            AssetLogic.delAssetContainer();
        }
    }
    // ················裁剪事件over··················//

    // ················滤镜事件start··················//
    public static getFilterClickEvent(id: string, classNames?: string[]) {
        const { isEffectTemplate, isDesigner, toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (!id) {
            emitter.emit('onClickFilter', { slide: false, click: true, isOriginal: true });
            // canvasStore.dispatch(paintOnCanvas('UPDATE_IMAGE_FILTER', {
            //   filters,
            // }));
            return this.resetImageFilter(classNames);
        } else {
            emitter.emit('onClickFilter', { slide: false, click: true });
            assetManager.getFilterInfo(id).then((data) => {
                data.json().then((filtersInfo) => {
                    if (filtersInfo.stat == 1) {
                        const filters = filtersInfo.msg.filters;
                        filters.resId = id;
                        // canvasStore.dispatch(paintOnCanvas('UPDATE_IMAGE_FILTER', {
                        //   filters: filtersInfo.msg.filters,
                        // }));
                        if (classNames) {
                            GroupAndMultipleSelectLogic.updateImageAssetsFilter(filters, classNames);
                            assetManager.setPv_new(7771, {
                                additional: {
                                    s0: toolPanel.assets?.length > 0 ? 'multipleSelect' : 'group',
                                },
                            });
                        } else {
                            AssetLogic.updateAssetImageFilter({
                                filters: filters,
                            });
                            assetManager.setPv_new(3348, { additional: { s0: id } });
                            if (isEffectTemplate && !isDesigner) {
                                assetManager.setPv_new(7714);
                            }
                        }
                    }
                });
            });
        }
    }
    // 重置图形滤镜
    public static resetImageFilter(classNames?: string[]) {
        const filters = {
            brightness: 0,
            saturate: 0,
            contrast: 0,
            blur: 0,
            sharpen: 0,
            hue: 0,
            'gamma-r': 1,
            'gamma-b': 1,
            'gamma-g': 1,
            strong: 1,
            resId: undefined as any,
        };
        const { toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (classNames) {
            GroupAndMultipleSelectLogic.updateImageAssetsFilter(filters, classNames);
            assetManager.setPv_new(7771, {
                additional: {
                    s0: toolPanel.assets?.length > 0 ? 'multipleSelect' : 'group',
                },
            });
        } else {
            AssetLogic.updateAssetImageFilter({
                filters: filters,
            });
            assetManager.setPv_new(3348, { additional: { s0: '' } });
        }
    }
    // 调节滤镜强度
    public static changeFilterStrong = debounce((value: number, className?: string) => {
        const { toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        AssetLogic.updateAssetFilter({
            assetIndex: toolPanel.asset_index,
            filterType: 'strong',
            value: value,
            fun_name: 'UPDATE_FILTER_STRONG',
            className,
        });
    }, 60);
    // ················滤镜事件over··················//

    // ················图片特效事件start··················//

    // 选择图片特效
    public static addImageEffect(imageEffect: ToolCateGoryItem) {
        const { toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const newImageEffect = cloneDeep(imageEffect);
        //   this.setState({
        //     previewImg: imageEffect.list_preview,
        //   });
        delete newImageEffect.list_preview;
        // toolPanel.asset.attribute.imageEffects = newImageEffect;
        if (newImageEffect.id !== toolPanel.asset.attribute.imageEffects?.id) {
            assetManager.setPv_new(3755, {
                additional: {
                    s0: imageEffect.id,
                },
            });
        }
        AssetLogic.updateAssetImage({
            assetIndex: toolPanel.asset_index,
            attribute: 'attribute',
            key: 'imageEffects',
            value: newImageEffect as unknown as number,
            // className:toolPanel.asset.meta.className,
            fun_name: 'UPDATE_IMAGEEFFECTS_END',
            isNew: newImageEffect.id !== toolPanel.asset.attribute.imageEffects?.id,
        });

        this.deleteCache(toolPanel.asset);
        // canvasStore.dispatch(paintOnCanvas('UPDATE_IMAGEEFFECTS_END', { asset: toolPanel.asset }));
    }
    // 清除图片特效
    public static removeImageEffect(unrecordable: boolean = false) {
        const { toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        UpdateAsset.updateAssets(
            'UPDATE_IMAGEEFFECTS_END',
            [
                {
                    index: toolPanel.asset_index,
                    changes: {
                        attribute: {
                            imageEffects: undefined,
                        },
                    },
                },
            ],
            undefined,
            undefined,
            unrecordable,
        );
        this.deleteCache(toolPanel.asset);
    }
    // 修改特效强度
    public static changeImageEffectStrong = debounce((value: number, isStop: boolean) => {
        const { toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const imageEffect = toolPanel.asset.attribute.imageEffects;
        const newImageEffect = { ...imageEffect };
        newImageEffect.strong = value;
        this.deleteCache(toolPanel.asset);
        AssetLogic.updateAssetImage({
            assetIndex: toolPanel.asset_index,
            attribute: 'attribute',
            key: 'imageEffects',
            value: newImageEffect,
            // className:toolPanel.asset.meta.className,
            fun_name: 'UPDATE_IMAGEEFFECTS' + (isStop ? '_END' : ''),
        });
    }, 60);
    // 删除图片缓存
    public static deleteCache(asset: IAsset) {
        const { isDesigner, toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (asset.attribute.renderCaId) {
            if (
                (isDesigner && asset.attribute.renderCaType === 2) ||
                (!isDesigner && asset.attribute.renderCaType === 1)
            ) {
                assetManager.deleteTemplateAssetCache(asset.attribute.renderCaId);
            }
            UpdateAsset.updateAssets('UPDATE_IMAGEEFFECTS_END', [
                {
                    index: toolPanel.asset_index,
                    changes: {
                        attribute: {
                            renderCaId: undefined,
                            renderCaType: undefined,
                            renderCa: undefined,
                        },
                    },
                },
            ]);
            // delete asset.attribute.renderCaId;
            // delete asset.attribute.renderCaType;
            // delete asset.attribute.renderCa;
        }
        assetManager.setPv_new(4109);
    }
    // 将相框添加到图片上
    public static addFrameToImage(asset: TAsset) {
        const { toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const curImageAsset = cloneDeep(toolPanel.asset);
        if (!curImageAsset) return;
        const {
            // transform: { posX, posY },
            attribute,
        } = curImageAsset;
        const frameAssetWidth = Number(asset.width);
        const frameAssetHeight = Number(asset.height);
        const scale = frameAssetWidth / frameAssetHeight;
        const imgWidth = Number(attribute.width);
        const imgHeight = Number(attribute.height);
        let calcHeight, calcWidth;
        if (imgWidth > imgHeight) {
            calcHeight = imgHeight;
            calcWidth = imgHeight * scale;
        } else {
            calcWidth = imgWidth;
            calcHeight = imgWidth * scale;
        }
        Object.assign(asset, {
            attribute: {
                width: calcWidth,
                height: calcHeight,
            },
            // transform: {
            //     posX,
            //     posY,
            // },
        });
        AssetLogic.deleteSelectAsset();
        this.addFrameTimer = setTimeout(() => {
            emitter.emit('ListAddFrame', asset);
            this.frameInitEndListener?.remove();
            this.frameInitEndListener = emitter.addListener('FrameInitEnd', (className: string) => {
                emitter.emit('addFrameImageByInit', curImageAsset, className);
                this.frameInitEndListener?.remove();
            });
        }, 0);
    }
    // ················图片ai处理start··················//
    public static replaceAiImage(params: {
        width: number;
        height: number;
        resId: string;
        picUrl: string;
        className: string;
        index: number;
        aiToolType?: string;
    }) {
        const changes = {
            attribute: {
                resId: params.resId,
                picUrl: params.picUrl
            },
        };
        const targets = [
            {
                index: params.index,
                className: params.className,
                changes,
            },
        ];
        UpdateAsset.updateAssets('UPDATE_IMAGE_ENHANCE', targets);
        AIGeneralLogic.AIToolUsedTypeStash(params.aiToolType);
    }
}
