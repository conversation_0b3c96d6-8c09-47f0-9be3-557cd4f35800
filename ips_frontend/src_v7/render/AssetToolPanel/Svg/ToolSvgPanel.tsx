import React,{Component} from 'react'

import {storeAdapter} from '@v7_logic_core/StoreAdapter';
import {FlowEditBlock} from '@v7_render/AssetSvgStroke';

import {GroupAssetEditBox} from '@v7_render/AssetToolPanel/Group';
import classNames from 'classnames';
import {TransformSettingBlock} from '@v7_render/AssetToolPanel/components/TransformSettingBlock';
import {AssetDecoration} from '@v7_render/AssetDecoration';
import { BaseTool } from '@v7_render//AssetToolPanel/Image/ImageEditTool/BaseTool/BaseTool';
import TextEditBlock from '@src/userComponentV6.0/canvas/ToolTextPanelV2/TextEditBlock';
import TypesettingBlock from '@src/userComponentV6.0/canvas/ToolTextPanelV2/TypesettingBlock';
import { AITextEntrance } from '../Text/AITextEntrance';

export class ToolSVGPanel extends Component {
    constructor(props) {
        super(props)
        const {toolPanel} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        this.state = {
            isEffectFollow: toolPanel.asset.attribute?.ks && toolPanel.asset.attribute?.ks?.i?.kw?.ks?.c?.length > 0 ? true : false
        }
    }
    render() {
        /*编辑器判断*/
        const {toolPanel,isDocDesigner} = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const isInnerGroup = toolPanel.asset?.meta.group
        const showTextEditBlock = toolPanel.asset?.attribute.textAttr
        return (
            <div className={classNames("toolSVGPanel", isInnerGroup && 'hasGroup')}>
                <div className='title'>形状编辑</div>
                {isDocDesigner && <AssetDecoration />}
                {isInnerGroup && <GroupAssetEditBox style={{marginBottom: '20px',paddingBottom: '15px',borderBottom: '1px solid #E9E8E8'}} />}
                <BaseTool isSvgAsset={true}/> 
                <div className="assetsAdjust-wrap">
                    <FlowEditBlock /> 
                    {
                        showTextEditBlock && (
                            <>
                                <TextEditBlock tempAsset = {toolPanel.asset} visible={true}/>
                                <AITextEntrance from="SVG"></AITextEntrance>
                                <TypesettingBlock visible={true} hideTop={true}/>
                                <div className="hr"></div>
                            </>
                        )
                    }
                    <TransformSettingBlock />
                </div>
            </div>
        )
    }
}