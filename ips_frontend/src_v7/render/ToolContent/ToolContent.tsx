import React, { PureComponent } from 'react';
import classNames from 'classnames';

import { TOOL_CONTENT_CONFIG } from '@editorConfig/ToolContent';
import { ETool } from '@v7_logic/Enum';
import { UserUploadArea } from '@component/toolV6.0/UserUploadArea';
import { GroupWordPanel } from '@component/toolV6.0/GroupWordPanel/GroupWordPanel';
import { DesignerPanel } from '@component/toolV6.0/Designer';
import { IPSConfig } from '@v7_utils/IPSConfig';
import { storeDecorator } from '@v7_logic/StoreHOC';
import { IStoreState } from '@v7_store/redux/store';

interface propsStruct {
    currentNav: ETool;
    isDesigner?: boolean;
}

/* 包裹工具栏内容的包装层，
 * 里面由不同的ToolTab组成，随着NavItem的点击而切换
 */
@storeDecorator((state: IStoreState) => {
    return {
        isDesigner: state.onCanvasPainted.isDesigner,
    };
})
export class Tool<PERSON>ontent extends PureComponent<propsStruct> {
    // eslint-disable-next-line @typescript-eslint/no-useless-constructor
    constructor(props: propsStruct) {
        super(props);
    }

    render(): JSX.Element {
        const urlProps = IPSConfig.getProps();
        return (
            <div className={`toolWrapper ${this.props.currentNav === ETool.AI_CHAT ? 'toolWrapper_noPadding' : ''}`}>
                {
                    // TODO 后面需要处理Nav uploadFile 这种类型到统一配置中
                }
                <ToolTab navType={ETool.UPLOAD_FILE} active={this.props.currentNav === ETool.UPLOAD_FILE}>
                    <UserUploadArea
                        key="UserUploadArea"
                        currentNav={this.props.currentNav}
                        isActive={this.props.currentNav === ETool.UPLOAD_FILE}
                    />
                </ToolTab>
                {this.props.currentNav === ETool.GROUP_WORD && (
                    <ToolTab navType={ETool.GROUP_WORD} active={this.props.currentNav === ETool.GROUP_WORD}>
                        <GroupWordPanel isActive={this.props.currentNav === ETool.GROUP_WORD} />
                    </ToolTab>
                )}
                {urlProps.isDesigner && (
                    <ToolTab navType={ETool.DESIGNER} active={this.props.currentNav === ETool.DESIGNER}>
                        <DesignerPanel isActive={this.props.currentNav === ETool.DESIGNER} />
                    </ToolTab>
                )}
                {TOOL_CONTENT_CONFIG['tool'].map((item, i: number) => {
                    const { show, nav } = item.showComponentFun({ currentNav: this.props.currentNav });
                    return (
                        <ToolTab navType={nav} active={show} key={i}>
                            {item['component']}
                        </ToolTab>
                    );
                })}
            </div>
        );
    }
}

/*工具栏具体每一个Tab下的内容*/
class ToolTab extends PureComponent<{
    active: boolean;
    navType: string;
}> {
    hasAcitve = false;

    render() {
        const isActive = this.props.active;
        const navType = this.props.navType;

        if (navType === ETool.UPLOAD_FILE) {
            //
        } else if (!this.hasAcitve && !this.props.active) {
            return null;
        }
        this.hasAcitve = true;
        const contentClass = classNames({
            invisible: isActive === false,
        });
        const childrenWithProps = React.Children.map(this.props.children, (child: JSX.Element) => {
            return React.cloneElement(child, {
                params: navType,
            });
        });
        // if(!isActive) return null // jyjin 取消样式隐藏，直接重新渲染，和其他菜单保持一样的逻辑；如果不重新渲染，很多状态无法更新，会有bug
        return <div className={'tool_tab_container ' + contentClass}>{childrenWithProps}</div>;
    }
}
