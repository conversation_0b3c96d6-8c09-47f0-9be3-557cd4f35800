import React, { Component } from 'react';
import classNames from 'classnames';

import { assetManager } from '@component/AssetManager';
import { TablePanel } from '@v7_render/TablePanel';
import { ChartPanel } from '@v7_render/ChartPanel';
import {ETool} from '@v7_logic/Enum';
import { emitter } from '@component/Emitter';

export function DisplayTableChartTool(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.TABLE_CHART, nav: ETool.TABLE_CHART};
}

interface propsStruct {
    isActive?: boolean;
}
interface stateStruct {
    navType: string;
}

export class TableChartPanel extends Component<propsStruct, stateStruct> {
    galleryNav: { iconClass: string; name: string; navType: string }[];
    changeGalleryNavItemTabEventEmitter: any
    constructor(props: propsStruct) {
        super(props);

        this.state = {
            navType: 'table',
        };

        this.galleryNav = [
            { iconClass: 'icon icon-artWord', name: '表格', navType: 'table' },
            { iconClass: 'icon icon-artWord', name: '图表', navType: 'chartType' },
        ];
        this.addGalleryNavItemTabEmiiter();
        // this.getSourceMaterial()
    }

    // componentDidMount():void{

    // }

    componentWillUnmount(): void {
        this.changeGalleryNavItemTabEventEmitter?.remove();
    }

    addGalleryNavItemTabEmiiter() {
        this.changeGalleryNavItemTabEventEmitter = emitter.addListener('ChangeGalleryNavItemEventEmitter', (tab: 'specialWord' | 'textGroup') => {
            this.setState({navType: tab});
        });
    }

    galleryNavItemClickEvent(type: string, e: MouseEvent): void {
        const { navType } = this.state;
        if (type != navType) {
            this.setState({
                navType: type,
            });
        }
        if (type == 'table') {
            assetManager.setPv_new(2347, {
                additional: {},
            });
        } else if (type == 'chartType') {
            assetManager.setPv_new(2877, {
                additional: {},
            });
        }
        this.stopPropagation(e);
    }

    stopPropagation(e: MouseEvent): void {
        e.stopPropagation();
        (e as any)?.nativeEvent.stopImmediatePropagation();
    }
    render(): JSX.Element {
        const { navType } = this.state;

        return (
            <div className="SourceMaterialPanel TableChartPanel">
                <div className="sourceNav">
                    {this.galleryNav.map((item, index) => {
                        return (
                            <div
                                className={classNames('galleryNavItem', item.navType, {
                                    active: navType == item.navType,
                                })}
                                key={index}
                                onClick={this.galleryNavItemClickEvent.bind(this, item.navType)}
                            >
                                {item.name}
                            </div>
                        );
                    })}
                </div>

                <div className="SourceMaterialSide">
                    {navType == 'table' && <TablePanel isActive={true} />}
                    {navType == 'chartType' && <ChartPanel isActive={true} />}
                </div>
            </div>
        );
    }
}
