import React, { useRef, useState } from 'react'
import classNames from 'classnames'
import { QRCodeCanvas } from 'qrcode.react';
import { emitter } from '@src/userComponentV6.0/Emitter';
import { ETool } from '@v7_logic/Enum';
import { assetManager } from '@src/userComponentV6.0/AssetManager';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import './scss/QRcodePanelFn.scss'
import { QrcodeInfo } from '@v7_utils/QrCode/typing';
import { AssetAddListener } from '@v7_logic/AssetAddListener';
import { useDebounceFn } from 'ahooks';
export const DisplayQRcodePanelFn = (params: { currentNav: ETool }) => {
    const { currentNav } = params;
    return {show: currentNav === ETool.QRCODEPANELFN, nav: ETool.QRCODEPANELFN};
}
export const QRcodePanelFn = () => {
    const QRcodeRef = useRef(null)

    const [QRcodeUrl, setQRcodeUrl] = useState<string>('')
    const [QRcodeInit, setQRcodeInitl] = useState<QrcodeInfo>({
        foreground: { r: 0, g: 0, b: 0 },
        background: { r: 255, g: 255, b: 255 },
        text: '', // 文本内容
        textType: 'url',
        iconType: undefined,
        padding: 20
    })
    const isQRcodeUrl = QRcodeUrl.length >= 1

    const setQRcodeUrlFn = (e: any) => {

        setQRcodeUrl(e.target.value)

        setQRcodeInitl(prevState => {
            return { ...prevState, text: e.target.value };
        })

        onEntering()

    }

    // 输入埋点
    const { run: onEntering } = useDebounceFn(
        () => {
            assetManager.setPv_new(7006);
        },
        {
            wait: 3000,
        },
    );

    const checkQRocde = () => {
        let { user } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        assetManager.setPv_new(7004);
        if (!(user.userId > 0)) {
            emitter.emit('LoginPanelShow');
            return;
        }
        if (user.bind_phone === 0) {
            emitter.emit('InfoBarPhoneBindPopup');
            return
        }

        if (QRcodeUrl == '') {
            return
        }
        assetManager.setPv_new(7005);

        try {
            AssetAddListener.addQrcode(
                {
                    width: 50,
                    height: 50,
                    qrcodeinfo: QRcodeInit,
                }
            );
            setQRcodeUrl('')

        } catch (error) {
            console.log(error);
        }
    }


    const selectFn = () => {
        QRcodeRef.current.select()
    }

    return (
        <div className="QRcodePanelFn">
            <div className="qrcode-Wrapper">
                <div className="qrcode-title">二维码</div>
                <div className="qrcode-scroll">
                    <div className="qrcode-introduce">
                        输入页面链接URL后，我们会生成相应的二维码供你在设计中使用。扫描二维码即可跳转到页面
                    </div>
                    <div className="qrcode-url">页面链接URL</div>
                    <div className={classNames('qrcode-inputBox', isQRcodeUrl && 'qrcode-inputBox-active')}>
                        <input
                            type="text"
                            ref={QRcodeRef}
                            value={QRcodeUrl}
                            name=""
                            id="qrcode-inputvalue"
                            onClick={() => selectFn()}
                            // onKeyDown={(e) => {
                            //     e.stopPropagation();
                            //     e.nativeEvent.stopPropagation();
                            // }}
                            onChange={(e) => setQRcodeUrlFn(e)}
                        />
                    </div>
                    <div className="preview-title">预览</div>
                    <div className="preview-canvas">
                        {isQRcodeUrl ? (
                            <QRCodeCanvas
                                //QRCodeCanvas预览图 真正生成走的是utils/QrCode
                                id="qrCode"
                                size={120} // 二维码的大小
                                value={QRcodeUrl}
                                fgColor="#000000" // 二维码的颜色
                                bgColor="#ffffff" //二维码背景色
                                level="H"
                            />
                        ) : (
                            <img src="//s.tuguaishou.com/editor/image/erweima.png" alt="" id="default-img" />
                        )}
                    </div>
                    <div
                        className={classNames('check-button', isQRcodeUrl && 'check-button-active')}
                        onClick={checkQRocde}
                    >
                        生成二维码
                    </div>

                    {/* <div className='QrCodePanel'>

                </div> */}
                </div>
            </div>
        </div>
    );
}
