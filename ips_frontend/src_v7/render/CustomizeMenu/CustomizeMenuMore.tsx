import { assetManager } from '@src/userComponentV6.0/AssetManager';
import { emitter } from '@src/userComponentV6.0/Emitter';
import { loginCheckDecorator } from '@v7_logic/Decorators/Method';
import { ETool } from '@v7_logic/Enum';
import { EventSubscription } from 'fbemitter';
import { klona as cloneDeep } from 'klona';
import React, { PureComponent } from 'react';
import { ReactSortable, Sortable } from 'react-sortablejs';
import { TMenuItem, menuItems, ecommerceMenuItems, fixedMenuIds, menuType, menuTypeMap, hotMenus, appAuthorization, haveThirdPage } from './config';
import { env } from '@editorConfig/env';
import './scss/CustomizeMenuMore.scss';
import { ErrorBoundaryDecorator } from '@v7_render/ErrorBoundaryHOC';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';

export function DisplayCustomizeMenuMore(params: { currentNav: ETool }) {
    const { currentNav } = params;
    return {show: currentNav === ETool.MENUMORE, nav: ETool.MENUMORE};
}

const cartConfig = env.editor === 'ecommerce' || env.editor === 'ecommerceteam' ? ecommerceMenuItems : menuItems;
interface ICustomizeMenuMoreState {
    menuList: TMenuItem[];
    menuListInit: TMenuItem[];
    dragMenuId: string;
    isTeam: boolean;
    thirdApp: TMenuItem;
    loginIds: string[]
}

@ErrorBoundaryDecorator()
export class CustomizeMenuMore extends PureComponent<
    {
        /*  */
    },
    ICustomizeMenuMoreState
> {
    onMenuUpdateEmitter: EventSubscription;
    state: ICustomizeMenuMoreState = {
        menuList: [],
        menuListInit: [],
        dragMenuId: undefined,
        isTeam: false,
        thirdApp: undefined,
        loginIds: []
    };

    componentDidMount() {
        this.onMenuUpdateEmitter = emitter.addListener(
            'onMenuUpdate',
            (menuList: ICustomizeMenuMoreState['menuList'], isTeam: boolean) => {
                this.setState({menuListInit: menuList})
                menuList = cloneDeep(menuList);
                if (env.categoryMenuMore) {
                    for (const k in menuType) {
                        menuType[k].menus = [];
                    }
                    menuList.forEach((menu) => {
                        if (!isTeam && menu.id === '12') {
                            // do nothing
                        } else if (menu.is_more === 1 && menu.online === '1' && menuType[menuTypeMap[menu.menu_type]]?.menus) {
                            menuType[menuTypeMap[menu.menu_type]].menus.push(menu);
                        }
                        if (hotMenus.includes(menu.id)) {
                            const index = hotMenus.findIndex(item => item === menu.id)
                            menuType[1].menus[index] = menu
                        }
                    });
                    for (const k in menuType) {
                        if (k !== '1') {
                            menuType[k].menus.sort((a, b) => cartConfig[a.id]?.categorySort - cartConfig[b.id]?.categorySort);
                        }
                    }
                }
                this.setState({
                    menuList: menuList.sort((a, b) => a.more_sort - b.more_sort),
                    isTeam,
                });
            },
        );
        this.getLoginId()
    }

    getLoginId = () => {
        assetManager.getBaiduNetdiskAuthUrl().then((data) => {
            data.json().then((resultData) => {
                if (resultData.code == 2) {
                    // 已经授权
                    this.setState({loginIds:['48']})
                }
            });
        });
    }

    componentWillUnmount(): void {
        this.onMenuUpdateEmitter?.remove();
        this.setState({ thirdApp: undefined })
    }

    @loginCheckDecorator
    addMenu = (e: React.MouseEvent<HTMLElement>, menu: TMenuItem, flag = false) => {
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
        const { type } = e.currentTarget.dataset;
        const menuid = menu.id;
        if (menu.need_confirm === '1'&& !flag && menu.is_more===1 && haveThirdPage.includes(menu.id)) {
            this.setState({ thirdApp: menu })
        } else {
            const { menuList } = this.state;
            const newMenuList = cloneDeep(menuList);
            const addMenuItem = newMenuList.find((m) => m.id === menuid);
            newMenuList.map((m) => {
                if (m.is_more === 1 /* && m.menu_type === addMenuItem.menu_type */ && m.more_sort > addMenuItem.more_sort) {
                    m.more_sort--;
                }
            });
            addMenuItem.sort = newMenuList.filter((m) => m.is_more === 0).length;
            addMenuItem.more_sort = -1;
            addMenuItem.is_more = 0;
            addMenuItem.action = 'add';
            emitter.emit('onMenuMoreEdit', 'add', newMenuList, menuid);
            assetManager.setPv_new(type === 'icon' ? 6290 : 6289, {
                additional: {
                    s0: menuid,
                    s1: 'click',
                },
            });
            storeAdapter.dispatch({
                fun_name: 'updateApp',
                store_name: storeAdapter.store_names.appStore,
                params: [{
                    type: 'updateAppMenu',
                    params: {
                        menu: {
                            menuKey: cartConfig[addMenuItem.id].key 
                        }
                    }
                }]
            })
        }
        emitter.emit('PopoverBoxClose')
    };

    /**
     * @description: 悬停埋点
     * @param {string} menuName 菜单名
     * @param {number} isMore 是否在更多里
     */
    mouseOver = (menuName: string, isMore: number) => {
        return () => {
            assetManager.setPv_new(6650, {
                additional: { s0: menuName, s1: isMore },
            });
        };
    };

    @loginCheckDecorator
    onSortStart = (e: Sortable.SortableEvent) => {
        this.setState({
            dragMenuId: e.item.dataset.menuid,
        });
    };

    @loginCheckDecorator
    onSortEnd = (e: Sortable.SortableEvent) => {
        const {
            newIndex,
            oldIndex,
            item: {
                dataset: { menuid },
            },
        } = e;
        const toMoreMenu = /sort-menu-more/.test(e.from.className) && /sort-menu-more/.test(e.to.className);
        const toMenu = !toMoreMenu;
        // const isSameMenuType = e.from.id === e.to.id;
        if (toMoreMenu /* && isSameMenuType */ && oldIndex === newIndex) {
            return;
        }
        /* if (toMoreMenu && !isSameMenuType) {
            return;
        } */
        const { menuList } = this.state;
        const newMenuList = cloneDeep(menuList);
        const dragMenuItem = newMenuList.find((m) => m.id === menuid);
        let action: 'add' | 'sort';
        if (toMenu) {
            action = 'add';
            newMenuList.forEach((m) => {
                if (m.sort >= newIndex) {
                    m.sort++;
                }
                if (m.more_sort > dragMenuItem.more_sort) {
                    m.more_sort--;
                }
            });
            dragMenuItem.action = 'add';
            dragMenuItem.more_sort = -1;
            dragMenuItem.is_more = 0;
            dragMenuItem.sort = newIndex;
            assetManager.setPv_new(6289, {
                additional: {
                    s0: menuid,
                    s1: 'drag',
                },
            });
            assetManager.setPv_new(6306, {
                additional: {
                    s0: 'menu-more',
                    s1: 'add',
                },
            });
        } else if (toMoreMenu && !env.categoryMenuMore /* && isSameMenuType */) {
            action = 'sort';
            if (dragMenuItem.more_sort > newIndex) {
                newMenuList.forEach((m) => {
                    if (
                        // m.menu_type === dragMenuItem.menu_type &&
                        m.more_sort >= newIndex &&
                        m.more_sort < dragMenuItem.more_sort
                    ) {
                        m.more_sort++;
                    }
                });
                dragMenuItem.more_sort = newIndex;
            } else {
                newMenuList.forEach((m) => {
                    if (
                        // m.menu_type === dragMenuItem.menu_type &&
                        m.more_sort > dragMenuItem.more_sort &&
                        m.more_sort <= newIndex
                    ) {
                        m.more_sort--;
                    }
                });
                dragMenuItem.more_sort = newIndex;
            }
            assetManager.setPv_new(6306, {
                additional: {
                    s0: 'menu-more',
                    s1: 'sort',
                },
            });
        }
        this.setState({
            dragMenuId: undefined,
        });
        emitter.emit('onMenuMoreEdit', action, newMenuList, dragMenuItem.id);
    };

    onSortMove = (e: Sortable.MoveEvent) => {
        if (
            env.categoryMenuMore &&
            /sort-menu-more/.test(e.from.className) &&
            /sort-menu-more/.test(e.to.className) &&
            e.from !== e.to
        ) {
            return false;
        }
        if (e.related.className.indexOf('ignore') > 0) {
            const { fixedSort } = e.related.dataset;
            if (Number.parseInt(fixedSort) === fixedMenuIds.length - 1) {
                return 1;
            }
        }
        return e.related.className.indexOf('ignore') === -1;
    };

    setMenuList = (newList: TMenuItem[]) => {
        // 不能使用库的排序结果，仅用来解决报错
    };

    onMoreMenuDragStart(e: React.DragEvent) {
        e.stopPropagation();
    }

    setAccount = (menu: TMenuItem, e: React.DragEvent) => {
        assetManager.setPv_new(7408)
        assetManager.delBaiduNetdiskAuth().then((data) => {
            data.json().then((resultData) => {
                emitter.emit('PopoverBoxClose')
                if (resultData.code == 1) {
                    // 删除授权成功
                    this.setState({ loginIds: [] });
                    emitter.emit('Message', '解绑成功')
                }
            });
        });
    }

    menuClick = async (menu: TMenuItem, e: MouseEvent) => {
        e.preventDefault()
        e.stopPropagation();
        emitter.emit('PopoverBox', {
            windowContent: <div>
                <div className='name'>{cartConfig[menu.id].name || menu.menu_name}</div>
                {/* {menu.id === '48' ? <> */}
                {menu.id === '48' && this.state.loginIds.includes(menu.id)? <>
                    <div className='line'></div>
                    <a className='star' onClick={this.setAccount.bind(this, menu)}>
                        <i className="iconfont icon-duankai"></i>
                        <span>解绑账号</span>
                    </a>
                </> : <></>}
            </div>,
            event:e,
        })
    }

    render() {
        const { thirdApp } = this.state;

        return (
            <div className="customize-menu-more">
                {thirdApp ? (
                    <div className='customize-menu-more-thirdPage'>
                        <div className='thirdPage-title' onClick={() => this.setState({ thirdApp: undefined })}>
                            <i className="iconfont icon-fanhui1"></i>
                            <span>返回应用</span>
                        </div>
                        <div className='thirdPage-line'></div>
                        <div className='thirdPage-content'>
                            <div className='header'>
                                <div className='header-img'>
                                    <img src={appAuthorization[thirdApp.id].titleUrl} />
                                </div>
                                <div className='header-right'>
                                    <div className='title'>{appAuthorization[thirdApp.id].title}</div>
                                    <div>
                                        <span>创建者：</span>
                                        <a>{appAuthorization[thirdApp.id].name}</a>
                                    </div>
                                </div>
                            </div>
                            <div className='img-box'>
                                <img src={appAuthorization[thirdApp.id].contentUrl} />
                            </div>
                            <div className='info'>{appAuthorization[thirdApp.id].info}</div>
                            <div className='addBtn' onClick={(e) => {
                                this.addMenu(e, thirdApp, true);
                                if (thirdApp.id ==='48') {
                                    assetManager.setPv_new(7411)
                                } else {
                                    assetManager.setPv_new(7412)
                                }
                            }}>添加使用</div>
                            <div className='authorization'>
                                使用此应用即表示你同意
                                <a href='https://818ps.com/page/1' rel='noopener noreferrer' target='_blank'>条款和条件</a>及
                                <a href='https://818ps.com/page/17' rel='noopener noreferrer' target='_blank'>隐私权政策</a>
                            </div>
                            
                            {thirdApp.id === '48' && <div className='netdiskRemind'>
                                <div>支持显示20M以内，png、jpg、jepg格式图片</div>
                                <div>依据国家相关法律法规，禁止上传包含色情、违 法、侵权等性质内容，违规内容将会删除处理</div>
                            </div>}

                            <div></div>
                        </div>
                    </div>
                ) : (
                    <>
                        {env.categoryMenuMore ? (
                            Object.keys(menuType).map((k) => {
                                
                                if (menuType[k].menus.length === 0) {
                                    return;
                                }
                                return (
                                    <div className="customize-menu-more-category" key={k}>
                                        <div className="customize-menu-more-category-title">{menuType[k].text}</div>
                                        <div
                                            className="customize-menu-more-category-content"
                                            onDragStart={this.onMoreMenuDragStart}
                                        >
                                            <ReactSortable
                                                className={`sort-menu-more ${this.state.dragMenuId ? 'dragging' : ''}`}
                                                // id={`menuType-${k}`}
                                                list={this.state.menuList}
                                                setList={this.setMenuList}
                                                filter=".ignore"
                                                animation={150}
                                                group="menu"
                                                chosenClass="dragging"
                                                onStart={this.onSortStart}
                                                onEnd={this.onSortEnd}
                                                onMove={this.onSortMove}
                                            >
                                                {menuType[k].menus.map((menu) => {
                                                     if(menu.id === '54')menu.online = '1'
                                                    if (
                                                        (menu.is_more === 0 ||
                                                            menu.online === '0' ||
                                                            (!this.state.isTeam && menu.id === '12')) && k !== '1' && k !== '3'
                                                    ) {
                                                        return <React.Fragment key={menu.id}></React.Fragment>;
                                                    }

                                                    if (menu.online === '0' && menu.id !== '11' && menu.id !== '36') {
                                                        return (
                                                            <div key={menu.id} className={`customize-menu-more-item be-coming-soon`}>
                                                                <div className='customize-menu-item-bg item-third'>
                                                                    <img
                                                                        src={cartConfig[menu.id].icon_url}
                                                                    />
                                                                    <div className='gray-box'></div>
                                                                </div>
                                                                <div className='coming-text'>即将上线</div>
                                                                <span className="customize-menu-item-text">{cartConfig[menu.id].name || menu.menu_name}</span>
                                                            </div>
                                                        )
                                                    } else {
                                                        return (
                                                            <div
                                                                className={`customize-menu-more-item`}
                                                                key={menu.id}
                                                                data-menuid={menu.id}
                                                                data-menuGroup={menu.menu_type}
                                                                data-type="model"
                                                                onClick={(e) => this.addMenu(e, menu)}
                                                                onMouseOver={this.mouseOver(
                                                                    menu.menu_name,
                                                                    menu.is_more,
                                                                )}
                                                            >
                                                                {menu.menu_type === '4' ? (
                                                                    <div className="customize-menu-item-bg item-third">
                                                                        <img src={cartConfig[menu.id].icon_url} />
                                                                        <div
                                                                            className="menuRight"
                                                                            onClick={this.menuClick.bind(this, menu)}
                                                                        >
                                                                            <i className="iconfont icon-gengduo3"></i>
                                                                        </div>
                                                                    </div>
                                                                ) : (
                                                                    <>
                                                                        {cartConfig[menu.id].icon !== 'custom' && (
                                                                            <div className="customize-menu-item-bg">
                                                                                <i
                                                                                    className={`customize-menu-icon ${
                                                                                        cartConfig[menu.id].icon
                                                                                    }`}
                                                                                ></i>
                                                                            </div>
                                                                        )}
                                                                        {cartConfig[menu.id].icon === 'custom' && (
                                                                            <div
                                                                                className={`customize-menu-item-bg ${
                                                                                    ["49",'52', '53', '54',"58",'56','57'].includes(menu.id)
                                                                                        ? 'item-ai'
                                                                                        : 'item-img'
                                                                                }`}
                                                                            >
                                                                                <img
                                                                                    style={
                                                                                        [
                                                                                           "49",'52', '53', '54',"58",'56','57',
                                                                                            '8',
                                                                                        ].includes(menu.id)
                                                                                            ? {}
                                                                                            : {
                                                                                                  width: '42px',
                                                                                                  height: '42px',
                                                                                              }
                                                                                    }
                                                                                    src={cartConfig[menu.id].icon_url}
                                                                                />
                                                                            </div>
                                                                        )}
                                                                        {/* <i
                                                                            className="iconfont icon-tianjia2"
                                                                            data-menuid={menu.id}
                                                                            data-type="icon"
                                                                            onClick={(e) => this.addMenu(e, menu)}
                                                                        ></i> */}
                                                                    </>
                                                                )}
                                                                <span className="customize-menu-item-text">
                                                                    {cartConfig[menu.id].name || menu.menu_name}
                                                                </span>
                                                            </div>
                                                        );
                                                    }

                                                })}
                                            </ReactSortable>
                                        </div>
                                    </div>
                                );
                            })
                        ) : (
                            <div className="customize-menu-more-category-content" onDragStart={this.onMoreMenuDragStart}>
                                <ReactSortable
                                    className={`sort-menu-more ${this.state.dragMenuId ? 'dragging' : ''}`}
                                    // id={`menuType-${k}`}
                                    list={this.state.menuList}
                                    setList={this.setMenuList}
                                    filter=".ignore"
                                    animation={150}
                                    group="menu"
                                    chosenClass="dragging"
                                    onStart={this.onSortStart}
                                    onEnd={this.onSortEnd}
                                    onMove={this.onSortMove}
                                >
                                            {this.state.menuList.map((menu) => {
                                        if(menu.id === '53')menu.online = '1'
                                        if (
                                            menu.is_more === 0 ||
                                            menu.online === '0' ||
                                            (!this.state.isTeam && menu.id === '12')
                                        ) {
                                            return <React.Fragment key={menu.id}></React.Fragment>;
                                        }
                                        return (
                                            <div
                                                className={`customize-menu-more-item`}
                                                key={menu.id}
                                                data-menuid={menu.id}
                                                data-menuGroup={menu.menu_type}
                                                data-type="model"
                                                onClick={(e) => this.addMenu(e, menu)}
                                                onMouseOver={this.mouseOver(menu.menu_name, menu.is_more)}
                                            >
                                                {cartConfig[menu.id].icon !== 'custom' && (
                                                    <div className='customize-menu-item-bg'>
                                                        <i className={`customize-menu-icon ${cartConfig[menu.id].icon}`}></i>
                                                    </div>
                                                )}
                                                {cartConfig[menu.id].icon === 'custom' && (
                                                    <div className='customize-menu-item-bg item-third'>
                                                        <img
                                                            style={{
                                                                width: '42px',
                                                                height: '42px',
                                                            }}
                                                            src={cartConfig[menu.id].icon_url}
                                                        />
                                                    </div>
                                                )}

                                                <span className="customize-menu-item-text">{cartConfig[menu.id].name || menu.menu_name}</span>
                                                {/* <i
                                                    className="iconfont icon-tianjia2"
                                                    data-menuid={menu.id}
                                                    data-type="icon"
                                                    onClick={(e) => this.addMenu(e, menu)}
                                                ></i> */}
                                            </div>
                                        );
                                    })}
                                </ReactSortable>
                            </div>
                        )}
                    </>)}
            </div>
        );
    }
}