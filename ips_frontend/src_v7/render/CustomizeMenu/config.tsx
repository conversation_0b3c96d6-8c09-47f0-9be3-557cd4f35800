import { imgHost } from '@src/userComponentV6.0/IPSConfig';
import { ETool } from '@v7_logic/Enum';

export const menuPointMap = {
    aiProducts: 7518,
    aiCutout: 7452,
    aiDraw: 7422,
    aiText: 7475,
    specificWord: [4734, 200],
    specificWord3D: 2405,
    groupWord: 201,
    artWord: 202,
    png: 203,
    SVG: 204,
    background: [4734, 205],
    gallery: 205,
    pic: [4734, 206],
    uploadFile: 207,
    emoji: 339,
    table: 2347,
    chartType: 2877,
    sourceMaterial: 6808,
    teamWork: 2938,
    tableChart: 2347,
    copywriting: [4734, 3541],
    animationEffect: 4638,
    videoe: 5521,
    music: 5542,
    medicalMaterial: 6320,
    goodNewsMaterial: 6321,
    findJobMaterial: 6359,
    schoolMaterial: 6562,
    template: 6444,
    wordartMaterial: 6562,
    template3D: 6769,
    templateEstate: 6865,
    templateBank: 6866,
    templateBeauty: 6867,
    addCanvas: 4249,
    templateFinance: 6977,
    qrcodePanelFn: 7003,
    myPanel: 7315,
    baiduNetdisk: 7406,
    tencentMaps: 7407,
    emojiPage: 7410,
    addMenu: 8140,
};
type TMenuItemDisplayInfo = Record<
    string,
    {
        key: string;
        name?: string;
        icon: string;
        icon_url?: string;
        hover?: string;
        hoverUrl?: string;
        hoverColor?: string;
        /** 是否固定排序 */
        sortFilter?: boolean;
        /** 固定排序顺序索引 */
        fixedSort?: number;
        /** 单个分类下单独的排序 */
        categorySort?: number;
    }
>;
// 功能组件
const menuItemsForFunction: TMenuItemDisplayInfo = {
    4: {
        key: 'pic', // 照片
        icon: 'iconfont icon-bianjiqi-xin-zuocedaohangzhaopianmoren',
        hoverColor: '#085BEA',
        hover: 'iconfont icon-bianjiqi-xin-zuocedaohangzhaopianxuanting',
        categorySort: 0,
    },
    // 18: {
    //     key: 'wordartMaterial', // 艺术字
    //     icon: 'iconfont icon-yishuzi',
    //     categorySort: 2,
    // },
    2: {
        key: 'specificWord3D', // 3D文字
        icon: 'iconfont icon-specificWord new1',
        categorySort: 3,
    },
    5: {
        key: 'tableChart', // 表格图表
        icon: 'iconfont icon-bianjiqi-xin-zuocedaohangbiaogetubiaomoren',
        hoverColor: '#4C9F51',
        hover: 'iconfont icon-bianjiqi-xin-zuocedaohangbiaogexuanting',
        categorySort: 4,
    },
    40: {
        key: 'qrcodePanelFn', // 二维码
        icon: 'iconfont icon-bianjiqi-xin-zuocedaohangerweimamoren',
        hoverColor: '#085BEA',
        hover: 'iconfont icon-bianjiqi-xin-zuocedaohangerweimaxuanting',
        categorySort: 5,
    },
    11: {
        key: 'animationEffect', // 页面动效 已下线
        icon: 'iconfont icon-yemiandongxiao',
        categorySort: 8,
    },
    9: {
        key: 'copywriting', // 文案大全
        icon: 'iconfont icon-tupianbianjiqizuoceicon_chuangyizi',
        categorySort: 6,
    },

    12: {
        key: 'teamWork', // 团队
        icon: 'iconfont icon-bianjiqi-xin-zuocedaohangtuanduimoren',
        hoverColor: '#EF3964',
        hover: 'iconfont icon-bianjiqi-xin-zuocedaohangtuanduixuanting',
        categorySort: 7,
    },
};
// 图怪兽ai
const menuItemsForAi = {
    49: {
        key: ETool.AI_TEXT, // ai 文案
        icon: 'custom',
        // icon_url: imgHost + `/index_img/editorV7.0/menuIcon-aiTextDs.png`,
        icon_url: imgHost + `/index_img/editorV7.0/menuIcon-${ETool.AI_TEXT}-2.svg`,
        categorySort: 0,
        hoverColor: '#01C6C6',
    },
    52: {
        key: ETool.AI_DRAW, // ai 素材
        icon: 'custom',
        // icon_url: imgHost + `/index_img/editorV7.0/menuIcon-${ETool.AI_DRAW}.png`,
        icon_url: imgHost + `/index_img/editorV7.0/menuIcon-${ETool.AI_DRAW}-2.svg`,
        categorySort: 1,
        hoverColor: '#962DFF'
    },
    53: {
        key: ETool.AI_CUTOUT, // ai 抠图
        icon: 'custom',
        // icon_url: imgHost + `/index_img/editorV7.0/menuIcon-${ETool.AI_CUTOUT}.png`,
        icon_url: imgHost + `/index_img/editorV7.0/menuIcon-${ETool.AI_CUTOUT}-2.svg`,
        categorySort: 2,
        hoverColor: '#085BEA',
    },
    54: {
        key: ETool.AI_PRODUCTS, // ai 商品图
        icon: 'custom',
        // icon_url: imgHost + `/index_img/editorV7.0/menuIcon-${ETool.AI_PRODUCTS}.png`,
        icon_url: imgHost + `/index_img/editorV7.0/menuIcon-${ETool.AI_PRODUCTS}-2.svg`,
        categorySort: 3,
        hoverColor: '#E77001',
    },
    56: {
        key: ETool.AI_ENHANCE, // ai 变清晰
        icon: 'custom',
        // icon_url: imgHost + `/index_img/editorV7.0/menuIcon-${ETool.AI_ENHANCE}.png`,
        icon_url: imgHost + `/index_img/editorV7.0/menuIcon-${ETool.AI_ENHANCE}-2.svg`,
        categorySort: 4,
        hoverColor: '#32A40F',
    },
    57: {
        key: ETool.AI_ELIMINATE, // ai 消除
        icon: 'custom',
        // icon_url: imgHost + `/index_img/editorV7.0/menuIcon-${ETool.AI_ELIMINATE}.png`,
        icon_url: imgHost + `/index_img/editorV7.0/menuIcon-${ETool.AI_ELIMINATE}-2.svg`,
        categorySort: 5,
        hoverColor: '#E5AC0F',
    },
    58: {
        key: ETool.AI_ELIMINATEREDRAW, // ai 生成
        icon: 'custom',
        // icon_url: imgHost + `/index_img/editorV7.0/menuIcon-${ETool.AI_ELIMINATEREDRAW}.png`,
        icon_url: imgHost + `/index_img/editorV7.0/menuIcon-${ETool.AI_ELIMINATEREDRAW}-2.svg`,
        categorySort: 6,
        hoverColor: '#DC33C3',
    },
};
// 场景元素
const menuItemsForScene: TMenuItemDisplayInfo = {
    7: {
        key: 'videoe', // 视频素材
        icon: 'iconfont icon-bianjiqi-xin-zuocedaohangshipinsucaimoren',
        hoverColor: '#962DFF',
        hover: 'iconfont icon-bianjiqi-xin-zuocedaohangshipinsucaixuanting',
        categorySort: 0,
    },
    8: {
        key: 'music', // 音乐素材
        name: '音乐库',
        // icon: 'iconfont icon-yinle2',
        icon: 'custom',
        icon_url: '//s.tuguaishou.com/img/logo-houziyinyue.svg',
        categorySort: 1,
    },
    14: {
        key: 'goodNewsMaterial', // 喜报元素
        icon: 'iconfont icon-bianjiqi-xin-zuocedaohangxibaoyuansumoren',
        hoverColor: '#F51A1A',
        hover: 'iconfont icon-bianjiqi-xin-zuocedaohangxibaoxuanting',
        categorySort: 2,
    },
    13: {
        key: 'medicalMaterial', //医疗元素
        icon: 'iconfont icon-bianjiqi-xin-zuocedaohangyiliaoyuansumoren',
        hoverColor: '#085BEA',
        hover: 'iconfont icon-bianjiqi-xin-zuocedaohangyiliaoxuanting1',
        categorySort: 3,
    },
    15: {
        key: 'findJobMaterial', // 招聘元素
        icon: 'iconfont icon-bianjiqi-xin-zuocedaohangzhaopinmoren',
        hoverColor: '#4C9F51',
        hover: 'iconfont icon-bianjiqi-xin-zuocedaohangzhaopinxuanting',
        categorySort: 4,
    },
    16: {
        key: 'schoolMaterial', // 校园日常
        icon: 'iconfont icon-bianjiqi-xin-zuocedaohangxiaoyuanmoren',
        hoverColor: '#085BEA',
        hover: 'iconfont icon-bianjiqi-xin-zuocedaohangxiaoyuanrichangxuanting',
        categorySort: 5,
    },
};
// 特色行业
const menuItemsForTrade: TMenuItemDisplayInfo = {
    19: {
        key: 'template3D', // 3d海报
        icon: 'custom',
        icon_url: 'https://s.tuguaishou.com/editor/image/3DIcon1.png',
        categorySort: 0,
    },
    39: {
        key: 'templateFinance', // 理财
        icon: 'custom',
        icon_url: 'https://s.tuguaishou.com/editor/image/menu-finance.png',
        categorySort: 1,
    },
    26: {
        key: 'templateEstate', // 地产
        icon: 'custom',
        icon_url: 'https://s.tuguaishou.com/editor/image/menu-estate2.png',
        categorySort: 2,
    },
    24: {
        key: 'templateBank', // 银行
        icon: 'custom',
        icon_url: 'https://s.tuguaishou.com/editor/image/menu-bank.png',
        categorySort: 3,
    },
    25: {
        key: 'templateBeauty', // 美业
        icon: 'custom',
        icon_url: 'https://s.tuguaishou.com/editor/image/menu-beauty.png',
        categorySort: 4,
    },
};
// 第三方资源
const thirdAsset: TMenuItemDisplayInfo = {
    46: {
        key: 'emojiPage', // 谷歌Emoji
        icon: 'custom',
        icon_url: 'https://s.tuguaishou.com/editor/image/menu-Emoji.png',
        categorySort: 1,
    },
    48: {
        key: 'baiduNetdisk', // 百度网盘
        icon: 'custom',
        icon_url: 'https://s.tuguaishou.com/editor/image/menu-bdyp.png',
        categorySort: 2,
        // hoverColor: '#EF3964',
    },
    47: {
        key: 'tencentMaps', // 腾讯地图
        icon: 'custom',
        icon_url: 'https://s.tuguaishou.com/editor/image/menu-txdt2.png',
        categorySort: 3,
    },
    50: {
        key: 'Pixabay', // Pixabay
        icon: 'custom',
        icon_url: 'https://s.tuguaishou.com/editor/image/menu-pixbay.png',
        categorySort: 4,
    },
    51: {
        key: 'lconduck', // lconduck
        icon: 'custom',
        icon_url: 'https://s.tuguaishou.com/editor/image/menu-iconduck.png',
        categorySort: 5,
    },
};
// 左侧固定菜单，不可添加进更多
const leftFixMenuItems: TMenuItemDisplayInfo = {
    55: {
        key: 'addMenu', // 添加菜单
        icon: 'iconfont icon-bianjiqi-xin-zuocedaohangtianjiamoren',
        hoverColor: '#EF3964',
        hover: 'iconfont icon-bianjiqi-xin-zuocedaohangtianjiaxuanting',
        sortFilter: true,
        fixedSort: 0,
    },
    17: {
        key: 'template', // 模板
        icon: 'iconfont icon-bianjiqi-xin-zuocedaohangmobanmoren',
        hoverColor: '#E86F1D',
        hover: 'iconfont icon-bianjiqi-xin-zuocedaohangmobanxuanting',
        sortFilter: true,
        fixedSort: 1,
    },
    3: {
        key: 'sourceMaterial', // 素材
        icon: 'iconfont icon-bianjiqi-xin-zuocesucaimobanmoren',
        hoverColor: '#784CEA',
        hover: 'iconfont icon-bianjiqi-xin-zuocedaohangsucaixuanting',
        sortFilter: true,
        fixedSort: 2,
    },
    1: {
        key: 'specificWord', // 特效字
        icon: 'iconfont icon-bianjiqi-xin-zuocewenzimoren',
        hoverColor: '#4C9F51',
        hover: 'iconfont icon-bianjiqi-xin-zuocedaohangwenzixuanting',
        sortFilter: true,
        fixedSort: 3,
    },
    44: {
        key: 'myPanel', //我的
        icon: 'iconfont icon-bianjiqi-xin-zuocedaohangwodemoren',
        hoverColor: '#456EEC',
        hover: 'iconfont icon-bianjiqi-xin-zuocedaohangwodexuanting',
        sortFilter: true,
        fixedSort: 4,
    },
    6: {
        key: 'gallery', // 背景
        icon: 'iconfont icon-bianjiqi-xin-zuocedaohangbeijingmoren',
        hoverColor: '#B34CEA',
        hover: 'iconfont icon-bianjiqi-xin-zuocedaohangbeijingxuanting',
        sortFilter: true,
        fixedSort: 6,
    },
    61: {
        key: ETool.AI_CHAT, // ai 聊天
        icon: 'iconfont icon-bianjiqi-xin-AIduihuamoren',
        hover: 'iconfont icon-bianjiqi-xin-AIduihuaxuanting',
        sortFilter: true,
        fixedSort: 7,
        // hoverColor: '#FF6A00',
    },
    '-1': {
        key: 'menuMore', // 应用
        icon: 'iconfont icon-bianjiqi-xin-zuocedaohangyingyongmoren',
        hoverColor: '#EF3964',
        hover: 'iconfont icon-bianjiqi-xin-zuocedaohangyingyongxuanting',
        sortFilter: true,
        fixedSort: 8,
    },
};

export const menuItems: TMenuItemDisplayInfo = {
    ...menuItemsForFunction,
    ...menuItemsForAi,
    ...menuItemsForScene,
    ...menuItemsForTrade,
    ...leftFixMenuItems,
    ...thirdAsset,
};

export const ecommerceMenuItems: TMenuItemDisplayInfo = {
    '-1': {
        key: 'menuMore', // 更多
        icon: 'iconfont icon-gengduo3', // 更多
    },
    28: {
        key: 'specificWord', // 特效字
        icon: 'iconfont icon-tupianbianjiqizuoceicon_texiaozi',
    },
    29: {
        key: 'sourceMaterial', // 素材
        icon: 'iconfont icon-tupianbianjiqizuoceicon_sucai1',
    },
    30: {
        key: 'pic', // 照片
        icon: 'iconfont icon-bianjiqi-xin-zuocedaohangzhaopianmoren',
        hoverColor: '#085BEA',
        hover: 'iconfont icon-bianjiqi-xin-zuocedaohangzhaopianxuanting',
    },
    35: {
        key: 'gallery', // 背景
        icon: 'iconfont icon-tupianbianjiqizuoceicon_beijing',
    },
    36: {
        key: 'animationEffect', // 页面动效
        icon: 'iconfont icon-yemiandongxiao',
    },
    27: {
        key: 'template', // 模板
        icon: 'iconfont icon-moban1',
    },
    33: {
        key: 'toolTik', // 工具
        icon: 'iconfont icon-tupianbianjiqizuoceicon_chuangyizifuben2',
    },
    34: {
        key: 'cutImg', // 智能抠图
        icon: 'iconfont icon-moshubang',
    },
    // 新加表格图表
    37: {
        key: 'tableChart', // 表格图表
        icon: 'iconfont icon-bianjiqi-xin-zuocedaohangbiaogetubiaomoren',
        hoverColor: '#085BEA',
        hover: 'iconfont icon-bianjiqi-xin-zuocedaohangbiaogexuanting',
    },
    38: {
        key: 'addCanvas', // 添加画布
        icon: 'iconfont icon-bianjiqi-daohang-tianjiahuabu',
    },
    41: {
        key: 'qrcodePanelFn', // 二维码
        icon: 'iconfont icon-bianjiqi-xin-zuocedaohangerweimamoren',
        hoverColor: '#085BEA',
        hover: 'iconfont icon-bianjiqi-xin-zuocedaohangerweimaxuanting',
    },
};
// 左侧的key不要变
export const menuType: Record<
    string,
    {
        text: string;
        menus: TMenuItem[];
    }
> = {
    1: {
        text: '热门',
        menus: [],
    },
    2: {
        text: 'AI工具',
        menus: [],
    },
    3: {
        text: '组件功能',
        menus: [],
    },
    4: {
        text: '第三方应用',
        menus: [],
    },
    5: {
        text: '场景元素',
        menus: [],
    },
    6: {
        text: '特色行业模板',
        menus: [],
    },
};
export const menuTypeMap: Record<string, string> = {
    1: '3', // 功能组件
    2: '5', // 场景元素
    3: '6', // 特色行业模板
    4: '4', // 第三方资源
    5: '2', // 图怪兽AI
};

export type TMenuItem = {
    id: string;
    menu_name: string;
    /** 菜单分类 1 素材 2 功能库 */
    menu_type: '1' | '2' | '3' | '4';
    /** 展示中的排序 */
    sort: number;
    /** 更多中的排序 */
    more_sort: number;
    /** 是否在更多中 */
    is_more: 0 | 1;
    delete_time?: number;
    action?: 'add' | 'delete'; // delete 用于标记后在服务器生成 delete_time
    online: '0' | '1';
    need_confirm?: string; //需要确认页面（第三方应用需要）：0不需要、1需要
    need_connect?: string; //需要绑定账号：0不需要、1:需要
};

export const appAuthorization: Record<
    string,
    {
        title: string;
        titleUrl: string;
        name: string;
        nameUrl: string;
        contentUrl: string;
        info: string;
    }
> = {
    47: {
        title: '腾讯地图',
        titleUrl: 'https://s.tuguaishou.com/editor/image/menu-txdt.png',
        name: '腾讯',
        nameUrl: 'https://map.qq.com/',
        contentUrl: 'https://s.tuguaishou.com/editor/image/bdwp-info.png',
        info: '在你的设计中添加地图来分享和发现地点',
    },
    48: {
        title: '百度网盘',
        titleUrl: 'https://s.tuguaishou.com/editor/image/menu-bdyp.png',
        name: '百度',
        nameUrl: 'https://pan.baidu.com',
        contentUrl: 'https://s.tuguaishou.com/editor/image/bdwp-info.png',
        info: '连接到百度网盘，即可将网盘的图片添加到你的设计当中。',
    },
};
export const haveThirdPage = ['47', '48']; // 第三方应用,是否还有隐私条款页面
// '46', '48', '47'
export const hotMenus = ['49', '52', '54']; // 热门菜单,顺序就是显示的顺序
export const fixedMenuIds = ['1', '3', '17', '44', '55','61', '6']; // 固定菜单

// 隐藏ppt不兼容的菜单
export const hideForPptMenus = ['7'];
