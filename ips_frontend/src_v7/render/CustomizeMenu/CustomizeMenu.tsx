import { assetManager } from '@src/userComponentV6.0/AssetManager';
import { emitter } from '@src/userComponentV6.0/Emitter';
import { SelectAsset } from '@v7_logic/AssetLogic';
import { loginCheckDecorator } from '@v7_logic/Decorators/Method';
import { storeAdapter } from '@v7_logic/StoreAdapter.bak';
import { storeDecorator } from '@v7_logic/StoreHOC';
import { ClickOutside } from '@v7_render/Ui';
import { IStoreState } from '@v7_store/redux/store';
import { IPSConfig } from '@v7_utils/IPSConfig';
import { EventSubscription } from 'fbemitter';
import { klona as cloneDeep } from 'klona';
import React, { PureComponent, useEffect, useRef, useState } from 'react';
import { ReactSortable, Sortable } from 'react-sortablejs';
import { TMenuItem, menuItems, ecommerceMenuItems, fixedMenuIds, menuPointMap, hideForPptMenus } from './config';
import { env } from '@editorConfig/env';
import './scss/CustomizeMenu.scss';
import { ICanvas } from '@v7_logic/Interface';
import { ErrorBoundaryDecorator } from '@v7_render/ErrorBoundaryHOC';
import { isUeAndEcommerce, isUeTeam } from '@v7_utils/webSource';
import { baseTools } from '@v7_utils/BaseTools';
import { isUeAndUeteam } from '@v7_utils/webSource';
import { getProps } from '@component/IPSConfig';

const cartConfig = env.editor === 'ecommerce' || env.editor === 'ecommerceteam' ? ecommerceMenuItems : menuItems;

function isElementInViewport(el) {
    const rect = el.getBoundingClientRect();
    console.log(rect)
    return (
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) - 112
    );
}

const MenuItem = function MenuItem({
    menu,
    activedMenuId,
    dragMenuId,
    isShowDragTip,
    isHover,
    onMenuItemClick,
    deleteMenu,
    overMenu,
    leaveMenu,
}: {
    menu: TMenuItem;
    activedMenuId: string;
    dragMenuId: string;
    isShowDragTip: boolean;
    isHover: boolean;
    onMenuItemClick: (e: React.MouseEvent<HTMLElement>) => void;
    deleteMenu: (e: React.MouseEvent<HTMLElement>) => void;
    overMenu: (e: React.MouseEvent<HTMLElement>) => void;
    leaveMenu: (e: React.MouseEvent<HTMLElement>) => void;
}) {
    const [shouldShowTip, setShowTip] = useState(true);
    const showTip = () => {
        if (localStorage.getItem('add_canvas_add_has_shown') === 'true') {
            return false;
        } else {
            return true;
        }
    };
    const node = useRef(null);
    if (env.editor === 'ecommerce' || env.editor === 'ecommerceteam') {
        emitter.addListener('emitterOpenAddCanvas', () => {
            if (
                node.current &&
                node.current.className.includes('active') &&
                (document.querySelector('.rightLayout') as HTMLElement).style.width === '360px'
            )
                return false;
            node.current && node.current.click();
        });
    }

    const onShowTip = () => {
        if (env.editor === 'ecommerce' || env.editor === 'ecommerceteam') {
            localStorage.setItem('add_canvas_add_has_shown', 'true');
            setShowTip(false);
        } else {
            return;
        }
    };
    const activeMenu = menu.id === activedMenuId;
    const activeMenuClassName = activeMenu
        ? ['49', '52', '53', '54', , '56', '57', '58'].includes(activedMenuId)
            ? 'active aiActive'
            : 'active'
        : '';
    if (menu.id === '54') menu.online = '1';
    return (
        // <div className="customize-menu-item-container">
        // ai模块菜单颜色需要自定义
        <div
            className={`customize-menu-item ${activeMenuClassName} ${
                /* menu.id === dragMenuId && isShowDragTip ? 'dragging' : */ ''
            } ${env.fixedMenu && cartConfig[menu.id]?.sortFilter ? 'ignore' : ''} ${
                isHover ? 'hover' : ''
            }`}
            id={'customize-menu-' + menu.id}
            key={menu.id}
            data-menuid={menu.id}
            data-fixed-sort={cartConfig[menu.id]?.fixedSort}
            onClick={(e) => {
                onMenuItemClick(e);
                onShowTip();
            }}
            onMouseOver={overMenu}
            onMouseLeave={leaveMenu}
            ref={(ref) => {
                if (ref === document.querySelector('#customize-menu-38')) {
                    node.current = ref;
                }
            }}
        >
            {cartConfig[menu.id]?.key === 'addCanvas' && showTip() && (
                <div className="new" hidden={!shouldShowTip}></div>
            )}
            {cartConfig[menu.id]?.icon !== 'custom' && (
                <>
                    <i className={`customize-menu-icon ${cartConfig[menu.id]?.icon}`}></i>
                    {cartConfig[menu.id]?.hover && (
                        <i
                            className={`customize-menu-icon hover ${cartConfig[menu.id]?.hover}`}
                            style={
                                cartConfig[menu.id]?.hoverColor ? { color: cartConfig[menu.id]?.hoverColor } : undefined
                            }
                        ></i>
                    )}
                </>
            )}
            {cartConfig[menu.id]?.icon === 'custom' && (
                <div className="menu-img-box">
                    <img className={menu.id === '49' ? 'aiCustomImg' : ''} src={cartConfig[menu.id].icon_url} />
                </div>
            )}
            <span className={`customize-menu-item-text ${menu.menu_type == '4' ? 'thirdAsset' : ''}`}
                style={
                    activeMenu && cartConfig[menu.id]?.hoverColor ? { color: cartConfig[menu.id]?.hoverColor } : undefined
                }
            >
                {cartConfig[menu.id]?.name || menu.menu_name}
            </span>
            {!(env.fixedMenu && cartConfig[menu.id]?.sortFilter) && (
                <i className="iconfont icon-bianjiqixin-zuocedaohang-shanchuyingyong" data-menuid={menu.id} onClick={deleteMenu}></i>
            )}
            {/* <div className="customize-menu-item-drag-tip">
                <div className="customize-menu-item-drag-tip-triangle first"></div>
                <div className="customize-menu-item-drag-tip-text">长按后拖动排序</div>
                <div className="customize-menu-item-drag-tip-triangle"></div>
            </div> */}
        </div>
        // </div>
    );
};

interface ICustomizeMenuProps {
    user?: {
        userId: string;
    };
    isDisableHotKey?: boolean;
    isTeam?: boolean;
    canvas: ICanvas;
    isDesigner: boolean;
    rt_currentNav?: string;
    rt_canvas_render_mode?: '' | 'pull' | 'board';
    info?: IStoreState['InfoManage']['info'];
    onItemEnter?: (navType: string, menuId: string, menuName: string) => void;
    onItemLeave?: () => void;
    isHover?: boolean;
}

interface ICustomizeMenuState {
    activedMenuId: string;
    menuList: TMenuItem[];
    dragMenuId: string;
    isShowDragTip: boolean;
    tipTop: number;
    sort: number;
    isVisible: 'visible' | 'hidden' | 'collapse';
    revertActions: Record<
        string,
        {
            originAction: 'add' | 'delete' | 'sort';
            menuItem: TMenuItem;
            enter: boolean;
            out: boolean;
            time: number; // Date().getTime()
        }
    >;
}

@ErrorBoundaryDecorator()
@storeDecorator((state: { onCanvasPainted: IStoreState['onCanvasPainted']; hotKeyStore: IStoreState['HotKey'], infoManageStore: IStoreState['InfoManage'] }) => {
    return {
        user: state.onCanvasPainted.user,
        isDisableHotKey: state.hotKeyStore.isDisableHotKey,
        canvas: state.onCanvasPainted.canvas,
        isDesigner: state.onCanvasPainted.isDesigner,
        rt_currentNav: state.onCanvasPainted.rt_currentNav,
        rt_canvas_render_mode: state.onCanvasPainted.rt_canvas_render_mode,
        info: state.infoManageStore.info,
    };
})
export class CustomizeMenu extends PureComponent<ICustomizeMenuProps, ICustomizeMenuState> {
    navType = 'specificWord';
    customMenuPosition = false;
    isNavFlag = true;
    // 自定义菜单id，通过url传入
    customMenuId: string;
    materialTabItemClickEventEmitter: EventSubscription;
    rightLayoutShowClickEmitter: EventSubscription;
    onMenuMoreEditEmitter: EventSubscription;
    changeActivedMenuIdEmitter: EventSubscription;
    listenerMenuShowStatus: EventSubscription;
    changeActivedMenuEmitter: EventSubscription;
    onAddMenuEmitter: EventSubscription;
    mouseDownMenuId: string;
    dragTimer: ReturnType<typeof window.setTimeout>;
    windowWidth: number;
    oldMenuList: TMenuItem[];
    status: '' | 'hide';

    state: ICustomizeMenuState = {
        activedMenuId: undefined,
        menuList: [],
        dragMenuId: undefined,
        isShowDragTip: false,
        revertActions: {},
        tipTop: 0,
        sort: 0,
        isVisible: 'hidden',
    };
    overFlag: boolean;

    constructor(props: ICustomizeMenuProps) {
        super(props);
        this.windowWidth = window.innerWidth;
        this.rightLayoutShowClickEmitter = emitter.addListener(
            'rightLayoutShowClick',
            (clickOrigin: string, addOrigin: string) => {
                this.onNavItemClick(clickOrigin, addOrigin);
            },
        );
        this.onMenuMoreEditEmitter = emitter.addListener(
            'onMenuMoreEdit',
            (action: 'add' | 'sort' | 'delete', newMenuList: TMenuItem[], menuId?: string) => {
                if (action === 'add') {
                    const menuListOnline = newMenuList.map((menu) => {
                        if (menuId === menu.id) {
                            return { ...menu, need_confirm: '0' };
                        } else {
                            return menu;
                        }
                    });
                    this.saveMenuList(menuListOnline, 'add', menuId);
                }
                if (action === 'sort') {
                    this.saveMenuList(newMenuList, 'sort', menuId);
                }
                // if (action === 'delete') {
                //     const menuListOnline = newMenuList.map(menu => {
                //         if (menuId === menu.id &&  menu.id === '48') {
                //             return { ...menu, need_confirm: '1'};
                //         } else {
                //             return menu;
                //         }
                //     })
                //     this.saveMenuList(menuListOnline, 'delete', menuId);
                // }
            },
        );
        this.changeActivedMenuIdEmitter = emitter.addListener('changeActivedMenuId', (menuId?: string) => {
            this.setState({ activedMenuId: menuId });
        });
        // 监听当前菜单的关闭展开功能
        this.listenerMenuShowStatus = emitter.addListener(
            'changeMenuItemLayout',
            ({ currentNav, open }: { currentNav: string; open: boolean }) => {
                if (currentNav === 'aiDraw' && open) {
                    assetManager.setPv_new(7474);
                }
                // if (currentNav === 'aiText' && open) {
                //     assetManager.setPv_new(7475);
                // }
                if (currentNav === 'aiProducts' && open) {
                    assetManager.setPv_new(7550);
                }
            },
        );
        this.customMenuId = baseTools.getUrlQuery('menu');
        this.getMenuList();
        this.overFlag = true;
        // this.myRef = React.createRef<HTMLInputElement>();
    }

    componentDidMount() {
        document.addEventListener('keyup', this.addNavHotKey);
        document.getElementsByClassName('customize-menu')[0].addEventListener('scroll', this.scrollHideTip, false);
        this.emitterGeneratorBusinessCanvas();
        this.changeActivedMenuEmitter = emitter.addListener('changeActiveMenu', (menuId: string) => {
            if (!menuId) return;
            this.customMenuPosition = true;
            this.navType = cartConfig[menuId].key;
            this.setState({ activedMenuId: menuId });
            emitter.emit('onNavChanged', this.navType);
            // emitter.emit('rightLayoutHideClick', '', this.navType, undefined);
        });
        // 添加菜单
        this.onAddMenuEmitter = emitter.addListener('onAddMenuEmitter', (menuId: string) => {
            const { menuList } = this.state;
            const newMenuList = cloneDeep(menuList);
            const addMenuItem = newMenuList.find((m) => m.id === menuId);
            // 已经添加了的菜单不再添加
            if (addMenuItem.is_more === 0) {
                emitter.emit('changeActiveMenu', menuId);
                emitter.emit('rightLayoutHideClick', '', this.navType, undefined);
            } else {
                newMenuList.map((m) => {
                    if (
                        m.is_more === 1 /* && m.menu_type === addMenuItem.menu_type */ &&
                        m.more_sort > addMenuItem.more_sort
                    ) {
                        m.more_sort--;
                    }
                });
                addMenuItem.sort = newMenuList.filter((m) => m.is_more === 0).length;
                addMenuItem.more_sort = -1;
                addMenuItem.is_more = 0;
                addMenuItem.action = 'add';
                emitter.emit('onMenuMoreEdit', 'add', newMenuList, menuId);
            }

            assetManager.setPv_new(6289, {
                additional: {
                    s0: menuId,
                    s1: 'click',
                },
            });
            storeAdapter.dispatch({
                fun_name: 'updateApp',
                store_name: storeAdapter.store_names.appStore,
                params: [
                    {
                        type: 'updateAppMenu',
                        params: {
                            menu: {
                                menuKey: cartConfig[addMenuItem.id].key,
                            },
                        },
                    },
                ],
            });
        });
    }

    componentWillUnmount() {
        this.rightLayoutShowClickEmitter?.remove();
        this.onMenuMoreEditEmitter?.remove();
        this.changeActivedMenuIdEmitter?.remove();
        this.listenerMenuShowStatus?.remove();
        this.changeActivedMenuEmitter?.remove();
        this.onAddMenuEmitter?.remove();
        document.removeEventListener('keyup', this.addNavHotKey);
        document.getElementsByClassName('customize-menu')[0].removeEventListener('scroll', this.scrollHideTip, false);
    }

    componentDidUpdate(prevProps: Readonly<ICustomizeMenuProps>, prevState: Readonly<ICustomizeMenuState>) {
        const { rt_is_online_detail_page } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const status = rt_is_online_detail_page ? 'hide' : '';
        if (
            Number.parseInt(this.props.user?.userId) !== Number.parseInt(prevProps.user?.userId) &&
            this.props.user?.userId !== undefined
        ) {
            this.getMenuList(this.props.user?.userId);
        }
        if (this.props.isTeam !== prevProps.isTeam) {
            this.getMenuList(this.props.user?.userId);
        }
        const { menuList } = this.state;
        //工作台通过上传的图片点击进入编辑器，也默认展开模板列表
        const urlProps = IPSConfig.getProps(true);
        const conditionFlag =
            menuList.length > 0 &&
            this.isNavFlag &&
            (window.user_template_get_ended || urlProps.user_asset_id || urlProps.menu);
        
        if (conditionFlag) {
            // 通过url传入的需要默认打开的菜单优先级最高
            const menuItem = menuList.find((m) => {
                if (this.customMenuId) return m.id === this.customMenuId;
                return ['55', '27', '17'].includes(m.id);
            });
            this.isNavFlag = false;
            this.navType = menuItem ? cartConfig[menuItem.id].key : getProps().menu || 'addMenu';
            if (menuItem) {
                const activedMenuId = menuItem?.is_more !== 1 ? menuItem.id : '-1';
                // this.setState({
                //     activedMenuId,
                // });
            }
            // setTimeout(() => {
            if (this.navType) {
                emitter.emit('onNavChanged', this.navType);
                if (this.props.info?.template_type === 3 || this.props.rt_canvas_render_mode === 'pull') {
                } else {
                    emitter.emit('rightLayoutHideClick', 'hover', this.navType, undefined);
                }
            }
            this.status = status;
            // }, 0);
        } else {
            if (this.customMenuPosition || !this.customMenuId) {
                if (!this.status && status == 'hide') {
                    emitter.emit('rightLayoutHideClick', status);
                    this.status = status;
                }
                return;
            }
            // 通过url传入的需要默认打开的菜单优先级最高
            const menuItem = menuList.find((m) => {
                return m.id === this.customMenuId;
            });
            if (menuItem) {
                this.customMenuPosition = true;
                this.navType = cartConfig[menuItem.id].key;
                this.setState({
                    activedMenuId: menuItem.id,
                });
                // setTimeout(() => {
                emitter.emit('onNavChanged', this.navType);
                emitter.emit('rightLayoutHideClick', status, this.navType, undefined);
                this.status = status;
                // }, 0);
            }

            // 空白画布
        }
        // if (this.props.rt_currentNav === 'qrcodePanelFn' && cartConfig[this.state.activedMenuId] !== 'qrcodePanelFn') {
        //     let menuId: string = undefined;
        //     for (const key in cartConfig) {
        //         if (cartConfig[key].key === 'qrcodePanelFn') {
        //             menuId = key;
        //             break
        //         }
        //     }
        //     if (menuId && this.state.menuList?.length > 0) {
        //         const menu = this.state.menuList.find(i => i.id === menuId)
        //         if (menu?.is_more === 0) {
        //             this.setState({
        //                 activedMenuId: menuId
        //             })
        //         }
        //     }
        // }
    }

    scrollHideTip = () => {
        this.setState({
            isVisible: 'hidden',
        });
        this.overFlag = true;
    };

    // 触发批量生成商品图,更新active_menu_id
    emitterGeneratorBusinessCanvas() {
        emitter.addListener('setAddCanvasAsCurrentMenuId', () => {
            if (this.state.activedMenuId !== '38') {
                const navType = 'addCanvas';
                emitter.emit('onNavChanged', navType);
                emitter.emit('rightLayoutHideClick', '', navType, undefined);
                this.setState({
                    activedMenuId: '38',
                });
            }
        });
    }

    addNavHotKey = (e: KeyboardEvent) => {
        if (this.props.isDisableHotKey && e.key >= '1' && e.key <= '9') {
            return;
        }
        const ele = e.target as HTMLElement;
        const flag = ele.getAttribute('contenteditable');
        if (ele.tagName.toLowerCase() === 'input' || ele.tagName.toLowerCase() === 'textarea' || flag) {
            return;
        }
        e.preventDefault();
        if (e.key >= '1' && e.key <= '9') {
            const buryShortcut = [6714, 6715, 6716, 6717, 6718, 6719, 6720, 6721, 6722];
            const nodes = document.getElementsByClassName('sort-menu')[0].childNodes;
            const key = Number(e.key) - 1;
            if (nodes.length > key) {
                const childnode = (nodes[key] as HTMLElement).childNodes[1];
                const text = (childnode as HTMLElement).innerText;
                assetManager.setPv_new(buryShortcut[key], {
                    additional: { s0: text },
                });
                const nodeDataSet = (nodes[key] as HTMLElement).dataset.menuid;
                const navType = cartConfig[nodeDataSet].key;
                if (navType === 'cutImg') {
                    emitter.emit('onNavChanged', navType);
                    emitter.emit('cutImgClick');
                    return;
                }
                this.setState(
                    {
                        activedMenuId: nodeDataSet,
                    },
                    () => {
                        emitter.emit('onNavChanged', navType);
                        emitter.emit('rightLayoutHideClick', '', navType, undefined);
                    },
                );
            }
        }
    };
    // type effect 页面其他副作用出发，否则被认为是初始化请求出发
    getMenuList = async (userId?: string, type?: string | undefined) => {
        try {
            const editor_type = env.editor === 'ecommerce' || env.editor === 'ecommerceteam' ? 2 : 1;
            const res = await assetManager.getUserMenuList(userId, editor_type);
            const { info } = storeAdapter.getStore({
                store_name: storeAdapter.store_names.InfoManage,
            });
            if (res.stat === 1) {
                // if (!userId && this.props.user?.userId) {
                //     return;
                // }

                let menuList = res.data.config;
                if (typeof menuList === 'string') {
                    menuList = JSON.parse(menuList);
                }
                menuList = (menuList as TMenuItem[]).filter((m) => cartConfig[m.id]);

                (menuList as TMenuItem[]).forEach((m) => {
                    m.is_more = Number.parseInt(`${m.is_more}`) as 0 | 1;
                    if (env.fixedMenu && fixedMenuIds.includes(m.id)) {
                        m.is_more = 0;
                        m.sort = cartConfig[m.id].fixedSort - fixedMenuIds.length; // 处理成负数 保证 sort 排序永远在最前
                    }
                    if (m.is_more === 0 && m.more_sort === undefined) {
                        m.more_sort = -1;
                    }
                    if (m.id === this.customMenuId && type !== 'effect') {
                        m.is_more = 0;
                    }
                });
                let arr = (menuList as TMenuItem[]).filter((m) => m.is_more === 1);
                if (arr.length > 0) {
                    arr.sort((a, b) => {
                        if (a.more_sort >= 0 && b.more_sort === undefined) {
                            return 1;
                        }
                        if (b.more_sort >= 0 && a.more_sort === undefined) {
                            return -1;
                        }
                        if (a.more_sort >= 0 && b.more_sort >= 0) {
                            if (a.more_sort === b.more_sort) {
                                if (a.delete_time && b.delete_time) {
                                    return b.delete_time - a.delete_time;
                                }
                                if (a.delete_time && !b.delete_time) {
                                    return 1;
                                }
                                if (!a.delete_time && b.delete_time) {
                                    return -1;
                                }
                                return Number.parseInt(a.id) - Number.parseInt(b.id);
                            }
                            return a.more_sort - b.more_sort;
                        }
                        if (a.delete_time && b.delete_time) {
                            return b.delete_time - a.delete_time;
                        }
                        if (a.delete_time && !b.delete_time) {
                            return 1;
                        }
                        if (!a.delete_time && b.delete_time) {
                            return -1;
                        }
                        return Number.parseInt(a.id) - Number.parseInt(b.id);
                    });
                    arr.forEach((m, i) => {
                        m.sort = -1;
                        m.more_sort = i;
                    });
                }
                if (!this.props.isTeam && (env.editor === 'ue' || isUeTeam) /* env.editor === 'ueteam' */) {
                    const teamMenu = (menuList as TMenuItem[]).find((m) => m.id === '12');
                    if (teamMenu.is_more === 0) {
                        teamMenu.is_more = 1;
                        teamMenu.sort = -1;
                        teamMenu.more_sort = arr.length;
                    } else if (teamMenu.is_more === 1) {
                        teamMenu.more_sort = arr.length + 1;
                        arr.sort((a, b) => a.more_sort - b.more_sort);
                        arr.forEach((m, i) => {
                            m.more_sort = i;
                        });
                    }
                }
                arr = (menuList as TMenuItem[]).filter((m) => m.is_more === 0).sort((a, b) => a.sort - b.sort);
                arr.forEach((m, i) => {
                    m.sort = i;
                    m.more_sort = -1;
                });
                (menuList as TMenuItem[]).sort((a, b) => {
                    return a.sort - b.sort;
                });
                const activedMenuId = this.state.activedMenuId;
                // if (['ue', 'ueteam'].includes(env.editor)) {
                //     const activedMenuItem = menuList.find((m: TMenuItem) => m.id === activedMenuId);
                //     if (
                //         !getProps().menu &&
                //         activedMenuId !== '-1' &&
                //         (!activedMenuItem || activedMenuItem?.is_more === 1)
                //     ) {
                //         // 默认不让'添加'展示
                //         activedMenuId =
                //             menuList.find((m: TMenuItem) => m.online === '1' && m.is_more === 0 && m.sort > -1)?.id ||
                //             '-1';
                //         if (Number.parseInt(activedMenuId) >= 0) {
                //             emitter.emit('onNavChanged', cartConfig[activedMenuId].key);
                //         }
                //     }
                // }
                let resMenuList = cloneDeep(menuList);
                if (['ecommerce'].includes(env.editor)) {
                    resMenuList = menuList.filter((item: TMenuItem) => {
                        return item.id !== '38';
                    });
                }
                // ppt模板 隐藏不兼容的元素
                if (info.template_type == '3' && env.hidePptIncompatibleAsset) {
                    // lyy
                    resMenuList = menuList.filter((item: TMenuItem) => {
                        return !hideForPptMenus.includes(item.id);
                    });
                }
                // resMenuList = resMenuList.map((item:TMenuItem) => {
                //     if (item.id === '48' || item.id ==='47') {
                //         return {...item, online: '1'}
                //     } else {
                //         return {...item}
                //     }
                // })
                this.setState({
                    menuList: resMenuList,
                    activedMenuId,
                });
                emitter.emit('onMenuUpdate', resMenuList, this.props.isTeam);
                // 暂时下线
                // if (
                //     String(res.data.is_first) === '0' &&
                //     this.props.user?.userId &&
                //     Number.parseInt(this.props.user.userId) > 0
                // ) {
                //     this.showDragMenuTip();
                // }
            }
        } catch (error) {
            console.error('get user menu list fail: ', error);
        }
    };

    @loginCheckDecorator
    saveMenuList = async (
        menuList: TMenuItem[],
        action?: 'add' | 'delete' | 'revert' | 'sort',
        menuId?: string,
        isFirst?: boolean,
    ) => {
        try {
            if (action !== 'revert') {
                this.oldMenuList = cloneDeep(this.state.menuList);
            }
            menuList.forEach((m: TMenuItem & { chosen?: boolean; selected?: boolean }) => {
                delete m.chosen;
                delete m.selected;
            });
            this.setState({
                menuList: cloneDeep(menuList).sort((a, b) => a.sort - b.sort),
            });
            emitter.emit('onMenuUpdate', menuList, this.props.isTeam);
            const editor_type = env.editor === 'ecommerce' || env.editor === 'ecommerceteam' ? 2 : 1;
            const res = await assetManager.saveUserMenuList(
                this.props.user?.userId,
                menuList.sort((a, b) => Number.parseInt(a.id) - Number.parseInt(b.id)),
                isFirst,
                editor_type,
            );
            if (res.stat === 1) {
                this.getMenuList(this.props.user?.userId, 'effect');
                if (action === 'add') {
                    this.revertMenuAction(action, menuList, menuId);
                    this.navType = cartConfig[menuId].key;
                    this.onNavItemClick();
                    this.setState({
                        activedMenuId: menuId,
                    });
                } else if (action === 'delete') {
                    this.revertMenuAction(action, menuList, menuId);
                } else if (action === 'revert') {
                    this.setState({
                        revertActions: {
                            ...this.state.revertActions,
                            [menuId]: undefined,
                        },
                    });
                } else if (action === 'sort') {
                    this.revertMenuAction(action, menuList, menuId);
                }
                if (isFirst) {
                    emitter.emit('popupClose');
                }
            } else {
                if (!isFirst) {
                    emitter.emit('PromptBox', {
                        windowContent: '菜单编辑操作保存失败',
                    });
                    setTimeout(() => {
                        emitter.emit('PromptBoxClose');
                    }, 3000);
                }
            }
        } catch (error) {
            console.error('save user menu list fail: ', error);
        }
    };

    revertMenuAction(action: 'add' | 'delete' | 'sort', menuList: TMenuItem[], menuId: string) {
        const menuItem = menuList.find((m) => m.id === menuId);
        if (menuItem) {
            this.windowWidth = window.innerWidth;
            const newRevertActions = cloneDeep(this.state.revertActions);
            const key = action + '-' + Math.round(Math.random() * 100000) + '-' + new Date().getTime();
            for (const k in newRevertActions) {
                if (newRevertActions[k]) {
                    newRevertActions[k].enter = false;
                    newRevertActions[k].out = true;
                    const t = setTimeout(() => {
                        this.setState({
                            revertActions: {
                                ...this.state.revertActions,
                                [k]: undefined,
                            },
                        });
                        clearTimeout(t);
                    }, 100);
                } else {
                    delete newRevertActions[k];
                }
            }
            newRevertActions[key] = {
                originAction: action,
                menuItem: menuItem,
                enter: false,
                out: false,
                time: new Date().getTime(),
            };
            this.messageAnimation(newRevertActions, key, action === 'sort' ? 1700 : 4700);
        }
    }

    messageAnimation(newRevertActions: ICustomizeMenuState['revertActions'], key: string, delay: number) {
        this.setState(
            {
                revertActions: newRevertActions,
            },
            () => {
                this.setState({
                    revertActions: {
                        ...this.state.revertActions,
                        [key]: {
                            ...this.state.revertActions[key],
                            enter: true,
                        },
                    },
                });
            },
        );
        setTimeout(() => {
            if (this.state.revertActions[key]) {
                this.setState(
                    {
                        revertActions: {
                            ...this.state.revertActions,
                            [key]: {
                                ...this.state.revertActions[key],
                                enter: false,
                                out: true,
                            },
                        },
                    },
                    () => {
                        setTimeout(() => {
                            if (this.state.revertActions[key]) {
                                this.setState({
                                    revertActions: {
                                        ...this.state.revertActions,
                                        [key]: undefined,
                                    },
                                });
                            }
                        }, 300);
                    },
                );
            }
        }, delay);
    }

    onNavItemClick(clickOrigin?: string, addOrigin?: string, menuName?: string, isMore?: string) {
        let navType = this.navType;
        SelectAsset.blurAsset();
        if (typeof clickOrigin === 'string') {
            if (clickOrigin === this.navType) {
                navType = clickOrigin;
                this.materialTabItemClickEventEmitter = emitter.once('materialAssetDidMount', () => {
                    if (addOrigin) {
                        emitter.emit('galleryNavItemClickEvent', addOrigin);
                    }
                    emitter.emit('materialTabItemClickEvent');
                });
            } else {
                return;
            }
        }
        if (navType === 'menuMore' && ['ecommerce', 'ue'].includes(env.editor) && !isUeTeam) {
            assetManager.setPv_new(6963);
        }
        if (navType === 'cutImg') {
            // window.open('https://818ps.com/koutu?origin=ecommerce_leftNav_cutImg', '_blank')
            emitter.emit('onNavChanged', navType);
            // emitter.emit('cutImgClick');
            emitter.emit('rightLayoutHideClick', 'hide');
            // 打开上传图片框
            emitter.emit('UserUploadBoxUploadFile', 'leftMatting');
            assetManager.setPv_new(3846, {
                additional: {},
            });
            return;
        } else if (navType === 'addMenu' || navType === 'specificWord') {
            // 关闭常用文案
            emitter.emit('closeCommonWritingEvent');
        }
        emitter.emit('onNavChanged', navType);
        emitter.emit('rightLayoutHideClick', '', navType, typeof clickOrigin === 'string' ? 'collection' : undefined);
        assetManager.setPv_new(6806, {
            additional: { s0: menuName, s1: isMore },
        });
        // const { info } = storeAdapter.getStore<typeof storeAdapter.store_names.InfoManage>({
        //     store_name: storeAdapter.store_names.InfoManage,
        // });
        // const is_firm = info?.is_company_temp === 1;
        if (menuPointMap[navType]) {
            if (['specificWord', 'background', 'pic', 'copywriting'].includes(navType)) {
                assetManager.setPv_new(menuPointMap[navType]?.[1]);
            } else {
                assetManager.setPv_new(menuPointMap[navType]);
            }
        }
    }

    onMenuItemClick = (e: React.MouseEvent<HTMLElement>, menuName?: string, isMore?: string) => {
        const { menuid } = e.currentTarget.dataset;
        if (!isElementInViewport(e.currentTarget)) {
            e.currentTarget.scrollIntoView(false);
        }
        if (menuid === '-1' && this.navType !== cartConfig['-1'].key) {
            assetManager.setPv_new(6288);
        }
        this.navType = cartConfig[menuid].key;
        this.onNavItemClick(undefined, undefined, menuName, isMore);
        if (Number.parseInt(menuid) === -1) {
            setTimeout(() => {
                emitter.emit('onMenuUpdate', this.state.menuList, this.props.isTeam);
            }, 0);
        }
        // 活动状态下，被点击的菜单项需要取消活动状态
        const cancelActive = e.currentTarget.classList.toString().indexOf('active') !== -1;

        this.setState({
            activedMenuId: cancelActive ? undefined : menuid,
        });
        // restore current menu
        storeAdapter.dispatch({
            fun_name: 'updateApp',
            store_name: storeAdapter.store_names.appStore,
            params: [
                {
                    type: 'updateAppMenu',
                    params: {
                        menu: {
                            // id: e.currentTarget.dataset.menuid
                            menuKey: cancelActive ? undefined : cartConfig[e.currentTarget.dataset.menuid].key,
                        },
                    },
                },
            ],
        });
    };

    onIsMore = (e: React.MouseEvent<HTMLDivElement>) => {
        assetManager.setPv_new(6651);
        e.buttons === 0 && this.props.onItemEnter?.(cartConfig['-1'].key, '-1', '应用');
        setTimeout(() => {
            emitter.emit('onMenuUpdate', this.state.menuList, this.props.isTeam);
        }, 330);
    };

    onClickOutSide = () => {
        this.setState({
            dragMenuId: undefined,
            isShowDragTip: false,
        });
    };

    @loginCheckDecorator
    deleteMenu = (e: React.MouseEvent<HTMLElement>) => {
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
        const { menuid } = e.currentTarget.dataset;
        const { menuList } = this.state;
        const newMenuList = cloneDeep(menuList);
        const deleteMenuItem = newMenuList.find((m) => m.id === menuid);
        newMenuList.map((m) => {
            if (m.is_more === 0 && m.sort > deleteMenuItem.sort) {
                m.sort--;
            }
            if (m.is_more === 1 /* && m.menu_type === deleteMenuItem.menu_type */) {
                m.more_sort++;
            }
        });
        deleteMenuItem.sort = -1;
        deleteMenuItem.more_sort = 0;
        deleteMenuItem.is_more = 1;
        deleteMenuItem.action = 'delete';
        this.saveMenuList(newMenuList, 'delete', menuid);
        assetManager.setPv_new(6287, {
            additional: {
                s0: menuid,
                s1: 'click',
            },
        });
        this.navType = cartConfig['-1'].key;
        this.onNavItemClick();
        this.setState({
            activedMenuId: '-1',
        });
        this.leaveMenu();
    };

    revertMenu = (e: React.MouseEvent<HTMLElement>) => {
        const { menuid, originaction } = e.currentTarget.dataset;
        // const { menuList } = this.state;
        // const newMenuList = cloneDeep(menuList);
        // const revertMenuItem = newMenuList.find((m) => m.id === menuid);
        if (originaction === 'add') {
            // revertMenuItem.is_more = 1;
            // revertMenuItem.action = 'delete';
            assetManager.setPv_new(6295, {
                additional: {
                    s0: menuid,
                },
            });
        } else if (originaction === 'delete') {
            // revertMenuItem.is_more = 0;
            // revertMenuItem.action = 'add';
            assetManager.setPv_new(6296, {
                additional: {
                    s0: menuid,
                },
            });
        }
        this.saveMenuList(this.oldMenuList, 'revert', menuid);
    };

    @loginCheckDecorator
    onSortStart = (e: Sortable.SortableEvent) => {
        this.setState({
            dragMenuId: e.item.dataset.menuid,
        });
    };

    @loginCheckDecorator
    onSortEnd = (e: Sortable.SortableEvent) => {
        let {
            newIndex,
            oldIndex,
            item: {
                dataset: { menuid },
            },
        } = e;
        newIndex -= 3;
        oldIndex -= 3;
        console.log(newIndex, oldIndex)
        const toMoreMenu = /sort-menu/.test(e.from.className) && /sort-menu-more/.test(e.to.className);
        const toMenu = !toMoreMenu;
        if (toMenu && oldIndex === newIndex) {
            return;
        }
        const { menuList } = this.state;
        const newMenuList = cloneDeep(menuList);
        const dragMenuItem = newMenuList.find((m) => m.id === menuid);
        let action: 'delete' | 'sort';
        if (toMenu) {
            action = 'sort';
            if (dragMenuItem.sort > newIndex) {
                newMenuList.forEach((m) => {
                    if (m.sort >= newIndex && m.sort < dragMenuItem.sort) {
                        m.sort++;
                    }
                });
                dragMenuItem.sort = newIndex;
            } else {
                newMenuList.forEach((m) => {
                    if (m.sort > dragMenuItem.sort && m.sort <= newIndex) {
                        m.sort--;
                    }
                });
                dragMenuItem.sort = newIndex;
            }
            assetManager.setPv_new(6306, {
                additional: {
                    s0: 'menu',
                    s1: 'sort',
                },
            });
        } else if (toMoreMenu) {
            action = 'delete';
            newMenuList.forEach((m) => {
                if (m.sort > dragMenuItem.sort) {
                    m.sort--;
                }
                if (/* m.menu_type === dragMenuItem.menu_type && */ m.more_sort >= newIndex) {
                    m.more_sort++;
                }
            });
            dragMenuItem.action = 'delete';
            dragMenuItem.sort = -1;
            dragMenuItem.is_more = 1;
            dragMenuItem.more_sort = newIndex;
            assetManager.setPv_new(6287, {
                additional: {
                    s0: dragMenuItem.id,
                    s1: 'drag',
                },
            });
            assetManager.setPv_new(6306, {
                additional: {
                    s0: 'menu',
                    s1: 'delete',
                },
            });
        }
        this.saveMenuList(newMenuList, action, dragMenuItem.id);
        this.setState({
            dragMenuId: undefined,
            isShowDragTip: false,
        });
        emitter.emit('onMenuUpdate', newMenuList, this.props.isTeam);
    };

    onSortMove = (e: Sortable.MoveEvent) => {
        return e.related.className.indexOf('ignore') === -1;
    };

    @loginCheckDecorator
    showDragMenuTip = () => {
        const windowInfo = {
            windowContent: (
                <div className="customize-menu-drag-guide">
                    <img
                        className="customize-menu-drag-guide-img"
                        src={IPSConfig.imgHost + '/editor/menu-guide-static-menu.png'}
                        data-tag="img"
                        onClick={this.hideGuide}
                    />
                    <div className="customize-menu-drag-guide-modal">
                        <div className="customize-menu-drag-guide-modal-title">
                            <span>自定义菜单</span>
                            <i className="iconfont icon-xianghujiaohuan-shangxia"></i>
                        </div>
                        <div className="customize-menu-drag-guide-modal-content">上下拖动菜单可调整排序</div>
                        <button
                            className="customize-menu-drag-guide-modal-button"
                            data-tag="button"
                            onClick={this.hideGuide}
                        >
                            我知道了
                        </button>
                        <div className="customize-menu-drag-guide-triangle"></div>
                    </div>
                </div>
            ),
            popupWidth: 'auto',
            popupHeight: 'auto',
            style: {
                borderRadius: 0,
                backgroundColor: 'rgba(255, 255, 255, 0)',
                top: 0,
                left: 0,
                bottom: 0,
                right: 0,
                padding: 0,
            },
            popupTitleBarStyle: {
                width: 0,
                height: 0,
                display: 'none',
            },
        };
        emitter.emit('popupWindow', windowInfo);
        assetManager.setPv_new(6297);
    };

    hideGuide = (e: React.MouseEvent<HTMLElement>) => {
        const { tag } = e.currentTarget.dataset;
        assetManager.getUserInfo().then((data) => {
            data.json().then((userInfo) => {
                if (!userInfo.id) {
                    emitter.emit('popupClose');
                    emitter.emit('LoginPanelShow');
                    return;
                } else {
                    this.saveMenuList(this.state.menuList, undefined, undefined, true);
                }
            });
        });
        assetManager.setPv_new(tag === 'button' ? 6298 : 6305);
    };

    setMenuList = (newList: TMenuItem[]) => {
        // 不能使用库的排序结果，仅用来解决报错
    };

    onMenuListDragStart(e: React.DragEvent) {
        e.stopPropagation();
    }

    /**
     * 悬停埋点
     * @param menuName - 菜单名
     * @param isMore - 是否在更多里
     */
    overMenu = (menuName: string, isMore: number, sort: number, menuId: string) => {
        return (e: React.MouseEvent<HTMLDivElement>) => {
            if (this.overFlag) {
                if (sort < 5) {
                    const scrollTop = document.getElementsByClassName('customize-menu')[0].scrollTop;
                    this.setState({
                        tipTop: 66 * sort - scrollTop + 23,
                        sort,
                        isVisible: 'visible',
                    });
                }
                assetManager.setPv_new(6650, {
                    additional: { s0: menuName, s1: isMore },
                });
                if (menuName === '智能抠图' && env.cutImgBuray) {
                    assetManager.setPv_new(6931);
                }
                this.overFlag = false;
                if (menuId && e.buttons === 0) {
                    this.props.onItemEnter?.(cartConfig[menuId].key, menuId, menuName);
                }
            }
        };
    };

    leaveMenu = (e?: React.MouseEvent<HTMLElement>) => {
        this.setState({
            tipTop: 0,
            sort: 0,
            isVisible: 'hidden',
        });
        this.overFlag = true;
    };

    render() {
        const { menuList, activedMenuId, dragMenuId, isShowDragTip, revertActions, tipTop, isVisible, sort } =
            this.state;
        const { isHover, rt_currentNav } = this.props;
        return (
            <div className="customize-menu">
                <div className="customize-menu-list" onDragStart={this.onMenuListDragStart}>
                    <ClickOutside onClickOutside={this.onClickOutSide}>
                        <ReactSortable
                            className={`sort-menu ${dragMenuId ? 'dragging' : ''}`}
                            list={menuList}
                            setList={this.setMenuList}
                            filter=".ignore"
                            animation={150}
                            group="menu"
                            chosenClass="dragging"
                            draggable='.customize-menu-item'
                            onStart={this.onSortStart}
                            onEnd={this.onSortEnd}
                            onMove={this.onSortMove}
                        >
                            {menuList.map((menu, index) => {
                                if (menu.id === '54') menu.online = '1';
                                // menu.online === '0' ||
                                if (
                                    (menu.online === '0' && menu.id !== '49') ||
                                    menu.is_more ||
                                    (!this.props.isTeam && menu.id === '12')
                                ) {
                                    return <React.Fragment key={menu.id}></React.Fragment>;
                                }
                                return (
                                    <React.Fragment key={menu.id}>
                                        <MenuItem
                                            key={menu.id}
                                            menu={menu}
                                            activedMenuId={activedMenuId}
                                            dragMenuId={dragMenuId}
                                            isShowDragTip={isShowDragTip}
                                            onMenuItemClick={(e) => this.onMenuItemClick(e, menu.menu_name, menu.is_more.toString())}
                                            deleteMenu={this.deleteMenu}
                                            overMenu={this.overMenu(menu.menu_name, menu.is_more, menu.sort, menu.id)}
                                            leaveMenu={this.leaveMenu}
                                            isHover={isHover && cartConfig[menu.id].key === rt_currentNav}
                                        />
                                        {env.editor === 'ue' && menu.id === '61' && menuList.length > 0 && (
                                            <>
                                                <div
                                                    className={`customize-menu-item more ignore ${activedMenuId === '-1' ? 'active' : ''} ${isHover && cartConfig['-1'].key === rt_currentNav ? 'hover' : ''}`}
                                                    key="menu-more"
                                                    data-menuid={-1}
                                                    onClick={this.onMenuItemClick}
                                                    onMouseOver={this.onIsMore}
                                                    onMouseLeave={this.leaveMenu}
                                                >
                                                    <i className={`customize-menu-icon ${cartConfig['-1'].icon}`}></i>
                                                    {cartConfig['-1']?.hover && (
                                                        <i
                                                            className={`customize-menu-icon hover ${cartConfig['-1']?.hover}`}
                                                            style={
                                                                cartConfig['-1']?.hoverColor ? { color: cartConfig['-1']?.hoverColor } : undefined
                                                            }
                                                        ></i>
                                                    )}
                                                    <span className="customize-menu-item-text">应用</span>
                                                    
                                                </div>
                                                <div className='divider ignore'></div>
                                                <div className='ignore' key="sortHide"></div>
                                            </>
                                        )}
                                    </React.Fragment>
                                );
                            })}
                        </ReactSortable>
                    </ClickOutside>
                    {env.editor !== 'ue' && menuList.length > 0 && (
                        <div
                            className={`customize-menu-item more ${activedMenuId === '-1' ? 'active' : ''}`}
                            key="menu-more"
                            data-menuid={-1}
                            onClick={this.onMenuItemClick}
                            onMouseOver={this.onIsMore}
                            style={{ marginLeft: '6px', marginRight: '6px' }}
                        >
                            <i className={`customize-menu-icon ${cartConfig['-1'].icon}`}></i>
                            <span className="customize-menu-item-text">{/* env.editor === "ueteam" */ '更多'}</span>
                        </div>
                    )}
                </div>

                <div className="menu-message" style={{ left: (this.windowWidth - 260 - 80) / 2 + 80 }}>
                    {Object.keys(revertActions)
                        .filter((k) => revertActions[k])
                        .sort((a, b) => revertActions[a].time - revertActions[b].time)
                        .map((k) => {
                            if (revertActions[k].originAction === 'sort') {
                                return (
                                    <div
                                        className={`menu-message-success success-sort ${
                                            revertActions[k].enter ? 'enter' : ''
                                        } ${revertActions[k].out ? 'out' : ''} `}
                                        key={k}
                                    >
                                        <i className="iconfont icon-duigou"></i>
                                        <div className="menu-message-text">
                                            {cartConfig[revertActions[k].menuItem.id].name ||
                                                revertActions[k].menuItem.menu_name}
                                            布局已保存
                                        </div>
                                    </div>
                                );
                            } else {
                                return (
                                    <div
                                        className={`menu-message-success ${revertActions[k].enter ? 'enter' : ''} ${
                                            revertActions[k].out ? 'out' : ''
                                        } `}
                                        key={k}
                                    >
                                        <i className="iconfont icon-duigou"></i>
                                        <div className="menu-message-text">
                                            {revertActions[k].originAction === 'add'
                                                ? `${cartConfig[revertActions[k].menuItem.id].name || revertActions[k].menuItem.menu_name}已添加到左侧菜单`
                                                : `${cartConfig[revertActions[k].menuItem.id].name || revertActions[k].menuItem.menu_name}移到${isUeAndUeteam ? '应用' : '更多'}`}
                                        </div>
                                        <span
                                            className="menu-message-revert"
                                            data-menuid={k}
                                            data-originaction={revertActions[k].originAction}
                                            onClick={this.revertMenu}
                                        >
                                            撤销
                                        </span>
                                    </div>
                                );
                            }
                        })}
                </div>
            </div>
        );
    }
}
