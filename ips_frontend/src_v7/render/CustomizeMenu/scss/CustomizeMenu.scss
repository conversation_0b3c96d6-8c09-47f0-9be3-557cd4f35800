@import '@src/sassV6.0/clientV6.0/Base.scss';

// body > .customize-menu-item-container {
//     z-index: 99999;
//     pointer-events: auto !important;
//     cursor: grabbing !important;
//     .customize-menu-item {
//         pointer-events: none;
//     }
// }

// .customize-menu-item-container {
// }

.customize-menu {
    box-sizing: border-box;
    padding-bottom: 40px;
    width: 72px;
    margin: 0 auto;
    height: 100%;
    overflow-y: overlay;
    overflow-x: hidden;
    height: calc(100% - 72px - var(--AssetPanelPadding));
    &::-webkit-scrollbar {
        display: none;
        width: 6px;
    }
    &:hover {
        &::-webkit-scrollbar {
            display: block;
        }
    }
    &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: rgba(31, 26, 27, 0.2);

        &:hover {
            background: rgba(31, 26, 27, 0.40);
        }
    }

    .sort-menu {
        // padding: 6px;

        &.dragging {
            .customize-menu-item:not(.dragging):hover {
                color: #333333;

                .new1 {
                    background-image: url($imgHost + '/index_img/editorV7.0/navIcon/3Dicon.svg');
                }
            }
        }
    }

    .divider {
        width: 56px;
        height: 1px;
        background: #e9e8e8;
        margin: 8px;
    }
}

.customize-menu-item {
    box-sizing: border-box;
    width: 72px;
    height: 72px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    cursor: pointer;
    color: #1f1a1b;
    // background: #ffffff;
    position: relative;
    border-radius: 5px 5px 5px 5px;
    scroll-margin-bottom: 40px;

    .menu-img-box {
        box-sizing: border-box;
        height: 34px;
        width: 34px;
        border-radius: 8px;
        overflow: hidden;
        padding: 5px;
        margin-bottom: 2px;

        img {
            border-radius: 4px;
            width: 24px;
            display: block;
        }
    }

    .new {
        position: absolute;
        width: 8px;
        height: 8px;
        background: #ef3964;
        border-radius: 50%;
        top: 16px;
        right: 16px;
    }

    &.more {
        position: relative;
    }

    &.active {
        .customize-menu-icon,
        .menu-img-box {
            color: #ef3964;
            border-radius: 8px;
            background: #fff;
            box-shadow: 0px 2px 8px 2px rgba(0, 0, 0, 0.15);
        }
        .customize-menu-icon {
            display: none;
            &.hover {
                display: block;
            }
        }

        .customize-menu-item-text {
            color: #ef3964;
            font-weight: 600;
        }
        .thirdAsset {
            // color: #1f1a1b;
        }
        svg g {
            fill: #ef3964;
            stroke: #ef3964;
        }
        svg path {
            fill: #ef3964;
            stroke: #ef3964;
        }

        .new1 {
            background-image: url($imgHost + '/index_img/editorV7.0/navIcon/3DiconHover.svg');
        }
        &:hover {
            .icon-bianjiqixin-zuocedaohang-shanchuyingyong {
                display: block;
            }
        }
    }

    &.hover,
    &:hover {
        position: relative;
        z-index: 10;

        .customize-menu-item-drag-tip {
            display: block;
        }
        .customize-menu-icon,
        .menu-img-box {
            color: #ef3964;
            border-radius: 8px;
            background: #fff;
            box-shadow: 0px 2px 8px 2px rgba(0, 0, 0, 0.15);
        }
        .customize-menu-icon {
            display: none;
            &.hover {
                display: block;
            }
        }
    }

    &.dragging {
        // box-shadow: 2px 2px 10px 0px rgba(0, 0, 0, 0.15);
        border-radius: 2px;
        z-index: 20;

        &.active .icon-bianjiqixin-zuocedaohang-shanchuyingyong {
            visibility: hidden;

            &:hover {
                visibility: visible;
            }
        }

        // &.customize-menu-item-drag-tip {
        //     display: block;
        // }
    }

    &.sortable-ghost {
        visibility: hidden;
    }

    .customize-menu-icon {
        box-sizing: border-box;
        font-size: 24px;
        width: 34px;
        height: 34px;
        padding: 5px;
        color: #1f1a1b;
        margin-bottom: 4px;

        &.hover {
            display: none;
        }
    }
    .aiCustomImg {
        border: 1px solid #e9e8e8;
        border-radius: 5px;
        box-sizing: border-box;
    }

    .new1 {
        background-image: url($imgHost + '/index_img/editorV7.0/navIcon/3Dicon.svg');
        background-size: 24px 24px;
        &.active {
            background-image: url($imgHost + '/index_img/editorV7.0/navIcon/3DiconHover.svg');
        }
    }

    .application-num {
        position: absolute;
        right: 18px;
        top: 10px;
        width: 16px;
        height: 16px;
        background: #ff0f0f;
        opacity: 1;
        border-radius: 24px;

        font-size: 12px;
        line-height: 14px;
        font-style: normal;
        font-family:
            PingFang SC-Regular,
            PingFang SC;
        font-weight: 400;
        color: #ffffff;
    }

    .application-icon {
        background-image: url($imgHost + '/index_img/editorV7.0/navIcon/application02.png');
        width: 38px;
        height: 38px;
        background-size: 38px 38px;
        margin-bottom: 6px;
    }

    .customize-menu-item-text {
        color: #1f1a1b;
        font-size: 12px;
        font-weight: 400;
        line-height: 20px;
    }

    .icon-bianjiqixin-zuocedaohang-shanchuyingyong {
        display: none;
        position: absolute;
        top: 5px;
        left: 5px;
        box-sizing: border-box;
        padding-top: 2px;
        width: 14px;
        height: 14px;
        cursor: pointer;
        font-size: 12px;
        line-height: 12px;
        text-align: center;
        border-radius: 50%;
        color: #4c4849;
        background: #f7f7f7;

        &:hover {
            background: #e9e8e8;
        }
    }

    .customize-menu-item-drag-tip {
        display: none;
        position: absolute;
        top: -20px;
        transform: scale(0.833333333);

        .customize-menu-item-drag-tip-text {
            width: 96px;
            height: 31px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 5px;
            font-size: 12px;
            font-family:
                PingFangSC-Regular,
                PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 28px;
        }

        .customize-menu-item-drag-tip-triangle {
            width: 0;
            height: 0;
            margin: 0 auto -5px;
            border-top: 5px solid rgba(0, 0, 0, 0.8);
            border-bottom: 5px solid transparent;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;

            &.first {
                display: none;
                margin: -5px auto 0;
                border-top: 5px solid transparent;
                border-bottom: 5px solid rgba(0, 0, 0, 0.8);
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
            }
        }
    }

    &:nth-of-type(1) {
        .customize-menu-item-drag-tip {
            top: unset;
            bottom: -20px;

            .customize-menu-item-drag-tip-triangle {
                display: none;

                &.first {
                    display: block;
                }
            }
        }
    }
}

.leftNavHitPopup {
    position: absolute;
    display: flex;
    align-items: center;
    left: 49px;
    z-index: 999;
    pointer-events: none;
    .left_arrow {
        width: 0px;
        height: 0px;
        border-width: 7px 11px;
        border-style: solid;
        border-color: transparent;
        border-right-color: #404040;
    }
    .hit_content {
        width: 83px;
        height: 32px;
        background: #404040;
        border-radius: 4px 4px 4px 4px;
        opacity: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-left: 8px;
        padding-right: 8px;
        p {
            text-align: center;
            line-height: 32px;
            font-size: 12px;
            font-family:
                PingFang SC-Regular,
                PingFang SC;
            font-weight: 400;
            color: #ffffff;
        }
        .shortcutNum {
            text-align: center;
            line-height: 18px;
            width: 18px;
            height: 18px;
            background: #666566;
            border-radius: 2px 2px 2px 2px;
            // opacity: 0.2;
            font-size: 14px;
            font-family:
                PingFang SC-Regular,
                PingFang SC;
            font-weight: 400;
            color: #ffffff;
        }
    }
}

.menu-message {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: #000000d9;
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: 'tnum';
    position: fixed;
    top: 32px;
    left: -1000px;
    z-index: 100000;
    // pointer-events: none;
    width: 400px;
    transform: translateX(-50%);
    text-align: left;

    .menu-message-success {
        box-sizing: border-box;
        width: 400px;
        background: #f6ffed;
        border-radius: 4px;
        border: 1px solid #b7eb8f;
        padding: 7px 6px 7px 18px;
        margin-bottom: 40px;
        font-size: 0;

        &.success-sort {
            width: 200px;
            text-align: center;
            margin: 0 auto 40px;

            .menu-message-text {
                width: auto;
            }
        }

        &.enter {
            animation: messageEnter 0.3s ease-in-out;
        }

        &.out {
            animation: messageOut 0.3s ease-in-out;
        }

        .icon-duigou {
            display: inline-block;
            width: 18px;
            height: 18px;
            background: #52c41a;
            border-radius: 50%;
            font-size: 12px;
            line-height: 18px;
            text-align: center;
            color: #ffffff;
            margin-right: 9px;
            vertical-align: middle;
        }

        .menu-message-revert {
            display: inline-block;
            width: 52px;
            height: 24px;
            border-radius: 14px;
            font-size: 12px;
            font-family:
                PingFangSC-Regular,
                PingFang SC;
            font-weight: 400;
            color: #1890ff;
            line-height: 24px;
            text-align: center;
            vertical-align: middle;
            cursor: pointer;
        }
    }

    .menu-message-text {
        display: inline-block;
        width: 286px;
        font-size: 14px;
        font-family:
            PingFangSC-Regular,
            PingFang SC;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
        text-align: left;
        margin-right: 9px;
        vertical-align: middle;
    }
}

@keyframes messageEnter {
    0% {
        transform: translateY(-100px);
        opacity: 0;
        display: none;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
        display: block;
    }
}

@keyframes messageOut {
    0% {
        transform: translateY(0);
        opacity: 1;
        display: block;
    }
    100% {
        transform: translateY(-100px);
        opacity: 0;
        display: none;
    }
}

.customize-menu-drag-guide {
    margin-left: -3px;
    padding-top: 48px;
    position: relative;

    .customize-menu-drag-guide-img {
        cursor: pointer;
        width: 90px;
        height: 800px;
    }

    .customize-menu-drag-guide-modal {
        box-sizing: border-box;
        position: absolute;
        left: 100px;
        top: 290px;
        width: 224px;
        height: 170px;
        background: linear-gradient(336deg, #ff7489 0%, #ff405e 100%);
        border-radius: 10px;
        padding: 24px 0 0 20px;
        animation: customizeMenuDragGuideModal ease-in-out 2s infinite;

        .customize-menu-drag-guide-modal-title {
            font-size: 20px;
            font-family:
                PingFangSC-Medium,
                PingFang SC;
            font-weight: 500;
            color: #ffffff;
            line-height: 28px;
            margin-bottom: 10px;

            span {
                vertical-align: middle;
            }

            .icon-xianghujiaohuan-shangxia {
                font-size: 28px;
                vertical-align: middle;
            }
        }

        .customize-menu-drag-guide-modal-content {
            width: 182px;
            height: 20px;
            font-size: 14px;
            font-family:
                PingFangSC-Regular,
                PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
            margin-bottom: 36px;
        }

        .customize-menu-drag-guide-modal-button {
            box-sizing: border-box;
            width: 76px;
            height: 30px;
            border-radius: 4px;
            border: 1px solid #ffffff;
            font-size: 14px;
            font-family:
                PingFangSC-Regular,
                PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 28px;
            outline: none;
            background: none;
            margin-left: 106px;
            cursor: pointer;
        }
        .customize-menu-drag-guide-triangle {
            position: absolute;
            top: 37px;
            left: -20px;
            width: 0;
            height: 0;
            border-top: 10px solid transparent;
            border-bottom: 10px solid transparent;
            border-left: 10px solid transparent;
            border-right: 10px solid #fe405d;
        }
    }
}

@keyframes customizeMenuDragGuideModal {
    0% {
        transform: translateY(-10px);
    }

    50% {
        transform: translateY(10px);
    }
    100% {
        transform: translateY(-10px);
    }
}
