import { ICanvas, IPage, IPageAttr } from '@v7_logic/Interface';
import React, { PureComponent } from 'react';
// import { PageThumbnailLib } from './lib';
import equal from 'fast-deep-equal';
import { TgsCanvasThumbnail } from '@tgs/canvas';

interface IPageThumbnailProps {
    canvas: ICanvas;
    page: IPage;
    pageAttr: IPageAttr;
    pageIndex: number;
    preview?: string;
    currentEditIndex: number;
}

interface IPageThumbnailState {
    hasChange: boolean;
}

export class PageThumbnail extends PureComponent<IPageThumbnailProps, IPageThumbnailState> {
    canvasRef = React.createRef<HTMLCanvasElement>();
    tgsCanvas: TgsCanvasThumbnail;
    ctx: CanvasRenderingContext2D | null = null;

    state: IPageThumbnailState = {
        hasChange: false,
    };

    componentDidMount() {
        const { canvas, preview } = this.props;
        const width = Math.ceil(canvas.width * canvas.scale);
        const height = Math.ceil(canvas.height * canvas.scale);
        this.tgsCanvas = new TgsCanvasThumbnail({
            element: this.canvasRef.current,
            options: {
                width,
                height,
                backgroundColor: '#F0F1F5',
            },
        });
        // this.ctx = this.canvasRef.current?.getContext('2d');
        if (!this.props.preview) {
            this.update(true);
        }
    }

    componentDidUpdate(prevProps: Readonly<IPageThumbnailProps>): void {
        const isCanvasSizeChange =
            prevProps.canvas.width !== this.props.canvas.width || prevProps.canvas.height !== this.props.canvas.height;
        if (
            isCanvasSizeChange ||
            !equal(
                this.getCurrentPageAttr(prevProps.pageAttr, prevProps.pageIndex),
                this.getCurrentPageAttr(this.props.pageAttr, this.props.pageIndex),
            ) ||
            this.checkPageAssetsChange(prevProps.page, this.props.page)
        ) {
            this.update(isCanvasSizeChange);
        }
        if (this.props.currentEditIndex === this.props.pageIndex && prevProps.currentEditIndex !== this.props.pageIndex) {
            const item = this.canvasRef.current?.closest('.page-item-container');
            const scrollDom = item?.closest('.contentPageList');
            const itemSize = item?.getBoundingClientRect();
            const scrollSize = scrollDom?.getBoundingClientRect();
            if (itemSize && scrollSize) {
                if (itemSize.left < scrollSize.left || itemSize.right > scrollSize.right) {
                    item.scrollIntoView(false);
                }
            }
        }
    }

    getCurrentPageAttr(pageAttr: IPageAttr, pageIndex: number) {
        return {
            backgroundColor: pageAttr.backgroundColor?.[pageIndex],
            backgroundImage: pageAttr.backgroundImage?.[pageIndex],
            backgroundOpacity: pageAttr.backgroundOpacity?.[pageIndex],
        };
    }

    checkPageAssetsChange = (prevPage: IPage, currentPage: IPage) => {
        if (prevPage.assets === currentPage.assets) {
            return false;
        }
        if (prevPage.assets.length !== currentPage.assets.length) {
            return true;
        }
        for (let i = 0; i < prevPage.assets.length; i++) {
            // 缩略图不关心 asset.meta 的变化, 除了 index 顺序
            if (
                prevPage.assets[i].attribute !== currentPage.assets[i].attribute ||
                prevPage.assets[i].transform !== currentPage.assets[i].transform ||
                prevPage.assets[i].meta.index !== currentPage.assets[i].meta.index
            ) {
                return (
                    !equal(prevPage.assets[i].attribute, currentPage.assets[i].attribute) ||
                    !equal(prevPage.assets[i].transform, currentPage.assets[i].transform)
                );
            }
        }
        return false;
    };

    update(sizeChange?: boolean) {
        const { canvas, page, pageAttr, pageIndex } = this.props;
        // const canvasDom = this.canvasRef.current;
        // if (!this.ctx || !canvasDom) return;
        // if (sizeChange || !this.state.hasChange) {
        //     canvasDom.width = Math.ceil(canvas.width * canvas.scale * window.devicePixelRatio);
        //     canvasDom.height = Math.ceil(canvas.height * canvas.scale * window.devicePixelRatio);
        // }
        // if (!this.state.hasChange) {
        //     this.setState({ hasChange: true });
        // }
        // PageThumbnailLib.renderPage({
        //     canvas,
        //     page: page as any,
        //     pageAttr,
        //     pageIndex,
        //     callback: (fabricCanvas: HTMLCanvasElement) => {
        //         this.ctx.clearRect(0, 0, canvasDom.width, canvasDom.height);
        //         this.ctx.drawImage(fabricCanvas, 0, 0);
        //     },
        // });
        this.tgsCanvas.renderPage({
            canvas,
            page: page as any,
            pageAttr,
            pageIndex,
            callback: (fabricCanvas: HTMLCanvasElement) => {
                this.setState({ hasChange: true });
            },
        });
    }

    render() {
        const { canvas, preview } = this.props;
        const { hasChange } = this.state;
        const width = Math.ceil(canvas.width * canvas.scale);
        const height = Math.ceil(canvas.height * canvas.scale);
        return (
            <>
                {!hasChange && preview && <img style={{ width}} src={preview} alt="" />}
                <canvas
                    key="canvas"
                    // style={{
                    //     width,
                    //     height,
                    //     background: 'white',
                    //     opacity: hasChange || !preview ? 1 : 0,
                    // }}
                    ref={this.canvasRef}
                ></canvas>
            </>
        );
    }
}
