import { assetManager } from '@src/userComponentV6.0/AssetManager';
import { CheckSame, TgsCanvas } from '@tgs/canvas';
import { IImageAsset, TgsTypes, IConvertPageToImageOptions } from '@tgs/types';
import { AssetHelper } from '@v7_logic/AssetHelper';
import { AssetLogic, SelectAsset, UpdateAsset, DeleteAsset } from '@v7_logic/AssetLogic';
import { ICanvas, IPage, IPageAttr, IPageInfo, IRichText, IToolPanel, IUserInfo } from '@v7_logic/Interface';
import { TemplateCanvasLogic } from '@v7_logic/TemplateCanvasLogic';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { Asset } from '@v7_render/Asset';
import {
    AssetActionBar,
    ImageClip,
    PageTools,
    AssetTooltip,
    BackgroundClip,
    CanvasScrollBar,
    AssetActionFreeBar
} from '@v7_renderCore/Controls';
import React, { PureComponent } from 'react';
import './scss/RenderCanvas.scss';
import { emitter } from '@src/userComponentV6.0/Emitter';
import { ClickOutside } from '@v7_render/Ui';
import { EventSubscription } from 'fbemitter';
import { CanvasPaintedLogic } from '@v7_logic/CanvasPaintedLogic';
import { FrameCanvasLogic, FrameInfo } from '@v7_render/Asset/Frame/FrameCanvasLib';
import { ImageCutout } from '@v7_renderCore/Controls/ImageCutout';
import { isCutout } from '@component/canvas/ToolModuleAssetCutPanel';
import { RecoverToolBox } from '@v7_render/AssetImage/ImageRecover/RecoverToolBox';
import { DeletedRecoveryLogic } from '@v7_logic/DeletedRecoveryLogic';
import { ImageCanvasRenderInfo } from '@v7_render/AssetToolPanel/Image/type/colorAdjust';
import { ImageFilterToolLogic } from '@v7_render/AssetToolPanel/Image/ImageToolLogic/ImageFilterToolLogic';
import { ImageToolLogic } from '@v7_render/AssetToolPanel/Image/ImageToolLogic/ImageToolLogic';
import { GroupAndMultipleSelectLogic } from '@v7_logic/GroupAndMultipleSelectLogic';
import { ErrorBoundaryDecorator } from '@v7_render/ErrorBoundaryHOC';
import { CanvasLogic } from '@v7_logic/CanvasLogic';
import { isEqual } from 'lodash-es';
import { RichTextLogic } from '@v7_logic/RichTextLogic/RichTextLogic';
import { AssetTextLogic } from '@v7_logic/AssetTextLogic';
import { HotKeyLogic } from '@v7_logic/HotKeyLogic';
import { CanvasToImage } from '@v7_logic/CanvasToImage';

interface IRenderCanvasProps {
    renderCoreDom: HTMLDivElement;
    canvas: ICanvas;
    pages: IPage[];
    pageInfo: IPageInfo;
    pageAttr: IPageAttr;
    pageIndex: number;
    toolPanel: IToolPanel;
    rt_is_online_detail_page: boolean;
    controls?: Record<string, React.ReactNode>;
    children?: React.ReactNode;
    rt_canvas_render_mode?: '' | 'pull' | 'board';
    baseGap: number;
    toolPanelWidth?: number;
    resourcePanelWidth?: number;
    user: IUserInfo;
    showWaterMark: boolean;
}

interface IRenderCanvasState {
    editingAsset: string | undefined;
    editingAssetType: string | undefined;
    rotating3DAsset: string | undefined;
    videoEAsset: string | undefined;
    rightClickMenu: {
        isShow: boolean;
        left: number;
        top: number;
        className?: string;
        type?: string;
        x?: number;
        y?: number;
        scale?: number;
    };
    clippingImage: string;
    canvasFrameEventObj?: fabric.Object;
    dragPicInfo?: {
        isDraging: boolean;
        element: Asset;
    };
    showPreview: boolean;
    selectAssetsPageIndex: number;
    isShowShortcutMenu: boolean;// 是否有物体移动
    isSelectPage: boolean;
    canvasAddPage: boolean;
}
@ErrorBoundaryDecorator()
export class RenderCanvas extends PureComponent<IRenderCanvasProps, IRenderCanvasState> {
    renderCanvasRef = React.createRef<HTMLDivElement>();
    resizeTimer: number | undefined;

    canvasRef = React.createRef<HTMLCanvasElement>();
    tgsCanvas: TgsCanvas;
    canvasWrapperDom: HTMLElement;
    canvasDomSize = {
        width: 0,
        height: 0,
    };
    editRef = React.createRef<any>();
    TextRectSwitchListener: EventSubscription;
    selectAllListener: EventSubscription;
    assetEditStatusListener: EventSubscription;
    canvasWrapperDomListener: EventSubscription;
    addTextEditingEventListener: EventSubscription;
    convertCanvasToImageListener: EventSubscription;
    setCanvasAddPageListener: EventSubscription
    state: IRenderCanvasState = {
        editingAsset: undefined,
        editingAssetType: undefined,
        rotating3DAsset: undefined,
        videoEAsset: undefined,
        rightClickMenu: {
            isShow: false,
            left: 0,
            top: 0,
            x: 0,
            y: 0,
            scale: 1,
        },
        clippingImage: undefined,
        canvasFrameEventObj: undefined,
        dragPicInfo: {
            isDraging: false,
            element: null,
        },
        showPreview: true,
        selectAssetsPageIndex: undefined,
        isShowShortcutMenu: false,
        isSelectPage: false,
        canvasAddPage: false,
    };

    override componentDidMount(): void {
        const { canvas, pages, pageAttr, pageIndex, rt_canvas_render_mode } = this.props;
        if (canvas && pages && pageAttr) {
            const dom = document.querySelector<HTMLElement>('.canvasWrapperCanvasAll');
            let width = 800;
            let height = 450;
            if (dom) {
                this.canvasWrapperDom = dom;
                width = dom.clientWidth;
                height = dom.clientHeight;
                this.canvasDomSize = {
                    width,
                    height,
                };
            }
            this.tgsCanvas = new TgsCanvas({
                element: this.canvasRef.current,
                options: {
                    width,
                    height,
                    backgroundColor: '#F0F1F5',
                },
            });
            HotKeyLogic.setTgsCanvas(this.tgsCanvas)
            this.tgsCanvas.init({
                canvas,
                pages: pages as TgsTypes.IPage[],
                pageAttr,
                pageIndex,
                currentPage: pageIndex,
                mode: rt_canvas_render_mode,
                showWaterMark: this.props.showWaterMark,
            });
            this.initActions();
            FrameCanvasLogic.listenAddFrameImage();
        }
        this.selectAllListener = emitter.addListener('selectCanvasPageAllAssets', () => {
            this.tgsCanvas?.selectPageAllAssets();
        });
        this.assetEditStatusListener = emitter.addListener('changeAssetEditStatus', () => {
            this.setState({ editingAsset: undefined, editingAssetType: undefined });
        });
        this.canvasWrapperDomListener = emitter.addListener('canvasWrapperDomListener', () => {
            this.updateCanvasDomSizeWidthoutAnim();
        });
        this.convertCanvasToImageListener = emitter.addListener('convertCanvasPageToImage', (options: IConvertPageToImageOptions) => {
            this.convertCanvasPageToImage(options);
        });
        /* 添加文字进入编辑状态 */
        this.addTextEditingEventListener = emitter.addListener(
            'addTextEditingEventListener',
            (asset: TgsTypes.IAsset<TgsTypes.TAssetType>) => {
                this.tgsCanvas?.actions.onAssetDoubleClick({}, asset);
            },
        );
        // 监听是否显示/隐藏文本框
        this.TextRectSwitchListener = emitter.addListener(
            'TextRectSwitchListener',
            (isShow: boolean) => {
                this.tgsCanvas?.switchAiDesignerTextRect(isShow);
            },
        );
        this.setCanvasAddPageListener = emitter.addListener('setCanvasAddPage', (isShowMorePage: boolean) => {
            this.setState({ canvasAddPage: !isShowMorePage });
        });
    }

    override componentWillUnmount(): void {
        this.selectAllListener?.remove?.();
        this.assetEditStatusListener?.remove?.();
        this.tgsCanvas.destroy();
        this.addTextEditingEventListener?.remove?.();
        this.convertCanvasToImageListener?.remove?.();
        this.TextRectSwitchListener?.remove?.();
        this.setCanvasAddPageListener?.remove();
        FrameCanvasLogic.removeFrameImageListener();
    }

    override componentDidUpdate(prevProps: Readonly<IRenderCanvasProps>, prevState: any) {
        this.updateCanvasDomSizeWidthoutAnim();
        this.renderCanvas(this.props, prevProps);
        const { toolPanel, user } = this.props;
        if (
            typeof toolPanel.asset_index === 'number' &&
            toolPanel.asset_index < 0 &&
            Array.isArray(toolPanel.assets_index) &&
            toolPanel.assets_index.length === 0
        ) {
            this.tgsCanvas.blurSelect();
        }
        if (this.state.editingAsset || this.state.videoEAsset) {
            const { pages, pageInfo, toolPanel, pageIndex } = this.props;
            const asset = AssetHelper.find({ pages: pages }, pageInfo, {
                index: toolPanel.asset_index,
                pageIndex: pageIndex,
            });
            if (
                asset &&
                asset.meta.className !== this.state.editingAsset &&
                asset.meta.className !== this.state.videoEAsset &&
                asset.meta.type != 'group'
            ) {
                this.setState({
                    editingAsset: undefined,
                    editingAssetType: undefined,
                    videoEAsset: undefined,
                });
            }
        }
        // 更新文本格式刷状态
        const isTextStyleBrushStatusChange =
            this.props.toolPanel.textStyleBrushStatus !== prevProps.toolPanel.textStyleBrushStatus;
        isTextStyleBrushStatusChange &&
            this.tgsCanvas.updateTextStyleBrushStatus(this.props.toolPanel.textStyleBrushStatus);
    }
    convertCanvasPageToImage(options: IConvertPageToImageOptions) {
        CanvasToImage.exportCanvasPoster(this.tgsCanvas, options);
    }
    updateCanvasDomSize() {
        if (!this.canvasWrapperDom) {
            const { canvas_wrapper_dom_info } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            if (canvas_wrapper_dom_info.is_exist) {
                this.canvasWrapperDom = document.querySelector('.' + canvas_wrapper_dom_info.class_name);
            }
        }
        if (this.canvasWrapperDom) {
            this.renderCanvasRef?.current?.classList.add('animating');
            const { diffwidth = '0' } = this.canvasWrapperDom.dataset;
            const { width, height } = this.canvasWrapperDom.getBoundingClientRect();
            const withAnimate = Math.abs(Number(diffwidth)) > 20;
            if (!withAnimate) {
                this.renderCanvasRef?.current?.classList.remove('animating');
            }
            if (this.resizeTimer) {
                clearTimeout(this.resizeTimer);
            }
            this.resizeTimer = window.setTimeout(() => {
                this.renderCanvasRef?.current?.classList.remove('animating');
                clearTimeout(this.resizeTimer);
            }, 300);
            if (withAnimate || this.canvasDomSize.width !== width || this.canvasDomSize.height !== height) {
                this.tgsCanvas.resize(width - Number(diffwidth), height, withAnimate, this.props.canvas);
                this.canvasDomSize = {
                    width: width - Number(diffwidth),
                    height,
                };
            }
        }
    }
    /* 无动画版本 */
    updateCanvasDomSizeWidthoutAnim() {
        if (!this.canvasWrapperDom) {
            const { canvas_wrapper_dom_info } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            if (canvas_wrapper_dom_info.is_exist) {
                this.canvasWrapperDom = document.querySelector('.' + canvas_wrapper_dom_info.class_name);
            }
        }
        if (this.canvasWrapperDom) {
            const { width, height } = this.canvasWrapperDom.getBoundingClientRect();
            if (this.canvasDomSize.width !== width || this.canvasDomSize.height !== height) {
                this.tgsCanvas.resize(width, height, false, this.props.canvas);
            }
            this.canvasDomSize = {
                width,
                height,
            };
        }
    }
    renderCanvas(props: Readonly<IRenderCanvasProps>, prevProps: Readonly<IRenderCanvasProps>) {
        const { pageIndex } = props;
        const updates: Record<string, any> = {};

        if (!CheckSame.canvas(props.canvas, prevProps.canvas)) {
            updates['updateCanvas'] = [props.canvas];
        }
        if (
            !CheckSame.backgroundColor(
                props.pageAttr?.backgroundColor?.[pageIndex],
                prevProps.pageAttr?.backgroundColor?.[pageIndex],
            ) ||
            !CheckSame.backgroundOpacity(
                props.pageAttr?.backgroundOpacity?.[pageIndex],
                prevProps.pageAttr?.backgroundOpacity?.[pageIndex],
            ) ||
            !CheckSame.backgroundImage(
                props.pageAttr?.backgroundImage?.[pageIndex],
                prevProps.pageAttr?.backgroundImage?.[pageIndex],
            )
        ) {
            updates['updateBackground'] = [
                {
                    backgroundColor: props.pageAttr?.backgroundColor,
                    backgroundOpacity: props.pageAttr.backgroundOpacity,
                    backgroundImage: props.pageAttr.backgroundImage,
                },
                pageIndex,
                props.pages,
            ];
        }

        if (
            props.pages !== prevProps.pages ||
            pageIndex !== prevProps.pageIndex ||
            props.rt_canvas_render_mode !== prevProps.rt_canvas_render_mode
        ) {
            // props.pageAttr.pageHash =
            updates['updatePages'] = [
                {
                    pages: props.pages,
                    pageIndex,
                    canvas: props.canvas,
                    pageAttr: props.pageAttr,
                    mode: props.rt_canvas_render_mode,
                },
            ];
            if (pageIndex !== prevProps.pageIndex) {
                updates['updateBackground'] = [
                    {
                        backgroundColor: props.pageAttr?.backgroundColor,
                        backgroundOpacity: props.pageAttr.backgroundOpacity,
                        backgroundImage: props.pageAttr.backgroundImage,
                    },
                    pageIndex,
                    props.pages,
                ];
            }
        }

        if (
            ((props.toolPanel.asset_index !== prevProps.toolPanel.asset_index || !prevProps.toolPanel.asset) &&
                props.toolPanel.asset_index >= 0 &&
                props.toolPanel.asset?.meta?.className !== prevProps.toolPanel.asset?.meta?.className) ||
            (props.toolPanel.assets_index?.length &&
                !isEqual(props.toolPanel.assets_index, prevProps.toolPanel.assets_index))
        ) {
            const indexArr = props.toolPanel.assets_index?.length
                ? props.toolPanel.assets_index
                : [props.toolPanel.asset_index];
            updates['syncSelectAsset'] = [
                {
                    index: indexArr,
                    pageIndex,
                },
            ];
        }
        if (props.showWaterMark != prevProps.showWaterMark) {
            updates['updateWaterMark'] = [props.showWaterMark];
        }
        for (const key in updates) {
            this.tgsCanvas?.[key]?.(...updates[key]);
        }
        // console.log('key of updates: ', Object.keys(updates));
    }

    displayRightClickMenu(param: { left: number; top: number; x: number; y: number; }) {
        this.setState({
            rightClickMenu: {
                isShow: true,
                ...param
            },
        });
    }

    override render() {
        const { canvas, pages, pageInfo, toolPanel, pageIndex, pageAttr, rt_canvas_render_mode, baseGap, user, toolPanelWidth, resourcePanelWidth } =
            this.props;
        const { editingAsset, rotating3DAsset, videoEAsset, clippingImage, selectAssetsPageIndex, isShowShortcutMenu, isSelectPage, rightClickMenu } = this.state;
        let asset;
        let assetIsCutout = false;

        if (toolPanel.asset_index >= 0) {
            // if (editingAsset || rotating3DAsset || videoEAsset || clippingImage) {
            asset = AssetHelper.find({ pages: pages }, pageInfo, {
                index: toolPanel.asset_index,
                className: editingAsset,
                pageIndex: selectAssetsPageIndex ?? pageIndex,
            });
            // }
            const { isCutouting, isCutouted } = isCutout(asset?.attribute);
            assetIsCutout = isCutouting || isCutouted;
        } else {
            const backgroundAsset = pages[pageInfo.pageNow].assets.find((asset) => asset.meta.type === 'background');
            const { isCutouting, isCutouted } = isCutout(backgroundAsset?.attribute);
            assetIsCutout = isCutouting || isCutouted;
            if (backgroundAsset && assetIsCutout) {
                asset = backgroundAsset;
            }
        }

        let pageTop = (canvas.height * canvas.scale + baseGap) * pageIndex; // 当前页顶部距离
        if (rt_canvas_render_mode === 'pull') {
            pageTop = 0;
        }
        const pagesTop: number[] = []; // 所有页顶部距离
        for (let i = 0; i < pages.length; i++) {
            pagesTop.push((canvas.height * canvas.scale + baseGap) * i);
        }
        const posY = rt_canvas_render_mode
            ? canvas.y
            : (canvas.y ?? 0) + (canvas.height * canvas.scale + baseGap) * pageIndex; // canvas.y ?? 0 因为 canvas.y 可能为 undefined
        const positionStyle: React.CSSProperties = {
            left: canvas.x,
            top: posY,
        };

        const controlsStyle: React.CSSProperties = {
            left: canvas.x,
            top: canvas.y,
            width: canvas.width * canvas.scale,
            height: canvas.height * canvas.scale,
        };

        const showEditingAsset =
            (editingAsset || videoEAsset) &&
            asset &&
            (asset.meta.type === 'text' || asset.meta.type === 'videoE' || asset.meta.type === 'table');

        let previewImage;
        // if (this.state.showPreview) {
        if (!window.loadedFirstPageAllImage) {
            const { preview } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                store_name: storeAdapter.store_names.paintOnCanvas,
            });
            previewImage = preview?.[0];
        }
        const storageFixedToolPanel = localStorage.getItem('fixedToolPanel');
        const fixedToolPanel = storageFixedToolPanel ? storageFixedToolPanel === 'true' : true;
        let leftPanelWidth = 0, rightPanelWidth = 0;
        
        if (resourcePanelWidth > 0 ) {
            leftPanelWidth = resourcePanelWidth - 72
        }
        if (fixedToolPanel) {
            rightPanelWidth = 271
        }

        return (
            <div
                className="render-canvas"
                ref={this.renderCanvasRef}
                onWheel={(e) => {
                    if (!(e.ctrlKey || e.metaKey)) {
                        e.deltaY = -e.deltaY;
                    }
                }}
                onDoubleClick={(e) => {
                    e.preventDefault();
                    assetManager.setPv_new(8354);
                }}
            >
                <ClickOutside onClickOutside={this.onClickoutside}>
                    <div>
                        <canvas ref={this.canvasRef} data-left={leftPanelWidth} data-right={rightPanelWidth}></canvas>
                    </div>
                    <div
                        className="editing-assets"
                        style={positionStyle}
                        onKeyDown={this.stopPropagation}
                        onKeyUp={this.stopPropagation}
                        onMouseDownCapture={this.onEditingAssetsMouseDown}
                        onMouseUp={this.onEditingAssetsMouseUp}
                    >
                        {showEditingAsset && (
                            <>
                                <Asset
                                    key={asset.meta.className + '_' + this.props.pageIndex}
                                    asset={asset}
                                    index={toolPanel.asset_index}
                                    assetEditFlag={true}
                                    isAssetActive={true}
                                    isHiddenFlag={false}
                                    // smallFontKeys={smallFontKeys}
                                    parent="canvas"
                                    pageInfo={pageInfo}
                                    canvas={canvas}
                                    canvasWaterMaskFlag={false}
                                    pageAttr={pageAttr}
                                    // rt_animatieTime={rt_animatieTime}
                                    // rt_previewFrame={rt_previewFrame}
                                    editRef={this.editRef}
                                    autoPlay={true}
                                    hideDragTable={true}
                                ></Asset>
                            </>
                        )}
                    </div>
                    <ClickOutside onClickOutside={this.hideRightClickMenu}>
                        <AssetActionBar rightClickMenu={this.state.rightClickMenu} hide={this.hideRightClickMenu} />
                        <AssetActionFreeBar
                            user={user}
                            canvasDom={this.canvasRef}
                            toolPanel={toolPanel}
                            isShow={isShowShortcutMenu}
                            toolPanelWidth={toolPanelWidth}
                            isSelectPage={isSelectPage}
                            canvas={canvas}
                            asset={asset}
                            pagesTop={pagesTop}
                            pageIndex={selectAssetsPageIndex || pageIndex}
                            isShowMoreMenu={rightClickMenu.isShow}
                            handleMoreEvent={this.displayRightClickMenu.bind(this)}
                            hide={this.hideRightClickMenu}
                            renderMode={rt_canvas_render_mode}
                        />
                    </ClickOutside>

                </ClickOutside>
                <div className="rotate-3d-text" style={positionStyle}></div>
                {previewImage && (
                    <div className="preview-image" style={controlsStyle}>
                        <img src={previewImage} style={controlsStyle} />
                    </div>
                )}

                <div className="controls" style={controlsStyle}>

                    <AssetTooltip canvas={canvas} asset={asset} pageIndex={selectAssetsPageIndex} />
                    {React.Children.map(this.props.children, (child) => {
                        return React.cloneElement(child as React.ReactElement, {
                            pagesTop,
                            rt_canvas_render_mode,
                        });
                    })}
                    {/* {controls?.addPage} */}
                    {clippingImage &&
                        asset &&
                        (asset.meta.type !== 'background' ? (
                            <ImageClip pageTop={pageTop} asset={asset} scale={canvas.scale} hide={this.hideImageClip} />
                        ) : (
                            <BackgroundClip
                                pagesTop={pagesTop}
                                pageIndex={selectAssetsPageIndex}
                                asset={asset}
                                scale={canvas.scale}
                                canvas={canvas}
                                hide={this.hideImageClip}
                                renderMode={rt_canvas_render_mode}
                            />
                        ))}
                    {assetIsCutout && asset && (
                        <ImageCutout pageTop={pageTop} asset={asset} scale={canvas.scale} hide={this.hideImageClip} />
                    )}
                    <PageTools
                        asset={asset}
                        canvasDom={this.canvasRef}
                        canvas={canvas}
                        pages={pages}
                        pageInfo={pageInfo}
                        pageIndex={pageIndex}
                        pageAttr={pageAttr}
                        renderMode={rt_canvas_render_mode}
                        assetIndex={toolPanel.asset_index}
                        asssetsIndex={toolPanel.assets_index}
                        user={user}
                        resourcePanelWidth={resourcePanelWidth}
                    />
                </div>
                <CanvasScrollBar
                    canvasDom={this.canvasRef}
                    parentDom={this.renderCanvasRef}
                    canvas={canvas}
                    pages={pages}
                    pageInfo={pageInfo}
                    pageIndex={pageIndex}
                    pageAttr={pageAttr}
                    renderMode={rt_canvas_render_mode}
                    toolPanelWidth={toolPanelWidth}
                    resourcePanelWidth={resourcePanelWidth}
                    toolPanel={toolPanel}
                />
            </div>
        );
    }

    /** 点击画布外部或元素失去选中触发 */
    onClickoutside = (e?: React.MouseEvent | MouseEvent) => {
        if (this.state.editingAsset) {
            let contains = false;
            if (e) {
                const doms = document.querySelectorAll('.floatToolPanel .toolPanel, .commonColorPanel');
                doms.forEach((d) => {
                    if (d.contains(e.target as Node)) {
                        contains = true;
                    }
                });
            }
            if (contains) {
                // do nothing
                return;
            } else {
                this.tgsCanvas.blurSelect();
            }
        }
        if (this.state.clippingImage) {
            // const { pages, pageInfo } = this.props;
            // UpdateAsset.updateAssets('CLIP_IMAGE_END', [
            //     {
            //         index: AssetHelper.getIndex({ pages: pages }, pageInfo, { className: this.state.clippingImage }),
            //         className: this.state.clippingImage,
            //         changes: {
            //             attribute: {
            //                 container: {
            //                     isEdit: false,
            //                 },
            //             },
            //         },
            //     },
            // ]);
        }
        setTimeout(() => {
            this.setState({
                editingAsset: undefined,
                editingAssetType: undefined,
                rotating3DAsset: undefined,
                videoEAsset: undefined,
                // clippingImage: undefined,
            });
        }, 100);
    };

    stopPropagation(e: React.KeyboardEvent) {
        e?.stopPropagation();
        e?.nativeEvent?.stopImmediatePropagation();
    }

    hideRightClickMenu = () => {
        this.setState({ rightClickMenu: { isShow: false, left: 0, top: 0, x: 0, y: 0, scale: 1 } });
    };

    hideImageClip = () => {
        this.setState({ clippingImage: undefined });
    };

    onEditingAssetsMouseDown = (e: React.MouseEvent) => {
        if (e.button === 2) {
            e?.stopPropagation();
            e?.nativeEvent?.stopImmediatePropagation();
        }
    };

    onEditingAssetsMouseUp = (e: React.MouseEvent<HTMLElement>) => {
        if (e.button === 2) {
            e?.stopPropagation();
            e?.nativeEvent?.stopImmediatePropagation();
            if (this.state.editingAsset && this.state.editingAssetType === 'table') {
                const { currentTarget: ct } = e;
                const size = ct.getBoundingClientRect();
                this.setState({
                    rightClickMenu: {
                        isShow: true,
                        className: this.state.editingAsset,
                        type: this.state.editingAssetType,
                        left: e.clientX - size.x + ct.offsetLeft,
                        top: e.clientY - size.y + ct.offsetTop,
                        x: this.props.canvas.x,
                        y: this.props.canvas.y,
                        scale: this.props.canvas.scale,
                    },
                });
            }
        }
    };

    initActions() {
        this.tgsCanvas.actions.setActions({
            /**
             * @param func_name - 触发自动保存的操作名
             * @param targets - IUpdateTarget[]
             */
            onUpdateAssets: (func_name: string, targets: any, unrecordable = false) => {
                UpdateAsset.updateAssets(func_name, targets, undefined, undefined, unrecordable);
                if (func_name === 'DRAP_ASSET_END') {
                    const { toolPanel } = storeAdapter.getStore({
                        store_name: storeAdapter.store_names.paintOnCanvas,
                    });
                    if (toolPanel?.asset) {
                        AssetLogic.changeFrequentTrigPointRule('2429', 2000, { s0: toolPanel.asset.meta.type })
                    } else if (toolPanel?.assets?.length > 1) {
                        assetManager.setPv_new(2430);
                    }
                }
                if (func_name === 'UPDATE_TEXT') {
                    const { toolPanel } = storeAdapter.getStore({
                        store_name: storeAdapter.store_names.paintOnCanvas,
                    });
                    const type = toolPanel?.asset ? toolPanel?.asset?.meta.type : 'text';
                    AssetTextLogic.changeFrequentTrigPointRule('2439', 5 * 1000, { s1: type,s3:toolPanel?.asset?.meta?.addFrom })
                }
                if (func_name === 'MOVE_ASSET_END') {
                    AssetLogic.changeFrequentTrigPointRule('8148', 0)
                }
                if (func_name === 'LINE_BIND_ASSET') {
                    assetManager.setPv_new(8916);
                }
            },
            onUpdateCanvasInfo: TemplateCanvasLogic.setCanvasPositionAndSize,
            onSelectAssets: (param) => {
                SelectAsset.setSelectAssets(param);
                if (param.length > 1) {
                    // 多选元素
                    AssetLogic.changeFrequentTrigPointRule('2444', 2000)
                }

                this.setState({ selectAssetsPageIndex: param[0].page_num });
            },
            onDelAssets: (params: {
                asset_index_list?: {
                    index: number;
                    className?: string;
                    page_num?: number;
                }[];
                origin: string;
            }) => {
                const { work, pageInfo } = storeAdapter.getStore({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                });
                if (params.asset_index_list && params.asset_index_list.length > 0) {
                    // 做一次校验，避免出现画布中的元素index和redux不匹配的情况
                    params.asset_index_list = params.asset_index_list.filter(
                        (item: { index: number; className?: string; page_num?: number }) => {
                            if (item.className) {
                                const pageNum = item.page_num != undefined ? item.page_num : pageInfo.pageNow;
                                return work.pages[pageNum].assets[item.index]?.meta.className == item.className;
                            }
                            return true;
                        },
                    );
                    DeleteAsset.deleteAssetFun(params);
                } else {
                    DeleteAsset.deleteSelectAsset({});
                }
                this.setState({ selectAssetsPageIndex: undefined });
            },
            onBlurAssets: () => {
                SelectAsset.blurAsset();
                this.setState({ selectAssetsPageIndex: undefined });
                this.onClickoutside();
            },
            onAssetEditStatus: () => {
                emitter.emit('changeAssetEditStatus');
            },
            onAssetDoubleClick: (
                params: { x?: number; y?: number; scale?: number },
                asset: TgsTypes.IAsset<TgsTypes.TAssetType> | undefined,
                options?: {
                    emitObj?: fabric.Object;
                    isClip?: boolean;
                },
            ) => {
                // TODO 支持传入 pageIndex
                TemplateCanvasLogic.setCanvasPositionAndSize(params);
                switch (asset.meta.type) {
                    case 'text': {
                        this.setState({ editingAsset: asset.meta.className, editingAssetType: 'text' }, () => {
                            // console.log(this.editRef.current);
                            setTimeout(() => {
                                this.editRef.current?.clickText({ detail: 2 });
                            }, 100);
                        });
                        break;
                    }
                    case 'image':
                    case 'pic':
                    case 'background': {
                        const { isEffectTemplate, isDesigner, toolPanel } = storeAdapter.getStore({
                            store_name: storeAdapter.store_names.paintOnCanvas,
                        });
                        if (isEffectTemplate && !isDesigner && (asset as IImageAsset).attribute.container?.id) {
                            return ImageToolLogic.containerItemClick();
                        }
                        if (isEffectTemplate && !isDesigner) {
                            assetManager.setPv_new(7723);
                        }
                        if (asset.meta.type === 'background') {
                            // 双击触发替换背景埋点
                            assetManager.setPv_new(7754, {
                                additional: {
                                    s0: 'dblclick',
                                },
                            });
                            emitter.emit('FloatToolPanelUpdateState');

                        } else {
                            if (options?.isClip) {
                                const { asset } = toolPanel;
                                const { width, height, posX, posY } = asset.attribute.container;
                                AssetLogic.updateAssetContainer({
                                    container: { width, height, posX, posY },
                                    asset: asset,
                                    assetIndex: toolPanel.asset_index,
                                });
                                AssetLogic.updateAssetContainerIsEdit({
                                    isEdit: true,
                                    asset: toolPanel.asset,
                                    assetIndex: toolPanel.asset_index,
                                    className: asset.meta.className,
                                });

                                assetManager.setPv_new(8032, { additional: { s0: 'image' } });
                            } else {
                                console.log('jyjin bg db click enterance')
                                emitter.emit('UserUploadBoxUploadFile', 'doubleClick', asset, (asset.meta.type === 'image' || asset.meta.type === 'pic') ? 'image-doubleClick' : 'unknown');
                            }
                        }
                        break;
                    }
                    case 'chart': {
                        emitter.emit('AssetChartEditDataTable', {});
                        break;
                    }
                    case 'table': {
                        this.setState({ editingAsset: asset.meta.className, editingAssetType: 'table' });
                        break;
                    }
                    case 'qrcode': {
                        emitter.emit('UserUploadBoxUploadFile', 'doubleClick', asset);
                        break;
                    }
                    case 'frame': {
                        const { emitObj, isClip } = options;
                        FrameCanvasLogic.setCanvasFrameEventObj(emitObj);
                        if (!isClip) {
                            emitter.emit('UserUploadBoxUploadFile', 'doubleClick', asset);
                        } else {
                            assetManager.setPv_new(8032, { additional: { s0: 'frame' } });
                        }
                        break;
                    }
                }
            },
            onRotate3D: (params: { x?: number; y?: number; scale?: number }) => {
                // TODO 支持传入 pageIndex
                TemplateCanvasLogic.setCanvasPositionAndSize(params);
            },
            updateText3DRotation: AssetLogic.updateText3DRotation,
            onPlayVideo: (
                params: { x?: number; y?: number; scale?: number },
                asset: TgsTypes.IVideoEAsset | undefined,
            ) => {
                // TODO 支持传入 pageIndex
                TemplateCanvasLogic.setCanvasPositionAndSize(params);
                if (asset.meta.type === 'videoE') {
                    this.setState({ videoEAsset: asset.meta.className });
                }
            },
            onRightClick: () => {
                assetManager.setPv_new(4720, {
                    additional: {
                        s4: 'canvas',
                    },
                });
            },
            onCancelGroupAssetMove: (groupName: string) => {
                GroupAndMultipleSelectLogic.updateGroupInnerMovingStatus(groupName);
            },
            displayRightClickMenu: (
                e: MouseEvent,
                param: {
                    className?: string;
                    type?: string;
                    x: number;
                    y: number;
                    scale: number;
                },
            ) => {
                // TODO 支持传入 pageIndex
                this.setState({
                    rightClickMenu: {
                        isShow: true,
                        left: e.offsetX,
                        top: e.offsetY,
                        ...param,
                    },
                });
                assetManager.setPv_new(4721, {
                    additional: {
                        s4: 'canvas',
                    },
                });
            },
            onMouseWheel: {
                scale: () => {
                    assetManager.setPv_new(2101, {
                        additional: {
                            s4: 'canvas',
                        },
                    });
                },
                scrollY: (delta: number) => {
                    assetManager.setPv_new(2102, {
                        additional: {
                            s0: delta > 0 ? 'top' : 'bottom',
                            s4: 'canvas',
                        },
                    });
                },
            },
            onRightButtonDragEnd: () => {
                assetManager.setPv_new(6271, {
                    additional: {
                        s4: 'canvas',
                    },
                });
            },
            onUpdateSelectedPage: (param: { pageNumber: number; isShowBorder?: boolean; isDisTool?: boolean }) => {
                if (this.props.rt_canvas_render_mode === 'board' && !this.state.canvasAddPage) {
                    return;
                }
                if (param.pageNumber < 0) {
                    // 取消dom选择框
                    this.setState({ isSelectPage: false });
                    emitter.emit('changeDomPageSelect', false);
                } else {
                    CanvasPaintedLogic.selectPageTemplate(param);
                    CanvasPaintedLogic.mutisizeSetFloorTemplateCurrentindex({
                        floorTemplateIndex: param.pageNumber,
                        clickTem: 'left' as any,
                    });
                    const flag = param.isShowBorder === undefined ? true : param.isShowBorder;
                    this.setState({ isSelectPage: flag });
                    emitter.emit('changeDomPageSelect', flag);
                }
            },
            changeAddPageBtnStatus(status: boolean) {
                emitter.emit('changeDomAddPageBtn', status);
            },

            changePageToolBtnStaus(status: boolean) {
                emitter.emit('changePageToolBtnStaus', status);
            },

            changeAssetTooltipStatus(params: { status: boolean; clientX?: number; clientY?: number; type?: string }) {
                emitter.emit('changeAssetTooltip', params);
            },

            updateAssetPage: (list: { index: number; page_num?: number; pre_page_num?: number; posY: number }[]) => {
                list && AssetLogic.updateAssetPage(list);
            },
            onClipImage: (
                params: { x?: number; y?: number; scale?: number },
                asset: TgsTypes.IImageAsset | undefined,
            ) => {
                TemplateCanvasLogic.setCanvasPositionAndSize(params);
                this.setState({
                    clippingImage: asset.meta.className,
                });
            },
            // 拿到canvas中的一些信息
            initFrameInfo: (frameInfo: FrameInfo) => {
                const frameCanvasLogic = new FrameCanvasLogic();
                frameInfo.setFrameCanvasLogic(frameCanvasLogic);
                frameCanvasLogic.initFrameInfo(frameInfo);
                frameCanvasLogic.addFrameListener();
            },
            // 更新删除的图片的恢复提示
            changeAssetRecoveryToolBoxStatus(params: {
                status: boolean;
                clientX?: number;
                clientY?: number;
                type: string;
            }) {
                const { status, clientX, clientY } = params;
                if (status) {
                    DeletedRecoveryLogic.showRecoveryToolBox({ clientX, clientY });
                } else {
                    DeletedRecoveryLogic.hideRecoveryToolBox();
                }
            },
            initImageCanvasRenderInfo: (imageCanvasRenderInfo: ImageCanvasRenderInfo) => {
                const imageFilterToolLogic = new ImageFilterToolLogic();
                imageCanvasRenderInfo?.setImageFilterToolLogic &&
                    imageCanvasRenderInfo?.setImageFilterToolLogic(imageFilterToolLogic);
                imageFilterToolLogic.initImageCanvasRenderInfo(imageCanvasRenderInfo);
            },
            hidePreviewImage: () => {
                if (this.state.showPreview) {
                    this.setState({ showPreview: false });
                }
            },
            onSplitGroup: (groupName: string) => {
                AssetLogic.splitAsset({ groupName });
            },
            onGroupEdit: (type: 'select' | 'edit') => {
                switch (type) {
                    case 'select':
                        assetManager.setPv_new(7797);
                        break;
                    case 'edit':
                        assetManager.setPv_new(7798);
                        break;
                }
            },
            onGroupInnerAssetTransformEnd: (groupName: string, actionType) => {
                GroupAndMultipleSelectLogic.updateGroupBoundingRectAfterAssetChange(groupName);
                switch (actionType) {
                    case 'moving':
                        assetManager.setPv_new(7810);
                        break;
                    case 'scaling':
                        assetManager.setPv_new(7811);
                        break;
                    case 'rotating':
                        assetManager.setPv_new(7812);
                }
            },
            updateCanvasLoadingStatus: (status: boolean) => {
                CanvasPaintedLogic.updateCanvasLoadingStatus(status);
            },
            onITextSelectionChange: (
                selectionData: {
                    richText: IRichText[];
                    start: number;
                    end: number;
                    index: number;
                    className: string;
                    isAnchored?: boolean;
                    rt_floor_index?: number;
                    page_num?: number;
                },
                isMultipleSelection: boolean,
            ) => {
                const { richText, start, end } = selectionData;
                const result = [];
                for (let i = start; i < end; i++) {
                    result.push(i);
                }
                RichTextLogic.updateText(richText, result);
                emitter.emit('checkSelectedTextProperty');
                emitter.emit('updateSelectedTextProperty');
            },
            onMultipleSelectMouseUp(e: MouseEvent, className: string) {
                AssetTextLogic.onMultipleSelectMouseUp(e, className);
            },

            onShowrtcutMenu: (status: boolean) => {
                this.setState({ isShowShortcutMenu: status });
            },

            assetEditing(targets, type) {
                emitter.emit("assetEditing", {
                    type,
                    targets
                })
            },
            assetEditEnd() {
                emitter.emit("assetEditEnd")
            },
            controlAppear(corner) {
                if (corner == "mtr2") {
                    assetManager.setPv_new(8173);
                }
            },
            addLineEffect(line, action) {
                assetManager.setPv_new(8915,{ additional: { s0: action, s1: line?.resId } });
                emitter.emit('ListAddLine', line);
            },
        });
    }
}
