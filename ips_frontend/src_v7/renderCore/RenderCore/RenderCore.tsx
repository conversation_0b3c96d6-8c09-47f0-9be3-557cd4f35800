import { getFontNameValueList } from '@src/userComponentV6.0/IPSConfig';
import { baseTools } from '@v7_utils/BaseTools'
import { TgsCanvas } from '@tgs/canvas';
import { ICanvas, IPageAttr, IPageInfo, IToolPanel, IWork, IUserInfo } from '@v7_logic/Interface';
import equal from 'fast-deep-equal';
import React, { PureComponent } from 'react';
import { RenderCanvas } from '../RenderCanvas';
import './scss/RenderCore.scss';
import { IPSConfig } from '@v7_utils/IPSConfig';

TgsCanvas.util.setFontFamilyList(getFontNameValueList() as any);
TgsCanvas.util.setSystemType(baseTools.isSystem())
interface IRenderCoreProps {
    canvas: ICanvas;
    work: IWork;
    pageAttr: IPageAttr;
    pageInfo: IPageInfo;
    toolPanel: IToolPanel;
    rt_is_online_detail_page: boolean;
    smallFontKeys: Record<string, boolean>;
    controls?: Record<string, React.ReactNode>;
    children?: React.ReactNode;
    rt_canvas_render_mode: '' | 'pull' | 'board';
    baseGap: number;
    toolPanelWidth?: number;
    resourcePanelWidth?: number;
    user: IUserInfo
}

export class RenderCore extends PureComponent<IRenderCoreProps> {
    renderCoreDom = React.createRef<HTMLDivElement>();

    override componentDidUpdate(prevProps: IRenderCoreProps) {
        const { smallFontKeys } = this.props;
        if (!equal(this.props.smallFontKeys, prevProps.smallFontKeys)) {
            // 设置小字体信息
            TgsCanvas.util.setSmallFontKeys(smallFontKeys);
        }
    }

    override render() {
        const { canvas, work, pageAttr, pageInfo, toolPanel, rt_is_online_detail_page, controls, rt_canvas_render_mode, baseGap, user, resourcePanelWidth, toolPanelWidth } = this.props;
        const urlProps = IPSConfig.getProps();
        const showWaterMark = user.vip <= 1  &&  !urlProps.isDesigner
        return (
            <div
                className="render-core"
                // style={{
                //     width: Math.floor(canvas.width * canvas.scale),
                //     height: Math.floor(canvas.height * canvas.scale),
                // }}
                ref={this.renderCoreDom}
            >
                <RenderCanvas
                    renderCoreDom={this.renderCoreDom?.current}
                    canvas={canvas}
                    pages={work.pages}
                    resourcePanelWidth={resourcePanelWidth}
                    toolPanelWidth={toolPanelWidth}
                    pageInfo={pageInfo}
                    pageAttr={pageAttr}
                    pageIndex={pageInfo.pageNow}
                    toolPanel={toolPanel}
                    rt_is_online_detail_page={rt_is_online_detail_page}
                    controls={controls}
                    rt_canvas_render_mode={rt_canvas_render_mode}
                    baseGap={baseGap}
                    user={user}
                    showWaterMark={showWaterMark}
                >
                    {this.props.children}
                </RenderCanvas>
            </div>
        );
    }
}
