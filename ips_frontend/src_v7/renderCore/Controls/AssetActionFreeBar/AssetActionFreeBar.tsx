/**
 * 悬浮框操作栏
 */
import { emitter } from '@src/userComponentV6.0/Emitter';
import SliderItem from '@src/userComponentV6.0/Slider/SliderItem';
import { imgHost } from '@src/userComponentV6.0/IPSConfig';
import { TableBorderStylePanel } from '@src/userComponentV6.0/canvas/tableTextSetting/ToolTablePanel.js';
import React, { Component } from 'react';
import { EditorLogic } from '@v7_logic/EditorLogic';
import { RightPanel } from '@v7_logic/RightPanel';
import { AssetLogic, SelectAsset, UpdateAsset } from '@v7_logic/AssetLogic';
import { AssetDeleteWrap } from '@v7_render/Asset/Frame/AssetDeleteWrap';
import { MoreColorsPanel } from '@src/userComponentV6.0/MoreColorsPanel/MoreColorsPanel';
import { ToolTip } from '@v7_render/Ui/ToolTip';
import { assetManager } from '@src/userComponentV6.0/AssetManager';
import { IAsset, ICanvas, IUserInfo, IToolPanel } from '@v7_logic/Interface';
import { ClickOutside } from '@v7_render/Ui';
import { CanvasPaintedLogic } from '@v7_logic/CanvasPaintedLogic';
import { GroupAndMultipleSelectLogic } from '@v7_logic/GroupAndMultipleSelectLogic';
import TagSelectBox from '@v7_render/AIDesignTools/TagSelectBox/TagSelectBox';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import ColorTagSelectBox from '@v7_render/AIDesignTools/TagSelectBox/ColorTagSelectBox';
import { isAIDesign } from '@v7_utils/estimate';
import AssetQuickFreeBar from './AssetQuickFreeBar';

const presetColorTableList = [
    {
        id: 3,
        color_table: [
            {
                color_table: [
                    { r: 96, g: 103, b: 112, a: 1, id: 1 },
                    { r: 141, g: 151, b: 164, a: 1, id: 2 },
                    { r: 214, g: 218, b: 222, a: 1, id: 3 },
                ],
                id: 1,
            },
            {
                color_table: [
                    { r: 95, g: 149, b: 145, a: 1, id: 1 },
                    { r: 128, g: 165, b: 162, a: 1, id: 2 },
                    { r: 168, g: 193, b: 186, a: 1, id: 3 },
                ],
                id: 2,
            },
            {
                color_table: [
                    { r: 93, g: 88, b: 86, a: 1, id: 1 },
                    { r: 132, g: 117, b: 111, a: 1, id: 2 },
                    { r: 164, g: 163, b: 162, a: 1, id: 3 },
                ],
                id: 3,
            },
            {
                color_table: [
                    { r: 95, g: 103, b: 149, a: 1, id: 1 },
                    { r: 112, g: 116, b: 150, a: 1, id: 2 },
                    { r: 144, g: 147, b: 170, a: 1, id: 3 },
                ],
                id: 4,
            },
            {
                color_table: [
                    { r: 149, g: 108, b: 95, a: 1, id: 1 },
                    { r: 171, g: 150, b: 142, a: 1, id: 2 },
                    { r: 199, g: 192, b: 182, a: 1, id: 3 },
                ],
                id: 5,
            },
        ],
    },
    {
        id: 4,
        color_table: [
            {
                color_table: [
                    { r: 96, g: 103, b: 112, a: 1, id: 1 },
                    { r: 141, g: 151, b: 164, a: 1, id: 2 },
                    { r: 214, g: 218, b: 222, a: 1, id: 3 },
                ],
                id: 1,
            },
            {
                color_table: [
                    { r: 95, g: 149, b: 145, a: 1, id: 1 },
                    { r: 128, g: 165, b: 162, a: 1, id: 2 },
                    { r: 168, g: 193, b: 186, a: 1, id: 3 },
                ],
                id: 2,
            },
            {
                color_table: [
                    { r: 93, g: 88, b: 86, a: 1, id: 1 },
                    { r: 132, g: 117, b: 111, a: 1, id: 2 },
                    { r: 164, g: 163, b: 162, a: 1, id: 3 },
                ],
                id: 3,
            },
            {
                color_table: [
                    { r: 95, g: 103, b: 149, a: 1, id: 1 },
                    { r: 112, g: 116, b: 150, a: 1, id: 2 },
                    { r: 144, g: 147, b: 170, a: 1, id: 3 },
                ],
                id: 4,
            },
            {
                color_table: [
                    { r: 149, g: 108, b: 95, a: 1, id: 1 },
                    { r: 171, g: 150, b: 142, a: 1, id: 2 },
                    { r: 199, g: 192, b: 182, a: 1, id: 3 },
                ],
                id: 5,
            },
        ],
    },
    {
        id: 5,
        color_table: [
            {
                color_table: [
                    { r: 96, g: 103, b: 112, a: 1, id: 1 },
                    { r: 141, g: 151, b: 164, a: 1, id: 2 },
                    { r: 214, g: 218, b: 222, a: 1, id: 3 },
                ],
                id: 1,
            },
            {
                color_table: [
                    { r: 95, g: 149, b: 145, a: 1, id: 1 },
                    { r: 128, g: 165, b: 162, a: 1, id: 2 },
                    { r: 168, g: 193, b: 186, a: 1, id: 3 },
                ],
                id: 2,
            },
            {
                color_table: [
                    { r: 93, g: 88, b: 86, a: 1, id: 1 },
                    { r: 132, g: 117, b: 111, a: 1, id: 2 },
                    { r: 164, g: 163, b: 162, a: 1, id: 3 },
                ],
                id: 3,
            },
            {
                color_table: [
                    { r: 95, g: 103, b: 149, a: 1, id: 1 },
                    { r: 112, g: 116, b: 150, a: 1, id: 2 },
                    { r: 144, g: 147, b: 170, a: 1, id: 3 },
                ],
                id: 4,
            },
            {
                color_table: [
                    { r: 149, g: 108, b: 95, a: 1, id: 1 },
                    { r: 171, g: 150, b: 142, a: 1, id: 2 },
                    { r: 199, g: 192, b: 182, a: 1, id: 3 },
                ],
                id: 5,
            },
        ],
    },
    {
        id: 6,
        color_table: [
            {
                color_table: [
                    { r: 96, g: 103, b: 112, a: 1, id: 1 },
                    { r: 141, g: 151, b: 164, a: 1, id: 2 },
                    { r: 214, g: 218, b: 222, a: 1, id: 3 },
                ],
                id: 1,
            },
            {
                color_table: [
                    { r: 95, g: 149, b: 145, a: 1, id: 1 },
                    { r: 128, g: 165, b: 162, a: 1, id: 2 },
                    { r: 168, g: 193, b: 186, a: 1, id: 3 },
                ],
                id: 2,
            },
            {
                color_table: [
                    { r: 93, g: 88, b: 86, a: 1, id: 1 },
                    { r: 132, g: 117, b: 111, a: 1, id: 2 },
                    { r: 164, g: 163, b: 162, a: 1, id: 3 },
                ],
                id: 3,
            },
            {
                color_table: [
                    { r: 95, g: 103, b: 149, a: 1, id: 1 },
                    { r: 112, g: 116, b: 150, a: 1, id: 2 },
                    { r: 144, g: 147, b: 170, a: 1, id: 3 },
                ],
                id: 4,
            },
            {
                color_table: [
                    { r: 149, g: 108, b: 95, a: 1, id: 1 },
                    { r: 171, g: 150, b: 142, a: 1, id: 2 },
                    { r: 199, g: 192, b: 182, a: 1, id: 3 },
                ],
                id: 5,
            },
        ],
    },
    {
        id: 7,
        color_table: [
            {
                color_table: [
                    { r: 96, g: 103, b: 112, a: 1, id: 1 },
                    { r: 141, g: 151, b: 164, a: 1, id: 2 },
                    { r: 214, g: 218, b: 222, a: 1, id: 3 },
                ],
                id: 1,
            },
            {
                color_table: [
                    { r: 95, g: 149, b: 145, a: 1, id: 1 },
                    { r: 128, g: 165, b: 162, a: 1, id: 2 },
                    { r: 168, g: 193, b: 186, a: 1, id: 3 },
                ],
                id: 2,
            },
            {
                color_table: [
                    { r: 93, g: 88, b: 86, a: 1, id: 1 },
                    { r: 132, g: 117, b: 111, a: 1, id: 2 },
                    { r: 164, g: 163, b: 162, a: 1, id: 3 },
                ],
                id: 3,
            },
            {
                color_table: [
                    { r: 95, g: 103, b: 149, a: 1, id: 1 },
                    { r: 112, g: 116, b: 150, a: 1, id: 2 },
                    { r: 144, g: 147, b: 170, a: 1, id: 3 },
                ],
                id: 4,
            },
            {
                color_table: [
                    { r: 149, g: 108, b: 95, a: 1, id: 1 },
                    { r: 171, g: 150, b: 142, a: 1, id: 2 },
                    { r: 199, g: 192, b: 182, a: 1, id: 3 },
                ],
                id: 5,
            },
        ],
    },
    {
        id: 8,
        color_table: [
            {
                color_table: [
                    { r: 96, g: 103, b: 112, a: 1, id: 1 },
                    { r: 141, g: 151, b: 164, a: 1, id: 2 },
                    { r: 214, g: 218, b: 222, a: 1, id: 3 },
                ],
                id: 1,
            },
            {
                color_table: [
                    { r: 95, g: 149, b: 145, a: 1, id: 1 },
                    { r: 128, g: 165, b: 162, a: 1, id: 2 },
                    { r: 168, g: 193, b: 186, a: 1, id: 3 },
                ],
                id: 2,
            },
            {
                color_table: [
                    { r: 93, g: 88, b: 86, a: 1, id: 1 },
                    { r: 132, g: 117, b: 111, a: 1, id: 2 },
                    { r: 164, g: 163, b: 162, a: 1, id: 3 },
                ],
                id: 3,
            },
            {
                color_table: [
                    { r: 95, g: 103, b: 149, a: 1, id: 1 },
                    { r: 112, g: 116, b: 150, a: 1, id: 2 },
                    { r: 144, g: 147, b: 170, a: 1, id: 3 },
                ],
                id: 4,
            },
            {
                color_table: [
                    { r: 149, g: 108, b: 95, a: 1, id: 1 },
                    { r: 171, g: 150, b: 142, a: 1, id: 2 },
                    { r: 199, g: 192, b: 182, a: 1, id: 3 },
                ],
                id: 5,
            },
        ],
    },
    {
        id: 9,
        color_table: [
            {
                color_table: [
                    { r: 96, g: 103, b: 112, a: 1, id: 1 },
                    { r: 141, g: 151, b: 164, a: 1, id: 2 },
                    { r: 214, g: 218, b: 222, a: 1, id: 3 },
                ],
                id: 1,
            },
            {
                color_table: [
                    { r: 95, g: 149, b: 145, a: 1, id: 1 },
                    { r: 128, g: 165, b: 162, a: 1, id: 2 },
                    { r: 168, g: 193, b: 186, a: 1, id: 3 },
                ],
                id: 2,
            },
            {
                color_table: [
                    { r: 93, g: 88, b: 86, a: 1, id: 1 },
                    { r: 132, g: 117, b: 111, a: 1, id: 2 },
                    { r: 164, g: 163, b: 162, a: 1, id: 3 },
                ],
                id: 3,
            },
            {
                color_table: [
                    { r: 95, g: 103, b: 149, a: 1, id: 1 },
                    { r: 112, g: 116, b: 150, a: 1, id: 2 },
                    { r: 144, g: 147, b: 170, a: 1, id: 3 },
                ],
                id: 4,
            },
            {
                color_table: [
                    { r: 149, g: 108, b: 95, a: 1, id: 1 },
                    { r: 171, g: 150, b: 142, a: 1, id: 2 },
                    { r: 199, g: 192, b: 182, a: 1, id: 3 },
                ],
                id: 5,
            },
        ],
    },
];

interface IAssetActionFreeBarProps {
    user: IUserInfo;
    isShow: boolean;
    canvas: ICanvas;
    asset: IAsset;
    pageIndex: number;
    pagesTop: number[];
    renderMode: '' | 'board' | 'pull';
    canvasDom: React.RefObject<HTMLCanvasElement>;
    toolPanel: IToolPanel;
    isShowMoreMenu: boolean;
    toolPanelWidth: any;
    isSelectPage: boolean;
    hide: () => void;
    handleMoreEvent: (param: { left: number; top: number; x: number; y: number; actionType?: string }) => void;
}
interface IAssetActionFreeBarState {
    isFix: boolean;
    isShowFramDelPanel: boolean;
    isShowRightMenu: boolean;
    fontSizeShow: boolean;
    showTool: string;
    presetColorTableList: any[];
    // 右侧工具栏是否固定，true为固定
    fixedToolPanel: boolean;
    // 渐变颜色面板
    showChooselinearGradient: boolean;
    // 渐变颜色面板
    isColorMorePanel: boolean;
    colorShow: boolean;
    showAiTextOptionWrap: boolean;
}

export class AssetActionFreeBar extends Component<IAssetActionFreeBarProps, IAssetActionFreeBarState> {
    wrapRef = React.createRef<HTMLDivElement>();
    dragRef = React.createRef<HTMLElement>();
    clientX: number;
    clientY: number;
    fixedToolPanelListener: any;
    choiceColorBtnClickListener: any;
    
    constructor(props: IAssetActionFreeBarProps) {
        super(props);
        this.state = {
            isFix: false,
            isShowFramDelPanel: false,
            isShowRightMenu: false,
            fontSizeShow: false,
            showTool: '',
            presetColorTableList: presetColorTableList,
            fixedToolPanel: true,
            showChooselinearGradient: false,
            isColorMorePanel: false,
            colorShow: false,
            showAiTextOptionWrap: false,
        };
    }

    getUserMenuPosition() {
        const { user } = this.props;
        assetManager.getShortcutMenuPosition(user.userId).then((data) => {
            data.json().then((res) => {
                if (res.code == 1) {
                    const pos = res.data.menu.position;
                    const screenWidth = window.innerWidth;
                    const screenHeight = window.innerHeight;
                    if (pos.isFixed === true) {
                        this.clientX = pos.x * screenWidth;
                        this.clientY = pos.y * screenHeight;
                    }
                    this.setState(
                        {
                            isFix: pos.isFixed,
                        },
                        () => {
                            this.onScreenResize();
                        },
                    );
                }
            });
        });
    }

    setUserMenuPosition() {
        const { user } = this.props;
        const { isFix } = this.state;
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;
        const x_percent = this.clientX / screenWidth;
        const y_percent = this.clientY / screenHeight;
        const postData = {
            uid: user.userId,
            is_fixed: isFix ? 2 : 1,
            custom_fixed: 2,
            x: isFix ? x_percent : 0,
            y: isFix ? y_percent : 0,
        };
        assetManager.savehortcutMenuPosition(postData).then((data) => {
            data.json().then((res) => {
                // console.log(res);
            });
        });
    }

    componentDidUpdate(
        prevProps: Readonly<IAssetActionFreeBarProps>,
        prevState: Readonly<IAssetActionFreeBarState>,
    ): void {
        if (prevProps.user !== this.props.user) {
            this.getUserMenuPosition();
        }
        if (prevProps.asset !== this.props.asset) {
            this.setState({
                isShowFramDelPanel: false,
            });
        }
        if (!this.state.isFix && this.wrapRef.current && this.props.renderMode !== 'board') {
            const boxLeftPosition =
                this.props.canvas.x +
                (this.props.canvas.width * this.props.canvas.scale) / 2 -
                this.wrapRef.current.clientWidth / 2;
            if (!this.wrapRef.current.style.left.startsWith(boxLeftPosition.toString())) {
                this.wrapRef.current.style.left = boxLeftPosition + 'px';
            }
        }
    }

    onScreenResize() {
        const oCanvasLayout: HTMLDivElement = document.querySelector('.canvasLayout');
        const parentDom = oCanvasLayout?.parentElement;
        const canvasHeight = oCanvasLayout?.clientHeight || 0;
        const canvasWidth = oCanvasLayout?.clientWidth || 0;
        const offsetLeft = oCanvasLayout?.offsetLeft || 0;
        const offsetTop = parentDom?.offsetTop || 0;
        const wrapWidth = this.wrapRef?.current?.clientWidth || 200;
        const wrapHeight = this.wrapRef?.current?.clientHeight || 40;
        if (this.state.isFix) {
            if (this.clientX < offsetLeft) {
                this.clientX = offsetLeft;
            } else if (this.clientX > offsetLeft + canvasWidth) {
                this.clientX = offsetLeft + canvasWidth - wrapWidth;
            }
            if (this.clientY < offsetTop) {
                this.clientY = offsetTop;
            } else if (this.clientY + wrapHeight > offsetTop + canvasHeight) {
                this.clientY = offsetTop + canvasHeight - wrapHeight;
            }
        }
    }

    /** font-size Array*/
    getFontSizeAllCharacters(value: number) {
        const fontSizeBase = [
            12, 14, 16, 18, 20, 24, 28, 32, 36, 40, 44, 48, 54, 60, 66, 72, 80, 88, 96, 100, 150, 200, 250,
        ];

        const content = fontSizeBase.map((item, index) => {
            return (
                <li
                    className={`${value === item ? 'active' : ''}`}
                    key={index}
                    onMouseDown={(e) => {
                        e.stopPropagation();
                        e.nativeEvent.stopPropagation();
                        this.fontSizeClickEvent(item.toString());
                    }}
                    onMouseEnter={(e) => {
                        e.stopPropagation();
                        e.nativeEvent.stopPropagation();
                        this.fontListItemHover(item.toString());
                    }}
                >
                    {item}
                </li>
            );
        });

        return content;
    }

    fontSizeInputChangeEvent(e: any, asset: any) {
        e.stopPropagation();
        const fontSize = parseInt(e.currentTarget.value) > 0 ? parseInt(e.currentTarget.value) : 0;
        const assetData = { index: asset?.meta?.index, className: asset?.meta?.className };
        emitter.emit('openFuncFloat', 'fontSizeInputChangeEvent', fontSize, assetData);
    }

    fontSizeInputScrollEvent(e: any, asset: any) {
        const assetData = { index: asset?.meta?.index, className: asset?.meta?.className };
        emitter.emit('openFuncFloat', 'fontSizeInputScrollEvent', e, assetData);
    }

    fontListAreaMouseLeave() {
        const { asset } = this.props;
        const assetData = { index: asset?.meta?.index, className: asset?.meta?.className };
        emitter.emit('openFuncFloat', 'fontListAreaMouseLeave', '', assetData);
        this.setState({ fontSizeShow: false });
    }

    fontSizeClickEvent(item: string) {
        this.setState({ fontSizeShow: false });
        emitter.emit('openFuncFloat', 'fontSizeClickEvent', item);
    }

    fontListItemHover(item: string) {
        emitter.emit('openFuncFloat', 'fontListItemHover', item);
    }

    fontSizeBtnMouseDownEvent(e: React.MouseEvent, type: string, fontSize: any) {
        e.preventDefault();
        e.stopPropagation();
        const { asset } = this.props;
        const assetData = { index: asset?.meta?.index, className: asset?.meta?.className, fontSize: fontSize };
        this.setState({ fontSizeShow: false });
        emitter.emit('openFuncFloat', 'fontSizeBtnMouseDownEvent', type, assetData);
    }

    componentDidMount(): void {
        const storageFixedToolPanel = localStorage.getItem('fixedToolPanel');
        const fixedToolPanel = storageFixedToolPanel ? storageFixedToolPanel === 'true' : true;
        this.setState({ fixedToolPanel });
        // 监听屏幕大小变化
        this.fixedToolPanelListener = emitter.addListener('setFixedToolPanel', (data: boolean) => {
            this.setState({ fixedToolPanel: data });
        });
        window.addEventListener('resize', this.onScreenResize.bind(this));
    }

    componentWillUnmount(): void {
        this.fixedToolPanelListener?.remove();
        window.removeEventListener('resize', this.onScreenResize.bind(this));
    }

    shouldComponentUpdate(nextProps: any, nextState: any) {
        if (nextProps.asset?.meta?.hash !== this.props.asset?.meta?.hash) {
            emitter.emit('floatMenu.hideFloat', 'switch-asset');
            this.setState({ showTool: '' });
        }
        return true;
    }

    onDragBtnMouseDown = (e: React.MouseEvent) => {
        e.stopPropagation();
        e.nativeEvent?.stopPropagation();
        this.props.hide();
        this.dragRef.current?.classList.add('active');
        assetManager.setPv_new(8146);
        window.addEventListener('mousemove', this.onDragBtnMouseMove);
        window.addEventListener('mouseup', this.onDragBtnMouseUp);
    };

    onDragBtnMouseMove = (e: MouseEvent) => {
        e.stopPropagation();
        const oCanvasLayout: HTMLDivElement = document.querySelector('.canvasLayout');
        const parentDom = oCanvasLayout?.parentElement;
        const canvasHeight = oCanvasLayout?.clientHeight || 0;
        const canvasWidth = oCanvasLayout?.clientWidth || 0;
        const offsetLeft = oCanvasLayout?.offsetLeft || 0;
        const offsetTop = parentDom?.offsetTop || 0;
        const wrapWidth = this.wrapRef?.current?.clientWidth || 200;
        const param = 15;
        const padding = 30;
        const { renderMode } = this.props;
        const bottom = renderMode === '' ? 80 : 200;
        let moveX = e.clientX;
        let moveY = e.clientY;
        if (
            e.clientX < offsetLeft + padding ||
            e.clientX + wrapWidth > offsetLeft + canvasWidth ||
            e.clientY < offsetTop + padding ||
            e.clientY + bottom > offsetTop + canvasHeight
        ) {
            if (e.clientX < offsetLeft + padding) {
                moveX = offsetLeft + padding;
            } else if (e.clientX + wrapWidth > offsetLeft + canvasWidth) {
                moveX = offsetLeft + canvasWidth - wrapWidth;
            }
            if (e.clientY < offsetTop + padding) {
                moveY = offsetTop + padding;
            } else if (e.clientY + bottom > offsetTop + canvasHeight) {
                moveY = offsetTop + canvasHeight - bottom;
            }
        }

        if (!this.state.isFix) {
            this.setState(
                {
                    isFix: true,
                },
                () => {
                    if (this.wrapRef.current) {
                        this.wrapRef.current.style.position = 'fixed';
                    }
                },
            );
        }

        this.clientX = moveX - param;
        this.clientY = moveY - param;
        this.wrapRef.current.style.left = this.clientX + 'px';
        this.wrapRef.current.style.top = this.clientY + 'px';
    };

    onDragBtnMouseUp = (e: MouseEvent) => {
        e.stopPropagation();
        this.setState({});
        this.setUserMenuPosition();
        this.dragRef.current.classList.remove('active');
        window.removeEventListener('mousemove', this.onDragBtnMouseMove);
        window.removeEventListener('mouseup', this.onDragBtnMouseUp);
    };

    /**
     * 解锁元素
     */
    unLockAsset(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.stopPropagation();
        assetManager.setPv_new(2431, { additional: { s0: 'menu' } });
        const { toolPanel } = this.props;
        if (toolPanel.assets.length > 0) {
            GroupAndMultipleSelectLogic.updateAssetsLocked(false);
        } else {
            AssetLogic.updateLocked({
                locked: false,
            });
            // canvasStore.dispatch(paintOnCanvas('UPDATE_LOCKED', { locked: !toolPanel.asset.meta.locked }))
        }
        assetManager.setPv_new(2425, { additional: {} });
    }

    /**
     * 获取文字属性
     */
    getTextProperty(key: any, value: any) {
        return AssetLogic.getSelectedTextProperty(key, value);
    }

    /**
     * 设置特效字
     */
    setEffectText(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.preventDefault();
        e.stopPropagation();
        const { asset } = this.props;
        if (asset?.meta?.type === 'text') {
            this.showToolPanel(e, 'effect');
            emitter.emit('floatMenu.hideFloat', 'base', this.state?.showTool === 'effect');

            setTimeout(() => {
                emitter.emit('openFuncFloat', 'effect');
            }, 200);
        }
    }

    /**
     * 渐变文字自定义颜色（点击事件）
     */
    linearGradientBtnClickEvent(e: any) {
        this.showToolPanel(e, 'font-color');
        console.log('linearGradientBtnClickEvent');

        emitter.emit('floatMenu.hideFloat', 'base', this.state?.showTool === 'font-color');

        setTimeout(() => {
            this.setState({
                isColorMorePanel: false,
                showChooselinearGradient: this.state?.showTool === 'font-color',
            });
        }, 200);

        if (this.choiceColorBtnClickListener) {
            this.choiceColorBtnClickListener.remove();
        }
        const th = this;
        this.choiceColorBtnClickListener = addEventListener('click', (e: any) => {
            th.choiceColorBtnClickListener?.remove();
            th.setState({
                showChooselinearGradient: false,
            });
        });

        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    }

    /**
     * 特效字渐变配色修改
     * @param { object } payload
     */
    chooseLinearGradientColor(payload: any) {
        const { toolPanel } = this.props;
        AssetLogic.updateTextSpecificWordLineArgradient({
            payload,
            assetIndex: toolPanel.asset_index,
        });
        // canvasStore.dispatch(paintOnCanvas('UPDATE_TEXT_SPECIFIC_WORD_LINEARGRADIENT', { ...payload }));
    }

    closeMoreColors() {
        this.setState({
            isColorMorePanel: false,
            colorShow: false,
        });
        RightPanel.updateRightDialogFlag({ rightDialog: false });
        // this.choiceColorClickListtener.remove()
    }

    /**
     * 设置字体
     */
    setFontFamily(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e?.preventDefault();
        e?.stopPropagation();
        const { asset } = this.props;
        if (asset?.meta?.type === 'text') {
            if (this.state?.showTool === 'font-color') {
                this.showToolPanel(null, 'font-color');
                emitter.emit('floatMenu.hideFloat', this.state?.showTool === 'font-color');

                setTimeout(() => {
                    emitter.emit('openFuncFloat', 'font-color');
                }, 200);
            }

            this.showToolPanel(e, 'font-family');
            emitter.emit('floatMenu.hideFloat', 'base', this.state?.showTool === 'font-family');

            setTimeout(() => {
                emitter.emit('openFuncFloat', 'font-family', this.state?.showTool === 'font-family');
            }, 400);
        }
    }

    /**
     * 设置字体颜色
     */
    setFontColor(e: React.MouseEvent<HTMLElement, MouseEvent>, type: string, index: number): void {
        e?.preventDefault();
        e?.stopPropagation();
        const { asset } = this.props;
        if (asset?.meta?.type === 'text') {
            if (this.state?.showTool === 'font-family') {
                this.setFontFamily(null);
            }

            this.showToolPanel(e, 'font-color');
            emitter.emit('floatMenu.hideFloat', this.state?.showTool === 'font-color');

            setTimeout(() => {
                emitter.emit('openFuncFloat', 'font-color');
                emitter.emit('setFontColor', type, index, asset?.attribute?.effectVariant?.variableColorPara?.length);
            }, 400);
        }
    }

    /**
     * 设置字体粗细
     */
    setFontWeight(e: React.MouseEvent<HTMLElement, MouseEvent>, type: string) {
        e.preventDefault();
        e.stopPropagation();
        const { asset, toolPanel } = this.props;
        if (asset?.meta?.type === 'text') {
            if (type === 'font-weight') {
                assetManager.setPv_new(8564);
                const fontWeight = this.getTextProperty('fontWeight', asset.attribute.fontWeight);
                AssetLogic.updateAssetText({
                    assetIndex: toolPanel.asset_index,
                    className: '',
                    attribute: 'attribute',
                    key: 'fontWeight',
                    value: fontWeight == 'bold' ? 'normal' : 'bold',
                    fun_name: 'UPDATE_FONTWEIGHT',
                });
            } else if (type === 'font-italic') {
                assetManager.setPv_new(8565);
                const fontStyle = this.getTextProperty('fontStyle', asset.attribute.fontStyle);
                const value = fontStyle == 'italic' ? 'normal' : 'italic';
                AssetLogic.updateAssetText({
                    assetIndex: toolPanel.asset_index,
                    className: '',
                    attribute: 'attribute',
                    key: 'fontStyle',
                    value: value,
                    fun_name: 'UPDATE_FONTSTYLE',
                });
            } else {
                emitter.emit('floatPanelChangeFontStyle', type, toolPanel?.asset_index);
            }
        }
    }

    /**
     * 设置字体对齐方式
     */
    setFontAlign(e: React.MouseEvent<HTMLElement, MouseEvent>, type: string) {
        e.preventDefault();
        e.stopPropagation();
        const { asset } = this.props;
        if (asset?.meta?.type === 'text') {
            emitter.emit('floatPanelChangeFontAlign', type);
        }
    }

    /**
     * 鼠标离开事件
     */
    handleMouseLeave(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.preventDefault();
        e.stopPropagation();
        const { asset } = this.props;
        if (asset?.meta?.type === 'text') {
            this.setState({
                showTool: '',
            });
        }
    }

    /**
     * 显示字体间距的浮动面板
     */
    showToolPanel(e: React.MouseEvent<HTMLElement, MouseEvent>, type: string) {
        e?.preventDefault();
        e?.stopPropagation();
        if (this.state.showTool === '' || this.state.showTool !== type) {
            this.setState({
                showTool: type,
            });
        } else {
            this.setState({
                showTool: '',
            });
        }
    }

    /**
     * 设置字体间距
     */
    setFontSpace(type: string, value: any, fontSize: any = undefined) {
        const { asset } = this.props;
        if (asset?.meta?.type === 'text') {
            if (type === 'letterSpacing') {
                emitter.emit('floatPanelChangeFontSpace', type, value, fontSize);
            } else if (type === 'lineHeight') {
                emitter.emit('floatPanelChangeFontSpace', type, value);
            }
        }
    }

    /**
     * 设置字体粗细
     */
    setFontLayer(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.preventDefault();
        e.stopPropagation();
        const { asset } = this.props;
        if (asset?.meta?.type === 'text') {
            this.setState({
                showTool: 'font-layer',
            });
            emitter.emit('openFuncFloat', 'font-layer');
        }
    }

    /**
     * 文字背景色
     * @param {*} selectTextBackgroundColor
     * @returns
     */
    getChoiceBackgroundColor = (selectTextBackgroundColor: any) => {
        const style = {
            border: '1px solid #E9E8E8',
            margin: '0px',
            width: '20px',
            height: '20px',
            borderRadius: '2px',
        };
        if (selectTextBackgroundColor && selectTextBackgroundColor.a) {
            return {
                ...style,
                background:
                    'rgba(' +
                    selectTextBackgroundColor.r +
                    ', ' +
                    selectTextBackgroundColor.g +
                    ', ' +
                    selectTextBackgroundColor.b +
                    ', ' +
                    selectTextBackgroundColor.a +
                    ')',
            };
        } else {
            return {
                ...style,
                backgroundImage: `url(${imgHost + '/image/picEditor/transparentBg.png'})`,
                backgroundRepeat: 'repeat-x',
                backgroundSize: 'auto 22px',
            };
        }
    };

    //----------------------------------------------------------------
    // 图片image

    /**
     * 图片替换
     */
    setImageReplace(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.preventDefault();
        e.stopPropagation();
        const { asset } = this.props;
        if (asset.meta?.type === 'image' || asset.meta?.type === 'pic') {
            this.showToolPanel(e, 'image-replace');
            // emitter.emit("floatMenu.hideFloat", this.state?.showTool === 'image-replace');

            setTimeout(() => {
                emitter.emit('openFuncFloat', 'image-replace', true);
            }, 200);
        }
    }

    /**
     * 滤镜
     */
    setImageFilter(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e?.preventDefault();
        e?.stopPropagation();
        const { asset } = this.props;
        if (asset.meta?.type === 'image' || asset.meta?.type === 'pic') {
            this.showToolPanel(e, 'image-filter');
            emitter.emit('floatMenu.hideFloat', this.state?.showTool === 'image-filter');

            setTimeout(() => {
                emitter.emit('openFuncFloat', 'image-filter', this.state?.showTool === 'image-filter');
            }, 200);
        }
    }

    /**
     * 图片抠图
     */
    setImageCutout(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.preventDefault();
        e.stopPropagation();
        const { asset } = this.props;
        if (asset.meta?.type === 'image' || asset.meta?.type === 'pic') {
            if (this.state?.showTool === 'image-filter') {
                this.setImageFilter(null);
            }

            this.showToolPanel(e, 'image-cutout');
            emitter.emit('floatMenu.hideFloat', this.state?.showTool === 'image-cutout');

            setTimeout(() => {
                emitter.emit('openFuncFloat', 'image-cutout', this.state?.showTool === 'image-cutout');
            }, 400);
        }
    }

    //----------------------------------------------------------------
    // SVG

    /**
     * 填充颜色
     */
    setColorSVG(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.preventDefault();
        e.stopPropagation();
        const { asset } = this.props;
        if (asset?.meta?.type === 'SVG') {
            this.showToolPanel(e, 'svg-color');
            emitter.emit('floatMenu.hideFloat', this.state?.showTool === 'svg-color');

            setTimeout(() => {
                emitter.emit('openFuncFloat', 'svg-color');
            }, 200);
        }
    }

    //----------------------------------------------------------------
    // 相框 frame

    /**
     * 上传相片
     */
    setFrameReplace(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.preventDefault();
        e.stopPropagation();
        const { asset } = this.props;
        if (asset?.meta?.type === 'frame') {
            this.showToolPanel(e, 'frame-replace');
            // emitter.emit("floatMenu.hideFloat", this.state?.showTool === 'frame-replace');

            emitter.emit('UserUploadBoxUploadFile', 'doubleClick', asset);

            // setTimeout(() => {
            //     emitter.emit("openFuncFloat", "image-replace");
            // }, 200);
        }
    }

    /**
     * 填充颜色
     */
    setColorFrame(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.preventDefault();
        e.stopPropagation();
        const { asset } = this.props;
        if (asset?.meta?.type === 'frame') {
            this.showToolPanel(e, 'frame-color');
            emitter.emit('floatMenu.hideFloat', this.state?.showTool === 'frame-color');

            setTimeout(() => {
                emitter.emit('openFuncFloat', 'frame-color');
            }, 200);
        }
    }

    //----------------------------------------------------------------
    // 视频 videoE

    /**
     * 视频剪辑
     */
    setVideoClip(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.preventDefault();
        e.stopPropagation();
        const { asset } = this.props;
        if (asset?.meta?.type === 'videoE') {
            this.showToolPanel(e, 'video-clip');
            emitter.emit('floatMenu.hideFloat', this.state?.showTool === 'video-clip');

            setTimeout(() => {
                emitter.emit('openFuncFloat', 'video-clip');
            }, 200);
        }
    }

    /**
     * 视频音量
     */
    setVideoVolume(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.preventDefault();
        e.stopPropagation();
        const { asset } = this.props;
        if (asset?.meta?.type === 'videoE') {
            this.showToolPanel(e, 'video-volume');
            emitter.emit('floatMenu.hideFloat', this.state?.showTool === 'video-volume');

            setTimeout(() => {
                emitter.emit('openFuncFloat', 'video-volume');
            }, 200);
        }
    }

    //----------------------------------------------------------------
    // 表格 table

    /**
     * 边框
     */
    setTableBorder(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.preventDefault();
        e.stopPropagation();
        const { asset } = this.props;
        if (asset?.meta?.type === 'table') {
            this.showToolPanel(e, 'table-border');
        }
    }

    /**
     * 填充颜色
     */
    setColorTable(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.preventDefault();
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
        const { asset } = this.props;
        if (asset?.meta?.type === 'table') {
            this.showToolPanel(e, 'table-color');
            emitter.emit('floatMenu.hideFloat', this.state?.showTool === 'table-color');

            setTimeout(() => {
                emitter.emit('openFuncFloat', 'table-color');
            }, 200);
        }
    }

    //----------------------------------------------------------------
    // 图表 chart

    /**
     * 设置图表数据
     */
    setChartData(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.preventDefault();
        e.stopPropagation();
        const { asset } = this.props;
        if (asset?.meta?.type === 'chart') {
            this.showToolPanel(e, 'chart-data');
            emitter.emit('floatMenu.hideFloat', this.state?.showTool === 'chart-data');

            setTimeout(() => {
                emitter.emit('openFuncFloat', 'chart-data', this.state?.showTool === 'chart-data');
            }, 200);
        }
    }

    //----------------------------------------------------------------
    // background

    /**
     * 背景图片删除(若背景有图片)
     */
    setBgImageDelete(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.preventDefault();
        e.stopPropagation();
        const { asset } = this.props;
        if (!asset) {
            this.showToolPanel(e, 'bg-delete');
            // emitter.emit("floatMenu.hideFloat", this.state?.showTool === 'bg-replace');

            setTimeout(() => {
                emitter.emit('openFuncFloat', 'bg-delete', this.state?.showTool === 'bg-delete');
            }, 200);
        }
    }

    /**
     * 背景替换
     */
    setCanvasSize(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.preventDefault();
        e.stopPropagation();
        const { asset } = this.props;
        if (!asset) {
            this.showToolPanel(e, 'canvas-size');
            emitter.emit('floatMenu.hideFloat', this.state?.showTool === 'canvas-size');

            setTimeout(() => {
                emitter.emit('openFuncFloat', 'canvas-size', this.state?.showTool === 'canvas-size');
            }, 200);
        }
    }

    /**
     * 背景替换
     */
    setBgReplace(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.preventDefault();
        e.stopPropagation();
        const { asset } = this.props;
        if (!asset) {
            this.showToolPanel(e, 'bg-replace');
            // emitter.emit("floatMenu.hideFloat", this.state?.showTool === 'bg-replace');

            setTimeout(() => {
                emitter.emit('openFuncFloat', 'bg-replace', true);
            }, 200);
        }
    }

    /**
     * 填充颜色
     */
    setBgColor(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.preventDefault();
        e.stopPropagation();
        const { asset } = this.props;
        if (!asset) {
            this.showToolPanel(e, 'bg-color');
            emitter.emit('floatMenu.hideFloat', this.state?.showTool === 'bg-color');

            setTimeout(() => {
                emitter.emit('openFuncFloat', 'bg-color', this.state?.showTool === 'bg-color');
            }, 200);
        }
    }

    openRightFloatPanel(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.stopPropagation();
        if (this.state?.showTool !== '' && this.state?.showTool !== 'more') {
            emitter.emit('openFuncFloat', this.state?.showTool, false);
        }

        setTimeout(() => {
            this.setState({ showTool: 'more' });
            emitter.emit('floatMenu.hideFloat', 'more');
        }, 100);
    }

    /**
     * 更多触发右键功能
     */
    showRightKeyPlane(left: number, top: number, e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.stopPropagation();
        // this.onChangeCurrentPageIndex();
        // 再次点击关闭
        if (this.props.isShowMoreMenu) {
            this.props.hide();
            return;
        }
        const oCanvasLayout: HTMLDivElement = document.querySelector('.canvasLayout');
        const { canvas } = this.props;
        const { isFix } = this.state;
        const offsetLeft = oCanvasLayout?.offsetLeft || canvas.x;
        const cWidth = this.wrapRef.current.clientWidth || 200;
        const cHeight = this.wrapRef.current.clientHeight || 40;
        if (isFix) {
            left = left + cWidth - offsetLeft;
            top = top - 18 - cHeight;
        } else {
            left = left + canvas.x + cWidth;
            top = top + canvas.y;
        }
        // menu快捷菜单
        assetManager.setPv_new(4721, {
            additional: {
                s4: 'menu',
            },
        });
        this.props.handleMoreEvent({
            left,
            top,
            x: canvas.x,
            y: canvas.y,
            actionType: 'shortcutMenu',
        });
    }

    onCancelFix(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        this.wrapRef.current.style.position = 'absolute';
        e.stopPropagation();
        this.setState(
            {
                isFix: false,
            },
            () => this.setUserMenuPosition(),
        );
        this.clientX = null;
        this.clientY = null;
        assetManager.setPv_new(8147);
    }

    onHideFrameDelPlane(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.stopPropagation();
        this.setState({ isShowFramDelPanel: false });
    }

    /**获取旋转后的边界值 */
    getBoundsAfterRotate(rect: any, angle: number) {
        if (!angle) return rect;
        const { x, y, width, height } = rect;
        const radian = (angle * Math.PI) / 180;
        const cos = Math.abs(Math.cos(radian));
        const sin = Math.abs(Math.sin(radian));
        const newWidth = width * cos + height * sin;
        const newHeight = width * sin + height * cos;
        // 获取旋转后，元素四个点最小的x,y坐标
        const newX = x - (newWidth - width) / 2;
        const newY = y - (newHeight - height) / 2;
        // 角度在180附近15度
        const foldAngle = Math.abs(angle % 180)
        let rotateBtnHeight = 0;
        if (foldAngle < 15 || foldAngle > 165) {
            rotateBtnHeight = 30;
        } 
        return {
            x: newX,
            y: newY - rotateBtnHeight,
            width: newWidth,
            height: newHeight,
        };
        
    }

    /**
     * 点击删除图标
     */
    deleteIconClickEvent(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.preventDefault();
        e.stopPropagation();
        this.onChangeCurrentPageIndex();
        const { asset } = this.props;
        assetManager.setPv_new(2432, { additional: { s0: 'menu' } });
        if (asset?.meta?.type !== 'frame') {
            AssetLogic.deleteSelectAsset();
        } else {
            this.setState({
                isShowFramDelPanel: true,
            });
        }
    }

    onChangeCurrentPageIndex() {
        CanvasPaintedLogic.selectPageTemplate({ pageNumber: this.props.pageIndex });
        CanvasPaintedLogic.mutisizeSetFloorTemplateCurrentindex({
            floorTemplateIndex: this.props.pageIndex,
            clickTem: 'free-tool' as any,
        });
    }

    renderQuickMenu({quickMenuStyle, tipsPosition, isMulAseests, asset, isBackground, isBackgroundImage, showAiTextOption, showAiTextOptionWrap}: any) {
        return <AssetQuickFreeBar 
            quickMenuStyle={quickMenuStyle}
            tipsPosition={tipsPosition}
            isMulAseests={isMulAseests}
            asset={asset}
            isBackground={isBackground}
            isBackgroundImage={isBackgroundImage}
            showAiTextOption={showAiTextOption}
            showAiTextOptionWrap={showAiTextOptionWrap}
            pageIndex={this.props.pageIndex}
            hide={this.props.hide}
            toolPanel={this.props.toolPanel}
        />
    }

    render(): React.ReactNode {
        const {
            canvas,
            asset,
            pageIndex = 0,
            pagesTop,
            isSelectPage,
            isShow,
            toolPanel,
            renderMode,
            toolPanelWidth,
        } = this.props;

        // if (this.state.fixedToolPanel &&  !isShow) return null;
        const {
            isFix,
            isShowFramDelPanel,
            presetColorTableList,
            isShowRightMenu,
            fontSizeShow,
            showTool,
            showChooselinearGradient,
            showAiTextOptionWrap,
            fixedToolPanel
        } = this.state;
        let isLocked = false;
        let posYScale = 0;
        let posXScale = 0;
        let assetsHeight = 0;
        let assetsWidth = 0;
        let backgroundColor = undefined;
        let isBackgroundImage = false;
        let isBackground = false;
        let isMulAseests = false;
        let showTopTool = false; // 顶部菜单栏在右侧面板固定时，表格，图片不展示
        let showAiTextOption = false;
        let angle = 0;
        const quickMenuStyle = {
            left: 0,
            top: 0,
            display: 'none',
        };

        if (!asset) {
            if (toolPanel?.assets?.length > 0 && toolPanel?.assets[0]) {
                isMulAseests = true;
                const hasUndefined = toolPanel.assets.some((item) => !item);
                const hasTextAsset = toolPanel.assets.some((item) => item.meta.type === 'text');
                if (hasUndefined) return null;
                if (hasTextAsset) {
                    showAiTextOption = true;
                }
                const assetsInfo =
                    toolPanel.assetsInfo ||
                    SelectAsset.selectAssetsPositionCompute({
                        assets: toolPanel?.assets,
                        canvas: canvas,
                    });
                posYScale = assetsInfo.posY;
                posXScale = assetsInfo.posX;
                assetsHeight = assetsInfo.height;
                assetsWidth = assetsInfo.width;

                quickMenuStyle.display = 'flex';
            } else {
                // if (this.state.fixedToolPanel &&  !isSelectPage) return null;
                const { work, pageInfo } = storeAdapter.getStore({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                });

                isBackground = true;
                backgroundColor =
                    'rgba(' +
                    work.pages[pageInfo.pageNow].backgroundColor.r +
                    ', ' +
                    work.pages[pageInfo.pageNow].backgroundColor.g +
                    ', ' +
                    work.pages[pageInfo.pageNow].backgroundColor.b +
                    ',' +
                    work.pages[pageInfo.pageNow].backgroundColor.a +
                    ')';

                const pageAssets = work.pages[pageInfo.pageNow].assets;
                const backgroundStatus = pageAssets.find((item: any) => item.meta.type === 'background');
                if (backgroundStatus && backgroundStatus.attribute.picUrl !== 'loading') {
                    isBackgroundImage = true;
                    quickMenuStyle.display = 'flex';
                    assetsWidth = canvas.width * canvas.scale;
                } else {
                    if (fixedToolPanel) {
                        return null;
                    } else {
                        if (toolPanelWidth !== 0 && showTool === 'more') {
                            console.log('toolPanel !asset 不固定 toolPanelWidth !== 0');
                            return null;
                        }
                    }
                }
            }
        } else {
            if (asset && (asset.attribute?.container?.isEdit || asset.meta.isClip)) {
                return null;
            } else {
                posYScale = asset.transform.posY * canvas.scale;
                posXScale = asset.transform.posX * canvas.scale;
                assetsHeight = asset.attribute.height * canvas.scale;
                assetsWidth = asset.attribute.width * canvas.scale;
                angle = asset.transform.rotate;
            }
            isLocked = asset.meta?.locked ?? false;
        }

        let boxStyle = {};
        const top = renderMode === '' ? pagesTop[pageIndex] : pagesTop[0];
        let assetLeft = posXScale;
        let assetTop = posYScale + top - 58;
        let popStyle = {};

        const {x, y: initialY, width: newWidth, height: newHeight} = this.getBoundsAfterRotate({x: assetLeft, y: posYScale, width:assetsWidth, height: assetsHeight}, angle);
        let y = initialY + top - 58;
        if (y + canvas.y < 40 + 50) {
            y = y + newHeight + 105;
        }
        quickMenuStyle.left = canvas.x + x + newWidth / 2;
        quickMenuStyle.top = canvas.y + y;

        let fontFamilyClass = null;
        let fontWeightClass = undefined;
        let fontStyleClass = undefined;
        let textDecorationClass = undefined;
        let textDecorationClass2 = undefined;
        let letterSpacing = undefined;
        let fontSize = undefined;
        let svgColor = undefined;
        let frameContentColor = undefined;
        let colorTable: any[] = [];

        let fontSizeInput = 0;
        if (asset) {
            quickMenuStyle.display = 'flex';
            if (asset.meta?.type === 'text') {
                fontFamilyClass = asset.attribute?.fontFamily;
                showAiTextOption = true;
                showTopTool = true;
                const selctFontSize = this.getTextProperty('fontSize', asset.attribute.fontSize);
                fontSizeInput = parseInt(selctFontSize);

                if (this.getTextProperty('fontWeight', asset.attribute.fontWeight) == 'bold') {
                    fontWeightClass = 'active';
                }

                if (this.getTextProperty('fontStyle', asset.attribute.fontStyle) == 'italic') {
                    fontStyleClass = 'active';
                }

                if (asset.meta.v == 3) {
                    const isUnderline = this.getTextProperty('underline', false);
                    textDecorationClass = isUnderline ? 'active' : '';
                    const isLineThrough = this.getTextProperty('lineThrough', false);
                    textDecorationClass2 = isLineThrough ? 'active' : '';
                } else {
                    if (asset.attribute.textDecoration == 'underline') {
                        textDecorationClass = 'active';
                    }
                    if (asset.attribute.textDecoration == 'line-through') {
                        textDecorationClass2 = 'active';
                    }
                }

                letterSpacing = this.getTextProperty('letterSpacing', asset.attribute.letterSpacing);
                fontSize = this.getTextProperty('fontSize', asset.attribute.fontSize);
                //按字号大小决定字间距实际范围
                letterSpacing = Math.round((letterSpacing * 100) / (fontSize || 1));
                if (letterSpacing > 100) {
                    letterSpacing = 100;
                }
                if (letterSpacing < -50) {
                    letterSpacing = -50;
                }

                letterSpacing = ((letterSpacing - -50) * 150) / (100 - -50);

                letterSpacing = letterSpacing + -50;
            } else if (asset.meta?.type === 'SVG') {
                showTopTool = true;
                if (asset.attribute.textAttr?.text?.length) {
                    showAiTextOption = true;
                }
                const colorsArr = [];
                // eslint-disable-next-line prefer-const
                for (let item in asset.attribute.colors) {
                    colorsArr.push([item, asset.attribute.colors[item]]);
                }

                if (colorsArr.length <= 0 || !colorsArr[0] || (!colorsArr[0].length && colorsArr[0]?.length < 0)) {
                    return null;
                }

                svgColor = colorsArr[0][0];
                if (colorsArr[0][1] != '') {
                    svgColor =
                        'rgba(' +
                        colorsArr[0][1].r +
                        ',' +
                        colorsArr[0][1].g +
                        ',' +
                        colorsArr[0][1].b +
                        ',' +
                        colorsArr[0][1].a +
                        ')';
                }
            } else if (asset.meta?.type === 'frame') {
                const item = asset.attribute?.color;
                frameContentColor = 'rgba(' + item?.r + ',' + item?.g + ',' + item?.b + ',' + item?.a + ')';
            } else if (asset.meta?.type === 'table') {
                showTopTool = true;
                let tableId = asset.attribute.resId;

                if (!tableId) {
                    tableId = 3;
                }

                let newColorTable2: any[] = [];
                let newColorTable1: any[] = [];

                for (let t = 0; t < presetColorTableList.length; t++) {
                    if (presetColorTableList[t].id == tableId) {
                        newColorTable2 = presetColorTableList[t].color_table;
                        for (let i = 0; i < newColorTable2.length; i++) {
                            if (newColorTable2[i].id == 1) {
                                newColorTable1 = newColorTable2[i].color_table;
                            }
                        }
                    }
                }

                colorTable = newColorTable1;
            } else if (asset.meta?.type === 'image' || asset.meta?.type === 'pic' || asset?.meta?.type === 'group') {
                showTopTool = true;
            }
        }

        // 对齐方式选中样式
        const isTextAlignActive = (target: string) => {
            // console.log('jyjin align: ', this.props, nowAsset, this.multipleSelectFontStatus.textAlign)
            return target === asset.attribute.textAlign ? 'active' : '';
        };

        let tipsPosition: 'top' | 'left' | 'bottom' | 'right' = 'top';
        if (!isFix) {
            if (renderMode === 'board') {
                boxStyle = { top: 58, left: '50%', transform: 'translateX(-50%)' };
            } else if (this.wrapRef.current) {
                const boxLeftPosition =
                    canvas.x + (canvas.width * canvas.scale) / 2 - this.wrapRef.current.clientWidth / 2;
                boxStyle = { top: 58, left: boxLeftPosition };
            } else {
                boxStyle = { top: 58, left: canvas.x };
            }

            popStyle = { top: 45, left: 50 };
            // tipsPosition = canvas.y < 110 ? 'bottom' : 'top';
            tipsPosition = 'bottom';
        } else {
            assetTop = this.clientY;
            assetLeft = this.clientX;
            boxStyle = { top: this.clientY, left: this.clientX, position: 'fixed' };
            popStyle = { top: assetTop > 140 ? -82 : 45, left: 90 };
            tipsPosition = this.clientY < 110 ? 'bottom' : 'top';
        }

        if (fixedToolPanel && ( showTopTool || isMulAseests) && !isAIDesign()) {
            if (!isShow && (showTopTool || isMulAseests)) {
                return null;
            }
            return this.renderQuickMenu({
                quickMenuStyle,
                tipsPosition,
                isMulAseests,
                asset,
                isBackground,
                isBackgroundImage,
                showAiTextOption, 
                showAiTextOptionWrap
            });
        }

        return (
            <>
                <div
                    className={`frame-edit-wrap ${renderMode === 'board' && !asset ? 'hide' : ''}`}
                    ref={this.wrapRef}
                    style={boxStyle}
                    onWheel={(e) => {
                        e.stopPropagation();
                    }}
                >
                    {isShowFramDelPanel && (
                        <ClickOutside onClickOutside={this.onHideFrameDelPlane.bind(this)}>
                            <div className="frame-delete-popover" style={popStyle}>
                                <AssetDeleteWrap />
                            </div>
                        </ClickOutside>
                    )}
                    <div className="frame-edit-toolbox">
                        <ToolTip
                            position={tipsPosition}
                            trigger={
                                <i
                                    className="iconfont icon-tuodong"
                                    ref={this.dragRef}
                                    onMouseDown={(e) => {
                                        this.onDragBtnMouseDown(e);
                                        assetManager.setPv_new(8518, {
                                            additional: { s0: asset ? asset.meta?.type : 'background' },
                                        });
                                    }}
                                ></i>
                            }
                            triggerType="hover"
                            content="拖拽固定菜单"
                        ></ToolTip>
                        {isFix && (
                            <ToolTip
                                position={tipsPosition}
                                trigger={
                                    <i
                                        className="iconfont icon-guding active"
                                        onMouseDown={(e) => this.onCancelFix(e)}
                                    ></i>
                                }
                                triggerType="hover"
                                content="取消固定"
                            ></ToolTip>
                        )}
                        {isLocked ? (
                            <>
                                <div className="splitLine"></div>
                                <ToolTip
                                    position={tipsPosition}
                                    trigger={
                                        <i
                                            className="iconfont icon-suoding3"
                                            onMouseDown={(e) => this.unLockAsset(e)}
                                        ></i>
                                    }
                                    triggerType="hover"
                                    content="解锁"
                                ></ToolTip>
                            </>
                        ) : (
                            <>
                                {/* <div className="splitLine"></div> */}
                                <ToolTip
                                    position={tipsPosition}
                                    trigger={<TagSelectBox hide={this.props.hide} />}
                                    triggerType="hover"
                                    content={void 0}
                                ></ToolTip>
                                <ToolTip
                                    position={tipsPosition}
                                    trigger={<ColorTagSelectBox hide={this.props.hide} />}
                                    triggerType="hover"
                                    content={void 0}
                                ></ToolTip>
                                { isBackgroundImage &&
                                    <>
                                        <div className="splitLine"></div>
                                        <ToolTip
                                            position={tipsPosition}
                                            trigger={<i className="iconfont icon-bianjiqi-xinhuabu-shanchu" onMouseDown={(e) => {
                                                if (!isBackground) {
                                                    this.deleteIconClickEvent(e)
                                                } else {
                                                    this.setBgImageDelete(e);
                                                }
                                                assetManager.setPv_new(8520, { additional: { s0: asset ? asset.meta?.type : 'background' } });
                                            }}></i>}
                                            triggerType="hover"
                                            content="删除"
                                        ></ToolTip>
                                    </>
                                }

                                {!this.state.fixedToolPanel && (toolPanelWidth === 0 || showTool !== 'more') && !isMulAseests&& (
                                    <div className="splitLine"></div>
                                )}

                                {!this.state.fixedToolPanel &&
                                    (toolPanelWidth === 0 || showTool !== 'more') &&
                                    asset &&
                                    asset.meta?.type === 'text' && (
                                        <>
                                            <ToolTip
                                                position={tipsPosition}
                                                trigger={
                                                    <div
                                                        className="font-family"
                                                        style={{
                                                            backgroundImage: `url(//s.tuguaishou.com/index_img/fonts/${fontFamilyClass}.svg?t=1547525790;)`,
                                                        }}
                                                        onMouseDown={(e) => {
                                                            this.setFontFamily(e);
                                                            assetManager.setPv_new(8498);
                                                        }}
                                                    ></div>
                                                }
                                                triggerType="hover"
                                                content="字体"
                                            />
                                            <ToolTip
                                                position={tipsPosition}
                                                trigger={
                                                    <div
                                                        className="font-size"
                                                        onMouseDown={(e) => {
                                                            e.stopPropagation();
                                                            e.nativeEvent.stopPropagation();
                                                            this.setState({ fontSizeShow: !this.state.fontSizeShow });
                                                        }}
                                                    >
                                                        <i
                                                            style={{ width: '14px', height: '14px' }}
                                                            className="iconfont icon-jianhao"
                                                            onMouseDown={(e) => {
                                                                e.stopPropagation();
                                                                e.nativeEvent.stopPropagation();
                                                                assetManager.setPv_new(8567);
                                                                this.fontSizeBtnMouseDownEvent(
                                                                    e,
                                                                    'less',
                                                                    fontSizeInput,
                                                                );
                                                            }}
                                                        ></i>
                                                        <input
                                                            type="text"
                                                            className="fontSizeSelectInput"
                                                            value={fontSizeInput}
                                                            onChange={(e) => {
                                                                e.stopPropagation();
                                                                e.nativeEvent.stopPropagation();
                                                                assetManager.setPv_new(8568);
                                                                this.fontSizeInputChangeEvent(e, asset);
                                                            }}
                                                            onWheel={(e) => {
                                                                e.stopPropagation();
                                                                e.nativeEvent.stopPropagation();
                                                                this.fontSizeInputScrollEvent(e, asset);
                                                            }}
                                                            onFocus={(e) => {
                                                                const dom = e.target;
                                                                dom.select && dom.select();
                                                            }}
                                                            onKeyDown={(e) => {
                                                                // 阻止冒泡
                                                                e.stopPropagation();
                                                                e.nativeEvent.stopPropagation();
                                                            }}
                                                        />
                                                        <i
                                                            style={{ width: '14px', height: '14px' }}
                                                            className="iconfont icon-jiahao"
                                                            onMouseDown={(e) => {
                                                                e.stopPropagation();
                                                                e.nativeEvent.stopPropagation();
                                                                assetManager.setPv_new(8566);
                                                                this.fontSizeBtnMouseDownEvent(
                                                                    e,
                                                                    'plus',
                                                                    fontSizeInput,
                                                                );
                                                            }}
                                                        ></i>
                                                        {fontSizeShow && (
                                                            <div
                                                                className="fontsizeListArea"
                                                                onMouseLeave={(e) => {
                                                                    e.stopPropagation();
                                                                    e.nativeEvent.stopPropagation();
                                                                    this.fontListAreaMouseLeave();
                                                                }}
                                                            >
                                                                <ul
                                                                    className="fontsizeList"
                                                                    onWheel={(e) => {
                                                                        e.stopPropagation();
                                                                        e.nativeEvent.stopPropagation();
                                                                    }}
                                                                >
                                                                    {this.getFontSizeAllCharacters(fontSizeInput)}
                                                                </ul>
                                                            </div>
                                                        )}
                                                    </div>
                                                }
                                                triggerType="hover"
                                                content="字号"
                                            />
                                            <div className="splitLine"></div>
                                            <ToolTip
                                                position={tipsPosition}
                                                trigger={
                                                    <div
                                                        className={
                                                            showTool === 'effect' ? 'effect-text active' : 'effect-text'
                                                        }
                                                        onMouseDown={(e) => {
                                                            this.setEffectText(e);
                                                            assetManager.setPv_new(8497);
                                                            // assetManager.setPv_new(2432, { additional: { s0: 'menu' } });
                                                        }}
                                                    >
                                                        特效
                                                    </div>
                                                }
                                                triggerType="hover"
                                                content="特效"
                                            />
                                            <div className="splitLine"></div>
                                            <ToolTip
                                                position={tipsPosition}
                                                trigger={
                                                    <div className="font-color">
                                                        {!asset.attribute.effect && (
                                                            <i
                                                                className="iconfont font-weight"
                                                                style={this.getChoiceBackgroundColor(
                                                                    this.getTextProperty(
                                                                        'color',
                                                                        asset.attribute.color,
                                                                    ),
                                                                )}
                                                                onMouseDown={(e) => {
                                                                    this.setFontColor(e, 'common', -1);
                                                                    assetManager.setPv_new(8499);
                                                                }}
                                                            ></i>
                                                        )}

                                                        {asset.attribute.effect && (
                                                            <div
                                                                style={{
                                                                    display: 'flex',
                                                                    alignItems: 'center',
                                                                    gap: '1px',
                                                                    border: '1px solid #E9E8E8',
                                                                    borderRadius: '4px',
                                                                }}
                                                            >
                                                                {asset.attribute.effectVariant.variableColorPara
                                                                    ?.length > 0 &&
                                                                    !asset.attribute.effectVariant.rt_linearGradient &&
                                                                    asset.attribute.effectVariant.variableColorPara.map(
                                                                        (item: any, index: any, arr: any) => {
                                                                            const choiceColorBtnStyle = {
                                                                                backgroundColor:
                                                                                    'rgba(' +
                                                                                    item.colorBlock.r +
                                                                                    ',' +
                                                                                    item.colorBlock.g +
                                                                                    ',' +
                                                                                    item.colorBlock.b +
                                                                                    ',' +
                                                                                    item.colorBlock.a +
                                                                                    ')',
                                                                                width: '20px',
                                                                                height: '20px',
                                                                                borderTopLeftRadius:
                                                                                    index === 0 ? '4px' : undefined,
                                                                                borderBottomLeftRadius:
                                                                                    index === 0 ? '4px' : undefined,
                                                                                borderTopRightRadius:
                                                                                    index === arr.length - 1
                                                                                        ? '4px'
                                                                                        : undefined,
                                                                                borderBottomRightRadius:
                                                                                    index === arr.length - 1
                                                                                        ? '4px'
                                                                                        : undefined,
                                                                            };
                                                                            return (
                                                                                <div
                                                                                    key={index}
                                                                                    className="choiceColorBtn"
                                                                                    style={choiceColorBtnStyle}
                                                                                    onMouseDown={(e) => {
                                                                                        this.setFontColor(
                                                                                            e,
                                                                                            'effect',
                                                                                            index,
                                                                                        );
                                                                                        assetManager.setPv_new(8499, {
                                                                                            additional: {
                                                                                                s0: 'effect',
                                                                                            },
                                                                                        });
                                                                                    }}
                                                                                />
                                                                            );
                                                                        },
                                                                    )}
                                                                {asset.attribute.effectVariant.variableColorPara
                                                                    ?.length > 0 &&
                                                                    !asset.attribute.effectVariant.rt_linearGradient &&
                                                                    asset.attribute.effectVariant.variableColorPara
                                                                        .length === 3 && (
                                                                        <div
                                                                            style={{
                                                                                width: '20px',
                                                                                height: '20px',
                                                                                position: 'absolute',
                                                                                left: '54.5px',
                                                                                fontSize: '20px',
                                                                                lineHeight: '10px',
                                                                            }}
                                                                        >
                                                                            ...
                                                                        </div>
                                                                    )}
                                                                {
                                                                    /* 如果是渐变效果 */
                                                                    asset.attribute.effectVariant.rt_linearGradient &&
                                                                        (() => {
                                                                            const rt_linearGradient =
                                                                                asset.attribute.effectVariant
                                                                                    .rt_linearGradient;
                                                                            // console.log(rt_linearGradient)
                                                                            const angle = rt_linearGradient.angle;
                                                                            const colorArray = [];
                                                                            for (const i of rt_linearGradient.colors) {
                                                                                colorArray.push(
                                                                                    `rgba(${i.color.r},${i.color.g},${i.color.b},${i.color.a}) ${i.position * 100}%`,
                                                                                );
                                                                            }
                                                                            const choiceColorBtnStyle = {
                                                                                backgroundImage: `linear-gradient(${angle}deg, ${colorArray.join(',')})`,
                                                                                width: '20px',
                                                                                height: '20px',
                                                                            };
                                                                            const color_end =
                                                                                rt_linearGradient.colors[
                                                                                    rt_linearGradient.colors.length - 1
                                                                                ].color;
                                                                            return (
                                                                                <div
                                                                                    style={{
                                                                                        borderRadius: 5,
                                                                                        width: '20px',
                                                                                        height: '20px',
                                                                                    }}
                                                                                    onMouseDown={(e) =>
                                                                                        this.linearGradientBtnClickEvent(
                                                                                            e,
                                                                                        )
                                                                                    }
                                                                                >
                                                                                    <div
                                                                                        className="choiceColorBtn"
                                                                                        style={choiceColorBtnStyle}
                                                                                    ></div>
                                                                                    {showChooselinearGradient && (
                                                                                        <div className="multChoiceColorPanel">
                                                                                            <MoreColorsPanel
                                                                                                onClose={
                                                                                                    this.closeMoreColors
                                                                                                }
                                                                                                color={{
                                                                                                    color: rt_linearGradient
                                                                                                        .colors[0]
                                                                                                        .color,
                                                                                                    color_end,
                                                                                                }}
                                                                                                angle={angle}
                                                                                                onChange={(
                                                                                                    color: any,
                                                                                                ) => {
                                                                                                    // {"type":"gradient_color","center":0.5,"color_end":{"r":255,"g":178,"b":0,"a":1}}
                                                                                                    this.chooseLinearGradientColor(
                                                                                                        {
                                                                                                            type: 'gradient_color',
                                                                                                            center: 0.5,
                                                                                                            ...color,
                                                                                                        },
                                                                                                    );
                                                                                                }}
                                                                                            />
                                                                                        </div>
                                                                                    )}
                                                                                </div>
                                                                            );
                                                                        })()
                                                                }
                                                            </div>
                                                        )}
                                                    </div>
                                                }
                                                triggerType="hover"
                                                content="颜色"
                                            />
                                            <div className="splitLine"></div>
                                            {/* <ToolTip
                                            position={tipsPosition}
                                            trigger={
                                                <div style={{height: '30px', width: '40px'}}>
                                                    <i className="iconfont icon-zitijiacu font-weight" onMouseDown={(e) => this.showToolPanel(e, 'font-weight')}></i>
                                                {
                                                    showTool === 'font-weight' && 
                                                    <div className="float-tool">
                                                        <i className={'iconfont icon-zitijiacu font-weight ' + fontWeightClass} onMouseDown={(e) => this.setFontWeight(e, "font-weight")}></i>
                                                        <i className={'iconfont icon-zitiqingxie font-weight ' + fontStyleClass} onMouseDown={(e) => this.setFontWeight(e, "font-italic")}></i>
                                                        <i className={'iconfont icon-zitixiahuaxian font-weight ' + textDecorationClass} onMouseDown={(e) => this.setFontWeight(e, "font-bottom-line")}></i>
                                                        <i className={'iconfont icon-zitishanchuxian font-weight ' + textDecorationClass2} onMouseDown={(e) => this.setFontWeight(e, "font-delete-line")}></i>
                                                    </div>
                                                }
                                                </div>
                                            }
                                            triggerType="hover"
                                            content="粗体"
                                        /> */}

                                            <ToolTip
                                                position={tipsPosition}
                                                trigger={
                                                    <div className="textProperty">
                                                        <i
                                                            className={
                                                                'iconfont icon-zitijiacu ' + fontWeightClass
                                                            }
                                                            onMouseDown={(e) => {
                                                                e.stopPropagation();
                                                                e.nativeEvent.stopPropagation();
                                                                this.setFontWeight(e, 'font-weight');
                                                            }}
                                                        ></i>
                                                    </div>
                                                }
                                                triggerType="hover"
                                                content="粗体"
                                            />

                                            <ToolTip
                                                position={tipsPosition}
                                                trigger={
                                                    <div className="textProperty">
                                                        <i
                                                            className={
                                                                'iconfont icon-zitiqingxie ' +
                                                                fontStyleClass
                                                            }
                                                            onMouseDown={(e) => {
                                                                e.stopPropagation();
                                                                e.nativeEvent.stopPropagation();
                                                                this.setFontWeight(e, 'font-italic');
                                                            }}
                                                        ></i>
                                                    </div>
                                                }
                                                triggerType="hover"
                                                content="斜体"
                                            />

                                            <ToolTip
                                                position={tipsPosition}
                                                trigger={
                                                    <div className="textProperty">
                                                        <i
                                                            className="iconfont icon-juzhong-copy "
                                                            onMouseDown={(e) => {
                                                                this.showToolPanel(e, 'font-align');
                                                                assetManager.setPv_new(8500);
                                                            }}
                                                        ></i>
                                                        {showTool === 'font-align' && (
                                                            <div className="float-align-tool">
                                                                <div
                                                                    className={'item ' + isTextAlignActive('left')}
                                                                    onMouseDown={(e) => this.setFontAlign(e, 'left')}
                                                                >
                                                                    <i className="iconfont icon-zuoduiqi1 font-weight"></i>
                                                                    <p>左对齐</p>
                                                                </div>
                                                                <div
                                                                    className={'item ' + isTextAlignActive('center')}
                                                                    onMouseDown={(e) => this.setFontAlign(e, 'center')}
                                                                >
                                                                    <i className="iconfont icon-juzhong-copy font-weight"></i>
                                                                    <p>居中对齐</p>
                                                                </div>
                                                                <div
                                                                    className={'item ' + isTextAlignActive('right')}
                                                                    onMouseDown={(e) => this.setFontAlign(e, 'right')}
                                                                >
                                                                    <i className="iconfont icon-youduiqi font-weight"></i>
                                                                    <p>右对齐</p>
                                                                </div>
                                                                <div
                                                                    className={'item ' + isTextAlignActive('justify')}
                                                                    onMouseDown={(e) => this.setFontAlign(e, 'justify')}
                                                                >
                                                                    <i className="iconfont icon-liangduanduiqi font-weight"></i>
                                                                    <p>两端对齐</p>
                                                                </div>
                                                                <div
                                                                    className={'item ' + isTextAlignActive('disperse')}
                                                                    onMouseDown={(e) =>
                                                                        this.setFontAlign(e, 'disperse')
                                                                    }
                                                                >
                                                                    <i className="iconfont icon-fensanduiqi font-weight"></i>
                                                                    <p>分散对齐</p>
                                                                </div>
                                                            </div>
                                                        )}
                                                    </div>
                                                }
                                                triggerType="hover"
                                                content="对齐"
                                            />

                                            <ToolTip
                                                position={tipsPosition}
                                                trigger={
                                                    <div className="textProperty">
                                                        <i
                                                            className="iconfont icon-fensanduiqi"
                                                            onMouseDown={(e) => {
                                                                this.showToolPanel(e, 'font-space');
                                                                assetManager.setPv_new(8501);
                                                            }}
                                                        ></i>
                                                        {showTool === 'font-space' && (
                                                            <div className="float-space-tool">
                                                                <SliderItem
                                                                    label="字间距"
                                                                    value={letterSpacing}
                                                                    onChange={(value: any) =>
                                                                        this.setFontSpace(
                                                                            'letterSpacing',
                                                                            value,
                                                                            asset.attribute.fontSize,
                                                                        )
                                                                    }
                                                                    direction={'horizontal'}
                                                                    range={[-50, 100]}
                                                                    sliderWidth={145}
                                                                    showInput={true}
                                                                    height={48}
                                                                    step={1}
                                                                    adjust
                                                                />
                                                                <SliderItem
                                                                    label="行间距"
                                                                    value={asset.attribute.lineHeight}
                                                                    onChange={(value: any) =>
                                                                        this.setFontSpace('lineHeight', value)
                                                                    }
                                                                    range={[0, 100]}
                                                                    sliderWidth={145}
                                                                    showInput={true}
                                                                    height={48}
                                                                    adjust
                                                                />
                                                            </div>
                                                        )}
                                                    </div>
                                                }
                                                triggerType="hover"
                                                content="间距"
                                            />
                                            {/* <div className="splitLine"></div> */}
                                            {/* <ToolTip
                                            position="top"
                                            trigger={
                                                <div style={{height: '30px', width: '40px'}}>
                                                    <i className="iconfont icon-tuceng2 font-weight" onMouseOver={(e) => this.setFontLayer(e)}></i>
                                                {
                                                    showTool === 'font-layer' && 
                                                    <div className="float-layer-tool">
                                                        <LayerTool />
                                                    </div>
                                                }
                                                </div>
                                            }
                                            triggerType="hover"
                                            content="图层"
                                        />
                                        <div className="splitLine"></div> */}
                                        </>
                                    )}
                                {!this.state.fixedToolPanel &&
                                    (toolPanelWidth === 0 || showTool !== 'more') &&
                                    asset &&
                                    (asset.meta?.type === 'image' || asset.meta?.type === 'pic') && (
                                        <>
                                            <ToolTip
                                                position={tipsPosition}
                                                trigger={
                                                    <div
                                                        className="effect-text"
                                                        onMouseDown={(e) => {
                                                            this.setImageReplace(e);
                                                            assetManager.setPv_new(8503);
                                                        }}
                                                    >
                                                        替换
                                                    </div>
                                                }
                                                triggerType="hover"
                                                content="图片替换"
                                            />
                                            <div className="splitLine"></div>
                                            <ToolTip
                                                position={tipsPosition}
                                                trigger={
                                                    <div
                                                        className={
                                                            showTool === 'image-filter'
                                                                ? 'effect-text active'
                                                                : 'effect-text'
                                                        }
                                                        onMouseDown={(e) => {
                                                            this.setImageFilter(e);
                                                            assetManager.setPv_new(8502);
                                                        }}
                                                    >
                                                        滤镜
                                                    </div>
                                                }
                                                triggerType="hover"
                                                content="滤镜"
                                            />
                                            <div className="splitLine"></div>
                                            <ToolTip
                                                position={tipsPosition}
                                                trigger={
                                                    <div
                                                        className={
                                                            showTool === 'image-cutout'
                                                                ? 'effect-text active'
                                                                : 'effect-text'
                                                        }
                                                        onMouseDown={(e) => {
                                                            this.setImageCutout(e);
                                                            assetManager.setPv_new(8504);
                                                        }}
                                                    >
                                                        抠图
                                                    </div>
                                                }
                                                triggerType="hover"
                                                content="抠图"
                                            />
                                            {/* <div className="splitLine"></div> */}
                                        </>
                                    )}
                                {!this.state.fixedToolPanel &&
                                    (toolPanelWidth === 0 || showTool !== 'more') &&
                                    asset &&
                                    asset.meta?.type === 'SVG' && (
                                        <>
                                            <ToolTip
                                                position={tipsPosition}
                                                trigger={
                                                    <div className="svg-color">
                                                        <i
                                                            className="iconfont color-block"
                                                            style={{ background: svgColor }}
                                                            onMouseDown={(e) => {
                                                                this.setColorSVG(e);
                                                                assetManager.setPv_new(8505);
                                                            }}
                                                        ></i>
                                                    </div>
                                                }
                                                triggerType="hover"
                                                content="填充颜色"
                                            />
                                            {/* <div className="splitLine"></div> */}
                                        </>
                                    )}
                                {!this.state.fixedToolPanel &&
                                    (toolPanelWidth === 0 || showTool !== 'more') &&
                                    asset &&
                                    asset.meta?.type === 'frame' && (
                                        <>
                                            <ToolTip
                                                position={tipsPosition}
                                                trigger={
                                                    <div
                                                        className="effect-text"
                                                        onMouseDown={(e) => {
                                                            this.setFrameReplace(e);
                                                            assetManager.setPv_new(8506);
                                                        }}
                                                    >
                                                        替换
                                                    </div>
                                                }
                                                triggerType="hover"
                                                content="上传照片"
                                            />
                                            <div className="splitLine"></div>
                                            <ToolTip
                                                position={tipsPosition}
                                                trigger={
                                                    <div className="svg-color">
                                                        <i
                                                            className="iconfont color-block"
                                                            style={{ background: frameContentColor }}
                                                            onMouseDown={(e) => {
                                                                this.setColorFrame(e);
                                                                assetManager.setPv_new(8507);
                                                            }}
                                                        ></i>
                                                    </div>
                                                }
                                                triggerType="hover"
                                                content="填充颜色"
                                            />
                                            <div className="splitLine"></div>
                                        </>
                                    )}
                                {!this.state.fixedToolPanel &&
                                    (toolPanelWidth === 0 || showTool !== 'more') &&
                                    asset &&
                                    asset.meta?.type === 'videoE' && (
                                        <>
                                            <ToolTip
                                                position={tipsPosition}
                                                trigger={
                                                    <div
                                                        className={
                                                            showTool === 'video-clip'
                                                                ? 'effect-text active'
                                                                : 'effect-text'
                                                        }
                                                        onMouseDown={(e) => {
                                                            this.setVideoClip(e);
                                                            assetManager.setPv_new(8508);
                                                        }}
                                                    >
                                                        剪辑
                                                    </div>
                                                }
                                                triggerType="hover"
                                                content="剪辑"
                                            />
                                            <div className="splitLine"></div>
                                            <ToolTip
                                                position={tipsPosition}
                                                trigger={
                                                    <div
                                                        className={
                                                            showTool === 'video-volume'
                                                                ? 'effect-text active'
                                                                : 'effect-text'
                                                        }
                                                        onMouseDown={(e) => {
                                                            this.setVideoVolume(e);
                                                            assetManager.setPv_new(8509);
                                                        }}
                                                    >
                                                        音频
                                                    </div>
                                                }
                                                triggerType="hover"
                                                content="音频"
                                            />
                                            <div className="splitLine"></div>
                                        </>
                                    )}
                                {!this.state.fixedToolPanel &&
                                    (toolPanelWidth === 0 || showTool !== 'more') &&
                                    asset &&
                                    asset.meta?.type === 'table' && (
                                        <>
                                            <ToolTip
                                                position={tipsPosition}
                                                trigger={
                                                    <div className="svg-color">
                                                        <i
                                                            className="iconfont icon-tianchong font-weight"
                                                            onMouseDown={(e) => {
                                                                this.setColorTable(e);
                                                                assetManager.setPv_new(8511);
                                                            }}
                                                        ></i>
                                                    </div>
                                                }
                                                triggerType="hover"
                                                content="填充颜色"
                                            />
                                            <div className="splitLine"></div>
                                            <ToolTip
                                                position={tipsPosition}
                                                trigger={
                                                    <div
                                                        className="table-border"
                                                        // style={{ height: '30px', width: '30px' }}
                                                    >
                                                        <i
                                                            className="iconfont icon-biankuang1 font-weight"
                                                            onMouseDown={(e) => {
                                                                this.setTableBorder(e);
                                                                assetManager.setPv_new(8512);
                                                            }}
                                                        ></i>
                                                        {showTool === 'table-border' && (
                                                            <TableBorderStylePanel
                                                                color={colorTable[1]}
                                                                onChange={(e: any) => {
                                                                    e.preventDefault();
                                                                    e.stopPropagation();

                                                                    emitter.emit('openFuncFloat', 'table-border');
                                                                }}
                                                                style={{
                                                                    position: 'relative',
                                                                    left: '-100px',
                                                                    top: '10px',
                                                                    zIndex: 99,
                                                                }}
                                                            />
                                                        )}
                                                    </div>
                                                }
                                                triggerType="hover"
                                                content="边框"
                                            />
                                            {/* <div className="splitLine"></div> */}
                                        </>
                                    )}
                                {!this.state.fixedToolPanel &&
                                    (toolPanelWidth === 0 || showTool !== 'more') &&
                                    asset &&
                                    asset.meta?.type === 'chart' && (
                                        <>
                                            <ToolTip
                                                position={tipsPosition}
                                                trigger={
                                                    <div
                                                        className={
                                                            showTool === 'chart-data'
                                                                ? 'effect-text active'
                                                                : 'effect-text'
                                                        }
                                                        onMouseDown={(e) => {
                                                            this.setChartData(e);
                                                            assetManager.setPv_new(8513);
                                                        }}
                                                    >
                                                        编辑
                                                    </div>
                                                }
                                                triggerType="hover"
                                                content="编辑数据"
                                            />
                                            <div className="splitLine"></div>
                                        </>
                                    )}
                                {!this.state.fixedToolPanel && !asset && !isMulAseests && renderMode !== 'board' && (
                                    <>
                                        <ToolTip
                                            position={tipsPosition}
                                            trigger={
                                                <div
                                                    className="icon-text"
                                                    onMouseDown={(e) => {
                                                        this.setCanvasSize(e);
                                                        assetManager.setPv_new(8517);
                                                    }}
                                                >
                                                    <i className="iconfont icon icon-a-tiaozhengchicun1"></i>
                                                    <div className="text">调整尺寸</div>
                                                </div>
                                            }
                                            triggerType="click"
                                            content="调整尺寸"
                                        />
                                        <div className="splitLine"></div>
                                        <ToolTip
                                            position={tipsPosition}
                                            trigger={
                                                <div
                                                    className="icon-text"
                                                    onMouseDown={(e) => {
                                                        this.setBgReplace(e);
                                                        if (isBackgroundImage) {
                                                            assetManager.setPv_new(8515);
                                                        } else {
                                                            assetManager.setPv_new(8514);
                                                        }
                                                    }}
                                                >
                                                    <i
                                                        className={`iconfont icon ${isBackgroundImage ? 'icon-xuanfu-tupiantihuan' : 'icon-xuanfu-shangchuanbeijing'}`}
                                                    ></i>
                                                    <div className="text">
                                                        {isBackgroundImage ? '替换背景' : '上传背景'}
                                                    </div>
                                                </div>
                                            }
                                            triggerType="hover"
                                            content="背景替换"
                                        />
                                        <div className="splitLine"></div>
                                        <ToolTip
                                            position={tipsPosition}
                                            trigger={
                                                <div className="svg-color">
                                                    <i
                                                        className="iconfont color-block"
                                                        style={{ background: backgroundColor }}
                                                        onMouseDown={(e) => {
                                                            this.setBgColor(e);
                                                            assetManager.setPv_new(8516);
                                                        }}
                                                    ></i>
                                                </div>
                                            }
                                            triggerType="hover"
                                            content="背景颜色"
                                        />
                                    </>
                                )}
 
                                {!this.state.fixedToolPanel && (toolPanelWidth === 0 || showTool !== 'more') && (
                                    <>
                                        <div className="splitLine"></div>
                                        <ToolTip
                                            position={tipsPosition}
                                            trigger={
                                                <i
                                                    className="iconfont icon-xuanfu-gengduo"
                                                    onMouseDown={(e) => {
                                                        this.openRightFloatPanel(e);
                                                        assetManager.setPv_new(8521, {
                                                            additional: { s0: asset ? asset.meta?.type : 'background' },
                                                        });
                                                    }}
                                                ></i>
                                            }
                                            triggerType="hover"
                                            content="更多"
                                        ></ToolTip>
                                    </>
                                )}
                            </>
                        )}
                    </div>
                </div>
                {/** 小菜单栏 位置在选中元素顶部剧中*/}
                {isShow && !isBackground && this.renderQuickMenu({
                    quickMenuStyle,
                    tipsPosition,
                    isMulAseests,
                    asset,
                    isBackground,
                    isBackgroundImage,
                    showAiTextOption, 
                    showAiTextOptionWrap
                })}
            </>
        );
    }
}
