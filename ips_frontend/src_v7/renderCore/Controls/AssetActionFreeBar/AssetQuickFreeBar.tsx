/**
 * 悬浮框操作栏
 */
import { emitter } from '@src/userComponentV6.0/Emitter';
import React, { Component } from 'react';
import { EditorLogic } from '@v7_logic/EditorLogic';
import { AssetLogic, SelectAsset, UpdateAsset } from '@v7_logic/AssetLogic';
import { ToolTip } from '@v7_render/Ui/ToolTip';
import { assetManager } from '@src/userComponentV6.0/AssetManager';
import { IAsset, ICanvas, IUserInfo, IToolPanel } from '@v7_logic/Interface';
import { CanvasPaintedLogic } from '@v7_logic/CanvasPaintedLogic';
import { GroupAndMultipleSelectLogic } from '@v7_logic/GroupAndMultipleSelectLogic';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { icon_kuoxie, icon_suoxie, icon_ruise, icon_fanyi, icon_yishuzi } from './icon';

interface AssetQuickFreeBarProps {
    quickMenuStyle: React.CSSProperties;
    tipsPosition: 'top' | 'left' | 'bottom' | 'right';
    isMulAseests: boolean;
    asset: IAsset;
    isBackground: boolean;
    isBackgroundImage: boolean;
    showAiTextOption: boolean;
    showAiTextOptionWrap: boolean;
    pageIndex: number;
    hide: () => void;
    toolPanel: IToolPanel;
}

interface AssetQuickFreeBarState {
    isShowFramDelPanel?: boolean;
    showAiTextOptionWrap?: boolean;
    isShowTableDelPanel?: boolean;
}

export default class AssetQuickFreeBar extends Component<AssetQuickFreeBarProps, AssetQuickFreeBarState> {
    isOverAiTextOptionBox: boolean = false;
    constructor(props) {
        super(props);
        this.state = {
            isShowTableDelPanel: false,
        };
    }

    /**
     * 组合元素
     * @param e
     */
    combinationAssetEvent(e: React.MouseEvent) {
        AssetLogic.combinationAsset({});
        assetManager.setPv_new(2426, { additional: { s0: 'bar' } });
    }

    /**
     * 分离元素
     * @param e
     */
    splitAssetEvent(e: React.MouseEvent) {
        const { toolPanel } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        if (toolPanel.asset.meta && toolPanel.asset.meta.groupWordID) {
            assetManager.setPv_new(169, {
                additional: {
                    groupWordID: toolPanel.asset.meta.groupWordID,
                    s0: 'bar',
                },
            });
        }
        assetManager.setPv_new(2427, { additional: { s0: 'bar' } });
        // 组内编辑元素的状态下解除组合
        if (toolPanel.asset.meta.type != 'group' && toolPanel.asset.meta.group) {
            if (toolPanel.asset.meta.groupInnerMoving) {
                GroupAndMultipleSelectLogic.updateGroupInnerMovingStatus(toolPanel.asset.meta.group);
                setTimeout(() => {
                    AssetLogic.splitAsset({ groupName: toolPanel.asset.meta.group });
                }, 100);
            } else {
                AssetLogic.splitAsset({ groupName: toolPanel.asset.meta.group });
            }
        } else {
            AssetLogic.splitAsset({ groupName: toolPanel.asset?.meta?.group });
        }
        e.stopPropagation();
    }

    /**
     * 复制元素
     */
    copyAsset(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        this.props.hide();
        e.stopPropagation();
        assetManager.setPv_new(2431, { additional: { s0: 'menu' } });
        const { asset, toolPanel } = this.props;
        // 不是特效字
        if (
            asset &&
            asset.meta.type === 'text' &&
            !(asset.attribute.effectVariant && asset.attribute.effect) &&
            asset.meta.isEdit
        ) {
            UpdateAsset.updateAssets('UPDATE_TEXT_EDIT_STATUS', [
                {
                    index: toolPanel.asset_index,
                    className: asset.meta.className,
                    changes: {
                        meta: {
                            isEdit: false,
                        },
                    },
                },
            ]);
        }
        setTimeout(() => {
            EditorLogic.copyToolPanelAsset();
        }, 0);
    }
    /**
     * 点击删除图标
     */
    deleteIconClickEvent(e: React.MouseEvent<HTMLElement, MouseEvent>) {
        e.preventDefault();
        e.stopPropagation();
        this.onChangeCurrentPageIndex();
        const { asset } = this.props;
        assetManager.setPv_new(2432, { additional: { s0: 'menu' } });
        if (asset?.meta?.type !== 'frame') {
            AssetLogic.deleteSelectAsset();
        } else {
            this.setState({
                isShowFramDelPanel: true,
            });
        }
    }

    onChangeCurrentPageIndex() {
        CanvasPaintedLogic.selectPageTemplate({ pageNumber: this.props.pageIndex });
        CanvasPaintedLogic.mutisizeSetFloorTemplateCurrentindex({
            floorTemplateIndex: this.props.pageIndex,
            clickTem: 'free-tool' as any,
        });
    }

    onAiTextOptionClick(mode: string, e: React.MouseEvent<HTMLElement, MouseEvent>) {
        const asset = this.props.asset;
        e.stopPropagation();
        assetManager.setPv_new(8866, { additional: { s0: mode, s1: asset?.meta.type || '' } });
        emitter.emit('showAiTextPopup', { mode: Number(mode) });
    }

    onWordArtClick() {
        emitter.emit('showAITextWordart');
        assetManager.setPv_new(8912);
    }

    isFullRowOrColSelected(
        selectedCells: string[],
        rowCount: number,
        colCount: number,
    ): { isFullRow: boolean; isFullCol: boolean } {
        if (selectedCells.length === 0) return { isFullRow: false, isFullCol: false };

        const rowMap: Record<number, Set<number>> = {};
        const colMap: Record<number, Set<number>> = {};

        for (const key of selectedCells) {
            const [rowStr, colStr] = key.split('_');
            const row = Number(rowStr);
            const col = Number(colStr);

            if (!rowMap[row]) rowMap[row] = new Set();
            if (!colMap[col]) colMap[col] = new Set();

            rowMap[row].add(col);
            colMap[col].add(row);
        }

        // Check if any row is fully selected
        const isFullRow = Object.values(rowMap).some((cols) => cols.size === colCount);

        // Check if any col is fully selected
        const isFullCol = Object.values(colMap).some((rows) => rows.size === rowCount);

        return { isFullRow, isFullCol };
    }

    getTableConfig() {
        const { toolPanel, asset, tipsPosition } = this.props;
        const isTableAsset = asset?.meta?.type === 'table';
        if (!isTableAsset) {
            return null;
        }
        let isChoiceTableOneKey = false;
        const { choosedTdKeys = [] } = toolPanel.asset;
        if (choosedTdKeys.length === 1) {
            isChoiceTableOneKey = true;
        } else if (choosedTdKeys.length > 1) {
            const { isFullRow, isFullCol } = this.isFullRowOrColSelected(
                choosedTdKeys,
                asset.attribute.cell.length,
                asset.attribute.cell[0].length,
            );
            if ((isFullRow && isFullCol) || (!isFullRow && !isFullCol)) {
                return null;
            } else {
                if (isFullRow) {
                    return (
                        <>
                            <ToolTip
                                position={tipsPosition}
                                trigger={
                                    <i
                                        className="iconfont icon-xuanfulan-zaishangfangtianjiayihang"
                                        onMouseDown={(e) => {}}
                                    ></i>
                                }
                                triggerType="hover"
                                content="在上方添加行"
                            ></ToolTip>
                            <ToolTip
                                position={tipsPosition}
                                trigger={
                                    <i
                                        className="iconfont icon-xuanfulan-xiafangtianjia1hang"
                                        onMouseDown={(e) => {}}
                                    ></i>
                                }
                                triggerType="hover"
                                content="在下方添加行"
                            ></ToolTip>
                        </>
                    );
                }
                if (isFullCol) {
                    return (
                        <>
                            <ToolTip
                                position={tipsPosition}
                                trigger={
                                    <i
                                        className="iconfont icon-xuanfulan-zuocetianjiayilie"
                                        onMouseDown={(e) => {}}
                                    ></i>
                                }
                                triggerType="hover"
                                content="在左侧添加列"
                            ></ToolTip>
                            <ToolTip
                                position={tipsPosition}
                                trigger={
                                    <i
                                        className="iconfont icon-xuanfulan-youcetianjiayilie"
                                        onMouseDown={(e) => {}}
                                    ></i>
                                }
                                triggerType="hover"
                                content="在右侧添加列"
                            ></ToolTip>
                        </>
                    );
                }
            }
        }
    }

    render() {
        const {
            quickMenuStyle,
            tipsPosition,
            isMulAseests,
            asset,
            isBackground,
            isBackgroundImage,
            showAiTextOption,
            showAiTextOptionWrap,
        } = this.props;
        const { isShowTableDelPanel } = this.state;

        return (
            <div className="quick-menu-wrap" style={quickMenuStyle} onWheel={(e) => e.stopPropagation()}>
                {/** 多选 显示组合按钮 */}
                {isMulAseests && (
                    <ToolTip
                        position={tipsPosition}
                        trigger={
                            <span className="quickGroupBtn" onMouseDown={this.combinationAssetEvent.bind(this)}>
                                组合
                            </span>
                        }
                        triggerType="hover"
                        content=""
                    ></ToolTip>
                )}
                {/** 是组合， 则显示拆分组合 */}
                {!isMulAseests && (asset?.meta?.type === 'group' || asset?.meta?.group) && (
                    <ToolTip
                        position={tipsPosition}
                        trigger={
                            <span className="quickGroupBtn" onMouseDown={this.splitAssetEvent.bind(this)}>
                                拆分组合
                            </span>
                        }
                        triggerType="hover"
                        content=""
                    ></ToolTip>
                )}
                {/** 表格， 添加行列 */}
                {this.getTableConfig()}
                {/** 单选 显示复制按钮 */}
                {!isBackground && (
                    <ToolTip
                        position={tipsPosition}
                        trigger={
                            <i
                                className="iconfont icon-bianjiqi-xinhuabu-fuzhi"
                                onMouseDown={(e) => {
                                    this.copyAsset(e);
                                    assetManager.setPv_new(8519, { additional: { s0: asset?.meta?.type } });
                                }}
                            ></i>
                        }
                        triggerType="hover"
                        content="创建副本"
                    ></ToolTip>
                )}
                {(!isBackground || isBackgroundImage) && (
                    <>
                        <ToolTip
                            position={tipsPosition}
                            trigger={
                                <i
                                    className="iconfont icon-bianjiqi-xinhuabu-shanchu"
                                    onMouseDown={(e) => {
                                        if (!isBackground) {
                                            this.deleteIconClickEvent(e);
                                        }
                                        assetManager.setPv_new(8520, {
                                            additional: { s0: asset ? asset.meta?.type : 'background' },
                                        });
                                    }}
                                ></i>
                            }
                            triggerType="hover"
                            content="删除"
                        ></ToolTip>
                    </>
                )}
                {showAiTextOption && (
                    <>
                        {/* <div className="splitLine"></div> */}
                        <div
                            className="aiTextOption"
                            onMouseEnter={() => {
                                this.isOverAiTextOptionBox = true;
                                this.setState({ showAiTextOptionWrap: true });
                            }}
                            onMouseLeave={(e) => {
                                this.isOverAiTextOptionBox = false;
                                setTimeout(() => {
                                    if (!this.isOverAiTextOptionBox) {
                                        this.setState({ showAiTextOptionWrap: false });
                                    }
                                }, 100);
                            }}
                            onMouseDown={(e) => {
                                e.stopPropagation();
                                e.nativeEvent.stopPropagation();
                            }}
                        >
                            <div className="imageIcon">
                                <img src="https://js.tuguaishou.com/image/editor/ai_text.png" alt="" />
                            </div>
                            {/* <i className="iconfont icon-AI"></i> */}
                            {/* <div className={"newIcon"}>NEW</div> */}

                            {showAiTextOptionWrap && (
                                <div
                                    className="option-wrap"
                                    onMouseEnter={() => {
                                        this.isOverAiTextOptionBox = true;
                                        assetManager.setPv_new(8865);
                                    }}
                                    onMouseLeave={() => {
                                        this.isOverAiTextOptionBox = false;
                                        setTimeout(() => {
                                            if (!this.isOverAiTextOptionBox) {
                                                this.setState({ showAiTextOptionWrap: false });
                                            }
                                        }, 100);
                                    }}
                                >
                                    <div className="option-item" onMouseDown={this.onAiTextOptionClick.bind(this, '1')}>
                                        {/* <i className="iconfont icon-a-kuoxie2"></i> */}
                                        <span className="svgIcon">{icon_kuoxie}</span>
                                        <span>扩写</span>
                                    </div>
                                    <div className="option-item" onMouseDown={this.onAiTextOptionClick.bind(this, '2')}>
                                        {/* <i className="iconfont icon-jianxie"></i> */}
                                        <span className="svgIcon">{icon_suoxie}</span>
                                        <span>缩写</span>
                                    </div>
                                    <div className="option-item" onMouseDown={this.onAiTextOptionClick.bind(this, '3')}>
                                        {/* <i className="iconfont icon-runse"></i> */}
                                        <span className="svgIcon">{icon_ruise}</span>
                                        <span>润色</span>
                                    </div>
                                    <div className="option-item" onMouseDown={this.onAiTextOptionClick.bind(this, '4')}>
                                        {/* <i className="iconfont icon-fanyi"></i> */}
                                        <span className="svgIcon">{icon_fanyi}</span>
                                        <span>翻译</span>
                                    </div>
                                    <div className={'option-item'} onMouseDown={this.onWordArtClick.bind(this)}>
                                        {/* <i className="iconfont icon-a-yishuzi1"></i> */}
                                        <span className="svgIcon">{icon_yishuzi}</span>
                                        <span>艺术字</span>
                                    </div>
                                </div>
                            )}
                        </div>
                    </>
                )}
                {isShowTableDelPanel && (
                    <div className="tableDelPanel">
                        <div className='item'>
                            <span className="iconfont icon-xuanfulan-shanchuyilie"></span>
                            <span>删除1列</span>
                        </div>
                        <div className='item'>
                            <span className="iconfont icon-xuanfulan-shanchuyihang"></span>
                            <span>删除1列</span>
                        </div>
                        <div className='item'>
                            <span className="iconfont icon-xuanfulan-biaoge"></span>
                            <span>删除1列</span>
                        </div>
                    </div>
                )}
            </div>
        );
    }
}
