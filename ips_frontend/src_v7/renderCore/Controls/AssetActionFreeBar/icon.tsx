import React from 'react';

export const icon_kuoxie = (
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="15" viewBox="0 0 14 15" fill="none">
        <g clip-path="url(#clip0_3936_355)">
            <path
                d="M13.022 9.92226L12.0595 8.77519C11.9023 8.58789 11.6234 8.56328 11.4361 8.72051L7.44664 12.0674C7.38101 12.1221 7.33316 12.1945 7.30855 12.2766L7.04878 13.1174C6.96539 13.3895 7.0105 13.6561 7.1732 13.8502C7.31402 14.017 7.52183 14.1086 7.75699 14.1086C7.79527 14.1086 7.83355 14.1059 7.8732 14.1018L8.7482 13.9937C8.83296 13.9828 8.91363 13.9486 8.97925 13.8939L12.9687 10.5471C13.0589 10.4705 13.115 10.3639 13.1259 10.2463C13.1341 10.1273 13.0986 10.0139 13.022 9.92226ZM12.0595 10.152L8.51031 13.1311L7.95113 13.1994L8.11656 12.6621L11.6671 9.68301L12.0595 10.152Z"
                fill="#E476FF"
            />
            <path
                d="M6.16133 13.1625H3.03457C2.74883 13.1625 2.51641 12.9301 2.51641 12.6444L2.5 3.35569C2.5 3.06995 2.73242 2.83752 3.01816 2.83752H10.7441C11.0299 2.83752 11.2623 3.06995 11.2623 3.35569L11.2705 7.9303H12.2275L12.2193 3.35569C12.2193 2.54221 11.5576 1.88049 10.7441 1.88049H3.01816C2.20469 1.88049 1.54297 2.54221 1.54297 3.35569L1.55937 12.6444C1.55937 13.4578 2.22109 14.1196 3.03457 14.1196H6.16133V13.1625Z"
                fill="#8C63F9"
            />
            <path
                d="M9.48965 4.75159H4.2793C4.01543 4.75159 3.80078 4.53694 3.80078 4.27307C3.80078 4.0092 4.01543 3.79456 4.2793 3.79456H9.48965C9.75351 3.79456 9.96816 4.0092 9.96816 4.27307C9.96816 4.53694 9.75351 4.75159 9.48965 4.75159Z"
                fill="#8C63F9"
            />
            <path
                d="M9.48965 6.3457H4.2793C4.01543 6.3457 3.80078 6.13105 3.80078 5.86719C3.80078 5.60332 4.01543 5.38867 4.2793 5.38867H9.48965C9.75351 5.38867 9.96816 5.60332 9.96816 5.86719C9.96816 6.13105 9.75351 6.3457 9.48965 6.3457Z"
                fill="#8C63F9"
            />
            <path
                d="M6.52285 12.371H4.2793C4.01543 12.371 3.80078 12.1563 3.80078 11.8925C3.80078 11.6286 4.01543 11.4139 4.2793 11.4139H6.52148C6.78535 11.4139 7 11.6286 7 11.8925C7 12.1563 6.78672 12.371 6.52285 12.371Z"
                fill="#8C63F9"
            />
            <path
                d="M6.79317 8.63572L6.29141 9.13748C6.29005 9.13884 6.28868 9.13748 6.28868 9.13611V7.45994C6.28868 7.19607 6.0754 6.98279 5.81153 6.98279C5.54766 6.98279 5.33438 7.19607 5.33438 7.45994L5.33302 9.13611C5.33302 9.13748 5.33165 9.13748 5.33028 9.13748L4.82852 8.63572C4.63028 8.43748 4.30763 8.44021 4.12306 8.65076C3.95763 8.83806 3.96446 9.12517 4.14356 9.30564L5.47247 10.6345C5.47794 10.64 5.48341 10.6455 5.48888 10.651C5.49161 10.6537 5.49434 10.6564 5.49708 10.6578C5.49981 10.6605 5.50255 10.6633 5.50665 10.666C5.50938 10.6687 5.51485 10.6715 5.51759 10.6742C5.51895 10.6756 5.52306 10.6769 5.52579 10.6797C5.53126 10.6824 5.53399 10.6851 5.53809 10.6879C5.53946 10.6892 5.54356 10.6906 5.54493 10.6933C5.5504 10.6961 5.55313 10.6988 5.55723 10.7015C5.5586 10.7029 5.5627 10.7043 5.56407 10.707C5.56954 10.7097 5.57227 10.7125 5.57774 10.7152C5.58048 10.7166 5.58321 10.7179 5.58595 10.7193C5.59005 10.7207 5.59415 10.7248 5.59825 10.7261C5.60098 10.7275 5.60372 10.7289 5.60645 10.7316C5.61056 10.733 5.61466 10.7357 5.61739 10.7371C5.62149 10.7385 5.62423 10.7398 5.62833 10.7426C5.63243 10.7439 5.63516 10.7453 5.6379 10.7467C5.642 10.748 5.6461 10.7508 5.64884 10.7508C5.65157 10.7521 5.65431 10.7521 5.65704 10.7535C5.66251 10.7549 5.66524 10.7562 5.67071 10.7576C5.67345 10.759 5.67618 10.759 5.67891 10.759C5.68438 10.7603 5.68848 10.7603 5.69395 10.7644C5.69669 10.7658 5.69942 10.7658 5.70216 10.7658C5.70763 10.7672 5.71173 10.7672 5.7172 10.7699C5.71993 10.7713 5.72266 10.7713 5.7254 10.7713C5.73087 10.7726 5.73497 10.7726 5.74044 10.774C5.74317 10.7754 5.74727 10.7754 5.74864 10.7754C5.75411 10.7767 5.75684 10.7767 5.76231 10.7767C5.76641 10.7767 5.77052 10.7767 5.77462 10.7781C5.77735 10.7781 5.77872 10.7781 5.78145 10.7781C5.78829 10.7781 5.80059 10.7781 5.8088 10.7795C5.817 10.7795 5.82384 10.7795 5.83067 10.7781C5.83477 10.7781 5.83888 10.7781 5.84161 10.7781C5.84434 10.7781 5.84981 10.7781 5.85391 10.7767C5.85938 10.7754 5.86212 10.7754 5.86759 10.7754C5.87032 10.774 5.87306 10.7754 5.87579 10.774C5.88126 10.7726 5.88536 10.7726 5.89083 10.7726C5.89356 10.7713 5.8963 10.7713 5.89903 10.7713C5.9045 10.7699 5.9086 10.7699 5.91407 10.7672C5.9168 10.7658 5.91954 10.7658 5.92227 10.7658C5.92774 10.7644 5.93184 10.7644 5.93731 10.7617C5.94005 10.7603 5.94278 10.7603 5.94552 10.7603C5.94962 10.759 5.95372 10.7576 5.95919 10.7562C5.96192 10.7549 5.96602 10.7535 5.96876 10.7535C5.97286 10.7521 5.97696 10.7508 5.98106 10.7494C5.9838 10.748 5.9879 10.7467 5.99063 10.7453C5.99473 10.7439 5.99747 10.7426 6.00157 10.7398C6.00567 10.7385 6.00977 10.7357 6.01251 10.7344C6.01524 10.733 6.01934 10.7316 6.02071 10.7289C6.02481 10.7275 6.02891 10.7248 6.03302 10.722C6.03575 10.7207 6.03848 10.7193 6.04122 10.7179C6.04669 10.7152 6.04942 10.7125 6.05489 10.7097C6.05626 10.7084 6.06036 10.707 6.06173 10.7043C6.0672 10.7015 6.06993 10.6988 6.07403 10.6961C6.0754 10.6947 6.0795 10.6933 6.08087 10.6906C6.08634 10.6879 6.08907 10.6851 6.09317 10.6824C6.09454 10.681 6.09864 10.6797 6.10138 10.6769C6.10411 10.6742 6.10958 10.6715 6.11231 10.6687C6.11505 10.666 6.11778 10.6633 6.12188 10.6605C6.12462 10.6578 6.12735 10.6551 6.13009 10.6537C6.13555 10.6482 6.14102 10.6427 6.14649 10.6373L7.4754 9.30837C7.6545 9.12927 7.66134 8.84216 7.49591 8.65349C7.31407 8.44021 6.99141 8.43748 6.79317 8.63572Z"
                fill="#8C63F9"
            />
        </g>
        <defs>
            <clipPath id="clip0_3936_355">
                <rect width="14" height="14" fill="white" transform="translate(0 0.733765)" />
            </clipPath>
        </defs>
    </svg>
);

export const icon_suoxie = (
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
        <path
            d="M12.6861 9.1885L11.7236 8.04143C11.5664 7.85412 11.2875 7.82952 11.1002 7.98674L7.1107 11.3336C7.04507 11.3883 6.99722 11.4608 6.97261 11.5428L6.71285 12.3836C6.62945 12.6557 6.67457 12.9223 6.83726 13.1164C6.97808 13.2832 7.18589 13.3748 7.42105 13.3748C7.45933 13.3748 7.49761 13.3721 7.53726 13.368L8.41226 13.26C8.49703 13.249 8.57769 13.2149 8.64332 13.1602L12.6328 9.8133C12.723 9.73674 12.7791 9.6301 12.79 9.51252C12.7982 9.39358 12.7627 9.2801 12.6861 9.1885ZM11.7236 9.41819L8.17437 12.3973L7.61519 12.4656L7.78062 11.9283L11.3312 8.94924L11.7236 9.41819Z"
            fill="#E476FF"
        />
        <path
            d="M5.8293 12.4288H2.70254C2.4168 12.4288 2.18438 12.1963 2.18438 11.9106L2.16797 2.62192C2.16797 2.33618 2.40039 2.10376 2.68613 2.10376H10.4121C10.6979 2.10376 10.9303 2.33618 10.9303 2.62192L10.9385 7.19653H11.8955L11.8873 2.62192C11.8873 1.80845 11.2256 1.14673 10.4121 1.14673H2.68613C1.87266 1.14673 1.21094 1.80845 1.21094 2.62192L1.22734 11.9106C1.22734 12.7241 1.88906 13.3858 2.70254 13.3858H5.8293V12.4288Z"
            fill="#8C63F9"
        />
        <path
            d="M9.15762 4.6344H3.94727C3.6834 4.6344 3.46875 4.41975 3.46875 4.15588C3.46875 3.89202 3.6834 3.67737 3.94727 3.67737H9.15762C9.42148 3.67737 9.63613 3.89202 9.63613 4.15588C9.63613 4.41975 9.42148 4.6344 9.15762 4.6344Z"
            fill="#8C63F9"
        />
        <path
            d="M6.96075 8.07288C7.12618 7.8842 7.11934 7.59709 6.94024 7.41799L5.61134 6.08909C5.60587 6.08362 5.6004 6.07815 5.59493 6.07268C5.5922 6.07131 5.58946 6.06858 5.58673 6.06584C5.58263 6.06311 5.57989 6.06038 5.57716 6.05764C5.57442 6.05491 5.56895 6.05217 5.56622 6.04944C5.56348 6.0467 5.55938 6.04534 5.55802 6.04397C5.55391 6.04124 5.55118 6.0385 5.54571 6.03577C5.54434 6.03303 5.54024 6.03167 5.53888 6.0303C5.53477 6.02756 5.53204 6.02483 5.52657 6.02209C5.5252 6.01936 5.5211 6.01799 5.51973 6.01663C5.51427 6.01389 5.51153 6.01116 5.50606 6.00842C5.50333 6.00706 5.50059 6.00569 5.49786 6.00432C5.49376 6.00159 5.48966 5.99885 5.48556 5.99749C5.48419 5.99475 5.48009 5.99338 5.47735 5.99202C5.47462 5.99065 5.47052 5.98791 5.46641 5.98655C5.46231 5.98381 5.45958 5.98245 5.45548 5.98108C5.45274 5.97971 5.44864 5.97834 5.44591 5.97698C5.44181 5.97561 5.4377 5.97424 5.4336 5.97288C5.43087 5.97288 5.42677 5.97151 5.42403 5.97014C5.41856 5.96877 5.41446 5.96741 5.41036 5.96604C5.40763 5.96604 5.40489 5.96604 5.40216 5.96467C5.39669 5.96194 5.39259 5.96194 5.38712 5.96057C5.38438 5.96057 5.38165 5.96057 5.37891 5.9592C5.37345 5.95647 5.36934 5.95647 5.36388 5.9551C5.36114 5.9551 5.35841 5.9551 5.35567 5.95374C5.3502 5.95374 5.3461 5.95374 5.34063 5.95237C5.3379 5.951 5.33516 5.95237 5.33243 5.951C5.32696 5.951 5.32423 5.951 5.31876 5.94963C5.31466 5.94827 5.30919 5.94827 5.30645 5.94827C5.30372 5.94827 5.29962 5.94827 5.29552 5.94827C5.28868 5.9469 5.28184 5.9469 5.27364 5.9469C5.26544 5.94827 5.25313 5.94827 5.2463 5.94827C5.24356 5.94827 5.2422 5.94827 5.23946 5.94827C5.23536 5.94963 5.23126 5.94963 5.22716 5.94963C5.22169 5.94963 5.21895 5.94963 5.21349 5.951C5.21212 5.951 5.20802 5.951 5.20528 5.95237C5.19981 5.95374 5.19571 5.95374 5.19024 5.9551C5.18751 5.9551 5.18477 5.9551 5.18204 5.95647C5.17657 5.9592 5.17247 5.9592 5.167 5.96057C5.16427 5.96057 5.16153 5.96057 5.1588 5.96194C5.15333 5.96604 5.14923 5.96604 5.14376 5.96741C5.14102 5.96741 5.13829 5.96741 5.13556 5.96877C5.13009 5.97014 5.12735 5.97151 5.12188 5.97288C5.11915 5.97424 5.11641 5.97424 5.11368 5.97561C5.11095 5.97561 5.10684 5.97834 5.10274 5.97971C5.10001 5.98108 5.09727 5.98245 5.09317 5.98381C5.08907 5.98655 5.08634 5.98792 5.08223 5.98928C5.0795 5.99065 5.0754 5.99338 5.0713 5.99475C5.06856 5.99749 5.06583 5.99885 5.06309 6.00022C5.05899 6.00159 5.05489 6.00569 5.05079 6.00706C5.04806 6.00842 5.04532 6.00979 5.04259 6.01116C5.03712 6.01389 5.03438 6.01663 5.02891 6.01936C5.02755 6.02209 5.02345 6.02346 5.02208 6.02483C5.01798 6.02756 5.01524 6.0303 5.00977 6.03303C5.00841 6.03577 5.00431 6.03713 5.00294 6.0385C4.99884 6.04124 4.9961 6.04397 4.99063 6.0467C4.9879 6.04944 4.9838 6.05081 4.98243 6.05217C4.9797 6.05491 4.97423 6.05764 4.97149 6.06038C4.96739 6.06311 4.96466 6.06584 4.96192 6.06858C4.95919 6.06995 4.95645 6.07268 4.95372 6.07541C4.94825 6.08088 4.94278 6.08635 4.93731 6.09182L3.60841 7.42073C3.42931 7.6012 3.42247 7.88831 3.5879 8.07561C3.77247 8.28616 4.09513 8.28889 4.29337 8.09065L4.79513 7.58889C4.79649 7.58889 4.79786 7.58889 4.79786 7.59026L4.79923 9.26643C4.79923 9.5303 5.01251 9.74358 5.27638 9.74358C5.54024 9.74358 5.75352 9.5303 5.75352 9.26643V7.59026C5.75352 7.58889 5.75489 7.58752 5.75626 7.58889L6.25802 8.09065C6.45626 8.28889 6.77891 8.28616 6.96075 8.07288Z"
            fill="#8C63F9"
        />
    </svg>
);

export const icon_ruise = (
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="15" viewBox="0 0 14 15" fill="none">
        <g clip-path="url(#clip0_3936_379)">
            <path
                d="M7.51094 13.1612H2.37441C2.08867 13.1612 1.85625 12.9288 1.85625 12.643L1.83984 3.35435C1.83984 3.0686 2.07227 2.83618 2.35801 2.83618H10.084C10.3697 2.83618 10.6021 3.0686 10.6021 3.35435L10.6104 7.52153H11.5674L11.5592 3.35435C11.5592 2.54087 10.8975 1.87915 10.084 1.87915H2.35801C1.54453 1.87915 0.882812 2.54087 0.882812 3.35435L0.899219 12.643C0.899219 13.4565 1.56094 14.1182 2.37441 14.1182H7.5123V13.1612H7.51094Z"
                fill="#8C63F9"
            />
            <path
                d="M8.82949 5.23145H3.61914C3.35527 5.23145 3.14062 5.0168 3.14062 4.75293C3.14062 4.48906 3.35527 4.27441 3.61914 4.27441H8.82949C9.09336 4.27441 9.30801 4.48906 9.30801 4.75293C9.30801 5.0168 9.09336 5.23145 8.82949 5.23145Z"
                fill="#8C63F9"
            />
            <path
                d="M8.82949 7.50928H3.61914C3.35527 7.50928 3.14062 7.29463 3.14062 7.03076C3.14062 6.76689 3.35527 6.55225 3.61914 6.55225H8.82949C9.09336 6.55225 9.30801 6.76689 9.30801 7.03076C9.30801 7.29463 9.09336 7.50928 8.82949 7.50928Z"
                fill="#8C63F9"
            />
            <path
                d="M10.9012 10.45C10.6278 10.3311 10.178 10.1096 9.93872 9.85669C9.73091 9.63794 9.54634 9.24966 9.44106 9.00083C9.40552 8.91606 9.32485 8.87915 9.25376 8.87915C9.18267 8.87915 9.102 8.91606 9.06509 9.00083C8.95981 9.24966 8.77388 9.63657 8.56743 9.85942C8.34048 10.1055 7.91392 10.3366 7.66646 10.4569C7.51743 10.5293 7.51743 10.7467 7.66646 10.8192C7.91255 10.9395 8.33911 11.1719 8.56743 11.4167C8.77251 11.6381 8.95845 12.0264 9.06509 12.2752C9.10063 12.36 9.18267 12.3969 9.25376 12.3969C9.32485 12.3969 9.40552 12.36 9.44106 12.2752C9.54634 12.0264 9.73091 11.6381 9.93872 11.4194C10.178 11.1665 10.6278 10.945 10.9012 10.826C10.9846 10.7891 11.0215 10.7098 11.0215 10.6387C11.0215 10.5663 10.9846 10.4856 10.9012 10.45ZM10.6633 10.5868C10.6825 10.5963 10.703 10.6045 10.7208 10.6127C10.703 10.6045 10.6838 10.5963 10.6633 10.5868ZM10.2709 10.3817C10.2942 10.3954 10.3161 10.4077 10.3393 10.4213C10.3161 10.4077 10.2928 10.3954 10.2709 10.3817C10.2477 10.368 10.2258 10.3543 10.2026 10.3407C10.2245 10.3543 10.2477 10.368 10.2709 10.3817Z"
                fill="#E376FF"
            />
            <path
                d="M13.0226 12.7456C12.9419 12.7127 12.8148 12.6567 12.6863 12.5829C12.555 12.5104 12.4279 12.4243 12.3431 12.3354C12.1914 12.1754 12.0587 11.8774 11.9959 11.7243C11.9562 11.6245 11.8126 11.6245 11.7716 11.7243C11.7087 11.876 11.5761 12.1754 11.4244 12.3381C11.3451 12.4229 11.2248 12.509 11.1017 12.5829C10.9814 12.6553 10.8625 12.7141 10.7914 12.7497C10.6998 12.7934 10.6998 12.9247 10.7914 12.9684C10.8625 13.0012 10.98 13.06 11.1017 13.1352C11.2248 13.2077 11.3423 13.2938 11.4244 13.3799C11.5748 13.5426 11.7087 13.8393 11.7716 13.9938C11.8126 14.0936 11.9562 14.0936 11.9959 13.9938C12.0587 13.8393 12.1914 13.5426 12.3431 13.3827C12.4279 13.2938 12.555 13.2077 12.6863 13.1352C12.8148 13.0627 12.9447 13.0067 13.0226 12.9725C13.1224 12.9288 13.1224 12.7852 13.0226 12.7456Z"
                fill="#E376FF"
            />
        </g>
        <defs>
            <clipPath id="clip0_3936_379">
                <rect width="14" height="14" fill="white" transform="translate(0 0.733765)" />
            </clipPath>
        </defs>
    </svg>
);

export const icon_fanyi = (
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="15" viewBox="0 0 14 15" fill="none">
        <g clip-path="url(#clip0_3936_391)">
            <path
                d="M4.87598 4.63811V4.24983C4.87598 4.13635 4.89102 4.03792 4.92246 3.95452C4.93066 3.94084 4.93613 3.92444 4.9375 3.90803C4.9375 3.89709 4.90059 3.88752 4.82812 3.87659H4.29902V4.63811H3.02344V6.45784H3.55254V6.17756H4.31406V7.51467H4.87461V6.17756H5.65254V6.41135H6.22813V4.63811H4.87598ZM4.3168 5.75784H3.55527V5.07424H4.3168V5.75784ZM5.65391 5.75784H4.87598V5.07424H5.65391V5.75784Z"
                fill="#E376FF"
            />
            <path
                d="M9.80859 8.89136H9.07578L8.03125 11.7734H8.62461L8.84199 11.165H9.97949L10.1982 11.7734H10.8217L9.80859 8.89136ZM8.99785 10.6837L9.41895 9.45327H9.43398L9.82363 10.6837H8.99785Z"
                fill="#E376FF"
            />
            <path
                d="M9.60547 3.36792H9.46875V4.24839H9.60547C10.2002 4.24839 10.6842 4.73237 10.6842 5.3271V5.46382H11.5646V5.3271C11.5633 4.24702 10.6855 3.36792 9.60547 3.36792Z"
                fill="#8C63F9"
            />
            <path
                d="M3.13828 10.6222V10.4855H2.25781V10.6222C2.25781 11.7023 3.13691 12.5814 4.21699 12.5814H4.35371V11.7009H4.21699C3.62227 11.7009 3.13828 11.2169 3.13828 10.6222Z"
                fill="#8C63F9"
            />
            <path
                d="M11.4662 6.47693H8.52402V3.53337C8.52402 2.62419 7.78438 1.88318 6.87383 1.88318H2.53301C1.62383 1.88318 0.882812 2.62283 0.882812 3.53337V7.87419C0.882812 8.78337 1.62246 9.52439 2.53301 9.52439H5.4752V12.4679C5.4752 13.3771 6.21484 14.1181 7.12539 14.1181H11.4662C12.3754 14.1181 13.1164 13.3785 13.1164 12.4679V8.12576C13.115 7.21658 12.3754 6.47693 11.4662 6.47693ZM5.4752 8.56599H2.53301C2.15156 8.56599 1.83984 8.25564 1.83984 7.87283V3.53337C1.83984 3.15193 2.1502 2.84021 2.53301 2.84021H6.87383C7.25527 2.84021 7.56699 3.15056 7.56699 3.53337V6.47693V7.43396V7.87419C7.56699 8.25564 7.25664 8.56736 6.87383 8.56736H6.43223H5.4752V8.56599ZM12.158 12.4666C12.158 12.848 11.8477 13.1597 11.4648 13.1597H7.12539C6.74395 13.1597 6.43223 12.8494 6.43223 12.4666V9.52302H6.87383C7.78301 9.52302 8.52402 8.78337 8.52402 7.87283V7.43259H11.4662C11.8477 7.43259 12.1594 7.74294 12.1594 8.12576V12.4666H12.158Z"
                fill="#8C63F9"
            />
        </g>
        <defs>
            <clipPath id="clip0_3936_391">
                <rect width="14" height="14" fill="white" transform="translate(0 0.733765)" />
            </clipPath>
        </defs>
    </svg>
);
export const icon_yishuzi = (
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="15" viewBox="0 0 14 15" fill="none">
        <g clip-path="url(#clip0_3936_402)">
            <path
                d="M5.98789 3.44724C5.71445 3.3283 5.26465 3.10681 5.02539 2.85388C4.81758 2.63513 4.63301 2.24685 4.52773 1.99802C4.49219 1.91326 4.41152 1.87634 4.34043 1.87634C4.26934 1.87634 4.18867 1.91326 4.15176 1.99802C4.04648 2.24685 3.86055 2.63376 3.6541 2.85662C3.42715 3.10271 3.00059 3.33376 2.75312 3.45408C2.6041 3.52654 2.6041 3.74392 2.75312 3.81638C2.99922 3.93669 3.42578 4.16912 3.6541 4.41384C3.85918 4.63533 4.04512 5.02361 4.15176 5.27244C4.1873 5.3572 4.26934 5.39412 4.34043 5.39412C4.41152 5.39412 4.49219 5.3572 4.52773 5.27244C4.63301 5.02361 4.81758 4.63533 5.02539 4.41658C5.26465 4.16365 5.71445 3.94216 5.98789 3.82322C6.07129 3.7863 6.1082 3.70701 6.1082 3.63591C6.1082 3.56345 6.07129 3.48279 5.98789 3.44724ZM5.75 3.58396C5.76914 3.59353 5.78965 3.60173 5.80742 3.60994C5.78965 3.60173 5.77051 3.59353 5.75 3.58396ZM5.35762 3.37888C5.38086 3.39255 5.40273 3.40486 5.42598 3.41853C5.40273 3.40486 5.37949 3.39255 5.35762 3.37888C5.33437 3.36521 5.3125 3.35154 5.28926 3.33787C5.31113 3.35154 5.33437 3.36521 5.35762 3.37888ZM3.17695 5.59509C3.09629 5.56228 2.96914 5.50623 2.84063 5.4324C2.70938 5.35994 2.58223 5.2738 2.49746 5.18494C2.3457 5.02498 2.21309 4.72693 2.1502 4.5738C2.11055 4.474 1.96699 4.474 1.92598 4.5738C1.86309 4.72556 1.73047 5.02498 1.57871 5.18767C1.49941 5.27244 1.3791 5.35857 1.25605 5.4324C1.13574 5.50486 1.0168 5.56365 0.945703 5.59919C0.854102 5.64294 0.854102 5.77419 0.945703 5.81794C1.0168 5.85076 1.13438 5.90955 1.25605 5.98474C1.3791 6.0572 1.49668 6.14334 1.57871 6.22947C1.7291 6.39216 1.86309 6.68884 1.92598 6.84334C1.96699 6.94314 2.11055 6.94314 2.1502 6.84334C2.21309 6.68884 2.3457 6.39216 2.49746 6.2322C2.58223 6.14334 2.70938 6.0572 2.84063 5.98474C2.96914 5.91228 3.09902 5.85623 3.17695 5.82205C3.27676 5.7783 3.27676 5.63474 3.17695 5.59509ZM12.8676 12.5937C12.7869 12.5609 12.6598 12.5049 12.5313 12.431C12.4 12.3586 12.2729 12.2724 12.1881 12.1836C12.0363 12.0236 11.9037 11.7256 11.8408 11.5724C11.8012 11.4726 11.6576 11.4726 11.6166 11.5724C11.5537 11.7242 11.4211 12.0236 11.2693 12.1863C11.19 12.2711 11.0697 12.3572 10.9467 12.431C10.8264 12.5035 10.7074 12.5623 10.6363 12.5978C10.5447 12.6416 10.5447 12.7728 10.6363 12.8166C10.7074 12.8494 10.825 12.9082 10.9467 12.9834C11.0697 13.0558 11.1873 13.142 11.2693 13.2281C11.4197 13.3908 11.5537 13.6875 11.6166 13.842C11.6576 13.9418 11.8012 13.9418 11.8408 13.842C11.9037 13.6875 12.0363 13.3908 12.1881 13.2308C12.2729 13.142 12.4 13.0558 12.5313 12.9834C12.6598 12.9109 12.7896 12.8549 12.8676 12.8207C12.9674 12.7769 12.9674 12.6334 12.8676 12.5937ZM9.06543 13.1871H3.53926C2.58496 13.1871 1.8125 12.4133 1.8125 11.4603V8.29392C1.8125 8.03005 1.59785 7.81541 1.33398 7.81541C1.07012 7.81541 0.855469 8.03005 0.855469 8.29392V11.4603C0.855469 12.9424 2.05723 14.1441 3.53926 14.1441H9.06543C9.3293 14.1441 9.54395 13.9295 9.54395 13.6656C9.54395 13.4017 9.33066 13.1871 9.06543 13.1871ZM7.35098 2.81287H10.46C11.4143 2.81287 12.1867 3.58669 12.1867 4.53962V9.94685C12.1867 10.2107 12.4014 10.4254 12.6652 10.4254C12.9291 10.4254 13.1438 10.2107 13.1438 9.94685V4.53962C13.1438 3.05759 11.942 1.85583 10.46 1.85583H7.35098C7.08711 1.85583 6.87246 2.07048 6.87246 2.33435C6.87246 2.59822 7.08711 2.81287 7.35098 2.81287Z"
                fill="#8C63F9"
            />
            <path
                d="M9.65548 10.9778L7.60196 5.10571C7.57325 5.02368 7.49532 4.96899 7.40782 4.96899H6.8295C6.742 4.96899 6.66407 5.02368 6.63536 5.10571L4.58184 10.9778C4.55997 11.0407 4.56954 11.1104 4.60782 11.1637C4.6461 11.217 4.70899 11.2499 4.77462 11.2499H5.38301C5.47051 11.2499 5.54844 11.1938 5.57716 11.1104L6.18419 9.31939H8.03536L8.62735 11.109C8.65469 11.1924 8.73399 11.2499 8.82149 11.2499H9.46134C9.52833 11.2499 9.58985 11.217 9.62813 11.1637C9.66778 11.1104 9.67735 11.0407 9.65548 10.9778ZM6.49317 8.40337L6.72696 7.71841C6.87872 7.27544 7.00177 6.8981 7.11251 6.54399C7.24512 6.96782 7.36817 7.3397 7.50216 7.71978L7.73048 8.40337H6.49317Z"
                fill="#E376FF"
            />
        </g>
        <defs>
            <clipPath id="clip0_3936_402">
                <rect width="14" height="14" fill="white" transform="translate(0 0.733765)" />
            </clipPath>
        </defs>
    </svg>
);
