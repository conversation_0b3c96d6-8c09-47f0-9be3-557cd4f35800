import { assetManager } from '@src/userComponentV6.0/AssetManager';
import { emitter } from '@src/userComponentV6.0/Emitter';
import { AssetLogic } from '@v7_logic/AssetLogic';
import { imgHost } from '@component/IPSConfig';
import { IAsset, ICanvas } from '@v7_logic/Interface';

import React, { PureComponent } from 'react';
import './scss/AssetTooltip.scss';
import classNames from 'classnames';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';

interface IAssetTooltipProps {
    pageTop?: number;
    canvas: ICanvas;
    asset?: IAsset;
    pageIndex?: number;
    baseGap?: number;
}

export class AssetTooltip extends PureComponent<
    IAssetTooltipProps,
    {
        isShowTooltip: boolean;
        clientX: number;
        clientY: number;
        type?: string;
        isOtherMove?: boolean;
        showFontLoading: boolean;
        showAiImageHandlerLoading: boolean;
        imageHandlerTipText: string;
        imageHandlerIcon: string;
        handleAssetClassName: string;
        curShowAsset: IAsset;
    }
> {
    constructor(props: IAssetTooltipProps) {
        super(props);
        this.state = {
            isShowTooltip: false,
            clientX: 0,
            clientY: 0,
            type: '',
            isOtherMove: false, // 是否是其他移动,例如顶部拖动画布
            showFontLoading: false,
            showAiImageHandlerLoading: false,
            imageHandlerTipText: '',
            imageHandlerIcon: '',
            handleAssetClassName: '',
            curShowAsset: this.props.asset,
        };
        emitter.addListener(
            'changeAssetTooltip',
            (params: { status: boolean; clientX?: number; clientY?: number; type: string; isOtherMove?: boolean }) => {
                const { status, clientX, clientY, type, isOtherMove } = params;
                if (isOtherMove !== undefined) {
                    this.setState({
                        isOtherMove,
                    });
                } else {
                    if (!this.state.isOtherMove) {
                        this.setState({
                            isShowTooltip: status,
                            clientX,
                            clientY,
                            type,
                        });
                    }
                }
            },
        );
        emitter.addListener('showFontLoadingListener', (status: boolean, curShowAsset?: IAsset) => {
            this.setState({
                showFontLoading: status,
            });
            if (curShowAsset) {
                this.setState({
                    curShowAsset,
                });
            }
        });
        emitter.addListener(
            'showAiImageHandlerListener',
            (params: {
                status: boolean;
                tipText?: string;
                icon?: string;
                handlerImageClassName?: string;
                curShowAsset?: IAsset;
            }) => {
                const { status, tipText = '', icon = '', handlerImageClassName = '', curShowAsset } = params;
                this.setState({
                    showAiImageHandlerLoading: status,
                    imageHandlerTipText: tipText,
                    imageHandlerIcon: icon,
                    handleAssetClassName: handlerImageClassName,
                });
                if (curShowAsset) {
                    this.setState({
                        curShowAsset,
                    });
                }
            },
        );
    }

    componentDidUpdate(
        prevProps: Readonly<IAssetTooltipProps>,
        prevState: Readonly<{
            isShowTooltip: boolean;
            clientX: number;
            clientY: number;
            type?: string;
            isOtherMove?: boolean;
            showFontLoading: boolean;
        }>,
        snapshot?: any,
    ): void {
        // if (prevProps.asset !== this.props.asset) {
        //     this.setState({
        //         showFontLoading: false,
        //     });
        // }
    }
    // 解锁
    cancelLockClickEvent(e: MouseEvent) {
        const { pageIndex } = this.props;
        AssetLogic.updateLocked({
            locked: false,
            pageIndex,
        });
        assetManager.setPv_new(2425, { additional: { s0: 'canvas' } });
        e.stopPropagation();
    }

    render() {
        const {
            isShowTooltip,
            clientX,
            clientY,
            type,
            showFontLoading,
            showAiImageHandlerLoading,
            imageHandlerTipText,

            imageHandlerIcon,
            handleAssetClassName,
            curShowAsset,
        } = this.state;
        const { asset, canvas, baseGap = 60 } = this.props;
        const { pageInfo } = storeAdapter.getStore({
            store_name: storeAdapter.store_names.paintOnCanvas,
        }); // canvasStore.getState().onCanvasPainted.toolPanel;
        const pageIndex = pageInfo.pageNow;
        let text;
        if (['image', 'qrcode', 'pic'].indexOf(type) > -1) {
            text = '双击替换图片';
        } else if (type === 'clipedImage') {
            text = '双击编辑图片';
        } else if (type === 'text') {
            text = '双击修改文字';
        } else if (type === 'background') {
            text = '双击编辑背景';
        }
        if (type == 'editImage') {
            text = '双击编辑图片';
        }
        if (type == 'group') {
            text = '双击编辑组合';
        }
        if (type === 'table') {
            text = '点击编辑表格';
        }

        const tooltipPosition = {
            top: clientY,
            left: clientX,
            display: 'none',
        };
        if (isShowTooltip) {
            tooltipPosition.display = 'block';
        }
        let loadWidth = 40;
        let loadingPos = { top: 0, left: 0, width: loadWidth + 'px', height: loadWidth + 'px' };
        let minSize = loadWidth;
        const showImageHandlerLoading =
            showAiImageHandlerLoading && curShowAsset && curShowAsset.meta.className == handleAssetClassName && (curShowAsset.meta.rt_page_index == pageInfo.pageNow);
            if (showFontLoading && this.props.asset) {
            const asset = this.props.asset;
            const minValue = Math.min(asset.attribute.width, asset.attribute.height) * canvas.scale;
            const min = minValue > loadWidth ? loadWidth : Math.max(minValue, 12);
            const dis = pageIndex * (baseGap + canvas.height * canvas.scale);
            const half = min / 2;
            loadingPos = {
                left:
                asset.transform.posX * canvas.scale +
                    (asset.attribute.width * canvas.scale) / 2 -
                    half,
                top:
                    dis +
                    asset.transform.posY * canvas.scale +
                    (asset.attribute.height * canvas.scale) / 2 -
                    half,
                width: min + 'px',
                height: min + 'px',
            };
        } else if (showImageHandlerLoading) {
            loadWidth = 120;
            const assetWidth = curShowAsset.attribute.container?.width || curShowAsset.attribute.width;
            const assetHeight = curShowAsset.attribute.container?.height || curShowAsset.attribute.height;
            const minValue = Math.min(assetWidth, assetHeight) * canvas.scale;
            const min = minValue > loadWidth ? loadWidth : Math.max(minValue / 2, 12);
            const dis = (pageIndex || 0) * (baseGap + canvas.height * canvas.scale);
            const half = min / 2;
            minSize = min;

            loadingPos = {
                left: curShowAsset.transform.posX * canvas.scale + (assetWidth * canvas.scale) / 2 - half,
                top: dis + curShowAsset.transform.posY * canvas.scale + (assetHeight * canvas.scale) / 2 - half,
                width: min + 'px',
                height: min + 'px',
            };
        }

        const lockedPosition = {
            top: clientY,
            left: clientX,
            display: 'none',
        };

        if (asset?.meta?.locked) {
            const dis = pageIndex * (baseGap + canvas.height * canvas.scale);
            lockedPosition.top = dis + asset.transform.posY * canvas.scale + asset.attribute.height * canvas.scale + 10;
            lockedPosition.left = asset.transform.posX * canvas.scale + (asset.attribute.width * canvas.scale) / 2;
            lockedPosition.display = 'block';
        }
        return (
            <>
                <div className={classNames('assetTooltip', type)} style={tooltipPosition}>
                    <span>{text}</span>
                </div>
                {asset?.meta?.locked && (
                    <div
                        className="assetLock"
                        style={lockedPosition}
                        onMouseDown={this.cancelLockClickEvent.bind(this)}
                    >
                        <span className="iconfont icon-suoding1"></span>
                    </div>
                )}
                {showFontLoading && (
                    <div className="fontLoadingWrap" style={loadingPos}>
                        <img alt="fontLoading" src={imgHost + '/site/editor/fontLoading.gif'} />
                    </div>
                )}
                {showImageHandlerLoading && (
                    <div className="imageLoadingWrap" style={loadingPos}>
                        {minSize < 120 ? (
                            <img
                                alt="fontLoading"
                                src={imgHost + '/site/editor/fontLoading.gif'}
                                style={{
                                    width: '80%',
                                    height: '80%',
                                }}
                            />
                        ) : (
                            <>
                                <i className={`iconfont ${imageHandlerIcon}`}></i>
                                <div className="loading-tip">
                                    {imageHandlerTipText}&nbsp;
                                    <span className={'loading-dots'}>
                                        <span></span>
                                        <span></span>
                                        <span></span>
                                    </span>
                                </div>
                            </>
                        )}
                    </div>
                )}
            </>
        );
    }
}
