import { assetManager } from '@src/userComponentV6.0/AssetManager';
import { SelectAsset } from '@v7_logic/AssetLogic';
import { CanvasPaintedLogic } from '@v7_logic/CanvasPaintedLogic';
import { IPSConfig } from '@v7_utils/IPSConfig';
import React, { Component } from 'react';
import { ICanvas, IPage, IPageAttr, IPageInfo, IToolPanel } from '@v7_logic/Interface';
import './canvasScroll.scss';
import { emitter } from '@src/userComponentV6.0/Emitter';
import { TemplateCanvasLogic } from '@v7_logic/TemplateCanvasLogic';
import { CANVAS_GAP } from '@v7_utils/canvas';
import { EventSubscription } from 'fbemitter';
import { applyToPoint, rotateDEG } from 'transformation-matrix';
import { DebouncedFunc, throttle } from 'lodash-es';


interface ICanvasScrollBarProps {
    canvas: ICanvas;
    pages: IPage[];
    pageInfo: IPageInfo;
    pageAttr: IPageAttr;
    pageIndex: number;
    renderMode?: string;
    canvasDom: React.RefObject<HTMLCanvasElement>;
    parentDom: React.RefObject<HTMLDivElement>;
    toolPanelWidth?: number;
    resourcePanelWidth?: number;
    toolPanel: IToolPanel;
}

interface ICanvasScrollBarState {
    canvasAddPage: boolean;
    contentSize?: {
        x: number;
        y: number;
        width: number;
        height: number;
    }
}

const BaseGap = 60;
const horizontalGap = 60;
export class CanvasScrollBar extends Component<
    ICanvasScrollBarProps, ICanvasScrollBarState
> {
    pageWrapRef: HTMLDivElement;
    clientX: number;
    clientY: number;
    thumbType: 'vertical'| 'horizontal';
    pageHeight: number;
    pageWidth: number;
    canvasHeight: number;
    canvasWidth: number;
    wrapWidth: number;
    fixedToolPanel: boolean;
    topGap: number;
    bottomGap: number;
    setCanvasAddPageListener: EventSubscription
    throttledCalcScrollBarSize: DebouncedFunc<() => void>
    scrolling = false;

    constructor(props: ICanvasScrollBarProps) {
        super(props);
        this.state = {
            canvasAddPage: false,
            contentSize: undefined,
        };
        this.getFixedToolPanel();
        this.setCanvasAddPageListener = emitter.addListener('setCanvasAddPage', (isShowMorePage: boolean) => {
            this.setState({ canvasAddPage: !isShowMorePage });
        });
        this.throttledCalcScrollBarSize = throttle(this.calcScrollBarSize.bind(this), 1000, {
            leading: true, // 首次调用执行
            trailing: true  // 保留最后一次调用
        })
    }

    shouldComponentUpdate(nextProps: ICanvasScrollBarProps) {
        if (nextProps.renderMode === 'board' && !this.scrolling) {
            if (
                !this.state.contentSize ||
                this.props.pageInfo.pageNow !== nextProps.pageInfo.pageNow ||
                this.checkPageAssetsChange(
                    nextProps.pages[nextProps.pageInfo.pageNow],
                    this.props.pages[this.props.pageInfo.pageNow],
                )
            ) {
                this.throttledCalcScrollBarSize();
                return true;
            }
        }
        if (
            nextProps.toolPanelWidth !== this.props.toolPanelWidth ||
            nextProps.resourcePanelWidth !== this.props.resourcePanelWidth ||
            nextProps.canvas !== this.props.canvas
        ) {
            this.getPageHeightWidth(nextProps);
            return true;
        }
        return false
    }

    componentWillUnmount(): void {
        this.setCanvasAddPageListener?.remove();
    }

    onVerticalThumbMouseDown = (type: 'vertical'| 'horizontal', e: React.MouseEvent) => {
        e.stopPropagation();
        e.nativeEvent?.stopPropagation();
        this.scrolling = true;
        this.clientX = e.clientX;
        this.clientY = e.clientY;
        this.thumbType = type;
        this.getFixedToolPanel();
        this.getPageHeightWidth(this.props);
        assetManager.setPv_new(7944, { additional: { s0: type } });
        window.addEventListener('mousemove', this.onVerticalThumbMouseMove);
        window.addEventListener('mouseup', this.onVerticalThumbMouseUp);
    };

    onVerticalThumbMouseMove = (e: MouseEvent) => {
        e.stopPropagation();
        const  { canvas, canvasDom, resourcePanelWidth } = this.props;
        const x = this.clientX - e.clientX;
        const y = this.clientY - e.clientY;
        const canvasHeight = canvasDom.current?.clientHeight || 0;
        const maxHeight = canvasHeight - this.bottomGap - this.topGap;
        const canvasWidth = this.canvasWidth || 0;
        const param = 4;

        if (this.thumbType === 'vertical') {
            let canvasY = canvas.y + y / this.canvasHeight * this.pageHeight;
            if (this.props.renderMode === 'board' && this.state.contentSize) {
                canvasY = canvas.y + y / this.canvasHeight * this.state.contentSize.height * canvas.scale;
            } else if (canvasY > this.topGap){
                canvasY = this.topGap
            } else if (canvasY + this.pageHeight <= maxHeight) {
                canvasY = maxHeight - this.pageHeight;
            }
            TemplateCanvasLogic.setCanvasPositionAndSize({y: canvasY})
            this.clientY = e.clientY;
        } else {
            // 水平方向
            const pageWidth = canvas.width * canvas.scale;
            const leftPanelWidth = resourcePanelWidth > 0 ? resourcePanelWidth - 72 : 0;
            let canvasX = canvas.x + x * param;
            if (this.props.renderMode === 'board' && this.state.contentSize) {
                 const moreWidth = this.props.toolPanel.asset_index < 0 && this.props.toolPanel.assets_index?.length === 0
                canvasX = canvas.x + x / (this.canvasWidth - 20 + (moreWidth ? this.props.toolPanelWidth : 0)) * this.state.contentSize.width * canvas.scale;
            } else if (canvasX > (horizontalGap + leftPanelWidth)){
                // 左边
                canvasX = (horizontalGap + leftPanelWidth)
            } else if (canvasX + pageWidth + horizontalGap - leftPanelWidth <= canvasWidth) {
                // 右边
                canvasX = canvasWidth - pageWidth - horizontalGap + leftPanelWidth;
            }
            TemplateCanvasLogic.setCanvasPositionAndSize({x: canvasX})
            this.clientX = e.clientX;
        }
    };

    onVerticalThumbMouseUp = (e: MouseEvent) => {
        e.stopPropagation();
        this.scrolling = false;
        window.removeEventListener('mousemove', this.onVerticalThumbMouseMove);
        window.removeEventListener('mouseup', this.onVerticalThumbMouseUp);
    };

    preventScrollHandler(e: React.WheelEvent) {
        e.stopPropagation()
    }

    getFixedToolPanel() {
        const storageFixedToolPanel = localStorage.getItem('fixedToolPanel');
        this.fixedToolPanel = storageFixedToolPanel ? storageFixedToolPanel === 'true' : true;
        const {TopGap, BaseGap, BottomGap } = CANVAS_GAP;
        this.topGap = this.fixedToolPanel ? BaseGap : TopGap; 
        this.bottomGap = BottomGap;
    }

    getPageHeightWidth(props: ICanvasScrollBarProps) {
        const { canvas, pages, renderMode, canvasDom, parentDom, resourcePanelWidth, toolPanelWidth} = props;
        const widgetHeight = canvas.height * canvas.scale;
        const widgetWidth = canvas.width * canvas.scale + horizontalGap * 2;
        let allPageHeight = 0;
        if (renderMode === 'pull') {
            allPageHeight = widgetHeight
        } else {
            const len = pages.length;
            allPageHeight = widgetHeight * len + BaseGap * (len - 1);
        }
        this.pageHeight = allPageHeight;
        this.pageWidth = widgetWidth;
        this.canvasWidth = canvasDom.current?.clientWidth || 0;
        this.canvasHeight = canvasDom.current?.clientHeight || 0;
        this.wrapWidth = parentDom.current?.clientWidth - 16 || 0;
        
        if (toolPanelWidth) {
            this.canvasWidth -= toolPanelWidth;
        }
        if (resourcePanelWidth) {
            this.canvasWidth -= (resourcePanelWidth - 72);
        }
    }

    calcRotateRect = (size: { x: number; y: number; w: number; h: number }, rotate: number) => {
        const { x, y, w, h } = size;
        const tl = { x, y };
        const tr = { x: x + w, y };
        const bl = { x, y: y + h };
        const br = { x: x + w, y: y + h };
        const matrix = rotateDEG(rotate, x + w / 2, y + h / 2);
        const newTl = applyToPoint(matrix, tl);
        const newTr = applyToPoint(matrix, tr);
        const newBl = applyToPoint(matrix, bl);
        const newBr = applyToPoint(matrix, br);
        const t: number = Math.min(newTl.y, newTr.y, newBl.y, newBr.y);
        const b: number = Math.max(newTl.y, newTr.y, newBl.y, newBr.y);
        const l: number = Math.min(newTl.x, newTr.x, newBl.x, newBr.x);
        const r: number = Math.max(newTl.x, newTr.x, newBl.x, newBr.x);
        return {
            x: l,
            y: t,
            w: r - l,
            h: b - t,
        };
    };
    
    calcScrollBarSize() {
        const { canvas, pages, pageInfo, renderMode } = this.props;
        if (renderMode !== 'board') {
            return;
        }
        let left: number;
        let right: number;
        let top: number;
        let bottom: number;
        pages[pageInfo.pageNow].assets.forEach((asset) => {
            const [ox, oy, ow, oh] = [
                asset.transform.posX,
                asset.transform.posY,
                'container' in asset.attribute
                    ? (asset.attribute.container?.width ?? asset.attribute.width)
                    : asset.attribute.width,
                'container' in asset.attribute
                    ? (asset.attribute.container?.height ?? asset.attribute.height)
                    : asset.attribute.height,
            ];
            const { x, y, w, h } = this.calcRotateRect({ x: ox, y: oy, w: ow, h: oh }, asset.transform.rotate);
            left = left === undefined ? x : Math.min(left, x);
            top = top === undefined ? y : Math.min(top, y);
            right = right === undefined ? x + w : Math.max(right, x + w);
            bottom = bottom === undefined ? y + h : Math.max(bottom, y + h);
        });
        const safeDistance = Math.ceil(500 / canvas.scale);
        this.setState({
            contentSize: {
                x: (left || 0) - safeDistance,
                y: (top || 0) - safeDistance,
                width: (right || 0) - (left || 0) + safeDistance * 2,
                height: (bottom || 0) - (top || 0) + safeDistance * 2,
            }
        })
    }

    checkPageAssetsChange = (prevPage: IPage, currentPage: IPage) => {
        if (prevPage.assets === currentPage.assets) {
            return false;
        }
        if (prevPage.assets.length !== currentPage.assets.length) {
            return true;
        }
        for (let i = 0; i < prevPage.assets.length; i++) {
            // 只判断尺寸信息
            if (
                prevPage.assets[i].attribute.width !== currentPage.assets[i].attribute.width ||
                prevPage.assets[i].attribute.height !== currentPage.assets[i].attribute.height ||
                prevPage.assets[i].transform.posX !== currentPage.assets[i].transform.posX ||
                prevPage.assets[i].transform.posY !== currentPage.assets[i].transform.posY ||
                prevPage.assets[i].transform.rotate !== currentPage.assets[i].transform.rotate ||
                prevPage.assets[i].attribute.container?.width !== currentPage.assets[i].attribute.container?.width ||
                prevPage.assets[i].attribute.container?.height !== currentPage.assets[i].attribute.container?.height
            ) {
                return true;
            }
        }
        return false;
    };

    render() {
        const { canvas, toolPanelWidth, resourcePanelWidth, renderMode, toolPanel } = this.props;
        if (!this.canvasHeight && !this.canvasWidth) return null;
        
        const moreWidth = renderMode === 'board' && toolPanel.asset_index < 0 && toolPanel.assets_index?.length === 0
        
        let wrapWidth = this.wrapWidth - (moreWidth ? 0 : toolPanelWidth);
        let horizontailLeft = 0;
        const leftWidth = resourcePanelWidth - 72;
        const leftPanelWidth = resourcePanelWidth > 0 ? leftWidth : 0;
        if (resourcePanelWidth) {
            wrapWidth -= leftWidth;
            horizontailLeft = leftWidth;
        }
        const canvasWidth = this.canvasWidth;
        const canvasHeight = this.canvasHeight;
        const widgetWidth = this.pageWidth;
        const allPageHeight = this.pageHeight;
        let thumbHeight = 0, thumbWidth = 0;
        let thumbTop = 0, thumbLeft = 0;
        if (renderMode === 'board' && this.state.contentSize) {
            thumbHeight = Math.min(100, (canvasHeight / (this.state.contentSize.height * canvas.scale)) * 100);
            thumbTop = Math.min(
                100 - thumbHeight,
                Math.max(
                    0,
                    (-(this.state.contentSize.y * canvas.scale + canvas.y) /
                        (this.state.contentSize.height * canvas.scale)) *
                        100,
                ),
            );
        } else if (canvasHeight > allPageHeight) {
            thumbHeight = 100
        } else {
            if (canvas.y <= this.topGap) {
                thumbTop = Math.abs(canvas.y - this.topGap) / allPageHeight * 100;
            }
            thumbHeight = canvasHeight / allPageHeight * 100
        }
        if (renderMode === 'board' && this.state.contentSize) {
            thumbWidth = Math.min(100, (wrapWidth / (this.state.contentSize.width * canvas.scale)) * 100);
            thumbLeft = Math.min(
                100 - thumbWidth,
                Math.max(
                    0,
                    (-(this.state.contentSize.x * canvas.scale + canvas.x) /
                        (this.state.contentSize.width * canvas.scale)) *
                        100,
                ),
            );
        } else if (canvasWidth > widgetWidth - 120) {
            thumbWidth = 100
        } else {
            if (canvas.x <= (horizontalGap + leftPanelWidth)) {
                thumbLeft = Math.abs(canvas.x - horizontalGap - leftPanelWidth) / widgetWidth * 100;
                if (canvasWidth - widgetWidth >= canvas.x) {
                    thumbLeft = (widgetWidth - canvasWidth) / widgetWidth * 100;
                }
            }
            thumbWidth = canvasWidth / widgetWidth * 100
        }
        return (
            <div className="canvasScrollWrap"
                onWheel={(e)=>this.preventScrollHandler(e)}
            >
                {
                    thumbHeight !== 100 && (
                        <div className='verticalScrollWrap' style={{transform: `translateX(-${moreWidth ? 0 : toolPanelWidth}px)`}}>
                            <div className='vertical-track' style={{height: canvasHeight - 16}}>
                                <div className='vertical-thumb' 
                                    style={{height: thumbHeight + '%',top: thumbTop + '%'}}
                                    onMouseDown={this.onVerticalThumbMouseDown.bind(this, 'vertical')}
                                ></div>
                            </div>
                        </div>
                    )
                }
                {
                    thumbWidth !== 100 && (
                        <div className='horizontailScrollWrap' style={{width: wrapWidth, left: horizontailLeft, bottom: renderMode === 'pull' || this.state.canvasAddPage ? 102 + 40 : 40}}>
                        <div className='horizontail-track'>
                            <div className='horizontail-thumb' 
                                style={{width: thumbWidth + '%', left: thumbLeft + '%'}}
                                onMouseDown={this.onVerticalThumbMouseDown.bind(this, 'horizontal')}
                            ></div>
                        </div>
                    </div>
                    )
                }

            </div>
        );
    }
}
