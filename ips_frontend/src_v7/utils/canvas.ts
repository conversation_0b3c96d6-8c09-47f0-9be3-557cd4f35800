export const CANVAS_GAP = {
    TopGap: 60,
    BottomGap: 66 - 16,
    BaseGap: 60,
};

export const getLFPanelFixedInfo = () => {
    const subPanelWidth = 360;
    const storageFixedToolPanel = localStorage.getItem('fixedToolPanel');
    const fixedToolPanel = storageFixedToolPanel ? storageFixedToolPanel === 'true' : true;
    const rightLayoutDom = document.querySelector('.rightLayout');
    if (!rightLayoutDom) {
        const hasMenu = /menu=\d+/.test(location.href)
        return {
            isNavOpen: hasMenu ? true : false,
            fixedToolPanel,
            subPanelWidth,
            leftToolWidth: 0,
        };
    }
    const leftToolWidth = rightLayoutDom?.clientWidth || 0;
    const isNavOpen = leftToolWidth - subPanelWidth > 0;
    return { isNavOpen, fixedToolPanel, subPanelWidth, leftToolWidth };
};

export const getBlockInfo = () => {
    const rightToolPanelWidth = 270;
    const { isNavOpen, fixedToolPanel, subPanelWidth, leftToolWidth } = getLFPanelFixedInfo();
    let blockWidth = isNavOpen ? subPanelWidth : 0;
    if (fixedToolPanel) {
        blockWidth = blockWidth + rightToolPanelWidth;
    }
    return {
        blockWidth,
        leftToolWidth,
        rightToolPanelWidth,
        isNavOpen,
        fixedToolPanel,
        subPanelWidth,
    };
};
