import { resetAssetOffset } from '@v7_logic/AssetAddListener/FitPageDomSize';
import { TStoreAction } from '@v7_logic/Interface';

// TODO 移除下列依赖
import { emitter } from '@component/Emitter';
import { assetManager } from '@src/userComponentV6.0/AssetManager';
import { BatchedUpdateAssetsFunName } from '@v7_logic/GroupAndMultipleSelectLogic/config';
import { checkIsKickOut } from '@v7_utils/user';

// 不要在此文件引入 store, 会导致循环引用

const autoSaveTemplArray = [
    'ADD_ELEMENT',
    'ADD_TEXTEDITOR',
    'ADD_SVG',
    'SVG_DRAG_ADD',
    'ADD_BACKGROUND',
    'UPDATE_TEXT',
    'UPDATE_FONTSIZE',
    'UPDATE_ASSETS',
    'UPDATE_ASSETS_FONTSIZE',
    'UPDATE_OPACITY',
    'UPDATE_ROTATE',
    'UPDATE_VALUEHINT',
    'UPDATE_FONTSTYLE',
    'UPDATE_TEXTDECORATION',
    'UPDATE_FONTWEIGHT',
    'UPDATE_ASSETS',
    'UPDATE_WIDTH',
    'UPDATE_HEIGHT',
    'UPDATE_POSY',
    'UPDATE_POSX',
    'UPDATE_TEXTALIGN',
    'UPDATE_LINEHEIGHT',
    'UPDATE_LETTERSPACING',
    'UPDATE_FONTFAMILY',
    'UPDATE_COLOR',
    'UPDATE_COLOR_END',
    'UPDATE_TEXT_SPECIFIC_WORD_EFFECT',
    'UPDATE_TEXT_3D_WORD_EFFECT',
    'UPDATE_LOCKED',
    'COPY_ASSET',
    'DELETE_ASSET',
    'MOVE_ASSET',
    'ASSET_CUT',
    'HORIZONTAL_FLIP',
    'VERTICAL_FLIP',
    'DRAP_ASSET',
    'DRAP_ASSETS',
    'DRAG_POINT',
    'DRAG_POINT_RIGHT',
    'SCALE_CONTAINER_SIZE',
    'DRAG_POINT_LEFT',
    'DRAG_POINT_UP',
    'DRAG_POINT_BOTTOM',
    'DRAG_POINT_RIGHT_BOTTOM',
    'DRAG_POINT_RIGHT_TOP',
    'DRAG_POINT_LEFT_TOP',
    'DRAG_POINT_LEFT_BOTTOM',
    'UPDATE_BACKGROUNDCOLOR_END',
    'UPDATE_BACKGROUNDCOLOR',
    'UPDATE_BACKGROUNDIMAGE',
    'UPDATE_BACKGROUND',
    'UPDATE_INFO_TITLE_AND_SAVE',
    'DEGREES_90_LEFT',
    'DEGREES_90_RIGHT',
    'QUICK_EDITING_UPDATE_TEXT',
    'UPDATE_ASSET_INFO',
    'DELETE_ASSET_KEYDOWM',
    'UPDATE_CANVAS_SIZE',
    'MOVE_ASSET_UP_KEYDOWN',
    'MOVE_ASSET_DOWN_KEYDOWN',
    'MOVE_ASSET_LEFT_KEYDOWN',
    'MOVE_ASSET_RIGHT_KEYDOWN',
    'SITE_SEARCH_TEMPL',
    'UPDATE_TEMPLATEWORD',
    'UPDATE_SVGWORD',
    'UPDATE_WORDARTWORD',
    'UPDATE_IMAGEWORD',
    'UPDATE_BACKGROUNDWORD',
    'UPDATE_SVG_COLORS_INFO',
    'UPDATE_CANVAS_MAP_REPLACE',
    'UPDATE_SVG_COLORS_INFO_END',
    'UPDATE_STROKE_START',
    'UPDATE_STROKE_COLOR',
    'UPDATE_STROKE_END',
    'COMBINATION_ASSET',
    'SPLIT_COMBINATION_ASSET',
    'UPDATE_CONTAINER_CONTENTINFO_END',
    'ADD_PIC',
    'ADD_PERSON',
    'ADD_PHOTOGRAPHY',
    'ADD_CONTAINER',
    'ADD_WORDART',
    'ADD_EMOJI',
    'PIC_DRAG_ADD',
    'ADD_PAGE',
    'PRE_PAGE',
    'NEXT_PAGE',
    'COPY_PAGE',
    'DEL_PAGE',
    
    'UPDATE_TEXT_SPECIFIC_WORD_COLORMATCH',
    'UPDATE_TEXT_SPECIFIC_WORD_LINEARGRADIENT',
    'UPDATE_TEXT_SPECIFIC_SIZES',
    'UPDATE_TEXT_WRITINGMODE',
    'ADD_GROUP_WORD',
    'DRAP_ASSET_END',
    'MOVE_ASSET_END',
    'ALIGN_DISTRIBUTE',
    'UPDATE_TEXT_SPECIFIC_WORD_COLORMATCH_NEW_END',
    'RESET_ASSET',
    // table
    'ADD_TABLE',
    'ADD_TABLE_END',
    'UPDATE_TABLE_TD_SCALE',
    'UPDATE_TABLE_SCALE',
    'UPDATE_TABLE_SELECTED_KEY',
    'UPDATE_TABLEBORDER_COLOR',
    'UPDATE_TABLEBG_COLOR',
    'UPDATE_TABLETEXT_COLOR',
    'UPDATE_TABLE_STYLE',
    'UPDATE_TABLETEXT_FONTSIZE',
    'UPDATE_TABLETEXT_FONTWEIGHT',
    'UPDATE_TABLETEXT_FONTSTYLE',
    'UPDATE_TABLETEXT_TEXTALIGN',
    'UPDATE_TABLETEXT_TEXTDECORATION',
    'UPDATE_TABLETEXT_WRITINGMODE',
    'UPDATE_TABLETEXT_LINEHEIGHT',
    'UPDATE_TABLETEXT_LETTERSPACING',
    'UPDATE_TABLETEXT_OPACITY',
    'UPDATE_TABLE_FONTFAMILY',
    'UPDATE_TABLE_ALLCELLS_ATTRIBUTE',
    'UPDATE_TABLETEXT',
    'UPDATE_TABLE_HEIGHT_AND_ROWHEIGHT',
    'UPDATE_TABLERIGHTMENU_MERGE',
    'UPDATE_TABLERIGHTMENU_SPLIT',
    'UPDATE_TABLERIGHTMENU_INSERTLEFT',
    'UPDATE_TABLERIGHTMENU_INSERTRIGHT',
    'UPDATE_TABLERIGHTMENU_INSERTTOP',
    'UPDATE_TABLERIGHTMENU_INSERTBOTTOM',
    'UPDATE_TABLERIGHTMENU_DELETEROW',
    'UPDATE_TABLERIGHTMENU_DELETECOL',
    'TABLE_CLEARSELECTCONTENTS',
    'PASTE_TABLE_SELECTED_TDS',
    'DRAGE_TABLE_COL',
    'DRAGE_TABLE_ROW',
    'CUT_TABLE_SELECTED_TDS',
    // table
    // 电商详情
    'MUTISIZE_RIGHTMENU_ITEM_DELETE',
    'MUTISIZE_RIGHTMENU_ITEM_ADD_EMPTY',
    'MUTISIZE_RIGHTMENU_ITEM_PASTE',
    'MUTISIZE_RIGHTMENU_ITEM_CUT',
    'MUTISIZE_UPDATE_FLOOR_ASSETS_HEIGHT',
    'MUTISIZE_PREVIEW_DRAG_CHANGE_SEQUENCE',
    // 电商详情

    // 3D text
    'UPDATE_DEPTH',
    'UPDATE_3D_MATERIAL',
    // 图表
    'ADD_CHART',
    'ADD_CHART_END',
    'ADD_E_CHART',

    'UPDATE_CHART_USERDATA',
    'UPDATE_CHART_USERDATA_MAP',
    'DELETE_CHART_DETEIL_ROW',
    'DELETE_CHART_DETEIL_COL',
    'ADD_CHART_DETEIL_ROW',
    'ADD_CHART_DETEIL_COL',
    'EDIT_CHART_MODAL_CONFIRM_ADJUST',

    'UPDATE_CHART_BASE_ID',
    'UPDATE_CHART_ACCUMULATION',
    // 'UPDATE_CHART_COLORTABLE',
    'UPDATE_CHART_ASSET_COLOR_TABLE',

    'UPDATE_IMAGE_FILTER',
    'UPDATE_TOOLPANEL_IMAGE_FILTER',
    'UPDATE_IMAGEEFFECTS',
    'UPDATE_IMAGEEFFECTS_END',
    'UPDATE_IMAGE_RENDER_CACHE',
    // 'MUTISIZE_SET_FLOOR_TEMPLATE_CURRENTINDEX',
    'SELECT_PAGE_TEMPLATE',
    'MUTISIZE_RIGHTMENU_ITEM_ADD_EMPTY_TEMPLATE',
    'MUTISIZE_RIGHTMENU_ITEM_COPY_EMPTY_TEMPLATE',
    'MUTISIZE_RIGHTMENU_ITEM_DELETE_EMPTY_TEMPLATE',
    'MUTISIZE_RIGHTMENU_ITEM_REPLACE_TEMPLATE',
    'SORT_PAGES',
    // 动画
    'UPDATE_PAGE_ANIMATION',
    'UPDATE_AINMATION_PAGETIME',

    // 音频
    'UPDATE_USER_AUDIO',
    'REMOVE_USER_AUDIO',

    // video e
    'ADD_VIDEOE',
    'UPDATE_VIDEOE_SLICE',
    'UPDATE_VIDEOE_OPACITY_END',
    'UPDATE_VIDEOE_VOLUME_END',
    'REPLACE_VIDEOE',
    'UPDATE_PAGETIME_ON_UPDATE_VIDEOE',
    'UPDATE_PAGE_ALL_VIDEOE_SLICE',


    // qrcode
    "ADD_QRCODE",
    "UPDATE_QRCODE_INFO",
    "UPDATE_QRCODE_RENDER_CACHE",

    // frame 相框
    "ADD_FRAME",
    "ADD_FRAME_BY_IMAGE",
    "UPDATE_FRAME_INFO",
    'UPDATE_FRAME_BACKGROUND_COLORS_INFO',
    'UPDATE_FRAME_CONTENT_COLORS_INFO',
    'UPDATE_FRAME_CONTENT_IMAGE_INFO',
    'UPDATE_FRAME_BACKGROUND_IMAGE_INFO',
    'UPDATE_FRAME_ATTRIBUTE_INFO',
    'FRAME_DRAG_ADD',

    // 裁剪
    'UPDATE_ASSET_CONTAINER',
    'UPDATE_ASSET_CONTAINER_VIEWBOX',
    'UPDATE_ASSET_CONTAINER_ISEDIT_FALSE',
    'CLIP_IMAGE_END',
    'DEL_ASSET_CONTAINER',

    'UPDATE_IS_FOLLOW_EFFECT',
    'SET_ELEMENT_ANIMATION',

    // 协作
    'ADD_PAGE_HASH',
    'REMOVE_PAGE_HASH',
    'UPDATE_ASSETS_HASH',
    // 组合或多选
    ...Object.values(BatchedUpdateAssetsFunName),
    // 格式刷
    'UPDATE_TEXT_STYLE_BY_BRUSH_STYLE',

    'UPDATE_TEXT_EDIT_STATUS',

    // 拖拽修改画布尺寸
    'DRAG_UPDATE_CANVAS_SIZE_START',
    'DRAG_UPDATE_CANVAS_SIZE_END',
    "MULT_UPDATE_TEXT_COLOR",

    // 背景编辑缩放
    'DRAG_POINT_START',
    'UPDATE_PAGE_PPT_MARK',

    // 文字划重点
    "UPDATE_TEXT_EMPHATICMARK",
    "UPDATE_IMAGE_ENHANCE",
    "REPLACE_AND_ADPATER_TEXT_CONTENT",
    
    "WORDARTS_ASSET_REPLACE",

    // 链接线
    "ADD_LINE",
    "UPDATE_LINE_POSITION",
    "LINE_BIND_ASSET",
    "UPDATE_LINE_COLORS",
    "UPDATE_LINE_ARROW",
    "UPDATE_STROKE_START",
    "UPDATE_LINE_WIDTH",
    "UPDATE_LINE_LINK",

    // 白板模式
    'BOARD_ADD_TEMPLATE_ASSETS',
];

const rightLayoutHide = [
    'ADD_ELEMENT',
    'ADD_TEXTEDITOR',
    'ADD_SVG',
    'ADD_PIC',
    'ADD_WORDART',
    'ADD_EMOJI',
    'ADD_PERSON',
    'ADD_PHOTOGRAPHY',
    'ADD_BACKGROUND',
    'UPDATE_BACKGROUNDCOLOR',
    'UPDATE_BACKGROUNDIMAGE',
    'ADD_GROUP_WORD',
    'UPDATE_TEMPLATEWORD',
    'UPDATE_SVGWORD',
    'UPDATE_WORDARTWORD',
    'UPDATE_IMAGEWORD',
    'UPDATE_BACKGROUNDWORD',
    'UPDATE_PAGE_ANIMATION',
    'UPDATE_LOCKED',
    'UPDATE_BACKGROUND',

    // 协作
    'COLLABORATIVE_UPDATE',

];

export class AutoSave {
    private static autoSaveTemplTimers: number;
    private static notLoginTemplTimers: number;

    static isSaveAction(actionType: string): boolean {
        return autoSaveTemplArray.includes(actionType);
    }

    static save(
        newState: any,
        action: {
            is_new: boolean;
            origin?: 'paintOnCanvas';
            sub_common: TStoreAction[];
            type: string;
        },
    ): void {
        if (autoSaveTemplArray.indexOf(action.type) >= 0) {
            checkIsKickOut().then(() => {
        
            resetAssetOffset();
            /*IN自动保存START*/
            let autoSaveFlag = 0;
            if (action.sub_common) {
                for (const storeAction of action.sub_common) {
                    if ('autoSave' in storeAction.params && typeof storeAction.params.autoSave === 'number') {
                        autoSaveFlag = storeAction.params.autoSave;
                        break;
                    }
                }
            }
            // if (typeof action.asset !== 'undefined' && typeof action.asset.autoSaveFlag !== 'undefined') {
            //     autoSaveFlag = action.asset.autoSaveFlag;
            // }
            if (autoSaveFlag !== 2 && newState.user.userId > 0) {
                if (this.autoSaveTemplTimers) {
                    window.clearTimeout(this.autoSaveTemplTimers);
                }

                if (!rightLayoutHide.includes(action.type) && !newState.isDesigner) {
                    // emitter.emit('rightLayoutHideClick', 'hide');
                }
                const is_super_user = newState.isSuperUser;
                this.autoSaveTemplTimers = window.setTimeout(() => {
                    if (!is_super_user) {
                        emitter.emit('autoSaveOperationTempl');
                    }
                }, 1000);
            } else {
                if (this.notLoginTemplTimers) {
                    window.clearTimeout(this.notLoginTemplTimers);
                }
                this.notLoginTemplTimers = window.setTimeout(() => {
                    emitter.emit('autoSaveOperationTempl', 'notLogin');
                }, 1000);
                // 未登录状态提示
                // emitter.emit('autoSaveOperationTempl', 'notLogin');
            }
                /*IN自动保存END*/
            }, () => {
                return
            })
        }
        if (autoSaveTemplArray.includes(action.type) && !(newState.user.userId > 0)) {
            if (this.autoSaveTemplTimers) {
                window.clearTimeout(this.autoSaveTemplTimers);
            }
            this.autoSaveTemplTimers = window.setTimeout(() => {
                assetManager.setPushOperationRecord();
            }, 1000);
        }
        /*自动保存END*/
    }
}
