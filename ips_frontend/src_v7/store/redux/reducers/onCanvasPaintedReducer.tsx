import produce, { setAutoFreeze } from 'immer';
import { IGenerateImage, IGenerateImages } from '@src/userComponentV6.0/toolV6.0/GeneratePicPopup/type';
import { CanvasLogic } from '@v7_logic/CanvasLogic';

// import { onCanvasPainted as onCanvasPaintedOld } from '@reducers/onCanvasPaintedReducer';
import {
    IAsset,
    ICanvasPainted,
    ICanvas,
    IWork,
    IPageInfo,
    IUpdateTarget,
    IToolPanel,
    IColor,
    IPageAttr,
    IMutiSizeInfo,
    IUserAsset,
    ITeamUserAsset,
    IAudio,
    IUserListVideoE,
    DeepPartial,
    IUserInfo,
} from '@v7_logic/Interface';
import { ESelectType } from '@v7_logic/Enum';
import { SortAsset, AutoSave, RedoAndUndo } from '@v7_store/logic/onCanvasPaintedReducer';
import { AssetHelper } from '@v7_logic/AssetHelper';
import { CanvasPaintedHelper } from '@v7_logic/CanvasPaintedHelper';
import { klona as cloneDeep } from 'klona';

import { checkoutAssetIsInArea } from '@src/userComponentV6.0/Function';
import { AssetClass } from '@v7_store/logic/onCanvasPaintedReducer/AssetClass';
import { DocSync, ICooperationUsers, ICollaborativeUserMap, CollaborativeLogic, ESelectAction, ITalkingUser } from '@v7_collaborativeService/index';
import { DocHash } from '@v7_logic/DocHash';

// TODO 移除下方依赖
import { emitter } from '@src/userComponentV6.0/Emitter';
import { generatePageTag } from '@src/userComponentV6.0/Function';
import { canvasStore } from '../store';
import  { IAssetTextBrushStatusEnum } from '@tgs/types';

interface AnyObjStruct {
    [key: number]: any;
    [key: string]: any;
}

export interface IPaintOnCanvasState {
    // jobId: string,
    // assetCutBackup: AnyObjStruct,
    // backgroundList: any[],
    // specificWordList: any[],
    // userAssetList: any[],
    // userTeamAssetsList: any[],
    // userAssetListPage: number,
    // imageEffectList: any[],
    // containerList: any[],
    scrollTop: {
        sourceMaterial: {
            oneType?: number;
            twoType?: number;
        };
        pic: {
            oneType?: number;
            twoType?: number;
        };
        gallery: {
            oneType?: number;
            twoType?: number;
        };
        emoji: {
            oneType?: number;
        };
        svg: {
            SVGK1: {
                oneType?: number;
                twoType?: number;
            };
            SVGK2: {
                oneType?: number;
            };
        };
    };
    canvas_dom: HTMLDivElement | '';
    canvas_dom_info: {
        class_name?: string;
        is_exist?: boolean;
    };
    canvas_wrapper_dom_info: {
        class_name?: string;
        is_exist?: boolean;
    };
    toolPanel: IToolPanel;
    isEdit: boolean;
    isDesigner: boolean;
    isSuperUser: boolean;
    GroupWordUser: boolean;
    isDocDesigner: boolean; // 是否是文档编辑器设计师端
    canvas: ICanvas;
    work: IWork;
    pageAttr: IPageAttr;
    pageInfo: IPageInfo;
    user: IUserInfo;
    info: {
        title: string;
        description: string;
        kid_1: number;
        kid_2: number;
        kid_3: number;
    };
    copyAssetUniqueTimeStr: number;
    editorTag?: string;
    rt_isMutiSizeInfo: IMutiSizeInfo[];
    rt_mutisize_current_selected_tid: string;
    rt_mutisize_subtemplates: {
        audit_through?: number;
        full_info: {
            canvas: ICanvas;
            work: IWork;
            pageAttr: IPageAttr;
            preview: string;
        };
        link_tid: string;
        tid: string | number;
        width: number;
        height: number;
    }[];
    rt_mutisize_user_show: boolean;
    rt_mutisize_current_selected_kidInfo: {
        kid_1?: number;
        kid_2?: number;
    };
    rt_first_save_recorder: Record<string, string>;
    userAssetList: IUserAsset[];
    userTeamAssetsList: ITeamUserAsset[];
    float_state: 0 | 1;
    canvasWaterMaskFlag: boolean;
    rt_bleeding_line: number | boolean;
    userAudioList: IAudio[];
    userVideoEList: IUserListVideoE[];
    userGifList: IUserListVideoE[];
    createTime: number;
    /* 协作 */
    cooperationUsers: ICooperationUsers[];
    collaborativeUserMap: ICollaborativeUserMap[];
    talkingUsers: ITalkingUser[];
    /* 协作 */
    rt_loadedTextFontClass: Record<string, boolean>;
    rt_is_online_detail_page: boolean; // 店铺首页
    /* 单页多页切换 */
    rt_canvas_render_mode: '' | 'pull' | 'board';
    /* 编辑器类型 dom或者canvas*/
    /* 画布加载 */
    canvasLoadingStatus: boolean;
    rt_editor_type: string;
    page_map: Record<string, string>;
    [key: number]: any;
    [key: string]: any;
    // 智能设计相关
    isAiDesign: boolean;
}

// 数据初始化 START
const INIT_STATE: IPaintOnCanvasState = {
    scrollTop: {
        sourceMaterial: {
            oneType: 0,
            twoType: 0
        },
        pic: {
            oneType: 0,
            twoType: 0
        },
        gallery: {
            oneType: 0,
            twoType: 0
        },
        emoji: {
            oneType: 0
        },
        svg: {
            SVGK1: {
                oneType: 0,
                twoType: 0
            },
            SVGK2: {
                oneType: 0
            }
        }
    },
    /* Store 适配器临时放置 START */
    is_new_life: false,
    mutipleChioceStyle: { info: { display: 'none' } },
    canvas_dom: '',
    canvas_dom_info: {},
    canvas_wrapper_dom_info: {},
    /* Store 适配器临时放置 END */
    // 画板水印
    canvasWaterMaskFlag: true,
    jobId: '',
    /*元素裁剪备份*/
    assetCutBackup: {},
    /*背景列表*/
    backgroundList: [],
    /*特效字列表*/
    specificWordList: [],
    userAudioList: [],
    userAssetList: [],
    userTeamAssetsList: [],
    userAssetListPage: 1,
    userTeamAssetsListPage: 1,
    userVideoEList: [],
    userGifList: [],
    imageEffectList: [],
    containerList: [],
    tempDownloadInterval: '',
    isDownloadFlag: 0,
    startDownloadTime: new Date().getTime(),
    copyAssetUniqueTimeStr: new Date().getTime(),
    containerEditorBak: '',
    isContainerEditor: false,
    containerAddedAsset: '',
    isEdit: false,
    isDesigner: false,
    isAiDesign: false,
    isSuperUser: false,
    GroupWordUser: false,
    isDocDesigner: false,
    isEffectTemplate:false, // 是否是效果模板
    pageInfo: {
        pageNow: 0,
    },
    canvasInfo: {
        isRemake: 0,
    },
    canvas: {
        height: 0,
        width: 0,
        scale: 1,
        backgroundColor: {
            r: 255,
            g: 255,
            b: 255,
            a: 1,
        },
    },
    work: {
        nameSalt: {
            salt: 0,
        },
        meta: {
            title: 'test title',
        },
        pages: [
            {
                assets: [],
                backgroundColor: {
                    r: 255,
                    g: 255,
                    b: 255,
                    a: 1,
                },
            },
        ],
    },
    pageAttr: {
        backgroundColor: [],
        backgroundImage: [
            {
                resId: '',
            },
        ],
        backgroundOpacity:[false],
    },
    page_map: {},
    common: {
        canvasCPX: 0,
        canvasCPY: 0,
    },
    copy: '',
    toolPanel: {
        asset: undefined,
        assets: [],
        assetsInfo: undefined,
        isQuickEdit: false,
        layersHoverAssets: '',
        reline: undefined,
        asset_index: -1,
        assets_index: [],
        select_type: ESelectType['unset'],
        textStyleBrushStatus: IAssetTextBrushStatusEnum.EMPTY, //文字样式格式刷状态
    },
    search: {
        imageWord: '',
        imagePage: 2,
        imageK1: 0,
        imageK2: 0,
        imageK3: 0,
        imageTag: 0,
        backgroundWord: '',
        backgroundPage: 2,
        backgroundK1: 0,
        backgroundK2: 0,
        backgroundK3: 0,
        backgroundStyle: 0,
        backgroundRatio: -1,
        backgroundScene: 0,
        assetTagList: [],
        templateWord: '',
        templateK1: 0,
        templateK2: 0,
        templateK3: 0,
        templatePage: 2,
        TemplateRatioId: -1,
        SVGWord: '',
        SVGK2: 0,
        SVGPage: 2,
    },
    info: {
        title: '未命名',
        description: '',
        kid_1: 0,
        kid_2: 0,
        kid_3: 0,
    },
    undo: '',
    successImgPath: '',
    dragAssetFlag: '',
    recommendFont: [],
    auxiliaryLine: {},
    operationRecord: {
        startDate: Math.floor(new Date().valueOf() / 1000),
        endDate: Math.floor(new Date().valueOf() / 1000),
        doc: [],
        preState: {},
        isDownload: 0,
        assetToCanvas: 0,
        source: 0,
        re_edit: 0,
    },
    operationRecordNum: 0,
    user: {
        userId: '0',
        vip: 0,
        userType: '',
        COPY_TEMPLATE_USER: false,
        pointNum: 0,
        fixNum: 0,
        wordsNum: 0,
        uploadTotal: 0,
        uploadLimit: 0,
        uploadAITotal: 0,
    },
    reline: [],
    relineNum: 0,
    QRcodeDoc: {
        text: '',
        forecolor: '',
        bg: {
            r: 255,
            g: 255,
            b: 255,
            a: 1,
        },
        transparent: 0,
        fg: {
            r: 0,
            g: 0,
            b: 0,
            a: 1,
        },
        icon: '',
    },
    rt_loadedTextFontClass: {},
    createTime: Math.round(new Date().getTime() / 1000),
    doc_createTime: new Date().getTime(),
    rt_base64_cookie_tag: '',
    rt_isMutiSizeTemplate: false,
    rt_isMutiSizeInfo: [],
    rt_mutisize_current_selected_tid: '',
    rt_mutisize_current_selected_kidInfo: {},
    rt_mutisize_subtemplates: [],
    rt_mutisize_user_show: true,
    //电商详情页
    rt_is_online_detail_page: false,
    rt_detail_page_click_floor_position: 'canvas', //点击楼层来源
    online_detail_rightaction_params: {
        copyIndex: -1, //复制的楼层index
        cutAssets: [], //被剪切的楼层元素
        cutFloorHeight: 0, //被剪切的楼层高度
    },
    online_detail_single_floor_uploaded_ids: [], //各个楼层单个提交后，保存具体的楼层模板ID
    //电商详情页
    /* 编辑器优化 */
    rt_current_is_moving_asset: false, //当前是否在拖动
    rt_current_is_drag_asset: false,
    rt_current_is_rotate_asset: false,
    rt_first_load_is_done: false, //记录第一次加载完成
    //记录模板/关联模板首次保存时生成的id,用于校验用户保存时是否正确带入id
    rt_first_save_recorder: {},
    float_state: 0,
    /* 编辑器优化end */
    rt_bleeding_line:false,
    rt_animateFrame: undefined,
    rt_animatieTime: undefined,
    rt_previewFrame: undefined,
    rt_is_preview_status: false,
    rt_is_playing_status: false,
    rt_is_follow_effect: false, // 特效是否跟随动效
    rt_isPauseCanvasEffect: false,
    rt_isTextFocus: false,
    rt_canvas_render_mode: '',
    rt_editor_type: 'dom',  
    canvasLoadingStatus: true, 
    // 协作
    cooperationUsers: [],
    collaborativeUserMap: [],
    talkingUsers:[], // 语音
    // 协作
    newTemplate: false,
};
// 数据初始化 END

interface ActionSubParamsStruct {
    type: string;
    params: {
        [key: number]: any;
        [key: string]: any;
    };
}

type ActionParamsStruct = ActionSubParamsStruct[];

export function paintOnCanvas(
    type: string,
    sub_common: ActionParamsStruct | any = [],
    params: {
        is_new?: boolean;
        isAutoSave?: boolean;
    } = {},
) {
    const { is_new=false, isAutoSave=true } = params;
    if (is_new === true && sub_common instanceof Array) {
        return {
            type: type,
            sub_common: sub_common,
            origin: 'paintOnCanvas',
            is_new: true,
            isAutoSave: isAutoSave
        };
    } else {
        return {
            type: type,
            asset: sub_common,
            origin: 'paintOnCanvas',
            is_new: false,
        };
    }
}

export function onCanvasPainted(
    state = INIT_STATE,
    master_action: {
        type: string;
        sub_common?: ActionParamsStruct | any;
        asset?: AnyObjStruct;
        is_new?: boolean;
        isAutoSave?: boolean;
    },
): IPaintOnCanvasState {
    try {
        const { is_new = false, sub_common, isAutoSave=true } = master_action;

        // if (!is_new) {
        //     return onCanvasPaintedOld(state as any, master_action) as unknown as IPaintOnCanvasState;
        // }
        // 兼容老版本的 toolPanel 选中格式 START
        // 兼容老版本的 toolPanel 选中格式 END

        setAutoFreeze(false);

        // 目前只用到了 {user, isDesigner, isSuperUser} = state
        isAutoSave && AutoSave.save(state, master_action as any);
        AutoSave.isSaveAction(master_action.type) && RedoAndUndo.record(state, master_action as any);

        const next_state = produce(state, (new_state: typeof state) => {
            // 符合指令处理（sub_common） START
            const { sub_common } = master_action;
            sub_common && sub_common.forEach((action: ActionSubParamsStruct) => {
                switch (action.type) {
                    /* TODO 独立成单独 store */
                    /*更新编辑器是否是设计师编辑器*/
                    case 'updateIsDesigner': {
                        let is_designer = true;
                        if (action.params.is_designer != undefined) {
                            is_designer = action.params.is_designer;
                        }
                        new_state.isDesigner = is_designer;
                        break;
                    }
                    case 'updateIsAiDesign': {
                        new_state.isAiDesign = !!action.params.isAiDesign;
                        break;
                    }
                      /*更新当前模板是否是特效模板*/
                    case 'updateIsEffectTemplate': {
                        let isEffectTemplate = true;
                        if (action.params.isEffectTemplate != undefined) {
                            isEffectTemplate = action.params.isEffectTemplate;
                        }
                        new_state.isEffectTemplate = isEffectTemplate;

                        // return newState;
                        break;
                    }
                    case 'updateGroupWordUser': {
                        new_state.GroupWordUser = action.params.isGroupWordUser || false;
                        break;
                    }

                    case 'updateDocDesigner': {
                        new_state.isDocDesigner = action.params.isDocDesigner || false;
                        break;
                    }
                    case 'updateVersion': {
                        new_state.version = action.params.version
                        break;
                    }
                    

                    case 'updateIsSuperUser': {
                        new_state.isSuperUser = action.params.isSuperUser || false;
                        break;
                    }
                    /* 设置画布 DOM */
                    case 'setCanvasDom': {
                        new_state.canvas_dom = action.params.canvas_dom;

                        break;
                    }
                    /* 设置画布 DOM Info */
                    case 'setCanvasDomInfo': {
                        Object.assign(new_state.canvas_dom_info, {
                            class_name: action.params.class_name,
                            is_exist: action.params.is_exist,
                        });
                        break;
                    }
                    /* 设置中心全画布 DOM Info */
                    case 'setCanvasWrapperDomInfo': {
                        Object.assign(new_state.canvas_wrapper_dom_info, {
                            class_name: action.params.class_name,
                            is_exist: action.params.is_exist,
                        });

                        break;
                    }
                    /*更新用户ID*/
                    case 'updateUserid': {
                        new_state.user.pre_userId = new_state.user.userId;
                        // newState.user.userId = action.asset.userId
                        // newState.user.vip = action.asset.vip
                        // newState.user.created = action.asset.created
                        // newState.user.userType = action.asset.userType;
                        // newState.user.DESIGNER_EDITOR_ASSET_USER = action.asset.DESIGNER_EDITOR_ASSET_USER;
                        // newState.user.super_designer = action.asset.super_designer;
                        // newState.user.isNewUser = action.asset.isNewUser;
                        // newState.user.GROUP_WORD_USER = action.asset.GROUP_WORD_USER;
                        // newState.user.COPY_TEMPLATE_USER = action.asset.COPY_TEMPLATE_USER;
                        // newState.user.daysRemaining = action.asset.daysRemaining;
                        // newState.user.downloadable = action.asset.downloadable;
                        // newState.user.userName = action.asset.username;
                        // newState.user.userPayCount = action.asset.userPayCount;
                        // newState.user.userPay53Count = action.asset.userPay53Count;
                        // newState.user.downloadNum = action.asset.downloadNum;
                        new_state.user = { ...new_state.user, ...action.params };

                        break;
                    }
                    /*更新用户ai相关信息*/
                    case 'updateUserAiInfo': {
                        new_state.user = { ...new_state.user, ...action.params };
                        break;
                    }
                     /*更新用户ai相关信息*/
                     case 'updateUserUploadInfo': {
                        new_state.user = { ...new_state.user, ...action.params };
                        break;
                    }
                    /* TODO 独立成单独 store */

                    /*撤销*/
                    case 'undo': {
                        if (RedoAndUndo.getPastLength() > 0) {
                            const item = RedoAndUndo.getLastestPast({
                                work: new_state.work,
                                canvas: new_state.canvas,
                                pageAttr: new_state.pageAttr,
                                pageInfo: new_state.pageInfo,
                                toolPanel: new_state.toolPanel
                            })
                            new_state.work = item.work;
                            new_state.canvas = item.canvas;
                            new_state.pageAttr = item.pageAttr;
                            new_state.pageInfo = item.pageInfo;
                            new_state.toolPanel = item.toolPanel;
                            // new_state.toolPanel.asset = undefined;
                            // new_state.toolPanel.assets = [];
                            // new_state.toolPanel.asset_index = -1;
                            // new_state.toolPanel.assets_index = [];

                            /* 多人协作同步上一步下一步 */
                            const userCount = new_state.cooperationUsers.length;
                            if (userCount > 0) {
                                const target: Parameters<typeof DocSync.Doc.coverDoc>[1]['target'] = {
                                    changes: {
                                        work: item.work,
                                        canvas: item.canvas,
                                        pageAttr: item.pageAttr,
                                        // pageInfo: item.pageInfo,
                                        toolPanel: {
                                            asset: '',
                                            assets: [],
                                            asset_index: -1,
                                            assets_index: [],
                                            select_type: ESelectType.unset,
                                        },
                                    },
                                };
                                DocSync.Doc.coverDoc(master_action.type, {
                                    target,
                                });
                            }
                            /* 多人协作同步上一步下一步 */
                        }

                        new_state.undo = new Date().valueOf();

                        emitter.emit('InfoBarUpdateState');

                        break;
                    }
                    /*重做*/
                    case 'redo': {
                        if (RedoAndUndo.getFutureLength() > 0) {
                            const item = RedoAndUndo.getLastestFuture({
                                work: new_state.work,
                                canvas: new_state.canvas,
                                pageAttr: new_state.pageAttr,
                                pageInfo: new_state.pageInfo,
                                toolPanel: new_state.toolPanel
                            })

                            new_state.work = item.work;
                            new_state.canvas = item.canvas;
                            new_state.pageAttr = item.pageAttr;
                            new_state.pageInfo = item.pageInfo;
                            new_state.toolPanel = item.toolPanel;
                            // new_state.toolPanel.asset = undefined;
                            // new_state.toolPanel.assets = [];
                            // new_state.toolPanel.asset_index = -1;
                            // new_state.toolPanel.assets_index = [];

                            /* 多人协作同步上一步下一步 */
                            const userCount = new_state.cooperationUsers.length;
                            if (userCount > 0) {
                                const target: Parameters<typeof DocSync.Doc.coverDoc>[1]['target'] = {
                                    changes: {
                                        work: item.work,
                                        canvas: item.canvas,
                                        pageAttr: item.pageAttr,
                                        // pageInfo: item.pageInfo,
                                        toolPanel: {
                                            asset: '',
                                            assets: [],
                                            asset_index: -1,
                                            assets_index: [],
                                            select_type: ESelectType.unset,
                                        },
                                    },
                                };
                                DocSync.Doc.coverDoc(master_action.type, {
                                    target,
                                });
                            }
                            /* 多人协作同步上一步下一步 */
                        }

                        emitter.emit('InfoBarUpdateState');

                        break;
                    }

                    // 元素 css index 排序
                    case 'moveAssetIndex': {
                        // TODO 处理类型
                        AssetHelper.moveAssetIndex(new_state, action.params as any);
                        // 同步更新放在 case: resetAssetIndex 中进行
                        break;
                    }
                    // 元素 css index slider 排序
                    case 'moveAssetIndexNew': {
                        AssetHelper.moveAssetIndexNew(new_state, action.params as any);
                        // 同步更新放在 case: resetAssetIndex 中进行
                        break;
                    }

                    case 'changeTextFontLoadState': {
                        const { className } = action.params;
                        if (!new_state.rt_loadedTextFontClass[className]) {
                            new_state.rt_loadedTextFontClass[className] = true;
                        }
                        break;
                    }

                    case 'pasteAsset': {
                        const diff = AssetHelper.pasteAsset(new_state);
                        /* 同步复制 */
                        if (new_state.cooperationUsers.length > 0) {
                            DocSync.Assets.addAssets(master_action.type, {
                                targets: diff.map((a) => {
                                    return {
                                        className: a.meta.className,
                                        pageHash: a.meta.hash?.split('-')[0],
                                        assetHash: a.meta.hash,
                                        changes: a,
                                    };
                                }),
                            })
                        }
                        /* 同步复制 */
                        break;
                    }

                    case 'copyToolPanelAsset': {
                        const diff = AssetHelper.copyToolPanelAsset(new_state);
                        /* 同步复制 */
                        if (new_state.cooperationUsers.length > 0) {
                            DocSync.Assets.addAssets(master_action.type, {
                                targets: diff.map((a) => {
                                    return {
                                        className: a.meta.className,
                                        pageHash: a.meta.hash?.split('-')[0],
                                        assetHash: a.meta.hash,
                                        changes: a,
                                    };
                                }),
                            })
                        }
                        /* 同步复制 */
                        break;
                    }

                    case 'selectReline': {
                        new_state.containerEditorBak = '';
                        new_state.isContainerEditor = false;
                        if (action.params.reline) {
                            new_state.toolPanel.reline = action.params.reline;
                        } else {
                            new_state.toolPanel.reline = undefined;
                        }

                        new_state.toolPanel.asset = undefined;
                        new_state.toolPanel.assets = [];
                        new_state.toolPanel.assetsInfo = undefined;
                        new_state.toolPanel.isQuickEdit = false;
                        new_state.toolPanel.asset_index = undefined;
                        new_state.toolPanel.assets_index = [];
                        break;
                    }
                    case 'blurReline': {
                        new_state.containerEditorBak = undefined;
                        new_state.isContainerEditor = false;
                        new_state.toolPanel.reline = undefined;

                        break;
                    }

                    /* 修改 canvas size */
                    case 'updateCanvasSize': {
                        // TODO 与 updateCanvasInfo 合并
                        const { width, height, showUnit } = action.params;
                        const { canvas, rt_is_online_detail_page, pageInfo } = new_state;
                        const canvasSize = CanvasLogic.getCanvasLimitSize();
                        if (width >= canvasSize.w.px.min && height >= canvasSize.h.px.min && width <= canvasSize.w.px.max && height <= canvasSize.h.px.max) {
                            if (rt_is_online_detail_page) {
                                const { floorCutting = [] } = canvas;
                                // TODO 电商分页相关逻辑
                                const allHeight = floorCutting.reduce((sum, v) => sum + v.height, 0);
                                new_state.canvas.height = allHeight;

                            } else {
                                AssetHelper.onCanvasSizeChange(width, height, new_state.canvas, new_state.work);
                            }

                            new_state.canvas.width = width;
                            // 更新单位
                            new_state.canvas.showUnit = showUnit
                            if (rt_is_online_detail_page && canvas.floorCutting && canvas.floorCutting.length > 0) {
                                const { rt_online_detail_current_index = 0} = pageInfo ;

                                const _floorCuts = cloneDeep(canvas.floorCutting) ,
                                canvasHeight = canvas.height ,
                                distanceHeight = action.params.height - _floorCuts[rt_online_detail_current_index || 0].height ;

                                _floorCuts[pageInfo.rt_online_detail_current_index || 0].height = action.params.height;
                                new_state.canvas = {
                                    ...new_state.canvas,
                                    floorCutting : _floorCuts,
                                    height : canvasHeight + distanceHeight
                                }
                            } else {
                                new_state.canvas.height = height;
                            }
                            /* 同步 canvas 更新 */
                            if (new_state.cooperationUsers.length > 0) {
                                DocSync.Doc.updateCanvasState(master_action.type, {
                                    state: {
                                        canvas: new_state.canvas,
                                    },
                                });
                            }
                            /* 同步 canvas 更新 */
                        }

                        break;
                    }

                    /* 修改 canvas 详情 */
                    case 'updateCanvasInfo': {
                        const change_list = {
                            x: 'number',
                            y: 'number',
                            width: 'number',
                            height: 'number',
                            scale: 'number',
                            backgroundColor: 'object',
                            showUnit: 'string',
                        };
                        if (typeof action.params.canvas == 'object') {
                            const temp_ob = {};
                            for (const key in change_list) {
                                if (typeof action.params.canvas[key] === change_list[key]) {
                                    Object.assign(temp_ob, {
                                        [key]: action.params.canvas[key],
                                    });
                                }
                            }
                            Object.assign(new_state.canvas, {
                                ...temp_ob,
                            });
                        }
                        /* 同步 canvas 更新 */
                        if (new_state.cooperationUsers.length > 0) {
                            DocSync.Doc.updateCanvasState(master_action.type, {
                                state: {
                                    canvas: new_state.canvas,
                                },
                            });
                        }
                        /* 同步 canvas 更新 */
                        // return new_state;
                        break;
                    }

                    case 'updateCanvasMap': {
                        // TODO 看起来没必要深复制
                        new_state.canvas = cloneDeep(action.params.canvas);
                        new_state.work = cloneDeep(action.params.work);

                        if ('pageAttr' in action.params) {
                            new_state.pageAttr = cloneDeep(action.params.pageAttr);
                        } else {
                            new_state.pageAttr = { backgroundColor: [], backgroundImage: [{ resId: '' }] };
                        }
                        new_state.pageInfo.pageNow = 0;
                        new_state.picId = action.params.picId;
                        new_state.preview = action.params.preview;
                        new_state.rt_canvas_render_mode = action.params.rt_canvas_render_mode;

                        /* 同步 doc 更新 */
                        // TODO 初始化的时候不同步，目前只有初始化用到了
                        // if (new_state.cooperationUsers.length > 0) {
                        //     DocSync.Doc.updateCanvasState({
                        //         state: {
                        //             canvas: new_state.canvas,
                        //             work: new_state.work,
                        //             pageAttr: new_state.pageAttr,
                        //             picId: new_state.picId,
                        //             preview: new_state.preview,
                        //             toolPanel: {
                        //                 asset: '',
                        //                 assets: [],
                        //                 asset_index: -1,
                        //                 assets_index: [],
                        //                 select_type: ESelectType.unset,
                        //             },
                        //         },
                        //     });
                        // }
                        /* 同步 doc 更新 */
                        break;
                    }

                    case 'delGroupInfoAll': {
                        const currentPage = new_state.work.pages[new_state.pageInfo.pageNow];
                        currentPage.assets = currentPage.assets.filter((item, index) => {
                            item.meta.group = undefined;

                            if (item?.meta?.type === 'group') {
                                return false;
                            }
                            return true;
                        });
                        new_state.toolPanel.asset_index = undefined;
                        new_state.toolPanel.assets_index = [];
                        new_state.toolPanel.assetsInfo = undefined;
                        // TODO Infobar , template , teamWorkPanel 更新调用

                        break;
                    }

                    // 选中元素 START
                    case 'selectAsset': {
                        let {
                            asset_index = INIT_STATE['toolPanel']['asset_index'],
                            assets_index = INIT_STATE['toolPanel']['assets_index'],
                            assets_info = new_state.toolPanel.assetsInfo,
                            select_type = INIT_STATE['toolPanel']['select_type'],
                        } = action.params;
                        const { asset_className = ''} = action.params
                        const {  page_num = undefined } =
                            action.params;
                        const oldSelectIndex = new_state.toolPanel.asset_index
                        const oldSelectAsset = AssetHelper.find(new_state.work, new_state.pageInfo, {
                            index: oldSelectIndex,
                        });
                        let selectAssetsPageIndex = page_num;
                        if (select_type === ESelectType['unset']) {
                            
                            if (oldSelectIndex >= 0) {
                                const asset = oldSelectAsset
                                emitter.emit('BlurAssetEmitter', new_state.work, new_state.pageInfo,asset);
                                if (asset?.meta?.type === 'group') {
                                    asset.groupTextEditAsset = undefined;
                                }
                            }
                            // 取消选中，格式化数据
                            asset_index = -1;
                            assets_index = [];
                            assets_info = [];
                            selectAssetsPageIndex = -1;

                            // 协作同步 select
                            if (new_state.cooperationUsers.length > 0) {
                                DocSync.SelectAssets.updateSelectAsset({
                                    target: {
                                        userId: new_state.user.userId,
                                        action: ESelectAction.DELETE,
                                        userName: new_state.user.userName,
                                    },
                                });
                            }
                        }
                        if (select_type === ESelectType.asset) {
                            // 之前有选中的元素，则选择新的元素的时候之前元素也是失焦状态
                            if (oldSelectIndex !== asset_index) { 
                                emitter.emit('BlurAssetEmitter', new_state.work, new_state.pageInfo, oldSelectAsset);
                            }
                         
                            const asset = AssetHelper.find(new_state.work, new_state.pageInfo, { index: asset_index });
                            // if (
                            //     asset?.meta.type !== 'group' &&
                            //     asset?.meta?.group
                            // ) {
                            //     const page = new_state.work.pages[new_state.pageInfo.pageNow];
                            //     asset_index = page.assets.findIndex(
                            //         (a) => a.meta.type === 'group' && a.meta.group === asset.meta.group,
                            //     );
                            //     asset = page.assets[asset_index];
                            // }
                            // 协作同步 select
                            if (new_state.cooperationUsers.length > 0 && asset) {
                                DocSync.SelectAssets.updateSelectAsset({
                                    target: {
                                        userId: new_state.user.userId,
                                        action: ESelectAction.ADD,
                                        userName: new_state.user.userName,
                                        assets: [
                                            {
                                                className: asset.meta.className,
                                                pageHash: asset.meta.hash?.split('-')[0],
                                                assetHash: asset.meta.hash,
                                            },
                                        ],
                                    },
                                });
                            }
                        }
                        if (select_type === ESelectType.assets) {
                            const assets = AssetHelper.findAll(new_state.work, new_state.pageInfo, assets_index.map((i: number) => ({index: i})))
                            if (assets) {
                                let groupName: string;
                                let isGroup = true;
                                let groupIndex: number;
                                for (const i in assets) {
                                    if (!assets[i]) {
                                        break;
                                    } else if (!assets[i].meta.group) {
                                        isGroup = false;
                                        break;
                                    }
                                    if (assets[i].meta.group && !groupName) {
                                        groupName = assets[i].meta.group;
                                    } else if (groupName && groupName !== assets[i].meta.group) {
                                        isGroup = false;
                                        break;
                                    }
                                    if (assets[i].meta.type === 'group') {
                                        groupIndex = assets_index[i];
                                    }
                                }
                                if (isGroup) {
                                    select_type = ESelectType.asset;
                                    asset_index = groupIndex;
                                    assets_index = [];
                                }

                                // 协作同步 select
                                if (new_state.cooperationUsers.length > 0) {
                                    DocSync.SelectAssets.updateSelectAsset({
                                        target: {
                                            userId: new_state.user.userId,
                                            action: ESelectAction.ADD,
                                            userName: new_state.user.userName,
                                            assets: assets.map((a) => {
                                                return {
                                                    className: a.meta.className,
                                                    pageHash: a.meta.hash?.split('-')[0],
                                                    assetHash: a.meta.hash,
                                                };
                                            }),
                                        },
                                    });
                                }
                            }
                        }

                        new_state.toolPanel = {
                            ...new_state.toolPanel,
                            asset_index: asset_index,
                            assets_index: assets_index,
                            select_type: select_type,
                            assetsInfo: assets_info,
                            reline: undefined,
                            selectAssetsPageIndex,
                        };

                        if (page_num !== undefined) {
                            new_state.pageInfo.pageNow = page_num;
                        }

                        break;
                    }
                    // 取消选中元素(组合图层)
                    case 'selectAssetdelete' : {

                        const { asset_index = INIT_STATE['toolPanel']['asset_index'], 
                                assets_index = INIT_STATE['toolPanel']['assets_index'], 
                                assets_info = new_state.toolPanel.assetsInfo, 
                                select_type = INIT_STATE['toolPanel']['select_type'] 
                            } = action.params
                        const assetTemp = new_state.toolPanel.assets_index.filter(item => {
                            if (item !== Number(assets_index)) {
                                return item
                            }
                        })
                        new_state.toolPanel = {
                            ...new_state.toolPanel,
                            asset_index: asset_index,
                            assets_index: assetTemp,
                            assetsInfo: assets_info,
                            select_type: select_type
                        }
                        /* 同步 select 选中信息 */
                        const selects: { className: string; pageHash: string; assetHash: string; }[] = [];
                        if (typeof new_state.toolPanel.asset_index === 'number' && new_state.toolPanel.asset_index >= 0) {
                            const a = new_state.work.pages[new_state.pageInfo.pageNow].assets[new_state.toolPanel.asset_index];
                            selects.push({
                                className: a.meta.className,
                                pageHash: a.meta.hash?.split('-')[0],
                                assetHash: a.meta.hash,
                            })
                        }
                        if (new_state.toolPanel.assets_index.length > 0) {
                            new_state.toolPanel.assets_index.forEach(i => {
                                const a = new_state.work.pages[new_state.pageInfo.pageNow].assets[i];
                                selects.push({
                                    className: a.meta.className,
                                    pageHash: a.meta.hash?.split('-')[0],
                                    assetHash: a.meta.hash,
                                })
                            })
                        }
                        if (selects.length > 0) {
                            DocSync.SelectAssets.updateSelectAsset({
                                target: {
                                    userId: new_state.user.userId,
                                    action: ESelectAction.ADD,
                                    userName: new_state.user.userName,
                                    assets: selects,
                                },
                            });
                        }
                        /* 同步 select 选中信息 */

                        break
                    }
                    // 选中元素 END
                    // 删除元素 START
                    case 'deleteAsset': {
                        const asset_index_list: {
                                index: number;
                                page_num?: number;
                            }[] = action.params.asset_index_list,
                            { pageInfo, work } = new_state;

                        let temp_page_num: number;
                        const changePages: Set<number> = new Set();
                        /* 协作同步删除变量 */
                        const syncInfo: {
                            className: string;
                            pageHash: string;
                            assetHash: string;
                        }[] = [];
                        const userCount = new_state.cooperationUsers.length;
                        /* 协作同步删除变量 */
                        asset_index_list.forEach((item) => {
                            temp_page_num = item.page_num === undefined ? pageInfo.pageNow : item.page_num;
                            changePages.add(temp_page_num);
                            /* 有协作人才需要同步 */
                            if (userCount > 0) {
                                const asset = work.pages[temp_page_num].assets[item.index];
                                syncInfo.push({
                                    className: asset.meta.className,
                                    pageHash: asset.meta.hash?.split('-')[0],
                                    assetHash: asset.meta.hash,
                                })
                            }
                            /* 有协作人才需要同步 */
                            work.pages[temp_page_num].assets.splice(item.index, 1);
                        });
                        changePages.forEach((i) => {
                            let hasVideoE = false;
                            let maxTime = 0;
                            work.pages[i].assets.forEach((a, index) => {
                                a.meta.rt_page_index = i;
                                a.meta.rt_page_assets_index = index;
                                if (a.meta.type === 'videoE') {
                                    hasVideoE = true;
                                    maxTime = Math.max(maxTime, a.attribute.cet - a.attribute.cst);
                                }
                            });
                            if (
                                new_state.pageAttr.pageInfo?.[i] &&
                                !new_state.pageAttr.pageInfo[i].rt_isUserUpdatedTime
                            ) {
                                if (hasVideoE) {
                                    new_state.pageAttr.pageInfo[i].pageTime = maxTime;
                                } else {
                                    new_state.pageAttr.pageInfo[i].pageTime = 5000;
                                }
                            }
                        });
                        /* 有协作人且存在要发送的删除信息 */
                        if (userCount > 0 && syncInfo.length > 0) {
                            DocSync.Assets.deleteAssets(master_action.type, syncInfo)
                        }
                        /* 有协作人且存在要发送的删除信息 */
                        break;
                    }
                    // 删除元素 END
                    // 更新元素 START
                    case 'updateAssets': {
                        const targets = action.params.targets as IUpdateTarget[];
                        // assets 返回 [asset, asset, undefined (如果找不到对应元素), asset] | undefined
                        const assets = AssetHelper.findAll(new_state.work, new_state.pageInfo, targets);
                        /* 协作同步更新变量 */
                        const syncInfo: {
                            className: string;
                            pageHash: string;
                            assetHash: string;
                            changes: DeepPartial<IAsset>;
                        }[] = [];
                        const userCount = new_state.cooperationUsers.length;
                        /* 协作同步更新变量 */
                        if (assets && assets?.length > 0) {
                            
                            assets.forEach((asset, i) => {
                                // if (targets[i].isCoverAsset) {
                                //     const pageNow = targets[i].pageIndex || new_state.pageInfo.pageNow;
                                //     new_state.work.pages[pageNow].assets[targets[i].changes.meta.index] = targets[i]
                                //         .changes as IAsset;
                                // } else {
                                    
                                // }
                                if (asset) {
                                    AssetHelper.update(asset, targets[i].changes);
                                    /* 有协作人才需要同步 */
                                    if (userCount > 0) {
                                        if (targets[i].changes.attribute?.container) {
                                            if (!asset.attribute.container.isEdit) {
                                                syncInfo.push({
                                                    className: asset.meta.className,
                                                    pageHash: asset.meta.hash?.split('-')[0],
                                                    assetHash: asset.meta.hash,
                                                    changes: {
                                                        attribute: asset.attribute, 
                                                    },
                                                });
                                            }
                                        } else {
                                            syncInfo.push({
                                                className: asset.meta.className,
                                                pageHash: asset.meta.hash?.split('-')[0],
                                                assetHash: asset.meta.hash,
                                                changes: targets[i].changes,
                                            });
                                        }
                                    }
                                    /* 有协作人才需要同步 */
                                }
                            });
                        }
                        /* 有协作人且存在要发送的更新信息 */
                        if (userCount > 0 && syncInfo.length > 0) {
                            DocSync.Assets.updateAssetProperty(master_action.type, {
                                targets: syncInfo
                            })
                        }
                        /* 有协作人且存在要发送的更新信息 */

                        break;
                    }
                    // 更新元素 END
                    // 将元素更换页面
                    case 'changeAssetPage': { 
                        let asset_index_list: {
                            index: number;
                            page_num?: number;
                            pre_page_num?: number;
                            posX: number;
                            posY: number;
                        }[] = action.params.asset_index_list;
                        const {work } = new_state;
                        asset_index_list.forEach((item) => {
                            const {page_num, index, posY} = item
                            const asset = AssetHelper.find(new_state.work, new_state.pageInfo, { index });
                            // asset.transform.posX = posX;
                            asset.transform.posY = posY;
                            asset.meta.hash = undefined;
                            asset.meta.index = (work.pages[page_num].assets.length || 0) + 1;
                            work.pages[page_num].assets.push(asset);
                        });
                        asset_index_list = asset_index_list.sort((a,b) => b.index - a.index)
                        asset_index_list.forEach(item => work.pages[item.pre_page_num].assets.splice(item.index, 1))
                        break;
                    }
                    
                    // 更新容器 START
                    case 'updateContainerAddedAsset': {
                        new_state.containerAddedAsset = action.params.containerAddedAsset;
                        break;
                    }
                    // 更新容器 END

                    // 添加元素 START
                    case 'addAssets': {
                        const { assets, isGroupWord } = action.params;
                        const currentPageIndex = new_state.pageInfo.pageNow; // TODO 处理可能存在的指定 pageIndex 的情况
                        const pageAssetsCount = new_state.work.pages[currentPageIndex].assets.length;
                        let isAddBackground = false;
                        let backgroundIndex = currentPageIndex;
                        const pageAssetsMap: Record<string, IAsset[]> = {};
                        const groupClassMembers:string[] = []
                        let groupAsset:IAsset = null
                        assets.forEach((asset: IAsset | { asset: IAsset; pageIndex: number }, i: number) => {
                            let pi = currentPageIndex;
                            if ('pageIndex' in asset) {
                                pi = asset.pageIndex;
                                asset = asset.asset as IAsset;
                            }
                            if (asset.meta.type === 'background') {
                                isAddBackground = true;
                                backgroundIndex = pi;
                                // 新添加的 asset.meta.index 默认为 0
                            } else if (asset.meta.group) {
                                asset.meta.index += 100000;
                            } else {
                                asset.meta.index = pageAssetsCount + i + 1; // meta.index 从 1 开始
                            }
                            asset.meta.className =
                                asset.meta.type + ++new_state.work.nameSalt.salt + '_' + new_state.user.userId;
                            if(isGroupWord && asset.meta.type !== 'group'){
                                groupClassMembers.push(asset.meta.className)
                            }
                            if(asset.meta.type === 'group') groupAsset = asset
                            asset.meta.rt_page_index = pi;
                            asset.meta.rt_page_assets_index = pageAssetsCount + i; // 第一个值是 len + 0 === len - 1 + 1
                            // 尝试给元素生成 hash, 如果不存在 pageAttr.pageHash 则无事发生
                            DocHash.createAssetHash(new_state, pi, asset);
                            if (Array.isArray(pageAssetsMap[pi])) {
                                pageAssetsMap[pi].push(asset);
                            } else {
                                pageAssetsMap[pi] = [asset];
                            }
                        });
                        if(groupAsset && isGroupWord){
                            groupAsset.meta.memberClassNames = groupClassMembers
                        }
                        if (isAddBackground) {
                            new_state.work.pages[backgroundIndex].assets.forEach((a) => {
                                a.meta.index++;
                            });
                        }
                        for (const key in pageAssetsMap) {
                            new_state.work.pages[key].assets.push(...pageAssetsMap[key]);
                        }
                        /* 协作同步添加元素 */
                        // TODO 同步处理添加背景的情况
                        const userCount = new_state.cooperationUsers.length;
                        if (userCount > 0) {
                            DocSync.Assets.addAssets(master_action.type, {
                                targets: assets.map((a: IAsset | { asset: IAsset; pageIndex: number }) => {
                                    if ('pageIndex' in a) {
                                        a = a.asset as IAsset;
                                    }
                                    return {
                                        className: a.meta.className,
                                        pageHash: a.meta.hash?.split('-')[0],
                                        assetHash: a.meta.hash,
                                        changes: a,
                                    }
                                })
                            })
                        }
                        /* 协作同步添加元素 */
                        break;
                    }
                    // 添加元素 END
                    // 更新画布 START
                    case 'updateCanvas': {
                        const targets = action.params.targets;
                        CanvasPaintedHelper.update(new_state, targets);
                        /* 同步更新 */
                        if (new_state.cooperationUsers.length > 0) {
                            let filterTarget = {};
                            if (master_action.type === 'MUTISIZE_SET_CURRENT_SELECTED_TID') {
                                filterTarget = cloneDeep(targets);
                            } else {
                                for (const i in targets) {
                                    if (['canvas', 'work', 'pageAttr'].includes(i)) {
                                        filterTarget[i] = cloneDeep(targets[i]);
                                    }
                                }
                            }
                            DocSync.Doc.updateCanvasState(master_action.type, {
                                state: filterTarget,
                            });
                        }
                        /* 同步更新 */
                        break;
                    }

                    case 'updateShotIsComplete': {
                        const { pageNum, asset } = action.params;
                        const pageNow = pageNum >= 0 ? pageNum : new_state.pageInfo.pageNow;
                        const assetLength = new_state.work.pages[pageNow].assets.length;

                        for (let i = 0; i < assetLength; i++) {
                            if (new_state.work.pages[pageNow].assets[i] == asset) {
                                new_state.work.pages[pageNow].assets[i].rt_shotIsComplete = true;
                            }
                        }
                        break;
                    }

                    /**
                     * 修改画布背景颜色
                     */
                    case 'updateBackgroundColor': {
                        const { isOpacity = false, backgroundColor, styleInfo, originBackgroundInfo} = action.params;
                        if (new_state.pageInfo.pageNow === 0) {
                            new_state.canvas.backgroundColor = {} as IColor;
                        }
                        // TODO 移除对 new_state.work.pages[].backgroundColor 和 isOpacityBg 的依赖
                        new_state.work.pages[new_state.pageInfo.pageNow].backgroundColor = backgroundColor;
                        new_state.work.pages[new_state.pageInfo.pageNow].isOpacityBg = isOpacity;
                        new_state.work.pages[new_state.pageInfo.pageNow]['styleInfo'] = styleInfo
                        new_state.work.pages[new_state.pageInfo.pageNow]['originBackgroundInfo'] = originBackgroundInfo
                        if(new_state.pageAttr.backgroundImage)new_state.pageAttr.backgroundImage[new_state.pageInfo.pageNow] = {
                            resId: '',
                        };
                        if(new_state.pageAttr.backgroundColor)new_state.pageAttr.backgroundColor[new_state.pageInfo.pageNow] = backgroundColor;
                        if(new_state.pageAttr.backgroundOpacity)new_state.pageAttr.backgroundOpacity[new_state.pageInfo.pageNow] = isOpacity;
                        /* 同步更新 */
                        if (new_state.cooperationUsers.length > 0) {
                            const ph = DocHash.getPageHash(new_state.pageAttr, new_state.pageInfo.pageNow);
                            DocSync.PageAttr.updatePageAttr(master_action.type, {
                                changes: {
                                    [ph]: {
                                        backgroundColor,
                                        isOpacityBg: isOpacity,
                                    }
                                },
                                pageAttr: {
                                    backgroundImage: new_state.pageAttr.backgroundImage,
                                    backgroundOpacity: new_state.pageAttr.backgroundOpacity
                                },
                            });
                        }
                        /* 同步更新 */
                        break;
                    }
                        
                    /**
                     * 页面打标
                     */
                    case 'updatePageMark': {
                        const { rt_page_ppt_mark } = action.params;
                        // if (!new_state.pageAttr.pageInfo) {
                        //     new_state.pageAttr.pageInfo = new Array(new_state.work.pages.length).fill({});
                        // }
                        new_state.pageAttr.pageInfo[new_state.pageInfo.pageNow]['rt_page_ppt_mark'] = rt_page_ppt_mark;
                        break;
                    }
                        
                    /**
                     * pageAttr的pageInfo
                     */
                    case 'updatePageAttrPageInfo': {
                        const { pageInfo } = action.params;
                        new_state.pageAttr.pageInfo = pageInfo;
                        break;
                    }

                    /*修改画板背景图片*/
                    case 'updateBackgroundImage': {
                        const { id, sample, width, height } = action.params;
                        new_state.work.pages[new_state.pageInfo.pageNow].isOpacityBg = false;
                        new_state.pageAttr.backgroundImage[new_state.pageInfo.pageNow] = {
                            resId: id,
                            rt_imageUrl: sample,
                            backgroundSize: {
                                width: width,
                                height: height,
                            },
                        };
                        /* 同步更新 */
                        if (new_state.cooperationUsers.length > 0) {
                            const ph = DocHash.getPageHash(new_state.pageAttr, new_state.pageInfo.pageNow);
                            DocSync.PageAttr.updatePageAttr(master_action.type, {
                                changes: {
                                    [ph]: {
                                        isOpacityBg: false,
                                    },
                                },
                                pageAttr: {
                                    backgroundImage: new_state.pageAttr.backgroundImage,
                                },
                            });
                        }
                        /* 同步更新 */
                        break;
                    }

                    // 更新画布 END
                    // 元素重新排序 START
                    case 'resetAssetIndex': {
                        SortAsset.resetAssetIndex({
                            new_state: new_state,
                            action: action,
                        });
                        /* 同步更新 */
                        if (new_state.cooperationUsers.length > 0) {
                            const pi = new_state.pageInfo.pageNow;
                            const assets = new_state.work.pages[pi].assets;
                            DocSync.Assets.updateAssetProperty(master_action.type, {
                                targets: assets.map((a) => {
                                    return {
                                        className: a.meta.className,
                                        pageHash: a.meta.hash?.split('-')[0],
                                        assetHash: a.meta.hash,
                                        changes: {
                                            meta: {
                                                index: a.meta.index,
                                            },
                                        },
                                    };
                                }),
                            });
                        }
                        /* 同步更新 */

                        break;
                    }
                    // 元素重新排序 END

                    //多选 分布 对齐
                    case 'alignDistribute': {
                        AssetClass.alignDistribute({
                            new_state: new_state,
                            action: action,
                        });
                        /* 同步更新 */
                        if (new_state.cooperationUsers.length > 0) {
                            DocSync.Assets.updateAssetProperty(master_action.type, {
                                targets: new_state.toolPanel.assets_index.map((i) => {
                                    const a = new_state.work.pages[new_state.pageInfo.pageNow].assets[i];
                                    return {
                                        className: a.meta.className,
                                        pageHash: a.meta.hash?.split('-')[0],
                                        assetHash: a.meta.hash,
                                        changes: {
                                            transform: a.transform,
                                        },
                                    };
                                }),
                            });
                        }
                        /* 同步更新 */
                        break;
                    }

                    // 元素组合
                    case 'combinationAsset': {
                        const { assets, assetsIndex } = action.params
                        const {diff, add, deleteAssets} = AssetClass.combinationAsset({
                            new_state: new_state,
                            assets,
                            assetsIndex
                        });
                        if (deleteAssets.length > 0) {
                            const assetIndex = AssetHelper.getIndexs(new_state.work, new_state.pageInfo, deleteAssets);
                            /* 协作同步删除变量 */
                            assetIndex.forEach((item) => {
                                const temp_page_num = new_state.pageInfo.pageNow;
                                new_state.work.pages[temp_page_num].assets.splice(item, 1);
                            });
                        }
                        /* 同步更新 */
                        if (new_state.cooperationUsers.length > 0 && add && add.length > 0) {
                            DocSync.Assets.addAssets(master_action.type, { targets: add });
                        }
                        if (new_state.cooperationUsers.length > 0 && deleteAssets && deleteAssets.length > 0) {
                            DocSync.Assets.deleteAssets(master_action.type, deleteAssets);
                        }
                        AssetClass.syncAssetsDiff(master_action.type, new_state, diff);
                        /* 同步更新 */
                        break;
                    }

                    // 元素拆分START
                    case 'splitAsset': {
                        const { groupAsset } = action.params;
                        const {diff, remove} = AssetClass.splitAsset({
                            new_state,
                            groupAsset
                        });
                        /* 同步更新 */
                        AssetClass.syncAssetsDiff(master_action.type, new_state, diff);
                        if (new_state.cooperationUsers.length > 0 && remove && remove.length > 0) {
                            DocSync.Assets.deleteAssets(master_action.type, remove);
                        }
                        /* 同步更新 */
                        break;
                    }

                    case 'updatePageMap': {
                        const { page_map } = action.params;
                        new_state.page_map = page_map
                        break;
                    }
                    
                    case 'updateAssetName': {
                        const { meta } = action.params;
                        new_state.work.pages[new_state.pageInfo.pageNow].assets.map((v) => {
                            if (v.meta.type == meta.type && v.meta.className === meta.className) {
                                v.meta.name = meta.name;
                            }
                        });
                        break;
                    }
                    // 更新元素旋转
                    case 'updateRotate': {
                        const { assetsGrounp, rotate } = action.params;
                        const diff = AssetClass.updateRotate({
                            new_state,
                            rotate,
                            assetsGrounp,
                        });
                        /* 同步更新 */
                        AssetClass.syncAssetsDiff(master_action.type, new_state, diff);
                        /* 同步更新 */
                        break;
                    }

                    /*替换模板*/
                    case 'replaceTemplate': {
                        const { canvas, rt_editor_type, pageInfo: { pageNow } } = new_state;
                        // 新版逻辑最好canvasheight和width，scale不更新， 不然可能会出现适口不对的问题
                        if (rt_editor_type === 'canvas') {
                            const { width, height } = action.params.canvas;
                            if (canvas.width === width && canvas.height === height) {
                                for (const key in action.params.canvas) {
                                    if (key !== 'width' && key !== 'height' && key !== 'scale') {
                                        if (key === 'backgroundColor' && pageNow !== 0) {
                                        } else {
                                            new_state.canvas[key] = action.params.canvas[key];
                                        }
                                    }
                                }
                            } else {
                                new_state.canvas = undefined;
                                new_state.canvas = cloneDeep(action.params.canvas);
                                if (pageNow !== 0) {
                                    new_state.canvas.backgroundColor = canvas.backgroundColor;
                                }
                            }
                        } else {
                            new_state.canvas = undefined;
                            new_state.canvas = cloneDeep(action.params.canvas);
                            if (pageNow !== 0) {
                                new_state.canvas.backgroundColor = canvas.backgroundColor;
                            }
                        }
                        new_state.work = undefined;
                        new_state.work = cloneDeep(action.params.work);
                        // 外部传入的参数是针对当前页的
                        const index = new_state.pageInfo.pageNow;
                        new_state.work.pages[index].assets.forEach((a) => {
                            a.meta.className = a.meta.type + ++new_state.work.nameSalt.salt;
                        });
                        if ('pageAttr' in action.params) {
                            new_state.pageAttr = undefined;
                            new_state.pageAttr = cloneDeep(action.params.pageAttr);
                        } else {
                            AssetClass.initPageAttr(new_state);
                            if (!new_state.pageAttr.pageHash || new_state.pageAttr.pageHash && Object.keys(new_state.pageAttr.pageHash).length !== new_state.work.pages.length) {
                                DocHash.initHash(new_state, action.params.picId);
                            }
                            new_state.pageAttr.backgroundColor[index] = {
                                r: 255,
                                g: 255,
                                b: 255,
                                a: 1,
                            };
                            new_state.pageAttr.backgroundImage[index] = { resId: '' };
                            AssetClass.updatePattrInfo({ new_state });
                        }
                        if (new_state.pageInfo.pageNow >= new_state.work.pages.length) {
                            new_state.pageInfo.pageNow = 0;
                        }
                        new_state.picId = action.params.picId;
                        new_state.preview = action.params.preview;
                        console.log(action.params.picId)
                        new_state.page_map[index + 1] = action.params.picId
                        console.log(new_state.page_map)
                        AssetClass.updatePageAudio(new_state); // 更新音乐时间
                        /* 同步替换模板 */
                        if (new_state.pageAttr.pageHash) {
                            const ph = DocHash.getPageHash(new_state.pageAttr, index);
                            new_state.work.pages[index].assets.forEach((a) => {
                                a.meta.hash = DocHash.assetHash(ph, new_state.user.userId, a.meta.type);
                            });
                            if (new_state.cooperationUsers.length > 0) {
                                new_state.work.pages[index].rt_timeStamp = new Date().getTime();
                                DocSync.Page.replacePage(master_action.type, {
                                    pageHash: ph,
                                    work: new_state.work,
                                    pageAttr: new_state.pageAttr,
                                    picId: new_state.picId,
                                    preview: new_state.preview,
                                    page_map: new_state.page_map
                                });
                            }
                        }
                        /* 同步替换模板 */
                        break;
                    }
                    /*使用空白模板*/
                    case 'editorBlankTempl': {
                        new_state.canvas = undefined;
                        new_state.canvas = {
                            ...INIT_STATE.canvas,
                        };
                        new_state.canvas.width = action.params.width;
                        new_state.canvas.height = action.params.height;
                        new_state.work = { ...INIT_STATE.work, ...{ pages: [{ assets: [] }] } };
                        // TODO 调用链已无效，可以移除，暂时无需同步
                        break;
                    }

                    case 'updateSetDetailFloorHeight': {
                        const { floorHeights, allCanvasHeight } = action.params
                        new_state.canvas = {
                            ...new_state.canvas,
                            floorCutting : floorHeights,
                            height : allCanvasHeight
                        }
                        /* 同步 canvas */
                        if (new_state.cooperationUsers.length > 0) {
                            DocSync.Doc.updateCanvasState(master_action.type, {
                                state: {
                                    canvas: new_state.canvas,
                                },
                            });
                        }
                        /* 同步 canvas */
                        break;
                    }

                    /* 长图多页模板功能设置楼层 */ 
                    case 'setDetailFloorCurrentIndex': {
                        const { currentFloorIndex = 0 , clickPos = 'canvas'} = action.params;

                        new_state.pageInfo = {...new_state.pageInfo,
                            rt_online_detail_current_index : currentFloorIndex
                        }
                        new_state.rt_detail_page_click_floor_position = clickPos;
                        break;
                    }

                    /* 判断鼠标当前停留的楼层 */
                    case 'setDetailFloorHoverCurrentIndex': {
                        const { rt_online_current_hover_index = -1 } = action.params;

                        new_state.pageInfo = {...new_state.pageInfo,
                            rt_online_current_hover_index : rt_online_current_hover_index
                        }

                        break;
                    }

                    /* 右键添加空模板（长图） */
                    case 'addRightMenuEmpty': {
                        const { pageInfo , canvas , work , pageAttr, online_detail_rightaction_params,online_detail_single_floor_uploaded_ids} = new_state;
                        const { pageNow } = pageInfo;
                        const { copyIndex } = online_detail_rightaction_params ;

                        const curIndex = pageInfo.rt_online_detail_current_index || 0 ,
                            canvasFloors = canvas.floorCutting ,
                            curHeight = canvasFloors[curIndex].height ;

                        canvasFloors.splice(curIndex + 1 , 0 , JSON.parse(JSON.stringify(canvasFloors[curIndex])));
                        
                        new_state.canvas = {...new_state.canvas,
                            floorCutting : canvasFloors,
                            height : canvas.height + curHeight
                        }
                        new_state.pageInfo = {...new_state.pageInfo,
                            rt_online_detail_current_index : curIndex + 1
                        }
                        new_state.rt_detail_page_click_floor_position = 'canvas';
                        let limitY = 0;
                        canvasFloors.map((v,i)=>{
                            if(i <= curIndex){
                                limitY += v.height;
                            }
                        })

                        work.pages[pageNow].assets.map((v,i)=>{
                            const transY = v.transform.posY;
                            if(transY >= limitY){ // 所有高度大于移动楼层的元素 都需要调整高度值
                                work.pages[pageNow].assets[i].transform.posY = transY+curHeight;
                            }
                        })

                        /* 修改楼层提交信息 */
                        const newFloorUploadStatus = [...online_detail_single_floor_uploaded_ids];
                        newFloorUploadStatus.splice(curIndex + 1,0,{id:'',isUpload:false});
                        new_state.online_detail_single_floor_uploaded_ids = newFloorUploadStatus;
                        /* 修改楼层提交信息 end*/

                        // 修改copyIndex
                        if(copyIndex > -1 &&  curIndex <= copyIndex ){
                            new_state.online_detail_rightaction_params = {...online_detail_rightaction_params,
                                copyIndex : copyIndex - 1
                            }
                        }
                        /* 同步更新 */
                        if (new_state.cooperationUsers.length > 0) {
                            DocSync.Doc.updateCanvasState(master_action.type, {
                                state: {
                                    canvas: new_state.canvas,
                                    work: new_state.work,
                                    online_detail_single_floor_uploaded_ids: new_state.online_detail_single_floor_uploaded_ids,
                                }
                            })
                        }
                        /* 同步更新 */

                        break;
                    }

                    /*右键复制模板（长图） */
                    case 'copyRightMenu': {
                        new_state.online_detail_rightaction_params = {...new_state.online_detail_rightaction_params,
                            copyIndex : new_state.pageInfo.rt_online_detail_current_index || 0 ,
                            cutAssets : [] ,
                            cutFloorHeight : 0
                        }
                        new_state.rt_detail_page_click_floor_position = 'canvas';
                        break;
                    }

                    /*右键粘贴模板（长图） */
                    case 'pasteRightMenu': {
                        const { pageInfo , work , canvas , online_detail_rightaction_params,online_detail_single_floor_uploaded_ids} = new_state;
                        const { floorCutting = [], height } = canvas ;
                        const { rt_online_detail_current_index = 0 , pageNow } = pageInfo;
                        const { copyIndex , cutAssets , cutFloorHeight} = online_detail_rightaction_params;

                        const currentFloorIndex = rt_online_detail_current_index ;
                          let copyFloor = floorCutting[copyIndex];

                        if(copyIndex > -1){ //复制
                            //计算在复制楼层内的元素
                            let limitY = 0 , minusY = 0;
                            const copyAssets: any[] = []
                            floorCutting.map((v: { height: number; },i: number)=>{
                                if(copyIndex > 0){
                                    if(i <= copyIndex - 1){
                                        minusY += v.height;
                                    }
                                }
                                if(i <= copyIndex){
                                    limitY += v.height;
                                }
                            })
                            work.pages[pageNow].assets.map(v=>{
                                const transY = v.transform.posY;
                                if(checkoutAssetIsInArea(minusY,limitY,v)){
                                    copyAssets.push(v);
                                }
                                /* if(transY >= minusY && transY <= limitY){ // 在最小高度和最大高度之间的元素 需要被复制
                                    // work.pages[pageNow].assets[i].transform.posY = transY+curHeight;
                                    copyAssets.push(v);
                                } */
                            });
                            let shouldChangeHeight = 0;
                            if(copyIndex <= currentFloorIndex){//shouldChangeHeight > 0
                                floorCutting.map((v: { height: number; },i: number)=>{
                                    if(i >= copyIndex && i <= currentFloorIndex){
                                        shouldChangeHeight += v.height ;
                                    }
                                })
                            }else{
                                floorCutting.map((v: { height: number; },i: number)=>{
                                    if(i > currentFloorIndex && i < copyIndex){
                                        shouldChangeHeight -= v.height ;
                                    }
                                });
                                shouldChangeHeight -= copyFloor.height;
                            }


                            //在新加入的楼层下的所有元素需添加楼层高度
                            limitY = 0;
                            floorCutting.map((v: { height: number; },i: number)=>{
                                if(i <= currentFloorIndex ){
                                    limitY += v.height ;
                                }
                            })
                            work.pages[pageNow].assets.map((v,i)=>{
                                const transY = v.transform.posY;
                                if(transY >= limitY){
                                    work.pages[pageNow].assets[i].transform.posY = transY+copyFloor.height;
                                }
                            });
                            //在新加入的楼层下的所有元素需添加楼层高度 end

                            copyAssets.map((item,index) => {
                                const _item = {
                                    meta : {...item.meta},
                                    transform : {...item.transform},
                                    attribute : {...item.attribute}
                                }
                                _item.meta.className = item.meta.type + ++new_state.work.nameSalt.salt + '_' + new_state.user.userId;
                                _item.meta.index += 10000;
                                _item.transform.posY += shouldChangeHeight;//加上位移值

                                work.pages[pageNow].assets.push(JSON.parse(JSON.stringify(_item)));
                            });
                            //计算在复制楼层内的元素
                        }else{//剪切
                            copyFloor = {height:cutFloorHeight};
                            let limitY = 0;
                            floorCutting.map((v: { height: number; },i: number)=>{
                                if(i <= currentFloorIndex){
                                    limitY += v.height;
                                }
                            })
                            work.pages[pageNow].assets.map((v,i)=>{
                                const transY = v.transform.posY;
                                if(transY >= limitY){
                                    work.pages[pageNow].assets[i].transform.posY = transY+cutFloorHeight;
                                }
                            });

                            cutAssets.map((item: any,index: any) => {
                                const _item = {
                                    meta : {...item.meta},
                                    transform : {...item.transform},
                                    attribute : {...item.attribute},
                                }
                                _item.meta.className = item.meta.type + ++new_state.work.nameSalt.salt + '_' + new_state.user.userId;
                                _item.meta.index += 10000;
                                _item.transform.posY += limitY;//加上位移值

                                work.pages[pageNow].assets.push(JSON.parse(JSON.stringify(_item)));
                            });
                        }

                        /* 修改楼层提交信息 */
                        const newFloorUploadStatus = [...online_detail_single_floor_uploaded_ids];
                        newFloorUploadStatus.splice(currentFloorIndex + 1,0,{id:'',isUpload:false});
                        new_state.online_detail_single_floor_uploaded_ids = newFloorUploadStatus;
                        /* 修改楼层提交信息 end*/


                        floorCutting.splice(currentFloorIndex + 1 , 0 , JSON.parse(JSON.stringify(copyFloor)));//添加复制楼层

                        new_state.canvas = {...new_state.canvas,
                            floorCutting : floorCutting,
                            height : height + copyFloor.height
                        }

                        new_state.pageInfo = {...new_state.pageInfo,
                            rt_online_detail_current_index : currentFloorIndex + 1
                        }
                        new_state.rt_detail_page_click_floor_position = 'canvas';
                        /* 同步更新 */
                        if (new_state.cooperationUsers.length > 0) {
                            DocSync.Doc.updateCanvasState(master_action.type, {
                                state: {
                                    canvas: new_state.canvas,
                                    work: new_state.work,
                                    online_detail_single_floor_uploaded_ids: new_state.online_detail_single_floor_uploaded_ids,
                                }
                            })
                        }
                        /* 同步更新 */
                        break;
                    }

                    /*右键删除模板（长图） */
                    case 'delRightMenuEmpty': {
                        const { pageInfo , canvas , work , online_detail_rightaction_params,online_detail_single_floor_uploaded_ids} = new_state;
                        const { pageNow , rt_online_detail_current_index } = pageInfo;
                        const { floorCutting = [], height } = canvas ;
                        const { copyIndex } = online_detail_rightaction_params ;

                        const deleteHeight = floorCutting[rt_online_detail_current_index].height ;

                        let limitY = 0;
                        floorCutting.map((v,i)=>{
                            if(i <= rt_online_detail_current_index){
                                limitY += v.height;
                            }
                        })

                        floorCutting.splice(rt_online_detail_current_index , 1 );
                        // 修改copyIndex
                        if(copyIndex > -1){
                            if(rt_online_detail_current_index < copyIndex){
                                new_state.online_detail_rightaction_params = {...online_detail_rightaction_params,
                                    copyIndex : copyIndex - 1
                                }
                            }
                            if(rt_online_detail_current_index == copyIndex){
                                new_state.online_detail_rightaction_params = {...online_detail_rightaction_params,
                                    copyIndex : - 1
                                }
                            }
                        }

                        if(rt_online_detail_current_index > floorCutting.length - 1){
                            new_state.pageInfo = {...new_state.pageInfo,
                                rt_online_detail_current_index : floorCutting.length - 1
                            }
                            new_state.rt_detail_page_click_floor_position = 'canvas';
                        }

                        /* 修改楼层提交信息 */
                        const newFloorUploadStatus = [...online_detail_single_floor_uploaded_ids];
                        newFloorUploadStatus.splice(rt_online_detail_current_index,1);
                        new_state.online_detail_single_floor_uploaded_ids = newFloorUploadStatus;
                        /* 修改楼层提交信息 end*/

                        new_state.canvas = {...new_state.canvas,
                            floorCutting : floorCutting,
                            height : canvas.height - deleteHeight
                        }

                        // /* 在移出画布范围内的元素需删除 */
                        const restAssets: any[] = [];
                        work.pages[pageNow].assets.map((v,i)=>{
                            const transY = v.transform.posY;
                            if(!(transY >= limitY - deleteHeight && transY <= limitY)){ // 找出删除后剩下的元素
                                restAssets.push(v)
                            }
                        })
                        /* 在移出画布范围内的元素需删除 */
                        work.pages[pageNow].assets = [];
                        restAssets.map((v,i)=>{
                            const transY = v.transform.posY;
                            if(transY >= limitY){ // 所有高度大于移动楼层的元素 都需要调整高度值
                                // work.pages[pageNow].assets[i].transform.posY = transY - deleteHeight;
                                v.transform.posY = transY - deleteHeight;
                            }
                            work.pages[pageNow].assets.push(v);
                        })

                        /* 同步更新 */
                        if (new_state.cooperationUsers.length > 0) {
                            DocSync.Doc.updateCanvasState(master_action.type, {
                                state: {
                                    canvas: new_state.canvas,
                                    work: new_state.work,
                                    online_detail_single_floor_uploaded_ids: new_state.online_detail_single_floor_uploaded_ids,
                                }
                            })
                        }
                        /* 同步更新 */
                        break;
                    }

                    /*右键剪切模板（长图） */
                    case 'cutRightMenuEmpty': {
                        const { pageInfo , canvas , work , online_detail_single_floor_uploaded_ids} = new_state ;
                        const { floorCutting = [], height } = canvas ;
                        const { rt_online_detail_current_index , pageNow} = pageInfo ;
            
                        const currentFloorIndex = rt_online_detail_current_index ,
                            copyFloor = floorCutting[currentFloorIndex];
            
                        let topHeight = 0; //将当前页的元素位置全部当做在第一层模板计算， 减去上面楼层的高度
                        floorCutting.map((v,i)=>{
                            if(i < rt_online_detail_current_index){
                                topHeight += v.height;
                            }
                        });
                        const saveDeleteAssets: any = []
                        work.pages[pageNow].assets.map((v,i)=>{
                            const _v = JSON.parse(JSON.stringify(v));
                            const transY = v.transform.posY;
                            if(checkoutAssetIsInArea(topHeight,topHeight + copyFloor.height,_v)){
                                _v.transform.posY -= topHeight;
                                saveDeleteAssets.push({..._v});
                            }
                        });
            
                        new_state.online_detail_rightaction_params = {...new_state.online_detail_rightaction_params,
                            copyIndex : -1,
                            cutAssets : saveDeleteAssets ,
                            cutFloorHeight : copyFloor.height
                        }

                        /* 同步更新 */
                        if (new_state.cooperationUsers.length > 0) {
                            DocSync.Doc.updateCanvasState(master_action.type, {
                                state: {
                                    canvas: new_state.canvas,
                                    work: new_state.work,
                                }
                            })
                        }
                        /* 同步更新 */
                        break;
                    }

                    /*详情预览处 拖拽修改顺序 */
                    case 'previewDragChangeSequence': {
                        let { dragFlagFloorIndex = 0 } = action.params;
                        const { pageInfo , work , canvas , online_detail_rightaction_params} = new_state;
                        const { floorCutting = [], height } = canvas ;
                        const { rt_online_detail_current_index = 0 , pageNow = 0 } = pageInfo ;

                        if(dragFlagFloorIndex < 0){
                            dragFlagFloorIndex = 0;
                        }else if( dragFlagFloorIndex > floorCutting.length - 1){
                            dragFlagFloorIndex = floorCutting.length - 1 ;
                        }

                        //将 rt_online_detail_current_index 当前元素替换到 dragFlagFloorIndex 位置处
                        let movedHeight = 0 , curLimitY = 0 , curFloorHeight = 0 , otherLimitY = 0 ; //otherLimitY - 其他需要移动的楼层最大y限制
                        if(rt_online_detail_current_index == dragFlagFloorIndex){
                            return new_state;
                        }else if(dragFlagFloorIndex > rt_online_detail_current_index ){//拖拽到下方
                            floorCutting.map((v,i)=>{
                                if(i > rt_online_detail_current_index && i <= dragFlagFloorIndex){
                                    movedHeight += v.height;
                                }
                                // 当前楼层的位置范围 ，用于计算元素
                                if(i <= rt_online_detail_current_index){
                                    curLimitY += v.height;
                                    if(i == rt_online_detail_current_index ){
                                        curFloorHeight = v.height;
                                    }
                                }
                                if(i <= dragFlagFloorIndex){
                                    otherLimitY += v.height;
                                }
                            });
                            const curMinY = curLimitY - curFloorHeight ,
                                otherMinY = curLimitY ;
                            work.pages[pageNow].assets.map((v,i)=>{
                                const transY = v.transform.posY;
                                const _pasteItem = JSON.parse(JSON.stringify(v));
                                if(checkoutAssetIsInArea(curMinY,curLimitY,v)){
                                    _pasteItem.transform.posY += movedHeight;
                                    work.pages[pageNow].assets[i] = _pasteItem ;
                                }else if(checkoutAssetIsInArea(otherMinY,otherLimitY,v)){
                                    _pasteItem.transform.posY -= curFloorHeight;
                                    work.pages[pageNow].assets[i] = _pasteItem ;
                                }
                            });
                        }else{ // 拖拽到上方
                            let otherMinY = 0 ;
                            floorCutting.map((v,i)=>{
                                if(i >= dragFlagFloorIndex && i < rt_online_detail_current_index){
                                    movedHeight += v.height;
                                }
                                // 当前楼层的位置范围 ，用于计算元素
                                if(i <= rt_online_detail_current_index){
                                    curLimitY += v.height;
                                    if(i == rt_online_detail_current_index ){
                                        curFloorHeight = v.height;
                                    }
                                }
                                if(i < dragFlagFloorIndex){
                                    otherMinY += v.height;
                                }
                            });
                            otherLimitY = curLimitY - curFloorHeight;
                            const curMinY = curLimitY - curFloorHeight ;

                            work.pages[pageNow].assets.map((v,i)=>{
                                const transY = v.transform.posY;
                                const _pasteItem = JSON.parse(JSON.stringify(v));
                                if(checkoutAssetIsInArea(curMinY,curLimitY,v)){
                                    _pasteItem.transform.posY -= movedHeight;
                                    work.pages[pageNow].assets[i] = _pasteItem ;
                                }else if(checkoutAssetIsInArea(otherMinY,otherLimitY,v)){
                                    _pasteItem.transform.posY += curFloorHeight;
                                    work.pages[pageNow].assets[i] = _pasteItem ;
                                }
                            });
                        }
                        const tempFloorHeight = floorCutting[dragFlagFloorIndex].height ,
                            _floorCutting = JSON.parse(JSON.stringify(floorCutting)) ;

                        _floorCutting[dragFlagFloorIndex].height = _floorCutting[rt_online_detail_current_index].height;
                        _floorCutting[rt_online_detail_current_index].height = tempFloorHeight;

                        new_state.canvas = {...canvas,
                            floorCutting : _floorCutting
                        }
                        new_state.pageInfo = {...new_state.pageInfo,
                            rt_online_detail_current_index : dragFlagFloorIndex
                        }
                        new_state.rt_detail_page_click_floor_position = 'canvas';

                        /* 同步更新 */
                        if (new_state.cooperationUsers.length > 0) {
                            DocSync.Doc.updateCanvasState(master_action.type, {
                                state: {
                                    canvas: new_state.canvas,
                                    work: new_state.work,
                                }
                            })
                        }
                        /* 同步更新 */
                        break;
                    }

                    case 'addNewCanvasMap': {
                        // templateId 是 upicId | picId 只用于 hash 生成
                        const { templateId, picId } = action.params;
                        const { pageInfo, work, pageAttr } = new_state;
                        const curIndex = pageInfo.rt_floor_template_current_index || 0;
                        action.params.work.pages[0].assets.forEach((a: IAsset) => {
                            a.meta.className = a.meta.type + ++work.nameSalt.salt;
                        })
                        work.pages.splice(curIndex + 1, 0, {
                            assets: action.params.work.pages[0].assets,
                            backgroundColor: action.params.work.pages[0].backgroundColor,
                            isOpacityBg: action.params.work.pages[0].isOpacityBg,
                            rt_page_tem_tag: generatePageTag(10),
                        });
                        const newPageMap = {};
                        for (const key in new_state.page_map) {
                            const i = Number.parseInt(key);
                            if (i > curIndex + 1) {
                                newPageMap[i + 1] = new_state.page_map[i];
                            } else {
                                newPageMap[i] = new_state.page_map[i];
                            }
                        }
                        newPageMap[curIndex + 2] = String(picId);
                        new_state.page_map = newPageMap;

                        AssetClass.initPageAttr(new_state)

                        if (action.params.pageAttr) {
                            new_state.pageAttr = undefined;
                            new_state.pageAttr = cloneDeep(action.params.pageAttr);
                        } else {
                            pageAttr.backgroundColor.splice(curIndex + 1, 0, { r: 255, g: 255, b: 255, a: 0 });
                            pageAttr.backgroundImage.splice(curIndex + 1, 0, { resId: '' });
                            pageAttr.backgroundOpacity.splice(curIndex + 1, 0, true);
                        }

                        AssetClass.updatePattrInfo({new_state})
                        if (new_state.rt_canvas_render_mode === 'board') {
                            pageAttr.pageInfo[curIndex + 1].type = 'board';
                        }

                        work.pages.forEach((page, index) => {
                            page.assets.forEach((asset) => {
                                asset.meta.rt_page_index = index;
                            });
                        });
                        new_state.pageInfo = {
                            ...new_state.pageInfo,
                            rt_floor_template_current_index: curIndex + 1,
                            pageNow: curIndex + 1,
                        };

                        new_state.rt_page_click_floor_template_position = 'canvas';

                        AssetClass.updatePageAudio(new_state) // 更新音乐时间
                        /* 同步更新 */
                        if (new_state.pageAttr.pageHash) {
                            for (const ph in new_state.pageAttr.pageHash) {
                                if (new_state.pageAttr.pageHash[ph] >= curIndex + 1) {
                                    new_state.pageAttr.pageHash[ph]++;
                                }
                            }
                            const newPh = DocHash.pageHash(templateId, new_state.user.userId);
                            new_state.pageAttr.pageHash[newPh] = curIndex + 1;
                            new_state.work.pages[curIndex + 1].assets.forEach((a) => {
                                a.meta.hash = DocHash.assetHash(newPh, new_state.user.userId, a.meta.type);
                            });
                            if (new_state.cooperationUsers.length > 0) {
                                new_state.work.pages[curIndex + 1].rt_timeStamp = new Date().getTime()
                                DocSync.Page.addPage(master_action.type, {
                                    work: new_state.work,
                                    pageAttr: new_state.pageAttr,
                                    page_map: new_state.page_map
                                });
                            }
                        }
                        /* 同步更新 */

                        break;
                    }

                    case 'mutisizeRightmenuItemAddEmptyTemplate': {
                        // templateId 是 upicId | picId 只用于 hash 生成
                        const { templateId, pageNumber } = action.params;
                        const { pageInfo, work, pageAttr } = new_state;
                        let curIndex = pageInfo.rt_floor_template_current_index || 0;
                        if (pageNumber !== undefined) {
                            curIndex = pageNumber;
                        }
                        work.pages.splice(curIndex + 1, 0, {
                            assets: [],
                            backgroundColor: { r: 255, g: 255, b: 255, a: 1 },
                            isOpacityBg: false,
                            rt_page_tem_tag: generatePageTag(10),
                        });
                        const newPageMap = {};
                        for (const key in new_state.page_map) {
                            const i = Number.parseInt(key);
                            if (i >= curIndex + 1) {
                                newPageMap[i + 1] = new_state.page_map[i];
                            } else {
                                newPageMap[i] = new_state.page_map[i];
                            }
                        }
                        newPageMap[curIndex + 2] = '0'; // 空模板用 0
                        new_state.page_map = newPageMap;

                        AssetClass.initPageAttr(new_state);
                        pageAttr.backgroundColor.splice(curIndex + 1, 0, {
                            r: 255,
                            g: 255,
                            b: 255,
                            a: 1,
                        });
                        pageAttr.backgroundImage.splice(curIndex + 1, 0, { resId: '' });
                        pageAttr.backgroundOpacity.splice(curIndex + 1, 0, false);
                        AssetClass.updatePattrInfo({new_state})
                        if (new_state.rt_canvas_render_mode === 'board') {
                            pageAttr.pageInfo[curIndex + 1].type = 'board';
                        }

                        work.pages.forEach((page, index) => {
                            page.assets.forEach((asset) => {
                                asset.meta.rt_page_index = index;
                            });
                        });

                        new_state.pageInfo = {
                            ...new_state.pageInfo,
                            rt_floor_template_current_index: curIndex + 1,
                            pageNow: curIndex + 1,
                        };

                        new_state.rt_page_click_floor_template_position = 'canvas';

                        AssetClass.updatePageAudio(new_state) // 更新音乐时间

                        /* 同步更新 */
                        if (new_state.pageAttr.pageHash) {
                            for (const ph in new_state.pageAttr.pageHash) {
                                if (new_state.pageAttr.pageHash[ph] >= curIndex + 1) {
                                    new_state.pageAttr.pageHash[ph]++;
                                }
                            }
                            new_state.pageAttr.pageHash[DocHash.pageHash(templateId, new_state.user.userId)] =
                                curIndex + 1;
                            if (new_state.cooperationUsers.length > 0) {
                                new_state.work.pages[curIndex + 1].rt_timeStamp = new Date().getTime()
                                DocSync.Page.addPage(master_action.type, {
                                    work: new_state.work,
                                    pageAttr: new_state.pageAttr,
                                    page_map: new_state.page_map
                                });
                            }
                        }
                        /* 同步更新 */
                        break;
                    }

                    case 'mutisizeRightmenuItemCopyEmptyTemplate': {
                        const { templateId } = action.params;
                        const curIndex = action.params.pageNow;
                        const { work, page_map } = new_state;
                        const page = cloneDeep(work.pages[action.params.pageNow]);
                        page.assets.map((item, key) => {
                            const classNameDateTag = new Date().getTime()
                            page.assets[key].meta.className = item.meta.className + '_' + classNameDateTag;
                            if(page.assets[key].meta.group) {
                                page.assets[key].meta.group += '_' + classNameDateTag
                            }
                            if(page.assets[key].meta.type == 'group' && page.assets[key].meta.memberClassNames){
                                page.assets[key].meta.memberClassNames =  page.assets[key].meta.memberClassNames.map(item=>{
                                    item +=  '_' + classNameDateTag
                                    return item
                                })
                            }
                        });

                        page.rt_page_tem_tag = generatePageTag(10);

                        if (
                            new_state.pageInfo.pageNow === 0 &&
                            new_state.canvas.backgroundColor &&
                            new_state.canvas.backgroundColor.r
                        ) {
                            page.backgroundColor = cloneDeep(new_state.canvas.backgroundColor);
                        }
                        new_state.work.pages.splice(curIndex + 1, 0, page);
                        const newPageMap = {};
                        for (const key in new_state.page_map) {
                            const i = Number.parseInt(key);
                            if (i >= curIndex + 1) {
                                newPageMap[i + 1] = new_state.page_map[i];
                            } else {
                                newPageMap[i] = new_state.page_map[i];
                            }
                        }
                        new_state.page_map[curIndex + 2] = page_map[curIndex + 1]
                        new_state.page_map = newPageMap;
                        for(const i in new_state.pageAttr) {
                            if (Array.isArray(new_state.pageAttr[i])) {
                                new_state.pageAttr[i].splice(curIndex + 1, 0, cloneDeep(new_state.pageAttr[i][curIndex]))
                            }
                        }
                        new_state.pageInfo = {
                            ...new_state.pageInfo,
                            rt_floor_template_current_index: curIndex + 1,
                            pageNow: curIndex + 1,
                        };
                        AssetClass.updatePageAudio(new_state) // 更新音乐时间
                        /* 同步更新 */
                        if (new_state.pageAttr.pageHash) {
                            for (const ph in new_state.pageAttr.pageHash) {
                                if (new_state.pageAttr.pageHash[ph] >= curIndex + 1) {
                                    new_state.pageAttr.pageHash[ph]++;
                                }
                            }
                            const newPh = DocHash.pageHash(templateId, new_state.user.userId);
                            new_state.pageAttr.pageHash[newPh] = curIndex + 1;
                            new_state.work.pages[curIndex + 1].assets.forEach((a) => {
                                a.meta.hash = DocHash.assetHash(newPh, new_state.user.userId, a.meta.type);
                            });
                            if (new_state.cooperationUsers.length > 0) {
                                new_state.work.pages[curIndex + 1].rt_timeStamp = new Date().getTime()
                                DocSync.Page.addPage(master_action.type, {
                                    work: new_state.work,
                                    pageAttr: new_state.pageAttr,
                                    page_map: new_state.page_map
                                });
                            }
                        }
                        /* 同步更新 */
                        break;
                    }

                    case 'mutisizeRightmenuItemDeleteEmptyTemplate': {
                        const curIndex = action.params.pageNow;
                        const picIdHashMap = {};
                        for (const ph in new_state.pageAttr.pageHash) {
                            const i = new_state.pageAttr.pageHash[ph];
                            picIdHashMap[ph] = new_state.page_map[i + 1];
                        }
                        new_state.work.pages.splice(curIndex, 1);
                        new_state.pageInfo.pageNow = curIndex - 1;
                        new_state.pageInfo.rt_floor_template_current_index = curIndex - 1;
                        new_state.pageInfo.pageNow = new_state.pageInfo.pageNow >= 0 ? new_state.pageInfo.pageNow : 0;
                        new_state.pageInfo.rt_floor_template_current_index =
                            new_state.pageInfo.rt_floor_template_current_index >= 0
                                ? new_state.pageInfo.rt_floor_template_current_index
                                : 0;
                        new_state.work.pages.forEach((v,i) => {
                            v?.assets.forEach(t => {
                                t.meta.rt_page_index = i;
                            })
                        })
                        for(const i in new_state.pageAttr) {
                            if (Array.isArray(new_state.pageAttr[i])) {
                                new_state.pageAttr[i].splice(curIndex, 1)
                            }
                        }
                        if (new_state.canvas.backgroundColor?.a) {
                            new_state.canvas.backgroundColor = new_state.pageAttr.backgroundColor[0];
                        }

                        AssetClass.updatePageAudio(new_state) // 更新音乐时间
                        /* 同步更新 */
                        if (new_state.pageAttr.pageHash) {
                            let deletaPh = '';
                            for (const ph in new_state.pageAttr.pageHash) {
                                if (new_state.pageAttr.pageHash[ph] === curIndex) {
                                    deletaPh = ph;
                                    delete new_state.pageAttr.pageHash[ph];
                                    break;
                                }
                            }
                            new_state.page_map = {};
                            for (const ph in new_state.pageAttr.pageHash) {
                                if (new_state.pageAttr.pageHash[ph] > curIndex) {
                                    new_state.pageAttr.pageHash[ph]--;
                                }
                                new_state.page_map[new_state.pageAttr.pageHash[ph] + 1] = picIdHashMap[ph];
                            }
                            if (new_state.cooperationUsers.length > 0) {
                                DocSync.Page.removePageByHash(master_action.type, {
                                    pageHash: deletaPh,
                                });
                            }
                        }
                        /* 同步更新 */
                        break;
                    }
                        
                    /* 页面列表排序 */
                    case 'sortPages': {
                        let { oldIndex, newIndex } = action.params;
                        oldIndex = Number.parseInt(oldIndex);
                        newIndex = Number.parseInt(newIndex);
                        AssetClass.initPageAttr(new_state);
                        AssetClass.updatePattrInfo({ new_state });
                        AssetClass.updatePageAudio(new_state);
                        let { work, pageAttr, pageInfo } = new_state;
                        const oldPh = DocHash.getPageHash(new_state.pageAttr, oldIndex);
                        work.pages.forEach((p) => {
                            if (!p.rt_page_tem_tag) {
                                p.rt_page_tem_tag = generatePageTag(10);
                            }
                        });
                        work = cloneDeep(work);
                        pageAttr = cloneDeep(pageAttr);
                        pageInfo = cloneDeep(pageInfo);
                        const page = work.pages[oldIndex];
                        const pageAttrArrayKeys = [
                            'backgroundColor',
                            'backgroundImage',
                            'backgroundOpacity',
                            'pageInfo',
                            'sound',
                        ];
                        const picIdHashMap = {};
                        for (const ph in new_state.pageAttr.pageHash) {
                            const i = new_state.pageAttr.pageHash[ph];
                            picIdHashMap[ph] = new_state.page_map[i + 1];
                        }
                        if (oldIndex < newIndex) {
                            work.pages.splice(newIndex + 1, 0, page);
                            work.pages.splice(oldIndex, 1);
                            for (const key of pageAttrArrayKeys) {
                                if (Array.isArray(pageAttr[key])) {
                                    const temp = pageAttr[key][oldIndex];
                                    pageAttr[key].splice(newIndex + 1, 0, temp);
                                    pageAttr[key].splice(oldIndex, 1);
                                }
                            }
                            if (new_state.pageAttr.pageHash) { 
                                for (const ph in pageAttr.pageHash) {
                                    if (pageAttr.pageHash[ph] > oldIndex && pageAttr.pageHash[ph] <= newIndex) {
                                        pageAttr.pageHash[ph]--;
                                    }
                                }
                                pageAttr.pageHash[oldPh] = newIndex;    
                            }
                        } else {
                            work.pages.splice(oldIndex, 1);
                            work.pages.splice(newIndex, 0, page);
                            for (const key of pageAttrArrayKeys) {
                                if (Array.isArray(pageAttr[key])) {
                                    const temp = pageAttr[key][oldIndex];
                                    pageAttr[key].splice(oldIndex, 1);
                                    pageAttr[key].splice(newIndex, 0, temp);
                                }
                            }
                            if (new_state.pageAttr.pageHash) { 
                                for (const ph in pageAttr.pageHash) {
                                    if (pageAttr.pageHash[ph] >= newIndex && pageAttr.pageHash[ph] < oldIndex) {
                                        pageAttr.pageHash[ph]++;
                                    }
                                }
                                pageAttr.pageHash[oldPh] = newIndex;     
                            }
                        }
                        work.pages.forEach((p, pi) => {
                            p.assets.forEach((a) => {
                                a.meta.rt_page_index = pi;
                            });
                        });
                        AssetClass.updatePageAudio(new_state);
                        
                        if (pageInfo.pageNow === oldIndex) {
                            pageInfo.pageNow = newIndex;
                            pageInfo.rt_floor_template_current_index = newIndex;
                        } else if (pageInfo.pageNow === newIndex) {
                            pageInfo.pageNow = oldIndex;
                            pageInfo.rt_floor_template_current_index = oldIndex;
                        }
                        new_state.work = work;
                        new_state.pageAttr = pageAttr;
                        new_state.pageInfo = pageInfo;
                        new_state.page_map = {};
                        for (const ph in new_state.pageAttr.pageHash) {
                            new_state.page_map[new_state.pageAttr.pageHash[ph] + 1] = picIdHashMap[ph];
                        }

                        /* 同步更新 */
                        if (new_state.pageAttr.pageHash) {
                            DocSync.Page.sortPage(master_action.type, {
                                oldIndex: oldIndex,
                                newIndex: newIndex,
                            });
                        }
                        /* 同步更新 */
                        break;
                    }

                    /** 批量图片替换 */
                    case 'mutisizeRightmenuItemReplaceTemplate': {
                        const { templateId, assetIdx, image } = action.params,
                            {canvas, pageInfo} = canvasStore.getState().onCanvasPainted;
                        
                        image.forEach((i: IGenerateImage, index: number) => {
                            const curIndex = action.params.pageCount + index;
                            const { work } = new_state;
                            const page = cloneDeep(work.pages[pageInfo.pageNow]);
                            page.assets.forEach((item, key) => {
                                page.assets[key].meta.className = item.meta.className + '_' + new Date().getTime();
                                if(page.assets[key].meta.index === assetIdx) {

                                    if(page.assets[key].attribute.container) {
                                        delete page.assets[key].attribute.container;
                                    }

                                    if (page.assets[key].attribute.renderCaId) {
                                        delete page.assets[key].attribute.renderCa;
                                        delete page.assets[key].attribute.renderCaId;
                                        delete page.assets[key].attribute.renderCaType;
                                    }

                                    const width = Math.round(parseFloat(i.width)),
                                        height = Math.round(parseFloat(i.height));
                                    let newWidth = width, newHeight = height;
                                    if(width > canvas.width) {
                                        newWidth = 500 * 1.2;
                                        newHeight = newWidth * height / width;
                                    }

                                    if(height > canvas.height) {
                                        newHeight = 500 * 1.2;
                                        newWidth = newHeight * width / height;
                                    }

                                    if(newWidth > canvas.width) {
                                        newWidth = 500 * 1.2;
                                        newHeight = newWidth * height / width;
                                    }

                                    if(newHeight > canvas.height) {
                                        newHeight = 500 * 1.2;
                                        newWidth = newHeight * width / height;
                                    }
                                    
                                    newHeight = newHeight === height ?  newHeight * 1.2 : newHeight;
                                    newWidth = newWidth === width ? newWidth * 1.2 : newWidth;
                              
                                    // page.assets[key].attribute.assetHeight = "" + newHeight;
                                    // page.assets[key].attribute.assetWidth = "" + newWidth;
                                    page.assets[key].attribute.height = newHeight;
                                    page.assets[key].attribute.width = newWidth;

                                    page.assets[key].attribute.picUrl = i.path;
                                    page.assets[key].attribute.rt_picUrl = i.path;
                                    page.assets[key].attribute.resId = i.id;
                                }
                            });

                            page.rt_page_tem_tag = generatePageTag(10);

                            if (
                                new_state.pageInfo.pageNow === 0 &&
                                new_state.canvas.backgroundColor &&
                                new_state.canvas.backgroundColor.r
                            ) {
                                page.backgroundColor = cloneDeep(new_state.canvas.backgroundColor);
                            }
                            new_state.work.pages.splice(curIndex + 1, 0, page);
                            const newPageMap = {};
                            for (const key in new_state.page_map) {
                                const i = Number.parseInt(key);
                                if (i >= curIndex + 1) {
                                    newPageMap[i + 1] = new_state.page_map[i];
                                } else {
                                    newPageMap[i] = new_state.page_map[i];
                                }
                            }
                            new_state.page_map[curIndex + 2] = templateId
                            new_state.page_map = newPageMap;
                            for(const i in new_state.pageAttr) {
                                if (Array.isArray(new_state.pageAttr[i])) {
                                    new_state.pageAttr[i].splice(curIndex + 1, 0, cloneDeep(new_state.pageAttr[i][pageInfo.pageNow]))
                                }
                            }

                            AssetClass.updatePageAudio(new_state) // 更新音乐时间
                        });

 
                        /* 同步更新 */
                        if (new_state.pageAttr.pageHash) {
                            const len = image.length;
                            const pageStartIndex = action.params.pageCount + 1;
                            for (let i =0; i < len; i++) {
                                const newPh = DocHash.pageHash(templateId, new_state.user.userId);
                                new_state.pageAttr.pageHash[newPh] = pageStartIndex + i;
                                new_state.work.pages[pageStartIndex + i].assets.forEach((a) => {
                                    a.meta.hash = DocHash.assetHash(newPh, new_state.user.userId, a.meta.type);
                                });
                                new_state.work.pages[pageStartIndex + i].rt_timeStamp = new Date().getTime()
                            }
                            if (new_state.cooperationUsers.length > 0) {
                                DocSync.Page.addPage(master_action.type, {
                                    work: new_state.work,
                                    pageAttr: new_state.pageAttr,
                                    page_map: new_state.page_map
                                });
                            }
                        }
                        /* 同步更新 */
                        break;
                    }
                    /* 更新画布水印 */
                    case 'updateWaterMask': {
                        new_state.canvasWaterMaskFlag = action.params.canvasWaterMaskFlag;
                        break;
                    }
                    /*存入滚动高度*/
                    case 'setScrollTop': {
                        const asset = action.params
                        if(asset.sourceMaterial){
                            if(asset.sourceMaterial.oneType){ new_state.scrollTop.sourceMaterial.oneType =  asset.sourceMaterial.oneType}
                            if(asset.sourceMaterial.twoType){ new_state.scrollTop.sourceMaterial.twoType =  asset.sourceMaterial.twoType} 
                        }
                        if(asset.pic){
                            if(asset.pic.oneType){ new_state.scrollTop.pic.oneType =  asset.pic.oneType}
                            if(asset.pic.twoType){ new_state.scrollTop.pic.twoType =  asset.pic.twoType}
                        }
                        if(asset.gallery){
                            if(asset.gallery.oneType){ new_state.scrollTop.gallery.oneType =  asset.gallery.oneType}
                            if(asset.gallery.twoType){ new_state.scrollTop.gallery.twoType =  asset.gallery.twoType}
                        }
                        if(asset.emoji){
                            if(asset.emoji.oneType){ new_state.scrollTop.emoji.oneType =  asset.emoji.oneType}
                        }
                        if(asset.svg){
                            if(asset.svg.SVGK1){
                                if(asset.svg.SVGK1.oneType){ new_state.scrollTop.svg.SVGK1.oneType =  asset.svg.SVGK1.oneType}
                                if(asset.svg.SVGK1.twoType){ new_state.scrollTop.svg.SVGK1.twoType =  asset.svg.SVGK1.twoType}
                            }

                            if(asset.svg.SVGK2){
                                if(asset.svg.SVGK2.oneType){ new_state.scrollTop.svg.SVGK2.oneType =  asset.svg.SVGK2.oneType}
                            }
                        }
                        break;
                    }
                    /*格式刷start*/
                    case 'SET_TEXT_STYLE_BRUSH_STATUS':
                        const { textStyleBrushStatus } = action.params
                        new_state.toolPanel.textStyleBrushStatus = textStyleBrushStatus
                        break;
                    /*格式刷end*/
                    /* 记录动画帧数 */
                    case 'saveAnimateFrame': {
                        const { rt_animateFrame, pageIndex, playPageTime } = action.params
                        const frame = rt_animateFrame === undefined ? undefined : Math.round(rt_animateFrame / 1000 * 30)
                        const pIndex = pageIndex ?? new_state.pageInfo.pageNow
                        const prevPageTime = new_state.pageAttr.pageInfo?.[pIndex]?.pageTime
                        let pageAnimateTime = rt_animateFrame
                        if (pIndex > 0) {
                            pageAnimateTime = rt_animateFrame - (playPageTime - prevPageTime)
                        }

                        new_state.rt_animatieTime = rt_animateFrame === undefined ? undefined : Math.round(pageAnimateTime / 1000 * 30)

                        new_state.rt_animateFrame = frame
                        break;
                    }
                    /* 预览帧数 */ 
                    case 'prevewAnimationFrame': {
                        const { rt_previewFrame } = action.params;
                        const frame = rt_previewFrame === undefined ? undefined : Math.round(rt_previewFrame / 1000 * 30);
                        new_state.rt_previewFrame = frame;
                        break;
                    }

                    /* hash */
                    case 'addPageHash': {
                        const { pageHash, assetsHash } = action.params;
                        AssetClass.addPageHash(new_state, pageHash, assetsHash)
                        break;
                    }
                    case 'removePageHash': {
                        const { ph } = action.params;
                        AssetClass.removePageHash(new_state, ph);
                        /* 同步删除 */
                        DocSync.Page.removePageByHash(master_action.type, {pageHash: ph});
                        /* 同步删除 */
                        break;
                    }
                    /* hash */

                    /* 协作 start */
                    case 'desynchronizaData': {
                        const { work } = action.params;
                        new_state.work = work;
                        break;
                    }
                    case 'collaborativeUsersUpdate': {
                        const { data } = action.params;
                        new_state.cooperationUsers = CollaborativeLogic.collaborativeUsersUpdate(data, new_state.user.userId);
                        emitter.emit('ChangeCollaborativeUserEmitter', new_state.cooperationUsers);
                        break;
                    }
                    case 'collaborativeSelectUpdate': {
                        const { data } = action.params;
                        new_state.collaborativeUserMap = CollaborativeLogic.collaborativeSelectUpdate(data, new_state.user.userId);
                        break;
                    }
                    case 'collaborativeUpdate': {
                        const { data } = action.params;
                        if (data?.l?.length > 0) {
                            RedoAndUndo.recordSync(new_state);
                        }
                        data?.l?.forEach(
                            (info: Parameters<typeof CollaborativeLogic.update>[1]) => {
                                CollaborativeLogic.update(new_state, info)
                            },
                        );
                        break;
                    }
                    case "talkAudio":{
                        const { data } = action.params;
                        const talkingUsers: ITalkingUser[] = [];
                        data.forEach((userId: string) => {
                            userId = String(userId);
                            const cooperationUser = new_state.cooperationUsers?.find((cooperationUser) => {
                                return cooperationUser.userId === userId;
                            });
                            cooperationUser && talkingUsers.push(cooperationUser);
                            if (new_state.user.userId === userId) {
                                talkingUsers.push({
                                    userId,
                                    userName: new_state.user.userName,
                                    avatar: new_state.user.avatar,
                                });
                            }
                        });
                        new_state.talkingUsers = talkingUsers;
                        break;
                    }
                    case 'updateWorkPagesState': {
                        const {work} = new_state
                        work.pages = work.pages.map(item => item)
                        break;
                    }
                    /* 协作 end */

                    case 'updateNewTemplate': {
                        let newTemplate = false;
                        if (action.params.newTemplate != undefined) {
                            newTemplate = action.params.newTemplate;
                        }
                        new_state.newTemplate = newTemplate;
                        break;
                    }
                }
            });
            // 符合指令处理（sub_common） END
            return new_state;
        });
        // 兼容老版本的 toolPanel 选中格式 START
        const { select_type = '', asset_index = 0, assets_index = [] } = next_state.toolPanel,
            { pageInfo } = next_state;

        next_state.toolPanel.asset = undefined;
        next_state.toolPanel.assets = [];
        // Object.assign(next_state.toolPanel, {
        //     assets_index: []
        // });
        // next_state.toolPanel = {
        //     ...next_state.toolPanel,
        //     assets_index: [],
        //     asset_index: -1
        // };
        // TODO 后续需移出,只用toolPanel中的asset_index
        switch (select_type) {
            case 'asset': {
                const selectAssetPageIndex = next_state.toolPanel.selectAssetsPageIndex >= 0 ? next_state.toolPanel.selectAssetsPageIndex : pageInfo.pageNow;
                if (asset_index >= 0) {
                    next_state.toolPanel.asset = next_state.work.pages[selectAssetPageIndex].assets[asset_index];
                }
                break;
            }
            case 'assets': {
                assets_index.forEach((item) => {
                    if (item >= 0) {
                        const selectAssetPageIndex = next_state.toolPanel.selectAssetsPageIndex >= 0 ? next_state.toolPanel.selectAssetsPageIndex : pageInfo.pageNow;
                        next_state.toolPanel.assets.push(next_state.work.pages[selectAssetPageIndex].assets[item]);
                    }
                });
                break;
            }
        }
        // 兼容老版本的 toolPanel 选中格式 END
        return next_state;
    } catch (error) {
        console.error(error);
    }

    return state;
}
