{"name": "ips_rich_editor", "version": "2.0.0", "private": true, "dependencies": {"@ant-design/cssinjs": "^1.20.0", "@tgs/ai_chat": "workspace:^0.4.54", "@tgs/canvas": "workspace:^0.1.0", "@tgs/cmd-system": "workspace:^0.1.0", "@tgs/general_components": "workspace:^0.2.16-beta", "@tgs/ips_fabric": "workspace:^0.1.0", "@tgs/utils": "workspace:^", "ahooks": "^3.7.2", "antd": "^5.20.2", "axios": "^1.3.5", "classnames": "^2.3.1", "core-js": "^3.10.1", "d3": "5.9.2", "draft-convert": "^2.1.12", "draft-js": "^0.10.1", "draft-js-import-html": "^1.3.3", "echarts": "5.4.0", "fabric": "5.3.0", "fast-deep-equal": "^3.1.3", "fbemitter": "^2.1.1", "gl-matrix": "3.4.3", "immer": "^9.0.1", "immutable": "^3.0.0", "js-md5": "^0.7.3", "jszip": "^3.10.1", "klona": "^2.0.6", "lodash-es": "^4.17.21", "lru-cache": "7.14.0", "mathjs": "^9.5.1", "prop-types": "^15.8.1", "qrcode.react": "3.1.0", "rc-util": "^4.0.2", "react": "^17.0.1", "react-color": "^2.11.7", "react-colorful": "^5.6.1", "react-dom": "^17.0.1", "react-infinite-scroller": "^1.2.6", "react-lazy-load": "3.0.13", "react-lazy-load4": "npm:react-lazy-load@^4.0.1", "react-masonry-css": "^1.0.16", "react-masonry-infinite": "^1.1.0", "react-redux": "^5.0.4", "react-sortablejs": "^6.1.4", "redux": "^3.6.0", "socket.io-client": "^4.3.2", "sort-keys": "^5.0.0", "sortablejs": "^1.15.0", "transformation-matrix": "^2.8.0", "wavesurfer.js": "^5.2.0", "webfontloader": "^1.6.28"}, "scripts": {"dev": "webpack serve --mode development --progress --config ../config/webpack/ue/dev.js", "dev:ueteam": "webpack serve --mode development --progress --config ../config/webpack/ueteam/dev.js", "dev:ecommerce": "webpack serve --mode development --progress --config ../config/webpack/ecommerce/dev.js", "dev:ecommerceteam": "webpack serve --mode development --progress --config ../config/webpack/ecommerceteam/dev.js", "dev:smart": "webpack serve --mode development --progress --config ../config/webpack/smart/dev.js", "dev:shentu": "webpack serve --mode development --progress --config ../config/webpack/shentu/dev.js", "prod": "webpack --mode production --progress --color --config ../config/webpack/ue/prod.js", "prod:ueteam": "webpack --mode production --progress --color --config ../config/webpack/ueteam/prod.js", "prod:ecommerce": "webpack --mode production --progress --color --config ../config/webpack/ecommerce/prod.js", "prod:ecommerceteam": "webpack --mode production --progress --color --config ../config/webpack/ecommerceteam/prod.js", "prod:smart": "webpack --mode production --progress --color --config ../config/webpack/smart/prod.js", "prod:shentu": "webpack --mode production --progress --color --config ../config/webpack/shentu/prod.js", "deploy:ue": "node ./Tool/AD/src/ue/reploy.js", "deploy:ueteam": "node ./Tool/AD/src/ueTeam/reploy.js", "deploy:ecommerce": "node ./Tool/AD/src/ecommerce/reploy.js", "deploy:ecommerceteam": "node ./Tool/AD/src/ecommerceTeam/reploy.js", "deploy:smart": "node ./Tool/AD/src/smart/reploy.js", "testd": "webpack serve --mode development --progress --config ../config/webpack/shentu/dev.js", "testp": "webpack --mode production --progress --color --config ../config/webpack/shentu/prod.js", "deploy:test": "node ./Tool/AD/src/test/reploy.js", "deploy:test:ue": "node ./Tool/AD/src/test/reploy.js -t 1", "deploy:test:ueteam": "node ./Tool/AD/src/test/reploy.js -t 2", "deploy:test:ecommerce": "node ./Tool/AD/src/test/reploy.js -t 3", "deploy:test:ecommerceteam": "node ./Tool/AD/src/test/reploy.js -t 4", "deploy:test:smart": "node ./Tool/AD/src/test/reploy.js -t 5", "deploy:nocheck:ue": "node ./Tool/AD/src/noCheck/reploy.js -t 1", "deploy:nocheck:ueteam": "node ./Tool/AD/src/noCheck/reploy.js -t 2", "deploy:nocheck:ecommerce": "node ./Tool/AD/src/noCheck/reploy.js -t 3", "deploy:nocheck:ecommerceteam": "node ./Tool/AD/src/noCheck/reploy.js -t 4", "deploy:nocheck:smart": "node ./Tool/AD/src/noCheck/reploy.js -t 5", "deploy:sum": "node ./Tool/AD/src/summary/entry.js"}, "browserslist": {"production": [">0.3%", "not dead", "not op_mini all", "not ie <= 10"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@tgs/types": "workspace:^0.1.2", "@types/fabric": "^5.3.3"}}