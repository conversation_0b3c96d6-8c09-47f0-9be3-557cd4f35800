{"name": "ips_editor_all", "version": "0.1.0", "description": "", "private": true, "scripts": {"build": "node scripts/build.mjs"}, "dependencies": {"@tgs/canvas": "workspace:^0.1.0", "@tgs/cmd-system": "workspace:*", "@tgs/ips_fabric": "workspace:*", "@tgs/ui": "workspace:*", "js-md5": "^0.7.3", "transformation-matrix": "^2.8.0"}, "devDependencies": {"@babel/core": "^7.13.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-decorators": "^7.14.5", "@babel/plugin-proposal-do-expressions": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0", "@babel/plugin-proposal-function-bind": "^7.0.0", "@babel/plugin-proposal-function-sent": "^7.0.0", "@babel/plugin-proposal-json-strings": "^7.0.0", "@babel/plugin-proposal-logical-assignment-operators": "^7.0.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.0.0", "@babel/plugin-proposal-numeric-separator": "^7.0.0", "@babel/plugin-proposal-optional-chaining": "^7.0.0", "@babel/plugin-proposal-pipeline-operator": "^7.0.0", "@babel/plugin-proposal-throw-expressions": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.0.0", "@babel/plugin-syntax-import-meta": "^7.0.0", "@babel/plugin-transform-runtime": "^7.0.0", "@babel/preset-env": "^7.14.2", "@babel/preset-react": "^7.13.13", "@babel/preset-typescript": "^7.14.5", "@babel/runtime-corejs3": "^7.18.9", "@microsoft/api-extractor": "^7.34.4", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-terser": "^0.4.0", "@types/d3": "5.9.2", "@types/draft-convert": "^2.1.4", "@types/draft-js": "^0.11.2", "@types/fabric": "^5.3.0", "@types/fbemitter": "^2.0.32", "@types/js-md5": "^0.4.3", "@types/lodash": "^4.14.169", "@types/lodash-es": "^4.17.7", "@types/node": "^16.18.104", "@types/offscreencanvas": "^2019.7.0", "@types/qrcode.react": "^1.0.2", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@types/react-redux": "^7.1.11", "@types/sortablejs": "^1.13.0", "@types/wavesurfer.js": "^5.2.2", "@types/webfontloader": "^1.6.32", "@typescript-eslint/eslint-plugin": "8.0.0", "@typescript-eslint/parser": "8.0.0", "@vitejs/plugin-basic-ssl": "^1.0.1", "@vitejs/plugin-legacy": "^4.1.1", "@vitejs/plugin-react": "^3.1.0", "@webpack-cli/serve": "^1.3.1", "acorn": "^8.8.2", "acorn-jsx": "^5.3.2", "autoprefixer": "^10.4.8", "babel-loader": "^8.0.0", "chalk": "^5.2.0", "clean-webpack-plugin": "^0.1.19", "css-loader": "^6.7.1", "css-minimizer-webpack-plugin": "^5.0.1", "esbuild-loader": "^4.0.2", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.30.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-tsdoc": "^0.2.16", "eslint-webpack-plugin": "^3.2.0", "execa": "^7.0.0", "file-loader": "^6.2.0", "fork-ts-checker-webpack-plugin": "^9.0.2", "html-webpack-plugin": "^5.5.0", "json5": "^2.2.1", "mini-css-extract-plugin": "^2.6.1", "minimist": "^1.2.6", "postcss": "^8.4.16", "postcss-loader": "^7.0.0", "prettier": "^3.3.2", "rollup": "^3.17.2", "rollup-plugin-scss": "^4.0.0", "rollup-plugin-typescript2": "^0.34.1", "sass": "^1.32.8", "sass-loader": "^13.0.2", "style-loader": "^3.3.1", "ts-loader": "^9.2.2", "typescript": "5.5.4", "vite": "^4.4.9", "webpack": "5.47.1", "webpack-bundle-analyzer": "^4.4.1", "webpack-cli": "^4.6.0", "webpack-dev-server": "^4.10.1", "webpack-merge": "^5.7.3", "worker-loader": "^3.0.8"}, "browserslist": {"production": [">0.3%", "not dead", "not op_mini all", "not ie <= 10"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}