import { useCanvasPainted } from '@/redux/adapter/useCanvasPainted';
import { RenderCore } from '@/components/RenderCore';
import styles from './scss/previewItem.module.scss';
import { IpsUtils } from '@tgs/utils';
import {
    updateTempBlobUrlMap
} from '@/redux/slice/canvas';
import { useDispatch } from 'react-redux';
import { useContext, useMemo, useRef, useState } from 'react';
import { IRenderCoreInstance } from '../../../../components/RenderCore/RenderCore';
import { RenderCoreProvider } from '@/provider/RenderCore';
import { message, Modal } from 'antd';
import { TemplateLogic } from '@/logic/TemplateLogic';
import { CanvasLogic } from '@/logic/CanvasLogic';
import { setPv } from '@/http/pv';
import { IMG_CDN_PATH } from '@/http/config';
import { useUserSelector } from '@/redux/adapter/useUserSelector';
import { IAiDesignTemplateTypeEnum,  } from '@tgs/types';
import { UploadLimitModal } from '@/components/userUpload/UploadLimit';
import { GenerateStatusEnum } from '@/enum/GenerateStatusEnum';
import { useTemplSave } from '@/hooks/useTemplSave';
interface IPreviewItemProps {
    picId: string;
    showSelect?: boolean;
    aiProduceId: number;
    groupId: number;
    reRender?: boolean;
    size?: string;
    index: number;
    isPureImage?: boolean;
}
export const PreviewItem = (props: IPreviewItemProps) => {
    const { picId, aiProduceId, size='large', index, isPureImage = false } = props;
    const { activeTempId, tempBlobUrlMap, completeRender, aiDrawTemplMap } = useCanvasPainted();
    const renderCoreRef = useRef<IRenderCoreInstance>(null);
    const isActiveRender = picId == activeTempId;
    const blobUrl = tempBlobUrlMap[picId]?.['blobUrl'] ?? '';
    const classNames = IpsUtils.Common.classNames;
    const dispatch = useDispatch();
    const { canvasPaintedMap } = useCanvasPainted();
    const { checkedTempIds, setCheckedTempIds, downloadHandler, regerateRender } = useContext(RenderCoreProvider);
    const isChecked = useMemo(() => {
        return checkedTempIds.includes(String(picId));
    }, [checkedTempIds.length, picId]);
    const aiTemplateType = useRef<string>(IpsUtils.Url.getUrlParam(window.location.href, 'ai_template_type'));
    const { isAiVip } = useUserSelector();

    const {
        goEdit,
        onDownloadClick,
        showAIDrawAssetSaveModal,
        showJumpLimitModal,
        showUploadLimitModal,
        setShowJumpLimitModal,
        setShowUploadLimitModal,
        stashJumpUrl,
        isSpecialVip,
    } = useTemplSave({
        picId,
        aiProduceId,
    });

    const onRenderOver = async (dataUrl: string) => {
        const { work, canvas, pageAttr, info } = canvasPaintedMap[picId];
        const stashUpicId = tempBlobUrlMap[picId]?.['upicId'] ?? '';
        // ai绘画模板不进行自动保存，需要手动保存并校验用户素材空间
        if (aiDrawTemplMap[picId]) return;
        requestIdleCallback(async () => {
            const upicId = await TemplateLogic.saveUserTempl({
                picId,
                upicId: stashUpicId,
                work: canvasPaintedMap[picId].work,
                canvas,
                pageAttr,
                info,
                aiproduce_id: aiProduceId,
            });
            if (!upicId) {
                message.error('模板保存失败');
                return;
            }
            IpsUtils.Url.appendParamsNoJump(window.location.href, {
                picId,
                upicId: upicId,
                aiproduce_id: aiProduceId as number,
                // group_id: groupId,
            });
            const blob = IpsUtils.File.base64ToBlob(dataUrl);
            CanvasLogic.upload(blob);
            const blobUrl = URL.createObjectURL(blob);

            dispatch(updateTempBlobUrlMap({ [picId]: { blobUrl, upicId, aiproduce_id: aiProduceId } }));
        });
        // setTimeout(async () => {
        //     const res = await PaintRequestManager.getGenerateNum();
        //     if (res.code === 1) {
        //         dispatch(setGenerateNum(res.data));
        //     }
        // }, 1000);
    };
    const checkedTempl = () => {
        setCheckedTempIds([String(picId)]);
        setPv(8328, {
            additional: {
                i0: picId,
            },
        });
    };

    const getExtraStyle = () => {
        switch (aiTemplateType.current) {
            case IAiDesignTemplateTypeEnum.MARKETING_LONG_IMAGE:
                return {
                    borderRadius: '8px',
                    overflow: 'hidden',
                };
            case IAiDesignTemplateTypeEnum.PUBLIC_ACCOUNT_FIRST_IMAGE:
                return {
                    height: 'calc(100% - 40px)',
                    marginTop: '40px',
                }
            default:
                return {};
        }
    };
    const getTemplateClassName = () => {
        switch (aiTemplateType.current) {
            case IAiDesignTemplateTypeEnum.PUBLIC_ACCOUNT_FIRST_IMAGE:
                return styles['official_account_item'];
            case IAiDesignTemplateTypeEnum.MARKETING_LONG_IMAGE:
                return styles['long_pic_item'];
            case IAiDesignTemplateTypeEnum.RED_BOOK:
                return styles['red_book_item'];
            default:
                return '';
        }
    };
    const getContent = () => {
        let isAiDrawTempl = false;
        if (aiDrawTemplMap[picId]) {
            isAiDrawTempl = true;
            if (aiDrawTemplMap[picId].status == 'ERROR') {
                return (
                    <div className={styles['error_wrap']}>
                        <div className={styles['error_icon']}>
                            <i className="iconfont icon-ai-tishi1"></i>
                        </div>
                        <div className={styles['error_title']}>生成失败</div>
                        <div className={styles['error_tip']}>内容存在敏感信息</div>
                        <div className={styles['error_tip']}>换个描述在试试吧</div>
                    </div>
                );
            } else {
                <div className={classNames(styles['default_render'], styles['skeleton-box'])}></div>;
            }
        }
        return canvasPaintedMap[picId] ? (
            <RenderCore
                showSelect={!isAiDrawTempl}
                picId={picId}
                onRenderOver={onRenderOver}
                style={getExtraStyle()}
                showWaterMark={!isAiVip}
                ref={renderCoreRef}
            />
        ) : (
            ''
            // <div className={styles['default_render']}>
            //     <img src={IMG_CDN_PATH + 'waitRender.png'} alt="" />
            //     <span> 等待渲染...</span>
            // </div>
        );
    };

    const isEmpty = !blobUrl && !canvasPaintedMap[picId];
    const isNotAiDrawTempl = !aiDrawTemplMap[picId];
    const isSuccessAiDrawTempl = aiDrawTemplMap[picId] && aiDrawTemplMap[picId].status == GenerateStatusEnum.SUCCESS;
    const checkShowActionWrap = () => {
        return completeRender && canvasPaintedMap[picId] && (isNotAiDrawTempl || isSuccessAiDrawTempl);
    };
    return (
        <>
            <div
                className={classNames(
                    styles['preview_item'],
                    styles[`size-${size}`],
                    isActiveRender ? styles['is_active'] : '',
                    checkShowActionWrap() ? styles['is_complete'] : '',
                    isEmpty ? styles['is_empty'] : '',
                    getTemplateClassName(),
                )}
                onClick={() => {
                    if (!checkShowActionWrap()) return;
                        setPv(9033, {
                            additional: {
                                s1: index,
                            },
                        });
                        checkedTempl();
                }}
            >
                <div
                    className={classNames(
                        styles['center-container'],
                        aiDrawTemplMap[picId] && aiDrawTemplMap[picId].status == GenerateStatusEnum.PROCESSING
                            ? styles['skeleton-box']
                            : '',
                    )}
                >
                    {getContent()}
                    {!isNotAiDrawTempl && <img className={styles['tag-warp']} src={IMG_CDN_PATH + 'ai_tag.png'} alt="AI Generate Tag" />}
                </div>
                {checkShowActionWrap() && (
                    <div className={classNames(styles['action-wrap'], isChecked ? styles['isChecked'] : '')}>
                        <div className={styles['top-wrap']}>
                            <div
                                className={styles['check-box']}
                                onClick={(e) => {
                                    checkedTempl();
                                    setPv(9033, {
                                        additional: {
                                            s1: index,
                                        },
                                    });
                                    e.stopPropagation();
                                }}
                            ></div>
                        </div>
                        <div className={styles['bottom-wrap']}>
                            <div
                                className={styles['edit-btn']}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    setPv(9033, {
                                        additional: {
                                            s1: index,
                                        },
                                    });
                                    goEdit(e);
                                }}
                            >
                                {isPureImage ? '设为底图编辑' : '免费编辑'}
                            </div>
                            <div
                                className={styles['download-btn']}
                                onClick={(e) => {
                                    onDownloadClick(e);
                                }}
                            >
                                直接下载
                            </div>
                        </div>
                    </div>
                )}
            </div>
            <Modal open={showJumpLimitModal} className={styles['jump-modal']} footer={null} centered closable={false}>
                <div className={styles['title']}>即将前往图怪兽编辑器</div>
                <div className={styles['tip']}>将在新页面进入图怪兽编辑器，请确认跳转</div>
                <div className={styles['action-container']}>
                    <div
                        className={classNames(styles['cancel'], styles['action-btn'])}
                        onClick={() => {
                            setShowJumpLimitModal(false);
                            stashJumpUrl.current = '';
                            setPv(8960);
                        }}
                    >
                        取消
                    </div>
                    <div
                        className={classNames(styles['confirm'], styles['action-btn'])}
                        onClick={() => {
                            setShowJumpLimitModal(false);
                            window.open(stashJumpUrl.current, '_blank');
                            stashJumpUrl.current = '';
                            setPv(8961);
                        }}
                    >
                        确定
                    </div>
                </div>
            </Modal>
            <UploadLimitModal
                open={showUploadLimitModal}
                isSpecialVip={isSpecialVip}
                origin={'aiDesignSaveAiDrawAsset'}
                onClose={() => {
                    setShowUploadLimitModal(false);
                }}
            ></UploadLimitModal>
            <Modal
                open={showAIDrawAssetSaveModal}
                className={styles['aiDrawAssetSaveModal']}
                footer={null}
                closable={false}
                width={380}
            >
                <div className={styles['save_modal_content']}>
                    <img src={IMG_CDN_PATH + 'aiSave.gif'} className={styles['animate_img']} alt="" />
                    <div className={styles['animate_text']}>{'正在保存AI绘画素材到您的上传空间...'}</div>
                </div>
            </Modal>
        </>
    );
};
