.painted_page_wrapper {
    width: 100%;
    height: 100%;
    // padding: 32px;
    .progress_wrap {
        position: absolute;
        height: 44px;
        left: 300px;
        right: 480px;
        top: 30px;
    }
    .search_wrap {
        position: absolute;
        height: 44px;
        left: 360px;
        right: 380px;
        top: 20px;
    }
    .back_btn {
        position: absolute;
        top: 84px;
        left: 90px;
        width: 38px;
        height: 38px;
        background-color: #fff;
        border-radius: 50%;
        @include flexCenter;
        cursor: pointer;
        i {
            font-size: 26px;
        }
    }
    .main_area {
        position: relative;
        width: 100%;
        // background-color: #fff;
        // padding: 20px;
        border-radius: 12px;
        min-height: 6.2rem;
        // margin-top: 64px;
        .mutiple_select_wrap {
            margin-top: 50px;
        }
        .preview_message_title {
            position: absolute;
            top: 25px;
            left: 0;
            color: var(---, #1F1A1B);
            font-size: 20px;
            font-weight: 700;
        }
        .preview_message_wrap {
            width: 539px;
            margin: 0 auto;
            margin-bottom: 18px;
        }
        .painted_area {
            width: 100%;
            @include flexCenter;
            .painted_canvas_wrap {
                width: 100%;
                height: 100%;
                .preview_url {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                }
            }
        }
        .preview_area {
            position: relative;
            width: 4.69rem;
            height: 85vh;
            min-height: 8.5rem;
            padding: 0.16rem;
            box-sizing: border-box;
            background-color: #fff;
            border-radius: 0.12rem;
            @include flexCenterColumn;
            justify-content: flex-start;
            .preview_title {
                width: 100%;
                color: #000;
                font-size: 0.2rem;
                font-style: normal;
                font-weight: 600;
            }

            .action_wrap {
                width: 100%;
                margin-bottom: 0.3rem;
                .jump_wrap {
                    @include flexCenter;
                    .go_edit,
                    .download_template {
                        flex: 1;
                        width: 2.09rem;
                        height: 0.42rem;
                        font-size: 0.14rem;
                        font-style: normal;
                        font-weight: 400;
                        border-radius: 0.41rem;
                        @include flexCenter;
                        cursor: pointer;
                    }
                    .go_edit {
                        padding: 0.02rem;
                        color: #7b5aff;
                        background: linear-gradient(111deg, #3e5fff 17.43%, #aa3eff 84.75%);
                        margin-right: 0.2rem;
                        @include flexCenter;
                        overflow: hidden;
                        .content_box {
                            width: 100%;
                            height: 100%;
                            background-color: #fff;
                            border-radius: 0.41rem;
                            @include flexCenter;
                        }
                    }
                    .download_template {
                        background: linear-gradient(111deg, #3e5fff 17.43%, #aa3eff 84.75%);
                        color: #fff;
                    }
                }

                .tips {
                    @include flexCenter;
                    justify-content: flex-end;
                    color: #797676;
                    font-size: 0.14rem;
                    cursor: default;
                    margin-top: 0.08rem;
                    .tip_text {
                        margin-right: 1.1rem;
                        color: #a5a3a4;
                    }
                    i {
                        position: relative;
                        margin-right: 0.08rem;
                        line-height: 0.14rem;
                        font-size: 0.14rem;
                    }
                }
            }
        }
    }
    .official_account_area{
        min-height: 3.2rem;
    }
}
