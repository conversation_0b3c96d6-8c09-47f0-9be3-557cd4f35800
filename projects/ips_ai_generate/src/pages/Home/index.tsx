import React, { useEffect, useState, useRef, UIEvent, useMemo, useCallback } from 'react';
import { useUserSelector } from '@/redux/adapter/useUserSelector';
import { message } from 'antd';
import { MessageItem } from '@/components/Message';

import { TProduceItem, IDesItem, ProcessingState } from './types';

import {
    wattingMessage,
    MSG_TYPE,
    markdownRenewLineReg,
    markdownPPTRenewLineReg,
} from './config';
import { closeAiDraw, updateMessageStatus } from './api';
import { PaintRequestManager } from '@/http/paintRequest';
import { useDispatch } from 'react-redux';
import { setOpenLoginModal, changePayModalStatus, setGenerateNum } from '@/redux/slice/user';
import { IpsUtils } from '@tgs/utils';
import { useGlobalStatus } from '@/redux/adapter/useGlobalStatus';
import { renderTaskQueue } from '@/components/MessageMarkdown/MessageMarkdown';
import { createModalProcessor, IEventTaskMap, PRODUCE_TYPE } from '@/logic/MarkdownProcessor';
import { useMutationObserver } from '@/hooks/useMutaitionObserver';
import { setPv } from '@/http/pv';
import { RenderPage } from '../AiGenerate';
import styles from './home.module.scss';
import { HomeInputContain } from '@/components/HomeInputContain';
import {
    updateAiAnalyseName,
    updateGeneratingPercent,
    updateProduceType,
    updateReGenerateStatus,
    updateReGenerateTemplStatus,
} from '@/redux/slice/global';
import { useMessageSelector } from '@/redux/adapter/useMessageSelector';
import { updateMessage, updatePptMessage } from '@/redux/slice/message';
import { useCanvasPainted } from '@/redux/adapter/useCanvasPainted';
import { IMessageItem as ISliceMessageItem } from '@/redux/slice/message';
import {
    clearState,
    IAiDrawPreviewInfo,
    setAiDrawTemplMap,
    updateCanvasRenderInfo,
    updateTempBlobUrlMap,
} from '@/redux/slice/canvas';
import { aiSelectPercentConfig } from '@/config/percentStatus';
import { useLocation, useNavigate } from 'react-router-dom';

import { basePath } from '@/router/router';

import { checkUserInputInPptWords } from '@/config/base';
import { getStartOriginByUrl } from '@/logic/utils';
import { IAiDesignTemplateImgTagTypeEnum, IAiDesignTemplateTypeEnum } from '@tgs/types';
import { useUploadSelector } from '@/redux/adapter/useUploadSelector';
import { IUploadAssetItem } from '@/redux/slice/upload';
import { useUserValidator } from '@/hooks/useUserValidator';
import { AiDrawTemplLogic } from '@/logic/AiDrawTemlLogic';
let page = 1;
let isPageAdd = false;
function Home() {
    const user = useUserSelector();
    const disPatch = useDispatch();
    const [activePreset, setActivePreset] = useState<IDesItem>({} as IDesItem);
    const [showInputArea, setShowInputArea] = useState<boolean>(false);
    const [showHomeContain, setShowHomeContain] = useState<boolean>(true);
    const [dataReLoad, setDataReLoad] = useState<boolean>(false);
    const [homeInputTextAreaValue, setHomeInputTextAreaValue] = useState<string>();
    const pollTimerRef = useRef<NodeJS.Timeout>();
    const isAiDrawingRef = useRef<boolean>(false);
    const stopPoll = useRef<boolean>(false);
    const isComposingModeRef = useRef<boolean>(false);
    const event = useRef<EventSource | null>(null);
    const { setGenLimitModal, setRechargeModal } = useGlobalStatus();
    const { message: aiContent } = useMessageSelector();
    const { prefetchTemplateInfo } = useCanvasPainted();
    const { generateValidate } = useUserValidator();
    const {
        globalStatus: { generatingPercent, reGenerate, reGenerateTempl, aiAnalyseName, aiPrompt },
    } = useGlobalStatus();
    const { isNewAiUser } = useUserSelector();
    const { uploadedAssetsMap } = useUploadSelector();
    const isPPT = useRef<boolean>(true);
    const needAiAnalyse = useRef<boolean>(false);
    const isFirstGenerate = useRef<boolean>(true);
    const needProcessing = useRef<ProcessingState>(ProcessingState.IDLE);
    // 预设关键词
    const [presets, setPresets] = useState<IDesItem[]>([]);
    const generatePercentStash = useRef<number>(generatingPercent);
    const aiTemplateType = useRef<string>(IAiDesignTemplateTypeEnum.POSTER);
    const getPresets = async () => {
        const res = await PaintRequestManager.getPresetDes();
        if (!res) return;
        setPresets(res.data);
    };
    const aiTextLimitLength = 500;
    let loadingRes = false;
    const location = useLocation();
    const { origin } = location.state || {};
    const urlParams = IpsUtils.Url.getUrlParamsWithChinese(window.location.href);
    const urlPromptRef = useRef<{ prompt: string | undefined; isUsed: boolean }>({
        prompt: urlParams.prompt,
        isUsed: false
    });
    // 修改提示词
    const [prompt, setPrompt] = useState<string>('');
    const changePrompt = (e: React.FormEvent<HTMLTextAreaElement>) => {
        let value = e.currentTarget.value;
        if (!isComposingModeRef.current) {
            value = value.slice(0, aiTextLimitLength);
        }
        setActivePreset({} as IDesItem);
        setHomeInputTextAreaValue(value);
        setPrompt(value);
    };

    // 点击发送prompt 信息，开始生成 defaultMessage
    const [messageList, setMessageList] = useState<IMessageType.IMessageItem[]>([]);

    // 关闭当前生成好的jobItem
    const downAiJobItem = (messageIndex: number, files: IMessageType.IOutlineItem[], count: number = 0) => {
        if (!files.length) return;
        const newPoint = (user.generateNum as number) - count;
        // user.fetchUserPoint()
        // newPoint <= 0 && setPv_new(7608)
        changeMessageItem(messageIndex, {
            type: MSG_TYPE.FINISH,
            step: 100,
            outLineList: files,
        });
    };

    // 修改当前message的状态
    const changeMessageItem = (
        messageIndex: number,
        messageOptions: Partial<IMessageType.IMessageItem>,
        errInfo?: { isNetError?: boolean; isClose?: boolean },
    ) => {
        const changeMessageIndex = Math.min(messageIndex, messageList.length - 1);
        setMessageList((preMessageList) => {
            preMessageList[changeMessageIndex] = { ...preMessageList[changeMessageIndex], ...messageOptions };
            if (errInfo) {
                if (errInfo.isNetError) {
                    updateMessageStatus({ id: preMessageList[changeMessageIndex].id, status: 'FAIL' });
                }
                if (errInfo.isClose) {
                    updateMessageStatus({ id: preMessageList[changeMessageIndex].id, status: 'CLOSE' });
                }
            }
            return [...preMessageList];
        });
    };
    // 添加新的messageItem
    // isReverse 上拉刷新 否则 用户发送消息添加
    const addMesssageItem = (newMessage: IMessageType.IMessageItem, isReverse = false) => {
        isPageAdd = isReverse;
        setMessageList((preMessageList) => {
            const newMsg = Array.isArray(newMessage) ? newMessage : [newMessage];
            preMessageList[isReverse ? 'unshift' : 'push'](...newMsg);
            if (newMessage.type === MSG_TYPE.BASE) {
                setTimeout(() => scrollToBottom());
            }
            return [...preMessageList];
        });
    };
    // 更新messageItem
    const updateMessageItem = (messageId: string, newMessage: IMessageType.IMessageItem) => {
        if (!messageId) return;
        setMessageList((preMessageList) => {
            const index = preMessageList.findIndex((msg) => msg.id === messageId);
            if (index < 0) return preMessageList;
            preMessageList[index] = newMessage;
            return [...preMessageList];
        });
    };
    // 删除最后一个messageItem
    const popMessageItem = () => {
        setMessageList((preMessageList) => {
            preMessageList.pop();
            return [...preMessageList];
        });
    };
    const CreateAiText = (mes?: string, isRestart = false, sceneCategorize?: string) => {
        getJobAiText({
            name: mes ?? prompt,
            sceneCategorize,
            isRestart,
        });
    };
    const getFirstMessageTask = (outLineMessage: IMessageType.IMessageItem) => {
        return async () => {
            if (origin === 'aiAnalyse' && isFirstGenerate.current) return;
            if (loadingRes) {
                if (isPPT.current) {
                    popMessageItem();
                    addMesssageItem({
                        role: 'ai',
                        content: '这是依据您的主题智能生成的内容大纲，觉得怎么样？',
                        id: '' + Date.now(),
                        type: MSG_TYPE.BASE,
                    });
                } else {
                    setMessageList([]);
                }
                loadingRes = false;
            }
            addMesssageItem(outLineMessage);
            disPatch(updateGeneratingPercent({ generatingPercent: 1 }));
        };
    };
    /**
     * @description: markdown解析成功任务
     * @return {*}
     */
    const getSuccessTask = (
        outLineMessage: IMessageType.IMessageItem,
        data: IMessageType.IOutlineItem[],
        processingTextStash: { current: string },
        concurrencyMode?: boolean,
        aiPPTId?: string,
    ) => {
        let tasks = [];
        // 成功后，有可能还有文字剩余
        if (processingTextStash.current) {
            const textRenderTask = async () => {
                outLineMessage.markdownText?.push(processingTextStash.current);
                updateMessageItem(outLineMessage.id, outLineMessage);
                processingTextStash.current = '';
            };
            tasks.push(textRenderTask);
        }
        const outlineListUpdateTask = async () => {
            if (!data?.length) return;
            if (isPPT.current && aiPPTId) {
                outLineMessage.outLineList = { id: aiPPTId, list: data as unknown as IMessageType.ITreeNode[] };
                IpsUtils.Url.appendParamsNoJump(window.location.href, { ai_ppt_id: aiPPTId });
            } else {
                const groupId = (data as IMessageType.IOutlineItem[])[0].group_unique;
                IpsUtils.Url.appendParamsNoJump(window.location.href, { group_id: groupId });
                (outLineMessage.outLineList as IMessageType.IOutlineItem[])?.push(
                    ...(data as IMessageType.IOutlineItem[]),
                );
            }
            // 并发模式下 不立即更新数据状态
            !concurrencyMode && updateMessageItem(outLineMessage.id, outLineMessage);
        };
        tasks.push(outlineListUpdateTask);
        return tasks;
    };

    /**
     * @description: 推流完成任务
     * @param {IMessageItem} outLineMessage
     * @return {*}
     */
    const getFinishTask = (outLineMessage: IMessageType.IMessageItem) => {
        return async () => {
            const { group_id: groupId, ai_ppt_id } = IpsUtils.Url.getUrlParams(window.location.href);
            // 更新messageItem状态
            isAiDrawingRef.current = false;
            outLineMessage.isFinish = true;
            outLineMessage.aiTemplateType = aiTemplateType.current;
            // 获取最新内容
            const { id, list, prompt } = await getAiContent(groupId);
            // 更新消息项
            updateMessageItem(outLineMessage.id, outLineMessage);
            getUserGenerateNum();
            // 更新生成进度
            disPatch(updateGeneratingPercent({ generatingPercent: 20 }));
            // 更新消息
            disPatch(
                updateMessage({
                    message: {
                        id: isPPT.current ? ai_ppt_id : groupId,
                        list: !isPPT.current ? list : outLineMessage.outLineList,
                        content: !isPPT.current ? prompt : outLineMessage.prompt,
                    },
                }),
            );
            generatePercentStash.current = 20;
        };
    };
    /**
     * @description: 推流出错任务
     * @param {string} code
     * @param {string} msg
     * @return {*}
     */
    const getErrorTask = (code: string, msg: string) => {
        return async () => {
            let msgText = msg;
            switch (code) {
                case '101':
                    msgText = '您输入的内容可能存在违规情况，请重新修改后尝试';
                    break;
                case '102':
                    msgText = '您输入的内容文案生成出现问题，请重新修改后尝试';
                    break;
                default:
                    break;
            }
            onErrorResult(msgText);
        };
    };
    /**
     * @description: 推流过程任务
     * @param {IMessageItem} outLineMessage
     * @param {object} processingTextStash
     * @return {*}
     */
    const getProcessingTask = (outLineMessage: IMessageType.IMessageItem, processingTextStash: { current: string }) => {
        return async () => {
            let renderText = processingTextStash.current,
                lastChars = '';
            const specialChar = '\uE000';
            const reNewLineReg = markdownRenewLineReg;
            if (reNewLineReg.test(renderText)) {
                renderText = processingTextStash.current
                    .replace(markdownPPTRenewLineReg, '')
                    .replace(reNewLineReg, specialChar);
                const textArr = renderText.split(specialChar);
                lastChars = textArr.pop() ?? '';
                renderText = textArr.join('\n');
                outLineMessage.markdownText?.push(renderText);
                processingTextStash.current = lastChars;
                updateMessageItem(outLineMessage.id, outLineMessage);
            }
            const left = 20 - generatePercentStash.current;
            const generatePercent = Math.min(20, generatePercentStash.current + Math.random() * 0.1 * left);
            disPatch(updateGeneratingPercent({ generatingPercent: Math.floor(generatePercent) }));
            generatePercentStash.current = generatePercent;
        };
    };
    /**
     * @description: 不同事件的任务
     * @param {string} name
     * @return {*}
     */
    const getEventTasks = (name: string): IEventTaskMap => {
        const outLineList: IMessageType.IOutlineItem[] = [];
        const markdownText: string[] = [];
        let outLineMessage = {
            role: 'user',
            content: '',
            id: 'finish' + Date.now(),
            type: MSG_TYPE.FINISH,
            isFinish: false,
            outLineList,
            markdownText,
            prompt: name,
            lastMesId: '',
        };
        if (origin === 'aiAnalyse' && isFirstGenerate.current) {
            addMesssageItem(outLineMessage);
        }
        return {
            firstMessageTask: (res: any) => getFirstMessageTask(outLineMessage),
            successTask: (res: any, proccessTextStash: { current: string }, concurrencyMode?: boolean) =>
                getSuccessTask(outLineMessage, res.data, proccessTextStash, concurrencyMode, res?.ai_ppt_id),
            errorTask: (res: any) => getErrorTask(res.code, res.msg),
            processingTask: (res: any, proccessTextStash: { current: string }) =>
                getProcessingTask(outLineMessage, proccessTextStash),
            finishTask: () => getFinishTask(outLineMessage),
        };
    };
    /**
     * @description: EventSource报错
     * @param {Event} error
     * @return {*}
     */
    const onEventError = (error: Event) => {
        if (messageList.length > 0) {
            const lastMessage = messageList[messageList.length - 1];
            if (lastMessage.type === MSG_TYPE.LOADING) {
                popMessageItem();
            }
        }
        isAiDrawingRef.current = false;
        onErrorResult('生成错误，请稍后再试');
    };
    const getSearchTemp = async (group_id: string) => {
        await PaintRequestManager.searchAiProduceInfo({ id: group_id });
    };
    const onProcessorInit = async (group_id: string) => {
        await getSearchTemp(group_id);
        AiDrawTemplLogic.startCheckAiDrawResult(group_id, {
            onGenerateProcessing: (aiDrawTemplMap: Record<string, IAiDrawPreviewInfo>) => {
                disPatch(setAiDrawTemplMap(aiDrawTemplMap));
            },
            onGenerateSuccess: ({ ai_produce_draw_id, loadingTime, group_id }) => {
                setPv(8984, {
                    additional: {
                        s0: ai_produce_draw_id,
                        s1: loadingTime,
                        s3: group_id,
                    },
                });
            },
            onGenerateError: ({ ai_produce_draw_id, loadingTime, group_id }) => {
                setPv(8985, {
                    additional: {
                        s0: ai_produce_draw_id,
                        s1: loadingTime,
                        s3: group_id,
                    },
                });
            },
        });
    };

    const getJobAiText = async (params: { name: string; sceneCategorize?: string; isRestart: boolean }) => {
        const { name, sceneCategorize, isRestart } = params;
        // if (name.includes('公众号')) {
        //     aiTemplateType.current = '3';
        //     IpsUtils.Url.appendParamsNoJump(window.location.href, { ai_template_type: '3' });
        // }
        let sanitizedName = name?.replace(/[|'"<>#\\{}]/g, ' ') || '';
        const {
            ai_template_type = IAiDesignTemplateTypeEnum.POSTER,
            group_id,
        } = IpsUtils.Url.getUrlParams(window.location.href);
        const isFirstAiAnalyse = origin === 'aiAnalyse' && isFirstGenerate.current;
        const image_id = uploadedAssetsMap[IAiDesignTemplateImgTagTypeEnum.IMGCONTAINER]
            .map((item) => item.id)
            .join(',');
        const res = await PaintRequestManager.getConcurrencyTaskInfo({
            name: sanitizedName,
            sceneCategorize,
            festival_name: sceneCategorize,
            ai_template_type,
            ai_judge: isFirstAiAnalyse ? '1' : '0',
            group_id: isFirstAiAnalyse ? group_id : '',
            image_id,
        });
        if (res.code == 1) {
            const { group_unique, list, maxNum } = res.data;
            if (isRestart) {
                clearAll();
            }
            // 创建模型任务
            createModalProcessor({
                name,
                group_unique,
                picIdList: list,
                concurrencyNum: maxNum || 3, // 并发数
                produceType: PRODUCE_TYPE.POSTER,
                renderTaskQueue,
                getEventTasks,
                onEventError,
                onProcessorInit,
            });
            isAiDrawingRef.current = true;
            setShowHomeContain(false);
            if (!isFirstGenerate.current || origin != 'aiAnalyse') {
                addMesssageItem({ role: 'user', id: '' + Date.now(), type: 'text', content: name });
                addMesssageItem(
                    isPPT.current
                        ? { ...wattingMessage, content: '好的，正在为您生成大纲文案，请稍等~' }
                        : wattingMessage,
                );
                addMesssageItem({ role: 'ai', content: '', id: '' + Date.now(), type: MSG_TYPE.LOADING });
            }
            if (isFirstAiAnalyse) {
                location.state.origin = '';
                isFirstGenerate.current = false;
            }
            loadingRes = true;
        } else {
            message.warning(res.msg);
            setShowHomeContain(true);
        }
    };

    const closeEvent = () => {
        if (event.current) {
            event.current.close();
            event.current = null;
        }
    };
    const onErrorResult = (content: string) => {
        closeEvent();
        addMesssageItem({
            role: 'ai',
            content: content,
            id: '' + Date.now(),
            type: MSG_TYPE.BASE,
        });
        isAiDrawingRef.current = false;
        setShowInputArea(true);
        setShowHomeContain(false);
    };

    /**
     * 发送用户信息 获取图片生成job，使用job去轮询
     * 或者 点击重新生成直接拿当前的信息的参数重新生成一条任务
     * @param param 重新生成的信息参数
     * @returns
     */
    const sendUserInfo = async (
        param?: IMessageType.IMessageItem,
        isRestart?: boolean,
        isSelectPreset?: boolean,
        sceneCategorize?: number | string,
    ) => {
        if (!user?.userInfo?.id) {
            disPatch(setOpenLoginModal({ status: true }));
            return;
        }
        const canGenerate = await generateValidate();
        // ppt不需要在这里验证用量，在ppthome中
        if (!canGenerate) {
            setPv(8272, {
                additional: {
                    //区分用户身份
                    s0: user.userInfo.selfVipName || '',
                    i0: param?.content ?? prompt ?? '',
                    s1: isRestart ? 'input_btn_again' : isSelectPreset ? 'input_btn_home' : 'input_home',
                    s3: user?.userInfo.selfVip || '',
                },
            });
            needProcessing.current = ProcessingState.WAITING;

            // setGenLimitModal(true);
            message.warning('智能设计次数已用完，开通VIP可继续使用');
            setRechargeModal(true, getStartOriginByUrl());
            if (loadingRes) {
                popMessageItem();
                loadingRes = false;
            }
            return;
        }

        const mes = param?.content || prompt || aiPrompt;
        if (!mes) {
            return message.warning('请输入内容');
        }
        const hasReStartParam = !!param;

        // 如果是点击重新生成，直接拿当前的信息的参数重新生成一条任务
        if (!hasReStartParam && !mes.replace(/\s/g, '')) return;
        // 非重新生成
        // if (!isRestart) {
        //
        // }
        setShowInputArea(false);
        if (needAiAnalyse.current) {
            needAiAnalyse.current = false;
            return navigate(basePath + '/aiAnalyse' + `${urlParams.origin ? `?origin=${urlParams.origin}` : ''}`, {
                state: {
                    name: mes,
                },
            });
        }
        // 是ppt
        if (isPPT.current) {
            gotoAiPPTHome(mes, 'user_input_select_type', { sceneCategorize });
            return;
        }
        const { group_id, ai_template_type } = IpsUtils.Url.getUrlParams(window.location.href);
        // 如果是新用户，且是海拔，跳转到新生成页
        if (isNewAiUser && ai_template_type == '1') {
            window.sessionStorage.setItem(
                'generateText',
                JSON.stringify({
                    content: mes,
                    time: new Date().getTime(),
                    generateType: ai_template_type,
                    sceneCategorize: sceneCategorize?.toString(),
                }),
            );
            navigate(basePath + '/generate' + `${urlParams.origin ? `?origin=${urlParams.origin}` : ''}`, {
                state: {
                    from: location.pathname,
                },
            });
            return;
        }
        CreateAiText(mes, isRestart, sceneCategorize?.toString());
        IpsUtils.Url.appendParamsNoJump(window.location.href, { sourceFrom: 2 });
        needProcessing.current = ProcessingState.COMPLETED;
    };

    // 处理信息自动定位地步
    const messageContentboxRef = useRef();
    const autoPos = (scrollTop?: number) => {
        const contentRef = messageContentboxRef.current;
        if (!contentRef) return;
        (contentRef as unknown as { autoScrollTop: (scrollTop?: number) => void }).autoScrollTop(scrollTop);
    };
    const scrollContainerRef = useRef<HTMLDivElement>(null);
    // 父级消息定位
    const messgeListRef = useRef<HTMLDivElement>(null);
    const toParentPos = (pid: string) => {
        const parentMessageIndex = messageList.findIndex((msg) => msg.id === pid);
        const listDom = messgeListRef.current as HTMLDivElement;
        const parentDom = listDom.childNodes[parentMessageIndex];
        const scrollTop = (parentDom as HTMLDivElement).offsetTop;
        autoPos(scrollTop);
    };

    const getUserGenerateNum = async () => {
        const res = await PaintRequestManager.getGenerateNum();
        if (res.code === 1) {
            disPatch(setGenerateNum(res.data));
        }
    };

    useEffect(() => {
        // getUserGenerateNum();
        // getPresets();
        //监听dom渲染完毕专用
        const targetNode = messgeListRef.current;
        const resizeObserver = new ResizeObserver((entries) => {
            if (isPageAdd && page !== 1) return;
            autoPos();
        });
        // const handleKeyPress = (event: KeyboardEvent) => {
        //     if (event.keyCode === 13) {
        //         onBtnSendClick();
        //     }
        // };
        // document.addEventListener('keydown', handleKeyPress);
        if (targetNode) {
            resizeObserver.observe(targetNode as HTMLDivElement);
        }
        // 在组件卸载时停止观察
        return () => {
            if (targetNode) {
                resizeObserver.disconnect();
            }
            // document.removeEventListener('keydown', handleKeyPress);
        };
    }, []);
    // 重新生成
    const onReStart = (messageInfo?: IMessageType.IMessageItem) => {
        setPv(8278);
        if (isAiDrawingRef.current) {
            return message.warning('正在生成中，请勿频繁操作');
        }
        // addMesssageItem({
        //     role: 'ai',
        //     id: '' + Date.now(),
        //     type: 'text',
        //     content: '好的，已收到您的要求，正在为您重新生成创意文案',
        // });
        clearAll();
        // addMesssageItem({ role: 'ai', content: '', id: '' + Date.now(), type: MSG_TYPE.LOADING });
        // loadingRes = true;
        stopPoll.current = false;
        sendUserInfo(messageInfo, true);
    };
    // 选择预设
    const selectPreset = (preset: IDesItem) => {
        setPv(8275);
        // 向输入框里写预设文案
        setHomeInputTextAreaValue(preset.name);
        setPrompt(preset.name);
        setActivePreset(preset);
        sendUserInfo({ content: preset.name } as IMessageType.IMessageItem, void 0, true);
    };

    /** 让内容滚动到底部 */
    const scrollToBottom = () => {
        scrollContainerRef.current?.scrollTo({ top: scrollContainerRef.current.scrollHeight });
    };

    // 取消任务
    const closeJob = (message: IMessageType.IMessageItem, messageIndex: number) => {
        if (pollTimerRef.current) {
            isAiDrawingRef.current = false;
            stopPoll.current = true;
            changeMessageItem(
                messageIndex,
                {
                    type: MSG_TYPE.CANCEL,
                },
                { isNetError: false, isClose: false },
            );
        }
        closeAiDraw(message.job as string);
    };

    const countNode = ({ value, count, maxLength }: { value: string; count: number; maxLength?: number }) => {
        const isMeet = count >= (maxLength ?? Infinity);
        return (
            <div className="text-right" style={{ color: isMeet ? '#B0BED9' : '#8491AA' }}>
                <span>{count}</span>
                <span>/{maxLength}</span>
            </div>
        );
    };

    const onCallback = () => {
        setShowInputArea(true);
    };

    const navigate = useNavigate();

    const resetIsPPT = (is: boolean, prompt?: string, origin?: string, params?: any): boolean => {
        isPPT.current = is;
        disPatch(updateProduceType(is ? PRODUCE_TYPE.PPT : PRODUCE_TYPE.POSTER));
        if (is && prompt && origin) {
            gotoAiPPTHome(prompt, origin, params);
        }
        return is;
    };

    // 跳转到aippt的首页并开始生成
    const gotoAiPPTHome = (prompt: string, origin?: string, params?: any) => {
        disPatch(updatePptMessage({ pptMessage: { prompt: prompt, origin, excessParams: params } }));
        navigate(basePath + '/ppthome' + `${urlParams.origin ? `?origin=${urlParams.origin}` : ''}`);
    };

    const onBtnSendClick = (
        params: {
            fromSelectAiType?: boolean;
            promptName?: string;
            sceneCategorize?: number | string;
            fromDropDown?: boolean;
        } = {},
    ) => {
        const { fromSelectAiType = false, promptName = '', sceneCategorize = 0, fromDropDown = false } = params;
        if (isAiDrawingRef.current) {
            return message.warning('正在为您生成，请稍等');
        }
        stopPoll.current = false;
        needAiAnalyse.current = !fromSelectAiType && !fromDropDown;
        !fromSelectAiType
            ? sendUserInfo()
            : sendUserInfo({ content: promptName } as IMessageType.IMessageItem, false, false, sceneCategorize);
        // 从点击ai场景类型进入的不触发这里的埋点
        !fromSelectAiType && !fromDropDown && setPv(prompt !== activePreset?.name ? 8274 : 8339);
    };
    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        const mes = prompt || aiPrompt;
        if (e.keyCode === 13) {
            if (!checkUserInputInPptWords(mes)) {
                resetIsPPT(false);
            }
            needAiAnalyse.current = true;
            onBtnSendClick();
            e.preventDefault();
        }
    };

    const initGenerate = () => {
        if (!user?.userInfo?.id) return;
        const mes = window.sessionStorage.getItem('generateText');
        let name = prompt;
        if (mes) {
            const { content, time, generateType } = JSON.parse(mes);
            if (content && Date.now() - time < 1000 * 60) {
                name = content;
            }
            if (generateType != IAiDesignTemplateTypeEnum.PPT) {
                isPPT.current = false;
            }
            window.sessionStorage.removeItem('generateText');
        }

        if (name) {
            setPrompt(name);
            sendUserInfo({ content: name } as IMessageType.IMessageItem, true);
            window.sessionStorage.removeItem('generateText');
        }
    };

    useEffect(() => {
        if (showInputArea) {
            scrollToBottom();
        }
    }, [showInputArea]);

    useMutationObserver(
        scrollContainerRef,
        (mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type == 'childList') {
                    scrollToBottom();
                }
            });
        },
        { childList: true, subtree: true },
    );
    useEffect(() => {
        document.title = '图怪兽-AI智能生成海报';
        if( urlParams.origin === 'home_search'){
            setPv(9023)
        }
        if ( urlParams.origin === 'home_muban') {
            setPv(9024)
        }
    }, []);
    const getAiContent = async (groupId: string) => {
        const res = await PaintRequestManager.getAiProduceInfo({ id: groupId });
        if (res.code !== 1 || res.data.length == 0) {
            return { id: '', list: [], prompt: '' };
        }
        const tempBlobUrlMap: Record<string, Record<string, string>> = {};
        const aiDrawTemplMap: Record<string, IAiDrawPreviewInfo> = {};
        let newData = [...res.data];
        const style6Templates = newData.filter(item => Number(item.draw_template_style) === 6);
        if (newData.length > 3) {
            if(style6Templates.length === 0) {
// 处理ai绘画模板的排序
                const SPECIAL_POSITIONS = [1, 5, 9]; // ai绘画模板的固定位置
                const MAX_SPECIAL_TEMPLATES = 3; // 最大ai绘画模板数量

                // 将模板分为ai绘画模板和普通模板
                const specialTemplates = newData.filter(item => Number(item.draw_template_style) > 1);
                const regularTemplates = newData.filter(item => Number(item.draw_template_style) <= 1);

                // 如果存在ai绘画模板，进行重新排序
                if (specialTemplates.length > 0) {
                    const reorderedData = new Array(newData.length);

                    // 将ai绘画模板放入固定位置（最多3个）
                    specialTemplates.slice(0, MAX_SPECIAL_TEMPLATES).forEach((template, index) => {
                        reorderedData[SPECIAL_POSITIONS[index]] = template;
                    });

                    // 将普通模板填充到剩余位置
                    let regularIndex = 0;
                    for (let i = 0; i < reorderedData.length; i++) {
                        if (!reorderedData[i]) {
                            reorderedData[i] = regularTemplates[regularIndex++];
                        }
                    }

                    newData = reorderedData;
                }
            }else {
                // 处理ai绘画模板的排序
                const STYLE_6_POSITIONS = [0,1,2,3]; // draw_template_style=6的固定位置
                const OTHER_SPECIAL_POSITIONS = [5, 6]; // draw_template_style>1且≠6的固定位置
                const MAX_SPECIAL_TEMPLATES = 4; // 最大ai绘画模板数量

                // 将模板分为三类：style=6、style>1且≠6、普通模板
                const style6Templates = newData.filter(item => Number(item.draw_template_style) === 6);
                const otherSpecialTemplates = newData.filter(item => Number(item.draw_template_style) > 1 && Number(item.draw_template_style) !== 6);
                const regularTemplates = newData.filter(item => Number(item.draw_template_style) <= 1);

                // 如果存在特殊模板，进行重新排序
                if (style6Templates.length > 0 || otherSpecialTemplates.length > 0) {
                    const reorderedData = new Array(newData.length);

                    // 将style=6的模板放入位置0,1,2,3（4个）
                    style6Templates.slice(0, 4).forEach((template, index) => {
                        if(template) {
                            reorderedData[index] = template; // 从位置1开始
                        }
                    });

                    // 将style>1且≠6的模板放入位置5,6（最多2个）
                    otherSpecialTemplates.slice(0, 2).forEach((template, index) => {
                        if(template) {
                            reorderedData[index + 5] = template; // 从位置5开始
                        }
                    });

                    // 将普通模板填充到剩余位置
                    let regularIndex = 0;
                    for (let i = 0; i < reorderedData.length; i++) {
                        if (!reorderedData[i]) {
                            reorderedData[i] = regularTemplates[regularIndex++];
                        }
                    }

                    newData = reorderedData;
                }
            }

        }
        const messageList = newData.map(
            (
                item: {
                    id: string;
                    edit_content: Record<string, string[]>;
                    content: Record<string, string[]>;
                    templ_id: string;
                    dl_id: string;
                    user_templ_id: string;
                    imgContainer: IUploadAssetItem[];
                    logo: IUploadAssetItem[];
                    qrcode: IUploadAssetItem[];
                    draw_template_style: string;
                    ai_draw_template_info: IAiDrawPreviewInfo;
                },
                index: number,
            ) => {
                const urlPrams = IpsUtils.Url.getUrlParams(window.location.href);
                const contentData = item.edit_content || item.content;
                tempBlobUrlMap[item.templ_id] = {
                    upicId: item.user_templ_id,
                    aiproduce_id: item.id,
                };
                if (item.draw_template_style > '1') {
                    aiDrawTemplMap[item.templ_id] = item.ai_draw_template_info;
                }
                return {
                    id: item.id,
                    ...contentData,
                    subtitle: contentData.sed_title ?? '',
                    main_text: contentData.main_text,
                    releted_temp: [{ picId: item['templ_id'] }],
                    imgContainer: item.imgContainer,
                    logo: item.logo,
                    qrcode: item.qrcode,
                    draw_template_style: item.draw_template_style,
                    ai_draw_template_info: item.ai_draw_template_info,
                    subjectColor: item.ai_draw_template_info?.title_color,
                    auxiliaryColor: item.ai_draw_template_info?.sed_title_color,
                };
            },
        );
        disPatch(setAiDrawTemplMap(aiDrawTemplMap));
        return { id: groupId, list: messageList, prompt: res.data?.[0].prompt ?? '', tempBlobUrlMap };
    };

    const initData = async () => {
        const { group_id, ai_template_type = '1' } = IpsUtils.Url.getUrlParams(window.location.href);
        if (!group_id) return;
        // 是ai海报
        resetIsPPT(false);
        let messageList = aiContent.list,
            reRender = false,
            generatingPercent = 100;
        const { id, list, prompt, tempBlobUrlMap } = await getAiContent(group_id);
        messageList = list;
        if (reGenerateTempl) {
            const res = await PaintRequestManager.getTemplateMore({ group_id, ai_template_type });
            if (res.code != 1) {
                message.error(res.msg);
            } else {
                messageList = list.map((item: ISliceMessageItem) => {
                    if (!item.id) return item;
                    const newContent = res.data[Number(item.id)];
                    if (!newContent) return item;
                    return {
                        ...item,
                        releted_temp: [
                            {
                                picId: newContent.templateId,
                            },
                        ],
                    };
                });
                reRender = true;
            }
            generatingPercent = 20;
            disPatch(updateReGenerateTemplStatus(false));
        } else {
            setDataReLoad(true);
        }
        disPatch(updateTempBlobUrlMap(tempBlobUrlMap));
        let outLineMessage = {
            role: 'user',
            content: '',
            id: 'finish' + Date.now(),
            type: MSG_TYPE.FINISH,
            isFinish: true,
            outLineList: list,
            markdownText: [],
            prompt: prompt,
        };
        disPatch(updateGeneratingPercent({ generatingPercent }));
        setPrompt(prompt);
        setShowHomeContain(false);
        addMesssageItem(outLineMessage);
        disPatch(updateMessage({ message: { id: group_id, list: messageList, content: prompt, reRender } }));
    };
    useEffect(() => {
        if (aiAnalyseName) return;
        initData();
    }, []);
    const clearAll = () => {
        setMessageList([]);
        generatePercentStash.current = 0;
        disPatch(
            updateGeneratingPercent({
                generatingPercent: 0,
            }),
        );
        setDataReLoad(false);
        disPatch(clearState());
        disPatch(updateTempBlobUrlMap({ tempBlobUrlMap: {} }));
        disPatch(updateCanvasRenderInfo({ canvasRenderInfo: {} }));
        disPatch(updateMessage({ message: { id: '', list: [] } }));
    };
    useEffect(() => {
        if (reGenerate) {
            // addMesssageItem({ role: 'ai', content: '', id: '' + Date.now(), type: MSG_TYPE.LOADING });
            initGenerate();
            disPatch(updateReGenerateStatus(false));
        }
    }, [reGenerate]);
    // ai分析生成
    useEffect(() => {
        if (aiAnalyseName) {
            isPPT.current = false;
            sendUserInfo({ content: aiAnalyseName } as IMessageType.IMessageItem);
            disPatch(updateAiAnalyseName(''));
        }
    }, [aiAnalyseName]);
    const { group_id: groupId } = IpsUtils.Url.getUrlParams(window.location.href);
    const classNames = IpsUtils.Common.classNames;
    const shouldRender =
        aiContent.list &&
        aiContent.list.length > 0 &&
        Object.keys(prefetchTemplateInfo).length > 0 &&
        generatingPercent >= aiSelectPercentConfig.percent;

    const jumpFrom = location.state?.from;

    // 处理URL参数中的输入内容
    useEffect(() => {
        if (urlPromptRef.current.prompt && !urlPromptRef.current.isUsed) {
            changePrompt({ currentTarget: { value: urlPromptRef.current.prompt } } as React.FormEvent<HTMLTextAreaElement>);
            urlPromptRef.current.isUsed = true;
        }
    }, [urlPromptRef.current.prompt, urlPromptRef.current.isUsed]);

    // 监听prompt变化，当prompt更新后自动触发生成
    useEffect(() => {
        if (prompt === urlPromptRef.current.prompt &&
            urlPromptRef.current.isUsed &&
            user?.userInfo?.id &&
            needProcessing.current === ProcessingState.IDLE) {
            onBtnSendClick();
        }
    }, [prompt, urlPromptRef.current.prompt, urlPromptRef.current.isUsed, user?.userInfo?.id]);

    // 监听用户生成次数变化，充值后重新生成
    useEffect(() => {
        if(needProcessing.current === ProcessingState.WAITING) {
            needProcessing.current = ProcessingState.PROCESSING;
            onBtnSendClick();
        }
    },[user?.generateNum]);

    return (
        <div className={styles['aiHomeWrap']}>
            {!groupId && showHomeContain && (
                <div
                    className={classNames(styles['home-contain'], styles[!showHomeContain ? 'hide-home-contain' : ''])}
                >
                    <HomeInputContain
                        onInput={changePrompt}
                        onKeyDown={handleKeyDown}
                        onCompositionStart={() => {
                            isComposingModeRef.current = true;
                        }}
                        onCompositionEnd={(e: React.CompositionEvent<HTMLTextAreaElement>) => {
                            isComposingModeRef.current = false;
                            const value = e.currentTarget.value.slice(0, aiTextLimitLength);
                            setPrompt(value);
                        }}
                        onBtnSendClick={onBtnSendClick}
                        onFocus={() => {
                            setPv(8276);
                        }}
                        activePreset={activePreset}
                        onSelectPreset={selectPreset}
                        onRefreshPresets={getPresets}
                        textAreaValue={homeInputTextAreaValue}
                        onResetIsPPT={resetIsPPT}
                    ></HomeInputContain>
                </div>
            )}
            {messageList.length > 0 ? (
                <section
                    className={classNames(styles['aiMessage_section'], styles[showHomeContain ? 'hide' : ''])}
                    ref={scrollContainerRef}
                >
                    <div className={styles['messageList']} ref={messgeListRef}>
                        {messageList.map((msg, index) => {
                            return (
                                <MessageItem
                                    key={msg.id + index}
                                    index={index}
                                    message={msg}
                                    onRefreshPresets={getPresets}
                                    onSelectPreset={selectPreset}
                                    onCloseJob={closeJob}
                                    onScrollToBottom={scrollToBottom}
                                    onToParent={toParentPos}
                                    onCallback={onCallback}
                                    onReStart={onReStart}
                                ></MessageItem>
                            );
                        })}
                    </div>
                </section>
            ) : (
                ''
            )}
            {shouldRender ? (
                <section className={classNames(styles['render_section'], styles[`size-${aiContent.list.length === 12 ? 'large' : 'small'}`])}>
                    <div className={classNames(styles['message_render'], styles[`size-${aiContent.list.length === 12 ? 'large' : 'small'}`])}>
                        <RenderPage animate={!dataReLoad}></RenderPage>
                    </div>
                </section>
            ) : (
                ''
            )}
        </div>
    );
}

export default Home;
