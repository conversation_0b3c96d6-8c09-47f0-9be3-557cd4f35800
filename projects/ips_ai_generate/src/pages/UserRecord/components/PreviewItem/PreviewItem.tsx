import { IpsUtils } from '@tgs/utils';
import styles from './scss/index.module.scss';
import { useEffect, useRef, useState } from 'react';
import { PaintModalEnum } from '@/enum/PaintModalEnum';
import { useDownloadTempl } from '@/hooks/useDownloadTempl';
import { PaintModal } from '@/components/PaintModal';
import { IMG_CDN_PATH } from '@/http/config';
import { useInViewport } from 'ahooks';
import { setPv } from '@/http/pv';
export interface IPreviewItem {
    id: string;
    picId: string;
    poster_type: string;
    preview: string;
    title: string;
    upicId: string;
    deletedStatus: number;
    draw_template_style: string;
}
interface IPreviewItemProps {
    previewItem: IPreviewItem;
    groupId: string;
    aiTemplateType: string;
    useSmallSize?: boolean;
}
export const PreviewItem = (props: IPreviewItemProps) => {
    const { previewItem, groupId,useSmallSize } = props;
    const classNames = IpsUtils.Common.classNames;
    const [showValidateModal, setShowValidateModal] = useState<PaintModalEnum>(PaintModalEnum.CLOSE);
    const [isIntersecting, setIsIntersecting] = useState(false);
    const previewBoxRef = useRef<HTMLDivElement>(null);
    const goEdit = () => {
        const { picId, upicId, id } = previewItem;
        const url = IpsUtils.Url.joinUrlParams('https://ue.818ps.com/v4', {
            picId,
            upicId,
            aiproduce_id: id,
            t: Date.now(),
            origin: 'aiDesign_record',
            ai_template_type: props.aiTemplateType,
        });
        setPv(8420, { additional: { s1: upicId, s2: groupId, s3: picId } });

        window.open(url, '_blank');
    };
    const handleItemClick = () => {
        if (previewItem.deletedStatus > 0) return;
        const { picId, upicId, id } = previewItem;
        setPv(8418, { additional: { s1: upicId, s2: groupId, s3: picId } });
        IpsUtils.Url.appendParamsNoJump(window.location.href, { picId, upicId, aiproduce_id: id, group_id: groupId });
        goEdit();
    };
    const { downloadTempl, onBindSuccess, onDownloadValidateSuccess } = useDownloadTempl({
        onDownloadSuccess: () => {
            setShowValidateModal(PaintModalEnum.CLOSE);
        },
        onDownloadLimit: () => {
            setShowValidateModal(PaintModalEnum.DOWNLOADLIMIT);
        },
        onBindPhoneLimit: () => {
            setShowValidateModal(PaintModalEnum.BINDPHONE);
        },
        onPhoneCodeLimit: () => {
            setShowValidateModal(PaintModalEnum.PHONECODE);
        },
        onTemplDownload: () => {
            setShowValidateModal(PaintModalEnum.TEMPLDOWNLOAD);
        },
        onBindSuceess: () => {
            setShowValidateModal(PaintModalEnum.CLOSE);
        },
        onDownloadVlidateSuccess: () => {
            setShowValidateModal(PaintModalEnum.CLOSE);
        },
    });
    const handleDownload = () => {
        const { picId, upicId, id } = previewItem;
        setPv(8419, { additional: { s1: upicId, s2: groupId, s3: picId } });
        IpsUtils.Url.appendParamsNoJump(window.location.href, { picId, upicId, aiproduce_id: id });
        downloadTempl();
    };
    useEffect(() => {
        if (!previewBoxRef.current) return;
        // 只监听一次
        const observer = new IntersectionObserver(
            ([entry]) => {
                if (entry.isIntersecting) {
                    setIsIntersecting(entry.isIntersecting);
                    previewBoxRef.current && observer.unobserve(previewBoxRef.current);
                }
            },
            { threshold: 0.01 },
        );

        if (previewBoxRef.current) {
            observer.observe(previewBoxRef.current);
        }

        return () => {
            if (previewBoxRef.current) {
                observer.unobserve(previewBoxRef.current);
            }
        };
    }, []);

    return (
        <>
            <div
                className={classNames(styles['preview_item'], useSmallSize && styles['small_size'])}
                onClick={handleItemClick}
                ref={previewBoxRef}
            >
                {previewItem.deletedStatus > 0 ? (
                    <div className={styles['delete_wrap']}>
                        <img src={IMG_CDN_PATH + 'design_delete.png'} className={styles['delete_img']} alt="" />
                        <div className={styles['delete_tip']}>模版被删除</div>
                    </div>
                ) : (
                    <div className={styles['preview_wrap']}>
                        {isIntersecting ? (
                            <img src={previewItem.preview} className={styles['preview_img']} alt="" />
                        ) : (
                            ''
                        )}
                        <div className={styles['action_wrap']}>
                            <div
                                className={classNames(styles['action_btn'], styles['edit_btn'])}
                                onClick={(e) => {
                                    goEdit();
                                    e.stopPropagation();
                                }}
                            >
                                {previewItem.draw_template_style === '6' ? '设为底图编辑' : '免费编辑'}
                            </div>
                            <div
                                className={classNames(styles['action_btn'], styles['download_btn'])}
                                onClick={(e) => {
                                    handleDownload();
                                    e.stopPropagation();
                                }}
                            >
                                直接下载
                            </div>
                        </div>
                    </div>
                )}
            </div>
            <PaintModal
                modalType={showValidateModal}
                onBindSuccess={onBindSuccess}
                onDownloadValidateSuccess={onDownloadValidateSuccess}
                onCancel={() => setShowValidateModal(PaintModalEnum.CLOSE)}
            ></PaintModal>
        </>
    );
};
