import { useMemo } from 'react';
import { IPreviewItem, PPTPreviewItem } from '../PreviewItem';
import styles from './scss/index.module.scss';
import { IMG_CDN_PATH } from '@/http/config';
import { IpsUtils } from '@tgs/utils';
import { setPv } from '@/http/pv';

export interface IPPTRecordItem {
    produce_type: string; // 1:海报 2:PPT
    prompt: string;
    group_unique: string;
    created: string;
    data: IPreviewItem[];
    updated: string;
    pages: string;
}
interface IRecordItemProps {
    recordItem: IPPTRecordItem;
}
export const PPTRecordItem = (props: IRecordItemProps) => {
    const { recordItem } = props;
    const isNoDesign = useMemo(() => {
        if (recordItem.data.length == 0) return true;
        const design = recordItem.data.find((item) => item.poster_type == '1' && item.upicId != '0');
        if (!design) return true;
    }, [recordItem.data]);

    // 免费编辑
    const freeEdit = () => {
        if(recordItem.data?.length == 0) return;
        const { picId, upicId, id } = recordItem.data[0];
        const url = IpsUtils.Url.joinUrlParams('https://ue.818ps.com/v4', {
            picId,
            upicId,
            ai_ppt_id: id,
            t: Date.now(),
            origin: 'aiDesign_record',
            ai_template_type: '2',
        });
        setPv(8454)
        window.open(url, '_blank');
    };

    const goToDesign = () => {
        const url = IpsUtils.Url.joinUrlParams('https://818ps.com/aihaibao/selectPPTTemp', {
            ai_ppt_id: recordItem.data[0].id,
            origin: "record",
            ai_template_type: 1,
            sourceFrom: 2
        })
        window.open(url, '_blank');
    }

    const getPreviewContent = () => {
        if (isNoDesign) {
            return (
                <div className={styles['noDesign_wrap']}>
                    <img src={IMG_CDN_PATH + 'no_design.png'} className={styles['noDesign_img']} alt="" />
                    <div className={styles['noDesign_tip']}>模版未生成，继续生成智能设计模版</div>
                </div>
            );
        }
        return (
            <>
                {recordItem.data.length > 0
                    ? recordItem.data.slice(0, 1).map((item) => {
                          return <PPTPreviewItem key={item.id} previewItem={item} pages={recordItem.pages}></PPTPreviewItem>;
                      })
                    : ''}
            </>
        );
    };
    return (
        <div className={styles['record_item']}>
            <div className={styles['preview_content']}>{getPreviewContent()}</div>
            <div className={styles['record_item_info']}>
                <div className={styles['create_time']}>{recordItem.updated}</div>
                <div className={styles['prompt']}>{recordItem.prompt}</div>
                {isNoDesign ?
                    <div className={styles['restart_btn']} onClick={goToDesign}>
                        继续生成
                    </div> :
                    <div className={styles['restart_btn']} onClick={freeEdit}>
                        免费编辑
                    </div>
                }
            </div>
        </div>
    );
};
