import { IPSConfig } from '@tgs/general_components';
import { TgsTypes } from '@tgs/types';
import { IpsUtils } from '@tgs/utils';
import { ipsApi, ipsDownload } from './config';
import { ProduceType } from '@/logic/MarkdownProcessor';

export const options: RequestInit = {
    credentials: 'include',
    //@ts-ignore
    // headers: {
    //     ...IPSConfig.getAuthenticatio(),
    // },
};
const downloadIps = process.env.NODE_ENV == 'development' ? ipsApi : ipsDownload;
// 单一模型推流链接
export const singleModalEventSourceUrl = (produceType?: ProduceType) => {
    switch (produceType) {
        case 'PPT':
            return ipsApi(`/ai-ppt/create`);
        case 'POSTER':
        default:
            return ipsApi(`/aiproduce/create-precis`);
    }
};
// 并发模型推流链接
export const concurrencyModalEventSourceUrl = ipsApi(`/aiproduce/template-data`);
export class PaintRequestManager {
    /**
     * @description:获取模板信息
     * @param {object} params
     * @return {*}
     */
    static async getTemplInfo(params: { picId: string; upicId?: string; version_id?: string }) {
        const fetchUrl = ipsApi('/api/user-get-templ');
        const url = IpsUtils.Url.joinUrlParams(fetchUrl, params);
        const res = await fetch(url, {
            ...options,
            method: 'GET',
        });
        return res.json();
    }

    /**
     * @description:获取对话预设
     * @param {object} params
     * @return {*}
     */
    static async getPresetDes() {
        const url = IpsUtils.Url.joinUrlParams(ipsApi('/aiproduce/theme-list?page=1&pageSize=20'), {});
        const res = await fetch(url, {
            ...options,
            credentials: 'include',
            // headers: { ...IPSConfig.getAuthenticatio() },
        });
        return res.json();
    }

    /**
     * @description:生成次数
     * @param {object} params
     * @return {*}
     */
    static async getGenerateNum() {
        const url = IpsUtils.Url.joinUrlParams(ipsApi('/aiproduce/get-num'), {});
        const res = await fetch(url, { ...options, credentials: 'include' });
        return res.json();
    }

    /**
     * @description:保存大纲
     * @param {object} params
     * @return {*}
     */
    static async saveOutline(params: {
        id: number;
        group_id: string;
        titile?: string[];
        sed_title?: string[];
        thd_title?: string[];
        main_text?: string[];
        time?: string;
    }) {
        const url = IpsUtils.Url.joinUrlParams(ipsApi('/aiproduce/edit-precis'), {});
        const res = await fetch(url, {
            ...options,
            credentials: 'include',
            method: 'POST',
            body: JSON.stringify({ ...params }),
        });
        return res.json();
    }

    static async saveUserTempl(params: { startTime: number; aiproduce_type: number }, template: any) {
        const url = IpsUtils.Url.joinUrlParams(ipsApi('/api/user-save-templ'), params);
        const res = await fetch(url, {
            method: 'POST',
            body: JSON.stringify({ ...template, aiproduce_type: params.aiproduce_type }),
            headers: {
                Accept: 'application/json',
            },
            credentials: 'include',
        });
        return res.json();
    }
    /**
     * @description: 下载模板
     * @param {object} params
     * @return {*}
     */
    static async downloadTempl(params: {
        upicId: string;
        aiproduce_id?: number;
        dpi?: number;
        format?: string;
        force?: number;
        version_id?: string;
        source?: number;
        ticket?: string;
        rendstr?: string;
        ai_ppt_id?: string;
    }) {
        const {
            upicId,
            aiproduce_id = '',
            dpi = 150,
            format = 'jpg',
            source = '0',
            force = '0',
            version_id = 0,
            ticket = '',
            rendstr = '',
            ai_ppt_id = '',
        } = params;
        const urlParams = {
            upicId,
            aiproduce_id,
            dpi,
            format: ai_ppt_id ? 'ppt' : 'pic',
            force: ai_ppt_id ? '1' : force,
            version_id,
            source,
            ticket,
            rendstr,
            ai_ppt_id,
            classify: ai_ppt_id ? 'ppt' : '',
            v: ai_ppt_id ? '2' : '',
        };
        let fetchUrl = downloadIps('/uapi/async-apply-download');
        if (ai_ppt_id) {
            fetchUrl = ipsApi('/uapi/async-apply-down-ppt-v5');
        }
        const url = IpsUtils.Url.joinUrlParams(fetchUrl, urlParams);

        const res = await fetch(url, {
            ...options,
        });
        return res.json();
    }
    /**
     * @description: 下载flag检查
     * @param {object} param
     * @return {*}
     */
    static async downloadFlagCheck(param: {
        upicId: string;
        jobId: string;
        version_id?: number;
        aiproduce_id?: number;
        ai_ppt_id?: string;
    }) {
        const { upicId, jobId, version_id = 0, aiproduce_id = '', ai_ppt_id = '' } = param;
        const urlParams = {
            upicId,
            jobId,
            version_id,
            ai_ppt_id,
            aiproduce_id,
            // classify: ai_ppt_id ? 'ppt' : '',
            // v: ai_ppt_id ? '2' : '',
        };

        const url = IpsUtils.Url.joinUrlParams(downloadIps('/uapi/async-check-download'), urlParams);
        const res = await fetch(url, options);
        return res.json();
    }
    /**
     * @description: 获取AI挑选的模板信息
     * @param {object} params
     */
    static async getTemplateList(params: { id: number }): Promise<{
        code: number;
        msg: string;
        data: {
            info: {
                id: number;
                title: string;
                description: string;
                width: number;
                height: number;
                kid_1: number;
                kid_2: number;
                kid_3: number;
                tags: string[];
                audit_through: number;
                template_type: number;
                dont_show_more_size: false;
            };
            recommendFont: null;
            stat: number;
            isRemake: number;
            preview: string[];
        }[];
    }> {
        const res = await fetch(ipsApi(`/aiproduce/template-list?id=${params.id}`), options);
        const result = await res.json();
        return result;
        // return {
        //     ...result,
        //     data: new Array(5).fill(result.data),
        // }
    }
    /**
     * @description: 上传oss
     * @param {string} filename
     * @param {string} prefix
     * @return {*}
     */
    static async getOssUploadPreviewToken(filename: string, prefix: string) {
        const res = await fetch(ipsApi(`/apiv3/upload-preview?filename=${filename}&prefix=${prefix}`), {
            ...options,
            headers: {
                ...(IPSConfig.getAuthenticatio() as HeadersInit),
            },
            method: 'GET',
        });
        return res.json();
    }
    static async putPreviewPoster(data: FormData) {
        const res = await fetch(ipsApi(`/apiv2/preview-set`), {
            ...options,
            headers: {
                ...(IPSConfig.getAuthenticatio() as HeadersInit),
            },
            method: 'POST',
            body: data,
        });
        return res.json();
    }
    /**
     * @description: 根据生成组id获取AI生成信息
     * @param {object} params
     * @return {*}
     */
    static async getAiProduceInfo(params: { id: string }) {
        const res = await fetch(ipsApi(`/aiproduce/list?group_unique=${params.id}`), options);
        return res.json();
    }
    /**
     * @description: 替换模板重新生成
     * @param {object} params
     * @return {*}
     */
    static async replaceTemplate(params: { id: string }) {
        const url = IpsUtils.Url.joinUrlParams(ipsApi('/aiproduce/again-template'), params);
        const res = await fetch(url, {
            ...options,
            method: 'GET',
        });
        return res.json();
    }
    /**
     * @description: 获取并发任务信息
     * @param {object} params
     * @return {*}
     */
    static async getConcurrencyTaskInfo(params: {
        name: string;
        sceneCategorize?: string;
        festival_name?: string;
        ai_template_type: string;
        ai_judge?: string;
        group_id?: string;
        image_id?: string;
    }) {
        const url = IpsUtils.Url.joinUrlParams(ipsApi('/aiproduce/template-info'), params);
        const res = await fetch(url, {
            ...options,
            method: 'GET',
        });
        return res.json();
    }
    /**
     * @description: 再获取几套模板
     * @param {object} params
     * @return {*}
     */
    static async getTemplateMore(params: { group_id: string; ai_template_type: string }) {
        const url = IpsUtils.Url.joinUrlParams(ipsApi('/aiproduce/again-template-more'), params);
        const res = await fetch(url, {
            ...options,
            method: 'GET',
        });
        return res.json();
    }
    static async getUserGenerateRecord(params: { page: number; type?: string; limit?: number }) {
        const { page = 1, limit = 10, type } = params;
        const url = IpsUtils.Url.joinUrlParams(ipsApi('/aiproduce/group-history'), { page, limit, produce_type: type });
        const res = await fetch(url, {
            ...options,
            method: 'GET',
        });
        return res.json();
    }

    // 获取ppt模版
    static async getPPTTemp(params: { page: number; pageSize: number }) {
        const url = IpsUtils.Url.joinUrlParams(ipsApi('/ai-ppt/search'), params);
        const res = await fetch(url, {
            ...options,
            method: 'GET',
        });
        return res.json();
    }

    // 获取ppt文案大纲
    static async getPPTOutLine(id: string) {
        const url = IpsUtils.Url.joinUrlParams(ipsApi('/ai-ppt/info'), { id });
        const res = await fetch(url, {
            ...options,
            method: 'GET',
        });
        return res.json();
    }

    /**
     * @description: 更新ppt模版
     * @param {object} params
     * @return {*}
     */
    static async savePicId(params: { ai_ppt_id: string; picId: string }) {
        const url = IpsUtils.Url.joinUrlParams(ipsApi('/ai-ppt/save-pic-id'), params);
        const res = await fetch(url, options);
        return res.json();
    }
    /**
     * @description: 根据生成组id获取AI生成信息
     * @param {object} params
     * @return {*}
     */
    static async searchAiProduceInfo(params: { id: string }) {
        const res = await fetch(ipsApi(`/aiproduce/search?group_id=${params.id}`), options);
        return res.json();
    }
    /**************新生成页接口start********************/
    // 新的稿定版本生成，不用markdown推流
    static async startGenerateTaskNew(params: {
        name: string;
        group_id: string;
        ai_judge: string;
        ai_template_type: string;
    }) {
        const res = await fetch(ipsApi('/aiproducev2/new-task'), {
            method: 'post',
            body: JSON.stringify(params),
            headers: {
                Accept: 'application/json',
            },
            credentials: 'include',
            ...options,
        });
        return res.json();
    }
    // 老页面的绘画check接口
    static async checkAiDrawResultV1(group_id: string) {
        const res = await fetch(ipsApi(`/aiproduce/draw-check?group_id=${group_id}`), options);
        return res.json();
    }
    static async getReletedTempNew(params: { group_id: string }) {
        const res = await fetch(ipsApi(`/aiproducev2/search?group_id=${params.group_id}`), options);
        return res.json();
    }
    static async getGenerateTaskData(params: { group_id: string; index?: number }) {
        const res = await fetch(
            ipsApi(`/aiproducev2/task-data?group_id=${params.group_id}&index=${params.index}`),
            options,
        );
        return res.json();
    }

    /**
     * @description: 获取AI生成信息
     * @param params aid: 百度外部链接
     * @returns
     */
    static async getAiContentListNew(params: { group_id: string, aid?: string }) {
        const addition = params.aid ? `&aid=${params.aid}&v=2` : '';
        const res = await fetch(ipsApi(`/aiproducev2/list?group_id=${params.group_id}${addition}`), options);
        return res.json();
    }
    static async getTemplateMoreNew(params: { group_id: string, is_prepare?: number | string }) {
        const res = await fetch(ipsApi(`/aiproducev2/change-batch?group_id=${params.group_id}&is_prepare=${params.is_prepare}`), options);
        return res.json();
    }
    /**************新生成页接口end********************/
    static async checkAiDrawResult(group_id:string){
        const res = await fetch(ipsApi(`/aiproducev2/draw-check?group_id=${group_id}`), options);
        return res.json();
    }
    static async saveAiDrawAsset(aiProduceDrawId:string){
        const res = await fetch(ipsApi(`/aiproducev2/user-assert?ai_produce_draw_id=${aiProduceDrawId}`), options);
        return res.json();
    }
}
