import { useEffect, useState } from 'react';
import styles from './scss/messageItem.module.scss';
import { useRef } from 'react';

export interface IMessageItem {
    avatar: string;
    text: string;
    children?: React.ReactNode;
    animate?: boolean;
}

export const MessageItem = (props: IMessageItem) => {
    const { avatar, text, animate = true, children } = props;
    const animateIndex = useRef(1);
    const timeoutRef = useRef<NodeJS.Timeout>();
    const [animateText, setAnimateText] = useState(text.slice(0, animateIndex.current));
    const animateInterval = 100;

    const startAnimate = () => {
        if (animateText.length >= text.length) {
            animateIndex.current = 1;
            return;
        }
        
        setAnimateText(text.slice(0, animateIndex.current++));
        timeoutRef.current = setTimeout(() => {
            startAnimate();
        }, animateInterval);
    };

    useEffect(() => {
        if (animate) {
            startAnimate();
        } else {
            setAnimateText(text);
        }

        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, [animate, text]);

    return (
        <div className={styles['message_item']}>
            <img src={avatar} className={styles['message_avatar']} alt="" />
            <div className={styles['message_text']}>
                <span>{animateText}</span>
                <>{children}</>
            </div>
        </div>
    );
};
