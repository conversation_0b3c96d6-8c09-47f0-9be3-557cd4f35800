import { ReactNode, useEffect } from 'react';
import styles from './scss/index.module.scss';
import { UserMenu } from '../UserMenu';
import { GenLimitModal } from '@/components/GenLimitModal';
import { useGlobalStatus } from '@/redux/adapter/useGlobalStatus';
import { VipRechargeModal } from '../VipRechargeModal';
import { HelpCenter } from '../HelpCenter';
import { setPv } from '@/http/pv';
import { LayoutHeader } from './Header/Header';
import { BrowserRouter } from 'react-router-dom';
import { BindPhoneModal } from '../BindPhoneModal';
import { message } from 'antd';
import { UploadAssetsModal } from '../UploadAssetsModal/UploadAssetsModal';

export const Layout = (params: { children: ReactNode }) => {
    const { children } = params;
    const {
        globalStatus: {
            showGenLimitModal,
            limitModalOrigin,
            showRechargeModal,
            showBindPhoneModal,
            bindPhoneModalOrigin,
        },
        setGenLimitModal,
        setRechargeModal,
        setBindPhoneModal,
    } = useGlobalStatus();
    const isNewGeneratePage = location.pathname.includes('/generate');

    return (
        <div className={styles['layout']}>
            <BrowserRouter>
                <LayoutHeader></LayoutHeader>
                {children}
            </BrowserRouter>
            <GenLimitModal
                open={showGenLimitModal}
                origin={limitModalOrigin}
                onCancel={() => {
                    setGenLimitModal(false);
                }}
            />
            <BindPhoneModal
                open={showBindPhoneModal}
                origin={bindPhoneModalOrigin}
                onCancel={() => {
                    setBindPhoneModal(false);
                }}
                onBindSuccess={() => {
                    setBindPhoneModal(false);
                    message.success('绑定成功');
                }}
            ></BindPhoneModal>
            <VipRechargeModal
                open={showRechargeModal}
                onCancel={() => {
                    setRechargeModal(false);
                    setPv(8343);
                }}
            ></VipRechargeModal>
            {!isNewGeneratePage && <HelpCenter></HelpCenter>}
        </div>
    );
};
