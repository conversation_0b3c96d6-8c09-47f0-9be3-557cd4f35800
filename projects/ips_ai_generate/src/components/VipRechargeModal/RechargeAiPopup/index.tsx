import React, { useCallback, useEffect, useRef, useState } from 'react';
import styles from './index.module.scss';
import { checkeAIPayStatus, getPayQrCode } from './api';
import { TgsModal } from '@tgs/general_components';
import { BaseRequestManager } from '@/http';
import { useUserSelector } from '@/redux/adapter/useUserSelector';
import { UserRequestManager } from '@/http/userRequest';
import { IMG_CDN_PATH } from '@/http/config';
import { Checkbox, message, Tooltip } from 'antd';
import { useGlobalStatus } from '@/redux/adapter/useGlobalStatus';
import { setPv } from '@/http/pv';
const { getPriceTextListApi, getTelLoginKey, getTelLogin, tencentChenckThreeableAuth } = BaseRequestManager;

interface IPriceProps {
    index: number;
    price: number;
    amountMax: number; //智能设计次数
    postNumber: number; //编辑器下载次数
    draw: number; //文生图
    kt: number; //抠图
    word: number; //ai写作
    extra?: number;
    vipType: string;
    checked: boolean;
    handleChecked(): void;
}
const PriceBoxText = ({
    index,
    price,
    amountMax,
    postNumber,
    kt,
    draw,
    word,
    extra,
    vipType,
    checked,
    handleChecked,
}: IPriceProps) => {
    
    return (
        <div
            className="ai_recharge_price overflow-hidden"
            onClick={handleChecked}
            style={
                checked
                    ? {
                          boxShadow: 'inset 0 0 0 2px #7B5AFF',
                          background: 'linear-gradient(180deg, #F8F6FF 0%, #FFFFFF 100%)',
                      }
                    : {}
            }
        >
            <div className="price-box">
                {index === 1 && <div className="suggest">超值推荐</div>}
                {vipType === '316' ? (
                    <div className="price-inner-box no-time-limit">
                        <div className="price-text-left">
                            <div className="price-text">3天畅用VIP</div>
                        </div>
                        <div className="price-text-center">
                            <div className="price-text">¥</div>
                            <div className="price" style={{ margin: '0 10px 0 5px' }}>
                                {price}
                            </div>
                            <div className="price-text">3天内</div>
                            <div className="price-text-color">不限生成次数</div>
                        </div>
                        <div className="price-bottom-box">
                            <div className="box-line">
                                <div className="price-text-icon">
                                    <i className="iconfont icon-ai-tuijiansel"></i>
                                    <span className="price-huobi price-huobi-color" style={{ fontWeight: 500 }}>
                                        超值
                                    </span>
                                </div>
                                <div>
                                    <span className="price-huobi">解锁畅用AI智能设计</span>
                                </div>
                                <Tooltip trigger={'hover'} title={() => (
                                        <>
                                            <div>·不含加赠增值权益内容</div>
                                            <div>
                                                ·购买后3天内不限智能生成次数
                                                和直接下载次数
                                            </div>
                                        </>
                                    )}>
                                    <div className='tip-icon'>
                                        <i className='iconfont icon-ai-wen'></i>
                                    </div>
                                </Tooltip>
                            </div>
                            <div className="box-line" style={{ marginTop: '-5px' }}>
                                <div className="price-huobi">
                                    <span>让你的</span>
                                    <span className="price-huobi-color">设计效率飙升</span>
                                    <span>，快人一步</span>
                                </div>

                            </div>
                        </div>
                    </div>
                ) : (
                    <div className="price-inner-box">
                        <div className="price-text-left">
                            <div className="price-text">1年VIP</div>
                        </div>
                        <div className="price-text-center">
                            <div className="price-text">¥</div>
                            <div className="price" style={{ margin: '0 10px 0 5px' }}>
                                {price}
                            </div>
                            <div className="price-text">智能设计</div>
                            <div className="price-text-color">{amountMax}</div>
                            <div className="price-text">次/每天</div>
                        </div>
                        <div className="price-bottom-box">
                            <div className="box-line">
                                <div className="price-text-icon">
                                    <i className="iconfont icon-ai-w_huo"></i>
                                    <span className="price-huobi price-huobi-color" style={{ fontWeight: 500 }}>
                                        加赠
                                    </span>
                                </div>
                                <div>
                                    <span className="price-huobi price-huobi-color">{postNumber}</span>
                                    <span className="price-huobi">张编辑器内下载/每天</span>
                                </div>
                                <Tooltip
                                    title={() => (
                                        <>
                                            <div>·加赠的增值权益VIP过期后失效</div>
                                            <div>
                                                ·赠送的下载张数可在编辑器内下载
                                                <br />
                                                &nbsp;非企业VIP专享的模版
                                            </div>
                                        </>
                                    )}
                                    onOpenChange={(open: boolean) => {
                                        if (open) {
                                            setPv(8346, {
                                                additional: {
                                                    s0: vipType,
                                                },
                                            });
                                        }
                                    }}
                                >
                                    <i className="iconfont icon-ai-wen" style={{ fontSize: '14px' }}></i>
                                </Tooltip>
                            </div>

                            <div className="box-line" style={{ marginTop: '-5px' }}>
                                <div className="price-huobi">
                                    <span className="price-huobi-color">{draw}</span>
                                    <span>张AI文生图</span>
                                </div>
                                <div className="price-huobi">
                                    <span className="price-huobi-color">{kt}</span>
                                    <span>次抠图</span>
                                </div>
                                <div className="price-huobi">
                                    <span className="price-huobi-color">{word}</span>
                                    <span>字AI写作</span>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
            {checked && (
                <div className="ai_recharge_checked_box">
                    <div className="triangle">
                        <svg
                            style={{ position: 'relative', top: '20px', right: '20px' }}
                            width="14"
                            height="14"
                            viewBox="0 0 14 14"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M5.36899 11.3367C5.25415 11.3367 5.09009 11.3066 4.952 11.1658L4.9438 11.1562L1.05005 7.2625C0.935205 7.14766 0.872314 6.9959 0.872314 6.83457C0.872314 6.67324 0.935205 6.52012 1.05005 6.40664C1.16489 6.2918 1.31665 6.22891 1.47798 6.22891C1.63931 6.22891 1.79243 6.2918 1.90591 6.40664L5.36763 9.86836L12.0928 3.14316C12.2077 3.02832 12.3594 2.96543 12.5208 2.96543C12.6821 2.96543 12.8352 3.02832 12.9487 3.14316C13.0635 3.25801 13.1264 3.40977 13.1264 3.57109C13.1264 3.73242 13.0635 3.88555 12.9487 3.99902L5.80239 11.1521C5.68208 11.2697 5.52485 11.3367 5.36899 11.3367Z"
                                fill="white"
                            />
                        </svg>
                    </div>
                </div>
            )}
        </div>
    );
};

interface IProps {
    isAiVip: boolean;
    produceNum: number;
    expireTime?: string;
    username: string;
    avatar: string;
    refresh(): void;
    onClose: () => void;
    setModalWidth: (width: number) => void;
}

type IStatus = 0 | 1 | 2; // 0 waiting 1 success 2 timeout
interface IPriceItem {
    vip_type: string;
    price: number;
    amount_max: number;
    give_kt: number;
    give_draw: number;
    give_word: number;
    ai_design_poster_num: number;
    suggest?: string;
}
const defaultVipIndex = 1
const RechargeAiPopup = ({
    isAiVip,
    produceNum,
    expireTime,
    username,
    avatar,
    refresh,
    onClose,
    setModalWidth,
}: IProps) => {
    const [select, setSelect] = useState<string>('99');
    const [price, setPrice] = useState<number>(99);
    const [priceList, setPriceList] = useState<IPriceItem[]>([]);
    const [status, setStatus] = useState<IStatus>(0);
    const [qrCode, setQrCode] = useState<string>('');
    const [popTencentFlag, setPopTencentFlag] = useState<number>(0);
    const [phoneNum, setPhoneNum] = useState<string>('');
    const [showQR, setShowQR] = useState<boolean>(true);
    const time = useRef<number>(0);
    const timer = useRef<number>(0);
    const timeout = useRef<number>(0);
    const successTimeout = useRef<number>(0);
    const {
        userInfo: { bind_phone },
    } = useUserSelector();
    const {
        globalStatus: { payModalOrigin },
    } = useGlobalStatus();
    const close = useCallback(() => {
        if (timer.current) {
            clearInterval(timer.current);
        }
        if (timeout.current) {
            clearTimeout(timeout.current);
        }
        if (successTimeout.current) {
            clearTimeout(successTimeout.current);
        }
        onClose();
        refresh();
    }, [refresh]);

    const check = useCallback(() => {
        checkeAIPayStatus(time.current).then((resultData) => {
            if (resultData.stat === 1) {
                message.success('充值成功！开始智能设计吧！');
                setPv(7598);
                setStatus(1);
                refresh();
                successTimeout.current = window.setTimeout(() => {
                    close();
                }, 2000);
                if (timer.current) {
                    clearInterval(timer.current);
                }
                if (timeout.current) {
                    clearTimeout(timeout.current);
                }
            }
        });
    }, [close, refresh, time]);

    const getQRCode = useCallback(
        (type: string) => {
            if (timer.current) {
                clearInterval(timer.current);
            }
            if (timeout.current) {
                clearTimeout(timeout.current);
            }
            if (successTimeout.current) {
                clearTimeout(successTimeout.current);
            }
            setStatus(0);
            getPayQrCode({ type, origin: payModalOrigin }).then((res) => {
                setPopTencentFlag(res.pop_tencent_flag);
                time.current = Number(new Date()) / 1000;
                setQrCode(res.unifyPayQrcode);
                timeout.current = window.setTimeout(() => {
                    if (timer.current) {
                        clearInterval(timer.current);
                    }
                    setStatus(2);
                }, 180 * 1000);
                timer.current = window.setInterval(() => {
                    check();
                }, 1000);
            });
        },
        [check],
    );

    const handleChecked = useCallback(
        (point: string, price: number) => {
            setSelect(point);
            getQRCode(point);
            setPrice(price);
            let pageId = 8344;
            switch (point) {
                case '312':
                    pageId = 8344;
                    break;
                case '313':
                    pageId = 8345;
                    break;
                case '316':
                    pageId = 8698;
                    break;
            }
            setPv(pageId);
        },
        [getQRCode],
    );
    useEffect(() => {
        setPv(8342);
    }, []);
    const getPriceTextList = useCallback(async () => {
        const res = await getPriceTextListApi();
        if (res.code === 1) {
            const resultData = res.data;
            if (resultData) {
                setPriceList(resultData.vip_type);
                setSelect(resultData.vip_type[defaultVipIndex].vip_type);
                getQRCode(resultData.vip_type[defaultVipIndex].vip_type);
                setPrice(resultData.vip_type[defaultVipIndex].price);
                setPhoneNum(resultData.phone);
                if (resultData.pop_tencent_flag) {
                    // TODO 埋点
                    // setPv(7989, {
                    //   additional: {
                    //     s0: resultData.pop_tencent_flag,
                    //     s1: 'work_platform',
                    //   }
                    // });
                }
            }
        }
    }, [getQRCode]);
    const getCode = async (num: string) => {
        const tokenRes = await getTelLoginKey();
        const tokenResData = await tokenRes.json();

        if (tokenResData.stat == 1) {
            const codeResult = await getTelLogin({
                num,
                noCodeImg: 1,
                sms_token: tokenResData.data,
            });
            return codeResult.json();
        }
    };
    useEffect(() => {
        getPriceTextList();
        return () => {
            if (timer.current) {
                clearInterval(timer.current);
            }
            if (timeout.current) {
                clearTimeout(timeout.current);
            }
            if (successTimeout.current) {
                clearTimeout(successTimeout.current);
            }
        };
    }, [bind_phone]);
    const checkThreeableAuth = async (data: {
        name: string;
        mobile: string;
        unique_id: string;
        card_id: string;
        band_card?: string;
    }) => {
        return await tencentChenckThreeableAuth(data);
    };
    const getUniqueId = async ({ bank_card = '' }) => {
        return await BaseRequestManager.getUniqueId(phoneNum, bank_card);
    };
    const onTencentSubmit = (validateStatus: number) => {
        setPv(7852, {
            additional: {
                s0: popTencentFlag,
                s1: 'work_platform',
            },
        });
        switch (validateStatus) {
            case -1:
            case 0:
                // 验证失败
                setPv(7858, {
                    additional: {
                        s0: popTencentFlag,
                        s1: 'work_platform',
                    },
                });

                return;
            case -2:
                // 达到限制
                close();
                setPv(7858, {
                    additional: {
                        s0: popTencentFlag,
                        s1: 'work_platform',
                    },
                });

                return;
            case 1:
                // 验证成功
                setPopTencentFlag(0);
                setPv(7854, {
                    additional: {
                        s0: popTencentFlag,
                        s1: 'work_platform',
                    },
                });
        }
    };
    if (!priceList) return null;
    const vipText = [
        '智能文案生成',
        '海量智能模版',
        '快速生成通道',
        '一键无水印下载',
        '版权保障个人商用无忧',
        '专属客服正规发票',
        '生成AI海报、PPT、公众号首图、长图',
    ];
    const rechargeText = ['个人商用下载权益', ' AI抠图次数', 'AI文生图次数', 'AI写作次数'];
    const renderRechargeBox = () => {
        return (
            <div className={styles.recharge_ai_box}>
                <div className="recharge_ai_content">
                    <div className="recharge_ai_content_left">
                        <div className="title_box">
                            <img className="title_img" src={IMG_CDN_PATH + 'ai_charge_logo.png?v=2'}></img>
                            <div className="title">VIP权益介绍</div>
                            <div className="spin"></div>
                        </div>
                        <div className="content_box">
                            <div className="text-box">
                                <div className="text-header">
                                    <i className="iconfont icon-ai-gongnengguanli"></i>
                                    <div className="header-title">VIP权益</div>
                                </div>
                                {vipText.map((item, index) => {
                                    return (
                                        <div key={index} className="ai_recharge_text_item">
                                            <div className="point"></div>
                                            <div className="ai_recharge_text">{item}</div>
                                        </div>
                                    );
                                })}
                            </div>

                            <div className="text-box">
                                <div className="text-header">
                                    <i className="iconfont icon-ai-zengzhibao"></i>
                                    <div className="header-title">增值权益</div>
                                </div>
                                {rechargeText.map((item, index) => {
                                    return (
                                        <div key={index} className="ai_recharge_text_item">
                                            <div className="point"></div>
                                            <div className="ai_recharge_text_red">加赠</div>
                                            <div className="ai_recharge_text">{item}</div>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>
                    </div>
                    <div className="recharge_ai_content_right">
                        <div className="recharge_ai_header">
                            <div className="recharge_uesr_box">
                                <div
                                    className="recharge_user_image"
                                    style={{
                                        backgroundImage: `url(${avatar})`,
                                        backgroundSize: '100% 100%',
                                    }}
                                ></div>
                                <div className="recharge_user_info">
                                    <div className="username">{username}</div>
                                    <div className="remain">
                                        {(isAiVip ? (
                                            <div className="remain-item" style={{ color: '#7B5AFF' }}>
                                                已开通
                                            </div>
                                        ) : (
                                            <div className="remain-item" style={{ color: '#797676' }}>
                                                未开通
                                            </div>
                                        )) || null}
                                        {(isAiVip && expireTime && (
                                            <div className="remain-item">到期时间: {expireTime}</div>
                                        )) ||
                                            null}
                                        <div className="remain-item">今日剩余智能设计次数: {produceNum}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="recharge_ai_price_box">
                            {priceList.map((item, index) => (
                                <PriceBoxText
                                    index={index}
                                    key={item.vip_type}
                                    price={item.price}
                                    amountMax={item.amount_max}
                                    kt={item.give_kt}
                                    draw={item.give_draw}
                                    checked={select === item.vip_type}
                                    word={item.give_word}
                                    postNumber={item.ai_design_poster_num}
                                    vipType={item.vip_type}
                                    handleChecked={() => {
                                        handleChecked(item.vip_type, item.price);
                                    }}
                                />
                            ))}
                        </div>
                        <div className="recharge_ai_pay_box">
                            {status !== 1 ? (
                                <>
                                    <div>
                                        <div
                                            className="recharge_ai_pay_qrcode"
                                            style={{
                                                backgroundImage: `url("${qrCode}")`,
                                                backgroundSize: '100% 100%',
                                                opacity: `${showQR ? '1' : '0'}`,
                                            }}
                                        >
                                            {status === 2 && (
                                                <div
                                                    className="res_mask"
                                                    onClick={() => {
                                                        getQRCode(select);
                                                    }}
                                                >
                                                    <i className="iconfont icon-zhongxin" style={{ fontSize: '14px' }}>
                                                        二维码已过期
                                                    </i>
                                                    <div className="refresh-btn">刷新</div>
                                                </div>
                                            )}
                                        </div>
                                        <div
                                            style={{
                                                marginTop: '15px',
                                                lineHeight: '20px',
                                                display: 'flex',
                                                alignItems: 'center',
                                            }}
                                        >
                                            <i className="pay_icon alipay"></i>
                                            <span>支付宝</span>
                                            <span>/</span>
                                            <div className="pay_icon wechat"></div>
                                            <span>微信扫码支付</span>
                                        </div>
                                    </div>
                                    <div className="recharge_ai_pay_message">
                                        <div>
                                            <div style={{ marginBottom: '15px' }}>支付金额：</div>
                                            <span
                                                style={{
                                                    color: '#DB0B0B',
                                                    fontWeight: '700',
                                                }}
                                            >
                                                <span style={{ marginBottom: '1px', fontSize: '14px' }}>￥</span>
                                                <span style={{ fontSize: '28px' }}>{price}</span>
                                            </span>
                                        </div>
                                        <div className="pay_message_tip">
                                            <Checkbox
                                                defaultChecked={true}
                                                onChange={(e) => setShowQR(e.target.checked)}
                                            />
                                            <div
                                                style={{
                                                    lineHeight: '20px',
                                                    fontSize: '12px',
                                                }}
                                            >
                                                支付即视为你同意
                                                {/* todo 颜色 字重 */}
                                                <span
                                                    style={{ color: '#0057ff', cursor: 'pointer' }}
                                                    onClick={() => {
                                                        window.open('https://818ps.com/page/70');
                                                    }}
                                                >
                                                    {' '}
                                                    相关协议{' '}
                                                </span>
                                                条例
                                            </div>
                                        </div>
                                    </div>
                                </>
                            ) : (
                                <div className="recharge_ai_pay_success">
                                    <div className="success_icon">
                                        <i className="iconfont icon-duigou"></i>
                                    </div>
                                    <div>充值成功</div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    const renderBox = () => {
        switch (popTencentFlag) {
            case -2:
            case -1:
                setModalWidth(600);
                return <TgsModal.ContactServiceAgent onClose={close}></TgsModal.ContactServiceAgent>;
            // 不需要认证
            case 0:
                setModalWidth(770);
                return renderRechargeBox();
            // 需要实名认证
            // 需要实名认证
            case 1:
                setModalWidth(420);
                return (
                    <TgsModal.BindPhone
                        onSubmit={onTencentSubmit}
                        submitBindPh={UserRequestManager.bindPh}
                        getCode={getCode}
                    ></TgsModal.BindPhone>
                );
            case 2:
                setModalWidth(420);
                return (
                    <TgsModal.TencentValidateBox
                        phoneNum={phoneNum}
                        submitValidate={checkThreeableAuth}
                        onSubmit={onTencentSubmit}
                        getUniqueId={getUniqueId}
                    ></TgsModal.TencentValidateBox>
                );
            case 3:
                setModalWidth(420);

                return (
                    <TgsModal.TencentValidateBox
                        phoneNum={phoneNum}
                        submitValidate={checkThreeableAuth}
                        onSubmit={onTencentSubmit}
                        getUniqueId={getUniqueId}
                        validateElementNums={4}
                    ></TgsModal.TencentValidateBox>
                );
            default:
                return <></>;
        }
    };
    return <>{renderBox()}</>;
};
export default RechargeAiPopup;
