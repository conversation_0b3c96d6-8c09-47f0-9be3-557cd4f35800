import { IMG_CDN_PATH } from '@/http/config';

// export const SaveResult = () => {
//     return (
//         <>
//             生成结果已保存到
//             <a
//                 href="https://818ps.com/home/<USER>"
//                 target="_blank"
//                 style={{ marginLeft: '4px', color: '#1f1a1b' }}
//             >
//                 我的设计
//             </a>
//             ，您可以点击下方作品免费编辑🎉
//         </>
//     );
// };
export const SaveResult = () => {
    return (
        <>
            作品生成完成可免费编辑，AI标识模板需手动编辑保存，其他模板自动保存哦～
        </>
    );
};
export const messageListConfig = [
    {
        id: '1',
        text: '请耐心等待正在渲染模版.....',
        avatar: IMG_CDN_PATH + 'ai_avatar.png',
        showPercent: 0,
    },
    {
        id: '2',
        text: '正在构思下一步，即将完成模版设计~',
        avatar: IMG_CDN_PATH + 'ai_avatar.png',
        showPercent: 20,
    },
    {
        id: '3',
        text: '我正在幕后努力工作，很快就会带给你一些惊喜。稍等一下哦',
        avatar: IMG_CDN_PATH + 'ai_avatar.png',
        showPercent: 50,
    },
    {
        id: '4',
        text: '模板渲染设计完成',
        avatar: IMG_CDN_PATH + 'ai_avatar.png',
    },
    {
        id: '5',
        text: '正在为您保存模板作品到我的设计',
        avatar: IMG_CDN_PATH + 'ai_avatar.png',
    },
    {
        id: '6',
        text: '',
        children: SaveResult(),
        animate: false,
        avatar: IMG_CDN_PATH + 'ai_avatar.png',
    },
];
export const SavePptResult = () => {
    return (
        <>
            生成结果已保存到
            <a
                href="https://818ps.com/home/<USER>"
                target="_blank"
                style={{ marginLeft: '4px', color: '#1f1a1b' }}
            >
                我的设计
            </a>
            ，您可以点击右侧预览区域免费编辑🎉
        </>
    );
};
export const pptMessageListConfig = [
    {
        id: '1',
        text: '请耐心等待正在渲染ppt.....',
        avatar: IMG_CDN_PATH + 'ai_avatar.png',
        showPercent: 0,
    },
    {
        id: '2',
        text: '正在构思下一步，即将完成ppt设计~',
        avatar: IMG_CDN_PATH + 'ai_avatar.png',
        showPercent: 20,
    },
    {
        id: '3',
        text: '我正在幕后努力工作，很快就会带给你一些惊喜。稍等一下哦',
        avatar: IMG_CDN_PATH + 'ai_avatar.png',
        showPercent: 50,
    },
    {
        id: '4',
        text: 'ppt渲染设计完成',
        avatar: IMG_CDN_PATH + 'ai_avatar.png',
    },
    {
        id: '5',
        text: '正在为您保存ppt作品到我的设计',
        avatar: IMG_CDN_PATH + 'ai_avatar.png',
    },
    {
        id: '6',
        text: '',
        children: SavePptResult(),
        animate: false,
        avatar: IMG_CDN_PATH + 'ai_avatar.png',
    },
];
