{"name": "ips_ai_generate", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "reploy": "node ./Tool/AD/src/main/reploy.js", "reploy:test": "node ./Tool/AD/src/main/reploy.js -c"}, "dependencies": {"@ant-design/cssinjs": "^1.20.0", "@reduxjs/toolkit": "^2.2.5", "@tgs/canvas": "workspace:^", "@tgs/general_components": "workspace:^0.2.16-beta", "@tgs/ips_text": "workspace:^0.1.0", "@tgs/utils": "workspace:^", "ahooks": "^3.7.2", "antd": "^5.18.0", "consola": "^3.2.3", "js-md5": "^0.7.3", "marked": "^13.0.1", "react": "^18.2.0", "react-activation": "^0.12.4", "react-dom": "^18.2.0", "react-redux": "^9.1.2", "react-router-dom": "^6.23.1", "sass": "^1.32.8", "transformation-matrix": "^2.8.0"}, "devDependencies": {"@tgs/types": "workspace:^", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-redux": "^7.1.11", "@vitejs/plugin-basic-ssl": "^1.0.1", "@vitejs/plugin-legacy": "^4.1.1", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "rollup-plugin-visualizer": "^5.12.0", "typescript": "^5.0.2", "vite": "^5.2.0", "vite-plugin-commonjs": "^0.10.3"}}