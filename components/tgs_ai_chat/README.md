# TGS AI Chat Component

一个功能完整的 AI 聊天组件，可以轻松集成到任何 React 应用中。该组件支持deepseek AI 模型、聊天历史管理、Markdown 渲染以及大小尺寸适应

## 特性

- 💬 完整的聊天界面，支持消息历史显示
- 🤖 Both deepseek chat and reasoner supported
- 🔒 mixed with outside login status
- ✨ Markdown 渲染支持

## 安装

```bash

# 使用 pnpm
pnpm add @tgs/ai_chat
```

## 基本用法

```jsx
import React from 'react';
import { TgsAiChat } from '@tgs/ai_chat';

const App = () => {
  return (
    <div style={{ width: '800px', height: '600px' }}>
      <TgsAiChat
        handleLogin={login}
        loginStatus={loginStatus}
        useMockApi={true}
        onClose={() => console.log('Chat closed')}
      />
    </div>
  );
};

export default App;
```

## 属性

| 属性名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| handleLogin | `() => void` | - | 登录回调函数 |
| loginStatus | `boolean` | - | 登录状态 |
| useMockApi | `boolean` | `false` | 是否使用模拟 API |
| initialPrompt | `string` | - | 初始提示消息 |
| onClose | `() => void` | - | 关闭聊天时的回调函数 |

## 高级用法

### 自定义 API 端点

```jsx
<TgsAiChat
  apiEndpoints={{
    initSession: '/api/chat/init',
    sendMessage: '/api/chat/send',
    getHistory: '/api/chat/history',
    getModels: '/api/chat/models',
    getSessionList: '/api/chat/session-list',
  }}
/>
```

### 自定义认证处理

```jsx
<TgsAiChat
  authHandler={{
    isAuthenticated: outside redux user info,
    login: () => function to call login model,
  }}
/>
```

## 响应式设计

该组件自动适配不同屏幕尺寸：

- big size accept more then 760px, message width size maxed by 760px
- small size accept only 360px, message width size maxed by 313px

## 开发

```bash
# 安装依赖
pnpm install

# 开发模式
pnpm dev

# 构建
pnpm build
```

## 示例运行

要运行示例应用，请执行以下步骤：

1. 安装依赖：
   ```bash
   pnpm install
   ```

2. 启动开发服务器：
   ```bash
   pnpm dev
   ```

3. 在浏览器中访问：
   ```
   https://ue.818ps.com:3000
   ```

--------
