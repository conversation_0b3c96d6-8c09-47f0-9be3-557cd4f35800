import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import basicSSL from '@vitejs/plugin-basic-ssl';
import path from 'path';
import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js';
import commonjs from 'vite-plugin-commonjs'

// https://vitejs.dev/config/
export default defineConfig(({ command }) => {
  const isDev = command === 'serve';
  
  return {
    plugins: [cssInjectedByJsPlugin(), react(), basicSSL(), commonjs()],
    build: {
      lib: {
        entry: path.resolve(__dirname, './src/index.ts'),
        name: '@tgs/ai_chat',
        formats: ['es', 'cjs'],
        fileName: 'ai_chat',
      },
      rollupOptions: {
        external: ['react', 'react-dom', 'antd', 'classnames', 'marked'],
        output: {
          // 在 UMD 构建中，确保输出格式为 UMD
        },
        input: path.resolve(__dirname, './src/index.ts'),
      },
    },
    optimizeDeps: {
      include: ['react'],
    },
    esbuild: {
      // 忽略类型检查
      tsconfigRaw: {
        compilerOptions: {
          skipLibCheck: true,
          noEmitOnError: false
        }
      }
    },
    define: {
      'process.env': {},
      'global': 'globalThis',
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
    },
    // Add development server configuration
    ...(isDev && {
      root: './example',
      server: {
        host: '0.0.0.0',
        port: 3000,
        open: 'https://ue.818ps.com:3000',
        https: true,
        allowedHosts: ['818ps.com', 'ue.818ps.com']
      },
      build: {
        rollupOptions: {
          input: {
            main: path.resolve(__dirname, 'example/index.html'),
            markdown: path.resolve(__dirname, 'example/markdown.html')
          }
        }
      }
    }),
  };
});
