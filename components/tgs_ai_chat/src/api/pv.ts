const pvParamsMap: Record<string, string> = {
    modelId: 's4',
    sourceFrom: 'i4',
    ai_template_type: 's3',
    scene_id: 'i2',
    origin: 'ro',
};
/**
 * @description: 放一些所有埋点都会加的参数
 * @param {number} page_id
 * @param {*} paras
 * @return {*}
 */
export const setPv = (
    page_id: number,
    paras: { additional?: { [key: string | number]: string | number } } = { additional: {} },
) => {
    if (paras && paras.additional) {
        // 默认携带i4字段，表示来源，1: home页； 2: 生成页
        paras.additional['i4'] = 1;
        const urlParams = getUrlParams(window.location.href);
        for (const key in urlParams) {
            if (pvParamsMap[key]) {
                paras.additional[pvParamsMap[key]] = urlParams[key];
            }
        }
    }

    setPv_new(page_id, paras);
};

/**
 * @description: 获取url参数
 * @param {string} url
 * @return {*}
 */
export const getUrlParams = (url: string) => {
    if (!url) return {};
    const obj: Record<string, string> = {};
    const param = url.split('?')[1];
    if (!param) return {};
    const parr = param.split('&');
    for (const i of parr) {
        const arr = i.split('=');
        obj[arr[0]] = arr[1];
    }
    return obj;
};
export const setPv_new = (
    page_id: number,
    paras: { additional?: { [key: string | number]: string | number } } = {},
) => {
    let dataStr = 'page_id=' + page_id;

    if (paras.additional) {
        for (let key in paras.additional) {
            dataStr += '&' + key + '=' + paras.additional[key];
        }
    }
    /* 新埋点请求 */
    setNewPv(dataStr);
    /* 新埋点请求 */
};
/**
 * 转换新pv参数名
 * @param {*} oldKey
 */
const newPvTransferTools = (oldKey = '') => {
    let keysArr = [
            //新老键名对比
            { old: 'page_id', new: 'pid' },
            { old: 'referer', new: 'r' },
            { old: 'origin', new: 'ro' },
            { old: 'keyword', new: 'kw' },
            { old: 'abType', new: 'ab' },
            { old: 'templ_id', new: 'ti' },
            { old: 'user_templ_id', new: 'ut' },
            { old: 'load_time', new: 'i0' },
            { old: 'class_id', new: 'ki' },
            { old: 'route_id', new: 'ri' },
            { old: 'route', new: 'rb' },
            { old: 'after_route', new: 'ra' },
            { old: 'dom_load_time', new: 'dom_load_time' },
            { old: 'img_load_time', new: 'img_load_time' },
        ],
        resKey = oldKey;

    for (let i = 0, len = keysArr.length; i < len; ++i) {
        let item = keysArr[i];
        if (oldKey == item.old) {
            resKey = item.new;
            break;
        }
    }
    return resKey;
};
const getCookieTools = (name: string) => {
    //可以搜索RegExp和match进行学习
    var arr,
        reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)');
    if ((arr = document.cookie.match(reg))) {
        return unescape(arr[2]);
    } else {
        return null;
    }
};
const setNewPv = (dataStr = '', options = {}) => {
    let paramsArr = dataStr.split('&'),
        newParams = [],
        newParamsStr = '',
        deleteParams = ['pram', 'option', 'type']; /* 需要删除的属性*/
    paramsArr.map((v) => {
        let itemArr = v.split('='),
            newKey = newPvTransferTools(itemArr[0]);
        /*过滤控制 和值等于 undefined的  */
        if (itemArr[1] && itemArr[1] != 'undefined' && itemArr[1] != 'NaN' && deleteParams.indexOf(newKey) < 0) {
            newParams.push(`${newKey}=${itemArr[1]}`);
        }
    });
    /* 获取cookie */
    let userInfoCookie;
    userInfoCookie = getCookieTools('ui_818ps');
    userInfoCookie ? (userInfoCookie = encodeURIComponent(userInfoCookie)) : '';
    /* 获取cookie */
    userInfoCookie && newParams.push(`ui=${userInfoCookie}`); //用户信息
    newParams.push(`pt=web`); //站点信息
    newParams.push(`u=${encodeURIComponent('' + location.href)}`); //用户地址信息
    newParams.push(`v=${new Date().getTime()}`); //添加时间戳
    newParamsStr = newParams.join('&');
    return fetch('//p.818ps.com/p.gif?' + newParamsStr, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        mode: 'no-cors', //后台做过配置 设置为cors会报错
        ...options,
    }).catch((error: any) => {
        console.error('setNewPv error', error);
        // 捕获错误，防止影响其他功能
    });
};
