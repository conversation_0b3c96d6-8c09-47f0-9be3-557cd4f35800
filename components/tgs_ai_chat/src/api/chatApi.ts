import { IChatCreateSessionResponse, IChatSessionListResponse, IDelSessionResponse, IDrawCheckResponse, ISaveImageResponse, IAddUserAssetResponse, IChatPromptSuggestionResponse, IChatListResponse } from '../types';
import { DEFAULT_API_ENDPOINTS } from '../constants';
import { IAppChatApi } from '../types/api';
import { getAuthenticatio } from '../utils/chatUtils';

// Request options with credentials
const defaultOptions = {
  credentials: 'include' as RequestCredentials,
};

/**
 * Base API class with common functionality
 */
class BaseApi {
    protected endpoints: typeof DEFAULT_API_ENDPOINTS;
    protected options: typeof defaultOptions;
    protected apiBaseUrl: string;

    constructor(
        apiBaseUrl: string = '',
        customEndpoints?: Partial<typeof DEFAULT_API_ENDPOINTS>,
        customOptions?: Partial<typeof defaultOptions>,
    ) {
        this.apiBaseUrl = apiBaseUrl;
        this.endpoints = { ...DEFAULT_API_ENDPOINTS, ...customEndpoints };
        this.options = { ...defaultOptions, ...customOptions };
    }

    protected getApiUrl(endpoint: string): string {
        return `${this.apiBaseUrl}${endpoint}`;
    }

    protected async get<T>(endpoint: string, params?: Record<string, string | number>, options?: RequestInit): Promise<T> {
        try {
            const queryString = params ? '?' + new URLSearchParams(params as Record<string, string>).toString() : '';
            const url = this.getApiUrl(endpoint + queryString);
            const config = {
                method: 'GET',
                ...this.options,
                ...options,
            };
            
            const response = await fetch(url, config);
            return await response.json();
        } catch (error) {
            console.error(`Failed to execute GET request to ${endpoint}:`, error);
            throw error;
        }
    }

    protected async post<T>(endpoint: string, data?: any, params?: Record<string, string | number>, options?: RequestInit): Promise<T> {
        try {
            const queryString = params ? '?' + new URLSearchParams(params as Record<string, string>).toString() : '';
            const url = this.getApiUrl(endpoint + queryString);
            const config = {
                method: 'POST',
                ...this.options,
                body: data ? JSON.stringify(data) : undefined,
                ...options,
            };
            
            const response = await fetch(url, config);
            return await response.json();
        } catch (error) {
            console.error(`Failed to execute POST request to ${endpoint}:`, error);
            throw error;
        }
    }
}

/**
 * Chat module API class
 */
class ChatModule extends BaseApi {
    async initChatSession(prompt: string, sourceFrom: number): Promise<IChatCreateSessionResponse> {
        try {
            return await this.post<IChatCreateSessionResponse>(this.endpoints.INIT_SESSION, {
                prompt,
                origin: sourceFrom
            });
        } catch (error) {
            return {
                code: 0,
                msg: 'Failed to initialize chat session',
                data: {
                    session_id: ''
                },
            };
        }
    }

    async getHistoryMessages(session_id: string): Promise<IChatListResponse> {
        try {
            return await this.get<IChatListResponse>(this.endpoints.GET_HISTORY, { session_id });
        } catch (error) {
            return {
                code: 0,
                data: {
                    list: []
                },
                msg: 'Failed to get history messages'
            };
        }
    }

    async getSessionList(page_id: number, limit: number): Promise<IChatSessionListResponse> {
        try {
            return await this.get<IChatSessionListResponse>(this.endpoints.GET_SESSION_LIST, { page_id, limit });
        } catch (error) {
            return {
                code: 0,
                msg: 'Failed to get session list',
                data: {
                    list: [],
                },
            };
        }
    }

    async getSuggestionsPrompt(): Promise<IChatPromptSuggestionResponse> {
        try {
            return await this.get<IChatPromptSuggestionResponse>(this.endpoints.GET_SUGGESTIONS);
        } catch (error) {
            return {
                code: 0,
                msg: 'Failed to get suggestions prompt',
                data: {
                    list: []
                },
            };
        }
    }

    async delSession(session_id: string): Promise<IDelSessionResponse> {
        try {
            return await this.get<IDelSessionResponse>(this.endpoints.DEL_SESSION, { session_id });
        } catch (error) {
            return {
                code: 0,
                data: {
                    session_id: '',
                },
                msg: 'Failed to delete session',
            };
        }
    }
        /**
     * @description:生成次数
     * @param {object} params
     * @return {*}
     */
    async getGenerateNum(): Promise<any> {
        try {
            return await this.get<any>(this.endpoints.GET_GENERATE_NUM);
        } catch (error) {
            return null;
        }
    }
}

/**
 * Template module API class
 */
class TemplateModule extends BaseApi {
    async drawCheck(group_id: string): Promise<IDrawCheckResponse> {
        try {
            return await this.get<IDrawCheckResponse>(this.endpoints.DRAW_CHECK, { group_id });
        } catch (error) {
            return {
                code: 0,
                data: [],
                msg: 'Failed to get draw check'
            };
        }
    }

    async addUserAsset(group_id: string, asset_id: string): Promise<IAddUserAssetResponse> {
        try {
            return await this.post<IAddUserAssetResponse>(this.endpoints.ADD_USER_ASSET, { group_id, asset_id });
        } catch (error) {
            return {
                code: 0,
                data: {
                    ua_id: '',
                    picId: 0,
                    ai_produce_draw_id: '',
                    message: 'Failed to add user asset',
                },
                msg: 'Failed to add user asset',
            };
        }
    }

    async saveImage(ai_produce_draw_id: string): Promise<ISaveImageResponse> {
        try {
            return await this.get<ISaveImageResponse>(this.endpoints.SAVE_IMAGE, { ai_produce_draw_id });
        } catch (error) {
            return {
                code: 0,
                data: [{
                    ua_id: '',
                    picId: 0,
                    ai_produce_draw_id: '',
                    message: 'Failed to save image',
                }],
                msg: 'Failed to save image',
            };
        }
    }

    async getOssUploadPreviewToken(filename: string, prefix: string) {
        return await this.get(`/apiv3/upload-preview?filename=${filename}&prefix=${prefix}`, {}, {
            headers: {
                ...(getAuthenticatio() as HeadersInit)
            }
        });
    }

    async getTemplInfo(params: { picId: string; upicId?: string; version_id?: string }) {
        const queryString = params ? '?' + new URLSearchParams(params as Record<string, string>).toString() : '';
        return await this.get(this.endpoints.GET_TEMPL_INFO + queryString);
    }

    async getPresetDes() {
        const queryString = '?page=1&pageSize=20';
        return await this.get(this.endpoints.GET_PRESET_DES + queryString);
    }

    async getGenerateNum() {
        return await this.get(this.endpoints.GET_GENERATE_NUM);
    }

    async saveOutline(params: {
        id: number;
        group_id: string;
        titile?: string[];
        sed_title?: string[];
        thd_title?: string[];
        main_text?: string[];
        time?: string;
    }) {
        return await this.post(this.endpoints.SAVE_OUTLINE, params);
    }

    async saveUserTempl(params: { startTime: number; aiproduce_type: number }, template: any) {
        const queryString = '?startTime=' + params.startTime + '&aiproduce_type=' + params.aiproduce_type;
        return await this.post(this.endpoints.SAVE_USER_TEMPL + queryString, { ...template, aiproduce_type: params.aiproduce_type });
    }

    async downloadTempl(params: {
        upicId: string;
        aiproduce_id?: number;
        dpi?: number;
        format?: string;
        force?: number;
        version_id?: string;
        source?: number;
        ticket?: string;
        rendstr?: string;
        ai_ppt_id?: string;
    }) {
        const {
            upicId,
            aiproduce_id = '',
            dpi = 150,
            format = 'jpg',
            source = '0',
            force = '0',
            version_id = 0,
            ticket = '',
            rendstr = '',
            ai_ppt_id = '',
        } = params;
        const urlParams = {
            upicId,
            aiproduce_id,
            dpi,
            format: ai_ppt_id ? 'ppt' : 'pic',
            force: ai_ppt_id ? '1' : force,
            version_id,
            source,
            ticket,
            rendstr,
            ai_ppt_id,
            classify: ai_ppt_id ? 'ppt' : '',
            v: ai_ppt_id ? '2' : '',
        };
        const queryString = '?' + new URLSearchParams(urlParams as unknown as Record<string, string>).toString();
        const url = ai_ppt_id ? this.endpoints.ASYNC_APPLY_DOWN_PPT_V5 : this.endpoints.ASYNC_APPLY_DOWNLOAD;
        return await this.get(url + queryString);
    }

    async downloadFlagCheck(param: {
        upicId: string;
        jobId: string;
        version_id?: number;
        aiproduce_id?: number;
        ai_ppt_id?: string;
    }) {
        const { upicId, jobId, version_id = 0, aiproduce_id = '', ai_ppt_id = '' } = param;
        const urlParams = {
            upicId,
            jobId,
            version_id,
            ai_ppt_id,
            aiproduce_id,
        };
        const queryString = '?' + new URLSearchParams(urlParams as unknown as Record<string, string>).toString();
        return await this.get(this.endpoints.ASYNC_CHECK_DOWNLOAD + queryString);
    }

    async getTemplateList(params: { id: number }) {
        const queryString = '?id=' + params.id;
        return await this.get(this.endpoints.GET_TEMPLATE_LIST + queryString);
    }

    async putPreviewPoster(data: FormData) {
        return await this.post(this.endpoints.PUT_PREVIEW_POSTER, data, {}, {
            headers: {
                ...(getAuthenticatio() as HeadersInit),
            }
        });
    }

    async getAiProduceInfo(params: { id: string }) {
        return await this.get(this.endpoints.GET_AI_PRODUCE_INFO + `?group_unique=${params.id}`);
    }

    async replaceTemplate(params: { id: string }) {
        const queryString = '?id=' + params.id;
        return await this.get(this.endpoints.REPLACE_TEMPLATE + queryString);
    }

    async getConcurrencyTaskInfo(params: {
        name: string;
        sceneCategorize?: string;
        festival_name?: string;
        ai_template_type: string;
        ai_judge?: string;
        group_id?: string;
        image_id?: string;
    }) {
        const queryString = '?' + new URLSearchParams(params as unknown as Record<string, string>).toString();
        return await this.get(this.endpoints.GET_CONCURRENCY_TASK_INFO + queryString);
    }

    async getTemplateMore(params: { group_id: string; ai_template_type: string }) {
        const queryString = '?' + new URLSearchParams(params as unknown as Record<string, string>).toString();
        return await this.get(this.endpoints.GET_TEMPLATE_MORE + queryString);
    }

    async getUserGenerateRecord(params: { page: number; type?: string; limit?: number }) {
        const { page = 1, limit = 10, type } = params;
        const queryString = '?page=' + page + '&limit=' + limit + '&produce_type=' + type;
        return await this.get(this.endpoints.GET_USER_GENERATE_RECORD + queryString);
    }

    async getPPTTemp(params: { page: number; pageSize: number }) {
        const queryString = '?page=' + params.page + '&pageSize=' + params.pageSize;
        return await this.get(this.endpoints.GET_PPT_TEMP + queryString);
    }

    async getPPTOutLine(id: string) {
        const queryString = '?id=' + id;
        return await this.get(this.endpoints.GET_PPT_OUTLINE + queryString);
    }

    async savePicId(params: { ai_ppt_id: string; picId: string }) {
        const queryString = '?ai_ppt_id=' + params.ai_ppt_id + '&picId=' + params.picId;
        return await this.get(this.endpoints.SAVE_PIC_ID + queryString);
    }

    async searchAiProduceInfo(params: { id: string }) {
        const queryString = '?group_id=' + params.id;
        return await this.get(this.endpoints.SEARCH_AI_PRODUCE_INFO + queryString);
    }

    async startGenerateTaskNew(params: {
        name: string;
        group_id: string;
        ai_judge: string;
        ai_template_type: string;
    }) {
        return await this.post(this.endpoints.START_GENERATE_TASK_NEW, params);
    }

    async checkAiDrawResultV1(group_id: string) {
        const queryString = '?group_id=' + group_id;
        return await this.get(this.endpoints.CHECK_AI_DRAW_RESULT_V1 + queryString);
    }

    async getReletedTempNew(params: { group_id: string }) {
        const queryString = '?group_id=' + params.group_id;
        return await this.get(this.endpoints.GET_RELETED_TEMP_NEW + queryString);
    }

    async getGenerateTaskData(params: { group_id: string; index?: number }) {
        const queryString = '?group_id=' + params.group_id + '&index=' + params.index;
        return await this.get(this.endpoints.GET_GENERATE_TASK_DATA + queryString);
    }

    async getAiContentListNew(params: { group_id: string, aid?: string }) {
        const addition = params.aid ? `&aid=${params.aid}&v=2` : '';
        const queryString = '?group_id=' + params.group_id + addition;
        return await this.get(this.endpoints.GET_AI_CONTENT_LIST_NEW + queryString);
    }

    async getTemplateMoreNew(params: { group_id: string, is_prepare?: number | string }) {
        const queryString = '?group_id=' + params.group_id + '&is_prepare=' + params.is_prepare;
        return await this.get(this.endpoints.GET_TEMPLATE_MORE_NEW + queryString);
    }

    async checkAiDrawResult(group_id: string) {
        const queryString = '?group_id=' + group_id;
        return await this.get(this.endpoints.CHECK_AI_DRAW_RESULT + queryString);
    }

    async saveAiDrawAsset(aiProduceDrawId: string): Promise<ISaveImageResponse> {
        try {
            const response = await this.get<ISaveImageResponse>(this.endpoints.SAVE_AI_DRAW_ASSET, { ai_produce_draw_id: aiProduceDrawId });
            return response;
        } catch (error) {
            console.error('Failed to save AI draw asset:', error);
            return {
                code: 0,
                data: [{
                    ua_id: '',
                    picId: 0,
                    ai_produce_draw_id: '',
                    message: 'Failed to save AI draw asset',
                }],
                msg: 'Failed to save AI draw asset',
            };
        }
    }
}

/**
 * Page View module API class
 */
class PageViewModule extends BaseApi {
    private pvParamsMap: Record<string, string> = {
        modelId: 's4',
        sourceFrom: 'i4',
        ai_template_type: 's3',
        scene_id: 'i2',
        origin: 'ro',
    };

    async setPv(page_id: number, paras: { additional?: { [key: string | number]: string | number } } = { additional: {} }): Promise<void> {
        if (paras && paras.additional) {
            paras.additional['i4'] = 1;
            const urlParams = this.getUrlParams(window.location.href);
            for (const key in urlParams) {
                if (this.pvParamsMap[key]) {
                    paras.additional[this.pvParamsMap[key]] = urlParams[key];
                }
            }
        }

        await this.setPv_new(page_id, paras);
    }

    private getUrlParams(url: string) {
        if (!url) return {};
        const obj: Record<string, string> = {};
        const param = url.split('?')[1];
        if (!param) return {};
        const parr = param.split('&');
        for (const i of parr) {
            const arr = i.split('=');
            obj[arr[0]] = arr[1];
        }
        return obj;
    }

    private async setPv_new(page_id: number, paras: { additional?: { [key: string | number]: string | number } } = {}): Promise<void> {
        let dataStr = 'page_id=' + page_id;

        if (paras.additional) {
            for (let key in paras.additional) {
                dataStr += '&' + key + '=' + paras.additional[key];
            }
        }

        await this.setNewPv(dataStr);
    }

    private async setNewPv(dataStr = ''): Promise<void> {
        let paramsArr = dataStr.split('&'),
            newParams = [],
            deleteParams = ['pram', 'option', 'type'];

        paramsArr.map((v) => {
            let itemArr = v.split('='),
                newKey = this.newPvTransferTools(itemArr[0]);
            if (itemArr[1] && itemArr[1] != 'undefined' && itemArr[1] != 'NaN' && deleteParams.indexOf(newKey) < 0) {
                newParams.push(`${newKey}=${itemArr[1]}`);
            }
        });

        let userInfoCookie = this.getCookieTools('ui_818ps');
        userInfoCookie ? (userInfoCookie = encodeURIComponent(userInfoCookie)) : '';

        userInfoCookie && newParams.push(`ui=${userInfoCookie}`);
        newParams.push(`pt=web`);
        newParams.push(`u=${encodeURIComponent('' + location.href)}`);
        newParams.push(`v=${new Date().getTime()}`);

        const newParamsStr = newParams.join('&');
        try {
            await fetch('//p.818ps.com/p.gif?' + newParamsStr, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                mode: 'no-cors',
            });
        } catch (error) {
            console.error('setNewPv error', error);
        }
    }

    private newPvTransferTools(oldKey = '') {
        const keysArr = [
            { old: 'page_id', new: 'pid' },
            { old: 'referer', new: 'r' },
            { old: 'origin', new: 'ro' },
            { old: 'keyword', new: 'kw' },
            { old: 'abType', new: 'ab' },
            { old: 'templ_id', new: 'ti' },
            { old: 'user_templ_id', new: 'ut' },
            { old: 'load_time', new: 'i0' },
            { old: 'class_id', new: 'ki' },
            { old: 'route_id', new: 'ri' },
            { old: 'route', new: 'rb' },
            { old: 'after_route', new: 'ra' },
            { old: 'dom_load_time', new: 'dom_load_time' },
            { old: 'img_load_time', new: 'img_load_time' },
        ];

        for (const item of keysArr) {
            if (oldKey === item.old) {
                return item.new;
            }
        }
        return oldKey;
    }

    private getCookieTools(name: string) {
        const reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)');
        const arr = document.cookie.match(reg);
        return arr ? unescape(arr[2]) : null;
    }
}
/**
 * Main ChatApi class that manages all API modules
 */
export class ChatApi implements IAppChatApi {
    private chatModule: ChatModule;
    private templateModule: TemplateModule;
    private pageViewModule: PageViewModule;
    private proxy: any;

    constructor(
        apiBaseUrl: string = '//818ps.com',
        customEndpoints?: Partial<typeof DEFAULT_API_ENDPOINTS>,
        customOptions?: Partial<typeof defaultOptions>,
    ) {
        this.chatModule = new ChatModule(apiBaseUrl, customEndpoints, customOptions);
        this.templateModule = new TemplateModule(apiBaseUrl, customEndpoints, customOptions);
        this.pageViewModule = new PageViewModule(apiBaseUrl, customEndpoints, customOptions);
        this.proxy = new Proxy(this, {
            get: (target, prop: string) => {
              return window?.AppParams?.AppChatApi?.[prop] || target[prop];
            }
          });
        return this.proxy;
        
    }

    // Chat Module Methods
    async initChatSession(prompt: string, sourceFrom: number) {
        return this.chatModule.initChatSession(prompt, sourceFrom);
    }

    async getHistoryMessages(session_id: string) {
        return this.chatModule.getHistoryMessages(session_id);
    }

    async getSessionList(page_id: number, limit: number) {
        return this.chatModule.getSessionList(page_id, limit);
    }

    async getSuggestionsPrompt() {
        return this.chatModule.getSuggestionsPrompt();
    }

    async delSession(session_id: string) {
        return this.chatModule.delSession(session_id);
    }

    // Template Module Methods
    async drawCheck(group_id: string) {
        return this.templateModule.drawCheck(group_id);
    }

    async addUserAsset(group_id: string, asset_id: string) {
        return this.templateModule.addUserAsset(group_id, asset_id);
    }

    async saveImage(ai_produce_draw_id: string) {
        return this.templateModule.saveImage(ai_produce_draw_id);
    }

    async getOssUploadPreviewToken(filename: string, prefix: string) {
        return this.templateModule.getOssUploadPreviewToken(filename, prefix);
    }

    async getTemplInfo(params: { picId: string; upicId?: string; version_id?: string }) {
        return this.templateModule.getTemplInfo(params);
    }

    async getPresetDes() {
        return this.templateModule.getPresetDes();
    }

    async getGenerateNum() {
        return this.templateModule.getGenerateNum();
    }

    async saveOutline(params: {
        id: number;
        group_id: string;
        titile?: string[];
        sed_title?: string[];
        thd_title?: string[];
        main_text?: string[];
        time?: string;
    }) {
        return this.templateModule.saveOutline(params);
    }

    async saveUserTempl(params: { startTime: number; aiproduce_type: number }, template: any) {
        return this.templateModule.saveUserTempl(params, template);
    }

    async downloadTempl(params: {
        upicId: string;
        aiproduce_id?: number;
        dpi?: number;
        format?: string;
        force?: number;
        version_id?: string;
        source?: number;
        ticket?: string;
        rendstr?: string;
        ai_ppt_id?: string;
    }) {
        return this.templateModule.downloadTempl(params);
    }

    async downloadFlagCheck(param: {
        upicId: string;
        jobId: string;
        version_id?: number;
        aiproduce_id?: number;
        ai_ppt_id?: string;
    }) {
        return this.templateModule.downloadFlagCheck(param);
    }

    async getTemplateList(params: { id: number }) {
        return this.templateModule.getTemplateList(params);
    }

    async putPreviewPoster(data: FormData) {
        return this.templateModule.putPreviewPoster(data);
    }

    async getAiProduceInfo(params: { id: string }) {
        return this.templateModule.getAiProduceInfo(params);
    }

    async replaceTemplate(params: { id: string }) {
        return this.templateModule.replaceTemplate(params);
    }

    async getConcurrencyTaskInfo(params: {
        name: string;
        sceneCategorize?: string;
        festival_name?: string;
        ai_template_type: string;
        ai_judge?: string;
        group_id?: string;
        image_id?: string;
    }) {
        return this.templateModule.getConcurrencyTaskInfo(params);
    }

    async getTemplateMore(params: { group_id: string; ai_template_type: string }) {
        return this.templateModule.getTemplateMore(params);
    }

    async getUserGenerateRecord(params: { page: number; type?: string; limit?: number }) {
        return this.templateModule.getUserGenerateRecord(params);
    }

    async getPPTTemp(params: { page: number; pageSize: number }) {
        return this.templateModule.getPPTTemp(params);
    }

    async getPPTOutLine(id: string) {
        return this.templateModule.getPPTOutLine(id);
    }

    async savePicId(params: { ai_ppt_id: string; picId: string }) {
        return this.templateModule.savePicId(params);
    }

    async searchAiProduceInfo(params: { id: string }) {
        return this.templateModule.searchAiProduceInfo(params);
    }

    async startGenerateTaskNew(params: {
        name: string;
        group_id: string;
        ai_judge: string;
        ai_template_type: string;
    }) {
        return this.templateModule.startGenerateTaskNew(params);
    }

    async checkAiDrawResultV1(group_id: string) {
        return this.templateModule.checkAiDrawResultV1(group_id);
    }

    async getReletedTempNew(params: { group_id: string }) {
        return this.templateModule.getReletedTempNew(params);
    }

    async getGenerateTaskData(params: { group_id: string; index?: number }) {
        return this.templateModule.getGenerateTaskData(params);
    }

    async getAiContentListNew(params: { group_id: string, aid?: string }) {
        return this.templateModule.getAiContentListNew(params);
    }

    async getTemplateMoreNew(params: { group_id: string, is_prepare?: number | string }) {
        return this.templateModule.getTemplateMoreNew(params);
    }

    async checkAiDrawResult(group_id: string) {
        return this.templateModule.checkAiDrawResult(group_id);
    }

    async saveAiDrawAsset(aiProduceDrawId: string): Promise<ISaveImageResponse> {
        return this.templateModule.saveAiDrawAsset(aiProduceDrawId);
    }

    // Page View Module Methods
    async setPv(page_id: number, paras?: { additional?: { [key: string | number]: string | number } }): Promise<void> {
        return this.pageViewModule.setPv(page_id, paras);
    }
}

