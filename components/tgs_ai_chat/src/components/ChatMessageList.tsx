import React, { useEffect, useRef, useState, useCallback } from 'react';
import classNames from 'classnames';
import { IChatMessage, IChatSession } from '../types';
import { ChatMessage } from './ChatMessage';
import { WELCOME_MESSAGE } from '../constants';
import styles from '../styles/ChatMessageList.module.scss';
import { ChatApi } from '../api/chatApi';
import { useChatSettings } from '../context/ChatSettingsContext';
import { useResizeObserver } from '../hooks/useResizeObserver';
import { MessageStatus } from '../constants';

interface ChatMessageListProps {
  /**
   * Array of messages to display
   */
  messages: IChatMessage[];

  /**
   * Whether to show timestamps
   */
  showTimestamp?: boolean;

  /**
   * Whether to enable markdown rendering
   */
  enableMarkdown?: boolean;

  /**
   * Custom welcome message
   */
  welcomeMessage?: string;

  /**
   * Additional CSS class name
   */
  className?: string;

  /**
   * <PERSON>le regenerate message
   */
  handleRegenerateMessage?: (message_id: string) => void;

  /**
   * Callback when scroll control functions are ready
   */
  onScrollControlReady?: (controls: {
    scrollToBottom: (smooth?: boolean) => void;
    maybeScrollToBottom: (smooth?: boolean) => void;
  }) => void;
    /**
     * Pop message
     */
    popMessage?: {
        error: (message: string) => void;
        info: (message: string) => void;
        success: (message: string) => void;
        warning: (message: string) => void;
    };

    /**
     * Add pic
     */
    addPic: (asset: any) => void;

    /**
     * Upload limit popup
     */
    uploadLimitPopup?: () => void;
    /**
     * Recharge modal popup
     */
    rechargeModalPopup?: (origin: string, callback: (isRecharge: boolean) => void) => void;
}

/**
 * Chat message list component
 */
export const ChatMessageList: React.FC<ChatMessageListProps> = ({
                                                                  messages,
                                                                  showTimestamp = true,
                                                                  enableMarkdown = true,
                                                                  welcomeMessage = WELCOME_MESSAGE,
                                                                  className,
                                                                  handleRegenerateMessage,
                                                                  onScrollControlReady,
                                                                  popMessage,
                                                                  addPic,
                                                                  uploadLimitPopup,
                                                                  rechargeModalPopup,
                                                                }) => {
  const { size, sourceFrom } = useChatSettings();
  const messageListInnerRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const topAnchorRef = useRef<HTMLDivElement>(null);
  const bottomAnchorRef = useRef<HTMLDivElement>(null);
  const [streamingMessage, setStreamingMessage] = React.useState<IChatMessage | null>(null);
  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true);
  const isAutoScrollingRef = useRef(false);
  const [showScrollToBottomBtn, setShowScrollToBottomBtn] = useState(false);
  const chatApi = new ChatApi();

  const autoScrollRef = useRef(autoScrollEnabled);
  useEffect(() => { autoScrollRef.current = autoScrollEnabled;}, [autoScrollEnabled]);

  // 抽取滚动到底部逻辑
  const scrollToBottom = (enableScroll = false) => {
    isAutoScrollingRef.current = true;
    bottomAnchorRef.current?.scrollIntoView({ behavior: 'auto' });
    setShowScrollToBottomBtn(false)
    if (!enableScroll) {
      setTimeout(() => {
        isAutoScrollingRef.current = false;
      }, 100);
    }else {
      setAutoScrollEnabled(true)
    }
  };
    
  // const scrollToTop = (smooth = true) => {
  //   isAutoScrollingRef.current = true;
  //   topAnchorRef.current?.scrollIntoView({ behavior: smooth ? 'smooth' : 'auto' });
  //   setTimeout(() => {
  //     isAutoScrollingRef.current = false;
  //   }, 100);
  // };
  const maybeScrollToBottom = (smooth = false) => {
    if (autoScrollRef.current) {
      // 检查是否真的需要滚动
      const container = containerRef.current;
      if (container) {
        const threshold = 50;
        const isAtBottom = container.scrollHeight - container.scrollTop - container.clientHeight < threshold;
        
        // 只有在不在底部时才滚动
        if (!isAtBottom) {
          scrollToBottom(smooth);
        }
      } else {
        scrollToBottom(smooth);
      }
    }
  };

  // 暴露滚动控制函数给父组件
  useEffect(() => {
    if (onScrollControlReady) {
      onScrollControlReady({
        scrollToBottom,
        maybeScrollToBottom,
      });
    }
  }, [onScrollControlReady]);

  // 监听消息变化，处理流式传输状态
  useEffect(() => {
    const lastMessage = messages[messages.length - 1];
    if (lastMessage && lastMessage.status === 'sending') {
      setStreamingMessage(lastMessage);
    } else {
      setStreamingMessage(null);
    }
  }, [messages]);

  // 初始加载时滚动到底部，延迟触发以确保 DOM 渲染完成
  useEffect(() => {
    // 结合 ResizeObserver 和消息变化来智能滚动
    const resizeEl = messageListInnerRef.current;
    if (!resizeEl) return;

    // 初始滚动
    const timeout = setTimeout(() => {
      maybeScrollToBottom();
    }, 100);

    return () => {
      clearTimeout(timeout);
    };
  }, [messages]);

  // Use the improved useResizeObserver hook with useCallback
  const handleResize = useCallback(() => {
    // 当内容大小变化时，检查是否需要滚动到底部
    if (autoScrollRef.current) {
      // 延迟滚动以确保所有内容都已渲染完成
      setTimeout(() => {
        maybeScrollToBottom();
      }, 50);
    }
  }, [maybeScrollToBottom]);

  useResizeObserver(messageListInnerRef, handleResize);

  // 监听流式消息内容变化
  useEffect(() => {
    if (streamingMessage?.content) {
      maybeScrollToBottom();
    }
  }, [streamingMessage?.content, maybeScrollToBottom]);

  // 监听消息状态变化，特别是当模板渲染完成时
  useEffect(() => {
    const lastMessage = messages[messages.length - 1];
    if (lastMessage && lastMessage.status === MessageStatus.SENT) {
      // 当消息状态变为已发送时，延迟滚动到底部以确保模板渲染完成
      const timeout = setTimeout(() => {
        maybeScrollToBottom();
      }, 200);
      return () => clearTimeout(timeout);
    }
  }, [messages, maybeScrollToBottom]);

  // 监听消息内容变化，特别是当模板或图片加载完成时
  useEffect(() => {
    const lastMessage = messages[messages.length - 1];
    if (lastMessage && lastMessage.status === MessageStatus.SENT) {
      // 检查消息是否包含模板或图片内容
      const hasTemplate = lastMessage.templateInfo || (lastMessage.draw && lastMessage.draw.length > 0);
      if (hasTemplate) {
        // 对于包含模板的消息，使用更长的延迟确保渲染完成
        const timeout = setTimeout(() => {
          maybeScrollToBottom();
        }, 500);
        return () => clearTimeout(timeout);
      }
    }
  }, [messages, maybeScrollToBottom]);

  // 监听模板渲染完成事件
  useEffect(() => {
    const handleTemplateRenderComplete = () => {
      // 当模板渲染完成时，滚动到底部
      setTimeout(() => {
        maybeScrollToBottom();
      }, 100);
    };

    window.addEventListener('template-render-complete', handleTemplateRenderComplete);
    
    return () => {
      window.removeEventListener('template-render-complete', handleTemplateRenderComplete);
    };
  }, [maybeScrollToBottom]);

  // 监听容器滚动位置变化，重新计算是否在底部
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleScroll = () => {
      if (isAutoScrollingRef.current) return;
      
      const threshold = 30;
      const isAtBottom = container.scrollHeight - container.scrollTop - container.clientHeight < threshold;
      
      // Show scroll button when scrolled up more than 200px from bottom
      const scrollUpDistance = container.scrollHeight - container.scrollTop - container.clientHeight;
      setShowScrollToBottomBtn(scrollUpDistance > 200);

      if (streamingMessage && !isAtBottom) {
        setAutoScrollEnabled(false);
        autoScrollRef.current = false;
      } else {
        if (autoScrollRef.current !== isAtBottom) {
          setAutoScrollEnabled(isAtBottom);
          autoScrollRef.current = isAtBottom;
        }
      }
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [streamingMessage]);

  // 过滤掉正在流式传输的消息
  const displayMessages = messages.filter(msg => msg.status !== 'sending');
  return (
      <div
          className={classNames(styles.messageList, className)}
          ref={containerRef}
      >
        {showScrollToBottomBtn && (
          <div 
            className={styles.scrollToBottomBtn} 
            onClick={() => {
              chatApi.setPv(9063, { additional: { i1: sourceFrom } });
              scrollToBottom();
            }}
            role="button"
            aria-label="滚动到底部"
          >
            <i className="iconfont-ai-chat ai-chat-xiangxia2"></i>
          </div>
        )}
          <div ref={topAnchorRef} style={{ scrollMarginTop: '20px', minHeight: '1px' }} />
          <div
            className={classNames(styles.messageListInner, size === 'large' ? styles.large : styles[size])}
            ref={messageListInnerRef}
        >
          {displayMessages.length === 0 && !streamingMessage ? (
              <div className={styles.empty}>
                <div className={styles.emptyIcon}>
                  <i className="iconfont-ai-chat ai-chat-AIshengcheng"></i>
                </div>
                <div className={styles.emptyText}>
                  {welcomeMessage}
                </div>
              </div>
          ) : (
              <>
                {displayMessages.map(message => (
                  <ChatMessage
                    key={message.message_id}
                    message={message}
                    showTimestamp={showTimestamp}
                    enableMarkdown={enableMarkdown}
                    isStreaming={message.status === 'sending'}
                    onRegenerate={handleRegenerateMessage}
                    popMessage={popMessage}
                    addPic={addPic}
                    uploadLimitPopup={uploadLimitPopup}
                    rechargeModalPopup={rechargeModalPopup}
                  />
                ))}
                {streamingMessage && (
                  <ChatMessage
                    key={streamingMessage.message_id}
                    message={streamingMessage}
                    showTimestamp={showTimestamp}
                    enableMarkdown={enableMarkdown}
                    isStreaming={true}
                    onRegenerate={handleRegenerateMessage}
                    popMessage={popMessage}
                    addPic={addPic}
                    uploadLimitPopup={uploadLimitPopup}
                  />
                )}
              </>
          )}
        </div>
        <div ref={bottomAnchorRef} style={{ scrollMarginBottom: '20px', minHeight: '1px' }} />
      </div>
  );
};
