.text-gen-skeleton {
    .title {
        font-size: 14px;
        font-weight: 400;
        line-height: 21px;
        height: 21px;
        color: var(--main-text1);
        margin-bottom: 9px;
    }

    :global {
        .ant-skeleton.ant-skeleton-active .ant-skeleton-paragraph > li {
            height: 10px;
            background-image: linear-gradient(
                90deg,
                rgba(117, 98, 217, 0.06) 25%,
                rgba(117, 98, 217, 0.15) 37%,
                rgba(117, 98, 217, 0.06) 63%
            );

            & + li {
                margin-top: 10px;
            }
        }
    }
}

.ct-popover {
    color: var(--main-text2);
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    cursor: pointer;
}

.ct-number {
    display: inline-block;
    margin: 0 4px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--main-light1);
    color: var(--main-color1);
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    text-align: center;
    cursor: pointer;

    &:hover {
        background: var(--main-color1);
        color: var(--main-color2);
    }
}

.content-header {
    color: var(--main-text1);
    line-height: 1;
}

.title {
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
    margin-right: 5px;
}

.count {
    color: var(--main-text2);
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
}

.pointer {
    cursor: pointer;
}

.table-container {
    overflow-x: auto;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-top: 16px;
    margin-bottom: 15px;
    overflow-y: hidden;
}
.tableActionBottom {
    margin-bottom: 30px;
    .table-footer {
        padding: 10px;
        display: flex;
        align-items: center;
        gap: 16px;
        background: linear-gradient(90deg, #FFFFFF 0%, #EFF2FD 50%, #FFFFFF 100%);
        background-size: 200% 100%;
        background-position: 100% 0;
        border-top: 1px solid #e0e0e0;
        border-radius: 8px;
        border: 1px solid #E9E8E8;
        cursor: pointer;
        transition: background-position 0.4s ease-in-out;

        &:hover {
            background-position: 0% 0;
        }

        .tableActionBottomLeft {
            display: flex;
            align-items: center;
            vertical-align: baseline;
            gap: 5px;
        }
        .tableActionBottomRight {
            i {
                font-size: 10px;
                color: #666;
            }
        }
    }
    
}


.inline-code {
    display: inline-block;
    width: 100%;
    padding: 2px 6px;
    background-color: #f8f8fa;
    border-radius: 4px;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
    font-size: 0.9em;
    color: var(--main-text1);
}

.code-block-container {
    margin: 16px 0;
    border-radius: 8px;
    background: #f8f8fa;
    border: 1px solid var(--main-border, #e5e7eb);
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.code-block-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--main-light1, #f4f5f7);
    border-bottom: 1px solid var(--main-border, #e5e7eb);
    padding: 10px 14px;
    min-height: 32px;
}

.code-language {
    font-size: 13px;
    color: #222;
    font-family: 'Fira Mono', 'Menlo', 'Consolas', monospace;
}

.code-copy-btn {
    font-size: 13px;
    color: #222;
    background: none;
    border: none;
    border-radius: 6px;
    padding: 2px 10px;
    cursor: pointer;
    transition: background 0.2s;
    &:hover, &:focus {
        background: #e5e7eb;
        color: #222;
    }
}

.code-block {
    margin: 0;
    padding: 16px;
    overflow-x: auto;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
    font-size: 14px;
    line-height: 1.5;
    color: var(--main-text1, #222);
    background: transparent;
    min-width: 0;
    width: 100%;
    box-sizing: border-box;
}

.inline-code {
    display: inline;
    background-color: #f5f5f5;
    font-family: Menlo, Monaco, Consolas, 'Courier New', monospace;
    font-size: 13px;
    padding: 2px 6px;
    border-radius: 4px;
    white-space: pre-wrap;
    word-break: break-word;
}

.block-code {
    display: inline-block;
    padding: 2px 6px;
    background-color: #f8f8fa;
    border-radius: 4px;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
    font-size: 0.95em;
    color: var(--main-text1, #222);
}
