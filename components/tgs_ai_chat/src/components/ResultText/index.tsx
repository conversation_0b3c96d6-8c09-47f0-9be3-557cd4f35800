import React, { useEffect } from 'react';
import styles from './index.module.scss';
import MarkdownIt from 'markdown-it';
import CodeBlock from './CodeBlock';
import { CustomTable } from './Table';
import { parseTableFromElement } from '../../utils/tableUtils';

const mdi = new MarkdownIt({
    html: true,
    breaks: false,
    linkify: true,
});

/**
 * 联网搜索组件-打开链接
 * @param url 链接
 */
function openLink(url: string) {
    if (url) {
        window.open(url, '_blank', 'noopener noreferrer');
    }
}

/**
 * 联网搜索组件-引用外部链接
 * @param props 
 */
function CitationNumber(props: { index: number; url: string; title: string }) {
    const { index, url } = props;

    return (
        <span
            className={styles['ct-number']}
            onClick={() => openLink(url)}
            title={props.title}
        >
            {index}
        </span>
    );
}

interface ITextResultProps {
    item: {
        id: string;
        content: string;
        code?: number;
        search_results?: Array<{
            url: string;
            title: string;
        }>;
    };
    showCitation?: () => void;
    sourceFrom?: number;
}

// Map of HTML tag names to their React component equivalents
const tagMap: Record<string, keyof JSX.IntrinsicElements | React.ComponentType<any>> = {
    'p': 'p',
    'div': 'div',
    'span': 'span',
    'strong': 'strong',
    'em': 'em',
    'blockquote': 'blockquote',
    'ul': 'ul',
    'ol': 'ol',
    'li': 'li',
    'h1': 'h1',
    'h2': 'h2',
    'h3': 'h3',
    'h4': 'h4',
    'h5': 'h5',
    'h6': 'h6',
    'a': 'a',
    'br': 'br',
    'hr': 'hr',
    'img': 'img',
    'table': 'table',
    'thead': 'thead',
    'tbody': 'tbody',
    'tr': 'tr',
    'th': 'th',
    'td': 'td',
};

export function TextResult({ item, showCitation, sourceFrom }: ITextResultProps) {
    const [result, setResult] = React.useState<React.ReactNode[]>([]);

    useEffect(() => {
        if (item.code) {
            const dom = document.getElementById(item.id + '-chat');
            if (dom) {
                dom.scrollIntoView({ behavior: 'smooth' });
            }
        }
    }, [item.code, item.id]);

    useEffect(() => {
        const htmlText = mdi.render(item.content);
        const parser = new DOMParser();
        const doms = parser.parseFromString(htmlText, 'text/html');
        const results: React.ReactNode[] = [];

        if (doms.body.childNodes.length > 0) {
            const checkDom = (dom: ChildNode): React.ReactNode => {
                if (dom.nodeName === '#text' && dom.textContent && dom.textContent !== '\n') {
                    const text = dom.textContent || '';
                    const r: React.ReactNode[] = [];
                    const textArray = text.split(/\[ct:\d+\]/);
                    let ctArray = text.match(/\[ct:\d+\]/g) as string[];
                    if (textArray?.length && ctArray?.length) {
                        ctArray = ctArray.map((ct: string) => {
                            return ct.replace('[ct:', '').replace(']', '');
                        });
                        for (let i = 0; i < textArray.length; i++) {
                            r.push(
                                <span key={i} style={{ userSelect: 'text' }}>
                                    {textArray[i]}
                                    {ctArray[i] &&
                                        item.search_results?.[Number(ctArray[i]) - 1] &&
                                        <CitationNumber
                                            key={`citation-${i}-${ctArray[i]}`}
                                            index={Number(ctArray[i])}
                                            url={item.search_results?.[Number(ctArray[i]) - 1]?.url || ''}
                                            title={item.search_results?.[Number(ctArray[i]) - 1]?.title || ''}
                                        />}
                                </span>,
                            );
                        }
                        return r;
                    }
                    return dom.textContent;
                }

                if (dom.nodeName === 'TD') {
                    return <td style={{ userSelect: 'text',maxWidth: sourceFrom === 1 ? '300px' : '150px' }}>{dom.textContent}</td>;
                }
                
                if (dom.nodeName === 'TABLE') {
                    // 解析表格数据并返回 CustomTable 组件
                    const tableData = parseTableFromElement(dom as Element);
                    return <CustomTable key={Math.random()} tableData={tableData} />;
                }
                
                if (dom?.childNodes.length > 0) {
                    const arr: React.ReactNode[] = [];
                    dom.childNodes.forEach((child, index) => {
                        arr.push(<React.Fragment key={index}>{checkDom(child)}</React.Fragment>);
                    });
                    const tagName = dom.nodeName.toLowerCase();
                    
                    if (tagName === 'code') {
                        const parentName = dom.parentElement?.nodeName.toLowerCase();
                        const isBlock = parentName === 'pre';

                        return (
                            <CodeBlock
                                inline={!isBlock}
                                className={(dom as Element).className}
                            >
                                {(dom.textContent || '').trim()}
                            </CodeBlock>
                        );
                    }
                    if (tagName === 'a') {
                        const href = (dom as Element).getAttribute('href') || '#';
                        if(href.includes('818ps.com')) {
                            return (
                                <a
                                    href={href}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    style={{ userSelect: 'text' }}
                                >
                                    {arr}
                                </a>
                            );
                        }else {
                            return (
                                <span>
                                    {arr}
                                </span>
                            );
                        }
                    }

                    const TagComponent = tagMap[tagName] || 'div';
                    if (dom.nodeType === 1 && (dom as Element).className) {
                        return React.createElement(TagComponent, {
                            className: (dom as Element).className,
                            style: { userSelect: 'text' }
                        }, arr);
                    }
                    return React.createElement(TagComponent, {
                        style: { userSelect: 'text' }
                    }, arr);
                }
                return null;
            };

            doms.body.childNodes.forEach((dom, index) => {
                const result = checkDom(dom);
                if (result) {
                    results.push(<React.Fragment key={index}>{result}</React.Fragment>);
                }
            });
            setResult(results);
        }
    }, [item.content, item.search_results]);

    if (item.code && item.code !== 1 && item.code !== 105) {
        return (
            <div className={styles['text-gen-skeleton']}>
                <div className={styles.title}>分析中...</div>
            </div>
        );
    }

    return (
        <>
            {item.content && (
                <div className={`${styles['content-header']}`}>
                    {item.search_results && item.search_results.length > 0 && (
                        <div
                            className={`${showCitation ? styles.pointer : ''}`}
                            onClick={() => {
                                if (showCitation) {
                                    showCitation?.();
                                    setTimeout(() => {
                                        const dom = document.getElementById(item.id + '-citation');
                                        if (dom) {
                                            dom.scrollIntoView({ behavior: 'smooth' });
                                        }
                                    }, 200);
                                }
                            }}
                        >
                            <span className={styles.title}>Sources</span>
                            <span className={styles.count}>{item.search_results.length}</span>
                        </div>
                    )}
                </div>
            )}

            <div className='markdown-content'>{result}</div>
        </>
    );
}
