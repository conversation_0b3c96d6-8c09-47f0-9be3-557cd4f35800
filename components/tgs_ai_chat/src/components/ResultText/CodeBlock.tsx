import React, { useState } from 'react';
import styles from './index.module.scss';

interface CodeBlockProps {
  children: React.ReactNode;
  inline?: boolean;
  className?: string;
}

const CodeBlock: React.FC<CodeBlockProps> = ({ children, inline, className }) => {
  const [copied, setCopied] = useState(false);
  const language = className?.replace('language-', '') || '';
  // 获取 code 字符串内容
  let codeString = '';
  if (typeof children === 'string') {
    codeString = children;
  } else if (Array.isArray(children)) {
    codeString = children.join('');
  } else if (children && typeof (children as any).toString === 'function') {
    codeString = (children as any).toString();
  }

  const handleCopy = () => {
    navigator.clipboard.writeText(codeString);
    setCopied(true);
    setTimeout(() => setCopied(false), 3000);
  };

  if (!inline) {
    return <div className={styles['code-block-container']}>
      <div className={styles['code-block-header']}>
      <span className={styles['code-language']}>{language}</span>
      <button
        className={styles['code-copy-btn']}
        onClick={handleCopy}
        aria-label="复制代码"
        tabIndex={0}
      >
        <i className='iconfont-ai-chat ai-chat-fuzhi1'></i> {copied ? '已复制' : '复制'}
      </button>
    </div>
    <pre className={styles['code-block']}>
      <code className={styles['block-code']}>{children}</code>
    </pre>
  </div>;
  }
  return <code className={styles['inline-code']}>{children}</code>;
};

export default CodeBlock; 