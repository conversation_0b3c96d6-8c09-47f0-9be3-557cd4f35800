// Custom table components
import React from 'react';
import styles from './index.module.scss';
import { useChatSettings } from '../../context/ChatSettingsContext';
import { 
  TableData, 
  copyTableToClipboard 
} from '../../utils/tableUtils';

interface CustomTableProps {
  tableData: TableData;
}

export const CustomTable: React.FC<CustomTableProps> = ({ tableData }) => {
  const { editorController, popMessage } = useChatSettings();

  const handleAddTable = async () => {
    if (editorController) {
      editorController.addTable?.(tableData);
    } else {
      await handleCopyHtml();
    }
  };

  const handleCopyHtml = async () => {
    await copyTableToClipboard(tableData);
    popMessage.success('复制成功,请在画布中粘贴');
  };

  const renderTable = () => {
    const { headers, rows } = tableData;
    
    return (
      <table>
        {headers.length > 0 && (
          <thead>
            <tr>
              {headers.map((header, index) => (
                <th key={index} style={{ userSelect: 'text' }}>
                  {header}
                </th>
              ))}
            </tr>
          </thead>
        )}
        {rows.length > 0 && (
          <tbody>
            {rows.map((row, rowIndex) => (
              <tr key={rowIndex}>
                {row.map((cell, cellIndex) => (
                  <td key={cellIndex} style={{ userSelect: 'text', maxWidth: '300px' }}>
                    {cell}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        )}
      </table>
    );
  };

  return (
    <>
      <div className={styles['table-container']}>
        {renderTable()}
      </div>
      <div className={styles.tableActionBottom}>
        <button className={styles['table-footer']} onClick={handleAddTable}>
          <div className={styles.tableActionBottomLeft}>
            <i className="iconfont-ai-chat ai-chat-shouyexinjian"></i>
            添加表格到画布
          </div>
          <div className={styles.tableActionBottomRight}>
            <i className="iconfont-ai-chat ai-chat-you1"></i>
          </div>
        </button>
      </div>
    </>
  );
};