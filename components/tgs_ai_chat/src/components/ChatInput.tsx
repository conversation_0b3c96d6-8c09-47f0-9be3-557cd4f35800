import React, { useRef, useEffect } from 'react';
import classNames from 'classnames';
import { isMobileDevice } from '../utils/chatUtils';
import styles from '../styles/ChatInput.module.scss';
import { ChatApi } from '../api/chatApi';
import { GenerationStatus, useGeneration } from '../context/GenerationContext';
import { useChatSettings } from '../context/ChatSettingsContext';

interface ChatInputProps {
    /**
     * Callback function when a message is sent
     */
    onSendMessage: (message: string) => void;

    /**
     * Placeholder text
     */
    placeholder?: string;

    /**
     * Input value
     */
    value: string;

    /**
     * Input value change handler
     */
    onChange: (value: string) => void;

    /**
     * Additional CSS class name
     */
    className?: string;

    /**
     * Maximum length of the message
     */
    maxLength?: number;



    /**
     * Whether the input is visible
     */
    isVisible?: boolean;
    /**
     * Whether to clear the input after sending
     */
    clearAfterSubmit?: boolean;

    /**
     * Abort current stream
     */
    abortCurrentStream?: () => void;
}

/**
 * Chat input component
 */
export const ChatInput: React.FC<ChatInputProps> = ({
    onSendMessage,
    placeholder = '可以问我任何问题，Shift + Enter 换行',
    value = '',
    onChange,
    className,
    maxLength = 500,
    isVisible = true,
    clearAfterSubmit = true,
    abortCurrentStream,
}) => {
    const { size, sourceFrom } = useChatSettings();
    const { generationStatus, deepThink, setDeepThink } = useGeneration();
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const isMobile = isMobileDevice();
    const chatApi = new ChatApi();

    // Focus the textarea when the component mounts or becomes visible
    useEffect(() => {
        if (textareaRef.current && isVisible && !window?.AppParams) {
            textareaRef.current.focus();
        }
    }, [isVisible]);

    // Handle input change
    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        const newValue = e.target.value;
        if (maxLength && newValue.length > maxLength) {
            return;
        }
        onChange(newValue);
    };

    // Handle key press (Enter to send, Shift+Enter for new line)
    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        // On mobile, don't use Enter to send
        if (isMobile) return;

        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSend();
        }
        e.stopPropagation();
    };

    // Handle send button click
    const handleSend = () => {
        if (value.trim()) {
            onSendMessage(value.trim());
            if( clearAfterSubmit ) {
                onChange('');
            }
            setTimeout(() => {
                textareaRef.current?.focus();
            }, 0);
        }
    };

    // Auto-resize textarea based on content
    useEffect(() => {
        if (textareaRef.current) {
            textareaRef.current.style.height = 'auto';
            textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
        }
    }, [value]);

    const handleCopy = (e: React.ClipboardEvent<HTMLTextAreaElement>) => {
        e.stopPropagation();
    }

    const handlePaste = (e: React.ClipboardEvent<HTMLTextAreaElement>) => {
        e.stopPropagation();
    }

    return (
        <div className={classNames(styles.input, className, size === 'large' ? styles.large : styles[size])}>
            <div className={styles.wrapper}>
                <textarea
                    ref={textareaRef}
                    className={styles.textarea}
                    value={value}
                    onChange={handleChange}
                    onKeyDown={handleKeyDown}
                    placeholder={window?.AppParams?.placeholder || placeholder}
                    onCopy={handleCopy}
                    onPaste={handlePaste}
                    rows={1}
                    autoFocus={window?.AppParams ? false: true}
                    maxLength={maxLength}
                    aria-label="聊天输入框"
                    onFocus={() => {
                        chatApi.setPv(9046, { additional: { i1: sourceFrom } });
                    }}
                />
                <div className={styles.changeModule}>
                    <button
                        type="button"
                        className={deepThink
                            ? styles.deepThinkBtn + ' ' + styles.deepThinkBtnActive
                            : styles.deepThinkBtn}
                        aria-label="深度思考 (R1)"
                        tabIndex={0}
                        onClick={() => setDeepThink(!deepThink)}
                    >
                        <span className={styles.deepThinkIcon}>
                            <i className="iconfont-ai-chat ai-chat-shendusikao"></i>
                        </span> 深度思考 (R1)
                    </button>
                    {generationStatus === GenerationStatus.PROCESSING ? <button
                        className={styles.sending}
                        onClick={abortCurrentStream}
                        title="终止生成"
                        aria-label="终止生成"
                    >
                        <i className="iconfont-ai-chat ai-chat-tingzhi"></i>
                    </button> : <button
                        className={styles.send}
                        onClick={handleSend}
                        disabled={!value.trim()}
                        title="发送消息"
                        aria-label="发送消息"
                    >
                        <i className="iconfont-ai-chat ai-chat-shang1"></i>
                    </button>}
                </div>
            </div>
        </div>
    );
};
