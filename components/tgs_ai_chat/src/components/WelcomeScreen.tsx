import React, { useEffect, useRef, useState, useCallback } from 'react';
import classNames from 'classnames';
import styles from '../styles/WelcomeScreen.module.scss';
import { ChatInput } from './ChatInput';
import { IPromptSuggestion } from '../types';
import { ChatApi } from '../api/chatApi';
import { useChatSettings } from '../context/ChatSettingsContext';
import { useGeneration } from '../context/GenerationContext';

interface WelcomeScreenProps {
  /**
   * Callback when user submits a prompt
   */
  onSubmit: (prompt: string, deepThink?: boolean) => Promise<void>;

  /**
   * Predefined prompt suggestions
   */
  promptSuggestions: IPromptSuggestion[];

  /**
   * Additional CSS class name
   */
  className?: string;
}

/**
 * Welcome screen component with only a centered input box and prompt suggestions below
 */
export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  onSubmit,
  promptSuggestions,
  className
}) => {
  const { size, sourceFrom } = useChatSettings();
  const [inputValue, setInputValue] = useState('');
  const welcomeScreenRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const chatApi = new ChatApi();
  
  const [paddingTop, setPaddingTop] = useState(0);

  // Calculate centering padding
  const calculateCentering = useCallback(() => {
    if (welcomeScreenRef.current && contentRef.current) {
      const containerHeight = welcomeScreenRef.current.clientHeight;
      const contentHeight = contentRef.current.scrollHeight;
      const padding = Math.max(0, (containerHeight - contentHeight) / 2);
      setPaddingTop(padding);
    }
  }, []);

  // Handle resize and initial calculation
  useEffect(() => {
    calculateCentering();
    
    const handleResize = () => {
      calculateCentering();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [calculateCentering, promptSuggestions]);

  // Move useResizeObserver to top level with useCallback
  const handleSubmit = (value: string) => {
    if (value.trim()) {
      onSubmit(value);
    }
  };

  const handleSuggestionClick = (suggestion: IPromptSuggestion) => {
    chatApi.setPv(9047, { additional: { i1: sourceFrom } });
    setInputValue(suggestion.prompt);
  };

  return (
    <div className={classNames(styles.welcomeScreen, className, styles[size])} ref={welcomeScreenRef}>
      <div 
        className={styles.content} 
        ref={contentRef}
        style={{ paddingTop: `${paddingTop}px` }}
      >
        {/* Logo 占位符 */}
        <div className={styles.logoPlaceholder}>
          <span className={styles.logoIcon}>
          </span>
        </div>
        {/* 欢迎语 */}
        <h1 className={styles.title}>Hi，很高兴见到你！</h1>
        {/* 支持信息 */}
        <p className={styles.subtitle}>
          图怪兽 AI 已支持 <img src="https://js.tuguaishou.com/ai/aiChat/assets/deepseekAI.png" alt="deepseek" className={styles.deepseekIcon} /> {size === 'large' ? '把你的任务交给我吧' : ''}
        </p>
        {/* 输入框 */}
        <div className={styles.form}>
          <div className={styles.inputWrapper}>
            <ChatInput
              onSendMessage={handleSubmit}
              placeholder="可以问我任何问题，Shift + Enter 换行"
              value={inputValue}
              onChange={setInputValue}
              clearAfterSubmit={false}
            />
          </div>
        </div>

        {/* prompt suggestions */}
        {promptSuggestions.length > 0 ? (
          <div className={styles.suggestions}>
            {promptSuggestions.map((suggestion) => (
              <button
                key={suggestion.key}
                className={styles.suggestionButton}
                onClick={() => handleSuggestionClick(suggestion)}
              >
                {suggestion.label}
              </button>
            ))}
          </div>
        ) : (
          <div className={styles.suggestions}>
            {[1, 2, 3, 4, 5].map((index) => (
              <div
                key={index}
                className={classNames(styles.suggestionButton, styles.suggestionSkeleton)}
                style={{
                  animationDelay: `${index * 0.1}s`,
                  width: `${120 + Math.random() * 160}px`,
                  height: '38px'
                }}
              >
                <div className={styles.suggestionSkeletonShimmer} />
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
