import React, { useState } from 'react';
import classNames from 'classnames';
import { IChatMessage } from '../types';
import { MessageRole, MessageStatus } from '../constants';
import { TextResult } from './ResultText';
import styles from '../styles/ChatMessage.module.scss';
import { ChatApi } from '../api/chatApi';
import { ChatRenderImage } from './ChatRenderItem/ChatRenderImage';
import { useChatSettings } from '../context/ChatSettingsContext';
import { useGeneration } from '../context/GenerationContext';
import { copyTextToClipboard } from '../utils/chatUtils';
interface ChatMessageProps {
  /**
   * Message object
   */
  message: IChatMessage;

  /**
   * Whether to show the timestamp
   */
  showTimestamp?: boolean;

  /**
   * Whether to enable markdown rendering
   */
  enableMarkdown?: boolean;

  /**
   * Whether the message is currently streaming
   */
  isStreaming?: boolean;

  /**
   * Additional CSS class name
   */
  className?: string;

  /**
   * 重新生成本条消息回调
   */
  onRegenerate?: (messageId: string) => void;

  /**
   * Whether to show border
   */
  showBorder?: boolean;

  /**
   * Whether to show action buttons
   */
  showActions?: boolean;

  /**
   * Whether to show thinking
   */
  showThinking?: boolean;

  /**
   * Pop message
   */
  popMessage?: {
    error: (message: string) => void;
    info: (message: string) => void;
    success: (message: string) => void;
    warning: (message: string) => void;
  };

  /**
   * Add pic
   */
  addPic: (asset: any) => void;

  /**
   * Upload limit popup
   */
  uploadLimitPopup?: () => void;

  /**
   * Open purchase popup
   */
  rechargeModalPopup?: (origin: string, callback: (isRecharge: boolean) => void) => void;
}


/**
 * Chat message component
 */
export const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  enableMarkdown = true,
  isStreaming = false,
  onRegenerate,
  showBorder = true,
  showActions = true,
  popMessage,
  addPic,
  uploadLimitPopup,
  className,
  rechargeModalPopup
}) => {
  const { size, sourceFrom } = useChatSettings();
  const { updateGenerationCount } = useGeneration();
  const { role, content, status } = message;
  const chatApi = new ChatApi();

  const handleCopy = async () => {
    if (content) {
        try {
          // 尝试现代API，失败后自动回退到传统方法
          try {
            await (navigator.clipboard?.writeText(content) || copyTextToClipboard(content));
          } catch {
            await copyTextToClipboard(content);
          }
          chatApi.setPv(9050, { additional: { i1: sourceFrom } });
          popMessage?.success('复制成功');
        } catch {
          popMessage?.error('复制失败');
        }
    }
  };

  const handleRegenerate = () => {
    onRegenerate?.(message.message_id);
    chatApi.setPv(9051, { additional: { i1: sourceFrom } });
  };

  // Function to render message content
  const renderContent = (content: string) => {
    if (status === MessageStatus.SENDING && !isStreaming) {
      return (
        <div className={styles.loading}>
          正在思考
          <div className={styles.loadingDots}>
            <div className={styles.loadingDotsDot}></div>
            <div className={styles.loadingDotsDot}></div>
            <div className={styles.loadingDotsDot}></div>
          </div>
        </div>
      );
    }

    if (status === MessageStatus.ERROR) {
      return (
        <div className={styles.error}>
          <i className="iconfont-ai-chat ai-chat-jinggao"></i>
          {content || '消息发送失败，请重试'}
        </div>
      );
    }

    if (role === MessageRole.ASSISTANT) {
      return (
        <div className={styles.aiPlain}>
          {enableMarkdown ? (
            <TextResult 
              item={{ 
                id: message.message_id,
                content,
              }}
              sourceFrom={sourceFrom}
            />
          ) : (
            content
          )}
        </div>
      );
    }

    // 用户消息
    if (enableMarkdown) {
      try {
        return (
          <TextResult 
            item={{ 
              id: message.message_id,
              content, 
            }}
            sourceFrom={sourceFrom}
          />
        );
      } catch (error) {
        return <div>{content}</div>;
      }
    }

    return <div>{content}</div>;
  };

  const renderMessageContent = (content: string, isThinking = false, role: string) => (
    <div className={classNames(
      styles.message,
      styles[size],
      styles[role.toLowerCase()],
      {
        [styles.streaming]: isStreaming,
        [styles.noBorder]: isThinking || !showBorder,
        [styles.thinking]: isThinking
      },
      className
    )}
    tabIndex={0}
    onKeyDown={(e) => {
      e.stopPropagation();
    }}
    >
      <div className={styles.content}>
        {renderContent(content)}
      </div>
    </div>
  );

  const shouldRenderImg = message.group_unique && message.draw && message.draw.length > 0 &&
                          (message.draw[0].width !== '0' || message.draw[0].status === 'PROCESSING');

  return (
      <div className={classNames(styles.messageWrapper, className)}>
        {message.thinking_content && role === MessageRole.ASSISTANT && (
          <Collapsible title="深度思考" sourceFrom={sourceFrom}>
            {renderMessageContent(message.thinking_content, true, role.toLowerCase())}
          </Collapsible>
        )}
        {message.content && renderMessageContent(message.content, false, role.toLowerCase())}
        {shouldRenderImg && message.status === MessageStatus.SENT && (
          <ChatRenderImage message={message} addPic={addPic} uploadLimitPopup={uploadLimitPopup} />
        )}
        {/* {shouldRenderTemplate && message.status === MessageStatus.SENT && (
          <ChatRenderTemplate message={message} popMessage={popMessage} />
        )} */}
        {message.status === MessageStatus.LACK && role === MessageRole.ASSISTANT && (
          <div className={styles.lack}>
            <div className={styles.purchaseText}>
              您的设计生成次数不足，无法生成设计图片，请先开通会员。
            </div>
            <div>
              <button className={styles.purchaseButton} onClick={() => {
                chatApi.setPv(9091, { additional: { i1: sourceFrom, i2: 2 } });
                rechargeModalPopup?.('active', updateGenerationCount)
              }}>
                立即开通
              </button>
            </div>
          </div>
        )}
        {showActions && role === MessageRole.ASSISTANT && !isStreaming && status === MessageStatus.SENT && (
          <div className={styles.actions}>
            {message.content && !message.draw && <button
              className={styles.actionButton}
              onClick={handleCopy}
              title="复制内容"
            >
              <i className="iconfont-ai-chat ai-chat-fuzhi1"></i>复制
            </button>}
            {onRegenerate && !(''+message.message_id).includes('SENSITIVE-') && (
              <button
                className={styles.actionButton}
                onClick={handleRegenerate}
                title="重新生成"
              >
                <i className="iconfont-ai-chat ai-chat-zhongxinshengcheng1"></i>重新生成
              </button>
            )}
          </div>
        )}
      </div>
  );
};

interface CollapsibleProps {
  title: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
  sourceFrom: number;
  className?: string;
}

const Collapsible: React.FC<CollapsibleProps> = ({ title, children, defaultOpen = true, sourceFrom, className }) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const chatApi = new ChatApi();

  return (
      <div className={classNames(styles.collapsible, className)}>
        <div
            className={styles.collapsibleHeader}
            onClick={() => {
              setIsOpen(!isOpen);
              chatApi.setPv(9052, { additional: { i1: sourceFrom, i2: isOpen ? 0 : 1 } });
            }}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                setIsOpen(!isOpen);
                chatApi.setPv(9052, { additional: { i1: sourceFrom, i2: isOpen ? 0 : 1 } });
              }
            }}
        >
          <div className={styles.left}>
            <i className="iconfont-ai-chat ai-chat-shendusikao"></i>
            <span>{title}</span>
          </div>
          <div className={styles.right}>
            <i className={classNames('iconfont-ai-chat', isOpen ? 'ai-chat-shang' : 'ai-chat-you')}></i>
          </div>
        </div>
        {isOpen && <div className={styles.collapsibleContent}>{children}</div>}
      </div>
  );
};