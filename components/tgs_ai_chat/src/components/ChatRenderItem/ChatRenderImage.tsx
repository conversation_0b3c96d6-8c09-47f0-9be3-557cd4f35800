import React, { useState, useEffect, useRef, useMemo, useCallback } from "react";
import { IChatMessage, IDrawCheckData, IDrawData } from "../../types";
import { GenerateStatusEnum, useChatRenderApi } from "../../hooks/useChatRenderApi";
import styles from '../../styles/ChatRenderImage.module.scss';
import classNames from "classnames";
import { API_BASE_URL, aspectRatios, getImageOrientation, getOrigin, ImageOrientation } from "../../utils/chatUtils";
import { useGeneration, GenerationStatus } from "../../context/GenerationContext";
import { ChatApi } from "../../api/chatApi";
import { useChatSettings } from '../../context/ChatSettingsContext';
import { useTemplSave } from "../../hooks/useTemplateSave";
import { useResizeObserver } from '../../hooks/useResizeObserver';
import { IMessageItem } from "../../hooks/useTemplateSave";

const colors = [
  '#FFCDD2',
  '#F8BBD0',
  '#E1BEE7',
  '#D1C4E9',
  '#C5CAE9',
  '#BBDEFB',
  '#B3E5FC',
  '#B2EBF2',
  '#B2DFDB',
  '#C8E6C9',
  '#DCEDC8',
  '#F0F4C3',
  '#FFF9C4',
  '#FFE0B2',
  '#FFCCBC',
  '#D7CCC8',
]
const CHECK_INTERVAL = 2000;
interface IImgAsset {
  width: string;
  source_width: string;
  height: string;
  source_height: string;
  image_url: string;
  id: string;
}

interface ChatRenderItemProps {
  message: IChatMessage;
  addPic: (asset: any) => void
  uploadLimitPopup?: () => void
}

/**
 * 图片渲染组件，生成图片时渲染 并且* 历史记录里的智能设计以图片形式渲染
 * @param param0 
 * @returns 
 */

export const ChatRenderImage = ({ message, addPic, uploadLimitPopup }: ChatRenderItemProps) => {
  const { setGenerationStatus } = useGeneration();
  const { size, sourceFrom } = useChatSettings();
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState(0);
  const { drawCheck, saveImage } = useChatRenderApi({
    apiBaseUrl: API_BASE_URL,
  });
  const { group_unique = '', draw } = message;
  const [images, setImages] = useState<IDrawData[]>([]);
  const [orientation, setOrientation] = useState<ImageOrientation>(ImageOrientation.Square);
  const [uploadResult, setUploadResult] = useState<Record<string, { picId: string, upicId: string, aiproduce_id: number }>>({});
  const checkRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const renderRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const chatApi = new ChatApi();
  const { goEditWithoutDoc, showUploadLimitModal } = useTemplSave(sourceFrom);

  // Move useResizeObserver to top level with useCallback
  const handleResize = useCallback((entries: ResizeObserverEntry[]) => {
    try {
      const width = entries[0]?.contentRect.width;
      
      // Only update if width actually changed
      if (width && width !== containerWidth) {
        setContainerWidth(width);
      }
    } catch (error) {
      console.error('Error in ResizeObserver callback:', error);
    }
  }, [containerWidth]);

  useResizeObserver(containerRef, handleResize);

  useEffect(() => {
    if (!containerRef.current) return;
    // Remove the useResizeObserver call from here since it's now at the top level
  }, []);

  const getImages = async () => {
    const res = await drawCheck(group_unique);
    if(res && res.code === 1) {
      const isAllEnding = res.data.every((item: any) => item.status !== GenerateStatusEnum.PROCESSING);
      
      // Update only images with preview URL using setImages function
      setImages(prevImages => {
        const newImages = [...prevImages];
        res.data.forEach((item: IDrawCheckData, index: number) => {
          if (item.preview) {
            newImages[index] = {
              id: item.ai_produce_id,
              preview: item.preview,
              status: item.status,
              width: item.width.toString(),
              height: item.height.toString(),
              group_unique,
              aiproduce_draw_id: item.ai_produce_draw_id.toString(),
              draw_template_style: '',
              path: '',
              templ_id: '',
              user_templ_id: '',
            } as IDrawData;
          }
        });
        return newImages;
      });

      if(!isAllEnding) {
        checkRef.current = setTimeout(() => {
          getImages();
        }, CHECK_INTERVAL);
      } else {
        setGenerationStatus(GenerationStatus.COMPLETED);
      }
    }
  }

  const updateUploadResult = (picId: string, upicId: string, aiproduce_id: number) => {

    setUploadResult(pre => {
      const newUploadResult = { ...pre };
      newUploadResult[aiproduce_id] = { picId, upicId, aiproduce_id };
      return newUploadResult;
    });
  }

  const handleImageOperation = async (image: IDrawData, type: 'add' | 'open') => {
    const { preview, width, height, path,user_asset_id } = image;
    chatApi.setPv(9065,{additional: {
      i1: sourceFrom,
      i2: type,
    }})
    if(user_asset_id && user_asset_id !== '0') {
      if(type === 'add') {
        handleAddToCanvas({width, source_width: width, height, source_height: height, image_url: path || preview, id: 'UA-'+user_asset_id});
      } else {
        handleEditImage(width, height, 'UA-'+user_asset_id, path || preview);
      }
      return;
    }
    const res = await saveImage(image.aiproduce_draw_id);
    if(res && res.code === 1) {
      const { ua_id } = res.data[0];
      if(type === 'add') {
        handleAddToCanvas({width, source_width: width, height, source_height: height, image_url: path || preview, id: ua_id});
      } else {
        handleEditImage(width, height, ua_id, path || preview);
      }
    }
    if(res && res.code === 0) {
      uploadLimitPopup && uploadLimitPopup();
    }
  }

  const handleEditImage = (width: string, height: string, user_asset_id: string, path: string) => {
    if (window.AppParams && window.AppParams.handleToEditor) { 
        return window.AppParams.handleToEditor(path, user_asset_id, width, height);
    }
    if(user_asset_id && user_asset_id !== '0') {
      const pram = btoa(JSON.stringify({ path }));
      const jumpUrl = `//ue.818ps.com/v4/?w=${width}&h=${height}&user_asset_id=${user_asset_id.split('-')[1]}&user_asset_id_2=${user_asset_id}&pram=${pram}&origin=${getOrigin(sourceFrom)}_aiChat`;
      window.open(jumpUrl, '_blank');
    }
  }

  const handleAddToCanvas = (image: IImgAsset) => {
    addPic(image);
  }

  useEffect(() => {
    if(showUploadLimitModal) {
      uploadLimitPopup && uploadLimitPopup();
    }
  }, [showUploadLimitModal]);
  
  useEffect(() => {
    if(group_unique) {
      if(draw && draw.length > 0) {
        setImages(draw);
        setOrientation(getImageOrientation(parseInt(draw[0].width), parseInt(draw[0].height)));
        const hasProcessingImages = draw.some((item: IDrawData) => !item.preview);
        if(hasProcessingImages) {
          getImages();
        }
      }
    } else {
      console.log('group_unique', group_unique)
      // Clear images when group_unique is removed
      setImages([]);
      setGenerationStatus(GenerationStatus.IDLE);
    }
    return () => {
      if(checkRef.current) {
        clearTimeout(checkRef.current);
      }
      if(renderRef.current) {
        clearInterval(renderRef.current);
      }
    }
  }, [group_unique, setGenerationStatus]);

  const renderSkeleton = useMemo(() => {
    const randomColorMap = {
      0: colors[Math.floor(Math.random() * colors.length)],
      1: colors[Math.floor(Math.random() * colors.length)],
      2: colors[Math.floor(Math.random() * colors.length)],
      3: colors[Math.floor(Math.random() * colors.length)],
      4: colors[Math.floor(Math.random() * colors.length)],
      5: colors[Math.floor(Math.random() * colors.length)],
      6: colors[Math.floor(Math.random() * colors.length)],
      7: colors[Math.floor(Math.random() * colors.length)],
      8: colors[Math.floor(Math.random() * colors.length)],
      9: colors[Math.floor(Math.random() * colors.length)],
      10: colors[Math.floor(Math.random() * colors.length)],
      11: colors[Math.floor(Math.random() * colors.length)],
      12: colors[Math.floor(Math.random() * colors.length)],
    }
    return (index: number) => {
      return (
        <div 
          className={styles.shimmer}
          style={{
            background: randomColorMap[index as keyof typeof randomColorMap]
          }}
        />
      );
    };
  }, []);
  const renderSkeletonPain = useMemo(() => {
    const randomColorMap = {
      0: colors[Math.floor(Math.random() * colors.length)],
      1: colors[Math.floor(Math.random() * colors.length)],
      2: colors[Math.floor(Math.random() * colors.length)],
      3: colors[Math.floor(Math.random() * colors.length)],
      4: colors[Math.floor(Math.random() * colors.length)],
      5: colors[Math.floor(Math.random() * colors.length)],
      6: colors[Math.floor(Math.random() * colors.length)],
      7: colors[Math.floor(Math.random() * colors.length)],
      8: colors[Math.floor(Math.random() * colors.length)],
      9: colors[Math.floor(Math.random() * colors.length)],
      10: colors[Math.floor(Math.random() * colors.length)],
      11: colors[Math.floor(Math.random() * colors.length)],
      12: colors[Math.floor(Math.random() * colors.length)],
    }
    return (index: number) => {
      return (
        <div 
          className={styles.shimmerPain}
          style={{
            background: randomColorMap[index as keyof typeof randomColorMap]
          }}
        />
      );
    };
  }, []);

  const calculateImageDimensions = (width: string, height: string, produce_type: string = '0') => {
    if (!containerWidth) return { width: '100%', height: 'auto' };
    const originalWidth = parseInt(width) || 1;
    const originalHeight = parseInt(height) || 1;
    if(typeof originalWidth !== 'number' || typeof originalHeight !== 'number' || originalWidth <= 0 || originalHeight <= 0) {
      return {
        width: '100%',
        height: 'auto'
      }
    }
    
    const templateAspectRatio = aspectRatios[Number(produce_type)];
    let aspectRatio
    if(Number(produce_type) > 0) {
      aspectRatio = templateAspectRatio.width / templateAspectRatio.height;
    } else {
      aspectRatio = originalWidth / originalHeight;
    }

    const columns = {
      small: {
        [ImageOrientation.Landscape]: 1,
        [ImageOrientation.Square]: 2,
        [ImageOrientation.Portrait]: 2
      },
      large: {
        [ImageOrientation.Landscape]: 2,
        [ImageOrientation.Square]: 4,
        [ImageOrientation.Portrait]: 4
      },      
      app: {
        [ImageOrientation.Landscape]: 1,
        [ImageOrientation.Square]: 2,
        [ImageOrientation.Portrait]: 2
      },
    }[size][orientation];
    const gap = 12;
    const availableWidth = (containerWidth - (gap * (columns - 1))) / columns;
    
    return {
      width: `${availableWidth}px`,
      height: `${availableWidth / aspectRatio}px`,
      aspectRatio
    };
  };
  const { title, main_text, sed_title, thd_title, introduction } = message.contents?.content || {};
  return (
    <div 
      ref={containerRef}
      className={classNames(styles.imagesWrap, styles[orientation], styles[size])}
    >
      {images.map((image, index) => (
        <div 
          className={classNames(styles.imageItem)} 
          key={index}
          style={calculateImageDimensions(image.width, image.height, image.produce_type)}
        >
          {!image.preview && renderSkeleton(index)}
          {image.preview && (
            <>
              {image.produce_type === '0' || (image.user_templ_id !== '0' || image.draw_template_style === '6') ? <img 
                src={image.preview}
                className={classNames(styles.image, {
                  [styles.loaded]: image.preview
                })}
                style={{ 
                  aspectRatio: calculateImageDimensions(image.width, image.height).aspectRatio
                }}
              /> : renderSkeletonPain(index)}
              {image.draw_template_style !== '1' && <div className={styles.imageTitle}>
                <img src="https://js.tuguaishou.com/ai/aiGenerate/assets/ai_tag.png" alt="ai_tag" />
              </div>}
              <div className={classNames(styles.imageInfo, styles[size])} onClick={(e) => {
                if(sourceFrom === 1 || sourceFrom === 3) {
                  handleImageOperation(image, 'open');
                } else if(sourceFrom === 2) {
                  handleImageOperation(image, 'add');
                }
                e.stopPropagation();
              }}>
                {(!image.produce_type || image.produce_type === '0') ? <>
                    {(sourceFrom === 1 || sourceFrom === 3) && <div className={styles.imageButtonWrap}>
                    <button className={styles.imageButton} onClick={(e) => {
                      e.stopPropagation();
                      handleImageOperation(image, 'open');
                    }}>
                      设为底图编辑
                    </button>
                  </div>}
                  {sourceFrom === 2 && <div className={styles.imageButtonWrap}>
                    <button className={styles.imageButton} onClick={(e) => {
                      e.stopPropagation();
                      handleImageOperation(image, 'add');
                    }}>
                      添加到画布
                    </button>
                    <button className={styles.imageButton} onClick={(e) => {
                      e.stopPropagation();
                      handleImageOperation(image, 'open');
                    }}>
                      新编辑器打开
                    </button>
                  </div>}
                </> : <>
                  <button className={styles.imageButton} onClick={(e) => {
                    e.stopPropagation();
                    goEditWithoutDoc(e, {
                      ...image,
                      user_templ_id: uploadResult[image.id]?.upicId || image.user_templ_id,
                    }, {
                      title,
                      main_text,
                      sed_title,
                      thd_title,
                      introduction,
                      subjectColor: image.colour?.title,
                      auxiliaryColor: image.colour?.auxiliary,
                    } as unknown as IMessageItem, updateUploadResult);
                  }}>
                    新编辑器打开
                  </button>
                </>}
              </div>
            </>
          )}
        </div>
      ))}
    </div>
  )
}