import React, { useState, useMemo } from 'react';
import classNames from 'classnames';
import styles from '../styles/ChatHeader.module.scss';
import { IChatSession, IModalProps } from '../types';
import { ChatApi } from '../api/chatApi';
import { useChatApi } from '../hooks';
import { API_BASE_URL } from '../utils/chatUtils';
import { useChatSettings } from '../context/ChatSettingsContext';

interface GroupedSessions {
  today: IChatSession[];
  last7Days: IChatSession[];
  last30Days: IChatSession[];
}

export interface ChatHeaderProps {
  /**
   * Callback function when a new chat is started
   */
  onNewChat: () => void;

  /**
   * List of chat sessions
   */
  sessions?: IChatSession[];

  /**
   * Current session ID
   */
  currentSessionId?: string;

  /**
   * Current session
   */
  currentSession: IChatSession | null;

  /**
   * Callback when a session is selected
   */
  onSessionSelect?: (sessionId: string) => void;

  /**
   * Callback when dropdown is scrolled
   */
  onScroll?: (e: React.UIEvent<HTMLDivElement>) => void;

  /**
   * Whether more sessions are being loaded
   */
  isLoadingMore?: boolean;

  /**
   * Callback to reload session list
   */
  onReloadSessions?: () => void;

  /**
   * Modal component
   */
  Modal: React.ComponentType<IModalProps>;
  /**
   * Recharge modal popup
   */
  rechargeModalPopup?: (origin: string, callback: (isRecharge: boolean) => void) => void;

  /**
   * User info
   */
  userInfo?: any;

  /**
   * Open login model
   */
  openLoginModel?: () => void;
}

/**
 * Chat header component
 */
export const ChatHeader: React.FC<ChatHeaderProps> = ({
  onNewChat,
  sessions = [],
  currentSessionId,
  currentSession,
  onSessionSelect,
  onScroll,
  isLoadingMore,
  onReloadSessions,
  Modal,
}) => {
  const { size, sourceFrom } = useChatSettings();
  const [showDropdown, setShowDropdown] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [sessionToDelete, setSessionToDelete] = useState<string | null>(null);
  const { delSession } = useChatApi({
    apiBaseUrl: API_BASE_URL,
  });
  const chatApi = new ChatApi();
  // Group sessions by time
  const groupedSessions = useMemo(() => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    return sessions.reduce<GroupedSessions>(
      (acc, session) => {
        const updatedDate = new Date(session.updated);
        if (updatedDate >= today) {
          acc.today.push(session);
        } else if (updatedDate >= sevenDaysAgo) {
          acc.last7Days.push(session);
        } else if (updatedDate >= thirtyDaysAgo) {
          acc.last30Days.push(session);
        }
        return acc;
      },
      { today: [], last7Days: [], last30Days: [] }
    );
  }, [sessions]);

  const handleSessionSelect = (sessionId: string) => {
    if (onSessionSelect) {
      onSessionSelect(sessionId);
    }
    setShowDropdown(false);
  };

  const handleDeleteClick = (e: React.MouseEvent, sessionId: string) => {
    e.stopPropagation();
    setSessionToDelete(sessionId);
    setShowDeleteConfirm(true);
  };

  const handleDeleteConfirm = async () => {
    if (!sessionToDelete) return;
    try {
      await delSession(sessionToDelete);
      
      if (sessionToDelete === currentSessionId) {
        onNewChat();
      }
      
      // Reload session list after successful deletion
      onReloadSessions?.();
      
      setShowDeleteConfirm(false);
      setSessionToDelete(null);
    } catch (error) {
      console.error('Error deleting session:', error);
      // You might want to show an error message to the user here
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteConfirm(false);
    setSessionToDelete(null);
  };

  return (
      <div className={classNames(styles.header, size === 'small' ? styles.small : styles[size])}>
          {currentSession?.title ? <div className={styles.title}>{currentSession?.title}</div> : <div></div>}
          <div className={styles.actions}>
              <button
                  className={classNames(styles.actionBtn, size === 'small' ? styles.smallBtn : '')}
                  onClick={onNewChat}
              >
                  <i className="iconfont-ai-chat ai-chat-xinduihua"></i>
                  <span className={styles.btnTxt}>开始新对话</span>
              </button>
              {
                  <div className={styles.dropdownWrapper}>
                      <button
                          className={classNames(styles.actionBtn, size === 'small' ? styles.smallBtn : '')}
                          onClick={() => {
                              setShowDropdown(!showDropdown);
                              chatApi.setPv(9054, { additional: { i1: sourceFrom } });
                          }}
                      >
                          <i className="iconfont-ai-chat ai-chat-lishi"></i>
                          <span className={styles.btnTxt}>历史记录</span>
                      </button>
                      {showDropdown && (
                          <>
                              <div className={styles.dropdownMask} onClick={() => setShowDropdown(false)} />
                              <div className={classNames(styles.dropdown, styles.rightDropdown, styles[size])}>
                                  <div className={styles.dropdownScrollWrap}>
                                      <div className={styles.dropdownTitle}>历史对话</div>
                                      <div 
                                          className={styles.dropdownListWrap}
                                      >
                                          <div className={styles.dropdownList}
                                          onScroll={onScroll}>
                                              {groupedSessions.today.length === 0 &&
                                              groupedSessions.last7Days.length === 0 &&
                                              groupedSessions.last30Days.length === 0 ? (
                                                  <div className={styles.emptyState}>
                                                      <i className="iconfont-ai-chat ai-chat-kongneirong"></i>
                                                      <p>暂无历史记录</p>
                                                  </div>
                                              ) : (
                                                  <>
                                                      {/* 今天 */}
                                                      {groupedSessions.today.length > 0 && (
                                                          <>
                                                              <div className={styles.groupLabel}>今天</div>
                                                              {groupedSessions.today.map((session) => (
                                                                  <div
                                                                      className={styles.dropdownItemWrap}
                                                                      key={session.session_id}
                                                                  >
                                                                      <div
                                                                          className={classNames(
                                                                              styles.dropdownItem,
                                                                              session.session_id === currentSessionId
                                                                                  ? styles.active
                                                                                  : '',
                                                                          )}
                                                                          onClick={() =>
                                                                              handleSessionSelect(session.session_id)
                                                                          }
                                                                          tabIndex={0}
                                                                          aria-label={`历史会话 ${session.title}`}
                                                                          onKeyDown={(e) => {
                                                                              if (e.key === 'Enter')
                                                                                  handleSessionSelect(
                                                                                      session.session_id,
                                                                                  );
                                                                          }}
                                                                      >
                                                                          <span className={styles.titleWarp}>
                                                                              {session.title}
                                                                          </span>
                                                                          <button
                                                                              className={styles.deleteBtn}
                                                                              onClick={(e) =>
                                                                                  handleDeleteClick(
                                                                                      e,
                                                                                      session.session_id,
                                                                                  )
                                                                              }
                                                                              aria-label="删除会话"
                                                                              tabIndex={0}
                                                                          >
                                                                              <i className="iconfont-ai-chat ai-chat-a-leixingjichushiyishanchu"></i>
                                                                          </button>
                                                                      </div>
                                                                  </div>
                                                              ))}
                                                          </>
                                                      )}
                                                      {/* 近7天 */}
                                                      {groupedSessions.last7Days.length > 0 && (
                                                          <>
                                                              <div className={styles.groupLabel}>近7天</div>
                                                              {groupedSessions.last7Days.map((session) => (
                                                                  <div
                                                                      className={styles.dropdownItemWrap}
                                                                      key={session.session_id}
                                                                  >
                                                                      <div
                                                                          className={classNames(
                                                                              styles.dropdownItem,
                                                                              session.session_id === currentSessionId
                                                                                  ? styles.active
                                                                                  : '',
                                                                          )}
                                                                          onClick={() =>
                                                                              handleSessionSelect(session.session_id)
                                                                          }
                                                                          tabIndex={0}
                                                                          aria-label={`历史会话 ${session.title}`}
                                                                          onKeyDown={(e) => {
                                                                              if (e.key === 'Enter')
                                                                                  handleSessionSelect(
                                                                                      session.session_id,
                                                                                  );
                                                                          }}
                                                                      >
                                                                          <span className={styles.titleWarp}>
                                                                              {session.title}
                                                                          </span>
                                                                          <button
                                                                              className={styles.deleteBtn}
                                                                              onClick={(e) =>
                                                                                  handleDeleteClick(
                                                                                      e,
                                                                                      session.session_id,
                                                                                  )
                                                                              }
                                                                              aria-label="删除会话"
                                                                              tabIndex={0}
                                                                          >
                                                                              <i className="iconfont-ai-chat ai-chat-a-leixingjichushiyishanchu"></i>
                                                                          </button>
                                                                      </div>
                                                                  </div>
                                                              ))}
                                                          </>
                                                      )}
                                                      {/* 近30天 */}
                                                      {groupedSessions.last30Days.length > 0 && (
                                                          <>
                                                              <div className={styles.groupLabel}>近30天</div>
                                                              {groupedSessions.last30Days.map((session) => (
                                                                  <div
                                                                      className={styles.dropdownItemWrap}
                                                                      key={session.session_id}
                                                                  >
                                                                      <div
                                                                          className={classNames(
                                                                              styles.dropdownItem,
                                                                              session.session_id === currentSessionId
                                                                                  ? styles.active
                                                                                  : '',
                                                                          )}
                                                                          onClick={() =>
                                                                              handleSessionSelect(session.session_id)
                                                                          }
                                                                          tabIndex={0}
                                                                          aria-label={`历史会话 ${session.title}`}
                                                                          onKeyDown={(e) => {
                                                                              if (e.key === 'Enter')
                                                                                  handleSessionSelect(
                                                                                      session.session_id,
                                                                                  );
                                                                          }}
                                                                      >
                                                                          <span className={styles.titleWarp}>
                                                                              {session.title}
                                                                          </span>
                                                                          <button
                                                                              className={styles.deleteBtn}
                                                                              onClick={(e) =>
                                                                                  handleDeleteClick(
                                                                                      e,
                                                                                      session.session_id,
                                                                                  )
                                                                              }
                                                                              aria-label="删除会话"
                                                                              tabIndex={0}
                                                                          >
                                                                              <i className="iconfont-ai-chat ai-chat-a-leixingjichushiyishanchu"></i>
                                                                          </button>
                                                                      </div>
                                                                  </div>
                                                              ))}
                                                          </>
                                                      )}
                                                      {isLoadingMore && (
                                                          <div className={styles.loadingMore}>
                                                              <span className={styles.loadingText}>加载中...</span>
                                                          </div>
                                                      )}
                                                  </>
                                              )}
                                          </div>
                                      </div>
                                  </div>
                              </div>
                          </>
                      )}
                  </div>
              }
          </div>
          {/* @ts-ignore */}
          <Modal
              open={showDeleteConfirm}
              centered
              footer={null}
              width={600}
              styles={{
                  content: {
                      borderRadius: '12px',
                  },
              }}
              onCancel={handleDeleteCancel}
          >
              <div className={styles.confirmBox}>
                  <div className={styles.title}>
                      <i className="iconfont-ai-chat ai-chat-a-leixingjichushiyishanchu"></i>
                      <span className={styles.tip}>删除会话</span>
                  </div>
                  <div className={styles.mainWrap}>
                      <p>删除的会话将不可恢复，是否确认删除？</p>
                  </div>
                  <div className={styles.footerWrap}>
                      <button className={styles.cancelBtn} onClick={handleDeleteCancel}>
                          取消
                      </button>
                      <button className={styles.confirmBtn} onClick={handleDeleteConfirm}>
                          确认删除
                      </button>
                  </div>
              </div>
          </Modal>
      </div>
  );
};
