export interface StreamData {
  s?: '' | 'SENSITIVE' | 'SUCCESS' | 'ERROR' | 'END' | 'WAIT'; // status
  r?: string; // result/response
  c?: string; // content
  msg_id?: string; // message id
  o?: 'DRAW' | 'TEMPLATE'; // object
  i?: object; // info
}

export type StreamCallback = (data: StreamData | EventSourceData) => void;

export interface FetchStreamOptions {
  url: string;
  method?: 'POST' | 'GET';
  headers?: Record<string, string>;
  body?: any;
  signal?: AbortSignal;
  onMessage: StreamCallback;
  onDone?: () => void;
  onError?: (err: any) => void;
  onAbort?: () => void;
}

export const fetchStream = async ({
  url,
  method = 'POST',
  headers = {},
  body,
  signal,
  onMessage,
  onDone,
  onError,
  onAbort,
}: FetchStreamOptions) => {
  try {
    const baseUrl = '//818ps.com' + url;
    const response = await fetch(baseUrl, {
      method,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'text/plain;charset=UTF-8',
        ...headers,
      },
      body: JSON.stringify(body),
      credentials: 'include',
      signal
    });

    if (!response.body) throw new Error('No response body');

    const reader = response.body.getReader();
    const decoder = new TextDecoder('utf-8');
    let done = false;
    let buffer = '';

    try {
      while (!done) {
        const { value, done: readerDone } = await reader.read();
        if (value) {
          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;

          // Process complete lines
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // Keep incomplete line in buffer

          for (const line of lines) {
            if (line.trim()) {
              try {
                const dataMatch = line.match(/^data:\s*(.+)$/);
                if (dataMatch) {
                  const jsonData = JSON.parse(dataMatch[1]);
                  const streamData: StreamData = {
                    s: jsonData.s || '',
                    r: jsonData.r || '',
                    c: jsonData.c || '',
                    o: jsonData.o || '',
                    i: jsonData.i || {},
                    msg_id: jsonData.msg_id || ''
                  };
                  onMessage(streamData);
                }
              } catch (parseError) {
                console.warn('Failed to parse stream data:', line, parseError);
              }
            }
          }
        }
        done = readerDone;
      }

      // Process any remaining data in buffer
      if (buffer.trim()) {
        try {
          const dataMatch = buffer.match(/^data:\s*(.+)$/);
          if (dataMatch) {
            const jsonData = JSON.parse(dataMatch[1]);
            const streamData: StreamData = {
              s: jsonData.s || '',
              r: jsonData.r || '',
              c: jsonData.c || '',
              o: jsonData.o || '',
              i: jsonData.i || {},
              msg_id: jsonData.msg_id || ''
            };
            onMessage(streamData);
          }
        } catch (parseError) {
          console.warn('Failed to parse remaining buffer data:', buffer, parseError);
        }
      }

      onDone && onDone();
    } finally {
      reader.releaseLock();
    }
  } catch (err) {
    if (err instanceof DOMException && err.name === 'AbortError') {
      // 请求被取消
      onAbort && onAbort();
    }else{
      onError && onError(err);
    }
  }
};

export interface EventSourceOptions {
  url: string;
  headers?: Record<string, string>;
  onMessage: StreamCallback;
  onDone?: StreamCallback;
  onError?: (err: any) => void;
  onAbort?: () => void;
}

interface EventSourceData {
  data: any;
  markDownData: any;
  status: 'PROCESSING' | 'SUCCESS' | 'ERROR';
  code: string;
  modelId: number;
  msg: string;
  currency_id: string;
}

export const getEventSource = ({
  url,
  headers = {},
  onMessage,
  onDone,
  onError,
  onAbort,
}: EventSourceOptions) => {
  const baseUrl = '//818ps.com' + url;
  const eventSource = new EventSource(baseUrl, {withCredentials: true});

  // Add custom headers if needed
  if (Object.keys(headers).length > 0) {
    console.warn('EventSource does not support custom headers. Headers will be ignored.');
  }

  eventSource.onmessage = (event) => {
    try {
      const jsonData = JSON.parse(event.data);
      const streamData: EventSourceData = jsonData;
      onMessage(streamData as EventSourceData);
      // Check if this is the end message
      if (streamData.status	 === 'SUCCESS' || streamData.status	 === 'ERROR') {
        eventSource.close();
        onDone && onDone(streamData);
      }
    } catch (parseError) {
      console.warn('Failed to parse event data:', event.data, parseError);
    }
  };

  eventSource.onerror = (error) => {
    if (eventSource.readyState === EventSource.CLOSED) {
      onAbort && onAbort();
    } else {
      onError && onError(error);
    }
    eventSource.close();
  };

  // Return cleanup function
  return () => {
    eventSource.close();
  };
};