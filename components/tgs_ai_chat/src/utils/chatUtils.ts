import { IChatMessage, IChatSession } from '../types';
import { STORAGE_KEYS, MessageRole, MessageStatus } from '../constants';
import { IPSConfig } from '@tgs/general_components';

export const API_BASE_URL = '//818ps.com';

/**
 * Generate a unique ID for messages or sessions
 */
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

/**
 * Format timestamp to readable format
 * @param timestamp Unix timestamp in milliseconds
 */
export const formatTimestamp = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

/**
 * Format date for session display
 * @param timestamp Unix timestamp in milliseconds
 */
export const formatDate = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleDateString([], { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  });
};

/**
 * Save chat sessions to local storage
 * @param sessions Array of chat sessions
 * @param storageKey Custom storage key
 */
export const saveChatSessions = (
  sessions: IChatSession[], 
  storageKey: string = STORAGE_KEYS.CHAT_HISTORY
): void => {
  try {
    localStorage.setItem(storageKey, JSON.stringify(sessions));
  } catch (error) {
    console.error('Failed to save chat sessions:', error);
  }
};

/**
 * Load chat sessions from local storage
 * @param storageKey Custom storage key
 */
export const loadChatSessions = (
  storageKey: string = STORAGE_KEYS.CHAT_HISTORY
): IChatSession[] => {
  try {
    const sessions = localStorage.getItem(storageKey);
    return sessions ? JSON.parse(sessions) : [];
  } catch (error) {
    console.error('Failed to load chat sessions:', error);
    return [];
  }
};

/**
 * Create a new chat message
 * @param role Message role (user or assistant)
 * @param content Message content
 * @param model Optional AI model used
 */
export const createChatMessage = (
  role: MessageRole,
  content: string,
  model?: string
): IChatMessage => {
  return {
    message_id: generateId(),
    role,
    content,
    status: role === MessageRole.USER ? MessageStatus.SENT : MessageStatus.SENDING,
    model_id: model || '',
    thinking_enabled: '0',
    thinking_content: '',
  };
};

/**
 * Check if the device is mobile
 */
export const isMobileDevice = (): boolean => {
  // return window.innerWidth <= 768 || 
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);    
};

/**
 * Sanitize HTML to prevent XSS attacks
 * @param html HTML string to sanitize
 */
export const sanitizeHtml = (html: string): string => {
  const temp = document.createElement('div');
  temp.textContent = html;
  return temp.innerHTML;
};

/**
 * Debounce function to limit the rate at which a function can fire
 * @param func Function to debounce
 * @param wait Wait time in milliseconds
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  
  return function(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      func(...args);
    };
    
    if (timeout !== null) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(later, wait);
  };
};


export enum ImageOrientation {
  // 横屏
  Landscape = 'landscape',
  // 正方形
  Square = 'square',
  // 竖屏
  Portrait = 'portrait',
}

/**
 * 根据图片宽高判断方向（支持容差）
 * @param width 图片宽度
 * @param height 图片高度
 * @param tolerance 容差范围，默认 0.05（即 ±5%）
 * @returns ImageOrientation 枚举值
 */
export const getImageOrientation = (
  width: number,
  height: number,
  tolerance: number = 0.05
): ImageOrientation => {
  if (width <= 0 || height <= 0) {
    throw new Error('Width and height must be positive numbers.');
  }

  const ratio = width / height;

  if (Math.abs(ratio - 1) <= tolerance) {
    return ImageOrientation.Square;
  } else if (ratio > 1) {
    return ImageOrientation.Landscape;
  } else {
    return ImageOrientation.Portrait;
  }
}

export const IMG_CDN_PATH = 'https://js.tuguaishou.com/ai/aiGenerate/assets/';
export const ipsApi = IPSConfig.ipsApi;
export const ipsDownload = IPSConfig.ipsDownload;
export const ipsMatting = IPSConfig.ipsMatting;
// 获取指定名称的cookie
export function getCookie(name: string) {
    var strcookie = document.cookie; //获取cookie字符串
    var arrcookie = strcookie.split('; '); //分割
    //遍历匹配
    for (var i = 0; i < arrcookie.length; i++) {
        var arr = arrcookie[i].split('=');
        if (arr[0] == name) {
            return arr[1];
        }
    }
    return '';
}
export const options: RequestInit = {
    credentials: 'include',
    //@ts-ignore
    // headers: {
    //     ...IPSConfig.getAuthenticatio(),
    // },
};
export const getAuthenticatio = IPSConfig.getAuthenticatio.bind(IPSConfig);

export const cloneDeep = (obj: any) => {
  return JSON.parse(JSON.stringify(obj));
}

export const hexToRgb = (hex: string) => {
  if(!hex) return;
  // 移除可能存在的 # 符号
  hex = hex.replace('#', '');
  // 提取红、绿、蓝分量的十六进制值
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  // 返回 RGB 格式的字符串
  return { r, g, b, a:1 };
};
/**
* 将十六进制颜色转换为带透明度的rgba颜色
* @param hex 十六进制颜色
* @param opacity 透明度 100-0
* @returns rgba颜色
*/
export const hexOpacityToRgba = (hex: string, opacity: number) => {
const rgb = hexToRgb(hex);
if (!rgb) return;
rgb.a = opacity / 100;
return rgb;
};
export const UploadAndReplaceConfig = {
  supportTemplateType: ['1', '4'],
};

export const assetTemplateConfig = {
  image: {
      meta: {
          locked: false,
          index: 0,
          name: '',
          group: '',
      },
      attribute: {
          resId: '',
          picUrl: '',
          width: undefined,
          height: undefined,
          assetHeight: '',
          assetWidth: '',
          opacity: 100,
          cropXFrom: 0,
          cropYFrom: 0,
          cropXTo: undefined,
          cropYTo: undefined,
      },
      transform: {
          posX: 0,
          posY: 0,
          horizontalFlip: false,
          verticalFlip: false,
          rotate: 0,
      },
  },
};

export const base64ToBlob = (base64: string): Blob => {
  const parts = base64.split(';');
  const mimeType = parts[0].split(':')[1];
  const data = parts[1].split(',')[1];
  const byteCharacters = atob(data);
  const byteNumbers = new Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  const byteArray = new Uint8Array(byteNumbers);
  const blob = new Blob([byteArray], { type: mimeType });

  return blob;
};

export const base64ToBlobURL = (base64: string) => {
  if(!base64) return '';
  const blob = base64ToBlob(base64);
  if(!blob) return '';
  // 生成blobURL
  return URL.createObjectURL(blob);
};

/**
 * @description: 获取url参数
 * @param {string} url
 * @return {*}
 */
export const getUrlParams = (url: string) => {
  if (!url) return {};
  const obj: Record<string, string> = {};
  const param = url.split('?')[1];
  if (!param) return {};
  const parr = param.split('&');
  for (const i of parr) {
      const arr = i.split('=');
      obj[arr[0]] = arr[1];
  }
  return obj;
};

export const getUrlParam = (url: string, key: string) => {
  return getUrlParams(url)[key];
};

/**
 * @description: 给链接拼接参数
 * @param {string} url
 * @param {Record} params
 * @param {*} unknown
 * @return {*}
 */
export const joinUrlParams = (url: string, params: Record<string, unknown>) => {
  if (!params) return url;
  const isHasQuery = url.charAt(url.length - 1) === '?';
  if (!isHasQuery) {
      url += '?';
  }
  for (const key in params) {
      if (params.hasOwnProperty(key)) {
          if (typeof params[key] == undefined) continue
          url += `${key}=${params[key]}&`;
      }
  }
  return url.slice(0, -1);
};

export class TaskQueue {
  private declare queue: Array<(...args: unknown[]) => Promise<void>>;
  private declare isRunning: boolean;
  declare clearListerner: (() => void)[];
  declare inActiveListener: (() => void)[];
  private declare isActive: boolean; // 是否是激活状态，后续是否还有task入队列
  constructor() {
      this.queue = [];
      this.isRunning = false;
      this.isActive = true;
      this.clearListerner = [];
      this.inActiveListener = [];
  }
  /**
   * @description: 运行任务
   * @return {*}
   */
  async run() {
      if (this.queue.length === 0) {
          this.isRunning = false;
          this.clearListerner.forEach((listener) => listener());
          this.clearListerner = [];
          return;
      }
      this.isRunning = true;
      const task = this.queue.shift();
      task && (await task());
      await this.run();
  }
  /**
   * @description: 添加任务
   * @param {function} task
   * @return {*}
   */
  addTask(task: (...args: any[]) => Promise<void>, immediate = true) {
      this.queue.push(task);
      if (!this.isRunning && immediate) {
          this.run();
      }
  }
  /**
   * @description: 阻塞队列执行
   * @param {number} time
   * @return {*}
   */
  async wait(time: number) {
      this.isRunning = true;
      await new Promise((resolve) => {
          setTimeout(() => {
              this.isRunning = false;
              this.run();
              resolve('');
          }, time);
      });
  }
  /**
   * @description: 清空队列
   * @return {*}
   */
  removeTasks() {
      this.queue = [];
      this.clearListerner = [];
  }
  checkIsRunning() {
      return this.isRunning;
  }
  /**
   * @description: 设置为false代表后续不会有task入队列
   * @param {boolean} active
   * @return {*}
   */
  setActive(active: boolean) {
      this.isActive = active;
      if (!active) {
          this.inActiveListener.forEach((listener) => listener());
          this.inActiveListener = [];
      }
  }
  /** 是否是激活状态
   * @description:
   * @return {*}
   */
  checkIsActive() {
      return this.isActive;
  }
  /**
   * @description: 增加队列清空监听
   * @param {function} listener
   * @return {*}
   */
  addClearListener(listener: () => void) {
      if (this.queue.length == 0 && !this.isRunning) {

          listener?.();
      } else {
          this.clearListerner.push(listener);
      }
  }
  addInActiveListener(listener: () => void) {
      this.inActiveListener.push(listener);
  }
  /**
   * @description: 获取当前待执行的队列任务运行长度
   * @return {*}
   */
  getQueueLength() {
      return this.queue.length;
  }
}

export const aspectRatios: Record<number, { width: number; height: number }> = {
  0: { width: 1, height: 1 }, // poster
  // Example: [templateType]: { width: number, height: number }
  1: { width: 1242, height: 2208 }, // poster
  3: { width: 900, height: 383 }, // public_account_first_image
  4: { width: 800, height: 2000 }, // marketing_long_image
  6: { width: 1242, height: 1660 }, // red_book
  7: { width: 1242, height: 2208 }, // pic_draw
  8: { width: 1242, height: 1660 }, // pic_red_note
  9: { width: 900, height: 383 }, // pic_prior
  10: { width: 1242, height: 1660 }, // pic_image_draw
  11: { width: 1242, height: 1660 }, // pic_image_red_note
  12: { width: 900, height: 383 }, // pic_image_prior
  // 1: { width: 1242, height: 1660 }, // poster
  // 2: { width: 1242, height: 1660 }, // public_account_first_image
  // 3: { width: 1242, height: 1660 }, // marketing_long_image
}; 

export const getOrigin = (sourceFrom: number) => {
  switch (sourceFrom) {
    case 1:
      return 'home';
    case 2:
      return 'editor';
    case 3:
      return 'app';
  }
}

function fallbackCopyTextToClipboard(text: string) {
	const textArea = document.createElement('textarea')
	textArea.value = text

	// Avoid scrolling to bottom
	textArea.style.top = '0'
	textArea.style.left = '0'
	textArea.style.position = 'fixed'

	document.body.appendChild(textArea)
	textArea.focus()
	textArea.select()

	return new Promise((resolve, reject) => {
		try {
			const successful = document.execCommand('copy')
			const msg = successful ? 'successful' : 'unsuccessful'
			console.log('Fallback: Copying text command was ' + msg)
			document.body.removeChild(textArea)
			resolve(msg)
		} catch (err) {
			console.warn('Fallback: Oops, unable to copy', err)
			document.body.removeChild(textArea)
			reject()
		}
	})
}

// 复制文本
export function copyTextToClipboard(text: string) {
	return fallbackCopyTextToClipboard(text)
}