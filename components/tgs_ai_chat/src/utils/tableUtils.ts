 // 表格处理工具函数
export interface TableData {
  headers: string[];
  rows: string[][];
  originalHtml?: string;
  tableElement?: Element;
}

/**
 * 从 DOM 元素解析表格数据
 */
export const parseTableFromElement = (tableElement: Element): TableData => {
  const headers: string[] = [];
  const rows: string[][] = [];
  
  // 提取表头
  const theadElements = tableElement.querySelectorAll('thead th');
  theadElements.forEach(th => {
    headers.push(th.textContent?.trim() || '');
  });
  
  // 提取表体数据
  const tbodyRows = tableElement.querySelectorAll('tbody tr');
  tbodyRows.forEach(tr => {
    const rowData: string[] = [];
    const cells = tr.querySelectorAll('td');
    cells.forEach(td => {
      rowData.push(td.textContent?.trim() || '');
    });
    if (rowData.length > 0) {
      rows.push(rowData);
    }
  });
  
  // 获取原始 HTML
  const originalHtml = tableElement.outerHTML;
  
  return { headers, rows, originalHtml, tableElement };
};

/**
 * 从 HTML 字符串解析表格数据
 */
export const parseTableFromHtml = (htmlContent: string): TableData | null => {
  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlContent, 'text/html');
  const table = doc.querySelector('table');
  
  if (!table) return null;
  
  return parseTableFromElement(table);
};

/**
 * 从制表符分隔的文本解析表格数据
 */
export const parseTableFromText = (textContent: string): TableData => {
  // 处理不同的换行符格式（Windows: \r\n, Mac: \r, Unix: \n）
  const lines = textContent.trim().split(/\r?\n/);
  if (lines.length === 0) return { headers: [], rows: [] };
  
  // 处理Excel复制的数据，可能包含引号包围的文本
  const parseExcelRow = (line: string): string[] => {
    const cells: string[] = [];
    let currentCell = '';
    let inQuotes = false;
    let i = 0;
    
    while (i < line.length) {
      const char = line[i];
      
      if (char === '"' && !inQuotes) {
        inQuotes = true;
      } else if (char === '"' && inQuotes) {
        if (i + 1 < line.length && line[i + 1] === '"') {
          currentCell += '"';
          i++;
        } else {
          inQuotes = false;
        }
      } else if (char === '\t' && !inQuotes) {
        cells.push(currentCell.trim());
        currentCell = '';
      } else {
        currentCell += char;
      }
      
      i++;
    }
    
    cells.push(currentCell.trim());
    return cells;
  };
  
  const headers = parseExcelRow(lines[0]);
  const rows: string[][] = [];
  
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();
    if (line) {
      const rowData = parseExcelRow(line);
      while (rowData.length < headers.length) {
        rowData.push('');
      }
      rows.push(rowData);
    }
  }
  
  return { headers, rows };
};

/**
 * 将表格数据转换为制表符分隔的文本
 */
export const convertTableToText = (tableData: TableData): string => {
  const { headers, rows } = tableData;
  
  if (headers.length === 0 && rows.length === 0) {
    return '';
  }
  
  let result = '';
  
  // 添加表头 (制表符分隔)
  if (headers.length > 0) {
    result += headers.join('\t') + '\n';
  }
  
  // 添加表体数据 (制表符分隔)
  rows.forEach(row => {
    result += row.join('\t') + '\n';
  });
  
  return result;
};

/**
 * 将表格数据转换为完整的 HTML
 */
export const convertTableToHtml = (tableData: TableData): string => {
  const { originalHtml, tableElement } = tableData;
  
  if (originalHtml) {
    return originalHtml;
  }
  
  if (tableElement) {
    return tableElement.outerHTML;
  }
  
  // 根据数据重新构建 HTML
  const { headers, rows } = tableData;
  let html = '<table>';
  
  // 构建表头
  if (headers.length > 0) {
    html += '<thead><tr>';
    headers.forEach(header => {
      html += `<th>${header}</th>`;
    });
    html += '</tr></thead>';
  }
  
  // 构建表体
  if (rows.length > 0) {
    html += '<tbody>';
    rows.forEach(row => {
      html += '<tr>';
      row.forEach(cell => {
        html += `<td>${cell}</td>`;
      });
      html += '</tr>';
    });
    html += '</tbody>';
  }
  
  html += '</table>';
  return html;
};

/**
 * 复制表格到剪贴板 (支持多格式)
 */
export const copyTableToClipboard = async (tableData: TableData): Promise<void> => {
  const htmlContent = convertTableToHtml(tableData);
  const textContent = convertTableToText(tableData);
  
  try {
    const clipboardItem = new ClipboardItem({
      'text/html': new Blob([htmlContent], { type: 'text/html' }),
      'text/plain': new Blob([textContent], { type: 'text/plain' })
    });
    
    await navigator.clipboard.write([clipboardItem]);
    console.log('Copied table with multiple formats to clipboard');
  } catch (error) {
    // 降级方案：使用 writeText
    console.warn('Failed to copy as HTML, falling back to text:', error);
    navigator.clipboard.writeText(textContent);
  }
};