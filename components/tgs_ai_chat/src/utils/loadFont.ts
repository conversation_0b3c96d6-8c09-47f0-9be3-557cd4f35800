export type SmallFont =  { font_family: string; font_path: string };
export class LoadFonts {
    static loadedSmallFontsLists: SmallFont[] = [];
    static fontNameValueList: Record<string, string> = {};
    public static getFontNameList(): Record<string, string> {
        return {
            font9: 'syht',
            font0: 'yahei',
            font1: 'fzfsjt',
            font2: 'fzhtjt',
            font3: 'fzktjt',
            font4: 'pmzdbtt',
            font5: 'qsgcfxt',
            font6: 'qscmxxjt',
            font7: 'qsyrt',
            font8: 'qsyh',
            // font9:'syht',
            font11: 'wqydkwmh',
            font12: 'wqydkzh',
            font13: 'zkgdh',
            font14: 'zkklt',
            font15: 'ztgjbs',
            font16: 'ztgjfm',
            font18: 'ztgjpxe',
            font19: 'ztgjrx',
            font54: 'systjixi',
            font55: 'systx',
            font56: 'systcg',
            font57: 'systct',
            font58: 'fn851sxzzt',
            font59: 'fnlhzt',
            font60: 'fnyrdzst',
            font61: 'fnzkkh',
            font62: 'fnzkxwlogot',
            font63: 'fnzqkhyt',
            font20: '2Dumb',
            font21: '3Dumb',
            font22: 'AaShouXieHB',
            font23: 'AaWanWan',
            font24: 'AbrilFatface',
            font25: 'AleoLight',
            font26: 'AleoRegular',
            font27: 'AlexBrush',
            font28: 'Aliquam',
            font29: 'AmaticSC',
            font30: 'Amble',
            font31: 'ArialRegular',
            font32: 'Aspire',
            font33: 'BebasNeueBook',
            font34: 'BebasNeue',
            font35: 'BukhariScript',
            font36: 'DancingScript',
            font37: 'DroidSerif',
            font38: 'Edo',
            font39: 'GrandHotel',
            font40: 'GreatVibes',
            font41: 'KoolBeans',
            font42: 'Langdon',
            font43: 'NeutonRegular',
            font44: 'NeutonSCLight',
            font45: 'OswaldStencil',
            font46: 'Parisish',
            font47: 'PlayfairDisplaySC',
            font48: 'Quango',
            font49: 'Rochester',
            font50: 'ScriptinaPro',
            font51: 'SixCaps',
            font52: 'Trocchi',
            font101: 'fnAmaranthBold',
            font102: 'fnAmaranthItalic',
            font103: 'fnAsapCondensedBold',
            font104: 'fnBadScriptRegular',
            font105: 'fnBahianaRegular',
            font106: 'fnBarrioRegular',
            font107: 'fnblowbrush',
            font108: 'fnDancingScriptBold',
            font109: 'fnDancingScriptRegular',
            font110: 'fnDolceVitaLight',
            font111: 'fnDroidSansBold',
            font112: 'fnFinelinerScript',
            font113: 'fnFirstShineregular',
            font114: 'fnLeagueScript',
            font115: 'fnMarckScriptRegular',
            font116: 'fnNISCRIPTRegular',
            font117: 'fnParalinesRegular',
            font118: 'fnPlaylistScript',
            font119: 'fnPoiretOneRegular',
            font120: 'fnPostNoBillsColomboExtraBold',
            font121: 'fnsoulhandwritingfreeversion',
            font122: 'fnTypoPRODancingScriptRegular',
            font123: 'fnfzfsft',
            font124: 'fnfzhtft',
            font125: 'fnfzktGBK',
            font126: 'fnfzktft',
            font127: 'fnhymztHanaMinA',
            font128: 'fnhymctHanaMinB',
            font129: 'fnsyhtLight',
            font130: 'fnsyhtRegular',
            font131: 'fnsyhtSourceHanSansKHeavy',
            // font132:'fnsyhtjzxExtraLight',
            font133: 'fnsyhtjzxLight',
            font134: 'fnsyhtjzxRegular',
            font135: 'fntwjybbzks',
            font136: 'fntwjybbzst',
            font137: 'fntwqzksmjs',
            font138: 'fnwhzbktkx',
            font139: 'fnwhzbktky',
            font140: 'fnwhzchtqpdw',
            font141: 'fnwhzcgtbz',
            font142: 'fnwhzchtsy',
            font143: 'fnwhzcktj',
            font144: 'fnwhzcytsk',
            font145: 'fnwhzhbtbts',
            font146: 'fnwhzktyf',
            font147: 'fnwhzklhb',
            font148: 'fnwhzkzhb',
            font149: 'fnwhzyktf',
            font150: 'fnwhzzlsf',
            font151: 'fnwhzzytf',
            font152: 'fnwhzzytsky',
            font153: 'fnyrdzstBold',
            font154: 'fnyrdzstExtralight',
            font155: 'fnyrdzstHeavy',
            font156: 'fnyrdzstMedium',
            font157: 'fnyrdzstRegular',
            font158: 'fnyrdzstSemibold',
            font159: 'fnyjmc',
            font160: 'fnzkwyt',
            font161: 'fnzkydlt01',
            font162: 'fnzkydlt02',
            font163: 'fnsystSCBold',
            font164: 'fnsystSCExtraLight',
            font165: 'fnsystSCHeavy',
            font166: 'fnsystSCLight',
            font167: 'fnfystSCRegular',
            font168: 'fnsystSCSemiBold',
            font169: 'fnsyhtHeavy',

            font200: 'zh2hllcht',
            font201: 'zh3hyxht',
            font202: 'zh4hcjxkt',
            font203: 'zh5hwwrht',
            font204: 'zh6hrjcht',
            font205: 'zh7hwntzt',
            font206: 'zh8hzznct',
            font207: 'zh10hgyxsjxkt',

            font208: 'zh17hmqgdt',
            font209: 'zh19hxyfbt',
            font210: 'zh20hstt',
            font211: 'zh21hbqsyt',
            font212: 'zh24hzhss',
            font213: 'zh27hbdt',

            font214: 'zh32hwzyxzt',
            font215: 'zh34hsnhft',
            font216: 'zh35hsxheht',
            font217: 'zh36hsxhskt',
            font218: 'zh37hsxhggt',
            font219: 'zh38hyxxkt',
            font220: 'zh39fzklt',

            font221: 'zh40hxcfft',
            font222: 'zh41hcxt',
            font223: 'zh42kxsk',
            font224: 'zh43hgcss',
            font225: 'zh44hkxyh',
            font226: 'zh46hmmt',
            font227: 'zh47hsfxk',
            font228: 'zh49hxyxs',

            font229: 'zh51qqzjt',
            font230: 'zh52hakmht',
            font231: 'zh53hyxt',
            font232: 'zh54hxh',
            font233: 'zh55hlyss',
            font234: 'zh56hhlmblsjt',
            font235: 'zh57hcxh',
            font236: 'zh58hczh',
            font237: 'zh59hcch',
            font238: 'zh60hmxszt',
            font239: 'zh62hsjt',
            font240: 'zh63hppt',
            font241: 'zh64hmqrtt',
            font242: 'zh66hzzt',

            font243: 'zh70hlyht',
            font244: 'zh71hysjs',
            font245: 'zh73hjnss',
            font246: 'zh74hfmss',
            font247: 'zh75hlyzmt',
            font248: 'zh79hmqnyt',

            font249: 'zh80hmqxyt',
            font250: 'zh81hqft',

            font251: 'zh92hxchbt',
            font252: 'zh95hsks',
            font253: 'zh96hhxss',

            font254: 'zh100hffxft',
            font255: 'zh101hxqx',
            font256: 'zh103hhtss',
            font257: 'zh104hsxt',
            font258: 'zh105hjyh',
            font259: 'zh107hmqhlt',
            font260: 'zh109hfgxzt',

            font261: 'zh110hwljht',
            font262: 'zh111hjbzpt',
            font263: 'zh113hdmsnt',
            font264: 'zh119htzeft',

            font265: 'zh120hpbsht',
            font266: 'zh122hqsb',
            font267: 'zh126hyzt',
            font268: 'zh127hyyt',
            font269: 'zh128hwxpmt',
            font270: 'zh129hbhxft',

            font271: 'zh130hklqht',
            font272: 'zh131hklcwt',
            font273: 'zh133hmkkb',
            font274: 'zh137hcyls',
            font275: 'zh138hbrss',
            font276: 'zh139hmqyyt',
            font277: 'zh141hhlydt',
            font280: 'zh116hfmss',

            font281: 'zh142hxhx',
            font282: 'zh151hlmzyt',
            font283: 'zh152hjjcjh',
            font284: 'zh155hfqt',
            // font285:'zh161hjhczpt',
            font286: 'zh162hyqllt',
            font287: 'zh164hfyh',
            font288: 'zh173hqxt',
            font289: 'zh179hzkbbt',
            font290: 'zh184hyycyh',
            font291: 'zh194hmqfxt',
            font292: 'zh199hmqcct',
            font293: 'zh201hmqyht',
            font294: 'zh208hxcqwt',

            font295: 'zh160ts',
            font296: 'zh176ccy',
            font297: 'zh181fcbtt',
            font298: 'zh143zkcjh',
            font299: 'zh167yrh',
            font300: 'zh144lyt',
            font301: 'zh31ns',
            font302: 'zh45byys',
            font303: 'zh23xws',
            font304: 'zh193wsfgt',
            font305: 'zh197dms',
            font306: 'zh237ylss',
            font307: 'zh210gfsnt',

            font308: 'zh2hllcht',
            font309: 'zh3hyxht',
            font310: 'zh49hxyxs',
            font312: 'zh71hysjs',
            font313: 'zh96hhxss',

            font314: 'zh111hjbzpt',
            font315: 'zh48btymhss',
            font316: 'zh67gyxs',
            font317: 'zh185zybs',
            font318: 'zh450fcpyt',

            font319: 'huaweisansmedium',
            font320: 'huaweisansthin',
            font321: 'huaweisans',
            font322: 'huaweisanslight',
            font323: 'huaweisansregular',
            font324: 'huaweifzlthbjw',
            font325: 'huaweifzltxhjw',
            font326: 'huaweifzltzhjw',
            font327: 'huaweifzltchfw',
            font328: 'huaweifzltxihfw',
            font329: 'huaweifzltzhunhfw',
            font330: 'huaweifzltchjw',
            font331: 'huaweifzlthjw',
            font332: 'huaweifzltxihjw',

            // vvip

            font333: 'zh5hwwrht',
            font334: 'zh64hmqrtt',
            font335: 'zh116hfmss',
            font336: 'zh138hbrss',
            font337: 'zh141hhlydt',
            font338: 'zh161hjhczpt',
            font339: 'zh106hmqlzt',
            font340: 'zh115hddt',
            font341: 'zh121hshss',
            font342: 'zh125hjzzs',
            font343: 'zh132hjzxk',
            font344: 'zh143hkaxs',
            font345: 'zh146hyrzzt',
            font346: 'zh147hxyh',
            font347: 'zh148hzkmft',
            font348: 'zh153hryjzt',
            font349: 'zh154hryh',
            font350: 'zh156hmqsdb',
            font351: 'zh157hmqtt',
            font352: 'zh158hmqchqq',
            font353: 'zh159hgks',
            font354: 'zh163hxchbb',
            font355: 'zh165hxbl',
            font356: 'zh166hqwt',
            font357: 'zh168hxml',
            font358: 'zh170hjyss',
            font359: 'zh171hflh',
            font360: 'zh172hmqmlt',
            font361: 'zh174hxbt',
            font362: 'zh175hczy',
            font363: 'zh177hplss',
            font364: 'zh178hxczyt',
            font365: 'zh180hhrss',
            font366: 'zh182hxckkh',
            font367: 'zh183hmlqqt',
            font368: 'zh186hjhxyt',
            font369: 'zh187hmqxyt',
            font370: 'zh188hxcsdt',
            font371: 'zh189hxylht',
            font372: 'zh190htnhbt',
            font373: 'zh191hnyxgt',
            font374: 'zh192hmqlbt',
            font375: 'zh195hmqbqt',
            font376: 'zh196hdms',
            font377: 'zh198hmqxdd',
            font378: 'zh202hpxt',
            font379: 'zh203hxcbwt',
            font380: 'zh204hkljmt',
            font381: 'zh206hjhss',
            font382: 'zh207htjznh',
            font383: 'zh209hyyss',
            font384: 'zh211hklxqt',
            font385: 'zh212hlmqst',
            font386: 'zh213hlls',
            font387: 'zh214hthgst',
            font388: 'zh215hqpss',
            font389: 'zh216hwyxft',
            font390: 'zh217hjdhft',
            font391: 'zh218hwyqxt',
            font392: 'zh219hmqllt',
            font393: 'zh220hhyss',
            font394: 'zh221hlclyt',
            font395: 'zh222hmqhhs',
            font396: 'zh223hxrjxt',
            font397: 'zh224hgdhxt',
            font398: 'zh225hmyss',
            font399: 'zh226hgcsshdb',
            font400: 'zh227hmqlbt',
            font401: 'zh228hyxss',
            font402: 'zh229hyyt',
            font403: 'zh230hklmxt',
            font404: 'zh231htcxrt',
            font405: 'zh232hchggt',
            font406: 'zh233hmqxyt',
            font407: 'zh234hldbdt',
            font408: 'zh235hqzh',
            font409: 'zh236hmqbdw',
            font410: 'zh238hjxt',
            font411: 'zh239hyfss',
            font412: 'zh240hqmss',
            font413: 'zh241hqft',
            font414: 'zh242hyyss',
            font415: 'zh243hzst',
            font416: 'zh244hyzt',
            font417: 'zh245hfyxs',
            font418: 'zh246hcqss',
            font419: 'zh247hjdyhqb',
            font420: 'zh248hyyss',
            font421: 'zh249hmqybt',
            font422: 'zh250hlyss',
            font423: 'zh251hjlss',
            font424: 'zh252hfyss',
            font425: 'zh253hmqttw',
            font426: 'zh254hmqtzt',
            font427: 'zh255hbwysh',
            font428: 'zh256hldh',
            font429: 'zh257hmyss',
            font430: 'zh258hegt',
            font431: 'zh259hchqqt',
            font432: 'zh50hbgtxt',
        };
    }
    /**
     * @description: 加载字体
     * @param {SmallFont} small_fonts
     * @return {*}
     */
    public static load(small_fonts: SmallFont[]) {
        if (!small_fonts) return;
        const fontlistObj = this.getFontNameList();
        const fontValueListObj = this.getFontNameValueList();
        const newFontFamily = document.createElement('style');
        newFontFamily.id = 'newFontFamily';
        this.loadedSmallFontsLists = small_fonts;
        const smallFontKeys: Record<string, boolean> = {};
        if (this.loadedSmallFontsLists) {
            this.loadedSmallFontsLists.forEach((font: { [key: string]: any }) => {
                smallFontKeys[font.font_family] = true;
            });
            localStorage.setItem('local_smallFontKeys', JSON.stringify(smallFontKeys));
        }
        let existSmallFont = false;
        small_fonts.map((font: { [key: string]: any }) => {
            const key = font.font_family;
            if (fontlistObj[font.font_family]) {
                //加载小字体
                newFontFamily.appendChild(
                    document.createTextNode(`
                                    @font-face {
                                        font-family: '${key}_small';
                                        src:url('${font.font_path}') format('truetype');
                                    }
                                `),
                );
                newFontFamily.appendChild(
                    document.createTextNode(`
                                    @font-face {
                                        font-family: '${key}';
                                        src:url('//js.tuguaishou.com/fonts/${fontlistObj[key]}.woff') format('truetype');
                                    }
                                `),
                );
                existSmallFont = true;
            } else if (fontValueListObj[key]) {
                newFontFamily.appendChild(
                    document.createTextNode(`
                                    @font-face {
                                        font-family: '${fontValueListObj[key]}';
                                        src:url('//js.tuguaishou.com/fonts/${key}.woff') format('truetype');
                                    }
                                `),
                );
                existSmallFont = true;
            }
        });

        if (!existSmallFont) {
            for (let key in fontlistObj) {
                //加载大字体

                newFontFamily.appendChild(
                    document.createTextNode(`
                    @font-face {
                        font-family: '${key}';
                        src:url('//js.tuguaishou.com/fonts/${fontlistObj[key]}.woff') format('truetype');
                    }
                `),
                );
            }
        }
        document.head.appendChild(newFontFamily);
    }
    public static getFontNameValueList() {
        if (Object.keys(this.fontNameValueList).length > 0) {
            return this.fontNameValueList;
        }
        const fontNameList = this.getFontNameList();
        const fontNameValueList: Record<string, string> = {};
        Object.keys(fontNameList).map(function (key) {
            Object.assign(fontNameValueList, { [fontNameList[key]]: key });
        });
        this.fontNameValueList = fontNameValueList;
        return fontNameValueList;
    }
}
