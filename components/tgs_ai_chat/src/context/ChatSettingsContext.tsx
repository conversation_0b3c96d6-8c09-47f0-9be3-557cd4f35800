import React, { createContext, useContext, ReactNode } from 'react';
import { ITgsAiChatProps, IEditorController, IPopMessage } from '../types';

export type ChatSize = NonNullable<ITgsAiChatProps['size']>;
export type SourceFrom = NonNullable<ITgsAiChatProps['sourceFrom']>;

interface ChatSettingsContextType {
  size: ChatSize;
  sourceFrom: SourceFrom;
  editorController?: IEditorController;
  popMessage: IPopMessage;
}

const defaultContext: ChatSettingsContextType = {
  size: 'large',
  sourceFrom: 1,
  editorController: undefined,
  popMessage: {
    error: () => {},
    info: () => {},
    success: () => {},
    warning: () => {}
  }
};

const ChatSettingsContext = createContext<ChatSettingsContextType>(defaultContext);

interface ChatSettingsProviderProps {
  children: ReactNode;
  size?: ChatSize;
  sourceFrom?: SourceFrom;
  editorController?: IEditorController;
  /**
   * Popover message component
   */
  popMessage: IPopMessage;
}

export const ChatSettingsProvider: React.FC<ChatSettingsProviderProps> = ({ 
  children, 
  size = defaultContext.size, 
  sourceFrom = defaultContext.sourceFrom,
  editorController,
  popMessage
}) => {
  const value = {
    size,
    sourceFrom,
    editorController,
    popMessage
  };

  return (
    <ChatSettingsContext.Provider value={value}>
      {children}
    </ChatSettingsContext.Provider>
  );
};

export const useChatSettings = () => {
  const context = useContext(ChatSettingsContext);
  if (!context) {
    throw new Error('useChatSettings must be used within a ChatSettingsProvider');
  }
  return context;
}; 