import React, { createContext, useContext, useState, useCallback, ReactNode, useEffect } from 'react';
import { ChatApi } from '../api/chatApi';

export enum GenerationType {
  TEXT = 'text',
  IMAGE = 'image',
  AIDESIGN = 'AIDESIGN'
}

export enum GenerationStatus {
  IDLE = 'idle',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  ERROR = 'error'
}

interface GenerationContextType {
  generationType: GenerationType;
  generationStatus: GenerationStatus;
  aiDesignInfo: any;
  deepThink: boolean;
  setGenerationType: (type: GenerationType) => void;
  setGenerationStatus: (status: GenerationStatus) => void;
  setDeepThink: (value: boolean) => void;
  updateGenerationCount: () => void;
  resetGeneration: () => void;
}

const GenerationContext = createContext<GenerationContextType | undefined>(undefined);

interface GenerationProviderProps {
  children: ReactNode;
  initialDeepThink?: boolean;
}

export const GenerationProvider: React.FC<GenerationProviderProps> = ({ 
  children,
  initialDeepThink = true
}) => {
  const chatApi = new ChatApi();
  const [generationType, setGenerationType] = useState<GenerationType>(GenerationType.TEXT);
  const [generationStatus, setGenerationStatus] = useState<GenerationStatus>(GenerationStatus.IDLE);
  const [aiDesignInfo, setAiDesignInfo] = useState<any>(null);
  const [deepThink, setDeepThink] = useState<boolean>(initialDeepThink);

  const updateGenerationCount = useCallback(async () => {
    // api fetch
    const res = await chatApi.getGenerateNum() as any
    setAiDesignInfo(res.data)
  }, []);

  const resetGeneration = useCallback(() => {
    setGenerationType(GenerationType.TEXT);
    setGenerationStatus(GenerationStatus.IDLE);
  }, []);

  useEffect(() => {
    console.log('generationType', generationType)
  },[generationType])

  const getAiDesignInfo = useCallback(() => {
    // api fetch
    updateGenerationCount()
  }, []);

  useEffect(() => {
    getAiDesignInfo()
  },[])

  return (
    <GenerationContext.Provider
      value={{
        generationType,
        generationStatus,
        aiDesignInfo,
        deepThink,
        setGenerationType,
        setGenerationStatus,
        setDeepThink,
        updateGenerationCount,
        resetGeneration
      }}
    >
      {children}
    </GenerationContext.Provider>
  );
};

export const useGeneration = () => {
  const context = useContext(GenerationContext);
  if (context === undefined) {
    throw new Error('useGeneration must be used within a GenerationProvider');
  }
  return context;
}; 