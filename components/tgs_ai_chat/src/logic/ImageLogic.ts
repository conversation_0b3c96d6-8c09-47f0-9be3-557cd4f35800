import { assetTemplateConfig, cloneDeep } from '../utils/chatUtils';
import { IImageAsset, TgsTypes } from '@tgs/types';
import { IAiDesignTemplateImgTagTypeEnum } from '@tgs/types';
import { IMessageItem } from '../hooks/useTemplateSave';

export interface IUploadAssetItem {
  id: string;
  path: string;
  width: number;
  height: number;
}

export class ImageLogic {
    private static imageType = ['image', 'pic', 'background', 'qrcode'];
    private static containerAssetType = [
        IAiDesignTemplateImgTagTypeEnum.IMGCONTAINER,
        IAiDesignTemplateImgTagTypeEnum.MATTINGIMGCONTAINER,
    ];
    private static hasImageContainer = false;
    private static imageReplaceStashIndex = {
        imgContainerIndex: 0,
        logoIndex: 0,
        qrcodeIndex: 0,
    };
    private static logoAssetConfig = {
        height: 100,
        left: 60,
        top: 60,
    };
    private static qrcodeAssetConfig = {
        height: 120,
        right: 40,
        bottom: 40,
    };
    public static replaceImageContent = (message: IMessageItem, asset: TgsTypes.IImageAsset) => {
        if (!this.imageType.includes(asset.meta.type)) return;
        if (!asset.meta.rt_tag_type) return;
        const { imgContainer, logo, qrcode } = message;
        this.hasImageContainer = false;
        let imgItem: IUploadAssetItem | undefined = void 0;
        switch (asset.meta.rt_tag_type) {
            case IAiDesignTemplateImgTagTypeEnum.IMGCONTAINER:
            case IAiDesignTemplateImgTagTypeEnum.MATTINGIMGCONTAINER:
                this.hasImageContainer = true;
                if (!imgContainer?.length) return;
                const imgContainerIndex = Math.min(
                    this.imageReplaceStashIndex.imgContainerIndex++,
                    imgContainer.length - 1,
                );
                imgItem = imgContainer[imgContainerIndex];
                this.replaceContainerAsset(imgItem, asset);
                return;
            case IAiDesignTemplateImgTagTypeEnum.LOGO:
                if (!logo?.length) return;
                const logoIndex = Math.min(this.imageReplaceStashIndex.logoIndex++, logo.length - 1);
                imgItem = logo[logoIndex];
                this.replaceAnchorAsset(imgItem, asset);
                return;

            case IAiDesignTemplateImgTagTypeEnum.QRCODE:
                if (!qrcode?.length) return;
                const qrcodeIndex = Math.min(this.imageReplaceStashIndex.qrcodeIndex++, qrcode.length - 1);
                imgItem = qrcode[qrcodeIndex];
                this.replaceAnchorAsset(imgItem, asset);
                return;
        }
    };
    // 替换标记过得图片素材
    public static replaceContainerAsset(imgInfo: IUploadAssetItem, replaceAsset: TgsTypes.IImageAsset) {
        if (!replaceAsset || !imgInfo || !replaceAsset.meta.rt_tag_type) return;
        if (!this.containerAssetType.includes(replaceAsset.meta.rt_tag_type)) return;
        if (!replaceAsset.attribute.container) return;
        const { id, path, width, height } = imgInfo;

        Object.assign(replaceAsset.attribute, {
            resId: id,
            picUrl: path,
            width,
            height,
        });
        const attr = replaceAsset.attribute;
        if (!attr.container) return;
        const isVerticalImage = height > width;
        if (isVerticalImage) {
            attr.width = attr.container.width;
            attr.height = (attr.width * height) / width;
            Object.assign(replaceAsset.attribute.container, {
                posX: 0,
                posY: -(attr.height - attr.container.height) / 2,
            });
        } else {
            attr.height = attr.container.height;
            attr.width = (attr.height * width) / height;
            Object.assign(replaceAsset.attribute.container, {
                posX: -(attr.width - attr.container.width) / 2,
                posY: 0,
            });
        }
    }
    /**
     * @description: 替换锚点图片素材
     * @param {IUploadAssetItem} imgInfo
     * @param {TgsTypes} replaceAsset
     * @return {*}
     */
    public static replaceAnchorAsset(
        imgInfo: IUploadAssetItem,
        replaceAsset: TgsTypes.IImageAsset | TgsTypes.IQRCodeAsset,
    ) {
        if (!replaceAsset || !imgInfo) return;
        const tagType = replaceAsset.meta.rt_tag_type;
        if (!tagType) return;
        const tagTypeWhiteList = [IAiDesignTemplateImgTagTypeEnum.LOGO, IAiDesignTemplateImgTagTypeEnum.QRCODE];
        if (!tagTypeWhiteList.includes(tagType)) return;
        // 保持高度，宽度适配
        const { id, path, width = 0, height = 0 } = imgInfo;
        const aspectRatio = Number(width) / Number(height);
        if (isNaN(aspectRatio)) return;
        // 以原素材高度为准，宽度自适应
        const assetWidth = aspectRatio * replaceAsset.attribute.height;

        Object.assign(replaceAsset.attribute, {
            resId: id,
            picUrl: path,
            width: assetWidth,
        });
        if (replaceAsset.meta.type == 'qrcode') {
            Object.assign(replaceAsset.meta, {
                type: 'pic',
            });
            Object.assign(replaceAsset.attribute, {
                resId: id,
                picUrl: path,
                width: assetWidth,
                height: replaceAsset.attribute.height,
                assetHeight: height,
                assetWidth: width,
                cropXFrom: 0,
                cropYFrom: 0,
                cropXTo: 1,
                cropYTo: 1,
                filters: {
                    brightness: 0,
                    saturate: 0,
                    contrast: 0,
                    blur: 0,
                    sharpen: 0,
                    hue: 0,
                    strong: 0,
                    'gamma-r': 1,
                    'gamma-b': 1,
                    'gamma-g': 1,
                },
            });
        }
    }

    public static addExtraAnchorAssets(
        message: IMessageItem,
        extraParams: { canvasWidth: number; canvasHeight: number; insertIndex: number },
    ): IImageAsset[] {
        const extraAssets: IImageAsset[] = [];
        const logoAsset = this.addLogoAsset(message, extraParams);
        logoAsset && extraAssets.push(logoAsset);
        const qrcodeAsset = this.addQrcodeAsset(message, {
            ...extraParams,
            insertIndex: extraParams.insertIndex + 1,
        });
        qrcodeAsset && extraAssets.push(qrcodeAsset);
        return extraAssets;
    }
    /**
     * @description: 画布中没有logo元素的话，添加logo素材
     * @param {IMessageItem} message
     * @return {*}
     */
    private static addLogoAsset(
        message: IMessageItem,
        extraParams: { canvasWidth: number; canvasHeight: number; insertIndex: number },
    ) {
        if (!message.logo?.length || this.imageReplaceStashIndex.logoIndex != 0) return;
        const imageInfo = message.logo[0];
        const { id, path, width, height } = imageInfo;
        const aspectRatio = Number(width) / Number(height);
        let asset: IImageAsset = cloneDeep(assetTemplateConfig.image);
        const ratioWidth = this.logoAssetConfig.height * aspectRatio;
        asset = {
            ...asset,
            meta: {
                ...asset.meta,
                index: extraParams.insertIndex,
                className: 'logo' + extraParams.insertIndex,
                rt_tag_type: IAiDesignTemplateImgTagTypeEnum.LOGO,
                type: 'pic',
            },
            attribute: {
                ...asset.attribute,
                resId: id,
                picUrl: path,
                width: Math.round(ratioWidth),
                height: this.logoAssetConfig.height,
                assetHeight: height,
                assetWidth: width,
                cropXTo: 1,
                cropYTo: 1,
                filters: {
                    brightness: 0,
                    saturate: 0,
                    contrast: 0,
                    blur: 0,
                    sharpen: 0,
                    hue: 0,
                    strong: 0,
                    'gamma-r': 1,
                    'gamma-b': 1,
                    'gamma-g': 1,
                },
            },
            transform: {
                ...asset.transform,
                posX: this.logoAssetConfig.left,
                posY: this.logoAssetConfig.top,
            },
        };
        return asset;
    }
    private static addQrcodeAsset(
        message: IMessageItem,
        extraParams: { canvasWidth: number; canvasHeight: number; insertIndex: number },
    ) {
        if (!message.qrcode?.length || this.imageReplaceStashIndex.qrcodeIndex != 0) return;
        const imageInfo = message.qrcode[0];
        const { id, path, width, height } = imageInfo;
        const aspectRatio = Number(width) / Number(height);
        let asset: IImageAsset = cloneDeep(assetTemplateConfig.image);
        const ratioWidth = this.qrcodeAssetConfig.height * aspectRatio;
        const { canvasWidth, canvasHeight } = extraParams;
        asset = {
            ...asset,
            meta: {
                ...asset.meta,
                index: extraParams.insertIndex,
                className: 'logo' + extraParams.insertIndex,
                rt_tag_type: IAiDesignTemplateImgTagTypeEnum.QRCODE,
                type: 'pic',
            },
            attribute: {
                ...asset.attribute,
                resId: id,
                picUrl: path,
                width: Math.round(ratioWidth),
                height: this.qrcodeAssetConfig.height,
                assetHeight: height,
                assetWidth: width,
                cropXTo: 1,
                cropYTo: 1,
                filters: {
                    brightness: 0,
                    saturate: 0,
                    contrast: 0,
                    blur: 0,
                    sharpen: 0,
                    hue: 0,
                    strong: 0,
                    'gamma-r': 1,
                    'gamma-b': 1,
                    'gamma-g': 1,
                },
            },
            transform: {
                ...asset.transform,
                posX: canvasWidth - ratioWidth - this.qrcodeAssetConfig.right,
                posY: canvasHeight - this.qrcodeAssetConfig.height - this.qrcodeAssetConfig.bottom,
            },
        };
        return asset;
    }
    public static checkHasImageContainer() {
        return this.hasImageContainer;
    }
    public static resetImageReplaceStash() {
        this.imageReplaceStashIndex = {
            imgContainerIndex: 0,
            logoIndex: 0,
            qrcodeIndex: 0,
        };
        //只有一个就是true
        // this.hasImageContainer = false;
    }
}
