import { TgsTypes } from '@tgs/types';
import { TemplateFormat } from './TemplateFormat';
import { ChatApi } from '../api/chatApi';
import md5 from 'js-md5';
import { cloneDeep } from '../utils/chatUtils';

interface SaveUserTemplResponse {
    stat: number;
    info: {
        id: string;
    };
}

export class TemplateLogic {
    private static chatApi = new ChatApi();

    // 保存模板
    public static saveUserTempl = async (params: {
        picId: string;
        upicId: string;
        aiproduce_id?: number;
        work: TgsTypes.IWork;
        canvas: TgsTypes.ICanvas;
        pageAttr: TgsTypes.IPageAttr;
        info: TgsTypes.ITemplateInfo;
        aiproduce_type?: number;
        ai_ppt_id?: string;
    }) => {
        // 这里要一次把多个模板全部保存
        // 有外部传入以外部传入为准
        const {
            picId,
            upicId,
            work,
            canvas,
            pageAttr,
            aiproduce_id = '',
            info = {},
            aiproduce_type = 1,
            ai_ppt_id = '',
        } = params;
        const doc = TemplateFormat.saveFormat(
            cloneDeep({ work, canvas, pageAttr }),
            ai_ppt_id ? 'ppt' : '',
        );
        const template = {
            doc,
            picId,
            width: canvas.width,
            height: canvas.height,
            lastTemplId: picId,
            last_templ_id: picId,
            ...info,
            title: canvas.title,
            id: upicId ?? '',
            aiproduce_id: aiproduce_id,
            ai_ppt_id
        };
        const res = await TemplateLogic.chatApi.saveUserTempl(
            { startTime: new Date().getTime(), aiproduce_type },
            template,
        ) as SaveUserTemplResponse;
        if (res.stat == 1) {
            return res.info.id;
        } else {
            return '';
        }
    };
    public static pageHash(picId: string, userId: string) {
        return md5(`${picId}-${userId}-${new Date().getTime()}-${Math.round(10000000 * Math.random())}`);
    }
}
