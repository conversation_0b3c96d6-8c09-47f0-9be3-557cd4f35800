import {
  IAiDesignTemplateTagTypeEnum,
  IAsset,
  IRichText,
  ITextAsset,
  TgsTypes,
} from '@tgs/types';
import { measureText } from '@tgs/canvas';
import { ImageLogic } from './ImageLogic';
import { getUrlParam, UploadAndReplaceConfig } from '../utils/chatUtils';
import { IAiDesignTemplateColorTagTypeEnum } from '@tgs/types/src/aiDesign/template';
import { hexToRgb } from '../utils/chatUtils';
import { SvgLogic } from './SvgLogic';
import { IMessageItem } from '../hooks/useTemplateSave';


export class TextLogic {
  private static minestFontSize = 32;
  private static minestLineHeight = 10;
  private static minestLetterSpacing = 10;
  private static lastLineCharNum = 2;
  private static textReplaceIndexStash = {
      thdTitleIndex: 0,
      mainTextIndex: 0,
      titleIndex: 0,
      introductionIndex: 0,
      subHeadIndex: 0,
  };
  private static textReplaceIndexStashList: (typeof TextLogic.textReplaceIndexStash)[] = [];
  /**
   * @description: 获取文本格式化数据
   * @param {TgsTypes} textAsset
   * @return {*}
   */
  public static getTextFormatData(textAsset: TgsTypes.ITextAsset): TgsTypes.IRichText[] {
      if (!textAsset || textAsset.meta.type !== 'text' || !textAsset.attribute.text) return [];
      if (typeof textAsset.attribute.text[0] === 'string') return [];
      const textInfo = textAsset.attribute.text as TgsTypes.IRichText[];
      const textSplitData: TgsTypes.IRichText[] = [];
      textInfo.forEach((item: TgsTypes.IRichText) => {
          const text = item.text;
          if (!text) return;
          const itemData = [];
          for (let i = 0; i < text.length; i++) {
              const itemText = text[i];
              itemData.push({
                  ...item,
                  text: itemText,
              });
          }
          textSplitData.push(...itemData);
      });
      return textSplitData;
  }
  /**
   * @description: 根据ai生成内容替换文本
   * @param {IMessage} message
   * @param {TgsTypes} work
   * @return {*}
   */
  public static replaceAssetContent = (
      message: IMessageItem,
      work: TgsTypes.IWork,
      canvas: TgsTypes.ICanvas,
      info: TgsTypes.ITemplateInfo,
      textTagGroupAsset?: Record<string, Record<string, IAsset<'text'>[][]>>,
  ) => {
      if (!textTagGroupAsset) {
          // 根据文本标记类型分组
          textTagGroupAsset = {
              ['main_head']: {
                  0: [],
              },
              ['sub_head']: {
                  0: [],
              },
              ['third_head']: {
                  0: [],
              },
              ['time']: {
                  0: [],
              },
              ['introduction']: {
                  0: [],
              },
              ['body']: {
                  0: [],
              },
              ['other']: {
                  0: [],
              },
          };
          work.pages.forEach((page, pageIndex) => {
              page.assets.forEach((asset) => {
                  if (asset.meta.type != 'text') return;
                  const curAsset = asset as IAsset<'text'>;
                  if (asset.meta.rt_tag_type && textTagGroupAsset && textTagGroupAsset[curAsset.meta.rt_tag_type]) {
                      const curPageTagGroup = textTagGroupAsset[curAsset.meta.rt_tag_type][pageIndex];
                      //@ts-ignore
                      if (curAsset.meta.rt_tag_type_group) {
                          // 0_1表示 第1组的第一个文本框，新年快
                          //@ts-ignore
                          const tagGroupArr = curAsset.meta.rt_tag_type_group.split('_');

                          const [groupIndex, tagIndex] = tagGroupArr;
                          if (!curPageTagGroup[groupIndex]) {
                              curPageTagGroup[groupIndex] = [];
                          }
                          //@ts-ignore
                          curAsset.meta.tagIndex = Number(tagIndex);
                          curPageTagGroup[groupIndex].push(curAsset);
                      } else {
                          curPageTagGroup.push([curAsset]);
                      }
                  }
              });
          });
      }
      let hasImageContaienr = false;
      const aiTemplateType = getUrlParam(window.location.href, 'ai_template_type');
      const needImageReplace = UploadAndReplaceConfig.supportTemplateType.includes(aiTemplateType);
      this.textReplaceIndexStashList = [];
      this.combandMainTextAssetByGroup(textTagGroupAsset);
      work.pages.forEach((page, pageIndex) => {
          let minTextIndex = Infinity;
          this.replaceAssetByTagGroup(message, textTagGroupAsset, pageIndex);
          page.assets.forEach((asset) => {
              // console.log(asset.meta.type)
              switch (asset.meta.type) {
                  case 'text':
                      // 文字图层提升两位，用来插入二维码和logo
                      // minTextIndex = Math.min(minTextIndex, asset.meta.index ?? Infinity);
                      // asset.meta.index && (asset.meta.index += 2);
                      // this.replaceTextContent(message, asset as TgsTypes.ITextAsset,textTagGroupAsset,pageIndex );
                      this.replaceTextAssetColor(asset as ITextAsset, message);
                      break;
                  case 'image':
                  case 'pic':
                  case 'background':
                  case 'qrcode':
                      if (!needImageReplace) return;
                      ImageLogic.replaceImageContent(message, asset as TgsTypes.IImageAsset);
                      break;
                  case 'SVG':
                      SvgLogic.replaceSvgColor(asset as IAsset<'SVG'>, message);
                      break;
              }
          });
          page.assets = page.assets.filter((asset) => {
              return asset.meta.deleted != 1;
          });
          this.textReplaceIndexStashList.push({ ...this.textReplaceIndexStash });
          if (needImageReplace) {
              const extraAssets = ImageLogic.addExtraAnchorAssets(message, {
                  canvasWidth: canvas.width,
                  canvasHeight: canvas.height,
                  insertIndex: page.assets.length,
              });
              page.assets.push(...extraAssets);
              hasImageContaienr = ImageLogic.checkHasImageContainer();
              ImageLogic.resetImageReplaceStash();
          }
          this.textReplaceIndexStash = {
              thdTitleIndex: 0,
              mainTextIndex: 0,
              titleIndex: 0,
              introductionIndex: 0,
              subHeadIndex: 0,
          };
          this.sortAssets(page);
      });
      info['hasImageContaienr'] = hasImageContaienr;
  };
    /**
     * @description: 元素排序
     * @param {TgsTypes} page
     * @return {*}
     */
    public static sortAssets(page: TgsTypes.IPage) {
        page.assets.sort((asset1, asset2) => {
            return (asset1.meta.index ?? 0) - (asset2.meta.index ?? 0);
        });
    }
  public static replaceTextAssetColor = (asset: TgsTypes.ITextAsset, message: IMessageItem) => {
      if (!asset || asset.meta.type != 'text') return;
      let color;
      switch (asset.meta.rt_color_tag_type) {
          case IAiDesignTemplateColorTagTypeEnum.SUBJECT_COLOR:
              if (message.subjectColor) {
                  const rgbColor = hexToRgb(message.subjectColor);
                  rgbColor && (color = rgbColor);
              }
              break;
          case IAiDesignTemplateColorTagTypeEnum.AUXILIARY_COLOR:
              if (message.auxiliaryColor) {
                  const rgbColor = hexToRgb(message.auxiliaryColor);
                  rgbColor && (color = rgbColor);
              }
              break
      }

      if (!color) return;
      if (this.checkIsNormalText(asset)) {
          asset.attribute.color = {
              r: color.r,
              g: color.g,
              b: color.b,
              a: color.a,
          };
      } else {
          asset.attribute.text.forEach((item: IRichText) => {
              item.color = {
                  r: color.r,
                  g: color.g,
                  b: color.b,
                  a: color.a,
              };
          });
      }
  };
  public static combandMainTextAssetByGroup = (
      textTagGroupAsset: Record<string, Record<string, IAsset<'text'>[][]>>,
  ) => {
      // 除了主标题外，其他文本框组合并成一个大的文本框
      Object.keys(textTagGroupAsset).forEach((tagGroupKey) => {
          if (tagGroupKey == 'main_head') return;
          const mainTextAssetGroupPages = textTagGroupAsset[tagGroupKey];
          if (!mainTextAssetGroupPages) return;

          Object.keys(mainTextAssetGroupPages).forEach((pageIndex) => {
              const mainTextAssetGroup = mainTextAssetGroupPages[pageIndex];
              if (!mainTextAssetGroup) return;
              mainTextAssetGroup.forEach((mainTextAssets) => {
                  // 一个文本框组只有一个文本框时，不需要合并
                  if (mainTextAssets.length <= 1) return;
                  let minLeft = Infinity,
                      minTop = Infinity,
                      maxRight = -Infinity,
                      maxBottom = -Infinity;
                  mainTextAssets.forEach((mainTextAsset) => {
                      const { attribute, transform } = mainTextAsset;
                      const { width, height } = attribute;
                      const { posX, posY } = transform;
                      minLeft = Math.min(minLeft, posX);
                      minTop = Math.min(minTop, posY);
                      maxRight = Math.max(maxRight, posX + width);
                      maxBottom = Math.max(maxBottom, posY + height);
                  });
                  mainTextAssets.forEach((mainTextAsset, assetIndex) => {
                      if (assetIndex == 0) {
                          mainTextAsset.transform.posX = minLeft;
                          mainTextAsset.transform.posY = minTop;
                          mainTextAsset.attribute.width = maxRight - minLeft;
                          mainTextAsset.attribute.height = maxBottom - minTop;
                      } else {
                          mainTextAsset.meta.deleted = 1;
                      }
                  });
                  // mainTextAssetGroup[groupIndex] = mainTextAssets.filter((asset) => asset.meta.deleted != 1);
              });
          });
      });
  };
  // 根据标记分组去替换元素内容
  public static replaceAssetByTagGroup = (
      message: IMessageItem,
      textTagGroupAsset: Record<string, Record<string, IAsset<'text'>[][]>>,
      pageIndex: number,
  ) => {
      const { title, sed_title, thd_title, main_text, time, introduction } = message;
      Object.keys(textTagGroupAsset).forEach((tagGroupKey) => {
          const tagAssetsGroups = textTagGroupAsset[tagGroupKey][pageIndex];
          if (!tagAssetsGroups || !tagAssetsGroups.length) return;
          let groupAssetTextNum = 0;
          tagAssetsGroups.forEach((tagAssetsGroup) => {
              tagAssetsGroup.forEach((asset) => {
                  groupAssetTextNum += this.getTextAssetLength(asset as TgsTypes.ITextAsset);
              });
          });
          tagAssetsGroups.forEach((tagAssetsGroup) => {
              let curTextIndex = 0;
              tagAssetsGroup
                  .sort((asset1, asset2) => {
                      //@ts-ignore
                      return asset1.meta.tagIndex - asset2.meta.tagIndex;
                  })
                  .forEach((asset, assetIndex) => {
                      let replaceText = '';
                      let messageText = '';
                      let reNewLineNum = 0;
                      const originTextLength = this.getTextAssetLength(asset as TgsTypes.ITextAsset);
                      switch (tagGroupKey) {
                          case 'main_head':
                              messageText = title?.[this.textReplaceIndexStash.titleIndex] ?? '';
                              // 一组替换完毕
                              if (assetIndex == tagAssetsGroup.length - 1) {
                                  this.textReplaceIndexStash.titleIndex++;
                              }
                              break;
                          case 'sub_head':
                              messageText = sed_title?.[this.textReplaceIndexStash.subHeadIndex] ?? '';
                              if (assetIndex == tagAssetsGroup.length - 1) {
                                  this.textReplaceIndexStash.subHeadIndex++;
                              }
                              break;
                          case 'third_head':
                              messageText = thd_title?.[this.textReplaceIndexStash.thdTitleIndex] ?? '';
                              if (assetIndex == tagAssetsGroup.length - 1) {
                                  this.textReplaceIndexStash.thdTitleIndex++;
                              }
                              break;
                          case 'time':
                              break;
                          case 'introduction':
                              messageText = introduction?.[this.textReplaceIndexStash.introductionIndex] ?? '';
                              if (assetIndex == tagAssetsGroup.length - 1) {
                                  this.textReplaceIndexStash.introductionIndex++;
                              }
                              break;
                          case 'body':
                              messageText = main_text?.[this.textReplaceIndexStash.mainTextIndex] ?? '';
                              replaceText = messageText.replace(/\n/g, '').replace(/[。！!\.]/g, ($1) => {
                                  reNewLineNum++;
                                  return $1 + '\n';
                              });
                              if (assetIndex == tagAssetsGroup.length - 1) {
                                  this.textReplaceIndexStash.mainTextIndex++;
                              }
                              break;
                          default:
                              break;
                      }
                      messageText = messageText.trimStart()?.replace(/\n/g, '');
                      if (!messageText) return;
                      if (messageText.endsWith('\n') || messageText.endsWith('\r\n')) {
                          // 去掉字符串末尾的换行符
                          messageText = messageText.slice(0, -1);
                      }
                      let curTextSliceNum = Math.round((originTextLength / groupAssetTextNum) * messageText.length);
                      if (assetIndex == tagAssetsGroup.length - 1) {
                          curTextSliceNum = messageText.length - curTextIndex;
                      }
                      const perAssetNum = Math.max(1, curTextSliceNum);

                      replaceText = messageText.substr(curTextIndex, perAssetNum);
                      curTextIndex += perAssetNum;
                      this.adaptaTextAssetSize(asset);
                      this.replaceTextContentByType(asset as TgsTypes.ITextAsset, replaceText ?? ' ');
                      replaceText &&
                          this.adaptarTextAsset(asset as TgsTypes.ITextAsset, replaceText.length, reNewLineNum);
                  });
          });
      });
  };
  // 获取文本元素文字数量
  public static getTextAssetLength = (asset: TgsTypes.ITextAsset) => {
      if (!asset || asset.meta.type != 'text') return 0;
      if (this.checkIsNormalText(asset)) {
          if (Array.isArray(asset.attribute.text)) {
              return (asset.attribute.text[0] as string).length;
          }
          return (asset.attribute.text as string).length;
      } else {
          return (asset.attribute.text as TgsTypes.IRichText[]).reduce((prev, curr) => {
              return prev + (curr?.text?.length || 0);
          }, 0);
      }
  };
  public static replaceTextContent = (
      message: IMessageItem,
      asset: TgsTypes.ITextAsset,
  ) => {
      const { title, sed_title, thd_title, main_text, introduction } = message;
      if (asset.meta.type != 'text') return;
      const textType = asset.meta.rt_tag_type;
      let replaceText = '';
      let reNewLineNum = 0;
      switch (textType) {
          // 主标题，主标题文本框不只有一个
          case IAiDesignTemplateTagTypeEnum.MAIN_HEAD:
              const titleText = title?.[this.textReplaceIndexStash.titleIndex++] ?? '';
              replaceText = titleText?.replace(/\n/g, '').replace(/[，,]/g, '\n');
              if (!replaceText) return;
              if (replaceText.endsWith('\n') || replaceText.endsWith('\r\n')) {
                  // 去掉字符串末尾的换行符
                  replaceText = replaceText.slice(0, -1);
              }
              break;
          // 副标题
          case IAiDesignTemplateTagTypeEnum.SUB_HEAD:
              const sedTitleText = sed_title?.[this.textReplaceIndexStash.subHeadIndex++] ?? '';
              replaceText = sedTitleText?.replace(/\n/g, '').replace(/[，,]/g, ' ');
              if (replaceText.endsWith('\n') || replaceText.endsWith('\r\n')) {
                  // 去掉字符串末尾的换行符
                  replaceText = replaceText.slice(0, -1);
              }
              break;
          case IAiDesignTemplateTagTypeEnum.THIRD_HEAD:
              // 标记了三级标题,但是没有三级标题内容时,不替换
              replaceText = thd_title?.[this.textReplaceIndexStash.thdTitleIndex++];
              if (!replaceText) return;
              break;
          case IAiDesignTemplateTagTypeEnum.INTRODUCTION:
              replaceText = introduction?.[this.textReplaceIndexStash.introductionIndex++];
              if (!replaceText) return;
              break;
          // 正文
          case 'body':
              // 标记了正文，但是没有正文内容时，不替换
              let mainText = main_text?.[this.textReplaceIndexStash.mainTextIndex++];
              if (mainText == undefined || mainText == null) return;
              // if (
              //     templateTagBodyNum &&
              //     this.textReplaceIndexStash.mainTextIndex == templateTagBodyNum &&
              //     mainTextLength > templateTagBodyNum
              // ) {
              //     mainText = mainText + main_text?.slice(this.textReplaceIndexStash.mainTextIndex).join('');
              // }
              if (!mainText) mainText = '';
              replaceText = mainText.replace(/\n/g, '').replace(/[。！!\.]/g, ($1) => {
                  reNewLineNum++;
                  return $1 + '\n';
              });
              if (replaceText.endsWith('\n') || replaceText.endsWith('\r\n')) {
                  // 去掉字符串末尾的换行符
                  replaceText = replaceText.slice(0, -1);
              }
              break;
          default:
              break;
      }
      replaceText = replaceText.trimStart();
      this.adaptaTextAssetSize(asset);
      this.replaceTextContentByType(asset as TgsTypes.ITextAsset, replaceText ?? ' ');

      replaceText && this.adaptarTextAsset(asset as TgsTypes.ITextAsset, replaceText.length, reNewLineNum);
  };
  // 调整原文本框尺寸
  public static adaptaTextAssetSize = (asset: TgsTypes.ITextAsset) => {
      const { width: originTextRenderWidth, height: originTextRenderHeight } = measureText(asset);

      const isVrMode = asset.attribute.writingMode == 'vertical-rl';
      const { width, height } = asset.attribute;
      if (!isVrMode) {
          switch (asset.attribute.textAlign) {
              case 'center':
                  // asset.attribute.width = (width + originTextRenderWidth) / 2;
                  // asset.transform.posX += (width - asset.attribute.width) / 2;
                  asset.attribute.width = originTextRenderWidth;
                  asset.transform.posX += (width - asset.attribute.width) / 2;
                  break;
              case 'left':
                  asset.attribute.width = originTextRenderWidth;
                  break;
              case 'right':
                  asset.attribute.width = originTextRenderWidth;
                  asset.transform.posX += width - asset.attribute.width;
          }
      } else {
          switch (asset.attribute.textAlign) {
              case 'center':
                  asset.attribute.height = (height + originTextRenderHeight) / 2;
                  asset.transform.posY += (height - asset.attribute.height) / 2;
                  break;
              case 'left':
                  asset.attribute.height = originTextRenderHeight;
                  break;
              case 'right':
                  asset.attribute.height = originTextRenderHeight;
                  asset.transform.posX += height - asset.attribute.height;
          }
      }
  };
  /**
   * @description: 判断是否是普通文本
   * @param {TgsTypes} textAsset
   * @return {*}
   */
  public static checkIsNormalText = (textAsset: TgsTypes.ITextAsset) => {
      return textAsset.meta.v != 3 && typeof textAsset.attribute.text[0] == 'string';
  };
  private static checkIsNoNewLineText = (textAsset: TgsTypes.ITextAsset) => {
      const noReNewLineType = [IAiDesignTemplateTagTypeEnum.MAIN_HEAD, IAiDesignTemplateTagTypeEnum.SUB_HEAD];
      return noReNewLineType.includes(textAsset.meta.rt_tag_type);
  };
  public static replaceTextContentByType = (asset: TgsTypes.ITextAsset, text: string) => {
      if (!text && !asset.meta.rt_tag_type) return false;
      const isNormalText = this.checkIsNormalText(asset);
      let sliceIndex = 0;
      let textOverFlow = false;
      if (isNormalText) {
          const originTextLength = asset.attribute.text.length;
          asset.attribute.text = [text];
          textOverFlow = text.length > originTextLength;
      } else {
          const textInfo = asset.attribute.text as TgsTypes.IRichText[];
          if (!text) {
              asset.attribute.text = [
                  {
                      ...textInfo[0],
                      text: '',
                  },
              ];
          } else {
              let totalOrginTextLength = 0;
              textInfo.forEach((item: TgsTypes.IRichText, index: number) => {
                  const originTextLength = item.text?.length ?? 0;
                  totalOrginTextLength += originTextLength;
                  item.text = text.slice(sliceIndex, sliceIndex + originTextLength);
                  sliceIndex += originTextLength;
                  if (index == textInfo.length - 1) {
                      item.text += text.slice(sliceIndex);
                  }
                  item.fontSize = asset.attribute.fontSize || 120;
              });
              textOverFlow = text.length > totalOrginTextLength;
          }
      }
      return textOverFlow;
  };
  public static async adaptarTextAsset(asset: TgsTypes.ITextAsset, textLength: number, reNewLineNum: number = 0) {
      const { width: renderWidth, height: renderHeight, lineInfo } = measureText(asset);

      if (renderWidth == 0 || renderHeight == 0 || lineInfo.length == 0) return;
      const attribute = asset.attribute as any;
      if (!attribute['originRenderWidth'] && !attribute['originRenderHeight']) {
          attribute['originRenderWidth'] = renderWidth;
          attribute['originRenderHeight'] = renderHeight;
      }
      const firstLineNum = lineInfo[0].length;
      const lastLineCharNum = lineInfo[lineInfo.length - 1].length;
      const isOverflow = Math.abs(firstLineNum - lastLineCharNum) >= this.lastLineCharNum;

      const isVrMode = asset.attribute.writingMode == 'vertical-rl';

      let { width, height } = asset.attribute;

      const isNoRenewLineText = this.checkIsNoNewLineText(asset);
      let inHeightBoundary = renderHeight <= height && !isVrMode;
      let inWidthBoundary = renderWidth <= width && isVrMode;
      if ((inWidthBoundary || inHeightBoundary) && !isOverflow) {2
          if (!isVrMode && asset.attribute.height - renderHeight > asset.attribute.fontSize) {
              asset.transform.posY +=
                  (Math.min(attribute['originRenderHeight'], attribute.height) - renderHeight) / 2;
              asset.attribute.height = renderHeight;
          } else if (isVrMode) {
              asset.transform.posX += (Math.min(attribute['originRenderWidth'], attribute.width) - renderWidth) / 2;
              asset.attribute.width = renderWidth;
          }
          return;
      }

      const overflowSize = Math.max(renderWidth - width, renderHeight - height);
      if (overflowSize < 0 && !isOverflow) return;
      const minusStep = Math.max(Math.min(Math.ceil(overflowSize / textLength), 1), 1);
      const isNormalText = this.checkIsNormalText(asset);
      let isMinestFontsize = false,
          isMinestLineHeight = false,
          isMinestLetterSpacing = false;
      if (isNormalText) {
          if (reNewLineNum > 0 && isMinestFontsize && isMinestLineHeight) {
              asset.attribute.text = [(asset.attribute.text[0] as string).replace('\n', '')];
              this.adaptarTextAsset(asset, textLength, reNewLineNum - 1);
              return;
          }
          const letterSpacingScale = asset.attribute.letterSpacing / asset.attribute.fontSize;

          asset.attribute.fontSize = Math.max(this.minestFontSize, asset.attribute.fontSize - minusStep);
          isMinestFontsize = asset.attribute.fontSize <= this.minestFontSize;
          if (asset.attribute.letterSpacing > this.minestLetterSpacing) {
              asset.attribute.letterSpacing = Math.max(
                  asset.attribute.fontSize * letterSpacingScale,
                  this.minestLetterSpacing,
              );
          }

          asset.attribute.lineHeight = Math.max(this.minestLineHeight, asset.attribute.lineHeight - minusStep);
          isMinestLineHeight = asset.attribute.lineHeight <= this.minestLineHeight;
      } else {
          const textInfo = asset.attribute.text as TgsTypes.IRichText[];
          textInfo.forEach((item: TgsTypes.IRichText) => {
              if (reNewLineNum > 0 && item.text?.includes('\n') && isMinestFontsize && isMinestLineHeight) {
                  item.text = item.text.replace('\n', '');
                  this.adaptarTextAsset(asset, textLength, reNewLineNum - 1);
                  return;
              }
              const letterSpacingScale = asset.attribute.letterSpacing / asset.attribute.fontSize;
              item.fontSize = Math.max(this.minestFontSize, (item.fontSize ?? 0) - minusStep);
              isMinestFontsize = item.fontSize <= this.minestFontSize;
              if (item.letterSpacing && item.letterSpacing > this.minestLetterSpacing) {
                  item.letterSpacing = asset.attribute.letterSpacing = Math.max(
                      asset.attribute.fontSize * letterSpacingScale,
                      this.minestLetterSpacing,
                  );
              }

              asset.attribute.lineHeight = Math.max(this.minestLineHeight, asset.attribute.lineHeight - minusStep);
              isMinestLineHeight = asset.attribute.lineHeight <= this.minestLineHeight;
          });
      }

      if (isMinestFontsize && isMinestLineHeight && reNewLineNum == 0) {
          return;
      }
      this.adaptarTextAsset(asset, textLength);
  }
  /**
   * @description: 获取文本元素的纯文本
   * @param {TgsTypes} asset
   * @return {*}
   */
  public static getPlainText(asset: TgsTypes.ITextAsset) {
      if (!asset || asset.meta.type != 'text') return '';
      if (typeof asset.attribute.text === 'string') return asset.attribute.text;
      if (Array.isArray(asset.attribute.text) && typeof asset.attribute.text[0] === 'string')
          return asset.attribute.text.join('');
      return (asset.attribute.text as TgsTypes.IRichText[]).map((item) => item.text).join('');
  }

  /**
   * @description: 获取替换文本次数
   * @return {*}
   */
  public static getReplaceContentCount() {
      return this.textReplaceIndexStashList[0];
  }
}
type IReplaceTextSubItem = { sub_head: string; body: string };

interface IReplaceTextItem {
  mainHead: string[];
  subItem: IReplaceTextSubItem[];
}
export interface IBodyContentItem {
  title: string;
  data: string;
}
export class PptTextLogic {
  static stashChapterPageMap = new Map<
      string,
      {
          mainHead: string[];
          subItem: IReplaceTextSubItem[];
          contentPage: IReplaceTextItem[];
      }[]
  >();
}
