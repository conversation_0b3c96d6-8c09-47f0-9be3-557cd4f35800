import { IAsset, IColor } from '@tgs/types';
import { IAiDesignTemplateColorTagTypeEnum } from '@tgs/types/src/aiDesign/template';
import { hexOpacityToRgba, hexToRgb } from '../utils/chatUtils';
import { IMessageItem } from '../hooks/useTemplateSave';

export class SvgLogic {
    public static replaceSvgColor(asset: IAsset<'SVG'>, messageItem: IMessageItem): void {
        if (!asset) return;
        if (!asset.attribute.colors) return;
        let color;

        switch (asset.meta.rt_color_tag_type) {
            case IAiDesignTemplateColorTagTypeEnum.SUBJECT_COLOR:
                if (messageItem.subjectColor) {
                    let rgbColor = hexToRgb(messageItem.subjectColor);
                    if (asset.attribute.opacity) {
                        rgbColor = hexOpacityToRgba(messageItem.subjectColor, asset.attribute.opacity);
                    }
                    rgbColor && (color = rgbColor);
                }
                break;
            case IAiDesignTemplateColorTagTypeEnum.AUXILIARY_COLOR:
                if (messageItem.auxiliaryColor) {
                    const rgbColor = hexToRgb(messageItem.auxiliaryColor);
                    rgbColor && (color = rgbColor);
                }
        }
        if (!color) return;

        const firstKey = Object.keys(asset.attribute.colors)[0];
        if(!firstKey) return;
        asset.attribute.colors[firstKey] = color as IColor;
    }
}
