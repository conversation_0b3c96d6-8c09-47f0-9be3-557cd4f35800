import { useState, useEffect, useCallback } from 'react';
import { IChatMessage, IChatSession } from '../types';

interface UseChatHistoryOptions {
    initialModel?: string;
    sessionId?: string;
    sessionList?: IChatSession[];
    getHistoryMessages?: (sessionId: string) => Promise<{ list: IChatMessage[] } | null>;
}

/**
 * Custom hook for managing current chat history
 */
export const useChatHistory = (options: UseChatHistoryOptions = {}) => {
  const {
    sessionId,
    getHistoryMessages,
    sessionList
  } = options;

  // State for sessions and current session
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [currentSession, setCurrentSession] = useState<IChatSession | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Load history messages from API when sessionId is provided
  const loadHistoryFromApi = useCallback(async (sessionIdToLoad: string) => {
    setIsLoading(true);
    if (!getHistoryMessages) {
      console.warn('getHistoryMessages function not provided');
      return;
    }
    const sessionInfo = sessionList?.find(session => session.session_id === sessionIdToLoad);
    try {
      const response = await getHistoryMessages(sessionIdToLoad);
      if (response && response.list) {
        // Convert API messages to IChatMessage format
        const apiMessages: IChatMessage[] = response.list
        if( apiMessages.length ){
          // Create a session with the loaded messages
          const session: IChatSession = {
            session_id: sessionIdToLoad || '',
            title: sessionInfo?.title || '',
            messages: apiMessages,
            title_type: sessionInfo?.title_type || 'auto',
            created: new Date(sessionInfo?.created || Date.now()).toISOString(),
            updated: new Date(sessionInfo?.updated || Date.now()).toISOString(),
            current_message_id: sessionInfo?.current_message_id || ''
          };
          setCurrentSession(session)
        }else {
          setCurrentSession(sessionInfo || null)
        }
        setCurrentSessionId(sessionIdToLoad);
      }
    } catch (error) {
      console.error('Failed to load history from API:', error);
    } finally {
      setIsLoading(false);
    }
  }, [getHistoryMessages, sessionList]);

  // Load sessions from API on mount
  useEffect(() => {
      if(sessionId) {
          loadHistoryFromApi(sessionId || '');
      }else {
          setCurrentSession(null)
      }
  }, [sessionId]);

  /**
   * Add a message to the current session
   * @param message Message to add
   */
  const addMessage = useCallback((message: IChatMessage): void => {
    setCurrentSession(prevSession => {
      if (!prevSession) {
        // 如果是新会话，尝试从 sessionList 中获取
        const newSession = sessionList?.find(session => session.session_id === sessionId);
        if (newSession) {
          return {
            ...newSession,
            messages: [...newSession.messages, message],
          };
        }
        return null;
      }
      return {
        ...prevSession,
        messages: [...prevSession.messages || [], message],
      }
    });
  }, [sessionId, sessionList]);

  /**
   * Update a message in the current session
   * @param messageId ID of the message to update
   * @param updates Updates to apply to the message
   */
  const updateMessage = useCallback((messageId: string, updates: Partial<IChatMessage>): void => {
    setCurrentSession(prevSession => {
        if (!prevSession) return null;

        const updatedMessages = prevSession.messages.map(message => {
            if (message.message_id === messageId) {
                return {
                    ...message,
                    ...updates,
                    updated: new Date().toISOString(),
                };
            }
            return message;
        });

        return {
            ...prevSession,
            messages: updatedMessages,
            updated: new Date().toISOString(),
        };
    });
  }, [currentSession]);

  const deleteMessage =(messageId: string): void => {
    setCurrentSession(prevSession => {
      if (!prevSession) return null;
      return {
        ...prevSession,
        messages: prevSession.messages.filter(message => message.message_id !== messageId),
      };
    })
  }

  return {
    currentSession,
    currentSessionId,
    setCurrentSessionId,
    addMessage,
    updateMessage,
    deleteMessage,
    loadHistoryFromApi,
    isLoading,
    setCurrentSession,
  };
};
