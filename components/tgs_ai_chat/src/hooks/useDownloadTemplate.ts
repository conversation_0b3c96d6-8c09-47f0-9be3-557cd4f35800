// import { <PERSON><PERSON><PERSON><PERSON> } from '../api/chatApi';
// import { UserRequestManager } from '../api/userRequest';
// import { UserLogic } from '../logic/User';
// import { useUserSelector } from '../context/userContext';
// import { setUserInfo } from '../context/userContext';
// import { IpsUtils } from '@tgs/utils';
// import { useDebounceFn } from 'ahooks';
// import { message } from 'antd';
// import { useDispatch } from 'react-redux';
// import { useRef } from 'react';

// interface IUseDownloadTemplateProps {
//     onDownloadSuccess: (params: Record<string, string>) => void;
//     onDownloadLimit: () => void;
//     onBindPhoneLimit: () => void;
//     onPhoneCodeLimit: () => void;
//     onTemplDownload: () => void;
//     onBindSuceess: () => void;
//     onDownloadVlidateSuccess: () => void;
// }

// export const useDownloadTemplate = (props: IUseDownloadTemplateProps) => {
//     const { userInfo } = useUserSelector();
//     const {
//         onDownloadSuccess,
//         onDownloadLimit,
//         onBindPhoneLimit,
//         onPhoneCodeLimit,
//         onTemplDownload,
//         onBindSuceess,
//         onDownloadVlidateSuccess,
//     } = props;
//     const dispatch = useDispatch();
//     const chatApi = useRef(new ChatApi()).current;

//     // 下载模板
//     const { run: downloadTempl } = useDebounceFn(
//         async (
//             params: { ticket?: string; rendstr?: string; title?: string } = { ticket: '', rendstr: '', title: '' },
//         ) => {
//             const { ticket, rendstr, title } = params;
//             if (userInfo.bind_phone == 0) {
//                 onBindPhoneLimit?.();
//                 return;
//             }
//             const { picId, upicId, aiproduce_id, ai_ppt_id = '' } = IpsUtils.Url.getUrlParams(window.location.href);
//             if (!upicId || (!aiproduce_id && !ai_ppt_id)) return message.error('请选择您要下载的模板');
//             const res = await chatApi.downloadTempl({
//                 upicId: upicId,
//                 aiproduce_id: Number(aiproduce_id),
//                 ticket,
//                 rendstr,
//                 ai_ppt_id,
//             });
//             if (res.stat == 1) {
//                 onTemplDownload?.();
//                 const jobId = res.job;
//                 checkDownloadFlag({
//                     upicId: upicId,
//                     jobId: jobId,
//                     aiproduce_id: Number(aiproduce_id),
//                     title: title ?? String(Date.now()),
//                     ai_ppt_id,
//                 });
//             } else if (res.code == -1) {
//                 // message.error(res.msg);
//                 onPhoneCodeLimit?.();
//             } else if (res.stat == -100) {
//                 showCaptcha();
//             } else if (res.stat == 0) {
//                 onDownloadLimit?.();
//             } else {
//                 message.error(res.msg);
//             }
//         },
//         { wait: 1000, leading: true },
//     );

//     // 检查状态
//     const checkDownloadFlag = async (params: {
//         upicId: string;
//         jobId: string;
//         title: string;
//         ai_ppt_id?: string;
//         version_id?: number;
//         aiproduce_id?: number;
//     }) => {
//         const res = await chatApi.downloadFlagCheck(params);
//         const { title } = params;
//         if (res.stat == 1 && res.job == 1) {
//             message.success('下载成功');
//             const imgPath = res.path;
//             IpsUtils.Download.downloadImg(imgPath, { title: title });
//             const { picId, upicId, aiproduce_id, ai_ppt_id } = IpsUtils.Url.getUrlParams(window.location.href);
//             onDownloadSuccess?.({
//                 picId: picId,
//                 upicId: upicId,
//                 aiproduce_id: aiproduce_id,
//                 ai_ppt_id: ai_ppt_id,
//             });
//         } else if (res.stat == 1) {
//             setTimeout(() => {
//                 checkDownloadFlag(params);
//             }, 1000);
//         } else if (res.stat == 0) {
//             onDownloadLimit?.();
//         } else {
//             message.error(res.msg);
//         }
//     };

//     // 手机绑定成功
//     const onBindSuccess = async () => {
//         onBindSuceess?.();
//         const res = await UserRequestManager.getUserInfo();
//         dispatch(setUserInfo({ userInfo: res }));
//         downloadTempl();
//     };

//     // 下载验证成功
//     const onDownloadValidateSuccess = async () => {
//         onDownloadVlidateSuccess?.();
//         await UserRequestManager.getTypeBy_CheckEveryDayDownload();
//         downloadTempl();
//     };

//     // 图形验证
//     const showCaptcha = async () => {
//         const { ticket, rendstr } = await UserLogic.showImageCheck();
//         downloadTempl({ ticket: ticket ?? '', rendstr: rendstr ?? '', title: '' });
//     };

//     return {
//         downloadTempl,
//         onBindSuccess,
//         onDownloadValidateSuccess,
//         showCaptcha,
//     };
// };
