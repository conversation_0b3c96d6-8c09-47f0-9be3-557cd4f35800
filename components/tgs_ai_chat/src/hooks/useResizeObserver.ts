import { useEffect, useRef } from "react";

export const useResizeObserver = (ref: React.RefObject<HTMLElement>, callback: ResizeObserverCallback) => {
    const timeoutRef = useRef<NodeJS.Timeout | null>(null);
    const rafRef = useRef<number | null>(null);
    const isResizingRef = useRef(false);
    const observerRef = useRef<ResizeObserver | null>(null);

    useEffect(() => {
        const element = ref.current;
        if (!element) return;

        const handleResize = (entries: ResizeObserverEntry[]) => {
            if (isResizingRef.current) return;

            try {
                isResizingRef.current = true;

                // Cancel any pending resize timeout
                if (timeoutRef.current) {
                    clearTimeout(timeoutRef.current);
                }

                // Cancel any pending animation frame
                if (rafRef.current) {
                    cancelAnimationFrame(rafRef.current);
                }

                // Debounce the resize callback
                timeoutRef.current = setTimeout(() => {
                    rafRef.current = requestAnimationFrame(() => {
                        try {
                            callback(entries, observerRef.current!);
                        } catch (error) {
                            console.error('Error in ResizeObserver callback:', error);
                        } finally {
                            isResizingRef.current = false;
                        }
                    });
                }, 100); // 100ms debounce
            } catch (error) {
                console.error('Error in ResizeObserver setup:', error);
                isResizingRef.current = false;
            }
        };

        observerRef.current = new ResizeObserver(handleResize);
        observerRef.current.observe(element);

        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
            if (rafRef.current) {
                cancelAnimationFrame(rafRef.current);
            }
            if (observerRef.current) {
                observerRef.current.disconnect();
            }
        };
    }, [ref, callback]);
}   