import { <PERSON>t<PERSON><PERSON> } from '../api/chatApi';
import { TemplateLogic } from '../logic/templateLogic';
import { IWork } from '@tgs/types';
import { cloneDeep, getOrigin, joinUrlParams } from '../utils/chatUtils';
import React, { useRef, useState } from 'react';
import { TextLogic } from '../logic/TextLogic';
import { TemplateFormat } from '../logic/TemplateFormat';
import { IUploadAssetItem } from '../logic/ImageLogic';
import { GenerateStatusEnum } from './useChatRenderApi';
import {
    IAiDesignTemplateImgTagTypeEnum,
    IAsset,
    IImageAsset,
    TAssetType,
    IAiDesignTemplateTypeEnum
} from '@tgs/types';

export interface IMessageItem extends Record<string, unknown> {
    id: number;
    title: string[];
    main_text: string[];
    sed_title: string[];
    thd_title: string[];
    time: string;
    introduction: string[];
    logo: IUploadAssetItem[];
    qrcode: IUploadAssetItem[];
    imgContainer: IUploadAssetItem[];
    subjectColor: string;
    auxiliaryColor: string;
    ai_draw_template_info: IAiDrawPreviewInfo;
    draw_template_style?: string;
    releted_temp: {
        picId: string;
        preview: string;
    }[];
    rendered?: boolean;
    aiTemplateType?: IAiDesignTemplateTypeEnum;
    // 替换模版内容数量统计
    replaceContentCount?: IContentCount;
}

export interface IContentCount {
    thdTitleIndex: number;
    mainTextIndex: number;
    titleIndex: number;
    introductionIndex: number;
    subHeadIndex: number;
}

export type IAiDrawPreviewInfo = {
    preview: string;
    width: number;
    height: number;
    status: GenerateStatusEnum;
    ai_produce_draw_id: string;
    resId: string; // 保存后的用户素材id
    title_color: string;
    sed_title_color: string;
};

export const useTemplSave = (sourceFrom: number) => {
    // 跳转触发限制弹窗
    const [showJumpLimitModal, setShowJumpLimitModal] = useState(false);
    // 保存素材限制弹窗
    const [showUploadLimitModal, setShowUploadLimitModal] = useState(false);
    // 保存素材弹窗
    const [showAIDrawAssetSaveModal, setShowAIDrawAssetSaveModal] = useState(false);
    const stashJumpUrl = useRef<string>('');
    const chatApi = useRef(new ChatApi()).current;

    const checkAiDrawAssetSaveWithoutDoc = async (work: IWork, picId: string) => {
        const res = await chatApi.saveAiDrawAsset(picId.toString());
        if(res && res.code === 0) {
            setShowUploadLimitModal(true);
        }
        if (res.code == 1) {
            const { ua_id } = res.data[0];
            updateAiDrawAssetId(ua_id, work);
            setShowAIDrawAssetSaveModal(false);
            return true;
        }
        setShowAIDrawAssetSaveModal(false);
        return false;
    }
    const updateAiDrawAssetId = (ua_id: string, work: IWork) => {
        work.pages[0].assets.forEach((asset) => {
            if (checkIsAiDrawAsset(asset)) {
                (asset as IImageAsset).attribute.resId = ua_id;
            }
        });
    };

    const checkIsAiDrawAsset = (asset: IAsset<TAssetType>) => {
            if (
                asset.meta.rt_tag_type == IAiDesignTemplateImgTagTypeEnum.PRIMARY_IMG ||
                asset.meta.rt_tag_type == IAiDesignTemplateImgTagTypeEnum.PRIMARY_IMG_CONTAINER ||
                asset.meta.type == 'background'
            ) {
                return true;
            }
            return false;
        }
    const onPreviewItemClickWithoutDoc = async (picId: string, templeId: number, user_templ_id: string, aiProduceId: number, messageItem: IMessageItem) => {
        const block = {
            nextStep: false,
            upicId: '',
            aiproduce_id: 0,
        }
        if(user_templ_id && user_templ_id !== '0') {
            return {
                nextStep: true,
                upicId: user_templ_id,
                aiproduce_id: Number(aiProduceId),
            }
        }
        const upicId = '';
        const aiproduce_id = Number(aiProduceId);
        const res = await chatApi.getTemplInfo({ picId: templeId.toString() }) as any;
        if (!res || res.stat != 1 || !res.info) return block;
        
        const templateInfo = res.info;
        if (!templateInfo) return block;
        // 根据文本标记类型分组
        const textTagGroupAsset: any = {
            ['main_head']: {
                0: [],
            },
            ['sub_head']: {
                0: [],
            },
            ['third_head']: {
                0: [],
            },
            ['time']: {
                0: [],
            },
            ['introduction']: {
                0: [],
            },
            ['body']: {
                0: [],
            },
            ['other']: {
                0: [],
            },
        };
        // 格式化模板数据
        const formatResult = TemplateFormat.getFormat(res.doc, {
            picId,
            fontFamilys: [],
            imgUrls: [],
            textTagGroupAsset,
        });
        // 设置标题
        formatResult.canvas.title = messageItem.title[0];
        res.info.title = messageItem.title[0];
        // 替换内容
        TextLogic.replaceAssetContent(
            messageItem,
            formatResult.work,
            formatResult.canvas,
            res.info,
            textTagGroupAsset,
        );
        // 保存格式化结果
        const final = { ...formatResult, info: res.info };
            // 保存替换内容
        const { work, canvas, pageAttr } = final;

        const savedWork = cloneDeep(work);
        const shouldJump = await checkAiDrawAssetSaveWithoutDoc(savedWork, picId);
        if (!shouldJump) {
            return block;
        }
        const upicIdSave = await TemplateLogic.saveUserTempl({
            picId: templeId.toString(),
            upicId: upicId,
            work: savedWork,
            canvas,
            pageAttr,
            info: final.info,
            aiproduce_id: Number(aiproduce_id),
        });

        return {
          nextStep: true,
          upicId: upicIdSave,
          aiproduce_id: Number(aiproduce_id),
        };
    };

    const goEditWithoutDoc = async (e: React.MouseEvent, template: any, messageItem: IMessageItem, setUploadResult: (picId: string, upicId: string, aiproduce_id: number) => void) => {
        e.stopPropagation();
        const { templ_id, aiproduce_draw_id, user_templ_id, id: aiProduceId } = template;
        const { nextStep, upicId, aiproduce_id } = await onPreviewItemClickWithoutDoc(aiproduce_draw_id, templ_id, user_templ_id, aiProduceId, messageItem);
        if (!nextStep) {
            return;
        }
        if(!user_templ_id || user_templ_id === '0') {
            setUploadResult(templ_id, upicId, aiproduce_id);
        }
        // const { upicId, aiproduce_id, ai_template_type = 1, aid='', origin } = IpsUtils.Url.getUrlParams(window.location.href);
        const params = {
            picId: templ_id,
            upicId,
            aiproduce_id,
            t: Date.now(),
            origin: `${getOrigin(sourceFrom)}_aiChat`,
            ai_template_type: template.ai_template_type,
        }
        const url = joinUrlParams('https://ue.818ps.com/v4', params);

        chatApi.setPv(8281, {
            additional: {
                i0: `${getOrigin(sourceFrom)}_aiChat`,
                picId: templ_id,
                upicId,
                aiproduce_id,
                ut: upicId,
                ti: templ_id,
            },
        });
        if (window?.AppParams?.handleImgClick) {
            return window?.AppParams?.handleImgClick(params)
        }
        const openResult = window.open(url, '_blank');
        if (!openResult) {
            stashJumpUrl.current = url;
            setShowJumpLimitModal(true);
            chatApi.setPv(8959);
        }
    }
    
    return {
        goEditWithoutDoc,
        showAIDrawAssetSaveModal,
        showJumpLimitModal,
        showUploadLimitModal,
        setShowAIDrawAssetSaveModal,
        setShowJumpLimitModal,
        setShowUploadLimitModal,
    };
};
