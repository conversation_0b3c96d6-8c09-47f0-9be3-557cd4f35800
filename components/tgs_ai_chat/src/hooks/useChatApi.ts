import { useState, useCallback, useMemo } from 'react';
import { ChatApi } from '../api/chatApi';
import { AppChatApiConstructor } from '../types/api';

interface UseChatApiOptions {
  apiBaseUrl?: string;
  customEndpoints?: any;
  useMockApi?: boolean;
  customOptions?: any;
  authHandler?: {
    isAuthenticated: () => boolean;
    login: () => void;
    getUserInfo: () => { id: string | number; name: string } | null;
  };
  AppChatApi?: AppChatApiConstructor;
}

/**
 * Custom hook for interacting with the chat API
 */
export const useChatApi = (options: UseChatApiOptions = {}) => {
  const {
    apiBaseUrl = '',
    customEndpoints,
    customOptions,
    authHandler,
    AppChatApi
  } = options;

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Create API instance - memoized to prevent recreation on every render
  const api = useMemo(() => {
    if (AppChatApi) {
      return new AppChatApi(apiBaseUrl, customEndpoints, customOptions);
    }
    return new ChatApi(apiBaseUrl, customEndpoints, customOptions);
  }, [apiBaseUrl, customEndpoints, customOptions, AppChatApi]);

  /**
   * Check if user is authenticated
   */
  const checkAuthentication = useCallback((): boolean => {
    if (authHandler) {
      if (!authHandler.isAuthenticated()) {
        authHandler.login();
        return false;
      }
      return true;
    }
    return true; // If no auth handler, assume authenticated
  }, [authHandler]);

  /**
   * Initialize a new chat session
   * @param model AI model ID
   */
  const initSession = useCallback(async (prompt: string, sourceFrom: number): Promise<string | null> => {
    // if (!checkAuthentication()) {
    //   return null;
    // }

    setIsLoading(true);
    setError(null);

    try {
      const response = await api.initChatSession(prompt, sourceFrom);
      if (response.code === 1 && response.data?.session_id) {
        return response.data?.session_id;
      } else {
        setError(response.msg || '初始化会话失败');
        return null;
      }
    } catch (err) {
      setError('初始化会话时发生错误');
      console.error('Error initializing session:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [api, checkAuthentication]);

  /**
   * Get all chat sessions list
   */
  const getSessionList = useCallback(async (page_id: number, limit: number) => {
    if (!checkAuthentication()) {
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await api.getSessionList(page_id, limit);

      if (response.code === 1 && response.data?.list) {
        return response.data;
      } else {
        setError(response.msg || '获取会话列表失败');
        return null;
      }
    } catch (err) {
      setError('获取会话列表时发生错误');
      console.error('Error getting session list:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [api, checkAuthentication]);

  const getHistoryMessages = useCallback(async (session_id: string) => {
    if (!checkAuthentication()) {
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await api.getHistoryMessages(session_id);

      if (response.data?.list) {
        return response.data
      } else {
        setError('获取历史消息失败');
        return null;
      }
    } catch (err) {
      setError('获取历史消息时发生错误');
      console.error('Error getting history messages:', err);
      return null;
      } finally {
      setIsLoading(false);
    }
  }, [api, checkAuthentication]);

  /**
   * get config recommend suggestions prompt list
   * @returns {Promise<string[]>} - List of recommended prompts
   */
  const getSuggestionsPrompt = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const list = await api.getSuggestionsPrompt();
      return list;
    } catch (err) {
      setError('获取推荐提示词时发生错误');
      console.error('Error getting suggestions prompt:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [api, checkAuthentication]);

  const delSession = useCallback(async (session_id: string) => {

    setIsLoading(true);
    setError(null);

    try {
      const response = await api.delSession(session_id);
      return response;
    } catch (err) {
      setError('删除会话时发生错误');
      console.error('Error deleting session:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [api, checkAuthentication]);

  return {
    initSession,
    getSessionList,
    getHistoryMessages,
    getSuggestionsPrompt,
    delSession,
    isLoading,
    error,
  };
};
