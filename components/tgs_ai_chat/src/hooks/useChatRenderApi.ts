import { useState, useCallback, useMemo } from 'react';
import { ChatApi } from '../api/chatApi';

interface UseChatApiOptions {
  apiBaseUrl?: string;
  customEndpoints?: any;
  useMockApi?: boolean;
  customOptions?: any;
  authHandler?: {
    isAuthenticated: () => boolean;
    login: () => void;
    getUserInfo: () => { id: string | number; name: string } | null;
  };
}

export enum GenerateStatusEnum {
  PROCESSING = 'PROCESSING',
  SUCCESS = 'SUCCESS',
  FAIL = 'FAIL',
  ERROR = 'ERROR',
}

/**
 * Custom hook for interacting with the chat API
 */
export const useChatRenderApi = (options: UseChatApiOptions = {}) => {
  const {
    apiBaseUrl = '',
    customEndpoints,
    useMockApi = false,
    customOptions,
    authHandler,
  } = options;

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Create API instance - memoized to prevent recreation on every render
  const api = useMemo(() => {
    const Api = window?.AppParams?.AppChatApi || ChatApi;
    return  new Api(apiBaseUrl, customEndpoints, customOptions);
  }, [apiBaseUrl, customEndpoints, useMockApi, customOptions]);

  /**
   * Check if user is authenticated
   */
  const checkAuthentication = useCallback((): boolean => {
    if (authHandler) {
      if (!authHandler.isAuthenticated()) {
        authHandler.login();
        return false;
      }
      return true;
    }
    return true; // If no auth handler, assume authenticated
  }, [authHandler]);

  const drawCheck = useCallback(async (group_id: string) => {
    if (!checkAuthentication()) {
      return null;
    }
    setIsLoading(true);
    try {
      const response = await api.drawCheck(group_id);
      return response;
    } catch (error) {
      setError(error as string);
    } finally {
      setIsLoading(false);
    }
  }, [api, checkAuthentication]);

  const saveImage = useCallback(async (picId: string) => {
    if (!checkAuthentication()) {
      return null;
    }
    setIsLoading(true);
    try {
      const response = await api.saveImage(picId);
      return response;
    } catch (error) {
      setError(error as string);
    } finally {
      setIsLoading(false);
    }
  }, [api, checkAuthentication]);

  return {
    isLoading,
    error,
    drawCheck,
    saveImage,
  };
};
