import { IChatSettings } from '../types';

/**
 * Default chat settings
 */
export const DEFAULT_CHAT_SETTINGS: IChatSettings = {
  showTimestamp: true,
  enableMarkdown: true,
  enableCodeHighlighting: true,
  theme: 'light',
};

/**
 * Local storage keys
 */
export const STORAGE_KEYS = {
  CHAT_HISTORY: 'tgs_ai_chat_history',
  CHAT_SETTINGS: 'tgs_ai_chat_settings',
  SELECTED_MODEL: 'tgs_ai_selected_model',
};

/**
 * Default welcome message
 */
export const WELCOME_MESSAGE: string =
  '你好！我是AI助手，可以帮助你回答问题、生成创意内容或提供建议。请告诉我你需要什么帮助？';

/**
 * Default API endpoints
 */
export const DEFAULT_API_ENDPOINTS = {
  INIT_SESSION: '/aichat/create',
  SEND_MESSAGE: '/aichat/completion',
  GET_HISTORY: '/aichat/history-messages',
  GET_MODELS: '/aichat/models',
  GET_SESSION_LIST: '/aichat/session-list',
  GET_SUGGESTIONS: '/aichat/prompt-list',
  DRAW_CHECK: '/aiproduce/draw-check',
  ADD_USER_ASSET: '/aiproducev2/user-assert',
  SAVE_IMAGE: '/aiproducev2/user-assert',
  DEL_SESSION: '/aichat/del-session',
  GET_TEMPL_INFO: '/api/user-get-templ',
  GET_PRESET_DES: '/aiproduce/theme-list',
  GET_GENERATE_NUM: '/aiproduce/get-num',
  SAVE_OUTLINE: '/aiproduce/edit-precis',
  SAVE_USER_TEMPL: '/api/user-save-templ',
  ASYNC_APPLY_DOWNLOAD: '/uapi/async-apply-download',
  ASYNC_APPLY_DOWN_PPT_V5: '/uapi/async-apply-down-ppt-v5',
  ASYNC_CHECK_DOWNLOAD: '/uapi/async-check-download',
  GET_TEMPLATE_LIST: '/aiproduce/template-list',
  PUT_PREVIEW_POSTER: '/apiv2/preview-set',
  GET_AI_PRODUCE_INFO: '/aiproduce/list',
  REPLACE_TEMPLATE: '/aiproduce/again-template',
  GET_CONCURRENCY_TASK_INFO: '/aiproduce/template-info',
  GET_TEMPLATE_MORE: '/aiproduce/again-template-more',
  GET_USER_GENERATE_RECORD: '/aiproduce/group-history',
  GET_PPT_TEMP: '/ai-ppt/search',
  GET_PPT_OUTLINE: '/ai-ppt/info',
  SAVE_PIC_ID: '/ai-ppt/save-pic-id',
  SEARCH_AI_PRODUCE_INFO: '/aiproduce/search',
  START_GENERATE_TASK_NEW: '/aiproducev2/new-task',
  CHECK_AI_DRAW_RESULT_V1: '/aiproduce/draw-check',
  GET_RELETED_TEMP_NEW: '/aiproducev2/search',
  GET_GENERATE_TASK_DATA: '/aiproducev2/task-data',
  GET_AI_CONTENT_LIST_NEW: '/aiproducev2/list',
  GET_TEMPLATE_MORE_NEW: '/aiproducev2/change-batch',
  CHECK_AI_DRAW_RESULT: '/aiproducev2/draw-check',
  SAVE_AI_DRAW_ASSET: '/aiproducev2/user-assert',
};

/**
 * Message status
 */
export enum MessageStatus {
  SENDING = 'sending',
  SENT = 'END',
  ERROR = 'failed',
  LACK = 'LACK',
}

/**
 * Message role
 */
export enum MessageRole {
  USER = 'USER',
  ASSISTANT = 'SYSTEM',
}
