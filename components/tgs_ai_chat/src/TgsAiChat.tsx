import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import classNames from 'classnames';
import './styles/iconfont.scss';
import {
  ChatMessageList,
  ChatInput,
  WelcomeScreen,
  ChatHeader
} from './components';
import { useChatHistory, useChatApi } from './hooks';
import { createChatMessage } from './utils/chatUtils';
import {
  DEFAULT_CHAT_SETTINGS,
  WELCOME_MESSAGE,
  MessageRole,
  MessageStatus
} from './constants';
import { ITgsAiChatProps, IChatSession, IPromptSuggestion, IDrawData } from './types/index';
import './styles/index.scss'
import styles from './styles/TgsAiChat.module.scss';
import { fetchStream } from './utils/fetchStream';
import { GenerationProvider, GenerationType, GenerationStatus, useGeneration } from './context/GenerationContext';
import { ChatSettingsProvider, useChatSettings } from './context/ChatSettingsContext';
import { ChatApi } from './api/chatApi';

/**
 * TGS AI Chat Component
 */
const TgsAiChatContent: React.FC<ITgsAiChatProps> = ({
  className,
  apiEndpoints,
  initialSettings,
  authHandler,
  welcomeMessage = WELCOME_MESSAGE,
  onMessageSent,
  onMessageReceived,
  userInfo,
  openLoginModel,
  Modal,
  Popover,
  popMessage,
  AppParams,
  uploadLimitPopup,
  rechargeModalPopup,
}) => {
  const { setGenerationStatus, generationStatus, updateGenerationCount, deepThink } = useGeneration();
  const { size, sourceFrom, editorController } = useChatSettings();

  // Merge default settings with initial settings
  const settings = { ...DEFAULT_CHAT_SETTINGS, ...initialSettings };
  
  /** 会话列表，数据仅供历史记录使用 */
  const [sessionList, setSessionList] = useState<IChatSession[]>([])

  // State to control welcome screen visibility
  const [showWelcomeView, setShowWelcomeView] = useState(true);
  const [suggestionsPromptList, setSuggestionsPromptList] = useState<IPromptSuggestion[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string>();
  const currentMessageId = useRef<string>('30');
  const abortControllerRef = useRef<AbortController | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const pageSize = 20;

  const [isInputVisible, setIsInputVisible] = useState(true);
  const [inputValue, setInputValue] = useState('');
  const inputBarRef = useRef<HTMLDivElement>(null);

  // Add scroll control state
  const [scrollControls, setScrollControls] = useState<{
    scrollToBottom: (smooth?: boolean, enableScroll?: boolean) => void;
    maybeScrollToBottom: (smooth?: boolean) => void;
  } | null>(null);

  const chatApi = useMemo(() => new ChatApi('//818ps.com'), []);

  // Handle page view tracking
  const setPv = useCallback((pageId: number, additional: { [key: string | number]: string | number } = {}) => {
    chatApi.setPv(pageId, { additional });
  }, [chatApi]);

  // Track input visibility
  useEffect(() => {
    if (!inputBarRef.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsInputVisible(entry.isIntersecting);
      },
      {
        threshold: 0.1, // Trigger when at least 10% of the element is visible
      }
    );

    observer.observe(inputBarRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    if(generationStatus === GenerationStatus.IDLE) {
      setCurrentSession(pre => {
        if(pre) {
          return {
            ...pre,
            messages: pre.messages.map(msg => {
              if(msg.draw && msg.draw.some((item: IDrawData) => item.status === 'PROCESSING')) {
                return {
                  ...msg,
                  group_unique: '',
                  draw: undefined,
                }
              }
              return msg
            })
          }
        }
        return pre;
      })
    }
  }, [generationStatus]);

  // Check authentication before API calls
  const checkAuth = useCallback(() => {
    if (!userInfo) {
      openLoginModel?.();
      return false;
    }
    return true;
  }, [userInfo, openLoginModel]);

  // Memoize customOptions to prevent recreation on every render
  const customOptions = useMemo(() => ({
    credentials: 'include' as RequestCredentials,
  }), []);

  // Custom hooks - API hook first
  const {
    initSession,
    getSessionList,
    getHistoryMessages,
    getSuggestionsPrompt,
    error,
  } = useChatApi({
    apiBaseUrl: '//818ps.com',
    customEndpoints: apiEndpoints,
    authHandler,
    customOptions,
    AppChatApi: AppParams?.AppChatApi
  });

  // History hook - uses getHistoryMessages from API hook
  const {
    currentSession,
    addMessage,
    setCurrentSession,
  } = useChatHistory({
    sessionId: currentSessionId,
    sessionList,
    getHistoryMessages: async (sessionId: string) => {
      const result = await getHistoryMessages(sessionId);
      if (result?.list) {
        return {
          list: result.list
        };
      }
      return null;
    },
  });

  // Load session list with pagination
  const loadSessionList = useCallback(async (pageNum: number = 1, isLoadMore: boolean = false) => {
    if (!userInfo || isLoadingMore) return;
    
    try {
      setIsLoadingMore(true);
      const result = await getSessionList(pageNum, pageSize);
      if (result) {
        const newList = result.list || [];
        setHasMore(newList.length === pageSize);
        
        if (isLoadMore) {
          setSessionList(prev => [...prev, ...newList]);
        } else {
          setSessionList(newList);
        }
      }
    } catch (error) {
      console.error('Failed to load sessions:', error);
      popMessage.error('加载会话列表失败');
    } finally {
      setIsLoadingMore(false);
    }
  }, [getSessionList, userInfo]);

  // Handle scroll event
  const handleHistoryScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    // Add a small threshold to prevent multiple triggers
    if (scrollHeight - scrollTop - clientHeight < 20 && hasMore && !isLoadingMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      setPv(9055, { i1: sourceFrom, i2: nextPage });
      loadSessionList(nextPage, true);
    }
  }, [hasMore, isLoadingMore, page, loadSessionList]);

  // Initial load
  useEffect(() => {
    if (userInfo) {
      loadSessionList(1, false);
    }
  }, [userInfo, loadSessionList]);

  useEffect(() => {
    getSuggestionsPromptList();
  }, []);

  const getSuggestionsPromptList = useCallback(async () => {
      const result = await getSuggestionsPrompt();
      if (result?.code === 1) {
        setSuggestionsPromptList(result.data.list || []);
      }
  }, [getSuggestionsPrompt]);

  useEffect(() => {
    setPv(9060, { i1: sourceFrom });
  }, [sourceFrom]);

  // Show error message if API returns an error
  useEffect(() => {
    if (error) {
      popMessage.error(error);
    }
  }, [error]);

  // Add abort stream function
  const abortCurrentStream = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    setGenerationStatus(GenerationStatus.IDLE);
  }, []);

  /**
   * 初始化图片信息
   * @param drawInfo
   * @returns
   */
  const initDrawInfo = (drawInfo: {num:string, width:string, height:string}): IDrawData[] => {
    if(!drawInfo) return [];
    const { width, height, num } = drawInfo;
    const images = [];
    for(let i = 0; i < parseInt(num); i++) {
      images.push({
        id: '',
        preview: '',
        status: 'PROCESSING',
        width: width,
        height: height,
        group_unique: '',
        aiproduce_draw_id: '',
        draw_template_style: '',
        path: '',
        templ_id: '',
        user_templ_id: '',
      } as IDrawData)
    }
    return images;
  }

  // 统一的流式消息发送函数
  const sendMessageWithStream = async (
    sessionId: string,
    messageText: string,
    message_id?: string,
  ) => {
    if (!checkAuth()) return;

    // Abort any existing stream
    abortCurrentStream();
    // record current editor state

    setGenerationStatus(GenerationStatus.PROCESSING);
    // Create new AbortController for this request
    abortControllerRef.current = new AbortController();

    let aiContent = '';
    let reasoningContent = '';
    let group_unique = '';
    let drawInfo: object | null | undefined = null;
    let generationType = GenerationType.TEXT;
    let lack = false;
    let templateInfo: any = null;
    let recordEditorState = false;
    /**
     * 如果message_id存在，则认为是重新生成消息，需要删除当前会话的最后一条消息
     */
    if(message_id) {
      setCurrentSession(pre => {
        if(pre) {
          const tempMessage = pre.messages
          /**
           * 找到message_id对应的消息
           */
          while(tempMessage[tempMessage.length - 1].message_id !== message_id) {
            tempMessage.pop();
          }
          /**
           * 删除message_id对应的消息
           */
          tempMessage.pop();
          return {
            ...pre,
            messages: tempMessage
          }
        }
        return pre;
      });
    }

    try {
      const fetchChatStream = AppParams?.fetchStream || fetchStream;
      await fetchChatStream({
        url: '/aichat/completion',
        body: {
          prompt: messageText,
          session_id: sessionId,
          thinking_enabled: deepThink,
          message_id: message_id,
          regenerate: message_id ? 1 : 0,
          origin: sourceFrom,
        },
        signal: abortControllerRef.current.signal,
        onMessage: (chunk: any) => {
          if (chunk.s === 'TITLE') {
            setSessionList(pre => {
              if(pre) {
                return pre.map(item => {
                  if(item.session_id === sessionId) {
                    return {
                      ...item,
                      title: chunk.c || '',
                    }
                  }
                  return item;
                })
              }
              return pre;
            })
            setCurrentSession(pre => {
              if (!pre) return null;
              return {
                ...pre,
                title: chunk.c || '',
                updated: new Date().toISOString(),
              }
            })
          }else if (chunk.s === 'WAIT') {
            if (chunk.o === 'DRAW') {
              group_unique = chunk.c || '';
              drawInfo = {
                num: chunk.i.num,
                width: chunk.i.width,
                height: chunk.i.height,
              }
              generationType = GenerationType.IMAGE;
            }else if (chunk.o === GenerationType.AIDESIGN) {
              generationType = GenerationType.AIDESIGN;
              group_unique = chunk.c || '';
              templateInfo = {
                templateIds: chunk.i.temp || [],
                produceType: chunk.i.produce_type,
                maxSize: 1,
              }
            }else{
              generationType = GenerationType.TEXT;
            }
        }else if (chunk.s !== 'END' && chunk.s !== 'ERROR') {
            // 推理内容
            if (chunk.r) {
              reasoningContent += chunk.r;
            }
            // 内容
            if (chunk.c) {
              aiContent += chunk.c;
            }
            // 消息id
            if(chunk.msg_id) {
              currentMessageId.current = chunk.msg_id;
              if(!recordEditorState) {
                editorController?.recordCurrentState(sessionId+'_'+currentMessageId.current);
                recordEditorState = true;
              }
            }
            /**
             * 敏感词,不生成messageId,不生成内容,id为前端处理
             */
            if(chunk.s === 'SENSITIVE') {
              currentMessageId.current = 'SENSITIVE-' + new Date().getTime().toString();
            }
            if (chunk.s === 'LACK') {
              lack = true;
              setPv(9090, { i1: sourceFrom });
              rechargeModalPopup?.('', updateGenerationCount);
            }
            setCurrentSession(pre => {
              if(pre) {
                const hasMessage = pre.messages.find(msg => msg.message_id === currentMessageId.current);
                return {
                  ...pre,
                  messages: hasMessage ? pre.messages.map(msg => {
                    if(msg.message_id === currentMessageId.current) {
                      return {
                        ...msg,
                        content: aiContent,
                        thinking_content: reasoningContent,
                        group_unique: group_unique,
                        draw: (group_unique ? msg.draw ? msg.draw : initDrawInfo(drawInfo as {num:string, width:string, height:string}) : undefined),
                        templateInfo: templateInfo,
                        type: generationType,
                        status: lack ? MessageStatus.LACK : MessageStatus.SENDING,
                      }
                    }
                    return msg
                  }) : [...pre.messages, {
                    message_id: currentMessageId.current,
                    role: MessageRole.ASSISTANT,
                    content: aiContent,
                    status: lack ? MessageStatus.LACK : MessageStatus.SENDING,
                    thinking_enabled: deepThink ? '1' : '0',
                    thinking_content: reasoningContent,
                    templateInfo: templateInfo,
                    type: generationType,
                  }]
                }
              }
              return pre;
            });
          }
        },
        onDone: () => {
          setPv(9056, { i1: sourceFrom, i2: deepThink ? 1 : 0 });
          if(generationType === GenerationType.TEXT) {
            setGenerationStatus(GenerationStatus.COMPLETED);
          }
          setTimeout(() => {
            updateGenerationCount();
          }, 3000);
          setCurrentSession(pre => {
            if(pre) {
              return {
                ...pre,
                messages: pre.messages.map(msg => {
                  if(msg.message_id === currentMessageId.current) {
                    return {
                      ...msg,
                        content: aiContent,
                        thinking_content: reasoningContent,
                      status: lack ? MessageStatus.LACK : MessageStatus.SENT,
                    }
                  }
                  return msg;
                })
              }
            }
            return pre;
          });
          if (onMessageReceived) {
            onMessageReceived({
              message_id: currentMessageId.current,
              role: MessageRole.ASSISTANT,
              content: aiContent,
              status: lack ? MessageStatus.LACK : MessageStatus.SENT,
              thinking_enabled: deepThink ? '1' : '0',
              thinking_content: reasoningContent
            });
          }
        },
        onError: () => {
          setPv(9057, { i1: sourceFrom });
          setGenerationStatus(GenerationStatus.ERROR);
        },
        onAbort: () => {
          setPv(9062, { i1: sourceFrom });
          setGenerationStatus(GenerationStatus.IDLE);
          setSessionList(pre => {
            if(pre) {
              return pre.map(item => {
                if(item.session_id === sessionId) {
                  return {
                    ...item,
                    title: item.title || '新会话',
                    title_type: 'auto',
                    updated: new Date().toISOString(),
                  }
                }
                return item;
              })
            }
            return pre;
          })
          setCurrentSession(pre => {
            if(pre) {
              return {
                ...pre,
                title: pre.title || '新会话',
                messages: pre.messages.map(msg => {
                  if(msg.message_id === currentMessageId.current) {
                    return {
                      ...msg,
                      status: lack ? MessageStatus.LACK : MessageStatus.SENT,
                    }
                  }
                  return msg;
                })
              }
            }
            return pre;
          });
        }
      });
    } finally {
      abortControllerRef.current = null;
    }
  };

  // Handle sending a message
  const handleSendMessage = useCallback(async (messageText: string) => {
    if (!checkAuth()) return;

    // Hide welcome screen if it's visible
    if (showWelcomeView) {
      setShowWelcomeView(false);
    }

    if (!currentSessionId) {
      popMessage.error('会话未初始化，请稍后再试');
      return;
    }

    // Scroll to bottom when sending message
    scrollControls?.scrollToBottom(true);

    // Create and add user message
    const userMessage = createChatMessage(MessageRole.USER, messageText);
    addMessage(userMessage);

    // Callback for sent message
    if (onMessageSent) {
      onMessageSent(userMessage);
    }

    // 使用统一的流式消息发送函数
    await sendMessageWithStream(currentSessionId, messageText);
  }, [currentSessionId, sendMessageWithStream, onMessageSent, showWelcomeView, setShowWelcomeView, checkAuth]);

  // Handle WelcomeScreen 提交
  const handleWelcomeSubmit = async (prompt: string) => {
    if (!checkAuth()) return;

    // Abort any existing stream before starting new session
    abortCurrentStream();
    setPv(9048, { i1: sourceFrom, i2: deepThink ? 1 : 0 });

    // Hide welcome screen immediately
    setShowWelcomeView(false);
    
    try {
      // 1. 首先初始化会话
      const sessionResult = await initSession(prompt, sourceFrom);
      if (!sessionResult) {
        popMessage.error('创建会话失败');
        setShowWelcomeView(true); // 恢复欢迎界面
        return;
      }
      const userMessage = createChatMessage(MessageRole.USER, prompt,);
      const newSession: IChatSession = {
        session_id: sessionResult,
        title: '',
        title_type: 'auto',
        created: new Date().toISOString(),
        updated: new Date().toISOString(),
        current_message_id: '',
        messages: [userMessage],
      }
      setSessionList([newSession, ...sessionList])
      setCurrentSessionId(sessionResult);

      // Scroll to bottom when starting new chat
      // scrollControls?.scrollToBottom(true);

      // Callback for sent message
      if (onMessageSent) {
        onMessageSent(userMessage);
      }
      // 3. 发送消息并处理流式响应
      await sendMessageWithStream(sessionResult, prompt);

    } catch (error) {
      console.error('Welcome submit error:', error);
      popMessage.error('发送消息失败');
      setShowWelcomeView(true); // 恢复欢迎界面
    }
  };

  // Handle input value change
  const handleInputChange = useCallback((value: string) => {
    setInputValue(value);
  }, []);

  // Reset input value when starting new chat
  const handleNewChat = useCallback(() => {
    // Abort any existing stream when starting new chat
    abortCurrentStream();
    setPv(9053, { i1: sourceFrom });
    setCurrentSessionId('');
    setShowWelcomeView(true);
    setInputValue('');
  }, [abortCurrentStream, sourceFrom]);

  const handleRegenerateMessage = async (message_id: string) => {
    if (!checkAuth()) return;
    if(!currentSessionId) {
      popMessage.error('会话未初始化，请稍后再试');
      return;
    }
    await sendMessageWithStream(currentSessionId, '', message_id);
  }

  // Combine class names
  const containerClassName = classNames(
    styles.container,
    styles[size],
    className,
    'ai-chat'
  );

  function handleSessionSelect(sessionId: string) {
    abortCurrentStream();
    setCurrentSessionId(sessionId);
  }

  const disclaimerContent = (
    <div className={styles.disclaimerContent}>
      您了解并同意，图怪兽 AI 处于前沿探索阶段，AI对话服务由deepseekAPI技术提供支持，请注意以下事项：您应当合法合规使用图怪兽 AI 及其生成的 AI 内容，请勿将AI生成的内容用于任何违法或不当用途，并承担违法违规使用产生的所有责任。图怪兽 AI 生成的作品仅供个人体验学习交流使用，您不可将图怪兽 AI 生成的作品任何部分或全部进行商业性质或非商业性质的利用、复制、拷贝、出售、分享、广告等。图怪兽 AI 对您的使用不做保证且不承担任何责任，且其生成的内容不代表图怪兽 AI 的态度或观点。
    </div>
  );

  // Add cleanup on unmount
  useEffect(() => {
    return () => {
      abortCurrentStream();
    };
  }, [abortCurrentStream]);
  useEffect(() => {
    setPv(9049, { i1: sourceFrom, i2: deepThink ? 1 : 0 });
  }, [sourceFrom, deepThink]);

  const addPic = (asset: any) => {
    if(editorController) {
      editorController.addPic(asset);
    }
  } 

  // Create a wrapper component for the custom header that provides context values
  const CustomHeaderWrapper = useCallback(({ HeaderComponent, ...props }: { HeaderComponent: React.ComponentType<any> } & any) => {
    return <HeaderComponent {...props} size={size} sourceFrom={sourceFrom} />;
  }, []);

  const Header = AppParams?.ChatHeader ? 
    (props: any) => <CustomHeaderWrapper HeaderComponent={AppParams.ChatHeader} {...props} /> : 
    ChatHeader;

  // Set AppParams to the global variable and attach it to the window object
  useEffect(() => {
    if (typeof window !== 'undefined' && AppParams) {
      window.AppParams = AppParams;
    }
  }, [AppParams]);

  return (
    <div
      className={containerClassName}
    >
      <Header
        userInfo={userInfo}
        openLoginModel={openLoginModel}
        onNewChat={handleNewChat}
        currentSession={currentSession}
        onSessionSelect={handleSessionSelect}
        sessions={sessionList}
        currentSessionId={currentSessionId}
        onScroll={handleHistoryScroll}
        isLoadingMore={isLoadingMore}
        onReloadSessions={() => {loadSessionList(1, false);setPage(1)}}
        Modal={Modal}
        rechargeModalPopup={rechargeModalPopup}
      />

      {showWelcomeView && !currentSessionId ? (
        <WelcomeScreen
          onSubmit={handleWelcomeSubmit}
          promptSuggestions={suggestionsPromptList}
        />
      ) : (
        <div className={classNames(styles.chatArea, styles[size])}>
          <ChatMessageList
            key={currentSessionId}
            messages={currentSession?.messages || []}
            showTimestamp={settings.showTimestamp}
            enableMarkdown={settings.enableMarkdown}
            welcomeMessage={welcomeMessage}
            handleRegenerateMessage={handleRegenerateMessage}
            onScrollControlReady={setScrollControls}
            popMessage={popMessage}
            addPic={addPic}
            uploadLimitPopup={uploadLimitPopup}
            rechargeModalPopup={rechargeModalPopup}
          />
          <div className={classNames(styles.inputBar, styles[size])}>
            <ChatInput
              onSendMessage={handleSendMessage}
              placeholder={'可以问我任何问题，Shift + Enter 换行'}
              value={inputValue}
              onChange={handleInputChange}
              isVisible={isInputVisible}
              abortCurrentStream={abortCurrentStream}
            />
            <div className={styles.inputBarRight}>
              <span className={styles.inputBarRightText}>内容由AI生成，仅供参考</span>
              {/* @ts-ignore */}
              <Popover
                content={disclaimerContent}
                title="免责声明"
                trigger="click"
                placement="top"
                overlayClassName={styles.disclaimerPopover}
              >
                <span 
                  className={styles.inputBarRightLink}
                  role="button"
                  tabIndex={0}
                >
                  免责声明
                </span>
              </Popover>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export const TgsAiChat: React.FC<ITgsAiChatProps> = (props) => {
  return (
    <ChatSettingsProvider size={props.size} sourceFrom={props.sourceFrom} editorController={props.editorController} popMessage={props.popMessage}>
        <GenerationProvider>
            <TgsAiChatContent {...props} />
        </GenerationProvider>
    </ChatSettingsProvider>
  );
};