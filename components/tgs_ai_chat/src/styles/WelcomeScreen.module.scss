@import './variables.scss';

.welcomeScreen {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding: 20px;
  box-sizing: border-box;

  &.large {
    .content {
      max-width: 760px;
      background-image: url("https://js.tuguaishou.com/ai/aiChat/assets/chat_background.png");
      background-size: 130%;
      background-position: center;
    }

    .title {
      font-size: 36px;
    }

    .subtitle {
      font-size: 15px;
      margin-bottom: 60px;
    }

    .logoPlaceholder {
      width: 64px;
      height: 64px;
      margin-bottom: 24px;
    }

    .deepseekIcon {
      width: 100px;
    }
  }

  &.small {
    padding-left: 16px;
    padding-right: 31px;

    .content {
      padding-bottom: 0;
      max-width: 313px;
    }

    .title {
      font-size: 24px;
    }

    .subtitle {
      margin-top: 10px;
      font-size: 14px;
      margin-bottom: 36px;
    }

    .logoPlaceholder {
      width: 48px;
      min-height: 48px;
      max-height: 48px;
      margin-bottom: 16px;
    }

    .deepseekIcon {
      width: 132px;
      vertical-align: middle;
    }
    .form {
      margin-bottom: 15px;
    }
    .suggestions {
      margin-top: 0;
      gap: 10px;
      .suggestionButton {
        box-shadow: none;
        border: 1px solid #E9E8E8;
        padding: 8px 12px;
      }
    }
  }
  &.app {
    padding-left: 20px;
    padding-right: 20px;

    .content {
      padding-bottom: 0;
      max-width: 100%;
      padding-top: calc((100vh - 200px - 400px) / 2)!important;
    }

    .title {
      font-size: 24px;
    }

    .subtitle {
      font-size: 14px;
      margin-bottom: 36px;
    }

    .logoPlaceholder {
      width: 48px;
      min-height: 48px;
      max-height: 48px;
      margin-bottom: 16px;
    }

    .deepseekIcon {
      width: 132px;
      vertical-align: middle;
    }
    .form {
      margin-bottom: 15px;
      position: fixed;
      width: calc(100% - 40px);
      bottom: 0;
    }
    .suggestions {
      margin-top: 0;
      gap: 10px;
      .suggestionButton {
        box-shadow: none;
        border: 1px solid #E9E8E8;
        padding: 8px 12px;
      }
    }
  }
}

.content {
  width: 100%;
  padding: 40px 0;
  text-align: center;
  margin: 0 auto;
  max-width: 100%;
  box-sizing: border-box;
  padding-top: calc((100vh - 40px - 650px) / 2);
}

.form {
  width: 100%;
  margin-bottom: 32px;
}

.inputWrapper {
  position: relative;
  width: 100%;
}

.suggestions {
  width: 100%;
  display: inline-block;
  text-align: center;
  margin-top: 20px;
}

.suggestionButton {
  display: inline-block;
  align-items: center;
  padding: 10px 12px;
  background-color: #fff;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0px 1px 10px 0px #0000001A;
  font-size: 14px;
  color: #000000;
  white-space: nowrap;
  margin: 0 6px 12px 6px;

  &:hover {
    background: #e8e8e8;
  }
}

.skeleton {
  cursor: default;
  background: #f0f0f0;
  position: relative;
  overflow: hidden;
  min-width: 120px;
  height: 32px;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    transform: translateX(-100%);
    background-image: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0,
      rgba(255, 255, 255, 0.2) 20%,
      rgba(255, 255, 255, 0.5) 60%,
      rgba(255, 255, 255, 0)
    );
    animation: shimmer 2s infinite;
  }
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

.logoPlaceholder {
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: $bg-color;
  background-image: url('https://js.tuguaishou.com/ai/aiChat/assets/chat_logo.png');
  background-size: cover;
}

.title {
  margin: 0;
  font-weight: 700;
  color: #000000;
}

.subtitle {
  color: $text-color-light;
  text-align: center;
}

.deepseekIcon {
  vertical-align: bottom;
}

.highlight {
  color: #2563eb;
  font-weight: 600;
}

.deepThinkBtn {
  width: 100%;
  padding: 10px 0;
  margin-bottom: 24px;
  border-radius: $border-radius;
  background: #f3f4f6;
  color: #374151;
  font-weight: 500;
  border: none;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;

  &:hover {
    background: #dbeafe;
  }
}

.deepThinkIcon {
  font-size: 20px;
  margin-right: 8px;
}

.suggestionIcon {
  font-size: 16px;
  margin-right: 6px;
}

.suggestionSkeleton {
  cursor: default;
  background: #f0f0f0;
  position: relative;
  overflow: hidden;
}

.suggestionSkeletonShimmer {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transform: translateX(-100%);
  background-image: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0,
    rgba(255, 255, 255, 0.2) 20%,
    rgba(255, 255, 255, 0.5) 60%,
    rgba(255, 255, 255, 0)
  );
  animation: suggestionShimmer 2s infinite;
}

@keyframes suggestionShimmer {
  100% {
    transform: translateX(100%);
  }
}
