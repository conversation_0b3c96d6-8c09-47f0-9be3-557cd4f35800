// Variables
$primary-color: #EF3964;
$primary-color-light: rgba(255, 69, 85, 0.1);
$primary-color-dark: #e63347;
$text-color: #333;
$text-color-light: #666;
$text-color-lighter: #999;
$bg-color: #F8FBFD;
$bg-color-light: #f8f9fb;
$bg-color-dark: #f0f1f5;
$border-color: #e6e6e6;
$border-radius: 8px;
$border-radius-sm: 4px;
$border-radius-lg: 12px;
$box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
$transition-speed: 0.2s;
$main-border: #E6E6E6;
// Global box model
.ai-chat {
  *, *::before, *::after {
    box-sizing: border-box;
  }
}


// Breakpoints
$small-width: 360px;
$large-width: 760px;

// Mixins
@mixin small-screen {
  @media (max-width: #{$small-width}) {
    @content;
  }
}

@mixin large-screen {
  @media (min-width: #{$large-width}) {
    @content;
  }
}

@mixin dark-mode {
  [data-theme="dark"] & {
    @content;
  }
}

// Component styles
.tgs-ai-chat {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: $bg-color;
  overflow: hidden;
  position: relative;

  &--small {
    width: $small-width;
  }

  &--large {
    width: $large-width;
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: $bg-color-light;
    border-bottom: 1px solid $border-color;

    .tgs-ai-chat--small & {
      padding: 12px;
    }

    // Light theme styles are sufficient

    &-title {
      font-size: 16px;
      font-weight: 600;
      color: $text-color;

      // Light theme styles are sufficient
    }

    &-actions {
      display: flex;
      align-items: center;
      gap: 8px;

      &-button {
        background: none;
        border: none;
        cursor: pointer;
        color: $text-color-light;
        font-size: 16px;
        padding: 4px;
        border-radius: $border-radius-sm;
        transition: background-color $transition-speed;

        // Light theme styles are sufficient

        &:hover {
          background-color: rgba(0, 0, 0, 0.05);

          // Light theme styles are sufficient
        }
      }
    }
  }

  &__model-selector {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    background-color: transparent;
    position: absolute;
    bottom: 16px;
    left: 16px;
    z-index: 10;
    max-width: 200px;

    .tgs-ai-chat--small & {
      padding: 8px;
      bottom: 12px;
      left: 12px;
      max-width: 150px;
    }

    &-label {
      font-size: 14px;
      color: $text-color-light;
      margin-right: 8px;

      @include dark-mode {
        color: #b0b0b0;
      }
    }

    &-select {
      flex: 1;
      padding: 6px 8px;
      border: 1px solid $border-color;
      border-radius: $border-radius-sm;
      background-color: $bg-color;
      font-size: 14px;
      color: $text-color;
      outline: none;

      @include dark-mode {
        background-color: #2a2a2a;
        border-color: #3a3a3a;
        color: #f0f0f0;
      }

      &:focus {
        border-color: $primary-color;
      }
    }
  }

  &__message-list {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;

    .tgs-ai-chat--small & {
      padding: 12px;
      gap: 12px;
    }

    // Light theme styles are sufficient

    &-empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: $text-color-lighter;
      text-align: center;
      padding: 0 20px;

      @include dark-mode {
        color: #b0b0b0;
      }

      &-icon {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.5;
      }

      &-text {
        font-size: 16px;
        max-width: 300px;
      }
    }
  }

  &__message {
    display: flex;
    flex-direction: column;
    max-width: 85%;

    .tgs-ai-chat--small & {
      max-width: 90%;
    }

    &--user {
      align-self: flex-end;

      .tgs-ai-chat__message-content {
        background-color: $primary-color;
        color: #fff;
        border-radius: $border-radius-lg $border-radius-lg 0 $border-radius-lg;

        pre {
          background-color: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
        }

        code {
          background-color: rgba(255, 255, 255, 0.1);
        }
      }

      .tgs-ai-chat__message-info {
        align-self: flex-end;
      }
    }

    &--assistant {
      align-self: flex-start;

      .tgs-ai-chat__message-content {
        background-color: $bg-color-dark;
        color: $text-color;
        border-radius: $border-radius-lg $border-radius-lg $border-radius-lg 0;

        // Light theme styles are sufficient
      }
    }

    &-content {
      padding: 12px 16px;
      font-size: 14px;
      line-height: 1.5;
      word-break: break-word;

      .tgs-ai-chat--small & {
        padding: 10px 14px;
        font-size: 13px;
      }

      p {
        margin: 0 0 8px 0;

        &:last-child {
          margin-bottom: 0;
        }
      }

      pre {
        background-color: rgba(0, 0, 0, 0.05);
        padding: 8px;
        border-radius: $border-radius-sm;
        overflow-x: auto;
        margin: 8px 0;
        font-family: monospace;
        font-size: 13px;

        .tgs-ai-chat--small & {
          font-size: 12px;
          padding: 6px;
        }
      }

      code {
        font-family: monospace;
        background-color: rgba(0, 0, 0, 0.05);
        padding: 2px 4px;
        border-radius: 3px;
        font-size: 13px;

        .tgs-ai-chat--small & {
          font-size: 12px;
        }
      }
    }

    &-info {
      display: flex;
      align-items: center;
      margin-top: 4px;
      font-size: 12px;
      color: $text-color-lighter;

      // Light theme styles are sufficient

      &-timestamp {
        margin-right: 8px;
      }

      &-model {
        background-color: $bg-color-dark;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 11px;

        // Light theme styles are sufficient
      }
    }
  }

  &__input {
    padding: 16px 16px 16px 220px; // Add left padding to make room for model selector
    background-color: $bg-color;
    position: relative;

    .tgs-ai-chat--small & {
      padding: 12px 12px 12px 120px;
      position: sticky;
      bottom: 0;
    }

    &-wrapper {
      position: relative;
      display: flex;
      flex-direction: column;
    }

    &-textarea {
      width: 100%;
      min-height: 60px;
      max-height: 150px;
      padding: 12px 60px 12px 12px; // Increased right padding for send button
      border: 1px solid $border-color;
      border-radius: $border-radius;
      resize: none;
      font-size: 14px;
      line-height: 1.5;
      color: $text-color;
      outline: none;
      transition: border-color $transition-speed;

      .tgs-ai-chat--small & {
        min-height: 48px;
        padding: 10px 50px 10px 10px;
        font-size: 13px;
      }

      &:focus {
        border-color: $primary-color;
      }

      &::placeholder {
        color: $text-color-lighter;

        // Light theme styles are sufficient
      }
    }

    &-send {
      position: absolute;
      right: 12px;
      bottom: 12px;
      background-color: $primary-color;
      color: white;
      border: none;
      cursor: pointer;
      font-size: 18px;
      padding: 8px;
      border-radius: 50%;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color $transition-speed;

      .tgs-ai-chat--small & {
        right: 10px;
        bottom: 10px;
        font-size: 16px;
        width: 32px;
        height: 32px;
      }

      &:hover {
        background-color: darken($primary-color, 10%);
      }

      &:disabled {
        background-color: $text-color-lighter;
        cursor: not-allowed;

        // Light theme styles are sufficient
      }
    }
  }

  &__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
    color: $text-color-lighter;
    font-size: 14px;

    // Light theme styles are sufficient

    &-dots {
      display: flex;
      margin-left: 8px;

      &-dot {
        width: 6px;
        height: 6px;
        background-color: $text-color-lighter;
        border-radius: 50%;
        margin: 0 2px;
        animation: tgs-ai-chat-pulse 1.5s infinite;

        // Light theme styles are sufficient

        &:nth-child(2) {
          animation-delay: 0.2s;
        }

        &:nth-child(3) {
          animation-delay: 0.4s;
        }
      }
    }
  }
}

@keyframes tgs-ai-chat-pulse {
  0%, 100% {
    opacity: 0.4;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

// Welcome screen styles
.tgs-ai-chat {
  &__welcome-screen {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    padding: 20px;
    background-color: $bg-color;

    // Light theme styles are sufficient

    &-content {
      max-width: 600px;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;

      .tgs-ai-chat--small & {
        max-width: 100%;
      }
    }

    &-form {
      width: 100%;
      margin-bottom: 32px;
    }

    &-input-wrapper {
      position: relative;
      width: 100%;
    }

    &-input {
      width: 100%;
      height: 50px;
      padding: 0 50px 0 16px;
      border: 1px solid $border-color;
      border-radius: $border-radius;
      font-size: 16px;
      color: $text-color;
      background-color: $bg-color;
      transition: border-color $transition-speed, box-shadow $transition-speed;

      &:focus {
        outline: none;
        border-color: $primary-color;
        box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
      }

      // Light theme styles are sufficient
    }

    &-submit {
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background-color: $primary-color;
      color: white;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: background-color $transition-speed;

      &:hover {
        background-color: darken($primary-color, 10%);
      }

      &:disabled {
        background-color: $text-color-lighter;
        cursor: not-allowed;
      }

      i {
        font-size: 18px;
      }
    }

    &-suggestions {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      justify-content: center;
      margin-top: 20px;
    }

    &-suggestion {
      padding: 8px 16px;
      background-color: $bg-color-light;
      border: 1px solid $border-color;
      border-radius: $border-radius;
      font-size: 14px;
      color: $text-color;
      cursor: pointer;
      transition: background-color $transition-speed, border-color $transition-speed;

      &:hover {
        background-color: $bg-color-dark;
        border-color: darken($border-color, 10%);
      }

      // Light theme styles are sufficient
    }
  }
}

.markdown-content {
  table {
    border: none;
    font-size: 14px;
    border-collapse: collapse;
    border-spacing: 0;
    min-width: 100%;
    width: -webkit-max-content;
    width: -moz-max-content;
    width: max-content;
    max-width: -webkit-max-content;
    max-width: -moz-max-content;
    max-width: max-content;
  }
    th:first-child,
    td:first-child {
    border-left: none;
  }
    th:last-child,
    td:last-child {
    border-right: none;
  }
    tr:first-child th {
    border-top: none;
  }
    tr:last-child td {
    border-bottom: none;
  }
    thead th {
    padding: 12px 16px;
    background-color: #ededed;
    border: 1px solid #e0e0e0;
  }
    tbody td {
    padding: 12px 16px;
    color: var(--yb-md-text-color);
    border: 1px solid #e0e0e0;
    background-color: #fff;
  }
  a {
    color: #EF3964;
  }
  ol {
    list-style-type: disc;
    padding-left: 20px;
    margin: 16px 0;
  }
  p {
    font-weight: 400;
    line-height: 2.0;
  }
  li {
    margin: 16px 0;
  }
}