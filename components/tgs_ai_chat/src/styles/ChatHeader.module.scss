@import './variables.scss';

.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 76px;
    padding: 0 24px;
    .title {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    &.small {
        flex-direction: column;
        height: auto;
        padding: 0;

        .title {
            box-sizing: content-box;
            font-size: 14px;
            padding: 12px 0;
            margin-left: 20px;
            width: calc(100% - 24px);
            text-align: left;
            order: 2;
        }

        .actions {
            width: calc(100% - 40px);
            justify-content: flex-end;
            order: 1;
            padding: 16px 0;
            height: 40px;
            box-sizing: content-box;
        }
    }

    &.app {
        height: 60px;
        
        .title {
            box-sizing: border-box;
            font-size: 14px;
            padding: 12px 0;
            margin-left: 20px;
            width: calc(100% - 24px);
            text-align: left;

            &+.actions {
                .btnTxt {
                    display: none;
                }
            }
        }
        .actions {
            padding: 12px 0;
            height: 40px;
            box-sizing: border-box;
            margin: 0;
            .actionBtn {
                margin: 0;
                font-size: 14px;
                padding: 0 12px;
                height: 40px;
            }
        }
    }
}

.emptyState {
    margin: 0 auto;
    text-align: center;
    padding: 12px 0;
    i {
        font-size: 24px;
        color: #999;
    }
}

.title {
    font-size: 18px;
    font-weight: 600;
    color: #222;
}

.actions {
    display: flex;
    align-items: center;
    gap: 10px;
}
.quotaBtn {
    position: relative;
    margin: 20px 0;
    height: 40px;
    background: #EFF6FF;
    color: #1f1a1b;
    font-weight: 400;
    border: none;
    border-radius: 8px;
    padding: 6px 16px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.2s;
    display: flex;
    align-items: center;
    gap: 4px;
    
    .popupText {
        position: absolute;
        top: calc(100% + 8px);
        left: 50%;
        transform: translateX(-50%);
        padding: 8px 12px;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        font-size: 13px;
        white-space: nowrap;
        z-index: 10;
        
        .popupTextTitle {
            position: relative;
            color: #fff;
            z-index: 11;
        }
        .popupTextBg {
            position: absolute;
            top: 0;
            left: 50%;
            width: 100%;
            height: 100%;
            transform: translateX(-50%);
            background: #1F1A1B;
            padding: 8px 12px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            font-size: 13px;
            opacity: 0.8;
            z-index: 0;
            
            &::before {
                content: '';
                position: absolute;
                top: -4px;
                left: 50%;
                transform: translateX(-50%) rotate(45deg);
                width: 8px;
                height: 8px;
                background: #1F1A1B;
            }
        }
    }

    .iconTag {
        color: #4F6BFE;
        font-weight: normal;
        font-size: 16px;
    }

    &.smallBtn {
        margin-top: 16px;
        font-size: 14px;
        padding: 0 12px;
        height: 40px;

        i {
            font-size: 16px;
        }
    }
}
.actionBtn {
    margin: 20px 0;
    height: 40px;
    background: #fff;
    color: #1f1a1b;
    font-weight: 700;
    border: none;
    border-radius: 8px;
    padding: 6px 12px;
    font-size: 16px;
    cursor: pointer;
    transition: background 0.2s;
    i {
        margin-right: 6px;
        font-weight: normal;
        font-size: 18px;
        vertical-align: middle;
    }
    &:hover {
        background: #dbeafe;
    }

    &.smallBtn {
        margin-top: 16px;
        font-size: 14px;
        padding: 0 12px;
        height: 40px;

        i {
            font-size: 16px;
        }
    }
}

.vip-info-box {
    position: absolute;
    top: calc(100% + 8px);
    left: 44px;
    transform: translateX(-50%);
    width: 300px;
    padding: 10px;
    border-radius: 12px;
    background: #ffffff;
    box-shadow: 0px 1px 10px 0px #00000026;
    z-index: 10;
    &.small {
        left: 150px;
    }
    .vip-info-inner-box {
        display: flex;
        justify-content: space-around;
        align-items: center;
        width: 280px;
        height: 50px;
        border-radius: 6px;
        background: linear-gradient(137.9deg, #f1f3ff 17.88%, #f7edff 79.03%);

        .title-logo {
            display: block;
            margin: auto 0;
            width: 101px;
            height: 20px;
        }

        .title-text {
            height: 17px;
            font-size: 12px;
            line-height: 16.8px;
            color: #797676;
        }

        .box-button {
            width: 70px;
            height: 34px;
            border-radius: 6px;
            background: linear-gradient(88.07deg, #3f61ff 1.03%, #a840ff 98.37%);
            color: #ffffff;
            text-align: center;
            font-size: 12px;
            line-height: 34px;
            cursor: pointer;
        }
    }

    .vip-info-message {
        padding-top: 15px;
        padding-bottom: 5px;
        .message-item {
            display: flex;
            align-items: center;
            height: 20px;
            .count-icon {
                width: 20px;
                height: 20px;
                display: inline-block;
                margin-right: 5px;
            }
            &:last-child {
                margin-top: 10px;
            }
        }
    }
}

.dropdownWrapper {
    position: relative;
}

.dropdown {
    position: absolute;
    right: 0;
    top: 110%;
    width: 340px;
    background: #fff;
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
    z-index: 20;
    padding: 0;
    overflow: hidden;

    &.rightDropdown {
        right: -16px;
        top: calc(100% - 15px);
    }
    &.small {
        top: 6px;
        right: -371px;
    }
}

.dropdownScrollWrap {
    max-height: 580px;
    overflow-y: auto;
}

.dropdownTitle {
    position: static;
    top: auto;
    z-index: auto;
    background: #fff;
    font-size: 16px;
    font-weight: 700;
    color: #222;
    padding: 15px;
    border-bottom: 1px solid #e9e8e8;
    letter-spacing: 1px;
}

.dropdownListWrap {
    padding: 4px 6px 0 4px;
    max-height: 466px;
}

.dropdownList {
    padding: 8px 0 8px 0;
    background: #fff;
    // height: 284px;
    max-height: 458px;
    overflow-y: auto;

    &::-webkit-scrollbar {
        width: 8px;
        background: transparent;
    }
    &::-webkit-scrollbar-thumb {
        background: #d1d5db;
        border-radius: 4px;
    }
    &::-webkit-scrollbar-track {
        background: transparent;
    }
}

.dropdownItem {
    background: #fff;
    margin: 0 0 0 15px;
    border-radius: 8px;
    // box-shadow: 0 1px 4px rgba(0,0,0,0.03);
    padding: 10px 10px;
    display: flex;
    align-items: center;
    transition:
        box-shadow 0.2s,
        background 0.2s;
    cursor: pointer;
    line-height: 1.43;

    .titleWarp {
        line-height: 1.43;
        font-size: 14px;
        color: #1f1a1b;
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .deleteBtn {
        opacity: 0;
        margin-left: 8px;
        transition: opacity 0.2s;
        border: none;
        background: none;
        cursor: pointer;
    }

    &:hover {
        background: #f3f4f6;

        .deleteBtn {
            opacity: 1;
        }
    }

    &.active {
        background: #f7f7f7;
        .titleWarp {
            color: #1f1a1b;
        }
    }
}

.dropdownItemWrap {
    margin-right: 6px;
}

.confirmBox {
    padding: 24px;

    .title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;

        i {
            font-size: 24px;
            color: #ef3964;
        }

        .tip {
            font-size: 18px;
            font-weight: 600;
            color: #1f1a1b;
        }
    }

    .mainWrap {
        margin-bottom: 24px;

        p {
            font-size: 16px;
            color: #1f1a1b;
            line-height: 1.5;
        }
    }

    .footerWrap {
        display: flex;
        justify-content: flex-end;
        gap: 16px;

        button {
            padding: 8px 24px;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .cancelBtn {
            border: 1px solid #e9e8e8;
            background: #fff;
            color: #1f1a1b;

            &:hover {
                border-color: #ef3964;
                color: #ef3964;
            }
        }

        .confirmBtn {
            border: 1px solid #ef3964;
            background: #ef3964;
            color: #fff;

            &:hover {
                background: #d7335a;
                border-color: #d7335a;
            }
        }
    }
}

.loadingMore {
    padding: 12px;
    text-align: center;
    color: #999;
    font-size: 13px;
    border-top: 1px solid #f0f0f0;
}

.loadingText {
    display: inline-flex;
    align-items: center;
    gap: 4px;

    &::after {
        content: '';
        width: 12px;
        height: 12px;
        border: 2px solid #999;
        border-top-color: transparent;
        border-radius: 50%;
        animation: spin 0.8s linear infinite;
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.dropdownMask {
    position: fixed;
    inset: 0;
    z-index: 19;
    background: transparent;
}

.timeGroupTabs {
    display: flex;
    gap: 8px;
    padding: 8px 16px;
    border-bottom: 1px solid #e5e7eb;
}

.timeGroupTab {
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 14px;
    color: #6b7280;
    background: transparent;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
        color: #374151;
        background: #f3f4f6;
    }

    &.active {
        color: #2563eb;
        background: #eff6ff;
    }
}

.groupLabel {
    font-weight: 600;
    font-size: 12px;
    color: #797676;
    margin: 18px 0 18px 18px;
    padding-left: 8px;
    line-height: 1.6;
    background: transparent;
}

.modalOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.2s ease-in-out;
}

.modalContent {
    background: #fff;
    border-radius: 12px;
    width: 600px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
    position: relative;
    animation: slideIn 0.2s ease-in-out;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}
