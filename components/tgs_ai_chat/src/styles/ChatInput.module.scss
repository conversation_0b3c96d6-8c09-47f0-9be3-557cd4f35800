@import './variables.scss';

.input {
  position: relative;
  margin: 0 auto;
  border-radius: 16px;
  background-color: #fff;
  max-height: 200px;
  box-shadow: 0px 1px 10px 0px #0000001A;
  border: none;


  .tgs-ai-chat--small & {
    padding: 12px 12px 12px 120px;
    position: sticky;
    bottom: 0;
  }
}

.wrapper {
  position: relative;
  display: flex;
  padding: 15px;
  flex-direction: column;
  border-radius: 16px;
  max-width: 760px;
  min-width: 313px;
  width: 100%;
  box-sizing: border-box;
}

.textarea {
  margin-bottom: 10px;
  width: 100%;
  min-height: 60px;
  max-height: 122px;
  font-size: 15px;
  resize: none;
  border: none;
  border-radius: $border-radius;
  font-size: 14px;
  line-height: 1.5;
  color: $text-color;
  outline: none;
  transition: border-color $transition-speed;

  &::-webkit-scrollbar {
    width: 8px;
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 4px;
  }
  &::-webkit-scrollbar-track {
    background: transparent;
  }

  .tgs-ai-chat--small & {
    min-height: 48px;
    padding: 10px 50px 10px 10px;
    font-size: 13px;
  }

  &:disabled {
    background-color: #fff;
    color: #A5A3A4;
    cursor: not-allowed;
  }

  &:focus {
    border-color: $primary-color;
  }

  &::placeholder {
    font-size: 14px;
    font-family: Source Han Sans CN;
    color: $text-color-lighter;

    // Light theme styles are sufficient
  }
}

.send {
  background-color: #4d6bfe;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 18px;
  padding: 8px;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color $transition-speed;
  margin-left: auto;

  .tgs-ai-chat--small & {
    right: 10px;
    bottom: 10px;
    font-size: 16px;
    width: 32px;
    height: 32px;
  }

  &:hover {
    background-color: #4d6bfe;
    color: white;
  }

  &:disabled {
    background-color: #E9E8E8;
    color: #A5A3A4;
    // cursor: not-allowed;
    // Light theme styles are sufficient
    .tgs-ai-chat--small & {
      right: 10px;
      bottom: 10px;
      font-size: 16px;
      width: 32px;
      height: 32px;
    }
  }
}

.sending {
  padding: 0;
  width: 38px;
  height: 38px;
  font-size: 38px;
  color: #000000;
  border-radius: 50%;
  border: none;
  background-color: transparent;
  cursor: pointer;

  .tgs-ai-chat--small & {
    right: 10px;
    bottom: 10px;
    font-size: 32px;
    width: 32px;
    height: 32px;
    i {
      margin-top: -2px;
    }
  }
  i {
    display: block;
    margin-top: -4px;
  }
}

.counter {
  font-size: 12px;
  color: $text-color-lighter;
  text-align: right;
  margin-top: 4px;
  padding-right: 12px;
}

.changeModule {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 12px;
}

.deepThinkBtn {
  background: #f3f4f6;
  color: #374151;
  font-weight: 400;
  border: 1px solid transparent;
  border-radius: 20px;
  padding: 4px 14px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background 0.2s, color 0.2s;

  &:hover {
    background: #dbeafe;
  }
}

.deepThinkBtnActive {
  font-size: 14px;
  background: #EFF2FD !important;
  color: #4F6BFE !important;
  border: 1px solid #4F6BFE;
  font-weight: 400;
}

.deepThinkIcon {
  font-size: 18px;
  margin-right: 6px;
}

.large {
  max-width: 760px;
  min-width: 313px;
  width: 100%;
}

.small {
  width: 100%;
  min-width: 313px;
  max-width: 313px;
  .send {
    width: 32px;
    height: 32px;
    right: 10px;
    bottom: 10px;
    font-size: 16px;
  }
  // .wrapper {
    
  // }
  &.input {
    box-shadow: none;
    border: 1px solid #E9E8E8
  }
}
