@import './variables.scss';


.messageList {
  flex: 1 1 0;
  flex-direction: column;
  align-items: center;
  width: 100%;
  min-height: 0;
  overflow-y: auto;
  gap: 16px;
  padding: 16px 16px 28px;
  height: 100%;
  overflow-x: hidden;

  .tgs-ai-chat--small & {
    padding: 12px;
    gap: 12px;
    padding-bottom: 88px;
  }

  @supports (-webkit-touch-callout: none) {
    padding-right: 22px;
  }

  &::-webkit-scrollbar {
    width: 8px;
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 4px;
  }
  &::-webkit-scrollbar-track {
    background: transparent;
  }
}

.scrollToBottomBtn {
  position: absolute;
  bottom: 200px;
  left: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  font-size: 20px;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0px 1px 10px 0px #0000001A;
  transform: translateX(-50%);
  cursor: pointer;
  z-index: 10;
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: $text-color-lighter;
  text-align: center;
  padding: 0 20px;

  // Light theme styles are sufficient
}

.emptyIcon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.emptyText {
  font-size: 16px;
  max-width: 300px;
}

.messageAi {
  max-width: 760px;
}

.messageUser {
  max-width: 620px;
}

.small .messageAi {
  max-width: 313px;
}

.small .messageUser {
  width: 270px;
}

.large .messageAi {
  max-width: 760px;
}

.large .messageUser {
  max-width: 620px;
}

.messageListInner {
  width: 100%;
  margin: 0 auto;
  &.app {
    
  }
}

.messageListInner.large {
  max-width: 760px;
  min-width: 313px;
}

.messageListInner.small {
  max-width: 313px;
  min-width: 313px;
}

.messageContainer {
  width: 100%;
}

.messageInner {
  margin: 0 auto;
  max-width: 760px;
}

.container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.reasoningContent {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 12px;
}

.reasoningHeader {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #f3f4f6;
  border-bottom: 1px solid #e5e7eb;

  i {
    margin-right: 8px;
    color: #6b7280;
  }

  span {
    font-weight: 500;
    color: #374151;
  }
}

.finalContent {
  background-color: #fff;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #52c41a;
  transition: all 0.3s ease;

  &.streaming {
    border-color: #52c41a;
    box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.1);
  }
}

.finalHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #52c41a;
  font-weight: 500;

  i {
    font-size: 1.2rem;
  }
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
  
  .loadingIcon {
    font-size: 24px;
    color: #999;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
