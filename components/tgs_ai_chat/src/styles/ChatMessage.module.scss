@import './variables.scss';

.collapsible {
  color: rgba(121, 118, 118, 1);
  background: #F0F3F5;
  border-radius: 16px;
  overflow: hidden;

  .collapsibleHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px 15px;
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s;
    background: #F0F3F5;
    color: #1F1A1B;


    .left {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      i {
        font-size: 18px;
      }
    }

    .icon-up,
    .icon-down {
      margin-left: 8px;
      font-size: 12px;
      color: #4C4849;
    }
  }

  .collapsibleContent {
    margin: 0 16px  16px 16px;
    border-top: 1px solid #e5e7eb;
  }
}

.messageWrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 32px;
}

.message {
  position: relative;
  border-radius: 8px;
  transition: all 0.3s ease;

  &.noBorder {
    background-color: transparent !important;
    padding: 0;
    margin-bottom: 8px;
    .content {
      color: #797676;
      font-size: 14px;
    }
  }

  &.streaming {
    opacity: 0.8;
  }

  &.user {
    padding: 0 12px;
    max-width: 760px;
    background-color: #EFF6FF;
    margin-left: auto;
    border-radius: 16px;
    p {
      line-height: 1.5;
      margin: 15px 0;
    }
    &.large {
      max-width: 620px;
    }
    &.small {
      max-width: 270px;
    }
  }

  &.system {
    margin-right: auto;
  }


  &.large {
    max-width: 760px;
  }

  &.small {
    max-width: 313px;
  }
}

.content {
  font-size: 16px;
  line-height: 1.5;
  word-break: break-word;

  .tgs-ai-chat--small & {
    padding: 10px 14px;
    font-size: 13px;
  }

}
.lack {
  display: flex;
  flex-direction: column;
  justify-content: center;
  border-radius: 8px;
  gap: 10px;
  margin-bottom: 20px;
  margin-top: 8px;
  font-size: 16px;

  .purchaseText {
    width: 100%;
  }

  .purchaseButton {
    width: 100px;
    height: 40px;
    font-weight: 700;
    border-radius: 6px;
    color: $primary-color-theme;
    background-color: $primary-color-theme-light;
    border: none;
    cursor: pointer;
  }
}
.actions {
  display: flex;
  gap: 12px;
  opacity: 1;
  pointer-events: auto;

  .actionButton {
    display: flex;
    gap: 4px;
    align-items: center;
    justify-content: center;
    padding: 0;
    border: none;
    background: transparent;
    color: #999;
    cursor: pointer;
    font-size: 14px;

    i {
      font-size: 16px;
    }
  }
}

.loading {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
}

.loadingDots {
  display: flex;
  gap: 4px;
}

.loadingDotsDot {
  width: 4px;
  height: 4px;
  background-color: currentColor;
  border-radius: 50%;
  animation: blink 1s infinite;
}

.error {
  color: #ff4d4f;
  display: flex;
  align-items: center;
  gap: 8px;
}

.meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
  font-size: 12px;
  color: #666;
  padding: 0 16px 8px;
}

.timestamp {
  color: #999;
}

.model {
  color: #666;
}

.aiPlain {
  width: 100%;
}

@keyframes blink {
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 1;
  }
}
