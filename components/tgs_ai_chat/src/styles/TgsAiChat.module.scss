@import './variables.scss';

.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-width: 313px;
  height: 100%;
  background: linear-gradient(277.67deg, #F8FBFD 72.22%, #FFFFFF 94.55%);
  position: relative;
  margin: 0 auto;
  &.small {
    width: 100%;
    margin: 0 auto;
    border-radius: 16px;
  }
  
  &.large {
    width: 100%;
    margin: 0 auto;
  }
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  color: $text-color-lighter;
  font-size: 14px;

  // Light theme styles are sufficient
}

.loadingDots {
  display: flex;
  margin-left: 8px;
}

.loadingDot {
  width: 6px;
  height: 6px;
  background-color: $text-color-lighter;
  border-radius: 50%;
  margin: 0 2px;
  animation: tgs-ai-chat-pulse 1.5s infinite;

  // Light theme styles are sufficient

  &:nth-child(2) {
    animation-delay: 0.2s;
  }

  &:nth-child(3) {
    animation-delay: 0.4s;
  }
}

.chatArea {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  flex: 1 1 0;
  min-height: 0;
  position: relative;
}

.chatArea.large {
  align-items: center;
}

.chatArea.small {
  align-items: flex-start;
}

.chatArea.app {
  align-items: flex-start;
}

.inputBar {
  position: sticky;
  width: 100%;
  margin-top: -16px;
  border-radius: 16px;
  z-index: 10;

  &.large {
    margin-left: -6px;
    width: 760px;
  }

  &.small {
    margin-left: 19px;
    width: 313px;
  }
  
  &.app {
    width: 100%;
    max-width: 760px;
    padding: 0 20px;
    margin: 0 auto;
  }
}

.inputBarRight {
  padding: 11px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #A5A3A4;
  gap: 4px;
}

.inputBarRightText {
  font-size: 12px;
}

.inputBarRightLink {
  font-size: 12px;
  cursor: pointer;
  text-decoration: underline;
}

.disclaimerContent {
  padding: 16px 0;
  max-width: 300px;

  p {
    margin-bottom: 16px;
    line-height: 1.6;
    color: #333;
    font-size: 13px;
  }

  ol {
    margin-bottom: 16px;
    padding-left: 20px;

    li {
      margin-bottom: 8px;
      line-height: 1.6;
      color: #333;
      font-size: 13px;
    }
  }
}

.disclaimerPopover {
  :global {
    .ant-popover-inner-content {
      padding: 12px 16px;
    }
    
    .ant-popover-title {
      padding: 12px 16px;
      font-size: 14px;
      font-weight: 500;
      border-bottom: 1px solid #f0f0f0;
    }
  }
}