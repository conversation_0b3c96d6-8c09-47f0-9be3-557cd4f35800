@import './variables.scss';
.render_core {
  height: 100%;
}
.templateWrap {
  margin-top: -10px;
  overflow: hidden;
  display: grid;
  gap: 12px;
  
  &.large {
    &.square {
      grid-template-columns: repeat(4, 1fr);
    }
    
    &.portrait {
      grid-template-columns: repeat(4, 1fr);
    }
    
    &.landscape {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  &.small {
    &.square {
      grid-template-columns: repeat(2, 1fr);
    }
    
    &.portrait {
      grid-template-columns: repeat(2, 1fr);
    }
    
    &.landscape {
      grid-template-columns: repeat(1, 1fr);
    }
  }
}

.templateItem {
  position: relative;
  border-radius: 8px;
  border: 1px solid #E5E7EB;
  overflow: hidden;

  .template {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    position: relative;
    z-index: 2;
    transition: opacity 0.3s ease-in-out;
    background: #f5f5f5;

    &.loading {
      opacity: 0;
    }

    &:not([src]), &[src=""], &[src="undefined"] {
      visibility: hidden;
    }

    &[src]:not([src=""]) {
      min-height: 100px;
      background: #f5f5f5;
      
      &:not(.loaded) {
        opacity: 0;
      }
    }
  }

  .shimmer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0) 100%
      );
      animation: shimmer 1.5s infinite;
    }
  }

  .templateTitle {
    width: 24px;
    height: 20px;
    z-index: 2;
    img {
      width: 100%;
      height: 100%;
    }
  }

  .templateInfo {
    z-index: 2;
  }
}

.templateInfo {
  position: absolute;
  width: 100%;
  height: 65%;
  bottom: 0;
  // inset: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: flex-end;
  padding: 15px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
  cursor: pointer;

  &.small {
    padding: 10px;
    .templateButton {
      width: 130px;
      height: 30px;
    }
  }

  .templateButtonWrap {
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  /* after 占位，补充hover区域*/
  &::after{
    position: absolute;
    content: '';
    /* 65% * 54% = 35.1% */
    top: -54%;
    left: 0;
    width: 100%;
    height: 54%;
    padding: 15px;
  }

  &:hover {
    opacity: 1;
  }
}

.templateTitle {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
  
  img {
    height: 24px;
  }
}

.templateButton {
  margin: 0 auto;
  height: 36px;
  width: 150px;
  background-color: rgba(255, 255, 255);
  color: #1F1A1B;
  border-radius: 6px;
  border: none;
  transition: background-color 0.2s ease;
  cursor: pointer;
  
  &:hover {
    color: $primary-color;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.poster_item {
  width: 100%;
}

