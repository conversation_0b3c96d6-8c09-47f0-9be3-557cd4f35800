/**
 * @description: AI设计模板标记
 * @return {*}
 */
export enum IAiDesignTemplateTagTypeEnum {
  MAIN_HEAD = 'main_head', // 主标题
  SUB_HEAD = 'sub_head', // 副标题
  THIRD_HEAD = 'third_head', // 正文标题
  BODY = 'body', // 正文内容
  TIME = 'time', // 时间
  INTRODUCTION = 'introduction', // 引言
}
export enum IAiDesignEcommerceTemplateTextTagTypeEnum {
  PRODUCT_NAME = 'product_name', // 商品名称
  PRODUCT_PRICE = 'product_price', // 商品价格
  PRODUCT_HIGHTLIGHT_POINT = 'product_highlight_point', // 商品亮点
  PRODUCT_SELL_POINT = 'product_sell_point', // 商品卖点
  PRODUCT_PROFIT_POINT = 'product_profit_point', // 商品利益点
}
/**
* @description: AI设计模板图片标记
* @return {*}
*/
export enum IAiDesignTemplateImgTagTypeEnum{
  PRIMARY_IMG = 'primary_img', // 主图
  LOGO = 'logo', // LOGO
  QRCODE = 'QRCode', // 二维码 
  IMGCONTAINER = 'imgContainer', // 图片容器，用来用户上传替换
  MATTINGIMGCONTAINER = 'mattingImgContainer', // 抠图图片容器
  PRIMARY_IMG_CONTAINER = 'primary_img_container', // 主图容器,用来ai生成替换
}
/**
* @description: AI设计模板svg标记
* @return {*}
*/
export enum IAiDesignTemplateSvgTagTypeEnum{
  MASKSVG = 'maskSvg', // 遮罩SVG
}


// 分为两部份，一部分为绘图，一部分为非绘图

export enum IAiDesignTemplateDrawTypeEnum {
  PIC_DRAW = '7', // 绘图海报
  PIC_RED_NOTE = '8', // 绘图小红书
  PIC_PRIOR = '9', // 绘图首图
  PIC_IMAGE_DRAW = '10', // 豆包3.0海报
  PIC_IMAGE_RED_NOTE = '11', // 豆包3.0小红书
  PIC_IMAGE_PRIOR = '12', // 豆包3.0首图
}

export enum IAiDesignTemplateTypeEnum {
  POSTER = '1', // 海报
  PPT = '2', // PPT
  PUBLIC_ACCOUNT_FIRST_IMAGE = '3', // 公众号首图
  MARKETING_LONG_IMAGE = '4', // 长图
  ECOMMERCE = '5', // 电商
  RED_BOOK = '6', // 小红书
}
export enum IAiDesignTemplateColorTagTypeEnum {
  SUBJECT_COLOR = 'subject_color', // 主题色
  AUXILIARY_COLOR = 'auxiliary_color', // 辅助色
}