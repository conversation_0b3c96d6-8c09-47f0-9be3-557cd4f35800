import { IChatCreateSessionResponse, IChatSessionListResponse, IDelSessionResponse, IDrawCheckResponse, ISaveImageResponse, IAddUserAssetResponse, IChatPromptSuggestionResponse, IChatListResponse } from './index';
import { DEFAULT_API_ENDPOINTS } from '../constants';

export interface IAppChatApi {
  initChatSession(prompt: string, sourceFrom: number): Promise<IChatCreateSessionResponse>;
  getHistoryMessages(session_id: string): Promise<IChatListResponse>;
  getSessionList(page_id: number, limit: number): Promise<IChatSessionListResponse>;
  getSuggestionsPrompt(): Promise<IChatPromptSuggestionResponse>;
  delSession(session_id: string): Promise<IDelSessionResponse>;
  drawCheck(group_id: string): Promise<IDrawCheckResponse>;
  addUserAsset(group_id: string, asset_id: string): Promise<IAddUserAssetResponse>;
  saveImage(ai_produce_draw_id: string): Promise<ISaveImageResponse>;
  getOssUploadPreviewToken(filename: string, prefix: string): Promise<any>;
  setPv(page_id: number, paras?: { additional?: { [key: string | number]: string | number } }): Promise<void>;
  saveAiDrawAsset(aiProduceDrawId: string): Promise<ISaveImageResponse>;
  getTemplInfo(params: { picId: string; upicId?: string; version_id?: string }): Promise<any>;
  getPresetDes(): Promise<any>;
  getGenerateNum(): Promise<any>;
  saveOutline(params: {
    id: number;
    group_id: string;
    titile?: string[];
    sed_title?: string[];
    thd_title?: string[];
    main_text?: string[];
    time?: string;
  }): Promise<any>;
  saveUserTempl(params: { startTime: number; aiproduce_type: number }, template: any): Promise<any>;
  downloadTempl(params: {
    upicId: string;
    aiproduce_id?: number;
    dpi?: number;
    format?: string;
    force?: number;
    version_id?: string;
    source?: number;
    ticket?: string;
    rendstr?: string;
    ai_ppt_id?: string;
  }): Promise<any>;
  downloadFlagCheck(param: {
    upicId: string;
    jobId: string;
    version_id?: number;
    aiproduce_id?: number;
    ai_ppt_id?: string;
  }): Promise<any>;
  getTemplateList(params: { id: number }): Promise<any>;
  putPreviewPoster(data: FormData): Promise<any>;
  getAiProduceInfo(params: { id: string }): Promise<any>;
  replaceTemplate(params: { id: string }): Promise<any>;
  getConcurrencyTaskInfo(params: {
    name: string;
    sceneCategorize?: string;
    festival_name?: string;
    ai_template_type: string;
    ai_judge?: string;
    group_id?: string;
    image_id?: string;
  }): Promise<any>;
  getTemplateMore(params: { group_id: string; ai_template_type: string }): Promise<any>;
  getUserGenerateRecord(params: { page: number; type?: string; limit?: number }): Promise<any>;
  getPPTTemp(params: { page: number; pageSize: number }): Promise<any>;
  getPPTOutLine(id: string): Promise<any>;
  savePicId(params: { ai_ppt_id: string; picId: string }): Promise<any>;
  searchAiProduceInfo(params: { id: string }): Promise<any>;
  startGenerateTaskNew(params: {
    name: string;
    group_id: string;
    ai_judge: string;
    ai_template_type: string;
  }): Promise<any>;
  checkAiDrawResultV1(group_id: string): Promise<any>;
  getReletedTempNew(params: { group_id: string }): Promise<any>;
  getGenerateTaskData(params: { group_id: string; index?: number }): Promise<any>;
  getAiContentListNew(params: { group_id: string, aid?: string }): Promise<any>;
  getTemplateMoreNew(params: { group_id: string, is_prepare?: number | string }): Promise<any>;
  checkAiDrawResult(group_id: string): Promise<any>;
}

export type AppChatApiConstructor = new (
  apiBaseUrl: string,
  customEndpoints?: Partial<typeof DEFAULT_API_ENDPOINTS>,
  customOptions?: any
) => IAppChatApi; 