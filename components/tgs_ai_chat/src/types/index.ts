import { MessageStatus } from '../constants';

/**
 * @ts-nocheck
 * popMessage 类型
 */

export type MessageInstance = {
  error: (content: string) => void;
  info: (content: string) => void;
  success: (content: string) => void;
  warning: (content: string) => void;
  open: (config: {
    content: string;
  }) => void;
};
/**
 * 图片生成状态
 */
export interface IDrawCheckData {
  ai_produce_id: string;
  preview: string;
  status: string;
  ai_produce_draw_id: number;
  template_id: string;
  width: number;
  height: number;
  title_color: string;
  sed_title_color: string;
}

/**
 * 图片生成数据
 */
export interface IDrawData {
  aiproduce_draw_id: string;
  draw_template_style: string;
  group_unique: string;
  height: string;
  width: string;
  id: string;
  path?: string;
  preview: string;
  templ_id?: string;
  user_templ_id?: string;
  user_asset_id?: ''
  status?: string;
  produce_type?: string;
  colour?: {
    title: string;
    auxiliary: string;
  };
}
/**
 * Chat message interface
 */

export interface IChatMessage {
  status: MessageStatus;
  /**
   * 消息id （敏感词不生成messageId,id为前端处理，以SENSITIVE-开头）
   */
  message_id: string;
  parent_id?: string;
  model_id?: string;
  role: 'USER' | 'SYSTEM';
  content: string;
  thinking_enabled: string;
  thinking_content: string;
  group_unique?: string;
  /**
   * 图片列表
   */
  draw?: IDrawData[];
  /**
   * 模板列表
   */
  lack?: boolean;
  templateInfo?: {
    templateIds: StreamTemplateInfo[];
    produceType: number;
    maxSize: number;
  };
  type?: string;
  /**
   * AIDesign 替换文本内容
   */
  contents?: any;
}

export interface StreamTemplateInfo {
  picId: string;
  aiProduceId: string;
  draw_template_style: number;
  produce_type: number;
}

/**
 * Chat session interface
 */
export interface IChatSession {
    created: string;
    current_message_id: string;
    session_id: string;
    messages: IChatMessage[];
    title: string;
    title_type: string;
    updated: string;
}

// code 1 成功， 0 失败
/**
 * Chat API response interface
 */
export interface IChatListResponse {
  code: number;
  data: {
    list: IChatMessage[];
  };
  msg: string;
}

export interface IChatPromptSuggestionResponse {
  code: number;
  data: {
    list: IPromptSuggestion[];
  };
  msg: string;
}

export interface IDrawCheckResponse {
  code: number;
  data: IDrawCheckData[]
  msg: string;
}

export interface IAddUserAssetResponse {
  code: number;
  data: {
    ua_id: string;
    picId: number;
    ai_produce_draw_id: string;
    message: string;
  };
  msg: string;
}
/**
 * Chat API response interface
 */
export interface IChatSessionListResponse {
  code: number;
  data: {
    list: IChatSession[];
  };
  msg: string;
}

/**
 * Chat create session response interface
 */
export interface IChatCreateSessionResponse {
  code: number;
  msg: string;
  data: {
    session_id: string;
  };
}

/**
 * Chat settings interface
 */
export interface IChatSettings {
  showTimestamp: boolean;
  enableMarkdown: boolean;
  enableCodeHighlighting: boolean;
  theme?: 'light' | 'dark';
}

export interface IPromptSuggestion {
  key: string;
  label: string;
  prompt: string;
}

export interface ISaveImageResponse {
  code: number;
  data: {
    ua_id: string;
    picId: number;
    ai_produce_draw_id: string;
    message: string;
  }[];
  msg: string;
}

export interface IDelSessionResponse {
  code: number;
  data: {
    session_id: string;
  };
  msg: string;
}

/**
 * Props for the TgsAiChat component
 */
export interface IModalProps {
  open: boolean;
  centered?: boolean;
  footer?: React.ReactNode | null;
  width?: number;
  styles?: Record<string, any>;
  bodyStyle?: Record<string, any>;
  style?: Record<string, any>;
  onCancel?: () => void;
  children?: React.ReactNode;
}

export interface IEditorController {
  // 文本编辑相关
  enterTextEdit: (asset: any) => void;
  exitTextEdit: (asset: any) => void;
  updateText: (text: string, asset: any) => void;

  // 图片裁剪相关
  enterImageClip: (asset: any) => void;
  exitImageClip: (asset: any) => void;
  updateImageClip: (clipData: any) => void;

  // 画布控制相关
  blurSelect: () => void;
  updateCanvas: (type: string, data: any) => void;

  // 组操作相关
  enterGroupEdit: (groupName: string) => void;
  exitGroupEdit: (groupName: string) => void;
  updateGroupContent: (groupName: string, content: any) => void;

  // 添加图片
  addPic: (asset: any) => void;

  // 添加表格
  addTable: (asset: any) => void;

  // 记录当前编辑器状态
  recordCurrentState: (messageId: string) => void;
}

export interface IPopMessage {
  error: (message: string) => void;
  info: (message: string) => void;
  success: (message: string) => void;
  warning: (message: string) => void;
}

export interface ITgsAiChatProps {

  /**
   * Additional CSS class name
   */
  className?: string;

  /**
   * Custom API endpoints for chat functionality
   */
  apiEndpoints?: {
    initSession?: string;
    sendMessage?: string;
    getHistory?: string;
    getModels?: string;
  };

  /**
   * Initial chat settings
   */
  initialSettings?: Partial<IChatSettings>;

  /**
   * Custom authentication handler
   */
  authHandler?: {
    isAuthenticated: () => boolean;
    login: () => void;
    getUserInfo: () => { id: string | number; name: string } | null;
  };

  /**
   * Custom welcome message
   */
  welcomeMessage?: string;

  /**
   * Callback when a message is sent
   */
  onMessageSent?: (message: IChatMessage) => void;

  /**
   * Callback when a message is received
   */
  onMessageReceived?: (message: IChatMessage) => void;

  /** 用户信息，未登录时为 undefined/null */
  userInfo: any;

  /** 打开登录弹窗的方法 */
  openLoginModel?: () => void;

  /** 来源
   * 1.工作台， 2.编辑器
   * */
  sourceFrom: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13 | 14 | 15 | 16;

  /**
   * Size of the chat component
   */
  size: 'large' | 'small' | 'app';

  /**
   * Modal component
   */
  Modal: React.ComponentType<IModalProps>;

  /**
   * Popover component
   */
  Popover: React.ComponentType<any>;

  /**
   * Popover message component
   */
  popMessage: IPopMessage;

  AppParams?: {
      AppChatApi: any;
      fetchStream: any;
      ChatHeader: any;
      handleToEditor: (picUrl: string, id: string, width: number, height: number) => void;
  }

  /**
   * 编辑器控制器
   */
  editorController?: IEditorController;

  /**
   * 弹出显示图片上传空间不足的提示
   */
  uploadLimitPopup?: () => void;

  /**
   * 弹出显示充值弹窗
   */
  rechargeModalPopup?: (origin: string, callback: (isRecharge: boolean) => void) => void;
}
