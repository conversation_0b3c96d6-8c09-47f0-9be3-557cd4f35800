{"name": "@tgs/ai_chat", "version": "0.4.83", "description": "AI Chat component for TGS applications", "main": "dist/ai_chat.js", "module": "src/index.ts", "types": "lib/index.d.ts", "files": ["dist", "lib", "README.md"], "scripts": {"dev": "vite", "dev:markdown": "vite example/markdown.html", "build": "vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "release": "pnpm version patch && pnpm build && pnpm publish --registry=http://*************:4873/ --no-git-checks"}, "dependencies": {"@tgs/canvas": "workspace:^", "@tgs/general_components": "workspace:^", "classnames": "^2.3.2", "markdown-it": "^14.1.0", "marked": "^9.1.6", "vite-plugin-commonjs": "^0.10.3"}, "peerDependencies": {"react": "^17.0.2", "react-dom": "^17.0.2", "ahooks": "^3.7.4"}, "devDependencies": {"@tgs/types": "workspace:^", "@types/react": "^17.0.65", "@types/react-dom": "^17.0.20", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.2.1", "antd": "^5.24.16", "autoprefixer": "^10.4.20", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.41", "react": "^17.0.2", "react-dom": "^17.0.2", "sass": "^1.32.8", "sass-loader": "^13.0.2", "typescript": "^5.2.2", "vite": "^5.0.0", "vite-plugin-css-injected-by-js": "^3.5.1"}}