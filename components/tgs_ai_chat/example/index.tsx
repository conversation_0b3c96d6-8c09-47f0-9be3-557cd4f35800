import { TgsAiChat } from '../src';
import React, { useEffect } from 'react';
import ReactDOM from 'react-dom';
import { message, Modal, Popover } from 'antd';
import { 
  TableData, 
  parseTableFromHtml, 
  parseTableFromText, 
  convertTableToText 
} from '../src/utils/tableUtils';



// 处理粘贴事件
const handlePasteData = (event: ClipboardEvent): TableData | null => {
  const clipboardData = event.clipboardData;
  if (!clipboardData) return null;
  
  // 获取HTML格式的数据
  const htmlData = clipboardData.getData('text/html');
  if (htmlData) {
    console.log('Pasted HTML:', htmlData);
    return parseTableFromHtml(htmlData);
  }
  
  // 如果没有HTML数据，尝试处理纯文本（制表符分隔）
  const textData = clipboardData.getData('text/plain');
  if (textData) {
    console.log('Pasted Text:', textData);
    return parseTableFromText(textData);
  }
  
  return null;
};

const BigSize = {
  width: '1360px',
  size: 'large' as const,
  sourceFrom: 1 as const,
}

const SmallSize = {
  width: '360px',
  size: 'small' as const,
  sourceFrom: 2 as const,
}

const ExampleApp: React.FC = () => {
  const size = BigSize;
  // const size = SmallSize;
  
  useEffect(() => {
    const handlePaste = (e: ClipboardEvent) => {
      const parsedTable = handlePasteData(e);
      if (parsedTable) {
        console.log('Parsed table from paste:', parsedTable);
        
        const tableText = convertTableToText(parsedTable);
        console.log('Table as text:', tableText);
        
        // 这里可以进一步处理解析出的表格数据
        // 比如发送到聊天框、保存到状态等
      }
    };
    
    addEventListener('paste', handlePaste);
    return () => {
      removeEventListener('paste', handlePaste);
    };
  }, []);

  return (
    <div style={{ width: size.width, height: '100vh', margin: '0 auto' }}>
      <div style={{ padding: '20px', backgroundColor: '#f5f5f5', marginBottom: '10px' }}>
        <h3>粘贴测试区域</h3>
        <p>在此页面任意位置粘贴表格数据（支持网页表格、Excel、纯文本等格式），查看控制台输出解析结果。</p>
      </div>
      <TgsAiChat 
        size={size.size}
        sourceFrom={size.sourceFrom}
        Modal={Modal as any}
        Popover={Popover}
        popMessage={message}
        userInfo={true}
        openLoginModel={() => console.log('openLoginModel')}
        onClose={() => console.log('Chat closed')}
      />
    </div>
  );
};

ReactDOM.render(
  <ExampleApp />,
  document.getElementById('root')
);
