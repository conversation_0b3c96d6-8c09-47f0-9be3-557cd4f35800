import React from 'react';
import ReactDOM from 'react-dom';
import { TextResult } from '../../src/components/ResultText';

const markdownContent = `
# 高效制作例会海报指南（附排版建议）

## 一、核心信息梳理
**主标题**：第7期学术例会（总第52期）
**核心要素**：
- 📅 时间：2025年6月6日 13:30-15:30
- 💻 地点：腾讯会议 742-323-020
- 🎯 会议流程：
\`\`\`
13:30-13:50 个人周报汇报
13:50-14:50 研一同学选题汇报（4位）
\`\`\`

## 二、设计路径推荐
### 方法1：模板快速生成
1. 登录[图怪兽官网](https://818ps.com)
2. 搜索关键词：\`学术会议/商务例会/线上会议海报\`
3. 推荐模板类型：
- 🔵 蓝色系学术风模板（推荐指数：★★★★★）
- 🟠 橙色活力版式（推荐指数：★★★★）

### 方法2：DIY设计技巧
- **版式布局**：采用「三段式结构」，顶部放主视觉，中部时间轴，底部会议号
- **字体搭配**：标题用思源黑体Bold，正文用阿里巴巴普惠体
- **视觉强化**：在时间数字旁添加⏰图标，会议号区域添加💻emoji

## 三、制作小贴士
1. 建议将会议号放大至正文1.5倍字号
2. 时间轴采用「阶梯式排列」增强阅读节奏
3. 可添加「扫码入会」提示框（模板库含现成组件）

💡 **特别提醒**：图怪兽智能设计功能正在升级接入中，当前可使用200万+专业模板库，支持3分钟快速出图，输入会议信息即可自动排版，立即体验高效设计 ➔ [进入模板库](https://818ps.com)
`;

const App = () => {
  return (
    <div className="p-8 max-w-4xl mx-auto">
      <TextResult 
        item={{
          id: 'markdown-example',
          content: markdownContent,
          code: 1
        }}
      />
    </div>
  );
};

ReactDOM.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
  document.getElementById('root')
); 