{"name": "@tgs/ips_text", "version": "0.1.8", "description": "canvas富文本编辑器", "main": "src/index.ts", "type": "module", "files": ["/src", "/dist"], "scripts": {"dev": "vite", "build": "tsc && vite build", "lib": "vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "release": "pnpm publish --registry=http://*************:4873/ --no-git-checks"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "fabric": "6.0.0-beta13", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.0.2", "vite": "^4.4.5"}, "dependencies": {"lodash-es": "^4.17.21", "svgpath": "^2.6.0", "@tgs/types": "workspace:^0.1.6", "fast-deep-equal": "^3.1.3", "klona": "^2.0.6"}}