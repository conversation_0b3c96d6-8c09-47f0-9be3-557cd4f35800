import { IAiDesignPageTagTypeEnum } from '../aiDesign';
import type { IPageAnimation } from './animation';
import type { IPageAudio } from './audio';
import type { IColor } from './color';
import type { IGifInfo } from './gifInfo';

export type TPageType = 'board' | ''

export interface IPageAttr {
    backgroundColor?: IColor[];
    backgroundImage?: {
        resId?: string;
        [key: string]: any;
    }[];
    backgroundOpacity?: boolean[];
    pageInfo?: {
        [key: string]: any;
        gifInfo?: IGifInfo | null;
        /** [key] 关键帧动效 */
        k?: {
            /** [in] 进场动画 */
            i: IPageAnimation;
            /** [stay] 驻场动画 */
            s: IPageAnimation;
            /** [out] 出场动画 */
            o: IPageAnimation;
        };
        rt_previewAnimaton?: IPageAnimation;
        pageTime?: number;
        title?: string;
        rt_isUserUpdatedTime?: boolean;
        rt_page_ppt_mark?: IAiDesignPageTagTypeEnum;
        type?: TPageType | undefined,
    }[];
    sound?: {
        list: [IPageAudio];
    }[];
    pageHash?: Record<string, number>;
    pageMargin?:number;
}
