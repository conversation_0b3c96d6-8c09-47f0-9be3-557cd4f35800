{"name": "@tgs/types", "version": "0.1.8", "description": "@tgs/types", "main": "src/index.ts", "types": "src/index.d.ts", "publishConfig": {"@tgs:registry": "http://*************:4873/"}, "files": ["/src", "/dist"], "scripts": {"release": "pnpm publish --registry=http://*************:4873/ --no-git-checks"}, "buildOptions": {"name": "index", "filename": "index", "formats": ["esm-bundler"]}, "devDependencies": {"@microsoft/api-documenter": "^7.21.5", "@types/fabric": "^5.3.3"}}