import { Asset } from './Asset';
import { Color } from './../Util';
import { Text } from './Text';

// ts类型
import { IAssetConstructor, ICellSizeTable, ICellTextTable, ICellTable, ICell, ICellText } from './Interface';

// 入参类型
interface TableParamsType extends IAssetConstructor {
    cell?: ICellTable;
    cellSize?: ICellSizeTable;
    text?: ICellTextTable;
    zoom?: number;
}

interface ICellSizeObj {
    cellWidth: number;
    cellHeight: number;
    cellX: number;
    cellY: number;
}

/**
 * 表格
 */
export class Table extends Asset {

    // 单元格属性
    private cell: ICellTable;

    // 单元格尺寸
    private cellSize: ICellSizeTable;

    // 单元格内文本
    private text: ICellTextTable;

    // 表格元素定位
    private preCellX: number;
    private preCellY: number;

    private isFisrtRow = false;
    private isFisrtCol = false;

    private rowNumber = 0;
    private colNumber = 0;

    private maxTextLimit = 2000;
    constructor(params: TableParamsType) {
        super(params);
        this.cell = params.cell;
        this.preCellX = - this.width / 2;
        this.preCellY = - this.height / 2;
        this.text = params.text;
        this.cellSize = this.calcCellSize(params.cellSize);
        this.rowNumber = this.cell.length;
        this.colNumber = this.cell[0].length;
        this.zoom = params.zoom || 1;
    }

    /**
     * 表格渲染
     */
    render(ctx: CanvasRenderingContext2D): void {
        ctx.save();
        
        this.changeCoordinateByCenter(ctx);
        // 根据cell数组渲染表格

        this.cell.forEach((row, i) => {
            this.isFisrtRow = i === 0;
            row.forEach((_, j) => {
                this.isFisrtCol = j === 0;
                this.renderCell(ctx, i, j, this.cell.length-1===i, row.length-1===j);
            })
        });
        
        // 调试
        // this.devRenderReference('reference', ctx);
        // this.devRenderView('tsble', ctx.canvas);
        
        ctx.restore();
        
        // 测试
    }

    // 渲染单元格
    private renderCell(ctx: CanvasRenderingContext2D, rowI:number, colI:number, isLastRow:boolean, isLastCol:boolean): void {
        const cellStyle = this.cell[rowI][colI];
        const cellText = this.text[rowI][colI];
        let cellWidth = this.cellSize.col[colI];
        let cellHeight = this.cellSize.row[rowI];
        const cellX = this.cellSize.colPos[colI];
        const cellY = this.cellSize.rowPos[rowI];

        // 被merge的单元格不参与渲染
        if (cellStyle.merged) {
            if (isLastRow) {
                const size = { cellWidth, cellHeight, cellX, cellY };
                this.renderCellBorderByIndex(ctx, cellStyle, size, isLastRow, isLastCol, 2);
            } 
            if (isLastCol) {
                const size = { cellWidth, cellHeight, cellX, cellY };
                this.renderCellBorderByIndex(ctx, cellStyle, size, isLastRow, isLastCol, 1);
            }
            return;
        }

        // merge元素尺寸重新计算
        if (cellStyle.merge) {
            [cellWidth, cellHeight] = this.calcCellMerge({ rowI, colI, oWidth: cellWidth, oHeight: cellHeight, merge: cellStyle.merge });
        }

        // 尺寸对象： 左上角定位，元素尺寸
        const sizeObj = { cellWidth, cellHeight, cellX, cellY };

        // 背景色
        ctx.fillStyle = Color.rgbO2S(cellStyle.background.color);
        ctx.fillRect(cellX, cellY, cellWidth, cellHeight);

        // 文字
        this.renderCellText(ctx, cellText, sizeObj);

        // 边框
        this.renderCellBorder(ctx, cellStyle, sizeObj, isLastRow, isLastCol);

    }
    private renderCellBorderByIndex(ctx: CanvasRenderingContext2D, cellStyle: ICell, sizeObj: ICellSizeObj, isLastRow: boolean, isLastCol: boolean, index: number): void {
        const { lineColor, lineStyle, lineWidth } = cellStyle;
        const color = lineColor[index];
        const style = lineStyle[index];
        const width = lineWidth[index];
        this.renderBorder({ ctx, color: Color.rgbO2S(color), style, width, index: index, sizeObj, isLastRow, isLastCol, cellStyle });
    }
    // 绘制边框
    private renderCellBorder(ctx: CanvasRenderingContext2D, cellStyle: ICell, sizeObj: ICellSizeObj, isLastRow: boolean, isLastCol: boolean): void {
        const { lineColor, lineStyle, lineWidth } = cellStyle;

        for (let i = 0; i < 4; i++) {
            const color = lineColor[i];
            const style = lineStyle[i];
            const width = lineWidth[i];
            // 渲染单个边框
            this.renderBorder({ ctx, color: Color.rgbO2S(color), style, width, index: i, sizeObj, isLastRow, isLastCol, cellStyle });
        }
    }

    // 绘制一条边框
    private renderBorder(params: {
        ctx: CanvasRenderingContext2D;
        color: string;
        style: string;
        width: number;
        index: number; // 0 1 2 3  上 右 下 左
        sizeObj: ICellSizeObj;
        isLastRow: boolean; //最后一行
        isLastCol: boolean; //最好一列
        cellStyle?: ICell;
    }) {
        const { ctx, color, style, width, index, sizeObj, isLastRow, isLastCol, cellStyle } = params;
        if (!width) return;
        const zoomWidth = Math.round(width / this.zoom)
        const maxWidth = width * 2;
        const newWidth = Math.max(zoomWidth, maxWidth);
        ctx.beginPath();
        ctx.lineWidth = newWidth;
        ctx.strokeStyle = color;
        let styleNum:number[] = [];
        if (style === 'dashed') {
            styleNum = [newWidth*6, newWidth]
        } else if (style === 'dotted') {
            styleNum = [newWidth, newWidth]
        } else if (style === 'none' || style === 'hidden') {
            return;
        }
        ctx.setLineDash(styleNum);
        const angleHalf = (newWidth - 1) / 2;


        switch (index) {
            case 0:
                if (this.isFisrtRow || this.rowNumber === 1) {
                    ctx.lineWidth = newWidth * 2;
                }
                ctx.moveTo(sizeObj.cellX-angleHalf, sizeObj.cellY);
                ctx.lineTo(sizeObj.cellX+sizeObj.cellWidth+angleHalf, sizeObj.cellY);
                break;
            case 1:
                if (this.colNumber === 1 || isLastCol) {
                    ctx.lineWidth = newWidth * 2;
                }
                if (isLastCol) {
                    ctx.moveTo(sizeObj.cellX+ sizeObj.cellWidth,sizeObj.cellY-angleHalf);
                    ctx.lineTo(sizeObj.cellX+sizeObj.cellWidth, sizeObj.cellY+sizeObj.cellHeight+angleHalf);                    
                }
                break;
            case 2:
                if (isLastRow || this.rowNumber === 1) {
                    ctx.lineWidth = newWidth * 2;
                }
                const topBorderShow = cellStyle?.lineWidth[0]
                if (isLastRow || !topBorderShow) {
                    ctx.moveTo(sizeObj.cellX - angleHalf, sizeObj.cellY + sizeObj.cellHeight);
                    ctx.lineTo(sizeObj.cellX+sizeObj.cellWidth+ angleHalf, sizeObj.cellY + sizeObj.cellHeight);                    
                }
                break;
            case 3:
                if (this.colNumber === 1 || this.isFisrtCol) {
                    ctx.lineWidth = newWidth * 2;
                }
                ctx.moveTo(sizeObj.cellX, sizeObj.cellY-angleHalf);
                ctx.lineTo(sizeObj.cellX, sizeObj.cellY + sizeObj.cellHeight+angleHalf);
                break;

            default:
                return
                break;
        }
        ctx.stroke();

    }

    // 绘制文字
    private renderCellText(
        ctx: CanvasRenderingContext2D,
        cellText:ICellText,
        sizeObj: {
            cellWidth: number;
            cellHeight: number;
            cellX: number;
            cellY: number;
        }
    ) {
        const { cellWidth, cellHeight, cellX, cellY } = sizeObj;
        const { content, fontSize, fontFamily, color, letterSpacing, lineHeight, textAlign, fontWeight, fontStyle, textDecoration } = cellText;
        if(content?.[0]?.length > this.maxTextLimit) return

        // Add 5px padding to all cells
        const padding = 5;
        const paddedWidth = Math.max(0, cellWidth - (padding * 2));
        const paddedHeight = Math.max(0, cellHeight - (padding * 2));
        const paddedX = cellX + padding;
        const paddedY = cellY + padding;

        const testAsset = new Text({
            width: paddedWidth * 2,
            height: paddedHeight * 2,
            left: paddedX,
            top: paddedY,

            overflowY: 'hidden',
            maxHeight: paddedHeight * 2,
            verticalAlign: 'center',
            content: content?.filter(item=>item)?.map(item=>({text:item})),
            fontSize,
            fontFamily,
            color,
            letterSpacing,
            lineHeight,
            align: textAlign,
            strokeColor: color,
            textStrokeWidth: fontWeight === 'bold' ? 1 : 0,
            isItalic: fontStyle === 'italic',
            underline: textDecoration === 'underline',
            isLineThrough: textDecoration === 'line-through',
        });
        testAsset.renderCache(ctx);
    }

    // 计算每个表格尺寸定位
    private calcCellSize(cellSize: ICellSizeTable): ICellSizeTable {
        let { col, row } = cellSize;
        const tableH = row.reduce((sum: number, item: number) => sum + item);
        const colRatio = this.width / col.reduce((sum: number, item: number) => sum + item);
        const rowRatio = this.height / tableH;
        // 表格的平均高度，当有些高度是负值时，使用平均高度
        const tdAverage = tableH / this.cell.length; 
        // 分配尺寸
        col = col.map(item => item * colRatio);
        row = row.map(item => item < 0 ? tdAverage * rowRatio : item * rowRatio);

        // 计算定位
        let preCol = this.preCellX;
        let preRow = this.preCellY;

        const colPos: Array<number> = [];
        const rowPos: Array<number> = [];

        /**
         * todo：doc有时候行数尺寸，和样式数组，对应不上；
         * 会有表格尺寸异常问题
         * 根据样式数组，处理下尺寸数组
        */

        // 列坐标
        col.forEach(item => {
            colPos.push(preCol);
            preCol += item;
        });
        // 行坐标
        row.forEach(item => {
            rowPos.push(preRow);
            preRow += item;
        });

        return {
            col,
            row,
            colPos,
            rowPos
        }
    }

    // 计算合并单元格的尺寸
    private calcCellMerge({ rowI, colI, oWidth, oHeight, merge }: {
        rowI: number;
        colI: number;
        oWidth: number;
        oHeight: number;
        merge: { x: number, y: number };
    }) {
        let width = oWidth, height = oHeight;
        const { row, col } = this.cellSize;

        // 累加合并单元格的尺寸
        while (rowI < merge.x) {
            const h = row[rowI + 1] ? row[rowI + 1] : 0;
            height += h;
            rowI++;
        }
        while (colI < merge.y) {
            const w = col[colI + 1] ? col[colI + 1] : 0;
            width += w;
            colI++;
        }

        return [width, height];
    }

}