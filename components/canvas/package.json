{"name": "@tgs/canvas", "version": "0.1.19", "description": "@tgs/canvas", "main": "src/index.ts", "module": "src/index.ts", "types": "lib/src/index.d.ts", "type": "module", "exports": {".": {"import": "./src/index.ts"}, "./dist": {"import": "./dist/tgs-canvas.js", "require": "./dist/tgs-canvas.cjs"}}, "publishConfig": {"@tgs:registry": "http://*************:4873/"}, "files": ["/src", "/dist", "/lib", "index.d.ts", "tgs.d.ts"], "scripts": {"dev": "vite", "build": "tsc && vite build", "lib": "vite build", "preview": "vite preview", "doc": "tsc --project tsconfig.doc.json", "release": "pnpm publish --registry=http://*************:4873/ --no-git-checks"}, "dependencies": {"@tgs/ips_fabric": "workspace:^0.1.2", "@tgs/ips_text": "workspace:^0.1.8", "@tgs/types": "workspace:^0.1.8", "d3": "5.9.2", "echarts": "5.4.0", "fabric": "^5.3.0", "fast-deep-equal": "^3.1.3", "gl-matrix": "3.4.3", "klona": "^2.0.4", "lodash-es": "^4.17.21", "lru-cache": "7.14.0", "transformation-matrix": "^2.8.0", "webfontloader": "^1.6.28"}, "devDependencies": {"@microsoft/api-documenter": "^7.21.5", "@types/fabric": "^5.3.3", "@types/lodash-es": "^4.17.7", "@types/offscreencanvas": "^2019.7.0", "react": "^17.0.1", "react-dom": "^17.0.1", "react-router-dom": "^6.8.2", "rollup-plugin-visualizer": "^5.12.0", "vite-plugin-dts": "^3.5.4"}}