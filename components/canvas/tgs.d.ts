/* eslint-disable @typescript-eslint/no-empty-interface */
import type { TgsTypes } from '@tgs/types';

declare namespace Tgs {
    type DeepPartial<T> = TgsTypes.DeepPartial<T>;

    /* 整体模板相关 */
    interface IDoc extends TgsTypes.IDoc {}

    interface ICanvas extends TgsTypes.ICanvas {}

    interface IWork extends TgsTypes.IWork {}

    interface IPage extends TgsTypes.IPage {}

    interface IPageAttr extends TgsTypes.IPageAttr {}

    type TPageType = TgsTypes.TPageType;

    /* 元素相关 */
    type TGroupAssetType = TgsTypes.TGroupAssetType;
    type TChartAssetType = TgsTypes.TChartAssetType;
    type TImageAssetType = TgsTypes.TImageAssetType;
    type TQRCodeAssetType = TgsTypes.TQRCodeAssetType;
    type TSvgAssetType = TgsTypes.TSvgAssetType;
    type TFlowAssetType = TgsTypes.TFlowAssetType;
    type TTableAssetType = TgsTypes.TTableAssetType;
    type TTextAssetType = TgsTypes.TTextAssetType;
    type TVideoEAssetType = TgsTypes.TVideoEAssetType;
    type TFrameAssetType = TgsTypes.TFrameAssetType
    type TLineAssetType = TgsTypes.TLineAssetType;

    type IGroupAsset = TgsTypes.IGroupAsset;
    type IChartAsset = TgsTypes.IChartAsset;
    type IImageAsset = TgsTypes.IImageAsset;
    type IQRCodeAsset = TgsTypes.IQRCodeAsset;
    type ISvgAsset = TgsTypes.ISvgAsset;
    type ITableAsset = TgsTypes.ITableAsset;
    type ITextAsset = TgsTypes.ITextAsset;
    type IVideoEAsset = TgsTypes.IVideoEAsset;
    type IFrameAsset = TgsTypes.IFrameAsset;
    type TAssetType = TgsTypes.TAssetType;
    type IAsset<T extends TAssetType> = TgsTypes.IAsset<T>;
    type ITextSelectData = TgsTypes.ITextSelectData;
    type ILineAsset = TgsTypes.ILineAsset;

    /* 特殊属性相关 */
    interface IColor extends TgsTypes.IColor {}

    interface IFilters extends TgsTypes.IFilters {}
    interface IContainer extends TgsTypes.IContainer {}
    interface IImageEffects extends TgsTypes.IImageEffects {}

    interface ICell extends TgsTypes.ICell {}

    interface IEffectVariant extends TgsTypes.IEffectVariant {}
    interface IEffectVariant3D extends TgsTypes.IEffectVariant3D {}
    interface IRichText extends TgsTypes.IRichText {}
    interface ITextAssetMap extends TgsTypes.ITextAssetMap {}
    interface IChartAttribute extends TgsTypes.IChartAttribute {}
    interface IChartDrawInfo extends TgsTypes.IChartDrawInfo {}

    /* 辅助线 */
    interface IAuxiliaryLine extends TgsTypes.IAuxiliaryLine {}

    /* 相框 */
    interface IFrameAttribute extends TgsTypes.IFrameAttribute {}
    interface FrameCanvasLogic extends TgsTypes.FrameCanvasLogic {}

      /* 图形 */
    interface IImageCanvasRenderInfo extends TgsTypes.IImageCavansRenderInfo {}
    interface IImageFilterToolLogic extends TgsTypes.IImageFilterToolLogic {}

    type IConvertPageToImageOptions = TgsTypes.IConvertPageToImageOptions;
}

export = Tgs;
export as namespace Tgs;
export {};
