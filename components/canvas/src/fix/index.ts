/* eslint-disable */
// @ts-nocheck
import { fabric } from 'fabric';
import { fixCursor } from './fixCursor';
import { fixIText } from './fixIText';
import { Point } from 'transformation-matrix';
export function fabricFix() {
    fabric.textureSize = 4096;
    function isInPage(point, bbox: { bl: Point, tr: Point, br: Point, tl: Point }) {
        if(!bbox)return true
        return (
            point.x >= bbox.bl.x && point.x <= bbox.tr.x &&
            point.y <= bbox.bl.y && point.y >= bbox.tr.y
        );
    }
    if ('_applyPatternForTransformedGradient' in fabric.Object.prototype) {
        fabric.Object.prototype._applyPatternForTransformedGradient = function (
            ctx: CanvasRenderingContext2D,
            filler: fabric.Gradient,
        ) {
            var dims = this._limitCacheSize(this._getCacheCanvasDimensions()),
                pCanvas = fabric.util.createCanvasElement(),
                pCtx,
                retinaScaling = this.canvas.getRetinaScaling(),
                width = dims.x / this.scaleX / retinaScaling,
                height = dims.y / this.scaleY / retinaScaling;
            // HTMLCanvasElement 要求宽高至少为 1
            pCanvas.width = Math.max(width, 1);
            pCanvas.height = Math.max(height, 1);
            pCtx = pCanvas.getContext('2d');
            pCtx.beginPath();
            pCtx.moveTo(0, 0);
            pCtx.lineTo(width, 0);
            pCtx.lineTo(width, height);
            pCtx.lineTo(0, height);
            pCtx.closePath();
            pCtx.translate(width / 2, height / 2);
            pCtx.scale(dims.zoomX / this.scaleX / retinaScaling, dims.zoomY / this.scaleY / retinaScaling);
            this._applyPatternGradientTransform(pCtx, filler);
            pCtx.fillStyle = filler.toLive(ctx);
            pCtx.fill();
            ctx.translate(-this.width / 2 - this.strokeWidth / 2, -this.height / 2 - this.strokeWidth / 2);
            ctx.scale((retinaScaling * this.scaleX) / dims.zoomX, (retinaScaling * this.scaleY) / dims.zoomY);
            ctx.strokeStyle = pCtx.createPattern(pCanvas, 'no-repeat');
        };
    }

    if ('isOnScreen' in fabric.Object.prototype) {
        fabric.Object.prototype.isOnScreen = function (calculate) {
            if (!this.canvas) {
                return false;
            }
            var pointTL = this.canvas.vptCoords.tl,
                pointBR = this.canvas.vptCoords.br;
            if (!pointTL || !pointBR) {
                return false;
            }
            var points = this.getCoords(true, calculate);
            // if some point is on screen, the object is on screen.
            if (
                points.some(function (point) {
                    return point.x <= pointBR.x && point.x >= pointTL.x && point.y <= pointBR.y && point.y >= pointTL.y;
                })
            ) {
                this.inScreen = true;
                return true;
            }
            // no points on screen, check intersection with absolute coordinates
            if (this.intersectsWithRect(pointTL, pointBR, true, calculate)) {
                this.inScreen = true;
                return true;
            }
            this.inScreen = this._containsCenterOfCanvas(pointTL, pointBR, calculate)
            return this.inScreen;
        };
    }

    if ('render' in fabric.Object.prototype) {
        function isClip(box, containerBox) {
            return (
                box.left < containerBox.left ||
                box.top < containerBox.top ||
                box.left + box.width > containerBox.left + containerBox.width ||
                box.top + box.height > containerBox.top + containerBox.height
            );
        }

        fabric.Object.prototype.render = function (ctx: CanvasRenderingContext2D) {
            // do not render if width/height are zeros or object is not visible
            if (this.container && (!this.container.inScreen || !this.container.visible) && this.container.pageType !== 'board') {
                return;
            }
            if (this.isNotVisible()) {
                return;
            }
            if (this.canvas && this.canvas.skipOffscreen && !this.group && !this.isOnScreen()) {
                return;
            }
            ctx.save();
            if (this.container && (this.container.pageType !== 'board' /* || this.data?.type === 'background' */)) {
                const activeObject = this.canvas?.getActiveObject?.();
                const box = this.getBoundingRect(undefined, activeObject?.isMoving);
                const containerBox = this.container.getBoundingRect();
                if (isClip(box, containerBox)) {
                    ctx.beginPath();
                    ctx.rect(this.container.left, this.container.top, this.container.width, this.container.height);
                    ctx.closePath();
                    ctx.clip();
                }
            }
            this._setupCompositeOperation(ctx);
            this.drawSelectionBackground(ctx);
            this.transform(ctx);
            this._setOpacity(ctx);
            this._setShadow(ctx, this);
            if (this.shouldCache()) {
                this.renderCache();
                this.drawCacheOnCanvas(ctx);
            } else {
                this._removeCacheCanvas();
                this.dirty = false;
                this.drawObject(ctx);
                if (this.objectCaching && this.statefullCache) {
                    this.saveState({ propertySet: 'cacheProperties' });
                }
            }
            ctx.restore();
        };
    }

    if (fabric.Canvas) {
        fabric.Canvas.prototype.searchNotTransparentTargets = function(objects, pointer) {
            // Cache all targets where their bounding box contains point.
            if (!objects) {
                return;
            }
            var target, i = objects.length, subTarget, backgroundTarget;
            // Do not check for currently grouped objects, since we check the parent group itself.
            // until we call this function specifically to search inside the activeGroup
            try {
                while (i--) {
                    var objToCheck = objects[i];
                    var pointerToUse = objToCheck.group ?
                        this._normalizePointer(objToCheck.group, pointer) : pointer;
                        
                    if (objToCheck.data && objToCheck.data?.pageIndex === this.pageIndex && this._checkTarget(pointerToUse, objToCheck, pointer)) {
                        if ( objToCheck.data.type !== 'group' ){
                            // 如果是文字 就直接返回
                            if (objToCheck.data.type === 'text') {
                                target = objects[i];
                                break;
                            }
                            var targetName = 'render-' + objToCheck.data.className;
                            var renderTarget = this._objects.find((item: fabric.Object) => item.name?.indexOf(targetName)>-1 && item?.data?.className === objToCheck?.data?.className);
                            if (renderTarget) {
                                var aPointer = objToCheck.group ? pointer : pointerToUse;
                                var isTransparent = this.isTargetTransparent(renderTarget, aPointer.x, aPointer.y);
                                if (!isTransparent) {
                                    if (objToCheck.data.type === 'background') {
                                        backgroundTarget = objects[i];
                                    } else {
                                        target = objects[i];
                                        break;
                                    }
                                }
                            } 
                            // else if (objToCheck.data.type === 'page') {
                            //     backgroundTarget = objects[i];
                            // }
                        } else {
                            if (objToCheck.subTargetCheck && objToCheck instanceof fabric.Group) {
                                subTarget = this.searchNotTransparentTargets(objToCheck._objects, pointerToUse);
                                subTarget && this.targets.push(subTarget);
                                if (subTarget) {
                                    target = objects[i];
                                    if (target.data) {
                                        target.data.subSelectTarget = subTarget;
                                    }
                                    break;
                                }
                            }
                        }
                    }
                }
            } catch(e) {
                console.error(e)
            }
            if(!target && backgroundTarget) {
                target = backgroundTarget;
            }

            return target;
        }
        fabric.Canvas.prototype.setPageIndex = function(index) {
            this.pageIndex = index;
        }
    }
    if ('findTarget' in fabric.Canvas.prototype) {
        fabric.Canvas.prototype.findTarget = function (e: MouseEvent, skipGroup, flag) {
            if (this.skipTargetFind) {
                return;
            }

            var ignoreZoom = true,
                pointer = this.getPointer(e, ignoreZoom),
                activeObject = this._activeObject,
                aObjects = this.getActiveObjects(),
                activeTarget,
                activeTargetSubs,
                isTouch = fabric.util.isTouchEvent(e),
                shouldLookForActive = (aObjects.length > 1 && !skipGroup) || aObjects.length === 1;

            // first check current group (if one exists)
            // active group does not check sub targets like normal groups.
            // if active group just exits.
            this.targets = [];
            // if we hit the corner of an activeObject, let's return that.
            if (shouldLookForActive && activeObject._findTargetCorner(pointer, isTouch)) {
                return activeObject;
            }
            if (
                aObjects.length > 1 &&
                !skipGroup &&
                activeObject === this._searchPossibleTargets([activeObject], pointer)
            ) {
                return activeObject;
            }
            const againActiveObject = activeObject && this._searchPossibleTargets([activeObject], pointer);
            if (aObjects.length === 1 && activeObject === againActiveObject) {
                if (!this.preserveObjectStacking) {
                    return activeObject;
                } else {
                    activeTarget = activeObject;
                    activeTargetSubs = this.targets;
                    this.targets = [];
                }
            }

            var target = this._searchPossibleTargets(this._objects, pointer);
            if ((e.type === 'mousedown') || e.type === 'mouseup'|| e.type === 'mousemove' || e.type==='dblclick') {
                var notMouseDown = e.type !== 'mousedown';
                var isMouseDown = e.type === 'mousedown';
                if (activeObject && activeObject.data && againActiveObject) {
                    var targetData = activeObject.data;
                    // 组合 如果选中的是文字，就不需要穿透
                    if (activeObject.data?.type === 'group' ){
                        // console.log('targetgroup1111',e.type, activeObject?.name, activeObject)
                        if (flag) {
                            this.targets = [];
                            var notTransparentTarget = this.searchNotTransparentTargets(this._objects, pointer);
                            if (notTransparentTarget && notTransparentTarget?.data?.group && notTransparentTarget?.data?.type === 'group') {
                                target = notTransparentTarget;
                            }
                            // console.log('targetgroup3333', flag,e.type,activeObject?.name, notTransparentTarget)
                        } else {
                            // 组合重置
                            var subTarget = activeObject?.data?.subSelectTarget;
                            var isGroupText = subTarget && subTarget?.data?.type === 'text' ;
                            const isSubTarget = subTarget && this._searchPossibleTargets([subTarget], pointer);
                            if (e.type === 'mousedown' && isSubTarget && isGroupText && activeObject?.data.isEdit) {
                                target = subTarget;
                                this.targets = []
                            } else {
                                target = activeObject;
                                if (isSubTarget) {
                                    this.targets = [subTarget]
                                } else {
                                    var isPossibleTarget = this._searchPossibleTargets(activeObject._objects, pointer);
                                    if (isPossibleTarget) {
                                        this.targets = [isPossibleTarget]
                                    }  else {
                                        var notTransparentTarget = this.searchNotTransparentTargets(this._objects, pointer);
                                        if (notTransparentTarget) {
                                            target = notTransparentTarget;
                                        }
                                    } 
                                }
                            }
                        }
                    } else {
                        // console.log('targetObject',activeObject?.name, activeObject)
                        var noPenetrate = targetData.isClip || targetData.imageClipping || targetData.editing || targetData?.type === 'frameClip';
                        if (!noPenetrate) {
                            // 判断当前目标位置是否是透明的
                            var isTargetPosTransparent = this.searchNotTransparentTargets([activeObject], pointer)
                            // console.log('target0000', e.type, isTargetPosTransparent?.name, isTargetPosTransparent)
                            // 透明位置，鼠标不是点击
                            if (!isTargetPosTransparent && notMouseDown) {
                                if (activeObject.data?.type !== 'text') {
                                    var notTransparentTarget = this.searchNotTransparentTargets(this._objects, pointer);
                                    if (notTransparentTarget) {
                                        target = notTransparentTarget;
                                    }
                                    // console.log('target1111', e.type, activeObject?.name, activeObject)
                                } else {
                                    // console.log('target2222', e.type, activeObject?.name, activeObject)
                                }
                            } else {
                                const isTarget = this._searchPossibleTargets([activeObject], pointer);
                                // console.log('target0000111', e.type, isTargetPosTransparent?.name, isTargetPosTransparent)
                                // 非透明位置
                                if (isTargetPosTransparent) {
                                    // 点击选择元素
                                    if (e.type === 'mousedown' || e.type === 'dblclick') {
                                        target = activeObject
                                        /*if (e.shiftKey || e.ctrlKey || e.metaKey) {}  */
                                    } else if (flag && target) {
                                        // 并未移动下判断 target的位置是不是透明,如果透明选择activeObject
                                        var isTransparent = this.searchNotTransparentTargets([target], pointer);
                                        if (!isTransparent) {
                                            // 透明位置 继续寻找不透明元素
                                            var notTransparentTarget = this.searchNotTransparentTargets(this._objects, pointer);
                                            // console.log('target555', e.type, notTransparentTarget?.name, notTransparentTarget)
                                            if (notTransparentTarget) {
                                                target = notTransparentTarget;
                                            } else {
                                                target = activeObject;
                                            }
                                        }
                                        // console.log('target3333', flag, e.type, target?.name, target)
                                    }
                                } else if(!isTargetPosTransparent && isTarget) {
                                    // 透明位置， 但是鼠标在当前元素上，还选择该元素
                                    target = activeObject
                                    // console.log('target4444', e.type, target?.name, target)
                                } else {
                                    // 点击元素之上的地方 
                                    if (e.type === 'mousedown') {
                                        var notTransparentTarget = this.searchNotTransparentTargets(this._objects, pointer);
                                        // console.log('target555', e.type, notTransparentTarget?.name, notTransparentTarget)
                                        if (notTransparentTarget) {
                                            target = notTransparentTarget;
                                        }
                                    }
                                }
                            }
                        } else {
                            target = activeObject;
                        }
                    }
                } else {
                    // 没有选中的元素
                    var targets = this.targets;
                    this.targets = []
                    var notTransparentTarget = this.searchNotTransparentTargets(this._objects, pointer);
                    if (notTransparentTarget) {
                        target = notTransparentTarget;
                    } else {
                        this.targets = targets;
                    }
                    // 鼠标在画布外
                    if (isMouseDown && target && target.container?.pageType !== 'board' && !isInPage(pointer, target.container?.lineCoords)) {
                        return null;
                    }
                }
            }
            // console.log('target777', e.type, target?.name, target, this.targets)

            if (e[this.altSelectionKey] && target && activeTarget && target !== activeTarget) {
                target = activeTarget;
                this.targets = activeTargetSubs;
            }

            return target;
        };
    }
    if('_renderControls' in fabric.Canvas.prototype){
        const renderControls = fabric.Canvas.prototype._renderControls;
        fabric.Canvas.prototype._renderControls = function(ctx: CanvasRenderingContext2D, controlKey: string, left: number, top: number) {
            renderControls(ctx, controlKey, left, top);
        };
    }

    fabric.CustomLineWithArrows = fabric.util.createClass(fabric.Object, {
        type: 'CustomLineWithArrows',
      
        initialize: function(startPoint, endPoint, options) {
          this.startPoint = startPoint || { x: 0, y: 0 };
          this.endPoint = endPoint || { x: 100, y: 100 };
          this.arrowLength = 10;
          this.arrowAngle = Math.PI / 6;
      
          // 创建直线
          this.line = new fabric.Line([this.startPoint.x, this.startPoint.y, this.endPoint.x, this.endPoint.y], {
            stroke: 'black',
            strokeWidth: 2,
            selectable: false,
            evented: false,
          });
      
          // 创建箭头
          this.startArrow = new fabric.Triangle({
            left: this.startPoint.x,
            top: this.startPoint.y,
            width: this.arrowLength * 2,
            height: this.arrowLength,
            angle: 180,
            fill: 'black',
            selectable: false,
            evented: false,
          });
      
          this.endArrow = new fabric.Triangle({
            left: this.endPoint.x,
            top: this.endPoint.y,
            width: this.arrowLength * 2,
            height: this.arrowLength,
            angle: 0,
            fill: 'black',
            selectable: false,
            evented: false,
          });
      
          // 添加到对象中
          this.set({
            left: this.startPoint.x,
            top: this.startPoint.y,
            width: this.endPoint.x - this.startPoint.x,
            height: this.endPoint.y - this.startPoint.y,
          });
      
          this.callSuper('initialize', options);
          this.updateArrows(); // 更新箭头位置
        },
      
        // 更新箭头位置
        updateArrows: function() {
          const angle = Math.atan2(this.endPoint.y - this.startPoint.y, this.endPoint.x - this.startPoint.x);
      
          // 更新起始箭头的位置
          this.startArrow.set({
            left: this.startPoint.x,
            top: this.startPoint.y,
            angle: angle + Math.PI,
          });
      
          // 更新结束箭头的位置
          this.endArrow.set({
            left: this.endPoint.x,
            top: this.endPoint.y,
            angle: angle,
          });
      
          // 更新直线的位置和角度
          this.line.set({
            x1: this.startPoint.x,
            y1: this.startPoint.y,
            x2: this.endPoint.x,
            y2: this.endPoint.y,
          });
        },
      
        // 重新绘制
        _render: function(ctx) {
          this.line.render(ctx);
          this.startArrow.render(ctx);
          this.endArrow.render(ctx);
        },
      
        // 设置起始点
        setStartPoint: function(x, y) {
          this.startPoint = { x: x, y: y };
          this.updateArrows();
        },
      
        // 设置结束点
        setEndPoint: function(x, y) {
          this.endPoint = { x: x, y: y };
          this.updateArrows();
        },
      });

    fixIText();
    fixCursor();
}
