import { fabric } from 'fabric';
import type { Actions } from '../../../actions';
import type { RenderZIndex } from '../../RenderZIndex';
import type { TgsCanvasPageAssets } from '../../TgsCanvas';
import { CustomPath } from './../Line/customPath';
// 存在这个方法，但是声明文件中没有写
type IUtil = fabric.IUtil & {
    calcRotateMatrix: ({ angle }: { angle: number }) => number[];
};

interface IOptions {
    page: TgsCanvasPageAssets;
    clipPage: fabric.Rect;
    mainCanvas: fabric.Canvas;
    actions: Actions;
    renderZIndex: RenderZIndex;
    index: number;
    pageIndex: number;
    readOnly?: boolean;
}

const multiply = fabric.util.multiplyTransformMatrices;
const invert = fabric.util.invertTransform;
const qrDecompose = fabric.util.qrDecompose;
const calcRotateMatrix = (fabric.util as IUtil).calcRotateMatrix;

export class AssetBase {
    renderOnly = false;
    page: TgsCanvasPageAssets;
    mainCanvas: fabric.Canvas;
    clipPage: fabric.Rect;
    protected actions: Actions;
    protected renderZIndex: RenderZIndex;
    //
    className: string;
    target: fabric.Rect | fabric.Group | fabric.Line | fabric.Path | undefined;
    helpAiDesignRect: fabric.Rect | fabric.Group | undefined;
    declare addHelpAiDesignRect: () => void;
    declare hideHelpAiDesignRect: () => void;
    protected renderTarget: fabric.Image | fabric.Object | fabric.Group | fabric.Path | undefined;
    protected groupTarget: fabric.Group | undefined;
    protected readonly targetZIndexOffset = 19;
    protected readonly renderTargetZIndexOffset = 9;

    hasAdd = false;
    protected hasAddControl = false;
    asset: Tgs.IAsset<Tgs.TAssetType>;
    groupName: string | undefined;
    locked = false;
    /** index in page assets array */
    index: number;
    /** index of pages which asset belongs to */
    pageIndex: number;
    /** pageIndex可能不修改 */
    readonly assetInPageIndex: number;

    destroyed = false;

    // events control
    protected moving = false;
    protected rotating = false;
    protected scaling = false;
    protected editing = false;
    // display control
    protected visible = true;
    protected readOnly = false;
    
    linkMap: Record<string, string> = {};
    // 连接线
    protected points: { x: number; y: number }[] = [];

    constructor(
        options: IOptions,
        asset: Tgs.IAsset<Tgs.TAssetType>,
    ) {
        this.page = options.page;
        this.mainCanvas = options.mainCanvas;
        this.clipPage = options.clipPage;
        this.actions = options.actions;
        this.renderZIndex = options.renderZIndex;
        this.index = options.index;
        this.pageIndex = options.pageIndex;
        this.assetInPageIndex = options.pageIndex;
        this.asset = asset;
        this.className = asset.meta.className;
        this.groupName = asset.meta.group;
        this.readOnly = options.readOnly || false;  
        // this.locked = asset.meta.locked || false;
        if (!this.renderOnly) {
            this.initTarget(options, asset)
            this.setControlsVisibility();
            this.bindEvents();
        }
        this.setLinkLineMap()
        this.init();
        this.setLockStatus();
    }

    async init() {
        /* need override */
        console.error('AssetBase init need override');
        return true;
    }

    protected initTarget(options: IOptions, asset: Tgs.IAsset<Tgs.TAssetType>) {
        const baseOption: fabric.IObjectOptions = {
            centeredRotation: true,
            container: options.clipPage,
            data: {
                className: asset.meta.className,
                group: asset.meta.group,
                type: asset.meta.type,
                pageIndex: options.pageIndex,
            },
            left: Math.floor(asset.transform.posX),
            top: Math.floor(asset.transform.posY),
            width: asset.attribute.width,
            height: asset.attribute.height,
            visible: this.visible,
            selectable: false,
            evented: false,
            name: this.className,
        };
        if (asset.meta.type === 'group') {
            this.target = new fabric.Group([], {
                ...baseOption,
            });
        } else if (asset.meta.type === 'line') {
            // @ts-ignore
            this.target = new CustomPath([], {
                ...baseOption,
            });
        } else {
            let otherOption = {};
            if (asset.meta.type === 'table') {
                otherOption = {
                    hasBorder: false,
                    hasControls: false,
                }
            }
            this.target = new fabric.Rect({
                // fill: 'transparent',
                fill: process.env.NODE_ENV === 'development' ? 'rgba(255,0,0,0.1)' : 'transparent',
                ...baseOption,
                ...otherOption
            });
        }
        this.target?.rotate(asset.transform.rotate);
    }

    async update(asset: Tgs.IAsset<Tgs.TAssetType>, index: number, pageIndex: number) {
        /* need override */
        console.error('AssetBase update need override');

        this.setIndexAndGroup(index, pageIndex, asset);
        return true;
    }

    async render() {
        /* need override */
        if (this.destroyed) {
            return true;
        }
        await this.renderTargets();
        this.add();
        !this.skipRequestRender() && this.mainCanvas.requestRenderAll();
        return true;
    }

    protected add() {
        if (this.renderTarget && !this.hasAdd && !this.destroyed) {
            this.hasAdd = true;
            this.mainCanvas.add(this.renderTarget);
            this.setRenderZIndex(this.className + '-render', this.renderTarget, this.renderTargetZIndexOffset);
        }
        // 组合也添加add
        if (this.asset.meta.type === 'group' && !this.hasAdd && !this.destroyed) {
            this.hasAdd = true;
        }
        if (
            this.target &&
            !this.hasAddControl &&
            !this.destroyed &&
            !this.renderOnly &&
            (this.asset.meta.type === 'group' || !this.groupName)
        ) {
            this.hasAddControl = true;
            this.mainCanvas.add(this.target);
            this.setRenderZIndex(this.className, this.target, this.targetZIndexOffset);
        }
    }

    protected remove() {
        if (this.renderTarget && (this.hasAdd || this.destroyed)) {
            this.renderTarget.clipPath = undefined;
            if ('dispose' in this.renderTarget) {
                this.renderTarget.dispose();
            }
            this.className && this.removeRenderZIndex(this.className);
            this.mainCanvas.remove(this.renderTarget);
            this.hasAdd = false;
        }
        if (this.target && this.destroyed) {
            this.removeEvents(this.target);
            this.target.clipPath = undefined;
            this.mainCanvas.remove(this.target);
            this.hasAddControl = false;
            if (this.helpAiDesignRect) {
                this.mainCanvas.remove(this.helpAiDesignRect);
            }
        }
    }

    /** 添加到 group 时, 从画布移除控制对象, 有一个返回 false 则不是新组合 */
    onAddToGroup() {
        this.groupTarget = this.target?.group;
        if (!this.groupTarget) {
            this.groupTarget = this.mainCanvas
                .getObjects()
                .find((obj) => obj.get('type') == 'group' && obj?.data?.group == this.groupName) as fabric.Group;
        }
        if (!this.destroyed && !this.renderOnly && this.target?.group && this.target.group.get('type') === 'group') {
            this.target.group.removeWithUpdate(this.target);
            return true;
        }
        if (
            !this.destroyed &&
            !this.renderOnly &&
            this.target &&
            this.hasAddControl &&
            this.groupName &&
            this.asset.meta.type !== 'group'
        ) {
            this.hasAddControl = false;
            this.mainCanvas.remove(this.target);
            // this.removeRenderZIndex(this.className);
            return true;
        }
        return false;
    }

    /** 从 group 移除时, 添加控制对象到画布 */
    onRemoveFromGroup() {
        if (
            !this.destroyed &&
            !this.renderOnly &&
            this.target &&
            !this.hasAddControl &&
            !this.groupName &&
            this.asset.meta.type !== 'group'
        ) {
            this.hasAddControl = true;
            this.mainCanvas.add(this.target);
            this.setRenderZIndex(this.className, this.target, this.targetZIndexOffset);
        }
    }

    destroy() {
        this.destroyed = true;
        this.removeRenderZIndex(this.className + '-render');
        this.removeRenderZIndex(this.className);
        this.remove();
        this.renderTarget = undefined;
        this.target = undefined;
        this.groupTarget = undefined;
        this.mainCanvas.requestRenderAll();
    }

    protected async renderTargets() {
        /* need override */
        console.error('AssetBase renderTargets need override');
    }

    /**
     * 设置渲染层级
     * @param key - className 或者 className-xxxxx-xxxxx 之类的
     * @param target - fabric.Object, 有些元素需要多个 fabric.Object 分工分别负责渲染和事件处理
     * @param offset - number, 基于 transform.zIndex 的偏移量, 每个元素计划有 0-19 的偏移层级
     * @param zIndex - number, 有些操作需要特殊的渲染层级
     */
    protected setRenderZIndex(key: string, target: fabric.Object | undefined, offset = 0, zIndex?: number) {
        if (this.groupName && target === this.target && this.asset.meta.type !== 'group') {
            return;
        }
        if (target) {
            this.renderZIndex.setAssetZIndex(
                key,
                target as fabric.Object,
                // 假设每页最多 100000 个元素, 考虑到性能, 是一个比较安全的值
                100000 * this.pageIndex + (zIndex ?? (this.asset?.transform.zIndex as number)),
                offset,
            );
        }
    }

    /**
     * 移除渲染层级
     * @param key - className 或者 className-xxxxx-xxxxx 之类的
     */
    protected removeRenderZIndex(key: string) {
        this.renderZIndex.removeAssetZIndex(key);
    }

    /** 更新元素在 page.assets 中的索引，元素所属页面的索引，元素的 group 信息 */
    setIndexAndGroup(index: number | undefined, pageIndex: number | undefined, asset: Tgs.IAsset<Tgs.TAssetType>) {
        if (typeof index === 'number' && index >= 0) {
            this.index = index;
        }
        if (typeof pageIndex === 'number' && pageIndex >= 0) {
            this.pageIndex = pageIndex;
        }
        if (this.groupName !== asset.meta.group) {
            this.groupName = asset.meta.group;
            this.target?.setOptions({
                data: {
                    ...this.target.data,
                    group: this.groupName,
                },
            });
            this.onGroupUpdate();
        }
        this.setLockStatus(asset);
    }

    setLockStatus(asset?: Tgs.IAsset<Tgs.TAssetType>) {
        if (!asset) {
            asset = this.asset;
        }
        if (!this.locked && asset.meta.locked) {
            this.target?.setOptions({
                lockMovementX: true,
                lockMovementY: true,
                lockRotation: true,
                lockScalingX: true,
                lockScalingY: true,
                lockSkewingX: true,
                lockSkewingY: true,
                lockUniScaling: true,
                hasControls: false,
            });
            this.locked = true;
        } else if (this.locked && !asset.meta.locked) {
            this.target?.setOptions({
                lockMovementX: false,
                lockMovementY: false,
                lockRotation: false,
                lockScalingX: false,
                lockScalingY: false,
                lockSkewingX: false,
                lockSkewingY: false,
                lockUniScaling: false,
                hasControls: asset.meta.type === 'table' ? false : true,
            });
            this.locked = false;
        }
    }

    protected skipRequestRender() {
        if (!this.page.page.visible || !this.page.page.inScreen) {
            return true;
        }
        if (!this.target?.isOnScreen() || !this.renderTarget?.isOnScreen()) {
            return true;
        }
        if (this.target?.isNotVisible() || this.renderTarget?.isNotVisible()) {
            return true;
        }
        return false;
    }

    /** 设置控件可见性 */
    protected setControlsVisibility() {
        /* maybe need override */
        if (this.asset.meta.type === 'table' && this.target) {
            this.target.setOptions({
                hasControls: false,
                hasBorder: false,
            });
        }
    }

    protected setControlTarget(
        options: fabric.IObjectOptions,
        transform: Tgs.IAsset<Tgs.TAssetType>['transform'],
        name?: string,
        config?: {
            width: number;
            height: number;
        },
    ) {
        if (this.target) {
            this.rotateBeforeSetTarget(transform.rotate);
      
            const { x, y } = this.calcMatrixPoint(
                this.target,
                (options.left ?? transform.posX) + ((options.width ?? config?.width) as number) / 2,
                (options.top ?? transform.posY) + ((options.height ?? config?.height) as number) / 2,
            );
            this.target.setOptions({
                name: name || this.target.name,
                ...options,
                data: {
                    ...this.target.data,
                    ...(options?.data || {}),
                },
                scaleX: 1,
                scaleY: 1,
            });
            this.target.setPositionByOrigin({ x, y } as fabric.Point, 'center', 'center');
            this.rotateAfterSetTarget(transform.rotate);
            this.target.setCoords();

            if (this.helpAiDesignRect) {
                this.helpAiDesignRect.rotate(0)
                this.helpAiDesignRect.setOptions({
                    scaleX: 1,
                    scaleY: 1,
                    width: options.width || this.helpAiDesignRect.width,
                    height: options.height || this.helpAiDesignRect.height,
                    left: (options.left ?? transform.posX) || this.helpAiDesignRect.left,
                    top: (options.top ?? transform.posY) || this.helpAiDesignRect.top,
                })
                if (!this.target.data.group && !this.target.group) {
                    this.helpAiDesignRect.setPositionByOrigin({ x, y } as fabric.Point, 'center', 'center');
                } else {
                    this.helpAiDesignRect.setOptions({
                        top: ((options.top ?? transform.posY) || this.helpAiDesignRect.top) as number + (this.page.page.top ?? 0)
                    })
                }
                this.helpAiDesignRect.rotate(transform.rotate)
                this.helpAiDesignRect.setCoords();
            }
        }
    }

    /**
     * 更新 target 前设置旋转
     * @param angle - group时使用原始值，内部做了取-负处理
     */
    protected rotateBeforeSetTarget(angle: number) {
        if (this.target?.group) {
            this.target?.rotate(-(angle - (this.target.group.angle as number)));
        } else {
            this.target?.rotate(0);
        }
    }

    /**
     * 更新 target 后设置旋转
     */
    protected rotateAfterSetTarget(angle: number) {
        if (this.target?.group) {
            this.target?.rotate(angle - (this.target.group.angle as number));
        } else {
            this.target?.rotate(angle);
        }
    }

    /** 更新渲染对象的坐标  */
    protected updateRenderTargetSize(param: {
        angle: number;
        x: number;
        y: number;
        w?: number;
        h?: number;
        options?: fabric.IObjectOptions;
    }) {
        if (this.renderTarget) {
            this.renderTarget.rotate(0);
            const { x, y } = this.tranPageingPoint(this.renderTarget, param.x, param.y);
            this.renderTarget.setOptions({
                ...(param.options || {}),
                left: Math.floor(x),
                top: Math.floor(y),
            });
            this.renderTarget.rotate(param.angle);
            this.renderTarget?.setCoords();
        }
        // if(this.asset.meta.group){
        //     this.updateGroupHelperRectPostion(this.asset)
        // }
    }

    protected bindEvents() {
        this.bindRightClick();
    }

    protected removeEvents(target: fabric.Object | undefined = this.target) {
        if (target) {
            const eventKeys = [
                'selected',
                'deselected',
                'moving',
                'rotating',
                'rightclick',
                'mouseup',
                'deselected',
                'mousedblclick',
            ];
            for (const k in eventKeys) {
                target?.off(k);
            }
        }
    }

    // 设置操作状态 限制更新
    setActionSate(key: 'moving' | 'rotating' | 'scaling', value: boolean) {
        this[key] = value;
        if (key === 'moving') {
            this.target?.setOptions({
                hasControls: this.asset.meta.type === 'table' ? false : !value,
            });
        }
    }

    protected bindRightClick(target: fabric.Object | undefined = this.target) {
        target?.on('rightclick', (opt) => {
            if (!this.clipPage.containsPoint(opt.pointer as fabric.Point)) {
                return;
            }
            const viewportTransform = this.mainCanvas.viewportTransform;
            const scale = this.mainCanvas.getZoom();
            this.actions.displayRightClickMenu(opt.e as MouseEvent, {
                className: this.className as string,
                type: this.asset?.meta.type as string,
                x: viewportTransform?.[4] as number,
                y: viewportTransform?.[5] as number,
                scale,
            });
        });
    }
    /** 获取 transform 结束松开鼠标后的尺寸信息 */
    getTransformEndSize(origin?:boolean, skipGroup = false) {
        const { page } = this.page;
        const matrix = this.target?.calcTransformMatrix(skipGroup) as number[];
        if(!matrix){
            return {
                left: 0,
                top: 0,
                cx: 0,
                cy: 0,
                width: 0,
                height: 0,
                angle: 0,
                scaleX: 0,
                scaleY: 0,
                offsetX: 0,
                offsetY: 0,
            }
        }
        const transformInfo = qrDecompose(matrix);

        // 不能使用 getScaledWidth 和 getScaledHeight，group 中 member 的 scaleX 和 scaleY 不会变
        const width = Math.max((this.target?.width as number) * transformInfo.scaleX, 1);
        const height = Math.max((this.target?.height as number) * transformInfo.scaleY, 1);
        const cx = transformInfo.translateX;
        const cy = transformInfo.translateY;
        const x = cx - width / 2;
        const y = cy - height / 2;
        const left = page?.left || 0;
        const top = page?.top || 0;

        return origin ? {
            left: x,
            top: y,
            cx,
            cy,
            width: width,
            height: height,
            angle: transformInfo.angle,
            scaleX: transformInfo.scaleX,
            scaleY: transformInfo.scaleY,
            offsetX: left,
            offsetY: top,
}:{
            left: Math.floor(x),
            top: Math.floor(y),
            cx,
            cy,
            width: Math.floor(width),
            height: Math.floor(height),
            angle: transformInfo.angle,
            scaleX: transformInfo.scaleX,
            scaleY: transformInfo.scaleY,
            offsetX: Math.floor(left),
            offsetY: Math.floor(top),
        };
    }

    /** 计算元素矩阵坐标相对画布 0, 0 的坐标 */
    protected calcMatrixPoint(target: fabric.Object | undefined = this.target, x: number, y: number) {
        if (target?.group) {
            ({ x, y } = this.tranPageingPoint(target, x, y));
            const matrix = target.group.calcTransformMatrix() as number[];
            const point = fabric.util.transformPoint(new fabric.Point(x, y), invert(matrix));
            return point;
        } else {
            return this.tranPageingPoint(target, x, y);
        }
    }

    /** 多页坐标输入转换 */
    protected tranPageingPoint(target: fabric.Object | undefined = this.target, x: number, y: number) {
        if (target) {
            const { top: pTop = 0, left: pLeft = 0 } = this.page.page;
            return {
                x: x + pLeft,
                y: y + pTop,
            };
        } else {
            return { x, y };
        }
    }

    protected setRelatedMatrix(
        mainTaget: fabric.Object | fabric.Rect | fabric.Group | undefined,
        otherObjects: (fabric.Object | undefined)[],
        key = 'relationship',
    ) {
        if (mainTaget) {
            const mainMatrix = mainTaget.calcTransformMatrix();
            const invertMatrix = invert(mainMatrix);
            otherObjects.forEach((obj) => {
                if (obj) {
                    const desiredMatrix = multiply(invertMatrix, obj.calcTransformMatrix());
                    obj.setOptions({
                        data: obj.data
                            ? {
                                  ...obj.data,
                                  [key]: desiredMatrix,
                              }
                            : {
                                  [key]: desiredMatrix,
                              },
                    });
                }
            });
        }
    }

    protected calcRelatedMatrix(
        mainTaget: fabric.Object | fabric.Rect | fabric.Group | undefined,
        objects: (fabric.Object | undefined)[],
        key = 'relationship',
    ) {
        if (mainTaget) {
            objects.forEach((obj) => {
                if (obj?.data?.[key]) {
                    const newMatrix = multiply(mainTaget.calcTransformMatrix(), obj.data?.[key]);
                    const opt = qrDecompose(newMatrix);
                    const flipX = obj.flipX;
                    const flipY = obj.flipY;
                    obj.setOptions({
                        flipX: false,
                        flipY: false,
                    });
                    if (flipX) {
                        // opt.angle 会多 180，flipY 没影响
                        opt.angle -= 180;
                    }
                    obj.setPositionByOrigin(
                        { x: opt.translateX, y: opt.translateY } as fabric.Point,
                        'center',
                        'center',
                    );
                    obj.setOptions({
                        ...opt,
                        flipX,
                        flipY,
                    });
                    obj.setCoords();
                }
            });
        }
    }

    updateRelatedObjects(key: 'moving' | 'rotating' | 'scaling', opt: fabric.IEvent<MouseEvent>) {
        const { left, top, scaleX, scaleY, angle } = this.getTransformEndSize();
        switch (key) {
            case 'moving':
            case 'rotating': {
                this.renderTarget?.rotate(0);
                this.renderTarget?.setOptions({
                    left,
                    top,
                });
                this.renderTarget?.rotate(angle);
                break;
            }

            case 'scaling': {
                this.renderTarget?.rotate(0);
                this.renderTarget?.setOptions({
                    left,
                    top,
                    scaleX,
                    scaleY,
                });
                this.renderTarget?.rotate(angle);
                break;
            }
        }
    }

    protected updateRenderZIndex() {
        this.setRenderZIndex(this.className + '-render', this.renderTarget, this.renderTargetZIndexOffset);
        this.setRenderZIndex(this.className, this.target, this.targetZIndexOffset);
    }

    setAssetScaleCoordinate(pageIndex: number) {
        // if (typeof pageIndex === 'number' && pageIndex >= 0) {
        //     this.pageIndex = pageIndex;
        // }
        const {
            attribute: { width, height },
            transform,
        } = this.asset;
        if (this.target && !this.renderOnly) {
            const {width, height} = this.getTargetSize();
            this.setControlTarget({}, transform, undefined, {
                width: width,
                height: height,
            });
        }
        if (this.renderTarget) {
            this.updateRenderTargetSize({
                angle: transform.rotate,
                x: transform.posX,
                y: transform.posY,
                w: width,
                h: height,
            });
            this.renderTarget?.setCoords();
        }
        this.updateRenderZIndex();
    }

    /** 元素在缩放后可能要特殊显示 比如说表格的边框 */
    setAssetScaleParam() {
        /* maybe need override */
    }

    setVisible(value: boolean) {
        this.visible = value;
        this.target?.setOptions({ visible: value });
        this.renderTarget?.setOptions({ visible: value });
    }

    setClipPath(pageContainer: TgsCanvasPageAssets) {
        const container = pageContainer.page;
        if (container) {
            this.clipPage = container;
            this.target?.setOptions({ container });
            this.renderTarget?.setOptions({ container });
        }
        this.page = pageContainer;
    }
    /** 设置非当前选中页内的元素是否可以被选中 */
    setControllable(isSelected: boolean, isEvented: boolean) {
        this.target?.setOptions({ selectable: isSelected, evented: isEvented });
    }

    /** 移动元素时，触发辅助线吸附，则设置坐标 */
    setPositionOnMoving(opt: fabric.IEvent<MouseEvent>, cx: number, cy: number) {
        this.target?.setPositionByOrigin({ x: cx, y: cy } as fabric.Point, 'center', 'center');
        this.updateRelatedObjects('moving', opt);
    }

    /** 缩放元素时，触发辅助线吸附，则设置坐标 todo*/
    setPositionOnScaling(opt: fabric.IEvent<MouseEvent>, cx: number, cy: number, width: number, height: number) {
        this.updateRelatedObjects('scaling', opt);
    }

    isDblclickOnAsset(opt: fabric.IEvent<MouseEvent>) {
        const pointer = opt.pointer as fabric.Point;
        // 画布里面而且还要在当前页面
        if (this.target?.containsPoint(pointer) && this.page.page.containsPoint(pointer)) {
            return true;
        }
        return false;
    }
    // 组内元素的辅助选中展示框
    protected hideGroupHelperRect() {
        this.target?.data?.groupHelperRectTarget?.setOptions({
            stroke: 'transparent',
            strokeWidth: 0,
        });
    }
    protected updateGroupHelperRectPostion(asset: Tgs.IAsset<Tgs.TAssetType>) {
        if (asset) {
            const { attribute, transform } = asset;
            const { top: pTop = 0, left: pLeft = 0 } = this.page.page;
            const x = transform.posX + (attribute.width - 2) / 2 + pLeft;
            const y = transform.posY + (attribute.height - 2) / 2 + pTop;
            this.target?.data?.groupHelperRectTarget?.setOptions({
                width: attribute.width,
                height: attribute.height,
            });
            this.target?.data?.groupHelperRectTarget?.rotate(0);
            this.target?.data?.groupHelperRectTarget?.setPositionByOrigin({ x, y } as fabric.Point, 'center', 'center');
            this.target?.data?.groupHelperRectTarget?.rotate(transform.rotate);
        }
        this.mainCanvas.requestRenderAll();
    }
    protected onGroupUpdate() {
        // needs to be override
    }
    protected showGroupHelperRect(asset?: Tgs.IAsset<Tgs.TAssetType>) {
        const width = 2 / this.mainCanvas.getZoom();
        this.target?.data?.groupHelperRectTarget?.setOptions({
            stroke: '#EF3964',
            strokeWidth: width,
        });
        this.updateGroupHelperRectPostion(asset as Tgs.IAsset<Tgs.TAssetType>);
    }
    protected checkIsOverPageEvent(opt: fabric.IEvent<MouseEvent>) {
        if (this.page.page.pageType === 'board') {
            return false;
        }
        return !this.page.page.containsPoint(opt.pointer as fabric.Point);
    }

    protected getTargetSize() {
        return {
            width: this.target?.width as number,
            height: this.target?.height as number,
        };
    }

    protected setLinkLineMap() {
        const assets = this.page.assets;
        if (this.asset.meta.linkedLineIds) {
            this.asset.meta.linkedLineIds.forEach((id) => {
                assets.forEach((a) => {
                    if (a.meta.type === 'line' && a.meta.uniqueId === id) {
                        this.linkMap[id] = a.meta.className;
                    }
                })
            });
        }
    }

    addLinkLineMap(id: string, className: string) {
        this.linkMap[id] = className;
    }

    removeLinkLineMap(id: string) {
        delete this.linkMap[id];
    }


    // 获取 ml, mr, mt, mb 的位置
    getControlPositions() {
        const coords = this.target?.aCoords;

        // 计算中点位置
        const ml = coords
            ? {
                  x: (coords.tl.x + coords.bl.x) / 2,
                  y: (coords.tl.y + coords.bl.y) / 2,
              }
            : { x: 0, y: 0 };
        const mr = coords
            ? {
                  x: (coords.tr.x + coords.br.x) / 2,
                  y: (coords.tr.y + coords.br.y) / 2,
              }
            : { x: 0, y: 0 };
        const mt = coords
            ? {
                  x: (coords.tl.x + coords.tr.x) / 2,
                  y: (coords.tl.y + coords.tr.y) / 2,
              }
            : { x: 0, y: 0 };
        const mb = coords
            ? {
                  x: (coords.bl.x + coords.br.x) / 2,
                  y: (coords.bl.y + coords.br.y) / 2,
              }
            : { x: 0, y: 0 };

        return { ml, mr, mt, mb };
    }

    /** svg 元素 clone 形状 */
    cloneShape(): Promise<fabric.Object[]> | void {
    }

}
