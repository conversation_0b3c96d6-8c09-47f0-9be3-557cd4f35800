import equal from 'fast-deep-equal';
import { fabric } from 'fabric';
import { Asset } from '../Asset';
import { Util } from '../../../util';
import { CustomPath } from './customPath';
import { circleGrayIconData } from '../../IconData';
import type { IUpdateTarget } from '../../../actions';
import type { TCanvasAsset } from '../index';
import LinePath,{ Point } from './LinePath';

const controlRadius = 10;
const transformPoint = fabric.util.transformPoint;
export class LineRenderOnly extends Asset {
    readonly type = 'line';
    declare asset: Tgs.ILineAsset;

    protected lineLayer: fabric.Line | undefined;
    protected arrowSize: number = 10;
    protected startArrowLayer: fabric.Triangle | undefined;
    protected endArrowLayer: fabric.Triangle | undefined;
    protected arrowMoving: boolean = false;
    protected linkObjectChanging: boolean = false;

    // protected points: { x: number; y: number }[] = [];
    protected middles: { x: number; y: number }[] = [];

    /** 控制器起始点 */
    protected startPoint: { x: number; y: number } = { x: 0, y: 0 };
    protected endPoint: { x: number; y: number } = { x: 0, y: 0 };
    protected first: fabric.Object | undefined;
    protected last: fabric.Object | undefined;
    protected startAnchor: 'ml' | 'mr' | 'mt' | 'mb' | undefined;
    protected endAnchor: 'ml' | 'mr' | 'mt' | 'mb' | undefined;

    override async init() {
        await this.render();
        this.resetTarget();
        return true;
    }

    override async update(asset: Tgs.IAsset<Tgs.TAssetType>, index: number, pageIndex: number) {
        this.setIndexAndGroup(index, pageIndex, asset);
        asset = asset as Tgs.ILineAsset;
        const isSameAttribute = equal(this.asset.attribute, asset.attribute);
        const isSameTransform = equal(this.asset.transform, asset.transform);
        const isSameStart = asset.attribute?.start && equal(this.asset.attribute?.start, asset.attribute?.start);
        const isSameEnd = asset.attribute?.end && equal(this.asset.attribute?.end, asset.attribute?.end);
        const isSameType = this.asset.attribute.type === asset.attribute.type;
        const isSameStartObj = equal(this.asset.attribute.startObj, asset.attribute.startObj);
        const isSameEndObj = equal(this.asset.attribute.endObj, asset.attribute.endObj);
        this.asset = asset as Tgs.ILineAsset;
        if (this.rotating || this.scaling || this.moving) {
            return true;
        }
        const {
            attribute: { width, height },
            transform,
        } = this.asset;
        if (!isSameAttribute) {
            if (this.target) {
                this.setControlTarget(
                    {
                        width,
                        height,
                    },
                    transform,
                );
            }
            if (!isSameType || !isSameStart || !isSameEnd) {
                this.points = [];
            }
            if (!isSameStartObj) {
                this.setFirstTarget();
            }
            if (!isSameEndObj) {
                this.setEndTarget();
            }
            this.updateLine();
            this.updateArrow();
            !this.skipRequestRender() && this.mainCanvas.requestRenderAll();
        }
        if (!isSameTransform || !isSameStart || !isSameEnd || !isSameType) {
            if (this.target) {
                const path = this.getTargetPath();
                this.target?.setOptions({
                    path,
                    strokeWidth: this.asset.attribute.lineWidth,
                    strokeLineCap: 'round',
                });
                this.setControlTarget(
                    {
                        width: width,
                        height: height,
                        left: transform.posX,
                        top: transform.posY,
                    },
                    transform,
                );
            }
            this.updateRenderTargetSize({
                angle: 0,
                x: transform.posX,
                y: transform.posY,
                options: {
                    flipX: transform.horizontalFlip,
                    flipY: transform.verticalFlip,
                },
            });
            !this.skipRequestRender() && this.mainCanvas.requestRenderAll();
        }
        if (this.asset.meta.group && (!isSameAttribute || !isSameTransform)) {
            this.updateGroupHelperRectPostion(this.asset);
        }
        this.updateRenderZIndex();
        return true;
    }
    /**
     * 获取target的相对坐标
     */
    getTargetXYCoord(posx?: number, posy?: number) {
        const { start, end } = this.asset.attribute;
        let { posX, posY } = this.asset.transform;
        if (posx !== undefined && posy !== undefined) {
            posX = posx;
            posY = posy;
        }
        const { top = 0 } = this.page.page;
        const x1 = start.x + posX;
        const y1 = start.y + posY + top;
        const x2 = end.x + posX;
        const y2 = end.y + posY + top;
        this.setStartPoint(x1, y1);
        this.setEndPoint(x2, y2);
        return { start: {x: x1, y: y1}, end: {x: x2, y: y2} };
    }

    override setLinkLineMap() {
        const assets = this.page.assets;
        if (this.asset.attribute.startObj || this.asset.attribute.endObj) {
            const { startObj, endObj } = this.asset.attribute;
            const ids = [startObj?.id, endObj?.id];
            ids.forEach((id) => {
                if (!id) return;
                assets.forEach((a) => {
                    if (a.meta.uniqueId === id) {
                        this.addLinkLineMap(id, a.meta.className);
                    }
                });
            });
        }
    }

    override addLinkLineMap(id: string, className: string) {
        const { startObj, endObj } = this.asset.attribute;
        this.linkMap[id] = className;
        if (startObj?.id === id && this.page.assetsMap[className]) {
            this.first = (this.page.assetsMap[className] as TCanvasAsset).target;
            this.startAnchor = startObj.anchor;
        }
        if (endObj?.id === id && this.page.assetsMap[className]) {
            this.last = (this.page.assetsMap[className] as TCanvasAsset).target;
            this.endAnchor = endObj.anchor;
        }
    }

    override removeLinkLineMap(id: string) {
        const { startObj, endObj } = this.asset.attribute;
        if (startObj?.id === id) {
            this.first = undefined;
        }
        if (endObj?.id === id) {
            this.last = undefined;
        }
        delete this.linkMap[id];
    }

    /** 更新箭头形状以及连接线类型 */
    protected updateArrow() {
        const pathStr = this.createArrowPath();
        // @ts-ignore
        const path = fabric.util.makePathSimpler(fabric.util?.parsePath(pathStr));
        if (path) {
            this.renderTarget?.setOptions({ path: path, scaleX: 1, scaleY: 1, angle: 0 });
            this.renderTarget?.setCoords(); // 更新坐标
        }
    }

    /** 更新线颜色,线宽 */
    protected updateLine() {
        const { color, lineWidth, opacity } = this.asset.attribute;
        if (!this.lineLayer || !color) return;
        const strokeColor = color ? Util.rgbaToString(color) : '#000';
        this.renderTarget?.setOptions({
            stroke: strokeColor,
            strokeWidth: lineWidth,
            opacity: typeof opacity === 'number' ? opacity / 100 : 1,
        });
    }

    override updateRelatedObjects(key: 'moving' | 'rotating' | 'scaling', opt: fabric.IEvent<MouseEvent>) {
        this.linkObjectChanging = true;
        switch (key) {
            case 'moving':
            case 'rotating': {
                const { left, top, angle, } = this.getTransformEndSize();
                // 判断起点终点有没有吸附点
                const {start, end} = this.getTargetXYCoord(left, top);
                const startNearPoint = this.page.getPointerNearObject(new fabric.Point(start.x, start.y), 'start');
                const endNearPoint = this.page.getPointerNearObject(new fabric.Point(end.x, end.y), 'end');
                if(startNearPoint || endNearPoint) {
                    startNearPoint && this.updateTargetByPointer('start', startNearPoint.x, startNearPoint.y);
                    endNearPoint && this.updateTargetByPointer('end', endNearPoint.x, endNearPoint.y);
                    return;
                }

                this.renderTarget?.rotate(0);
                this.renderTarget?.setOptions({
                    left,
                    top,
                });
                this.renderTarget?.rotate(angle);
                break;
            }
            case 'scaling': {
                const { left, top, width, height, start, end } = this.scaleLine();
                const pathStr = this.getPath(start, end);
                // @ts-ignore
                this.renderTarget?._setPath(pathStr);
                this.renderTarget?.setOptions({
                    left,
                    top,
                    width,
                    height,
                });
                break;
            }
        }
        this.linkObjectChanging = false;
    }

    protected getLineCoord() {
        const { attribute, transform } = this.asset;
        const { lineWidth, width, height, start, end } = attribute;
        this.arrowSize = lineWidth * 4;
        const halfArrowSize = this.arrowSize / 2;
        const halfLineWidth = 0;
        // const lineXSize = startArrow ? width - this.arrowSize : width - halfArrowSize;
        const startPoint = { x: start.x - halfLineWidth, y: start.y - halfLineWidth };
        const endPoint = { x: end.x - halfLineWidth, y: end.y - halfLineWidth };
        return { startPoint, endPoint };
    }

    protected override async renderTargets() {
        const { attribute, transform} = this.asset;
        const { color, lineWidth } = attribute;
        const strokeColor = color ? Util.rgbaToString(color) : '#000';
        const path = this.createArrowPath();
        // @ts-ignore
        const line = new CustomPath(path, {
            stroke: strokeColor,
            strokeWidth: lineWidth,
            selectable: false,
            evented: false,
            strokeLineCap: 'round', // 圆角线端
            strokeUniform: true,
        });

        this.lineLayer = line;
        this.renderTarget = line;
        const options: fabric.IObjectOptions = {
            data: {
                className: this.className,
                group: this.groupName,
                pageIndex: this.pageIndex,
            },
            left: transform.posX,
            top: transform.posY,
            opacity: typeof attribute.opacity === 'number' ? attribute.opacity / 100 : 1,
            centeredRotation: true,
            container: this.clipPage,
            visible: this.visible,
        };
        this.renderTarget?.setOptions({
            ...options,
            name: 'line-render-' + this.className,
            evented: false,
            hasBorders: false,
            selectable: false,
            fill: '', // 不能给值 不然会有闭合图形
            stroke: strokeColor,
            objectCaching: false,
        });
        this.renderTarget?.setCoords();

    }
    protected resetTarget() {
        // @ts-ignore
        const path = this.getTargetPath();
        this.target?.setOptions({
            path: path,
            fill: '',
            strokeWidth: this.asset.attribute.lineWidth,
            strokeLineCap: 'round',
            hasBorders: false,
            strokeUniform: true,
            ...this.getTargetSize(),
        });
        this.setControlTarget({}, this.asset.transform, undefined, this.getTargetSize());
        const changeArrowPoint = function (eventData: MouseEvent, transform: fabric.Transform, x: number, y: number) {
            return true;
        };
        // @ts-ignore
        const arrowMovingAction = fabric.controlsUtils.wrapWithFireEvent('arrow:moving', changeArrowPoint);
        const grayIcon = Util.loadImage(circleGrayIconData);
        // 直线控制起点
        if (this.target) {
            this.target.controls = {
                startArrow: new fabric.Control({
                    x: -0.5,
                    y: -0.5,
                    offsetX: 0,
                    offsetY: 0,
                    cursorStyle: 'pointer',
                    visible: true,
                    // @ts-ignore
                    actionHandler: arrowMovingAction,
                    actionName: 'start',
                    positionHandler: (dim, finalMatrix, fabricObject, currentControl) => {
                        const center = fabricObject.getCenterPoint();
                        const x = this.startPoint.x - center.x;
                        const y = this.startPoint.y - center.y;
                        const vpt = fabricObject.getViewportTransform();
                        const p = transformPoint(new fabric.Point(x, y), vpt, true);
                        const point = transformPoint(p, finalMatrix);
                        return point;
                    },
                    render(ctx, left, top, styleOverride, fabricObject) {
                        const rotateAngle = fabricObject?.angle ? fabricObject.angle : 0;
                        ctx.save();
                        ctx.translate(left, top);
                        ctx.rotate(fabric.util.degreesToRadians(rotateAngle));
                        ctx.drawImage(grayIcon, -controlRadius / 2, -controlRadius / 2, controlRadius, controlRadius);
                        ctx.restore();
                    },
                }),
                endArrow: new fabric.Control({
                    x: 0.5,
                    y: 0.5,
                    offsetX: 0,
                    offsetY: 0,
                    cursorStyle: 'pointer',
                    visible: true,
                    actionHandler: arrowMovingAction,
                    actionName: 'end',
                    positionHandler: (dim, finalMatrix, fabricObject, currentControl) => {
                        const center = fabricObject.getCenterPoint();
                        const x = this.endPoint.x - center.x;
                        const y = this.endPoint.y - center.y;
                        const vpt = fabricObject.getViewportTransform();
                        const p = transformPoint(new fabric.Point(x, y), vpt, true);
                        const point = transformPoint(p, finalMatrix);
                        return point;
                    },
                    render(ctx, left, top, styleOverride, fabricObject) {
                        const rotateAngle = fabricObject?.angle ? fabricObject.angle : 0;
                        ctx.save();
                        ctx.translate(left, top);
                        ctx.rotate(fabric.util.degreesToRadians(rotateAngle));
                        ctx.drawImage(grayIcon, -controlRadius / 2, -controlRadius / 2, controlRadius, controlRadius);
                        ctx.restore();
                    },
                }),
            };
            this.target?.setControlsVisibility({
                mt: false,
                mb: false,
                ml: false,
                mr: false,
                bl: false,
                br: false,
                tl: false,
                tr: false,
            });
        }
    }

    /** 直线箭头 */
    protected createArrowPath() {
        const { startPoint, endPoint } = this.getLineCoord();

        const path = this.getPath(startPoint, endPoint);
        return path;
    }

    protected getPath(start: Point, end: Point) {
        const { startArrow = 'line', endArrow = 'line' } = this.asset.attribute;

        let path = this.getLinePath(start, end);
        if (startArrow === 'arrow') {
            const start = this.points[0];
            const end = this.points[1];
            path += LinePath.getStartPath(start.x, start.y, end.x, end.y);
        }
        if (endArrow === 'arrow') {
            const start = this.points[this.points.length - 2];
            const end = this.points[this.points.length - 1];
            path += LinePath.getEndPath(start.x, start.y, end.x, end.y);
        }
        return path;
    }
    
    getLinePoints(start: Point, end: Point) {
        const { type, points } = this.asset.attribute;
        const { x: x1, y: y1 } = start;
        const { x: x2, y: y2 } = end;
        let newPoints = points;
        if (type === 'line') {
            newPoints = [
                { x: x1, y: y1 },
                { x: x2, y: y2 },
            ];
        } else if (type === 'poly') {
            if (this.arrowMoving || this.linkObjectChanging || !points) {
                newPoints = this.getPolyLinePoints(x1, y1, x2, y2);
                if (!points) {
                    this.actions.onUpdateAssets('CHANGE_POLYLINE_POINTS', [{
                        index: this.index,
                        pageIndex: this.pageIndex,
                        changes:{
                            attribute: {
                                points: newPoints,
                            },
                        }
                    }], true);
                }
            } 
        }
        return newPoints
    }

    getLinePath(start: Point, end: Point, isRenderTarget = true) {
        let points = this.points;
        if (this.arrowMoving ||  this.linkObjectChanging || !this.points?.length) {
            points = this.getLinePoints(start, end) || [];
            if (isRenderTarget) {
                this.points = points || [];
            } 
        }
        return LinePath.createLinePath(points);
    }

    getPolyLinePoints(x1: number, y1: number, x2: number, y2: number) {
        const zoom = this.mainCanvas.getZoom();
        const start = { x: x1, y: y1 };
        const end = { x: x2, y: y2 };
        !this.first && this.setFirstTarget();
        !this.last && this.setEndTarget();
        return LinePath.normalLayout(start, end, this.first, this.last, this.startAnchor, this.endAnchor, zoom);
    }

    protected getTargetPathStr(start: Point, end: Point) {
        if (!start || !end) return;
        const pathStr = this.getLinePath(start, end, false);
        // @ts-ignore
        const path = fabric.util.makePathSimpler(fabric.util?.parsePath(pathStr));
        return path;
    }

    getTargetPath() {
        const {start, end} = this.getTargetXYCoord();
        return this.getTargetPathStr(start, end);
    }

    protected updateTargetByPointer(key: string, x: number, y: number) {
        if (key === 'start') {
            this.setStartPoint(x, y);
            this.setFirstTarget();
        } else {
            this.setEndPoint(x, y);
            this.setEndTarget();
        }
        const { start, end, left, top, width, height } = this.getlineRectBound(
            this.startPoint.x,
            this.startPoint.y,
            this.endPoint.x,
            this.endPoint.y,
        );
        const targetPath = this.getTargetPathStr(start, end);
        this.target?.setOptions({ path: targetPath, left, top, width, height });
        this.target?.setCoords();
        const pathStr = this.getPath(start, end);
        // @ts-ignore
        this.renderTarget?._setPath(pathStr);
        this.renderTarget?.setOptions({
            left,
            top,
            width,
            height,
        });
        this.renderTarget?.setCoords();
        this.mainCanvas.requestRenderAll(); // 刷新画布
        this.linkObjectChanging = false;
    }

    override getTransformEndSize(origin?: boolean, skipGroup = false) {
        const rs = super.getTransformEndSize(origin, skipGroup);
        const lineWidth = this.asset.attribute.lineWidth;
        const halfWidth = lineWidth / 2;
        const { width, height, left, top } = rs;
        const transform = {
            left: left - halfWidth,
            top: top - halfWidth,
            width: width - lineWidth,
            height: height - lineWidth,
        };
        return { ...rs, ...transform };
    }

    protected getOriginalACoords(obj: fabric.Object, group: fabric.Group) {
        if (!group) return obj.aCoords; // 如果没有分组，直接返回

        const matrix = group.calcTransformMatrix(); // 获取 Group 变换矩阵
        if (!obj.aCoords) return null;
        return {
            tl: transformPoint(obj.aCoords.tl, matrix),
            tr: transformPoint(obj.aCoords.tr, matrix),
            bl: transformPoint(obj.aCoords.bl, matrix),
            br: transformPoint(obj.aCoords.br, matrix),
        };
    }

    // 获取 ml, mr, mt, mb 的位置
    protected getOtherControlPositions(object: fabric.Object) {
        const coords = this.getOriginalACoords(object, object.group as fabric.Group);
        // 计算中点位置
        const ml = coords
            ? {
                  x: (coords.tl.x + coords.bl.x) / 2,
                  y: (coords.tl.y + coords.bl.y) / 2,
              }
            : { x: 0, y: 0 };
        const mr = coords
            ? {
                  x: (coords.tr.x + coords.br.x) / 2,
                  y: (coords.tr.y + coords.br.y) / 2,
              }
            : { x: 0, y: 0 };
        const mt = coords
            ? {
                  x: (coords.tl.x + coords.tr.x) / 2,
                  y: (coords.tl.y + coords.tr.y) / 2,
              }
            : { x: 0, y: 0 };
        const mb = coords
            ? {
                  x: (coords.bl.x + coords.br.x) / 2,
                  y: (coords.bl.y + coords.br.y) / 2,
              }
            : { x: 0, y: 0 };

        return { ml, mr, mt, mb };
    }

    /**链接元素位置改变， 连接线位置跟随改变*/
    updatePositionByLinkObject(object: fabric.Object, objectUniqueId?: string) {
        if (!objectUniqueId) return;
        const rs = this.getLinkObjectPosition(object, objectUniqueId);
        if (!rs) return;
        const { x, y, key } = rs;
        this.linkObjectChanging = true;
        this.updateTargetByPointer(key, x, y);
    }

    getLinkObjectPosition(object: fabric.Object, objectUniqueId?: string) {
        object.setCoords();
        const coords = this.getOtherControlPositions(object);
        const { startObj, endObj } = this.asset.attribute;
        let key = null;
        if (startObj?.id === objectUniqueId) {
            key = 'start';
        } else if (endObj?.id === objectUniqueId) {
            key = 'end';
        }
        if (!key) return null;
        const corner = key === 'start' ? startObj?.anchor : endObj?.anchor;
        if (corner && coords[corner]) {
            const { x, y } = coords[corner];
            return { x, y, key };
        }
        return null;
    }

    getChangeData(skipGroup = false) {
        const { top: pageTop = 0 } = this.page.page;
        const { left, top, width, height, start, end } = this.getlineRectBound(
            this.startPoint.x,
            this.startPoint.y,
            this.endPoint.x,
            this.endPoint.y,
        );

        return {
            attribute: {
                width,
                height,
                startObj: this.asset.attribute.startObj,
                endObj: this.asset.attribute.endObj,
                start,
                end,
                points: this.points,
            },
            transform: {
                posX: left,
                posY: top - pageTop,
                angle: 0,
            },
        };
    }
    /** redux change */
    onMouseUpLineChange(skipGroup = false) {
        const lineChange = {
            className: this.className,
            index: this.index,
            changes: this.getChangeData(skipGroup),
        };
        return lineChange;
    }

    unbindAsset() {
        // 判断linkMap中是否有绑定的关系，如果有绑定的asset，则移除绑定
        const { startObj, endObj } = this.asset.attribute || {};
        const linkIds = [startObj?.id, endObj?.id].filter(Boolean);
        if (linkIds.length) {
            const updateTargets: IUpdateTarget[] = [];
            linkIds.forEach((id) => {
                if (id === undefined) return;
                const linkClassName = this.linkMap[id];
                const linkAsset = this.page.assetsMap[linkClassName];
                if (linkAsset) {
                    const linkedLineIds = linkAsset.asset.meta?.linkedLineIds || [];
                    const lineId = this.asset.meta?.uniqueId;
                    const index = linkedLineIds.indexOf(lineId);
                    if (index > -1) {
                        linkedLineIds.splice(index, 1);
                    }
                    updateTargets.push({
                        className: linkClassName,
                        index: linkAsset.index,
                        changes: {
                            meta: {
                                linkedLineIds,
                            },
                        },
                    });
                    this.removeLinkLineMap(id);
                }
            });
            return updateTargets;
        }
    }

    override getTargetSize() {
        const { width, height } = this.asset.attribute;
        const lineWidth = this.asset.attribute.lineWidth;
        // const { width: w = 0, height: h = 0 } = this.target as fabric.Line;
        // width = w + lineWidth;
        // height = h + lineWidth;
        return { width, height };
    }

    getlineRectBound(x1New: number, y1New: number, x2New: number, y2New: number) {
        const width = Math.abs(x2New - x1New);
        const height = Math.abs(y2New - y1New);

        const start = {
            x: 0,
            y: 0,
        };
        const end = {
            x: width,
            y: height,
        };
        if (x1New > x2New) {
            start.x = width;
            end.x = 0;
        }
        if (y1New > y2New) {
            start.y = height;
            end.y = 0;
        }
        const left = Math.min(x1New, x2New);
        const top = Math.min(y1New, y2New);
        return {
            left,
            top,
            width,
            height,
            start,
            end,
        };
    }

    rotateLine() {
        // 获取旋转后的x1, x2, y1, y2坐标
        const { x1 = 0, x2 = 0, y1 = 0, y2 = 0 } = this.target as fabric.Line;
        this.target?.setCoords(); // 确保坐标已更新

        const activeSelection = this.mainCanvas.getActiveObject(); // 获取多选对象
        const center = activeSelection?.getCenterPoint() as fabric.Point; // 计算多选框的中心点
        const angle = activeSelection?.angle || 0; // 获取旋转角度
        const radian = fabric.util.degreesToRadians(angle); // 获取旋转角度（转换为弧度）

        const cosA = Math.cos(radian);
        const sinA = Math.sin(radian);

        // 计算 line 的原始端点相对多选框中心的偏移量
        const x1Offset = x1 - center.x;
        const y1Offset = y1 - center.y;
        const x2Offset = x2 - center.x;
        const y2Offset = y2 - center.y;

        // 旋转矩阵计算新坐标
        const x1New = center.x + (x1Offset * cosA - y1Offset * sinA);
        const y1New = center.y + (x1Offset * sinA + y1Offset * cosA);
        const x2New = center.x + (x2Offset * cosA - y2Offset * sinA);
        const y2New = center.y + (x2Offset * sinA + y2Offset * cosA);

        return this.getlineRectBound(x1New, y1New, x2New, y2New);
    }

    scaleLine() {
        const { startObj, endObj } = this.asset.attribute;
        this.target?.setCoords(); // 确保坐标已更新
        const line = this.target as fabric.Line;
        const coord = this.getOriginalACoords(line, line.group as fabric.Group);
        let start: { x?: number; y?: number } = {},
            end: { x?: number; y?: number } = {};
        [startObj, endObj].forEach((item) => {
            if (item && item.id) {
                const className = this.linkMap[item.id];
                if (className) {
                    const asset = this.page.assetsMap[className];
                    if (asset) {
                        const rs = this.getLinkObjectPosition(asset.target as fabric.Object, item.id);
                        if (rs) {
                            const { x, y, key } = rs;
                            if (key && x && y && key === 'start') {
                                start = { x, y };
                            }
                            if (key === 'end' && x && y) {
                                end = { x, y };
                            }
                        }
                    }
                }
            }
        });
        const rs = {
            x1: start?.x ? start?.x : coord?.tl.x,
            y1: start?.y ? start?.y : coord?.tl.y,
            x2: end?.x ? end?.x : coord?.br.x,
            y2: end?.y ? end?.y : coord?.br.y,
        };
        // @ts-ignore
        return this.getlineRectBound(rs.x1, rs.y1, rs.x2, rs.y2);
    }

    getLineTransformSize(
        key: 'moving' | 'rotating' | 'scaling',
        updateTargets: IUpdateTarget[],
        selectNames: string[],
    ) {
        this.linkObjectChanging = true;
        const type = this.asset.attribute.type;
        let changes: Tgs.DeepPartial<Tgs.IAsset<Tgs.TAssetType>> = {
            attribute: {
                points: type === 'poly' ? this.points : undefined,
            }
        };
        const { top: pageTop = 0 } = this.page.page;

        switch (key) {
            case 'moving': {
                const { left, top, offsetX, offsetY } = this.getTransformEndSize();
                changes.transform = {
                    posX: left - offsetX,
                    posY: top - offsetY,
                };
                if (selectNames?.length === 1) {
                    const startNearObject = this.getNearObjectData('start');
                    const endNearObject = this.getNearObjectData('end');
                    if (startNearObject) {
                        const { uniqueId, className, index, anchor, changes: change } = startNearObject;
                        changes.attribute = {
                            ...changes.attribute,
                            startObj: {
                                id: uniqueId,
                                anchor,
                            },
                        };
                        updateTargets.push({
                            className,
                            index,
                            changes: change,
                        });
                    }
                    if (endNearObject) {
                        const { uniqueId, className, index, anchor, changes: change } = endNearObject;
                        changes.attribute = {
                            ...changes.attribute,
                            endObj: {
                                id: uniqueId,
                                anchor,
                            },
                        };
                        updateTargets.push({
                            className,
                            index,
                            changes: change,
                        });
                    }
                    // 单独挪动线时解绑并且没有靠近的object的时候解绑
                    if (this.hasLinkObject() && !startNearObject && !endNearObject) {
                        changes.attribute = {
                            ...changes.attribute,
                            startObj: undefined,
                            endObj: undefined,
                        };
                        const linkAssetChange = this?.unbindAsset();
                        linkAssetChange && updateTargets.push(...linkAssetChange);
                    }
                } else {
                    const linkClassNames = Object.values(this.linkMap);
                    if (this?.hasLinkObject() && !linkClassNames.every((item) => selectNames.includes(item))) {
                        changes = this.getChangeData(true);
                    }
                }
                break;
            }
            case 'rotating': {
                const { start, end, left, top, width, height } = this.rotateLine();
                changes = {
                    attribute: {
                        start,
                        end,
                        width,
                        height,
                    },
                    transform: {
                        posX: left,
                        posY: top - pageTop,
                        rotate: 0,
                    },
                };
                break;
            }
            case 'scaling': {
                const { start, end, left, top, width, height } = this.scaleLine();
                changes = {
                    attribute: {
                        start,
                        end,
                        width,
                        height,
                        points: type === 'poly' ? this.points : undefined,
                    },
                    transform: {
                        posX: left,
                        posY: top - pageTop,
                        rotate: 0,
                    },
                };
                const linkClassNames = Object.values(this.linkMap);
                if (this?.hasLinkObject() && !linkClassNames.every((item) => selectNames.includes(item))) {
                    changes = this.getChangeData(true);
                }
                break;
            }
        }
        this.linkObjectChanging = false;
        updateTargets.push({
            className: this.className,
            index: this.index,
            changes,
        });
        this.page.page?.fire('line:mouse:up')
    }

    getNearObjectData(key: 'start' | 'end' = 'start') {
        let nearObject = this.page.startClosestObject;
        let anchor = this.page.startLinkCorner;
        if (key === 'end') {
            nearObject = this.page.endClosestObject;
            anchor = this.page.endLinkCorner;
        }
        if (nearObject) {
            const closestClassName = nearObject.data?.className;
            const closetAsset = this.page.assetsMap[closestClassName];
            if (closetAsset) {
                const uniqueId = closetAsset.asset.meta?.uniqueId
                    ? closetAsset.asset.meta?.uniqueId
                    : Util.generateShortUUID();
                const closestLinkLineIds = closetAsset.asset.meta?.linkedLineIds || [];
                const lineId = this.asset.meta?.uniqueId;
                if (!closestLinkLineIds.includes(lineId)) {
                    closestLinkLineIds.push(lineId);
                }

                this.addLinkLineMap(uniqueId, closestClassName);
                closetAsset?.addLinkLineMap(lineId, this.className);
                return {
                    uniqueId,
                    className: closestClassName,
                    index: closetAsset.index,
                    anchor,
                    changes: {
                        meta: {
                            uniqueId,
                            linkedLineIds: closestLinkLineIds,
                        },
                    },
                }
            }
        }
        return null
    }

    /** 判断是否有连接的对象 */
    hasLinkObject() {
        return this.asset.attribute.startObj || this.asset.attribute.endObj;
    }

    protected setStartPoint(x: number, y: number) {
        this.startPoint = { x, y };
    }

    protected setEndPoint(x: number, y: number) {
        this.endPoint = { x, y };
    }

    protected setFirstTarget() {
        if (this.page.startClosestObject && this.page.startLinkCorner) {
            this.first = this.page.startClosestObject;
            this.startAnchor = this.page.startLinkCorner;
        } else if (this.asset.attribute.startObj) {
            const { startObj } = this.asset.attribute;
            const className = this.linkMap[startObj.id];
            this.startAnchor = startObj.anchor;
            this.first = this.page.assetsMap[className]?.target;
        } else {
            this.first = undefined;
            this.startAnchor = undefined;
        }
    }

    protected setEndTarget(target?: fabric.Object, anchor?: 'ml' | 'mr' | 'mt' | 'mb' | undefined) {
        if (target && anchor) {
            this.last = target;
            this.endAnchor = anchor;
        } else if (this.page.endClosestObject && this.page.endLinkCorner) {
            this.last = this.page.endClosestObject;
            this.endAnchor = this.page.endLinkCorner;
        } else if (this.asset.attribute.endObj) {
            const { endObj } = this.asset.attribute;
            const className = this.linkMap[endObj.id];
            this.endAnchor = endObj.anchor;
            this.last = this.page.assetsMap[className]?.target;
        } else {
            this.last = undefined;
            this.endAnchor = undefined;
        }
    }
}
