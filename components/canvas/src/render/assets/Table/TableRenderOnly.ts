/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-ignore
import type { Table } from '@tgs/ips_fabric';
import { fabric } from 'fabric';
import equal from 'fast-deep-equal';
import { TableToImage } from '../../../tableToImage';
import { Asset } from '../Asset';
import { klona } from 'klona';

export class TableRenderOnly extends Asset {
    readonly type = 'table';
    override renderOnly = true;
    declare asset: Tgs.ITableAsset;
    declare renderTarget: fabric.Image | undefined;

    protected tableRenderTarget: Table | undefined;
    private cacheCanvas = document.createElement('canvas');
    private cacheCtx = this.cacheCanvas.getContext('2d') as CanvasRenderingContext2D;

    protected override editing = false;

    override async init() {
        if (!this.cacheCanvas) {
            this.cacheCanvas = document.createElement('canvas');
            this.cacheCtx = this.cacheCanvas.getContext('2d') as CanvasRenderingContext2D;
        }
        await this.render();
        return true;
    }

    override async update(asset: Tgs.IAsset<Tgs.TAssetType>, index: number, pageIndex: number) {
        this.setIndexAndGroup(index, pageIndex, asset);
        const isSameAttribute = equal(this.asset.attribute, asset.attribute);
        const isSameTransform = equal(this.asset.transform, asset.transform);
        const isSameWidthHeight =
            this.asset.attribute.width === asset.attribute.width &&
            this.asset.attribute.height === asset.attribute.height;
        this.asset = asset as Tgs.ITableAsset;
        if (this.rotating || this.scaling || this.moving || this.editing) {
            if (this.editing) {
                if (!isSameWidthHeight || !isSameTransform) {
                    if (this.target) {
                        this.setControlTarget(
                            {
                                width: asset.attribute.width,
                                height: asset.attribute.height,
                            },
                            asset.transform,
                        );
                        this.mainCanvas.requestRenderAll();
                    }
                }
            }
            return true;
        }

        const {
            attribute: { width, height },
            transform,
        } = this.asset;
        if (!isSameAttribute) {
            await this.render();
            if(this.groupName){
                this.groupTarget?.fire('group:assetSizeChange',{width, height})
            }
        }
        if (!isSameTransform) {
            if (this.target) {
                this.setControlTarget(
                    {
                        width,
                        height,
                    },
                    transform,
                );
            }
            this.updateRenderTargetSize({
                angle: transform.rotate,
                x: transform.posX,
                y: transform.posY,
            });
            !this.skipRequestRender() && this.mainCanvas.requestRenderAll();
        }
        if (this.asset.meta.group && (!isSameAttribute || !isSameTransform)) {
            this.updateGroupHelperRectPostion(this.asset);
        }
        this.updateRenderZIndex();
        return true;
    }

    protected override async renderTargets() {
        this.remove();
        if (this.asset.attribute.cell && this.asset.attribute.cellSize && this.asset.attribute.text) {
            const zoom = this.mainCanvas.getZoom();
            this.tableRenderTarget = TableToImage.table(this.asset, zoom);
            this.tableRenderTarget.fontFamily += ',font130';
            const { attribute, transform } = this.asset;
            this.cacheCtx.clearRect(0, 0, this.cacheCanvas.width, this.cacheCanvas.height);
            this.cacheCanvas.width = attribute.width;
            this.cacheCanvas.height = attribute.height;
            this.tableRenderTarget.left = 0;
            this.tableRenderTarget.top = 0;
            this.tableRenderTarget.render(this.cacheCtx);

            const options = {
                data: {
                    className: this.className,
                    group: this.groupName,
                    pageIndex: this.pageIndex
                },
                left: transform.posX,
                top: transform.posY,
                width: this.tableRenderTarget.width,
                height: this.tableRenderTarget.height,
                centeredRotation: true,
                container: this.clipPage,
                visible: this.visible,
                scaleX: 1,
                scaleY: 1,
            };
            this.renderTarget = new fabric.Image(this.cacheCanvas, {
                ...options,
                name: 'table-render-' + this.className,
                selectable: false,
                evented: false,
            });
            this.renderTarget.setOptions(options);
            this.updateRenderTargetSize({
                angle: transform.rotate,
                x: transform.posX,
                y: transform.posY,
            });
            this.setControlTarget(options, transform, 'table-' + this.className);
        }
    }

    override setAssetScaleParam() {
        this.render();
    }

    // 表格不需要控制器
    override setActionSate(key: 'moving' | 'rotating' | 'scaling', value: boolean) {
        // nothing to do
    }

    scaleFont(width: number, height: number) {
        const { attribute } = this.asset;
        const { text } = attribute;
        if (width === attribute.width || height === attribute.height) {
            return {
                width,
                height,
            };
        }
        const fontSizeRatio = height / attribute.height;
        const textCopy = klona(text);
        textCopy.forEach((item: any) => {
            item.forEach((textItem: any)=> {
                // const lineNum = Math.round(attribute.height / Math.floor(textItem.fontSize * (textItem.lineHeight / 10)));
                let fontSize = textItem.fontSize;
            
                fontSize *= fontSizeRatio;
                fontSize = fontSize < 12 ? 12 : fontSize;
                textItem.fontSize = fontSize;
                textItem.letterSpacing = textItem.letterSpacing ? textItem.letterSpacing * fontSizeRatio : 0;
            })
        })
        return {
            text: textCopy,
            width: width,
            height: height,
        }
    }
}
