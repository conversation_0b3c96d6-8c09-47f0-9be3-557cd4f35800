import { TableRenderOnly } from './TableRenderOnly';

export class TableAsset extends TableRenderOnly {
    override renderOnly = false;

    protected override bindEvents() {
        this.bindBlurEvent();
        // this.bindMousedblclick();
        //改为单击触发编辑
        this.bindMouseClick();
        this.bindRightClick();
    }

    private bindBlurEvent() {
        this.target?.on('deselected', async () => {
            if (this.editing) {
                this.editing = false;
                await this.render();
                if (this.groupName) {
                    this.groupTarget?.fire('group:assetSizeChange', {
                        width: this.target?.width,
                        height: this.target?.height,
                    });
                }
            }
        });
    }

    private bindMousedblclick() {
        this.target?.on('mousedblclick', (opt) => {
            if (
                !this.isDblclickOnAsset(opt) &&
                !this.target?.group
            ) {
                return;
            }
            const viewportTransform = this.mainCanvas.viewportTransform;
            const scale = this.mainCanvas.getZoom();
            this.editing = true;
            this.actions.onAssetDoubleClick(
                {
                    x: viewportTransform?.[4] as number,
                    y: viewportTransform?.[5] as number,
                    scale,
                },
                this.asset,
            );
            this.renderTarget?.setOptions({
                opacity: 0,
            });
            this.mainCanvas.requestRenderAll();
        });
    }

    private bindMouseClick() {
        this.target?.on('mouse:up', (opt) => {
            if (
                !this.isDblclickOnAsset(opt) &&
                !this.target?.group &&
                !this.target?.data?.groupHelperRectTarget
            ) {
                return;
            }
            const viewportTransform = this.mainCanvas.viewportTransform;
            const scale = this.mainCanvas.getZoom();
            this.editing = true;
            this.actions.onAssetDoubleClick(
                {
                    x: viewportTransform?.[4] as number,
                    y: viewportTransform?.[5] as number,
                    scale,
                },
                this.asset,
            );
            this.renderTarget?.setOptions({
                opacity: 0,
            });
            this.mainCanvas.requestRenderAll();
        });
    }
}
