import { IImageFilterPropertyEnum } from '@tgs/types';
import { fabric } from 'fabric';
import { klona } from 'klona';

const defaultFilter = {
    brightness: 0,
    saturate: 0,
    contrast: 0,
    blur: 0,
    sharpen: 0,
    hue: 0,
    'gamma-r': 1,
    'gamma-g': 1,
    'gamma-b': 1,
    resId: undefined as string | undefined,
    strong: undefined as number | undefined,
} as const;

export interface FilterObj {
    brightness: number | undefined;
    saturate: number | undefined;
    hue: number | undefined;
    contrast: number | undefined;
    blur: number | undefined;
    sharpen: number[] | undefined;
    gamma: number[];
}

export class ImageLib {
    /**
     * @param svgString - svg字符串
     */
    static async svgToClipPath(svgString: string) {
        return new Promise<fabric.Object>((res, rej) => {
            fabric.loadSVGFromString(svgString, (obj) => {
                if (obj[0]?.clipPath) {
                    const clipPath = obj[0].clipPath;
                    obj[0].clipPath = undefined;
                    const w = clipPath.getScaledWidth();
                    const h = clipPath.getScaledHeight();
                    const scaleX = clipPath.scaleX as number;
                    const scaleY = clipPath.scaleY as number;
                    clipPath.setOptions({
                        centeredRotation: true,
                        left: 0,
                        top: 0,
                        width: w / scaleX,
                        height: h / scaleY,
                        scaleX: 1,
                        scaleY: 1,
                    });
                    clipPath.clone((t: fabric.Object) => {
                        res(t);
                    });
                } else {
                    rej();
                }
            // @ts-ignore
            }, null, {crossOrigin: 'anonymous'});
            // const parse = new DOMParser();
            // const element = parse.parseFromString(svgString, 'image/svg+xml');
            // const svgPath = element.querySelector('path') as SVGPathElement;
            // const svgPolygon = element.querySelector('polygon') as SVGElement;
            // let pathStr: string;
            // let clipPath;
            // // 多边形和不规则图形
            // if (svgPath) {
            //     // 曲线形
            //     pathStr = svgPath.getAttribute('d') as string;
            //     clipPath = new fabric.Path(pathStr);
            // } else if (svgPolygon) {
            //     // 多边形
            //     pathStr = svgPolygon.getAttribute('points') as string;
            //     const trimPathArr = pathStr.trim().split(' ');
            //     const pointArr = trimPathArr.map((v) => {
            //         const [x, y] = v.split(',').map((v) => +v);
            //         return { x, y };
            //     });
            //     clipPath = new fabric.Polygon(pointArr);
            // } else {
            //     for (const key of ['rect', 'ellipse', 'circle']) {
            //         const shape = element.querySelector(key);
            //         if (shape) {
            //             if (key === 'rect') {
            //                 const width = +(shape.getAttribute('width') as string);
            //                 const height = +(shape.getAttribute('height') as string);
            //                 clipPath = new fabric.Rect({ width, height });
            //             } else if (key === 'circle') {
            //                 const radius = +(shape.getAttribute('r') as string);
            //                 clipPath = new fabric.Circle({ radius });
            //             } else if (key === 'ellipse') {
            //                 const rx = +(shape.getAttribute('rx') as string);
            //                 const ry = +(shape.getAttribute('ry') as string);
            //                 clipPath = new fabric.Ellipse({ ry, rx });
            //             }
            //             break;
            //         }
            //     }
            // }
            // res(clipPath as fabric.Object);
        });
    }

    static createFilter(filterValueList: FilterObj) {
        const filters: fabric.IBaseFilter[] = [];

        for (const filterType in filterValueList) {
            const filterValue = filterValueList[filterType as keyof typeof filterValueList];
            if (filterType == 'brightness') {
                /*亮度 START*/
                filters.push(
                    new fabric.Image.filters.Brightness({
                        brightness: filterValue as number,
                    }),
                );
                /*亮度 END*/
            } else if (filterType === 'gamma') {
                // 色温 / 色调 START
                filters.push(
                    new (fabric.Image.filters as any).Gamma({
                        gamma: filterValue,
                    }),
                );
                // 色温 / 色调 END
            } else if (filterType == 'saturate') {
                /*饱和度 START*/
                filters.push(
                    new fabric.Image.filters.Saturation({
                        saturation: filterValue as number,
                    }),
                );
                /*饱和度 END*/
            } else if (filterType == 'contrast') {
                /*对比度 START*/
                filters.push(
                    new fabric.Image.filters.Contrast({
                        contrast: filterValue as number,
                    }),
                );
                /*对比度 END*/
            } else if (filterType == 'hue') {
                /*偏色 START*/
                filters.push(
                    new fabric.Image.filters.HueRotation({
                        rotation: filterValue as number,
                    }),
                );
                /*偏色 END*/
            } else if (filterType == 'blur') {
                /*模糊 START*/
                filters.push(
                    new fabric.Image.filters.Blur({
                        blur: filterValue as number,
                    }),
                );
                /*模糊 END*/
            } else if (filterType == 'sharpen') {
                /*锐化 START*/
                const v = filterValue as number;
                const centerValue = 1 + v;
                const boundaryValue = -(0 + v / 8);
                const matrix = new Array(9).fill(boundaryValue);
                matrix[4] = centerValue;
                filters.push(
                    new fabric.Image.filters.Convolute({
                        matrix,
                    }),
                );
                /*锐化 END*/
            }
        }

        return filters;
    }
    static setFilter(filterValueList: FilterObj, filtersMap: Record<string, fabric.IBaseFilter>, obj: fabric.Image) {
        for (const filterType in filterValueList) {
            const filterValue = filterValueList[filterType as keyof typeof filterValueList];
            switch (filterType) {
                case IImageFilterPropertyEnum.SHARPEN:
                    /*锐化 START*/
                    const sharpenFilter = filtersMap[IImageFilterPropertyEnum.SHARPEN] as fabric.IConvoluteFilter;
                    if (sharpenFilter) {
                        (sharpenFilter as fabric.IConvoluteFilter).setOptions({
                            matrix: filterValue,
                        });
                    } else {
                        const sharpenFilter = new fabric.Image.filters.Convolute({
                            matrix: filterValue as number[],
                        });
                        filtersMap[IImageFilterPropertyEnum.SHARPEN] = sharpenFilter;
                        obj.filters?.push(sharpenFilter);
                    }
                    break;
                case IImageFilterPropertyEnum.HUE:
                    const hueFilter = filtersMap[IImageFilterPropertyEnum.HUE] as fabric.IHueRotationFilter;
                    if (hueFilter) {
                        hueFilter.setOptions({
                            rotation: filterValue,
                        });
                    } else {
                        const hueFilter = new fabric.Image.filters.HueRotation({
                            rotation: filterValue as number,
                        });
                        filtersMap[IImageFilterPropertyEnum.HUE] = hueFilter;
                        obj.filters?.push(hueFilter);
                    }
                    break;
                case IImageFilterPropertyEnum.SATURATE:
                    const saturateFilter = filtersMap[IImageFilterPropertyEnum.SATURATE] as fabric.ISaturationFilter;
                    if (saturateFilter) {
                        saturateFilter.setOptions({
                            saturation: filterValue,
                        });
                    } else {
                        const saturateFilter = new fabric.Image.filters.Saturation({
                            saturation: filterValue as number,
                        });
                        filtersMap[IImageFilterPropertyEnum.SATURATE] = saturateFilter;
                        obj.filters?.push(saturateFilter);
                    }
                    break;
                default:
                    const filter = filtersMap[filterType] as fabric.IBaseFilter;
                    if (filter) {
                        filter.setOptions({
                            [filterType]: filterValue,
                        });
                    } else {
                        const filterName = (filterType.charAt(0).toUpperCase() +
                            filterType.slice(1)) as keyof fabric.IAllFilters;
                        const commonFilter = new fabric.Image.filters[filterName]({
                            [filterType]: filterValue,
                        });
                        filtersMap[filterType] = commonFilter;
                        obj.filters?.push(commonFilter);
                    }
            }
            /*锐化 END*/
        }
    }
    static updateFilter(filters: Tgs.IFilters) {
        const { brightness, saturate, hue, contrast, blur, sharpen = 0, strong } = filters;
        const gamma = [
            !filters['gamma-r'] ? 1 : filters['gamma-r'],
            !filters['gamma-g'] ? 1 : filters['gamma-g'],
            !filters['gamma-b'] ? 1 : filters['gamma-b'],
        ];
        const filterValueList = {
            brightness,
            saturate,
            hue,
            contrast,
            blur,
            sharpen: [sharpen],
            gamma,
        };
        type FilterKeys = keyof typeof filterValueList;
        // 设置强度
        Object.keys(filterValueList).forEach((key) => {
            const s = strong || 1;
            const k = key as FilterKeys;
            if (k === 'gamma') {
                filterValueList[k] = filterValueList[k].map((v) => (v - 1) * s + 1);
            } else if (k === 'sharpen') {
                const v = sharpen as number;
                const centerValue = 1 + v;
                const boundaryValue = -(0 + v / 8);
                const matrix = new Array(9).fill(boundaryValue);
                matrix[4] = centerValue;
                filterValueList[k] = matrix;
            } else {
                filterValueList[k] = (filterValueList[k] as number) * s;
            }
        });
        return filterValueList;
    }

    /**
     * 计算图片特效需要的显示尺寸
     */
    static calcImageEffectsSize(layers: Tgs.IImageEffects['layers'], asset: Tgs.IImageAsset) {
        const maxLength = 2000;
        const originWidth = asset.attribute.container?.width || asset.attribute.width;
        const originHeight = asset.attribute.container?.height ||  asset.attribute.height;
        let scaleRate = 1;
        if (originWidth >= originHeight) {
            if (originWidth > maxLength) {
                scaleRate = maxLength / originWidth;
            }
        } else {
            if (originHeight > maxLength) {
                scaleRate = maxLength / originHeight;
            }
        }
        const imgWidth = originWidth * scaleRate;
        const imgHeight = originHeight * scaleRate;
        const strong = asset.attribute.imageEffects?.strong as number;
        if (!layers) {
            layers = klona(asset.attribute.imageEffects?.layers) as Tgs.IImageEffects['layers'];
        }
        let maxWidth = 0;
        let minLeft = 0;
        let maxRight = 0;
        let minTop = 0;
        let maxBottom = 0;
        layers.forEach((layer: any) => {
            layer.stroke.width = layer.stroke.width * strong * scaleRate;
            layer.stroke.offsetX = layer.stroke.offsetX * strong * scaleRate;
            layer.stroke.offsetY = layer.stroke.offsetY * strong * scaleRate;
            layer.stroke.blur = (layer.stroke.blur || 0) * scaleRate;
            maxWidth = Math.max(maxWidth, layer.stroke.width);
            minLeft = Math.min(minLeft, layer.stroke.offsetX - layer.stroke.blur);
            maxRight = Math.max(maxRight, layer.stroke.offsetX + layer.stroke.blur);
            minTop = Math.min(minTop, layer.stroke.offsetY - layer.stroke.blur);
            maxBottom = Math.max(maxBottom, layer.stroke.offsetY + layer.stroke.blur);
        });
        return {
            width: Math.round(
                imgWidth +
                    maxWidth * 2 +
                    Math.max(Math.abs(minLeft) - maxWidth, 0) +
                    Math.max(Math.abs(maxRight) - maxWidth, 0),
            ),
            height: Math.round(
                imgHeight +
                    maxWidth * 2 +
                    Math.max(Math.abs(minTop) - maxWidth, 0) +
                    Math.max(Math.abs(maxBottom) - maxWidth, 0),
            ),
            // left: Math.round(minLeft >= 0 || minLeft + maxWidth >= 0 ? -maxWidth : minLeft),
            // top: Math.round(minTop >= 0 || minTop + maxWidth >= 0 ? -maxWidth : minTop),
            // right: Math.round(maxRight >= 0 || maxRight - maxWidth >= 0 ? maxWidth : maxRight),
            // bottom: Math.round(maxBottom >= 0 || maxBottom - maxWidth >= 0 ? maxWidth :  maxBottom),
            left: Math.round(Math.min(-maxWidth, minLeft)),
            top: Math.round(Math.min(-maxWidth, minTop)),
            right: Math.round(Math.max(maxWidth, maxRight)),
            bottom: Math.round(Math.max(maxWidth, maxBottom)),
            imgWidth,
            imgHeight,
            scaleRate,
        };
    }

    static drawImageEffects(imageSource: CanvasImageSource, asset: Tgs.IImageAsset) {
        if (asset.attribute.imageEffects?.layers) {
            const layers = klona(asset.attribute.imageEffects.layers);
            const size = this.calcImageEffectsSize(layers, asset);
            const tempScaleRate = 2;
            let tempCanvas: OffscreenCanvas | HTMLCanvasElement;
            try {
                tempCanvas = new OffscreenCanvas(size.width * tempScaleRate, size.height * tempScaleRate);
            } catch (error) {
                tempCanvas = document.createElement('canvas');
                tempCanvas.width = size.width * tempScaleRate;
                tempCanvas.height = size.height * tempScaleRate;
            }
            const tempCtx = tempCanvas.getContext('2d') as CanvasRenderingContext2D | OffscreenCanvasRenderingContext2D;
            const resultCanvas = document.createElement('canvas');
            resultCanvas.width = size.width;
            resultCanvas.height = size.height;
            const resultCtx = resultCanvas.getContext('2d') as
                | CanvasRenderingContext2D
                | OffscreenCanvasRenderingContext2D;
            const top = Math.abs(size.top);
            const left = Math.abs(size.left);
            layers.forEach((layer) => {
                const color = layer.stroke.color;
                tempCtx.shadowOffsetX = 0;
                tempCtx.shadowOffsetY = 0;
                tempCtx.shadowBlur = 0;
                tempCtx.shadowColor = 'rgba(0, 0, 0, 0)'; // chrome 默认值
                tempCtx.globalCompositeOperation = 'source-over';
                tempCtx.clearRect(0, 0, tempCanvas.width, tempCanvas.height);
                if (layer.stroke.width > 0) {
                    // 画描边
                    const sw = layer.stroke.width;
                    for (let i = 0; i < 360; i++) {
                        tempCtx.drawImage(
                            imageSource,
                            (sw * (1 + Math.cos(i)) - sw + left + layer.stroke.offsetX) * tempScaleRate,
                            (sw * (1 + Math.sin(i)) - sw + top + layer.stroke.offsetY) * tempScaleRate,
                            size.imgWidth * tempScaleRate,
                            size.imgHeight * tempScaleRate,
                        );
                    }
                    tempCtx.globalCompositeOperation = 'source-in';
                    tempCtx.fillStyle = `rgba(${color.r},${color.g},${color.b},${color.a})`;
                    tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
                    resultCtx.drawImage(tempCanvas as CanvasImageSource, 0, 0, size.width, size.height);
                } else if (layer.stroke?.blur && layer.stroke.blur > 0) {
                    tempCtx.shadowOffsetX = layer.stroke.offsetX * tempScaleRate;
                    tempCtx.shadowOffsetY = layer.stroke.offsetY * tempScaleRate;
                    tempCtx.shadowBlur = layer.stroke.blur * tempScaleRate;
                    tempCtx.shadowColor = `rgba(${color.r},${color.g},${color.b},${color.a})`;
                    tempCtx.drawImage(
                        imageSource,
                        left * tempScaleRate,
                        top * tempScaleRate,
                        size.imgWidth * tempScaleRate,
                        size.imgHeight * tempScaleRate,
                    );
                    resultCtx.drawImage(tempCanvas as CanvasImageSource, 0, 0, size.width, size.height);
                } else if (layer.stroke.offsetX !== 0 || layer.stroke.offsetY !== 0) {
                    // 画投影
                    tempCtx.drawImage(
                        imageSource,
                        (left + layer.stroke.offsetX) * tempScaleRate,
                        (top + layer.stroke.offsetY) * tempScaleRate,
                        size.imgWidth * tempScaleRate,
                        size.imgHeight * tempScaleRate,
                    );
                    tempCtx.globalCompositeOperation = 'source-in';
                    tempCtx.fillStyle = `rgba(${color.r},${color.g},${color.b},${color.a})`;
                    tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
                    resultCtx.drawImage(tempCanvas as CanvasImageSource, 0, 0, size.width, size.height);
                }
            });
            resultCtx.drawImage(imageSource as CanvasImageSource, left, top, size.imgWidth, size.imgHeight);
            return { resultCanvas, l: size.left, t: size.top, r: size.right, b: size.bottom };
        }
    }

    static isNoFilters(filters: Tgs.IFilters) {
        let result = true;
        if (filters.resId) {
            return false;
        }
        if (Number(filters.strong) === 0) {
            return true;
        }
        for (const key in filters) {
            if (key !== 'strong' && key !== 'resId') {
                if (Number(filters[key as keyof Tgs.IFilters]) !== defaultFilter[key as keyof Tgs.IFilters]) {
                    result = false;
                    break;
                }
            }
        }
        return result;
    };
}
