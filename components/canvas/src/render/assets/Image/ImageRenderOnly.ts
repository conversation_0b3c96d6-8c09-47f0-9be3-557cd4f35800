import { fabric } from 'fabric';
import { Asset } from '../Asset';
import { ImageLib } from './lib';
import equal from 'fast-deep-equal';
import type { TgsCanvasPage } from '../../TgsCanvas';
import { SvgLoader } from '../../../util/SvgLoader';
import { cloneDeep } from 'lodash-es';
// 存在这个方法，但是声明文件中没有写
type IUtil = fabric.IUtil & {
    calcRotateMatrix: ({ angle }: { angle: number }) => number[];
};

const transformPoint = fabric.util.transformPoint;
const invert = fabric.util.invertTransform;
const qrDecompose = fabric.util.qrDecompose;
const calcRotateMatrix = (fabric.util as IUtil).calcRotateMatrix;

export class ImageRenderOnly extends Asset {
    readonly type = 'image';
    override renderOnly = true;
    declare asset: Tgs.IImageAsset;
    protected declare renderTarget: fabric.Image | undefined;
    protected clipPathTarget: fabric.Object | fabric.Rect| undefined;
    protected imageEffectTarget: fabric.Image | undefined;
    protected hasAddClipPathTarget = false;
    protected hasAddImageEffectTarget = false;
    protected clipPathTargetZIndexOffset = 8;
    protected imageEffectTargetZIndexOffset = 7;
    protected filtersMap: Record<string, fabric.IBaseFilter> = {};
    protected imageFilterToolLogic: Tgs.IImageFilterToolLogic | undefined;
    protected cache: {
        originImage: HTMLImageElement | HTMLCanvasElement | HTMLVideoElement | undefined;
        /** 缓存 svg 字符串 */
        clipSvg: string | undefined;
        clipResult: HTMLCanvasElement | undefined;
        /** 缓存滤镜处理后的图片结果 */
        filterImage: HTMLImageElement | HTMLCanvasElement | undefined;
        /** 缓存图片边框特效的图片结果 */
        borderImage: HTMLImageElement | HTMLCanvasElement | OffscreenCanvas | undefined;
    } = {
        originImage: undefined,
        clipSvg: undefined,
        clipResult: undefined,
        filterImage: undefined,
        borderImage: undefined,
    };

    override async init() {
        try {
            if (this.asset.attribute.picUrl === 'loading') {
                return true;
            }
            const result = await this.loadImage(this.asset.attribute.picUrl);
            if (result && this.cache.originImage) {
                await this.render();
            }
        } catch (error) {
            console.error('image init error: ', this.className, error);
        }
        return true;
    }

    override async update(asset: Tgs.IAsset<Tgs.TAssetType>, index: number, pageIndex: number) {
        this.setIndexAndGroup(index, pageIndex, asset);
        asset = asset as Tgs.IImageAsset;
        if (asset.attribute.picUrl === 'loading') {
            return true;
        }
        const isSameAttribute = equal(this.asset.attribute, asset.attribute);
        const isSameTransform = equal(this.asset.transform, asset.transform);
        let isSameImage = true;
        let isSameContainer = true;
        let isAddClip = false;
        let isRemoveClip = false;
        let isSameClip = true;
        let isSameFilter = true;
        let isSameImageEffect = true;
        if (!isSameAttribute) {
            isSameImage = equal(this.asset.attribute?.picUrl, asset.attribute?.picUrl);
            isSameContainer = equal(this.asset.attribute?.container, asset.attribute?.container);
            isAddClip = !!(!this.asset.attribute.container?.id && asset.attribute.container?.id);
            isRemoveClip = !!(this.asset.attribute.container?.id && !asset.attribute.container?.id);
            if (!isSameContainer && this.asset.attribute.container?.id && asset.attribute.container?.id) {
                isSameClip = this.asset.attribute.container.id === asset.attribute.container.id;
            }
            isSameFilter = equal(this.asset.attribute?.filters, asset.attribute?.filters);
            isSameImageEffect =
                equal(this.asset.attribute?.imageEffects, asset.attribute?.imageEffects) &&
                equal(this.asset.attribute?.width, asset.attribute?.width) &&
                equal(this.asset.attribute?.height, asset.attribute?.height);
        }
        this.asset = asset as Tgs.IImageAsset;
        if (this.rotating || this.scaling || this.moving) {
            return true;
        }
        if (!isSameAttribute) {
            if (isRemoveClip) {
                this.removeContainer();
                await this.render();
            }
            if (!isSameImage) {
                const result = await this.loadImage(this.asset.attribute.picUrl);
                if (result && this.cache.originImage) {
                    await this.render();
                }
            } else if (!isSameContainer) {
                if (!isSameClip || isAddClip) {
                    await this.addClipPath();
                }
            }
            if (!isSameFilter && this.renderTarget) {
                this.addFilter(this.renderTarget);
                this.renderImageEffect();
            }
            if (!isSameImageEffect && this.asset.attribute.imageEffects?.layers) {
                this.renderImageEffect();
            }
        }
        if (this.asset.meta.group && (!isSameAttribute || !isSameTransform)) {
            this.updateGroupHelperRectPostion(this.asset);
        }
        if (!isSameTransform) {
            this.updateRenderZIndex();
        }
        this.rotateTarget();
        !this.skipRequestRender() && this.mainCanvas.requestRenderAll();
        return true;
    }

    override async render() {
        await super.render();
        this.rotateTarget();
        !this.skipRequestRender() && this.mainCanvas.requestRenderAll();
        if (!this.imageFilterToolLogic) {
            this.actions.initImageCanvasRenderInfo({
                target: this.target as fabric.Group,
                renderTarget: this.renderTarget as fabric.Image,
                mainCanvas: this.mainCanvas as fabric.Canvas,
                filtersMap: this.filtersMap,
                setImageFilterToolLogic: (imageFilterToolLogic: Tgs.IImageFilterToolLogic) => {
                    this.imageFilterToolLogic = imageFilterToolLogic;
                },
            });
        }else{

        }
        return true;
    }

    protected override add() {
        super.add();
        this.addClipPathTarget();
        this.addImageEffectTarget();
    }

    protected override remove() {
        super.remove();
        this.removeClipPathTarget();
        this.removeImageEffectTarget();
    }


    protected addClipPathTarget() {
        // clipPath 直接渲染会模糊，改为导出 canvas 绘制结果再作为普通图片添加
        // if (this.clipPathTarget && !this.hasAddClipPathTarget && !this.destroyed) {
        //     this.mainCanvas.add(this.clipPathTarget);
        //     this.setRenderZIndex(this.className + '-clipPath', this.clipPathTarget, this.clipPathTargetZIndexOffset);
        //     this.hasAddClipPathTarget = true;
        // }
    }

    protected removeClipPathTarget() {
        if (this.clipPathTarget && this.hasAddClipPathTarget) {
            this.mainCanvas.remove(this.clipPathTarget);
            this.renderZIndex.removeAssetZIndex(this.className + '-clipPath');
            this.hasAddClipPathTarget = false;
        }
    }

    protected addImageEffectTarget() {
        if (this.imageEffectTarget && !this.hasAddImageEffectTarget && !this.destroyed) {
            this.mainCanvas.add(this.imageEffectTarget);
            this.setRenderZIndex(
                this.className + '-image-effect',
                this.imageEffectTarget,
                this.imageEffectTargetZIndexOffset,
            );
            this.hasAddImageEffectTarget = true;
        }
    }

    protected removeImageEffectTarget() {
        if (this.imageEffectTarget && this.hasAddImageEffectTarget) {
            this.mainCanvas.remove(this.imageEffectTarget);
            this.renderZIndex.removeAssetZIndex(this.className + '-image-effect');
            this.imageEffectTarget = undefined;
            this.hasAddImageEffectTarget = false;
        }
    }

    override destroy() {
        super.destroy();
        this.clipPathTarget = undefined;
        this.imageEffectTarget = undefined;
        this.cache = {
            originImage: undefined,
            clipSvg: undefined,
            clipResult: undefined,
            filterImage: undefined,
            borderImage: undefined,
        };
    }

    protected override async renderTargets() {
        await this.renderImage();
    }

    protected async renderImage() {
        this.remove();
        const { attribute, transform, meta } = this.asset;
        const options: fabric.IImageOptions = {
            data: {
                className: this.className,
                group: this.groupName,
                isMarkedReplace:attribute?.container?.markedReplace,
                isClip: !!attribute?.container?.id,
                pageIndex: this.pageIndex,
                type: meta.type,
            },
            left: Math.floor(transform.posX),
            top: Math.floor(transform.posY),
            width: Math.floor(attribute.width),
            height: Math.floor(attribute.height),
            opacity: this.opacityFormat(attribute.opacity),
            centeredRotation: true,
            container: this.clipPage,
            visible: this.visible,
        };

        if (this.cache.originImage) {
            const sizeOptions: fabric.IImageOptions = {
                width: this.cache.originImage.width,
                height: this.cache.originImage.height,
                scaleX: attribute.width / this.cache.originImage.width,
                scaleY: attribute.height / this.cache.originImage.height,
                flipX: transform.horizontalFlip,
                flipY: transform.verticalFlip,
            };
            this.renderTarget = new fabric.Image(this.cache.originImage, {
                name: 'image-render-' + this.className,
                ...options,
                selectable: false,
                evented: false,
                ...sizeOptions,
                objectCaching: false,
            });
            this.setControlTarget(options, transform, 'image-' + this.className);
            await this.addClipPath();
            this.addFilter(this.renderTarget);
            this.renderImageEffect();
        } else {
            this.setControlTarget(options, transform, 'image-' + this.className);
        }
        return true;
    }

    protected async addClipPath() {
        const clip = await this.getClipObject();

        if (clip) {
            const { attribute, transform } = this.asset;
            const container = attribute.container;
            if (container?.id && this.cache.originImage) {
                if (clip !== this.clipPathTarget) {
                    this.removeClipPathTarget();
                    this.clipPathTarget = clip;
                }
                clip?.rotate(0);
                const clipWidth = clip.width as number;
                const clipHeight = clip.height as number;
                const imgScaleX = attribute.width / this.cache.originImage.width;
                const imgScaleY = attribute.height / this.cache.originImage.height;
                this.clipPathTarget.setOptions({
                    name: 'image-clipPath-' + this.className,
                    data: {
                        className: this.className,
                        group: this.groupName,
                    },
                    fill: '#fff',
                    centeredRotation: true,
                    absolutePositioned: true,
                    left: 0,
                    top: 0,
                    scaleX: container.width / clipWidth / imgScaleX,
                    scaleY: container.height / clipHeight / imgScaleY,
                    selectable: false,
                    evented: false,
                    // container: this.clipPage,
                    // visible: this.visible,
                    // flipX: transform.horizontalFlip,
                    // flipY: transform.verticalFlip,
                    objectCaching: false,
                });
                // 将裁剪形状绘制成有颜色的图片，用于颜色叠加
                this.cache.clipResult = this.clipPathTarget.toCanvasElement();
                const ctx = this.cache.clipResult.getContext('2d') as CanvasRenderingContext2D;
                ctx.globalCompositeOperation = 'source-in';
                ctx.drawImage(
                    this.cache.originImage,
                    Math.floor(container.posX / imgScaleX),
                    Math.floor(container.posY / imgScaleY),
                );
                this.remove();
                const w = this.cache.clipResult.width;
                const h = this.cache.clipResult.height;
                const options: fabric.IImageOptions = {
                    data: {
                        className: this.className,
                        group: this.groupName,
                    },
                    name: 'image-render-' + this.className + '-clip',
                    left: Math.floor(transform.posX),
                    top: Math.floor(transform.posY),
                    width: w,
                    height: h,
                    opacity: this.opacityFormat(attribute.opacity),
                    scaleX: container.width / (w ?? 1),
                    scaleY: container.height / (h ?? 1),
                    flipX: transform.horizontalFlip,
                    flipY: transform.verticalFlip,
                    centeredRotation: true,
                    container: this.clipPage,
                    visible: this.visible,
                    objectCaching: false,
                    selectable: false,
                    evented: false,
                };
                this.renderTarget = new fabric.Image(this.cache.clipResult, options);
                this.filtersMap = {}
                this.imageFilterToolLogic?.updateImageCanvasRenderInfo({
                    target: this.target as fabric.Group,
                    renderTarget: this.renderTarget as fabric.Image,
                    mainCanvas: this.mainCanvas as fabric.Canvas,
                    filtersMap: this.filtersMap,
                });
                this.rotateTarget();
            }
            // this.addClipPathTarget();
        }
    }

    protected addFilter(obj: fabric.Image) {
        const { attribute } = this.asset;
        if (attribute.filters) {
            if (ImageLib.isNoFilters(attribute.filters)) {
                // 使用 [] 来移除滤镜
                obj?.applyFilters([]);
            } else {
                const filterValueList = ImageLib.updateFilter(attribute.filters);
                ImageLib.setFilter(filterValueList, this.filtersMap, obj);
                obj?.applyFilters();
            }
        }
    }

    protected renderImageEffect() {
        const { attribute, transform } = this.asset;
        this.removeImageEffectTarget();
        if (attribute.imageEffects?.layers && this.renderTarget && this.cache.originImage) {
            // TODO 复制后处理翻转再绘制
            let img: fabric.Image;
            if (attribute.container?.id && this.cache.clipResult) {
                const w = this.cache.clipResult.width;
                const h = this.cache.clipResult.height;
                img = new fabric.Image(this.cache.clipResult, {
                    left: Math.floor(transform.posX),
                    top: Math.floor(transform.posY),
                    width: w,
                    height: h,
                    scaleX: attribute.container.width / (w ?? 1),
                    scaleY: attribute.container.height / (h ?? 1),
                });
            } else {
                img = new fabric.Image(this.cache.originImage, {
                    left: Math.floor(transform.posX),
                    top: Math.floor(transform.posY),
                    width: this.cache.originImage.width,
                    height: this.cache.originImage.height,
                    scaleX: attribute.width / this.cache.originImage.width,
                    scaleY: attribute.height / this.cache.originImage.height,
                });
            }
            this.addFilter(img);
            const canvas = img.toCanvasElement();
            // canvas.style.position = 'absolute';
            // canvas.style.top = '0';
            // canvas.style.zIndex = '999999999';
            // document.body.appendChild(canvas);
            const result = ImageLib.drawImageEffects(canvas, this.asset);
            if (result) {
                const { resultCanvas, l, t, r, b } = result;
                const flipX = transform.horizontalFlip;
                const flipY = transform.verticalFlip;
                const w = this.renderTarget.getScaledWidth() + Math.abs(l) + Math.abs(r);
                const h = this.renderTarget.getScaledHeight() + Math.abs(t) + Math.abs(b);
                this.imageEffectTarget = new fabric.Image(resultCanvas as HTMLCanvasElement, {
                    name: 'image-imageEffect-' + this.className,
                    left: Math.floor(transform.posX + (flipX ? -r : l)),
                    top: Math.floor(transform.posY + (flipY ? -b : t)),
                    evented: false,
                    selectable: false,
                    hasBorders: false,
                    hasControls: false,
                    container: this.clipPage,
                    visible: this.visible,
                    data: {
                        className: this.className,
                        group: this.groupName,
                        l,
                        t,
                        r,
                        b,
                    },
                    scaleX: w / resultCanvas.width,
                    scaleY: h / resultCanvas.height,
                    flipX,
                    flipY,
                    opacity: this.opacityFormat(attribute.opacity),
                    objectCaching: false,
                });
                this.addImageEffectTarget();
                this.rotateTarget();
            }
        }
    }

    protected opacityFormat(opacity: number | undefined) {
        if (opacity === 0) {
            return 0;
        } else {
            return opacity ? opacity / 100 : 1;
        }
    }

    protected removeContainer() {
        if (this.clipPathTarget) {
            this.renderTarget?.setOptions({
                clipPath: undefined,
            });
            this.removeClipPathTarget();
            this.clipPathTarget = undefined;
        }
        this.rotateTarget();
        // if (this.target) {
        //     this.mainCanvas.setActiveObject(this.target);
        // }
    }

    protected rotateTarget() {
        const { attribute, transform } = this.asset;
        const flipX = transform.horizontalFlip;
        const flipY = transform.verticalFlip;
        const opacity = this.opacityFormat(attribute.opacity);
        if (this.target) {
            this.setControlTarget(
                {
                    width: Math.floor(attribute.container?.width ?? attribute.width),
                    height: Math.floor(attribute.container?.height ?? attribute.height),
                },
                transform,
            );
        }
        if (this.renderTarget && this.cache.originImage) {
            this.renderTarget.rotate(0);
            const cPosX = attribute.container?.posX ?? 0;
            const cPosY = attribute.container?.posY ?? 0;
            const cWidth = attribute.container?.width ?? 0;
            const cHeight = attribute.container?.height ?? 0;
            const flipCPosX = attribute.container?.id ? -(attribute.width + cPosX - cWidth) : 0;
            const flipCPosY = attribute.container?.id ? -(attribute.height + cPosY - cHeight) : 0;
            if (attribute.container?.id && this.cache.clipResult) {
                const { x, y } = this.calcMatrixPoint(this.clipPathTarget, transform.posX, transform.posY);
                const w = this.cache.clipResult.width;
                const h = this.cache.clipResult.height;
                this.renderTarget.setOptions({
                    left: Math.floor(x),
                    top: Math.floor(y),
                    width: w,
                    height: h,
                    scaleX: w ? attribute.container.width / w : 1,
                    scaleY: h ? attribute.container.height / h : 1,
                    flipX: transform.horizontalFlip,
                    flipY: transform.verticalFlip,
                    opacity,
                });
            } else {
                const { x, y } = this.calcMatrixPoint(
                    this.renderTarget,
                    transform.posX + (flipX ? flipCPosX : cPosX),
                    transform.posY + (flipY ? flipCPosY : cPosY),
                );
                this.renderTarget.setOptions({
                    left: Math.floor(x),
                    top: Math.floor(y),
                    width: this.cache.originImage.width,
                    height: this.cache.originImage.height,
                    scaleX: attribute.width / this.cache.originImage.width,
                    scaleY: attribute.height / this.cache.originImage.height,
                    flipX: transform.horizontalFlip,
                    flipY: transform.verticalFlip,
                    opacity,
                });
            }
            this.renderTarget.setCoords();
        }
        // if (this.clipPathTarget) {
        //     this.clipPathTarget.rotate(0);
        //     const { x, y } = this.calcMatrixPoint(this.clipPathTarget, transform.posX, transform.posY);
        //     const clipWidth = this.clipPathTarget.width as number;
        //     const clipHeight = this.clipPathTarget.height as number;
        //     this.clipPathTarget.setOptions({
        //         left: Math.floor(x),
        //         top: Math.floor(y),
        //         scaleX: (attribute.container?.width ?? 0) / clipWidth,
        //         scaleY: (attribute.container?.height ?? 0) / clipHeight,
        //         flipX: transform.horizontalFlip,
        //         flipY: transform.verticalFlip,
        //     });
        // }
        if (this.imageEffectTarget) {
            this.imageEffectTarget.rotate(0);
            const { l, t, r, b } = this.imageEffectTarget.data;
            const { x, y } = this.calcMatrixPoint(
                this.imageEffectTarget,
                transform.posX + (flipX ? -r : l),
                transform.posY + (flipY ? -b : t),
            );
            const w = (this.renderTarget?.getScaledWidth() as number) + Math.abs(l) + Math.abs(r);
            const h = (this.renderTarget?.getScaledHeight() as number) + Math.abs(t) + Math.abs(b);
            this.imageEffectTarget.setOptions({
                left: Math.floor(x),
                top: Math.floor(y),
                scaleX: w / (this.imageEffectTarget.width as number),
                scaleY: h / (this.imageEffectTarget.height as number),
                flipX: transform.horizontalFlip,
                flipY: transform.verticalFlip,
                opacity,
            });
        }
        // if (this.clipPathTarget) {
        //     this.setRelatedMatrix(this.clipPathTarget, [this.renderTarget, this.imageEffectTarget], 'image-rotate');
        //     this.clipPathTarget.rotate(transform.rotate);
        //     this.calcRelatedMatrix(this.clipPathTarget, [this.renderTarget, this.imageEffectTarget], 'image-rotate');
        //     this.calcRelatedMatrix(this.clipPathTarget, [this.renderTarget, this.imageEffectTarget], 'image-rotate');
        // } else
        if (this.renderTarget) {
            this.setRelatedMatrix(this.renderTarget, [, this.imageEffectTarget], 'image-rotate');
            this.renderTarget?.rotate(transform.rotate);
            this.calcRelatedMatrix(this.renderTarget, [this.imageEffectTarget], 'image-rotate');
            this.calcRelatedMatrix(this.renderTarget, [this.imageEffectTarget], 'image-rotate');
        }
        this.setRelatedMatrix(this.target, [this.renderTarget, this.clipPathTarget, this.imageEffectTarget]);
    }

    protected override updateRenderZIndex() {
        this.setRenderZIndex(this.className + '-render', this.renderTarget, this.renderTargetZIndexOffset);
        this.setRenderZIndex(this.className, this.target, this.targetZIndexOffset);
        this.setRenderZIndex(this.className + '-clipPath', this.clipPathTarget, this.clipPathTargetZIndexOffset);
        this.setRenderZIndex(
            this.className + '-image-effect',
            this.imageEffectTarget,
            this.imageEffectTargetZIndexOffset,
        );
    }

    protected async getClipObject(isClipping = false) {
        try {
            const { attribute } = this.asset;
            if (attribute.container?.id) {
                let svgContent = undefined;
                if (attribute.container.picUrl && attribute.container.picUrl !== 'loading') {
                    svgContent = attribute.container.picUrl;
                } else {
                    svgContent = await this.getContainerSvg(attribute.container);
                }
                if (svgContent) {
                    this.updateAssetCutContainer(svgContent);
                    if (isClipping) {
                        return await ImageLib.svgToClipPath(svgContent);
                    }
                    let clip = this.clipPathTarget;
                    if (!clip || svgContent !== this.cache.clipSvg) {
                        clip = await ImageLib.svgToClipPath(svgContent);
                        if (clip) {
                            this.cache.clipSvg = svgContent;
                        }
                        return clip;
                    } else {
                        return clip;
                    }
                }
            }
        } catch (error) {
            console.error('get clip object fail: ', error);
        }
    }
    protected updateAssetCutContainer(svgContent:string) {
        const {viewBox} = SvgLoader.getParamsFromSvgString(svgContent, {viewBox:''})
        const tempViewBox = viewBox.split(' ');
        this.actions.onUpdateAssets('UPDATE_ASSET_CONTAINER_END', [
            {
                index: this.index,
                className: this.className,
                pageIndex: this.pageIndex,
                changes: {
                    attribute: {
                        container:{
                            ...this.asset.attribute.container,
                            picUrl: svgContent,
                            viewBoxWidth: parseInt(tempViewBox[2]),
                            viewBoxHeight: parseInt(tempViewBox[3]),
                        },
                    },
                },
            },
        ]);
    }
    protected loadImage(url: string) {
        const failImageUrl = 'https://js.tuguaishou.com/common/deleted_user_asset2x.png';
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.crossOrigin = 'anonymous';
            img.onload = () => {
                this.cache.originImage = img;
                resolve(true);
            };
            img.onerror = () => {
                console.error('load image fail: ', this.className, url);
                this.cache.originImage = undefined;
                if (img.src === failImageUrl) {
                    reject(false);
                } else {
                    img.src = failImageUrl;
                }
            };
            img.src = url;
        });
    }

    protected getContainerSvg(container: Tgs.IContainer) {
        return new Promise<string>((resolve, reject) => {
            if (container.picUrl === 'loading') {
                SvgLoader.loadSvg(container.source_key as string, 'token')
                    .then((svgContent) => {
                        // TODO 更新到 redux
                        resolve(svgContent as string);
                    })
                    .catch((e) => {
                        reject(false);
                    });
            } else if (container.SVGUrl) {
                SvgLoader.loadSvg(container.SVGUrl as string, 'content')
                    .then((svgContent) => {
                        resolve(svgContent as string);
                    })
                    .catch((e) => {
                        reject(false);
                    });
            }  else {
                resolve(container.picUrl as string);
            }
        });
    }

    override updateRelatedObjects(key: 'moving' | 'rotating' | 'scaling', opt: fabric.IEvent<MouseEvent>) {
        switch (key) {
            case 'moving':
            case 'rotating':
            case 'scaling': {
                this.calcRelatedMatrix(this.target, [
                    this.renderTarget,
                    /* this.clipPathTarget, */ this.imageEffectTarget,
                ]);
                break;
            }
        }
    }

    protected override updateRenderTargetSize(param: { angle: number; x: number; y: number; w: number; h: number }) {
        this.calcRelatedMatrix(this.target, [this.renderTarget, /* this.clipPathTarget, */ this.imageEffectTarget]);
    }

    override setVisible(value: boolean) {
        super.setVisible(value);
        this.clipPathTarget?.setOptions({ visible: value });
        this.imageEffectTarget?.setOptions({ visible: value });
    }

    override setClipPath(pageContainer: TgsCanvasPage) {
        super.setClipPath(pageContainer);
        const container: fabric.Object = pageContainer.page;
        // if (this.clipPathTarget) {
        //     this.renderTarget?.setOptions({ clipPath: this.clipPathTarget });
        // }
        // this.clipPathTarget?.setOptions({ container });
        this.imageEffectTarget?.setOptions({ container });
    }

    scaleImage(width: number, height: number, angle: number) {
        // if (this.asset.attribute?.container?.id && this.target && this.renderTarget) {
        //     // 旧
        //     const clipTransform = qrDecompose(this.target.calcTransformMatrix());
        //     const imageTransform = qrDecompose(this.renderTarget.calcTransformMatrix());
        //     const rotateMatrix = calcRotateMatrix({ angle });
        //     const invertRotate = invert(rotateMatrix);
        //     // 将中心点旋转到角度为 0 的位置
        //     const clipCenter = transformPoint(
        //         { x: clipTransform.translateX, y: clipTransform.translateY } as fabric.Point,
        //         invertRotate,
        //     );
        //     const imageCenter = transformPoint(
        //         { x: imageTransform.translateX, y: imageTransform.translateY } as fabric.Point,
        //         invertRotate,
        //     );
        //     const imageWidth = this.renderTarget?.getScaledWidth() as number;
        //     const imageHeight = this.renderTarget?.getScaledHeight() as number;
        //     const clipWidth = width;
        //     const clipHeight = height;
        //     const imageLeft = imageCenter.x - imageWidth / 2;
        //     const imageTop = imageCenter.y - imageHeight / 2;
        //     const imageRight = imageCenter.x + imageWidth / 2;
        //     const imageBottom = imageCenter.y + imageHeight / 2;
        //     const clipLeft = clipCenter.x - clipWidth / 2;
        //     const clipTop = clipCenter.y - clipHeight / 2;
        //     const clipRight = clipCenter.x + clipWidth / 2;
        //     const clipBottom = clipCenter.y + clipHeight / 2;
        //     const flipX = this.asset.transform.horizontalFlip;
        //     const flipY = this.asset.transform.verticalFlip;
        //     return {
        //         width: imageWidth,
        //         height: imageHeight,
        //         container: {
        //             posX: flipX ? clipRight - imageRight : imageLeft - clipLeft,
        //             posY: flipY ? clipBottom - imageBottom : imageTop - clipTop,
        //             width: clipWidth,
        //             height: clipHeight,
        //             viewBoxWidth: clipWidth,
        //             viewBoxHeight: clipHeight,
        //         },
        //     };
        // }
        if (this.asset.attribute?.container?.id && this.target && this.renderTarget) {
            const {
                attribute: {
                    width: oldWidth,
                    height: oldHeight,
                    container: { width: oldCWidth, height: oldCHeight, posX: oldCPosX, posY: oldCPosY },
                },
            } = this.asset;
            const percentX = oldCWidth / oldWidth;
            const percentY = oldCHeight / oldHeight;
            const imageWidth = Math.floor(width / percentX);
            const imageHeight = Math.floor(height / percentY);
            const clipLeft = Math.floor((oldCPosX / oldWidth) * imageWidth);
            const clipTop = Math.floor((oldCPosY / oldHeight) * imageHeight);
            return {
                width: imageWidth,
                height: imageHeight,
                container: {
                    posX: clipLeft,
                    posY: clipTop,
                    width,
                    height,
                    viewBoxWidth: width,
                    viewBoxHeight: height,
                },
            };
        }
        return {
            width,
            height,
        };
    }
}
