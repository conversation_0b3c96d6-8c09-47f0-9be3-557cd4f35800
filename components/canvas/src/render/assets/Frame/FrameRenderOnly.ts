/* eslint-disable tsdoc/syntax */
import { Util } from '../../../util';
import { Asset } from '../Asset';
import equal from 'fast-deep-equal';
import { fabric } from 'fabric';
import { FrameSvgUntils } from './lib';
import { SvgLoader } from '../../../util/SvgLoader';
type ExtraParams = {
    id: string;
    viewBoxWidth: number;
    viewBoxHeight: number;
};
export type ExtraFabricObject = fabric.Object & ExtraParams;
export type ExtraFabricImage = fabric.Image & ExtraParams;

export class FrameRenderOnly extends Asset {
    readonly type = 'frame';
    override renderOnly = true;
    declare asset: Tgs.IFrameAsset;
    protected declare renderTarget: fabric.Group | undefined;
    protected svgOriginWidth = 0;
    protected svgOriginHeight = 0;
    protected svgScaleRadioWidth = 1;
    protected svgScaleRadioHeight = 1;
    protected viewBoxWidth = 0;
    protected viewBoxHeight = 0;
    // 初始化相关信息
    protected declare initAttributeInfo: Tgs.IFrameAttribute;
    // 记录svg中的偏移
    protected svgTranslate = {
        backgroundGroup: {
            x: 0,
            y: 0,
        },
        contentGroup: {
            x: 0,
            y: 0,
        },
        contentImage: {
            x: 0,
            y: 0,
        },
        backgroundImage: {
            x: 0,
            y: 0,
        },
    };
    // svg对象的尺寸
    protected svgObjectSize = {
        backgroundImage: {
            width: 0,
            height: 0,
        },
        contentImage: {
            width: 0,
            height: 0,
        },
    };
    // svg视口缩放
    protected svgScale = 1;
    protected declare contentAreaTarget: ExtraFabricObject | undefined;
    protected declare backgroundAreaTarget: ExtraFabricObject | undefined;
    protected contentImageTarget: ExtraFabricImage | undefined;
    protected backgroundImageTarget: ExtraFabricImage | undefined;
    protected declare clipImageTarget: fabric.Image | null;
    protected declare frameCanvasLogic: Tgs.FrameCanvasLogic;
    override async init() {
        await this.render();
        return true;
    }
    async loadSvg(asset: Tgs.IFrameAsset) {
        if (asset.attribute.SVGUrl) {
            return await SvgLoader.loadSvg(asset.attribute.SVGUrl, 'content');
        }
    }
    protected tranOpactiy(opacity: number | undefined) {
        if (opacity !== undefined) {
            return opacity / 100;
        } else {
            return 1;
        }
    }
    protected setFrameCanvasLogic = (frameCanvasLogic: Tgs.FrameCanvasLogic) => {
        this.frameCanvasLogic = frameCanvasLogic;
    };
    /**
     * 替换宽高尺寸
     * @param svg svg字符串
     * @returns
     */
    protected replaceSvgSize(svg: string) {
        return svg
            .replace(/width="[^"]+"/, 'width="' + this.asset.attribute.width + '"')
            .replace(/height="[^"]+"/, 'height="' + this.asset.attribute.height + '"');
    }
    /**
     * 更新事件重载
     * @param asset 元素
     * @param index 索引
     * @param pageIndex 页面索引
     * @returns
     */
    override async update(asset: Tgs.IAsset<Tgs.TAssetType>, index: number, pageIndex: number) {
        this.setIndexAndGroup(index, pageIndex, asset);
        // 判断是否有状态更新
        const isSameAttribute = equal(this.asset.attribute, asset.attribute);
        const isSameTransform = equal(this.asset.transform, asset.transform);
        const { frameBackgroundInfo, color: newColor, picUrl } = asset.attribute as Tgs.IFrameAttribute;
        // 颜色是否一致
        const isSameContentColor = equal(this.asset.attribute.color, newColor);
        const isSameBackgroundColor = equal(this.asset.attribute.frameBackgroundInfo.color, frameBackgroundInfo.color);
        // 图片是否一致
        const isSameContentPic = equal(this.asset.attribute.picUrl, picUrl);
        const isSameBackgroundPic = equal(this.asset.attribute.frameBackgroundInfo.picUrl, frameBackgroundInfo.picUrl);
        this.asset = asset as Tgs.IFrameAsset;
        if (this.rotating || this.scaling || this.moving) {
            return true;
        }
        const {
            attribute: { width, height, opacity },
            transform,
        } = this.asset;
        if (!isSameAttribute) {

            if (this.target) {
                this.setControlTarget(
                    {
                        width,
                        height,
                    },
                    transform,
                );
            }
            this.renderTarget?.setOptions({
                opacity: opacity ? opacity / 100 : 1,
                scaleX: width / this.svgOriginWidth,
                scaleY: height / this.svgOriginHeight,
            });
            if (!isSameContentColor || !isSameBackgroundColor) {
                this.updateColor(this.asset.attribute, isSameContentColor ? 'background' : 'content');
            }

            if (!isSameContentPic || !isSameBackgroundPic) {
                await this.updatePic(this.asset.attribute, isSameContentPic ? 'background' : 'content');
            }
            // // console.log(this.renderTarget)
            // this.renderTarget?.destroy();
            // // await this.renderTargets();
            !this.skipRequestRender() && this.mainCanvas.requestRenderAll();
            this.frameCanvasLogic?.updateAsset();
            // this.render();
        }
        if (!isSameTransform) {
            if (this.target) {
                this.setControlTarget(
                    {
                        width,
                        height,
                    },
                    transform,
                );
            }
            this.updateRenderTargetSize({
                angle: transform.rotate,
                x: transform.posX,
                y: transform.posY,
                options: {
                    flipX: transform.horizontalFlip,
                    flipY: transform.verticalFlip,
                },
            });
            !this.skipRequestRender() && this.mainCanvas.requestRenderAll();
            this.frameCanvasLogic?.updateAsset();
        }
        if (this.asset.meta.group && (!isSameAttribute || !isSameTransform)) {
            this.updateGroupHelperRectPostion(this.asset);
        }
        this.updateRenderZIndex();
        return true;
    }
    override remove() {
        if (this.renderTarget && (this.hasAdd || this.destroyed)) {
            this.renderTarget.clipPath = undefined;
            this.className && this.removeRenderZIndex(this.className);
            this.mainCanvas.remove(this.renderTarget);
            this.hasAdd = false;
        }
        if (this.target && this.destroyed) {
            this.removeEvents(this.target);
            this.target.clipPath = undefined;
            this.mainCanvas.remove(this.target);
            this.hasAddControl = false;
        }
        this.frameCanvasLogic?.removeFrameListener?.()
    }
    protected updateColor(attribute: Tgs.IFrameAttribute, updateType: 'content' | 'background') {
        if (!attribute) return;
        const { color, frameBackgroundInfo } = attribute;
        switch (updateType) {
            case 'content':
                this.contentAreaTarget?.setOptions({
                    fill: Util.rgbaToString(color),
                    fillOpacity: 1,
                });
                (this.contentImageTarget as fabric.Image)?.setSrc('', () => {
                    this.contentImageTarget?.setOptions({
                        width: 0,
                        height: 0,
                        dirty: true,
                    });
                    !this.skipRequestRender() && this.mainCanvas.requestRenderAll();
                });

                break;
            case 'background':
                if (updateType === 'background') {
                    this.backgroundAreaTarget?.setOptions({
                        fillOpacity: 1,
                        fill: Util.rgbaToString(frameBackgroundInfo.color),
                    });
                    (this.backgroundImageTarget as fabric.Image)?.setSrc('', () => {
                        this.backgroundImageTarget?.setOptions({
                            width: 0,
                            height: 0,
                            dirty: true,
                        });
                        !this.skipRequestRender() && this.mainCanvas.requestRenderAll();
                    });

                    break;
                }
        }
    }
    protected updatePic(attribute: Tgs.IFrameAttribute, updateType: 'content' | 'background') {
        if (!attribute) return;
        const { transformX, transformY, picUrl, picWidth, picHeight, frameBackgroundInfo } = attribute;
        // 这里需要使用初始化时的宽高，因为元素缩放只改变了scale比列，没有动宽高
        const { width, height } = this.initAttributeInfo;
        return new Promise((res, rej) => {
            switch (updateType) {
                case 'content':
                    if (this.contentImageTarget) {
                        fabric.util.loadImage(picUrl, (img) => {
                            if(picUrl != this.asset.attribute.picUrl) return
                            // const { width: originWidth, height: originHeight } = (
                            //     obj as fabric.Image
                            // ).getOriginalSize();
                            const { width: originWidth, height: originHeight } = img;
                            // svg视口缩放
                            const svgScale = Math.min(
                                Number(this.contentImageTarget?.viewBoxWidth) / width,
                                Number(this.contentImageTarget?.viewBoxHeight) / height,
                            );
                            const scale = Math.min(
                                Number(picWidth) / Number(originWidth) / svgScale,
                                Number(picHeight) / Number(originHeight) / svgScale,
                            );
                            // 内容区的位置计算：先将内容区移至整个元素的左上顶点，再加上svg中content group的偏移
                            const areaLeft = -width / 2 + this.svgTranslate.contentGroup.x / svgScale;
                            const areaTop = -height / 2 + this.svgTranslate.contentGroup.y / svgScale;
                            // 图形区位置计算：先将图片移至与内容区左上顶点重叠，再根据图形偏移参数进行偏移
                            const imageLeft = areaLeft + transformX / svgScale;
                            const imageTop = areaTop + transformY / svgScale;
                            // 需要重新计算scaleX,scaleY
                            (this.contentImageTarget as fabric.Image).setElement(img);
                            this.contentImageTarget?.setOptions({
                                width: originWidth,
                                height: originHeight,
                                dirty: true,
                                left: imageLeft,
                                top: imageTop,
                                scaleX: scale,
                                scaleY: scale,
                            });
                            const clipLeft = -Number(originWidth) / 2 - transformX / svgScale / scale;
                            const clipTop = -Number(originHeight) / 2 - transformY / svgScale / scale;
                            this.contentImageTarget?.clipPath?.setOptions({
                                left: clipLeft,
                                top: clipTop,
                                scaleX: 1 / scale / svgScale,
                                scaleY: 1 / scale / svgScale,
                            });
                            // 解决图片替换后有残留的问题
                            this.renderTarget?.setOptions({
                                width: Number(this.renderTarget.width) + 0.0000001,
                                height: Number(this.renderTarget.height) + 0.0000001,
                                dirty: true,
                            });
                            console.log('更新图片完成',picUrl)
                            this.mainCanvas.requestRenderAll();
                            res('');
                        }, null, 'anonymous');
                        // this.mainCanvas.requestRenderAll();
                    }
                    if(this.contentAreaTarget && picUrl){
                        this.contentAreaTarget?.set({
                            fill:'transparent'
                        })
                    }
                    break;
                case 'background':
                    if (!this.backgroundImageTarget) return;
                    fabric.util.loadImage(frameBackgroundInfo.picUrl, (img) => {
                        const { width: originWidth, height: originHeight } = img;
                        // svg视口缩放
                        const svgScale = Number(
                            Math.min(
                                Number(this.backgroundImageTarget?.viewBoxWidth) / width,
                                Number(this.backgroundImageTarget?.viewBoxHeight) / height,
                            ).toFixed(2),
                        );
                        const scale = Number(
                            Math.max(
                                Number(frameBackgroundInfo.width) / Number(originWidth) / svgScale,
                                Number(frameBackgroundInfo.height) / Number(originHeight) / svgScale,
                            ).toFixed(2),
                        );
                        // 内容区的位置计算：先将内容区移至整个元素的左上顶点，再加上svg中content group的偏移
                        const areaLeft = -width / 2 + this.svgTranslate.backgroundGroup.x / svgScale;
                        const areaTop = -height / 2 + this.svgTranslate.backgroundGroup.y / svgScale;
                        // 图形区位置计算：先将图片移至与内容区左上顶点重叠，再根据图形偏移参数进行偏移
                        const imageLeft = areaLeft + frameBackgroundInfo.transformX / svgScale;
                        const imageTop = areaTop + frameBackgroundInfo.transformY / svgScale;
                        // 需要重新计算scaleX,scaleY
                        this.backgroundImageTarget?.setOptions({
                            width: Number(originWidth),
                            height: Number(originHeight),
                            dirty: true,
                            left: imageLeft,
                            top: imageTop,
                            scaleX: scale,
                            scaleY: scale,
                        });
                        this.backgroundImageTarget?.setElement(img);

                        const clipLeft =
                            -Number(originWidth) / 2 - frameBackgroundInfo.transformX / svgScale / Number(scale);
                        const clipTop =
                            -Number(originHeight) / 2 - frameBackgroundInfo.transformY / svgScale / Number(scale);
                        this.backgroundImageTarget?.clipPath?.setOptions({
                            left: clipLeft,
                            top: clipTop,
                            scaleX: 1 / scale / svgScale,
                            scaleY: 1 / scale / svgScale,
                        });
                        // 解决图片替换后有残留的问题
                        this.renderTarget?.setOptions({
                            width: Number(this.renderTarget.width) + 0.0000001,
                            height: Number(this.renderTarget.height) + 0.0000001,
                            dirty: true,
                        });
                        this.mainCanvas.requestRenderAll();
                        res('');
                    }, null, 'anonymous');
                    if(this.backgroundAreaTarget && frameBackgroundInfo.picUrl){
                        this.backgroundAreaTarget?.set({
                            fill:'transparent'
                        })
                    }
                    break;
            }
        });
    }
    // renderTarget 销毁再创建
    protected override async renderTargets() {
        let svgString: string | undefined = undefined;
        if (this.asset.attribute.svg) {
            svgString = this.asset.attribute.svg;
            // svgString = this.replaceSvgSize(svgString);
            svgString = this.replaceSvgSize(svgString);
            svgString = this.replaceSvgParams(svgString);
        } else {
            try {
                const svg = await this.loadSvg(this.asset);
                if (typeof svg == 'string') {
                    svgString = this.replaceSvgSize(svg);
                    // 区分是初次加载还是二次加载
                    if (this.asset.attribute.defaultPicInfo.picUrl) {
                        svgString = this.replaceSvgParams(svgString);
                    }
                }
            } catch (error) {
                console.error('load svg error: ', error);
            }
        }
        if (typeof svgString === 'string') {
            await new Promise<void>(async (res, rej) => {
                const { obj, opt } = await new Promise<{ obj: fabric.Object[]; opt: any }>((resp, reje) => {
                    fabric.loadSVGFromString(svgString as string, (obj, opt) => {
                        if (obj && opt) {
                            this.omitSvgTranlateParams(svgString as string);
                            this.initAttributeInfo = FrameSvgUntils.omitSvgStringParams(
                                svgString as string,
                                this.asset,
                            ) as Tgs.IFrameAttribute;
                            resp({ obj, opt });
                        } else {
                            reje({});
                        }
                        // @ts-ignore
                    }, null, {crossOrigin: 'anonymous'});
                });
                if (!obj || !opt) {
                    rej();
                    return;
                }
                const { attribute, transform } = this.asset;

                const group = new fabric.Group(obj, {
                    ...opt,
                    left: transform.posX,
                    top: transform.posY,
                    opacity: attribute.opacity ? attribute.opacity / 100 : 1,
                    flipY: transform.verticalFlip,
                    flipX: transform.horizontalFlip,
                });

                this.svgOriginWidth = group.width as number;
                this.svgOriginHeight = group.height as number;
                if (this.svgOriginWidth === 0) {
                    this.svgOriginWidth = group.width as number;
                    this.svgOriginHeight = group.height as number;
                }
                const options: fabric.IObjectOptions = {
                    data: {
                        className: this.className,
                        group: this.groupName,
                        pageIndex: this.pageIndex
                    },
                    left: transform.posX,
                    top: transform.posY,
                    opacity: attribute.opacity ? attribute.opacity / 100 : 1,
                    centeredRotation: true,
                    container: this.clipPage,
                    visible: this.visible,
                };
                this.renderTarget = group;
                await this.setSvgObjectOptions(obj);
                this.renderTarget.setOptions({
                    name: 'frame-render-' + this.className,
                    selectable: false,
                    evented: false,
                    ...options,
                    scaleX: attribute.width / this.svgOriginWidth,
                    scaleY: attribute.height / this.svgOriginHeight,
                });
                this.updateRenderTargetSize({
                    angle: transform.rotate,
                    x: transform.posX,
                    y: transform.posY,
                });
                this.setControlTarget(
                    {
                        ...options,
                        width: attribute.width,
                        height: attribute.height,
                    },
                    transform,
                    'frame-' + this.className,
                );

                this.actions.initFrameInfo({
                    target: this.target as fabric.Group,
                    renderTarget: this.renderTarget as fabric.Group,
                    asset: this.asset,
                    contentAreaObj: this.contentAreaTarget as fabric.Object,
                    backgroundAreaObj: this.backgroundAreaTarget as fabric.Object,
                    contentImageObj: this.contentImageTarget as fabric.Object,
                    backgroundImageObj: this.backgroundImageTarget as fabric.Object,
                    isSingleFrame: !this.backgroundAreaTarget,
                    mainCanvas: this.mainCanvas as fabric.Canvas,
                    afterUpdateCb: () => {
                        this.mainCanvas.renderAll();
                    },
                    svgTranslate: {
                        contentGroup: this.svgTranslate.contentGroup,
                        backgroundGroup: this.svgTranslate.backgroundGroup,
                    },
                    initAttributeInfo: this.initAttributeInfo,
                    setFrameCanvasLogic: this.setFrameCanvasLogic,
                });
                res();
            });
        }
    }
    /**
     * 根据事件更新对象信息
     * @param key 移动、选择、缩放
     */
    override updateRelatedObjects(key: 'moving' | 'rotating' | 'scaling', opt: fabric.IEvent<MouseEvent>) {
        const { left, top, width, height, angle } = this.getTransformEndSize();
        switch (key) {
            case 'moving':
            case 'rotating': {
                this.renderTarget?.rotate(0);
                this.renderTarget?.setOptions({
                    left,
                    top,
                });
                this.renderTarget?.rotate(angle);
                break;
            }

            case 'scaling': {
                this.renderTarget?.rotate(0);
                this.renderTarget?.setOptions({
                    left,
                    top,
                    scaleX: width / this.svgOriginWidth,
                    scaleY: height / this.svgOriginHeight,
                });
                this.renderTarget?.rotate(angle);
                break;
            }
        }
    }
    /**
     * 替换svg字符串中的参数
     * @param svgString svg字符串
     * @returns
     */
    protected replaceSvgParams(svgString: string) {
        svgString = FrameSvgUntils.replaceSvgAreaParams(svgString, 'content-area', this.asset);
        svgString = FrameSvgUntils.replaceSvgAreaParams(svgString, 'background-area', this.asset);
        svgString = FrameSvgUntils.replaceSvgImageParams(svgString, 'content-image', this.asset);
        svgString = FrameSvgUntils.replaceSvgImageParams(svgString, 'background-image', this.asset);
        return svgString;
    }
    /**
     * 提取svg中各标签的translate参数
     * @param svgString
     */
    protected omitSvgTranlateParams(svgString: string) {
        if (!this.svgTranslate) return;
        // 提取background group的translate参数
        const [backgroundGroupTranslateX, backgroundGroupTranslateY] = FrameSvgUntils.getContentTransformParams(
            svgString as string,
            'background',
        );
        this.svgTranslate.backgroundGroup.x = backgroundGroupTranslateX;
        this.svgTranslate.backgroundGroup.y = backgroundGroupTranslateY;
        // 提取content group的translate参数
        const [contentGroupTranslateX, contentGroupTranslateY] = FrameSvgUntils.getContentTransformParams(
            svgString as string,
        );
        this.svgTranslate.contentGroup.x = contentGroupTranslateX;
        this.svgTranslate.contentGroup.y = contentGroupTranslateY;
        // 提取background-image的宽高参数
        const [backgroundImageWidth, backgroundImageHeight] = FrameSvgUntils.getImageSizeParams(
            svgString as string,
            'background-image',
        );
        this.svgObjectSize.backgroundImage.width = backgroundImageWidth;
        this.svgObjectSize.backgroundImage.height = backgroundImageHeight;
        // 提取background-image的translate参数
        const [backgroundImageTranslateX, backgroundImageTranslateY] = FrameSvgUntils.getImageTranslateParams(
            svgString as string,
            'background-image',
        );
        this.svgTranslate.backgroundImage.x = backgroundImageTranslateX;
        this.svgTranslate.backgroundImage.y = backgroundImageTranslateY;
        // 提取content-image的translate参数
        const [contentImageTranslateX, contentImageTranslateY] = FrameSvgUntils.getImageTranslateParams(
            svgString as string,
            'content-image',
        );
        this.svgTranslate.contentImage.x = contentImageTranslateX;
        this.svgTranslate.contentImage.y = contentImageTranslateY;
        // 提取background-image的宽高参数
        const [contentImageWidth, contentImageHeight] = FrameSvgUntils.getImageSizeParams(
            svgString as string,
            'content-image',
        );
        this.svgObjectSize.contentImage.width = contentImageWidth;
        this.svgObjectSize.contentImage.height = contentImageHeight;
    }
    /**
     * 设置裁剪
     * @param areaObject 内容区fabric对象
     * @param imageObject 图片区fabric对象
     * @param type 设置类型
     */
    protected async setImageClipPath(
        areaObject: fabric.Object,
        imageObject: fabric.Object,
        type: 'background' | 'content' = 'background',
    ) {
        await new Promise((res) => {
            const {
                attribute: { width, height },
            } = this.asset;
            const svgScale = Number(
                Math.min(
                    (areaObject as ExtraFabricObject)?.viewBoxWidth / width,
                    (areaObject as ExtraFabricObject)?.viewBoxHeight / height,
                ).toFixed(2),
            );
            this.svgScale = svgScale;
            let groupTranslate: { x: number; y: number }, imageTranslate: { x: number; y: number };
            switch (type) {
                case 'background':
                    groupTranslate = this.svgTranslate.backgroundGroup;
                    imageTranslate = this.svgTranslate.backgroundImage;
                    break;
                case 'content':
                    groupTranslate = this.svgTranslate.contentGroup;
                    imageTranslate = this.svgTranslate.contentImage;
                    break;
            }
            // 内容区的位置计算：先将内容区移至整个元素的左上顶点，再加上svg中content group的偏移
            const areaLeft = -width / 2 + groupTranslate.x / svgScale;
            const areaTop = -height / 2 + groupTranslate.y / svgScale;
            areaObject?.setOptions({
                originX: 'left',
                originY: 'top',
                left: areaLeft,
                top: areaTop,
            });
            areaObject?.setCoords();
            areaObject?.clone(
                (obj: fabric.Object) => {
                    // 裁剪对象位置计算：先将裁剪对象移动至图像左上顶点+svg中图像的平移参数，注意会受图片的缩放影响，需要除以缩放比列
                    const clipLeft =
                        -Number(imageObject.width) / 2 - imageTranslate.x / svgScale / Number(imageObject.scaleX);
                    const clipTop =
                        -Number(imageObject.height) / 2 - imageTranslate.y / svgScale / Number(imageObject.scaleY);
                    obj.setOptions({
                        centeredRotation: true,
                        originX: 'left',
                        originY: 'top',
                        left: clipLeft,
                        top: clipTop,
                        scaleX: 1 / Number(imageObject.scaleX) / svgScale,
                        scaleY: 1 / Number(imageObject.scaleY) / svgScale,
                    });
                    obj.setCoords();
                    // 图形区位置计算：先将图片移至与内容区左上顶点重叠，再根据图形偏移参数进行偏移
                    const imageLeft = areaLeft + imageTranslate.x / svgScale;
                    const imageTop = areaTop + imageTranslate.y / svgScale;

                    imageObject.setOptions({
                        originX: 'left',
                        originY: 'top',
                        left: imageLeft,
                        top: imageTop,
                        clipPath: obj,
                    });
                    imageObject.setCoords();
                    res('');
                },
                ['points', 'd', 'viewBoxHeight', 'viewBoxWidth'],
            );
        });

        return [areaObject, imageObject];
    }
    /**
     * 计算设置解析出的svg对象的各个属性
     * @param objects fabric解析出的所有svg对象
     */
    protected async setSvgObjectOptions(objects: fabric.Object[]) {
        const backgroundClipPath = objects[0];
        const backgroundImageElement = objects[1];
        const contentClipPath = objects[2];
        const contentImageElement = objects[3];
        // 双层相框
        if (contentImageElement) {
            const [backgroundAreaTarget, backgroundImageTarget] = await this.setImageClipPath(
                backgroundClipPath,
                backgroundImageElement,
            );
            this.backgroundAreaTarget = backgroundAreaTarget as ExtraFabricObject;
            this.backgroundImageTarget = backgroundImageTarget as ExtraFabricImage;
            const [contentAreaTarget, contentImageTarget] = await this.setImageClipPath(
                contentClipPath,
                contentImageElement,
                'content',
            );
            this.contentAreaTarget = contentAreaTarget as ExtraFabricObject;
            this.contentImageTarget = contentImageTarget as ExtraFabricImage;
        } else {
            const [contentAreaTarget, contentImageTarget] = await this.setImageClipPath(
                backgroundClipPath,
                backgroundImageElement,
                'content',
            );
            this.contentAreaTarget = contentAreaTarget as ExtraFabricObject;
            this.contentImageTarget = contentImageTarget as ExtraFabricImage;
        }
    }
}
