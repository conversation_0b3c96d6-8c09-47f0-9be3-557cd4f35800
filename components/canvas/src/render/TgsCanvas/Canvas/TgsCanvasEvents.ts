import type { IUpdateTarget } from '../../../actions';
import { Util } from '../../../util';
import type { ImageAsset, LineAsset, SvgAsset, TCanvasAsset, TextAsset } from '../../assets';
import { TgsCanvasPage } from '../Page';
import { TgsCanvasBase } from './TgsCanvasBase';
import { fabric } from 'fabric';
import { klona as cloneDeep } from 'klona';
import { CANVAS_GAP } from '../config';

interface IPageContainerMap {
    [key: string | number]: TgsCanvasPage;
}

/** 整个画布类, 基类只处理事件 */
export class TgsCanvasEvents extends TgsCanvasBase {
    override pageContainerMap: IPageContainerMap = {};
    override filmStripPageMap: IPageContainerMap = {};

    protected downPointer: { pointer: fabric.Point; isInPage: boolean } | null = null;

    protected bindEvent() {
        // this.canvas.on('after:render', () => {
        //     console.log('initTime ', this.initTime, 'after:render ', new Date().getTime() - this.initTime);
        // });
        // 选择元素
        this.bindSelect();
        // 画布缩放
        this.bindOnWheel();
        // 画布hover
        this.bindeOnHover();
        // 画布移动
        let isMouseDown = false;
        let isRightButton = false;
        let isRightButtonDrag = false;
        let startX = 0;
        let startY = 0;
        let startTranslateX = 0;
        let startTranslateY = 0;
        this.canvas.on('mouse:down:before', (opt) => {
            opt.e.stopPropagation();
            isMouseDown = true;
            const pointer = opt.pointer as fabric.Point;
            const { isInPage, pageIndex } = this.isPointInPage(pointer, opt.target?.container);
            this.downPointer = { pointer, isInPage };
            const selectTarget = this.canvas.getActiveObjects();
            if (isInPage && typeof pageIndex === 'number' && pageIndex >= 0) {
                this.currentPage = pageIndex;
                this.setFabricCanvasPageIndex(pageIndex);
                if (opt.target?.data?.type === 'background' && opt.target?.data?.imageClipping) {
                    this.isActivePage = false;
                } else {
                    this.isActivePage = true;
                }
                this.actions.onUpdateSelectedPage({ pageNumber: pageIndex, isShowBorder: this.isActivePage });
            } else {
                if (selectTarget?.length > 1) {
                    this.isActivePage = false;
                    this.actions.onUpdateSelectedPage({ pageNumber: -1 });
                    this.onWheelToPage(undefined, opt);
                } else {
                    // 点击画布空白处，取消选中页面
                    this.actions.onUpdateSelectedPage({ pageNumber: -1 });
                }
            }
            if (opt.button === 1 && selectTarget?.length === 1) {
                const active = this.canvas.getActiveObject();
                if (active && active?.name === 'image-clipping-select-' + active?.data?.className) {
                    active.fire('mouse:down', opt);
                }
            }
            if (opt.target && this.isShowHoverBorder(opt.target)) {
                this.cancelAssetHoverEffect(opt.target);
                this.canvas.requestRenderAll();
            }
            // this.hoverLinkLine.hide();
        });
        this.canvas.on('mouse:down', (opt) => {
            opt.e.stopPropagation();
            startX = opt.e.clientX;
            startY = opt.e.clientY;

            this.handleGroupMouseUpSubTarget(opt);
            if (opt.button === 3 && isMouseDown) {
                isRightButton = true;
                const transform = this.canvas.viewportTransform as number[];
                startTranslateX = transform[4];
                startTranslateY = transform[5];
                // 右键点击 也可以选中元素并且有弹窗
                if (
                    opt.target &&
                    opt.target.get('type') !== 'activeSelection' &&
                    opt.target.data?.type !== 'background'
                ) {
                    this.canvas.setActiveObject(opt.target);
                }
            }
            // console.log(opt.target?.get('type'),opt.target?.get('type') == 'activeSelection' )
            if (opt.target?.get('type') == 'activeSelection') {
                // console.log(opt)
                return;
            } else {
                opt.target && opt.target.fire('mouse:down', opt);
            }
        });

        this.canvas.on('mouse:move', (opt) => {
            // opt.e.stopPropagation();
            // console.log('mouse:move', opt);

            const pointer = opt.pointer as fabric.Point;
            if (!pointer) return;
            const activeObject = this.canvas.getActiveObject();
            if (!isMouseDown) {
                const { isInPage, pageIndex } = this.isPointInPage(pointer);
                this.setFabricCanvasPageIndex(pageIndex);
                if (isInPage && typeof pageIndex === 'number' && pageIndex >= 0) {
                    this.setPageAssetIsSelected(pageIndex);
                    if (activeObject?.name?.indexOf('page') === -1) {
                        activeObject?.setOptions({ selectable: true, evented: true });
                    }
                }
                // hover tooltip 如果放在mouse:over里面，背景图超出画布之外鼠标悬浮就会触发，当进入画布反而不触发了
                if (opt.target && this.isShowHoverBorder(opt.target)) {
                    if (!isInPage) {
                        this.hoveringAsset = undefined;
                        this.cancelAssetHoverEffect(opt.target);
                    } else {
                        this.hoveringAsset = opt.target;
                        this.addAssetHoverEffect(opt.target, opt.subTargets);
                    }
                    this.canvas.requestRenderAll();
                }
            }
            const cornerName = activeObject?._findTargetCorner(pointer) as string;
            if (activeObject && ['mtl', 'mbl', 'mll', 'mrl'].includes(cornerName)) {
                if (!isMouseDown) {
                    const asset = this.getPageAssetByClassName(activeObject.data?.className);
                    this.hoverLinkLine.show({
                        target: asset!,
                        anchor: cornerName,
                        pointer: opt.absolutePointer!,
                        from: 'mouse:move',
                        page: this.pageContainerMap[this.currentPage],
                    });
                }
                activeObject.setOptions({
                    data: {
                        ...activeObject.data,
                        hoverControl: cornerName,
                    },
                });
            } else if (this.hoverLinkLine.visible) {
                if (isMouseDown && activeObject) {
                    // const asset = this.getPageAssetByClassName(activeObject.data?.className);
                    // this.hoverLinkLine.show({ target: asset, anchor: cornerName, pointer });
                } else {
                    this.hoverLinkLine.hide({
                        page: this.pageContainerMap[this.currentPage]
                    });
                }
                activeObject?.setOptions({
                    data: {
                        ...activeObject.data,
                        hoverControl: undefined,
                    },
                });
            }
            // if (activeObject) {
            //     const asset = this.getPageAssetByClassName(activeObject.data?.className);
            //     console.log(444444)
            //     this.hoverLinkLine.show({ target: asset, anchor: cornerName, pointer });
            // }
            // 如果存在选择元素要判断 鼠标是否在选择元素的内部
            if (!(activeObject && (activeObject.containsPoint(pointer) || activeObject._findTargetCorner(pointer)))) {
                this.canvas.setCursor(opt.target?.hoverCursor || 'default');
            }
            if (isRightButton && opt.target) {
                const deltaX = opt.e.clientX - startX;
                const deltaY = opt.e.clientY - startY;
                if (Math.sqrt(deltaX * deltaX + deltaY * deltaY) > 5) {
                    isRightButtonDrag = true;
                    this.canvas.setCursor('grab');
                    this.onRightButtonDragging(opt, startX, startY, startTranslateX, startTranslateY);
                }
            }
            opt.target && opt.target.fire('mouse:move', opt);
        });
        this.canvas.on('mouse:up', (opt) => {
            // opt.e.stopPropagation();
            // console.log('mouse:up', opt);
            if (opt.target && opt.target.data?.type === 'background') {
                this.canvas.setCursor('default');
            }
            if (opt.button === 3 && isRightButton && !isRightButtonDrag) {
                // 如果按下右键，再按下左键，先松开左键，再松开右键，只会触发一次左键的 mouse:up 事件
                this.onRightClick(opt);
            }
            if (isRightButtonDrag) {
                this.onRightButtonDragEnd();
                this.canvas.setCursor('default');
            }
            opt.target && !opt.target.group && opt.target.fire('mouse:up', opt);

            isRightButton = false;
            isRightButtonDrag = false;
            const optAsset = this.getPageAssetByClassName(opt.target?.data?.className);
            if (optAsset?.groupName && optAsset.asset?.meta?.groupInnerMoving) {
                if (this.rotating || this.moving || this.scaling) {
                    const actionType = this.rotating ? 'rotating' : this.moving ? 'moving' : 'scaling';
                    setTimeout(() => {
                        this.actions.onGroupInnerAssetTransformEnd(optAsset.groupName as string, actionType);
                        opt.target?.data?.groupTarget?.fire('group:innerAssetTransformEnd');
                    }, 30);
                }
            }

            const isImageClipping = opt.target?.data?.imageClipping;
            const isEditing = opt.target?.data?.editing;
            if (!isImageClipping && !isEditing) {
                this.splitGroupAndSelectedTarget(opt);
                // 图片裁剪操作由内部进行更新
                if (this.rotating) {
                    this.onRotateEnd();
                    opt.target?.setControlVisible('mbr', false);
                }
                if (this.moving) {
                    this.moving = false;
                    if (opt?.target?.group) return;
                    this.onMoveEnd(opt);
                }
                if (this.scaling) {
                    this.onScaleEnd();
                    this.setCornerScaleViability(opt);
                    if (opt.target && opt.target?.data?.type == 'text') {
                        opt.target.fire('scale:end');
                    }
                }
            }
            // 图片以及背景状态下不显示快捷菜单
            this.actions.onShowrtcutMenu(!isImageClipping);
            isMouseDown = false;
        });
        this.canvas.on('before:transform', (opt) => {
            if (opt.target?.get('type') === 'group' && opt.target?.data?.group) {
                opt.target.fire('before:transform', opt);
            }
            this.hideHelperRectTarget();
        });
        // 移动
        this.bindMoving();
        // 旋转
        this.bindRotating();
        // 缩放
        this.bindScaling();
        // 双击
        this.bindObjectRemoved();
        // 画布渲染
        this.bindAfterRender();
        // 展示链接线
        this.bindAddLineEffect();
    }

    protected splitGroupAndSelectedTarget(opt: fabric.IEvent<MouseEvent>) {
        // 鼠标抬起时，如果没有拖动，并且有选中重叠元素，那么另外一个重叠的元素
        // 多选元素时，拖动元素不改变选中状态，点击某个元素，选中该元素，其他元素取消选中
        if (!this.rotating && !this.moving && !this.scaling) {
            const isSkipGroup = this.multiFunctionKey(opt.e) || opt.button !== 1;
            const optSubTarget = opt.subTargets?.[0];
            if (opt.target && !optSubTarget?.data.editing) {
                const optTarget = opt.target ? fabric.util.object.clone(opt.target) : undefined;
                // @ts-ignore
                const overlapedObj = this.canvas.findTarget(opt.e, !isSkipGroup, true);
                if (!overlapedObj?.data || (overlapedObj?.data?.group && overlapedObj?.data.type !== 'group')) return;
                // console.log('splitGroupAndSelectedTarget111', overlapedObj)

                if (overlapedObj !== optTarget && overlapedObj.name !== optTarget.name) {
                    this.cancelAssetHoverEffect(overlapedObj);
                    this.canvas.discardActiveObject();
                    // console.log('splitGroupAndSelectedTarget2222', overlapedObj)
                    if (overlapedObj.data?.type !== 'background' && overlapedObj.name?.indexOf('page') === -1) {
                        this.canvas.setActiveObject(overlapedObj);
                        if (overlapedObj?.data.type == 'group' && !overlapedObj?.data.isEdit) {
                            this.handleGroupSubTargetSelect(
                                overlapedObj as fabric.Group,
                                overlapedObj.data.subSelectTarget,
                            );
                        }
                    }
                }
            }
        }
    }

    protected handleGroupMouseUpSubTarget(opt: fabric.IEvent<MouseEvent>) {
        const { isInPage } = this.isPointInPage(opt.pointer as fabric.Point, opt.target?.container);
        // 处理组合
        let subSelectTarget = opt?.subTargets?.[0];
        if (!subSelectTarget && !opt.target?.data?.group) {
            this.hideHelperRectTarget();
        }
        const isGroupTarget = opt.target && opt.target.get('type') == 'group';
        if (isGroupTarget && (!subSelectTarget || subSelectTarget != this.lastSubSelectTarget)) {
            this.lastSubSelectTarget?.fire('deselected', Object.assign(opt, { groupSubSelectChange: true }));
            if (!this.lastSubSelectTarget) {
                opt.target?.data?.subSelectTarget?.fire(
                    'deselected',
                    Object.assign(opt, { groupSubSelectChange: true }),
                );
            }
            this.lastSubSelectTarget = undefined;
            this.hideHelperRectTarget();
        }
        // 处理group中子元素情况
        if (isGroupTarget && isInPage) {
            if (opt.target?.data?.isEdit || this.multiFunctionKey(opt.e)) {
                subSelectTarget = undefined;
            } else {
                this.handleGroupSubTargetSelect(opt.target as fabric.Group, subSelectTarget);
                subSelectTarget?.fire('selected', opt);
            }
        } else if (!opt.target?.data?.group && this.lastSubSelectTarget) {
            this.lastSubSelectTarget?.fire('deselected', Object.assign(opt, { groupSubSelectChange: true }));
            this.lastSubSelectTarget = undefined;
        }
        subSelectTarget && (this.lastSubSelectTarget = subSelectTarget);
        this.canvas.requestRenderAll();
    }

    protected onRightClick(opt: fabric.IEvent<MouseEvent>) {
        this.actions.onRightClick();
        if (opt.target) {
            if (opt.target?.get('type') === 'activeSelection') {
                this.selectClassNames.forEach((className) => {
                    const a = this.getPageAssetByClassName(className);
                    a?.target?.fire('rightclick', opt);
                });
            } else {
                opt.target?.fire('rightclick', opt);
            }
        }
    }

    protected onRightButtonDragging(
        opt: fabric.IEvent<MouseEvent>,
        startX: number,
        startY: number,
        startTranslateX: number,
        startTranslateY: number,
    ) {
        const transform = this.canvas.viewportTransform?.slice() as number[];
        transform[4] = startTranslateX + opt.e.clientX - startX;
        transform[5] = startTranslateY + opt.e.clientY - startY;
        this.canvas.setViewportTransform(transform);
        this.canvasInfo.x = transform?.[4];
        this.canvasInfo.y = transform?.[5];
        // TODO 有 dom 依赖坐标，待改进性能
        this.actions.onUpdateCanvasInfo({
            x: transform?.[4],
            y: transform?.[5],
        });
        this.canvas.requestRenderAll();
    }

    protected onRightButtonDragEnd() {
        const transform = this.canvas.viewportTransform;
        this.actions.onUpdateCanvasInfo({
            x: transform?.[4],
            y: transform?.[5],
        });
        this.actions.onRightButtonDragEnd();
    }
    /* 滚动选择页面 */
    protected onWheelToPage(transformY?: number, opt?: fabric.IEvent<MouseEvent>) {
        if (this.renderMode !== '') return;
        if (transformY === undefined) {
            const transform = this.canvas.viewportTransform?.slice() as number[];
            transformY = transform?.[5];
        }
        const zoom = this.canvas.getZoom();
        const canvasHeight = this.canvas?.height as number;
        const baseLine = transformY < 0 ? canvasHeight / 2 : canvasHeight / 2;
        const pageHeight = this.canvasInfo.height * zoom + this.baseGap;
        // const activeObject = this.canvas.getActiveObject();
        for (const key in this.pageContainerMap) {
            const item = this.pageContainerMap[key];
            const pageTop = (item.page.top as number) * zoom + transformY;
            const pageBottom = pageTop + pageHeight;
            if (pageTop < baseLine && pageBottom > baseLine) {
                const isShowBorder = this.isActivePage;
                this.currentPage = Number(key);
                this.actions.onUpdateSelectedPage({ pageNumber: Number(key), isShowBorder });
                break;
            }
        }
    }

    /** 滚动限制判断 */
    protected onWheelLimit(transformY: number, delta: number, scrollY: number) {
        //向上滚动限制
        const canvasHeight = this.canvas?.height as number;
        let cHeight = canvasHeight || 0;
        const pageHeight = this.canvasInfo.height * this.canvasInfo.scale;

        // 单页情况下页面超出dom才可以滚动，
        if (this.renderMode === 'pull' || this.renderMode === 'board') {
            const limitNum = 90;
            const pageBottom = transformY + pageHeight;
            cHeight = cHeight - 100;
            if (pageHeight < cHeight && pageBottom < cHeight && transformY > 0) {
                // 切换页面
                this._onWheelChangePageTimer(delta);
                if (this.renderMode === 'board') {
                    return false;
                }
                return true;
            } else {
                if (this.renderMode === 'board') {
                    return false;
                }
                const nextY = transformY - scrollY;
                if (nextY > limitNum) {
                    return delta < 0 ? limitNum : false;
                } else if (nextY + pageHeight < cHeight) {
                    return delta < 0 ? false : cHeight - pageHeight;
                }
            }
        } else {
            const right = Number((this.canvas as any)?.lowerCanvasEl?.dataset.right) || 0;
            const { TopGap, BottomGap, BaseGap } = CANVAS_GAP;
            const topLimit = right > 0 ? BaseGap : TopGap;
            cHeight = cHeight - 10;
            const len = Object.keys(this.pageContainerMap).length;
            const height = pageHeight * len + this.baseGap * (len - 1);
            const maxHeight = canvasHeight - BottomGap - topLimit;

            if (height < cHeight && height + transformY < cHeight && transformY > 40) {
                return true;
            }
            const nextY = transformY - scrollY;
            if (nextY > topLimit) {
                return delta < 0 ? topLimit : false;
            } else if (nextY + height < maxHeight) {
                return delta < 0 ? false : maxHeight - height;
            }
        }
    }
    protected bindOnWheel() {
        // const offset_step = 3;
        let lastTime = new Date().getTime();
        this.canvas.on('mouse:wheel', (opt) => {
            opt.e.preventDefault();
            opt.e.stopPropagation();
            const delta = opt.e.deltaY;
            const pointer = opt.pointer as fabric.Point;
            const { isInPage, page } = this.isPointInPage(opt.pointer as fabric.Point);
            const flag = opt.e.metaKey || opt.e.ctrlKey;
            if (flag || opt.e.ctrlKey) {
                opt.e.stopPropagation();
                opt.e.preventDefault();
                // 缩放
                const diffTime = new Date().getTime() - lastTime;
                let zoom = this.canvas.getZoom();
                let factor = 1;
                lastTime = new Date().getTime();

                if (Math.abs(delta) < 10 || diffTime > 60) {
                    factor = delta < 0 ? 1.025 : 0.975;
                } else {
                    factor = delta < 0 ? 1.25 : 0.75;
                }

                zoom = Math.round(zoom * factor * 1000) / 1000;
                if (zoom > 4) zoom = 4;
                if (zoom < 0.1) zoom = 0.1;
                this.centerZoom(zoom, pointer, delta, page?.page);
                clearTimeout(this._onWheelScaleTimer);
                this.addAssetHoverEffect(this.hoveringAsset);
                this._onWheelScaleTimer = window.setTimeout(() => {
                    this.actions.onMouseWheel('scale');
                    this.updateAsssetScale();
                    clearTimeout(this._onWheelScaleTimer);
                }, 1000);
            } else {
                const transform = this.canvas.viewportTransform?.slice() as number[];
                // 横向滚动 如果横向滚动了 竖向就不滚动了
                if (Math.abs(opt.e.deltaX) > Math.abs(opt.e.deltaY) || opt.e.shiftKey) {
                    let moveX = opt.e.deltaX;
                    if (opt.e.shiftKey) {
                        moveX = Util.isMac() ? opt.e.deltaX : opt.e.deltaY;
                    }
                    const isScrollX = this.onHorizontalMove(moveX, transform);
                    if (isScrollX) return;
                }
                // 竖向滚动
                const scrollY = (delta / 2) * 1.3;
                const isBack = this.onWheelLimit(transform?.[5], delta, scrollY);
                if (isBack) {
                    if (isBack === true) return;
                    if (typeof isBack === 'number') transform[5] = isBack;
                } else {
                    transform[5] -= scrollY;
                }
                this.canvas.setViewportTransform(transform);
                this.canvasInfo.y = transform?.[5];
                // TODO 有 dom 依赖坐标，待改进性能
                this.actions.onUpdateCanvasInfo({
                    y: transform?.[5],
                });
                this.onWheelToPage(transform?.[5]);
                clearTimeout(this._onWheelScrollYTimer);
                this._onWheelScrollYTimer = window.setTimeout(() => {
                    this.actions.onMouseWheel('scrollY', -delta);
                    clearTimeout(this._onWheelScrollYTimer);
                }, 1000);
            }
            this.canvas.requestRenderAll();
        });
    }

    protected bindeOnHover() {
        // 展示tooltip
        // this.canvas.on('mouse:over', (opt) => {
        //     opt.e.stopPropagation();
        //     if (opt.target && this.isShowHoverBorder(opt.target)) {
        //         console.log(opt.target.name)
        //         const pointer = opt.pointer ? opt.pointer : { x: opt.e.offsetX, y: opt.e.offsetY };
        //         const { isInPage } = this.isPointInPage(pointer as fabric.Point);
        //         if (!isInPage) {
        //             return;
        //         }
        //         this.hoveringAsset = opt.target;
        //         this.addAssetHoverEffect(opt.target);
        //         this.canvas.requestRenderAll();
        //     }
        // });
        this.canvas.on('mouse:out', (opt) => {
            this.hoveringAsset = undefined;
            this.cancelAssetHoverEffect(opt.target);
            this.canvas.requestRenderAll();
        });
    }

    /** 取消元素的hover效果，边框以及tooltip */
    protected cancelAssetHoverEffect(target: fabric.Object | undefined) {
        if (!target || (target?.data?.type == 'text' && target?.data?.editing) || target?.data?.hasAssetMoving) return;
        target?.setOptions({ stroke: 'transparent' });
        if (target?.get('type') == 'group' && !target.data?.isEdit) {
            target?.data?.helperShowBoundaryTarget?.setOptions({ stroke: '#transparent', strokeWidth: 0 });
        }
        this.actions.changeAssetTooltipStatus({ status: false });
    }

    /** 添加元素的hover效果，边框以及tooltip */
    protected addAssetHoverEffect(target: fabric.Object | undefined, subTargets?: fabric.Object[]) {
        // 背景处于裁剪状态不展示hover效果
        if (target?.data?.type === 'group' && subTargets?.[0]) {
            target = subTargets[0];
        }
        if (
            !target ||
            (target?.data?.type === 'background' && target?.data?.imageClipping) ||
            target?.data?.hasAssetMoving
        )
            return;
        const width = 1 / this.canvas.getZoom();
        let dis = 2;
        if (target.data?.type !== 'background' && target.name?.indexOf('page') === -1 && !subTargets?.[0]) {
            dis = 0;
            if (target.data?.type === 'line') {
                target?.setOptions({ stroke: '#EF3964' });
            } else {
                target?.setOptions({ stroke: '#EF3964', strokeWidth: width });
            }
            if (target.get('type') == 'group' && !target.data?.isEdit) {
                target?.data?.helperShowBoundaryTarget?.setOptions({ stroke: '#EF3964', strokeWidth: width });
            }
        }
        // 没有选中元素才展示tooltip
        const activeObject = this.canvas.getActiveObject();
        if (['text', 'image', 'qrcode', 'pic', 'table'].indexOf(target.data?.type) > -1 && !activeObject) {
            let bounds = { left: 0, top: 0, width: 0, height: 0 };
            if (target.data?.type === 'background' && target.container) {
                bounds = target.container.getBoundingRect();
            } else {
                if (target.group) {
                    const boundingRect = target.getBoundingRect();
                    const groupTransform = target.group!.calcTransformMatrix();
                    const transformedPoints = [
                        new fabric.Point(boundingRect.left, boundingRect.top),
                        new fabric.Point(boundingRect.left + boundingRect.width, boundingRect.top),
                        new fabric.Point(boundingRect.left, boundingRect.top + boundingRect.height),
                        new fabric.Point(
                            boundingRect.left + boundingRect.width,
                            boundingRect.top + boundingRect.height,
                        ),
                    ].map((point) => fabric.util.transformPoint(point, groupTransform));
                    const minY = Math.min(...transformedPoints.map((point) => point.y));
                    const maxY = Math.max(...transformedPoints.map((point) => point.y));
                    const minX = Math.min(...transformedPoints.map((point) => point.x));
                    const maxX = Math.max(...transformedPoints.map((point) => point.x));
                    const scale = this.canvas.getZoom();
                    const canvasTranslateX = this.canvas.viewportTransform?.[4] || 0;
                    const canvasTranslateY = this.canvas.viewportTransform?.[5] || 0;
                    bounds = {
                        left: minX * scale + canvasTranslateX,
                        top: minY * scale + canvasTranslateY,
                        width: (maxX - minX) * scale,
                        height: (maxY - minY) * scale,
                    };
                } else {
                    bounds = target.getBoundingRect();
                }
            }
            const transform = this.canvas.viewportTransform?.slice() as number[];
            let x = bounds.left + bounds.width / 2 - transform?.[4];
            let y = bounds.top - 33 - transform?.[5] - dis;
            if (target.data?.type == 'text') {
                //文本提示左对齐
                const a = this.getPageAssetByClassName(target.data?.className);
                if (!a) {
                    return;
                }
                const { height } = a.getTransformEndSize();
                x = bounds.left - transform?.[4];
                y = bounds.top + bounds.height / 2 - (height / 2) * this.canvas.getZoom() - 33 - transform?.[5] - 12;
            }
            // 标记为可以替换的裁剪，双击走编辑逻辑
            const isMarkedReplace = target.data?.isMarkedReplace;
            let tipType = target.data?.type;
            if (isMarkedReplace) {
                tipType = 'editImage';
            } else if (target.data?.isClip) {
                tipType = 'clipedImage';
            }
            this.actions.changeAssetTooltipStatus({
                status: true,
                clientX: x,
                clientY: y,
                type: tipType,
            });
        }
    }

    /** 判断是否是需要加边框的元素 */
    protected isShowHoverBorder(target: fabric.Object) {
        const className: string = target.data?.className;
        if (Array.from(this.selectClassNames).includes(className)) {
            return false;
        }
        return true;
    }

    protected bindSelect() {
        const skipSyncSelect = () => {
            this.isSyncSelect = false;
            clearTimeout(this._syncSelectTimer);
        };
        this.canvas.on('selection:cleared', (opt) => {
            if (this.updateActiveSelection) return (this.updateActiveSelection = false);
            skipSyncSelect();
            this.isAllAssetsInPage();
            this.actions.blurAssets();
            this.selectClassNames.clear();
            this.hideHelperRectTarget();
            this.hoverLinkLine.hide();
        });
        this.canvas.on('selection:created', (opt) => {
            skipSyncSelect();
            if (opt.selected && opt.selected.length > 0) {
                if (opt.selected[0].data?.group && opt?.deselected?.[0]?.get('type') == 'group') {
                    return this.canvas.setActiveObject(opt?.deselected?.[0]);
                }
                if (opt.deselected?.[0].group && opt.selected[0]?.get('type') == 'group') return;
                if (this.overPageDeselectedAsset(opt)) return;

                this.setSelectedGroupControlsVisibility(opt.selected?.[0]);
                for (const t of opt.selected) {
                    // if(t.group){
                    //     return this.canvas.setActiveObject(t.group)
                    // }
                    if (t.name && t.data?.className) {
                        this.selectClassNames.add(t.data.className);
                    }
                }
            }
            this.syncSelect(opt);
        });
        this.canvas.on('selection:updated', (opt) => {
            skipSyncSelect();
            if (opt.selected && opt.selected.length > 0) {
                // 在组内元素编辑时，保留外层组的选中态，取消组内元素的选中态
                const isSelectedGroupInnerAsset =
                    opt.selected[0]?.data?.group && opt.selected[0]?.get('type') != 'group';
                if (isSelectedGroupInnerAsset && opt?.deselected?.[0]?.get('type') == 'group') {
                    return this.canvas.setActiveObject(opt?.deselected?.[0], opt.e);
                }

                // 取消选择的是否是组内元素
                const isDeselectedGroupInnerAsset =
                    opt.deselected?.[0]?.data?.group && opt.deselected?.[0].get('type') != 'group';
                if (isDeselectedGroupInnerAsset && opt.selected[0]?.get('type') == 'group') return;
                if (this.overPageDeselectedAsset(opt)) return;
                this.setSelectedGroupControlsVisibility(opt.selected?.[0]);

                if (
                    opt.deselected?.[0]?.data?.group &&
                    opt.deselected?.[0].get('type') === 'group' &&
                    opt.selected[0]?.get('type') !== 'group'
                ) {
                    this.lastSubSelectTarget?.fire('deselected', Object.assign(opt, { groupSubSelectChange: true }));
                    this.lastSubSelectTarget = undefined;
                    this.canvas.setActiveObject(opt.selected?.[0], opt.e);
                }

                for (const t of opt.selected) {
                    if (t.name && t.data?.className) {
                        this.selectClassNames.add(t.data.className);
                    }
                }
            }
            if (opt.deselected && opt.deselected.length > 0) {
                this.setSelectedGroupControlsVisibility(opt.deselected?.[0]);
                for (const t of opt.deselected) {
                    if (t.name && t.data?.className) {
                        this.selectClassNames.delete(t.data.className);
                    }
                }
            }
            this.syncSelect(opt);
        });
        this.canvas.on('selection:sizeChanged', (opt) => {
            if (
                !this.selectClassNames ||
                this.selectClassNames.size <= 1 ||
                !this.selectClassNames.has(opt.target?.data?.className)
            )
                return;
            const targetsInPage: fabric.Object[] = [];
            const pageMap = this.getModePage();
            const page = pageMap[this.currentPage] || pageMap[0];
            this.selectClassNames.forEach((className) => {
                targetsInPage.push(page.assetsMap[className].target as fabric.Object);
            });
            this.updateActiveSelection = true;
            this.setCanvasActiveObject(targetsInPage);
        });
    }
    protected syncSelect(opt?: fabric.IEvent<MouseEvent>) {
        if (this.selectClassNames.size > 0 && !this.isSyncSelect) {
            const selectInfo: {
                asset_index: number;
                className: string;
                page_num?: number;
            }[] = [];
            const pageMap = this.getModePage();
            const page = pageMap[this.currentPage] || pageMap[0];
            const size = this.selectClassNames.size;
            const targetsInPage: fabric.Object[] = [];
            this.selectClassNames.forEach((className) => {
                // 如果只选择一个元素，如果元素被锁定也可以选中
                let isSelected = true;
                if (size > 1 && page?.assetsMap[className]?.asset.meta.locked) {
                    isSelected = false;
                }
                if (page?.assetsMap[className] && isSelected) {
                    selectInfo.push({
                        asset_index: page.assetsMap[className].index,
                        className,
                        page_num: page.pageConfig.pageIndex,
                    });
                    if (
                        page.assetsMap[className].target &&
                        (!page.assetsMap[className].groupName || page.assetsMap[className].type === 'group')
                    ) {
                        targetsInPage.push(page.assetsMap[className].target as fabric.Object);
                    }
                } else {
                    this.selectClassNames.delete(className);
                }
            });
            if (this.selectClassNames.size !== size) {
                this.setCanvasActiveObject(targetsInPage, opt);
            }
            selectInfo.length > 0 && this.actions.onSelectAssets(selectInfo);
        }
    }

    setCanvasActiveObject(targets: fabric.Object[], opt?: fabric.IEvent<MouseEvent>) {
        this.canvas.discardActiveObject();
        const newSelect = new fabric.ActiveSelection(targets, {
            canvas: this.canvas,
        });
        this.canvas.setActiveObject(newSelect, opt?.e);
        this.canvas.requestRenderAll();
    }

    /** 设置多选框样式 */
    protected setSelectedGroupControlsVisibility(obj: fabric.Object) {
        if (obj?.group?.get('type') === 'activeSelection') {
            const options = {
                mb: false,
                mt: false,
                ml: false,
                mr: false,
                mtr: true,
                mtl: false,
                mbl: false,
                mll: false,
                mrl: false,
            };
            obj.group.setControlsVisibility(options);
            obj.group.setControlVisible('mtr2', true);
            obj.group.borderDashArray = [5, 5];
            obj.group.borderColor = 'rgba(239, 57, 100, 0.40)';
            // obj.group.borderScaleFactor = 1;
            obj.group.padding = 2;
        }
    }

    protected bindMoving() {
        this.canvas.on('object:moving', (opt) => {
            if (!this.moving) {
                this.moving = true;
                this.startMovingTime = new Date().getTime();
                this.setSelectedAssetActionState('moving', true);
                // 元素移动
            }
            const curTime = new Date().getTime();
            // 防误触
            if (this.startMovingTime && curTime - this.startMovingTime < 50) return;
            if (opt.target && this.helperRectTargetStatus == 'show') {
                this.hideHelperRectTarget();
            }

            const targetAsset = this.getPageAssetByClassName(opt.target?.data?.className);
            if (this.selectClassNames.size == 1 && targetAsset?.groupName && targetAsset.asset?.meta.groupInnerMoving) {
                const groupTarget = opt.target?.data.groupTarget;
                groupTarget?.data?.helperShowBoundaryTarget?.setOptions({
                    stroke: 'transparent',
                    strokeWidth: 0,
                });
            }
            this.syncAssetRelatedObjects('moving', opt);

            const isGroupInnerAsset = opt.target && opt.target.type !== 'group' && opt.target?.data?.group;
            if (opt.target?.data?.type !== 'background') {
                this.auxiliaryLine.onMoving(opt, this.pageContainerMap[this.currentPage], this.selectClassNames);
                !isGroupInnerAsset &&
                    this.intervalLine.onMoving(opt, this.pageContainerMap[this.currentPage], this.selectClassNames);
            }
            if (opt.target && opt.target?.data?.className) {
                const className = opt.target?.data.className;
                const pageMap = this.getModePage();
                const page = pageMap[this.currentPage] || pageMap[0];
                const selectInfo = [
                    {
                        asset_index: page.assetsMap[className].index,
                        className,
                        page_num: page.pageConfig.pageIndex,
                    },
                ];
                this.actions.onSelectAssets(selectInfo);
            }
            this.assetEditing('moving');
        });
    }

    protected bindRotating() {
        this.canvas.on('object:rotating', (opt) => {
            // console.log('object:rotating: ', opt);
            if (!this.rotating) {
                this.rotating = true;
                this.setSelectedAssetActionState('rotating', true);
                opt.target?.setControlVisible('mbr', true);
            }
            const targetAsset = this.getPageAssetByClassName(opt.target?.data?.className);
            if (this.selectClassNames.size == 1 && targetAsset?.groupName && targetAsset.asset?.meta.groupInnerMoving) {
                const groupTarget = opt.target?.data?.groupTarget;
                groupTarget?.data?.helperShowBoundaryTarget?.setOptions({
                    stroke: 'transparent',
                    strokeWidth: 0,
                });
            }
            if (opt.target) {
                const angle = opt.target?.angle ?? 0;
                if (-3 < angle % 90 && angle % 90 < 3) {
                    opt.target?.straighten();
                }
            }

            this.syncAssetRelatedObjects('rotating', opt);
            this.hideHelperRectTarget();
            this.assetEditing('rotating');
        });
    }

    protected bindScaling() {
        this.canvas.on('object:scaling', (opt) => {
            // console.log('object:scaling: ', opt);
            if (!this.scaling) {
                this.scaling = true;
                this.setSelectedAssetActionState('scaling', true);
            }
            const targetAsset = this.getPageAssetByClassName(opt.target?.data?.className);
            if (this.selectClassNames.size == 1 && targetAsset?.groupName && targetAsset.asset?.meta.groupInnerMoving) {
                const groupTarget = opt.target?.data?.groupTarget;
                groupTarget?.data?.helperShowBoundaryTarget?.setOptions({
                    stroke: 'transparent',
                    strokeWidth: 0,
                });
            }
            // 节流
            // throttle(()=>this.syncAssetRelatedObjects('scaling', opt), 500);
            this.syncAssetRelatedObjects('scaling', opt);
            const isGroupInnerAsset = opt.target && opt.target.type !== 'group' && opt.target?.data?.group;

            if (opt.target?.data?.type !== 'background') {
                this.auxiliaryLine.onScaling(opt, this.pageContainerMap[this.currentPage], this.selectClassNames);
                !isGroupInnerAsset &&
                    this.intervalLine.onScaling(opt, this.pageContainerMap[this.currentPage], this.selectClassNames);
            }
            this.setCornerScaleViability(opt);
            this.hideHelperRectTarget();
            this.assetEditing('scaling');
        });
    }
    protected bindObjectRemoved() {
        this.canvas.on('object:removed', (opt) => {
            const className = opt?.target?.data?.className;
            const pageMap = this.getModePage();
            const page = pageMap[this.currentPage] || pageMap[0];
            const isDestoryed = page?.assetsMap[className]?.destroyed;
            if (isDestoryed) {
                this.hideHelperRectTarget();
                this.actions.onUpdateSelectedPage({ pageNumber: -1 });
            }
        });
        this.canvas.on('object:added', (opt) => {
            if (!opt?.target?.data?.className || this.helperRectTargetStatus != 'show') return;
            const className = opt?.target?.data?.className;
            const pageMap = this.getModePage();
            const page = pageMap[this.currentPage] || pageMap[0];
            if (!page.assetsMap[className]) return;
            const asset = page.assetsMap[className].asset;
            if (asset.meta.group) {
                // if (asset.meta.type == 'text') {
                //     // this.groupHelperRectTarget?.setOptions({
                //     //     width: opt.target.width,
                //     //     height: opt.target.height,
                //     //     top: opt.target.top,
                //     //     left: opt.target.left,
                //     // });
                // } else {
                // this.addHelperRectTarget(asset);
                // }
            }
        });
    }

    protected bindAfterRender() {
        const visibilities: { [key: string]: boolean } = {};
        this.canvas.on('after:render', () => {
            const obj = this.canvas.getActiveObject();
            if (obj) {
                for (const key in obj.controls) {
                    const visiable = obj.controls[key].getVisibility(obj, key);
                    if (visiable && !visibilities[key]) {
                        this.actions.controlAppear(key);
                    }
                    visibilities[key] = visiable;
                }
            } else {
                for (const key in visibilities) {
                    visibilities[key] = false;
                }
            }
        });
    }

    protected onMoveEnd(opt: fabric.IEvent<MouseEvent>) {
        this.moving = false;
        const updateTargets: IUpdateTarget[] = [];
        this.syncAssetData('moving', updateTargets);
        const currentPage = this.pageContainerMap[this.currentPage];
        if (updateTargets.length > 0) {
            // 先做超出边界判断，如果超出边界，删除元素
            currentPage.page.pageType !== 'board' && this.getAssetOverPage(updateTargets, opt);
            let func_name = 'DRAP_ASSET_END';
            if (opt.transform?.corner == 'mtr2') {
                func_name = 'MOVE_ASSET_END';
            }
            this.actions.onUpdateAssets(func_name, updateTargets);
        }
        // 拖动超出判断
        currentPage.page.pageType !== 'board' && this.getAssetTransformEndPage(updateTargets, opt);
        this.startMovingTime = 0;
        this.auxiliaryLine.hide();
        this.intervalLine.end();
        this.actions.assetEditEnd();
    }

    protected onRotateEnd() {
        this.rotating = false;
        const updateTargets: IUpdateTarget[] = [];
        this.syncAssetData('rotating', updateTargets);
        if (updateTargets.length > 0) {
            this.actions.onUpdateAssets('UPDATE_ROTATE', updateTargets);
        }
        this.actions.assetEditEnd();
    }

    protected onScaleEnd() {
        this.scaling = false;
        const updateTargets: IUpdateTarget[] = [];
        this.syncAssetData('scaling', updateTargets);
        const selecttion = this.canvas.getActiveObject();
        if (selecttion?.get('type') === 'activeSelection') {
            selecttion.setOptions({
                width: selecttion.getScaledWidth(),
                height: selecttion.getScaledHeight(),
                scaleX: 1,
                scaleY: 1,
            });
        }
        if (updateTargets.length > 0) {
            this.actions.onUpdateAssets('DRAG_POINT', updateTargets);
        }
        this.auxiliaryLine.hide();
        this.intervalLine.end();
        this.actions.assetEditEnd();
    }

    protected setSelectedAssetActionState(key: 'moving' | 'rotating' | 'scaling', value: boolean) {
        if (value) {
            this.actions.onShowrtcutMenu(false);
        }
        const pageMap = this.getModePage();
        const page = pageMap[this.currentPage] || pageMap[0];
        this.selectClassNames.forEach((className) => {
            if (page.assetsMap[className]) {
                const a = page.assetsMap[className];
                a.setActionSate(key, value);
            }
        });
        // this.selectClassNames.forEach((className) => {
        //     const a = this.page.assetsMap[className];
        //     a.setActionSate(key, value);
        // });
    }

    protected getPageAssetByClassName(className: string) {
        if (!className) return;
        let selectAsset!: TCanvasAsset;
        const pageMap = this.getModePage();
        const page = pageMap[this.currentPage] || pageMap[0];
        if (page?.assetsMap[className]) {
            selectAsset = page.assetsMap[className];
        }
        if (!selectAsset) {
            for (const key in pageMap) {
                if (pageMap[key].assetsMap[className]) {
                    selectAsset = pageMap[key].assetsMap[className];
                    break;
                }
            }
        }
        return selectAsset;
    }

    protected getPageAssetByUniqueId(id: string) {
        let selectAsset!: TCanvasAsset;
        const pageMap = this.getModePage();
        const page = pageMap[this.currentPage] || pageMap[0];
        const assets = page.assets || [];
        assets.find((asset) => {
            if (asset.meta.uniqueId === id) {
                const className = asset.meta.className;
                selectAsset = page.assetsMap[className];
                return true;
            }
        });
        return selectAsset;
    }

    protected syncAssetRelatedObjects(key: 'moving' | 'rotating' | 'scaling', opt: fabric.IEvent<MouseEvent>) {
        this.selectClassNames.forEach((className) => {
            const a = this.getPageAssetByClassName(className);

            if (a && 'updateRelatedObjects' in a && a.asset.meta.type !== 'line') {
                a?.updateRelatedObjects?.(key, opt);
            }
            // 移动相应的连接线
            this.updateLinePosition(a, key, opt);
        });
    }

    /** 更新连接线位置*/
    protected updateLinePosition(a: TCanvasAsset | undefined , key: 'moving' | 'rotating' | 'scaling', opt: fabric.IEvent<MouseEvent>) {
        if (!a) return;
        if (a?.asset.meta.linkedLineIds && a.asset.meta.type !== 'line') {
            a.target?.setCoords();
            const linkedLineIds = a.asset.meta.linkedLineIds;
            const objectUniqueId = a.asset.meta.uniqueId;
            linkedLineIds?.forEach((id) => {
                let lineClassName = a.linkMap[id];
                let lineTarget;
                // 多选中不包括
                if (!this.selectClassNames.has(lineClassName)) {
                    lineTarget = this.getPageAssetByClassName(lineClassName);
                }
                if (!lineTarget) {
                    lineTarget = this.getPageAssetByUniqueId(id);
                    lineClassName = lineTarget?.className;
                    if (!lineClassName) return;
                    a?.addLinkLineMap(id, lineClassName);
                    lineTarget?.addLinkLineMap(objectUniqueId as string, a.className);
                    if (this.selectClassNames.has(lineClassName)) return;
                } 
                (lineTarget as LineAsset)?.updatePositionByLinkObject?.(a.target as fabric.Rect, objectUniqueId);
            });
        } else if (a?.asset.meta.type === 'line') {
            // 如果只是单选或者线条的连接元素都被选中 则移动线条
            const lineAsset = a as LineAsset;
            if (this.selectClassNames.size === 1) {
                a?.updateRelatedObjects?.(key, opt);
                return;
            }
            const { startObj, endObj } = lineAsset.asset.attribute;
            const linkArr: { id: string; anchor: string }[] = [];
            const linkClassNames: string[] = [];
            
            [startObj, endObj].forEach((obj) => {
                if (!obj?.id) return;
                const assetClassName = a.linkMap[obj.id] ?? this.getPageAssetByUniqueId(obj.id)?.className;
                if (!assetClassName) return;
                linkClassNames.push(assetClassName);
                a?.addLinkLineMap(obj.id, assetClassName);
                this.getPageAssetByUniqueId(obj.id)?.addLinkLineMap(obj.id, a.className);
                linkArr.push(obj);
            });
            
            if (linkClassNames?.every((item) => this.selectClassNames.has(item))) {
                a?.updateRelatedObjects?.(key, opt);
                return;
            }
            // 判断连接线是否连接元素，连接的元素是否也被选中，如果没有选中，那么就更新连接线的位置
            linkArr?.forEach((item) => {
                if (item && item.id) {
                    const className = a.linkMap[item.id];
                    if (className && this.selectClassNames.has(className)) {
                        const asset = this.getPageAssetByClassName(className);
                        lineAsset?.updatePositionByLinkObject?.(asset?.target as fabric.Rect, item.id);
                    }
                }
            })
        }
    }

    protected getBoundingRect() {
        const target = this.canvas.getActiveObject();
        const matrix = target?.calcTransformMatrix(false) as number[];
        if (matrix) {
            const transformInfo = fabric.util.qrDecompose(matrix);
            const height = (target?.height as number) * transformInfo.scaleY;
            // const width = (target?.width as number) * transformInfo.scaleX;
            // const cx = transformInfo.translateX;
            const cy = transformInfo.translateY;
            const y = cy - height / 2;
            return { y, height };
        }
        return { y: 0, height: 0 };
    }

    protected getAssetOverPage(updateTargets: IUpdateTarget[], opt: fabric.IEvent<MouseEvent>) {
        // 单选
        const element = this.canvas.getActiveObject();
        if (element) {
            const isIntersecting = this.isIntersectingWithPages(element);
            if (!isIntersecting) {
                if (element.name && element.data?.className) {
                    this.selectClassNames.delete(element.data.className);
                }
                this.actions?.onDelAssets({ origin: '' });
                updateTargets = [];
            }
        }
    }

    /** 获取 transform 结束松开鼠标后元素所属页面 */
    protected getAssetTransformEndPage(updateTargets: IUpdateTarget[], opt: fabric.IEvent<MouseEvent>) {
        if (updateTargets.length === 0) return;
        const zoom = this.canvas.getZoom();
        const pageMap = this.getModePage();
        const page = pageMap[this.currentPage] || pageMap[0];
        const top = page.page?.top as number;
        const list: { index: number; page_num: number; pre_page_num: number; posY: number }[] = [];
        // 拖动换页，要根据鼠标的位置来判断是否换页
        let newPage: TgsCanvasPage[] = [];
        const { isInPage, pageIndex } = this.isPointInPage(opt.pointer as fabric.Point);
        if (isInPage && pageIndex !== undefined && pageIndex !== this.currentPage) {
            newPage.push(pageMap[pageIndex]);
        }
        // 鼠标在两个页面之间 ，那就判定元素所在的页面位置
        if (!newPage.length) {
            const { height = 0, y = 0 } = this.getBoundingRect();
            const pageBottom = top + (page.pageConfig?.height as number) || 0;
            const elementBottom = y + height;
            const pageGap = this.baseGap / zoom;
            if (y > pageBottom) {
                // 超出页面底部， 判断挪动到了下面哪一个页面
                newPage = Object.values(pageMap).filter((item) => {
                    const { pageConfig, page } = item;
                    let { top: pageTop = 0 } = page;
                    pageTop = pageTop > 0 ? pageTop - pageGap : pageTop;
                    return pageTop <= y && y < pageTop + pageConfig.height;
                });
            } else if (elementBottom < top) {
                // 超出页面顶部， 判断挪动到了上面哪一个页面
                newPage = Object.values(pageMap).filter((item) => {
                    const { pageConfig, page } = item;
                    const { top: pageTop = 0 } = page;
                    return pageTop < elementBottom && elementBottom < pageTop + pageConfig.height + pageGap;
                });
            }
        }

        if (newPage.length && updateTargets.length) {
            const { top: newTop = 0 } = newPage[0].page;
            const index = newPage[0].pageConfig.pageIndex;
            updateTargets.forEach((target) => {
                let posY = target.changes?.transform?.posY || 0;
                posY = posY + top - newTop;
                list.push({
                    index: target.index,
                    pre_page_num: this.currentPage,
                    page_num: index,
                    posY,
                });
            });
            if (list.length) {
                const movedPage = newPage[0];
                const pageNum = list[0].page_num;
                const selectInfo: {
                    asset_index: number;
                    className: string;
                    page_num?: number;
                }[] = [];
                const assetLen = movedPage.assets?.length || 0;
                Array.from(this.selectClassNames).forEach((className, index) => {
                    if (page?.assetsMap[className]) {
                        movedPage.assetsMap[className] = page.assetsMap[className];
                        movedPage.pageAssetsMap[className] = page.pageAssetsMap[className];
                        movedPage.assetsMap[className].page = movedPage;
                        movedPage.assetsMap[className].setClipPath(movedPage);
                        movedPage.assetsMap[className].setAssetScaleCoordinate(pageNum);
                        delete page.assetsMap[className];
                        delete page.pageAssetsMap[className];
                        selectInfo.push({
                            asset_index: assetLen + Number(index),
                            className,
                            page_num: pageNum,
                        });
                    }
                });
                this.currentPage = pageNum;
                this.actions.updateAssetPage(list);
                this.actions.onUpdateSelectedPage({ pageNumber: pageNum });
                this.actions.onSelectAssets(selectInfo);
                // this.syncSelect(opt)
            }
        }
    }

    protected syncAssetData(key: 'moving' | 'rotating' | 'scaling', updateTargets: IUpdateTarget[]) {
        this.selectClassNames.forEach((className) => {
            const a = this.getPageAssetByClassName(className);
            if (!a) {
                return;
            }
            a?.setActionSate(key, false);

            // 连接线变化数据走自己的一套流程
            const isLineAsset = a.asset.meta.type === 'line';
            if (isLineAsset) {
                return (a as LineAsset)?.getLineTransformSize(key, updateTargets, Array.from(this.selectClassNames));
            }

            const { left, top, width, height, angle, offsetX, offsetY } = a?.getTransformEndSize();
            let changes: Tgs.DeepPartial<Tgs.IAsset<Tgs.TAssetType>> = {};
            switch (key) {
                case 'moving': {
                    changes = {
                        transform: {
                            posX: left - offsetX,
                            posY: top - offsetY,
                        },
                    };
                    break;
                }
                case 'rotating': {
                    const x = left - offsetX;
                    const y = top - offsetY;
                    changes = {
                        transform: {
                            posX: x,
                            posY: y,
                            rotate: Math.round(angle),
                        },
                    };
                    break;
                }
                case 'scaling': {
                    changes = {
                        attribute: {
                            width,
                            height,
                        },
                        transform: {
                            posX: left - offsetX,
                            posY: top - offsetY,
                        },
                    };
                    if (a.asset.meta.type === 'text' || a.asset.meta.type === 'table') {
                        changes.attribute = (a as TextAsset).scaleFont(width, height);
                    } else if (['image', 'pic', 'background'].includes(a.asset.meta.type)) {
                        changes.attribute = (a as ImageAsset).scaleImage(width, height, angle);
                    } 
                    break;
                }
            }
        
            updateTargets.push({
                index: a.index,
                className: a.className,
                pageIndex: a.pageIndex,
                changes,
            });
            if (a.type === 'group') {
                let groupMembersUpdates;
                if (key == 'moving') {
                    const groupOffestX = left - offsetX - a.asset.transform.posX;
                    const groupOffestY = top - offsetY - a.asset.transform.posY;
                    groupMembersUpdates = a.updateGroupMembers(key, width, height, groupOffestX, groupOffestY);
                } else {
                    groupMembersUpdates = a.updateGroupMembers(key, width, height);
                }
                if (groupMembersUpdates.length > 0) {
                    updateTargets.push(...groupMembersUpdates);
                }
            } else if (a.asset.meta?.linkedLineIds?.length) {
                const linkedLineIds = a.asset.meta.linkedLineIds.filter((lineId) => a.linkMap[lineId]);
                linkedLineIds.forEach((lineId) => {
                    const lineClassName = a.linkMap[lineId];
                    const lineAsset = this.getPageAssetByClassName(lineClassName) as LineAsset;
                    // 已经选择的元素不包含这条线
                    if (lineAsset && !this.selectClassNames.has(lineClassName)) {
                        const lineChange = lineAsset?.onMouseUpLineChange(false);
                        updateTargets.push(lineChange);
                    }
                });
                // 去掉不存在的lineId
                if (linkedLineIds.length && linkedLineIds !== a.asset.meta.linkedLineIds) {
                    if (!changes.meta) {
                        changes.meta = {};
                    }
                    changes.meta.linkedLineIds = linkedLineIds;
                }
            }
        });
    }

    /** 设置页面中的元素为可选状态， 没选中的页面是不可选择 */
    protected setPageAssetIsSelected(pageNumber: number) {
        const pageMap = this.getModePage();
        for (const index in pageMap) {
            const page = pageMap[index];
            const pageAsset = page.assetsMap;
            const pageIndex = page.pageConfig.pageIndex;
            for (const key in pageAsset) {
                const item = pageAsset[key];
                const flag = pageIndex === pageNumber;
                // 背景图片不能选中
                if (item.asset.meta.type === 'background' && page.page?.pageType !== 'board') {
                    item.setControllable(false, flag);
                } else {
                    item.setControllable(flag, flag);
                }
            }
        }
    }
    /** 取消选择 以及拖动判断元素是否超出画布 超出画布删除元素 */
    protected isAllAssetsInPage() {
        const pageMap = this.getModePage();
        const page = pageMap[this.currentPage] || pageMap[0];
        const size = this.selectClassNames.size;
        if (size > 0 && page.page.pageType !== 'board') {
            const asset_index_list: {
                index: number;
                className?: string;
                page_num?: number;
            }[] = [];
            this.selectClassNames.forEach((className) => {
                if (page?.assetsMap[className]) {
                    const item = page?.assetsMap[className].target;
                    if (item) {
                        const isIntersecting = this.isIntersectingWithPages(item);
                        if (!isIntersecting) {
                            asset_index_list.push({
                                index: page.assetsMap[className].index,
                                className: className,
                                page_num: page.pageConfig.pageIndex,
                            });
                        }
                    }
                }
            });
            asset_index_list?.length &&
                this.actions?.onDelAssets({
                    asset_index_list,
                    origin: '',
                });
            return false;
        }
        return true;
    }
    /** 排除鼠标在画布外选择的元素 */
    overPageDeselectedAsset(opt: fabric.IEvent<MouseEvent>) {
        return false;
        // 判断初始点击坐标和结束坐标，如果两个坐标只要有一个在画布内，那么就选中元素
        // 如果两个坐标都不在画布内，判断两个坐标画布的上下左右
        if (!opt.e) return false;
        const upPointer = { x: opt.e.offsetX, y: opt.e.offsetY };
        const { isInPage: isUpInPage } = this.isPointInPage(upPointer as fabric.Point);
        if (this.downPointer) {
            const { pointer, isInPage } = this.downPointer;
            if (isInPage || isUpInPage) {
                return false;
            } else {
                const { x = 0, y = 0, width, height, scale } = this.canvasInfo;
                const canvasRight = x + width * scale;
                const len = Object.keys(this.pageContainerMap).length;
                let canvasBottom = 0;
                if (this.renderMode === 'pull' || this.renderMode === 'board') {
                    canvasBottom = y + height * scale;
                } else {
                    canvasBottom = y + height * len * scale + this.baseGap * (len - 1);
                }
                let left, right, top, bottom;
                if (pointer.x > upPointer.x) {
                    left = upPointer.x;
                    right = pointer.x;
                } else {
                    left = pointer.x;
                    right = upPointer.x;
                }

                if (pointer.y > upPointer.y) {
                    top = upPointer.y;
                    bottom = pointer.y;
                } else {
                    top = pointer.y;
                    bottom = upPointer.y;
                }

                if (right < x || left > canvasRight || bottom < y || top > canvasBottom) {
                    this.canvas.discardActiveObject();
                    this.canvas.requestRenderAll();
                    return true;
                }
            }
        }
        return false;
    }
    protected handleGroupSubTargetSelect(groupTarget: fabric.Group, subSelectTarget?: fabric.Object) {
        const selectInfo: {
            asset_index: number;
            className: string;
            page_num?: number | undefined;
        }[] = [];
        const pageMap = this.getModePage();
        const page = pageMap[this.currentPage] || pageMap[0];
        const className = subSelectTarget?.data.className;
        const subTargetAsset = page.assetsMap[className]?.asset;
        if (groupTarget?.data?.hasAssetMoving) return;
        if (subSelectTarget && subTargetAsset) {
            this.hideHelperRectTarget();
            this.addHelperRectTarget(subTargetAsset);
            subSelectTarget.data.groupHelperRectTarget = this.groupHelperRectTarget;
            groupTarget.data.subSelectTarget = subSelectTarget;
            // 避免鼠标按下快速拖动时出现闪动问题
            setTimeout(() => {
                if (!this.moving) {
                    selectInfo.push({
                        asset_index: page.assetsMap[className].index,
                        className,
                        page_num: page.pageConfig.pageIndex,
                    });
                    this.actions.onSelectAssets(selectInfo);
                    this.actions.onGroupEdit('select');
                }
            }, 30);
            // }
            // else{
            //     this.hideHelperRectTarget()
            //     const className = groupTarget.data.className;
            //     // this.hideHelperRectTarget();
            //     selectInfo.push({
            //         asset_index: page.assetsMap[className].index,
            //         className,
            //         page_num: page.pageConfig.pageIndex,
            //     });
            //     groupTarget.data.groupHelperRectTarget = this.groupHelperRectTarget;
            //     this.actions.onSelectAssets(selectInfo);
            // }
            // this.actions.onGroupAssetEdit('edit')
        } else {
            const className = groupTarget.data.className;
            // this.hideHelperRectTarget();
            selectInfo.push({
                asset_index: page.assetsMap[className].index,
                className,
                page_num: page.pageConfig.pageIndex,
            });
            groupTarget.data.groupHelperRectTarget = this.groupHelperRectTarget;
            groupTarget.data.subSelectTarget = null;
            this.actions.onSelectAssets(selectInfo);
        }
        this.canvas.requestRenderAll();
    }
    // 画布内辅助展示矩形框
    protected addHelperRectTarget<T extends Tgs.TAssetType>(addAsset: Tgs.IAsset<T>) {
        if (!this.groupHelperRectTarget) {
            this.groupHelperRectTarget = new fabric.Rect();
        }
        const { attribute, transform } = addAsset;
        const pageMap = this.getModePage();
        const page = pageMap[this.currentPage] || pageMap[0];
        const { top: pTop = 0, left: pLeft = 0 } = page.page;
        const strokeWidth = 2 / this.canvas.getZoom();
        const x = transform.posX + (attribute.width - 2) / 2 + pLeft;
        const y = transform.posY + (attribute.height - 2) / 2 + pTop;
        this.groupHelperRectTarget.setOptions({
            fill: 'transparent',
            name: 'renderRect',
            width: attribute.width,
            height: attribute.height,
            scaleX: 1,
            scaleY: 1,
            stroke: '#EF3964',
            strokeWidth: strokeWidth,
            evented: false,
            selectable: false,
        });
        this.groupHelperRectTarget.bringToFront();
        this.groupHelperRectTarget?.rotate(0);
        this.groupHelperRectTarget.setPositionByOrigin({ x, y } as fabric.Point, 'center', 'center');
        this.groupHelperRectTarget?.rotate(transform.rotate);
        if (this.helperRectTargetStatus == 'remove') this.canvas.add(this.groupHelperRectTarget);
        this.canvas.requestRenderAll();
        this.helperRectTargetStatus = 'show';
    }
    protected hideHelperRectTarget(remove?: boolean) {
        if (!this.groupHelperRectTarget) return;
        if (remove) {
            this.canvas.remove(this.groupHelperRectTarget);
            this.helperRectTargetStatus = 'remove';
        } else {
            this.groupHelperRectTarget.setOptions({
                stroke: 'transparent',
                strokeWidth: 0,
            });
        }
        this.helperRectTargetStatus = 'hide';
    }
    /** 横向移动*/
    protected onHorizontalMove(deltaX: number, transform: number[]) {
        if (!deltaX) return false;
        deltaX = -deltaX;
        const zoom = this.canvas.getZoom();
        const left= Number((this.canvas as any)?.lowerCanvasEl?.dataset.left) || 0;
        const right = Number((this.canvas as any)?.lowerCanvasEl?.dataset.right) || 0;
        const padding = 60;
        const canvasWidth =  this.canvas.getWidth() - left - right;
        const distance = (deltaX / 2) * 1.3;
        const scrollX = transform[4] + distance;
        const pageWidth = this.canvasInfo.width * zoom;
        //&& scrollX < 0 && scrollX + pageWidth > canvasWidth
        // deltax < 0 向右滑动 deltax > 0 向左滑动
        if (canvasWidth < pageWidth) {
            transform[4] = scrollX;
            if (this.renderMode !== 'board') {
                if (scrollX > (padding + left) && deltaX > 0) {
                    transform[4] = (padding + left);
                } else if (scrollX + pageWidth - left < canvasWidth - padding && deltaX < 0) {
                    transform[4] = canvasWidth - padding - pageWidth + left - 15;
                }
            }
            this.canvas.setViewportTransform(transform);
            this.canvasInfo.x = transform?.[4];
            // TODO 有 dom 依赖坐标，待改进性能
            this.actions.onUpdateCanvasInfo({
                x: transform?.[4],
            });
            return true;
        }
        return false;
    }
    /** 设置缩放以及选中控制器corner隐藏以及显示*/
    protected setCornerScaleViability(opt?: fabric.IEvent<MouseEvent>) {
        const corner = opt?.transform?.corner as string;
        const target = opt?.target || (opt?.selected?.[0] as fabric.Object);
        if (this.scaling && target) {
            const isImageClipping = target?.data?.imageClipping;
            if (isImageClipping) return;
            this.setCornerViability(target, [corner]);
        } else {
            const selecttion = this.canvas.getActiveObject();
            const isImageClipping = selecttion?.data?.imageClipping;
            if (isImageClipping) return;
            if (selecttion) {
                switch (selecttion?.data?.type) {
                    case 'text':
                        const cornerArr = ['tl', 'tr', 'bl', 'br'];
                        const targetAsset = this.getPageAssetByClassName(selecttion?.data?.className) as TextAsset;
                        if (targetAsset.asset.attribute.writingMode == 'vertical-rl') {
                            cornerArr.push('mt', 'mb');
                        } else {
                            cornerArr.push('ml', 'mr');
                        }
                        this.setCornerViability(selecttion, cornerArr);

                        break;
                    case 'group':
                    case 'frameClip':
                    case 'frame':
                        this.setCornerViability(selecttion, ['tl', 'tr', 'bl', 'br']);
                        break;
                    case 'line':
                        this.setCornerViability(selecttion, []);
                        break;
                    case 'SVG':
                        this.setCornerViability(selecttion, ['tl', 'tr', 'bl', 'br', 'mr', 'ml', 'mb', 'mt', 'mtl', 'mbl', 'mll', 'mrl']);
                        break;
                    default:
                        const activeLength = this.canvas.getActiveObjects()?.length;
                        if (activeLength && activeLength > 1) {
                            this.setCornerViability(selecttion, ['tl', 'tr', 'bl', 'br']);
                        } else {
                            this.setCornerViability(selecttion, ['tl', 'tr', 'bl', 'br', 'mr', 'ml', 'mb', 'mt']);
                        }

                        break;
                }
            }
        }
    }
    protected setCornerViability(target: fabric.Object, corner: string[]) {
        const options = {
            mt: false,
            mb: false,
            ml: false,
            mr: false,
            tl: false,
            tr: false,
            bl: false,
            br: false,
            mtl: false,
            mbl: false,
            mll: false,
            mrl: false,
        };
        corner.forEach((item) => (options[item as keyof typeof options] = true));
        target?.setControlsVisibility(options);
    }

    /** 更新页面中有些元素需要缩放后的大小 比如表格的边框 */
    protected updateAsssetScale() {
        for (const index in this.pageContainerMap) {
            const item = this.pageContainerMap[index];
            item.updateAssetScaleParams();
        }
    }

    protected assetEditing(type: string) {
        const targets: any[] = [];
        this.selectClassNames.forEach((className) => {
            const a = this.getPageAssetByClassName(className);
            if (!a) {
                return;
            }
            targets.push({
                className,
                transformAndSize: a.getTransformEndSize(),
            });
        });
        this.actions.assetEditing(targets, type);
    }

    /** 是否按下ctrl shift command */
    protected multiFunctionKey(e: MouseEvent) {
        return e.shiftKey || e.ctrlKey || e.metaKey;
    }

    /** 设置fabric中画布的所在页码*/
    protected setFabricCanvasPageIndex(pageIndex: number | undefined) {
        this.canvas?.setPageIndex?.(pageIndex);
    }


    protected bindAddLineEffect() {
        let aa: TCanvasAsset | undefined;
        let mouseDownX = 0;
        let mouseDownY = 0;
        const calcLineSize = (startPointer: { x: number, y: number }, endPointer: { x: number, y: number }, pageTop: number) => {
            const transform = { posX: 0, posY: 0 };
            const start = { x: 0, y: 0 };
            const end = { x: 0, y: 0 };
        
            if (endPointer.x <= startPointer.x) {
                transform.posX = endPointer.x;
                start.x = Math.abs(endPointer.x - startPointer.x);
            } else {
                transform.posX = startPointer.x;
                end.x = Math.abs(endPointer.x - startPointer.x);
            }
            if (endPointer.y <= startPointer.y) {
                transform.posY = endPointer.y - pageTop;
                start.y = Math.abs(endPointer.y - startPointer.y);
            } else {
                transform.posY = startPointer.y - pageTop;
                end.y = Math.abs(endPointer.y - startPointer.y);
            }
            return { start, end, transform };
        }

        this.canvas.on('mouseDown:line:effect', (opt: any) => {
            mouseDownX = opt.x;
            mouseDownY = opt.y;
        });
        this.canvas.on('move:line:effect', (opt: any) => {
            const { anchor, target } = opt;
            if (!aa) {
                aa = this.getPageAssetByClassName(target?.data?.className);
            }
            if (!aa) return;
            this.hoverLinkLine?.show({
                target: aa,
                anchor,
                pointer: { x: opt.x, y: opt.y },
                page: this.pageContainerMap[this.currentPage],
                from: 'move:line'
            });
        });
        this.canvas.on('add:line:effect', (opt: any) => {
            aa = undefined;
            const { anchor, target } = opt;
            const optAsset = this.getPageAssetByClassName(target?.data?.className);
            if (!optAsset) return;
            this.hoverLinkLine?.show({
                target: optAsset,
                anchor,
                pointer: { x: opt.x, y: opt.y },
                page: this.pageContainerMap[this.currentPage],
            });
            const lineWidth = (optAsset as SvgAsset).asset.attribute.stroke?.width || 10;
            const uniqueId = optAsset.asset.meta.uniqueId || Util.generateShortUUID();
            const lineUniqueId = Util.generateShortUUID();
            const resId = (optAsset as SvgAsset)?.asset.attribute?.resId;
            const updateAssets: IUpdateTarget[] = [
                {
                    index: optAsset.index,
                    className: optAsset.className,
                    pageIndex: optAsset.pageIndex,
                    changes: {
                        meta: {
                            uniqueId,
                            linkedLineIds: optAsset.asset.meta.linkedLineIds
                                ? [...optAsset.asset.meta.linkedLineIds, lineUniqueId]
                                : [lineUniqueId],
                        },
                    },
                },
            ];
            const mouseDeltaX = Math.abs(mouseDownX - opt.x);
            const mouseDeltaY = Math.abs(mouseDownY - opt.y);
            const isClick = Math.sqrt(mouseDeltaX * mouseDeltaX + mouseDeltaY * mouseDeltaY) < 5;
            const pointers = optAsset.getControlPositions();
            const page = this.pageContainerMap[this.currentPage];
            const pageTop = page.page.top!;
            const eventPointer = { x: opt.x, y: opt.y };
            const isNearPointer = page.getPointerNearObject(eventPointer as fabric.Point);
            const polyPoints = this.hoverLinkLine?.points?.reverse();
            if (!isClick && isNearPointer) {
                eventPointer.x = isNearPointer.x;
                eventPointer.y = isNearPointer.y;
            }
            const centerPoint = optAsset.target!.getCenterPoint();
            let endPointer = eventPointer;
            let startPointer = { x: 0, y: 0 };
            const clickLineLength = 50 / this.canvas.getZoom();
            if (anchor === 'mtl') {
                if (isClick) {
                    endPointer = this.hoverLinkLine?.getExtendedPoint(centerPoint, pointers.mt, clickLineLength);
                }
                startPointer = pointers.mt;
            } else if (anchor === 'mbl') {
                if (isClick) {
                    endPointer = this.hoverLinkLine?.getExtendedPoint(centerPoint, pointers.mb, clickLineLength);
                }
                startPointer = pointers.mb;
            } else if (anchor === 'mll') {
                if (isClick) {
                    endPointer = this.hoverLinkLine?.getExtendedPoint(centerPoint, pointers.ml, clickLineLength);
                }
                startPointer = pointers.ml;
            } else if (anchor === 'mrl') {
                if (isClick) {
                    endPointer = this.hoverLinkLine?.getExtendedPoint(centerPoint, pointers.mr, clickLineLength);
                }
                startPointer = pointers.mr;
            }
            let endObj;
            if (!isClick && isNearPointer) {
                const startClosestObject = page.startClosestObject;
                if (startClosestObject) {
                    const closestClassName = startClosestObject.data?.className;
                    const closetAsset = page.assetsMap[closestClassName];

                    if (closetAsset) {
                        const uniqueId = closetAsset.asset.meta?.uniqueId
                            ? closetAsset.asset.meta?.uniqueId
                            : Util.generateShortUUID();
                        const closestLinkLineIds = closetAsset.asset.meta?.linkedLineIds || [];
                        const lineId = lineUniqueId;
                        if (!closestLinkLineIds.includes(lineId)) {
                            closestLinkLineIds.push(lineId);
                        }

                        endObj = {
                            id: uniqueId,
                            anchor: page.startLinkCorner ?? undefined,
                        };
                        updateAssets.push({
                            className: closestClassName,
                            index: startClosestObject.data?.index,
                            changes: {
                                meta: {
                                    uniqueId,
                                    linkedLineIds: closestLinkLineIds,
                                },
                            },
                        });
                    }
                }
            }
            this.hoverLinkLine.hide({
                page
            });
            const activeObject = this.canvas.getActiveObject();
            activeObject?.setOptions({
                data: {
                    ...activeObject.data,
                    hoverControl: undefined,
                },
            });
            // this.canvas.discardActiveObject();
            let {start, end, transform} = calcLineSize(startPointer, endPointer, pageTop);
            let copyShape;
            // 尾部没有链接元素
            if (!endObj) {
                copyShape = cloneDeep(optAsset.asset) as Tgs.IAsset<Tgs.TSvgAssetType>;
                copyShape.meta.uniqueId = Util.generateShortUUID();
                copyShape.meta.linkedLineIds = [lineUniqueId];
                copyShape.attribute.textAttr = undefined;
                const w = copyShape.attribute.width;
                const h = copyShape.attribute.height;
                let centerPointer = { x: 0, y: 0 };
                let edge: "left" | "top" | "bottom" | "right" = 'top';
                let startAnchor: 'mt' | 'mb' | 'ml' | 'mr' = 'mt';
                // 根据points的最后两个点的位置来判断方向，并且计算anchor
                if (!isClick && polyPoints && polyPoints.length > 1) {
                    const lastPoint = polyPoints[polyPoints.length - 1];
                    const preLastPoint = polyPoints[polyPoints.length - 2];
                    if (lastPoint.x === preLastPoint.x) {
                        if (lastPoint.y < preLastPoint.y) {
                            edge = 'bottom';
                            startAnchor = 'mb';
                        } else {
                            edge = 'top';
                            startAnchor = 'mt';
                        }
                    } else {
                        if (lastPoint.x < preLastPoint.x) {
                            edge = 'right';
                            startAnchor = 'mr';
                        } else {
                            edge = 'left';
                            startAnchor = 'ml';
                        }
                    }
                    endObj = { anchor: startAnchor, id: copyShape.meta.uniqueId };
                } else {
                    switch (anchor) {
                        case 'mtl':
                            endObj = { anchor: 'mb', id: copyShape.meta.uniqueId };
                            edge = 'bottom'
                            break;
                        case 'mbl':
                            endObj = { anchor: 'mt', id: copyShape.meta.uniqueId };
                            edge = 'top'
                            break;
                        case 'mll':
                            endObj = { anchor: 'mr', id: copyShape.meta.uniqueId };
                            edge = 'right'
                            break;
                        case 'mrl':
                            endObj = { anchor: 'ml', id: copyShape.meta.uniqueId };
                            edge = 'left'
                            break;
                    }
                }
                centerPointer = Util.getCenterPointFromRotatedMidpoint(endPointer, w, h, edge, copyShape.transform.rotate);
                copyShape.transform.posX = centerPointer.x - copyShape.attribute.width / 2;
                copyShape.transform.posY = centerPointer.y - copyShape.attribute.height / 2 - pageTop;
                if (isClick) {
                    const linkedLines: Tgs.IAsset<Tgs.TLineAssetType>[] = []
                    page.assets.forEach((item) => {
                        if (item.meta.type === 'line' && optAsset.asset.meta.linkedLineIds?.includes(item.meta.uniqueId)) {
                            linkedLines.push(item as Tgs.IAsset<Tgs.TLineAssetType>)
                        }
                    })
                    const linkedShapes: Tgs.IAsset<Tgs.TSvgAssetType>[] = []
                    page.assets.forEach((item) => {
                        if (item.meta.type === 'SVG' && linkedLines.some((line) => line.attribute.endObj?.id === item.meta.uniqueId)) {
                            linkedShapes.push(item as Tgs.IAsset<Tgs.TSvgAssetType>)
                        }
                    })
                    let copyShapeInfo = Util.getRotatePoints({ x: copyShape.transform.posX, y: copyShape.transform.posY, w, h, angle: copyShape.transform.rotate });
                    function checkIsOverLap(copyShapeInfo: {
                        x: number;
                        y: number;
                    }[]) {
                        let isOverLap = false
                        for (let i = 0; i < linkedShapes.length; i++) {
                            const shape = linkedShapes[i]
                            const shapeInfo = {
                                x: shape.transform.posX,
                                y: shape.transform.posY,
                                w: shape.attribute.width,
                                h: shape.attribute.height,
                                cx: shape.transform.posX + shape.attribute.width / 2,
                                cy: shape.transform.posY + shape.attribute.height / 2,
                                angle: shape.transform.rotate,
                            }
                            const isPolygonOverlap = Util.isPolygonOverlap(copyShapeInfo, Util.getRotatePoints(shapeInfo))
                            if (isPolygonOverlap) {
                                isOverLap = true
                                break;
                            }
                        }
                        return isOverLap
                    }
                    const midPointer = { x: endPointer.x, y: endPointer.y }
                    const center = {x: centerPointer.x, y: centerPointer.y}
                    let tryCount = 1
                    while (checkIsOverLap(copyShapeInfo)) {
                        const direction = tryCount % 2 === 0 ? 1 : -1
                        if (anchor === 'mtl' || anchor === 'mbl') {
                            // endPointer 和 centerPointer 连线的垂直方向上基于 centerPointer 移动中心点，并且考虑旋转角度，在垂直线上移动 width + 20 的距离
                            centerPointer = Util.getNewCenterPoint(center, midPointer, direction * Math.floor(tryCount / 2) * (w + 20));
                        } else {
                            centerPointer = Util.getNewCenterPoint(center, midPointer, direction * Math.floor(tryCount / 2) * (h + 20));
                        }
                        endPointer = Util.getNewMidPoint(center, centerPointer, midPointer);
                        ({start, end, transform} = calcLineSize(startPointer, endPointer, pageTop));
                        copyShape.transform.posX = centerPointer.x - copyShape.attribute.width / 2;
                        copyShape.transform.posY = centerPointer.y - copyShape.attribute.height / 2 - pageTop;
                        copyShapeInfo = Util.getRotatePoints({
                            x: copyShape.transform.posX,
                            y: copyShape.transform.posY,
                            w,
                            h,
                            angle: copyShape.transform.rotate,
                        });
                        tryCount++
                    }
                }
            }

            this.actions.addLineEffect({
                fromCanvas: true,
                id: 1,
                width: Math.max(Math.abs(start.x - end.x), 1),
                height: Math.max(Math.abs(start.y - end.y), 1),
                meta: {
                    uniqueId: lineUniqueId,
                },
                resId,
                attribute: {
                    endObj,
                    startObj: { anchor: (anchor as string).substring(0, 2), id: uniqueId },
                    start,
                    end,
                    startArrow: 'line',
                    endArrow: 'arrow',
                    points: !isClick ? polyPoints : null,
                    lineWidth
                },
                transform,
                copyShape,
            }, isClick ? 'click' : 'drag');
            this.actions.onUpdateAssets('UPDATE_LINE_LINK', updateAssets);
        });
    }
}
