import { fabric } from 'fabric';
import equal from 'fast-deep-equal';
import { throttle } from 'lodash-es';
import { Actions } from '../../../actions';
import { Util } from '../../../util';
import { AuxiliaryLine } from '../../AuxiliaryLine';
import { IntervalLine } from '../../IntervalLine';
import { RenderZIndex } from '../../RenderZIndex';
import { customControls } from '../customControls';
import { HoverLinkLine } from '../../HoverLinkLine'
import { TgsCanvasPage, TgsCanvasPageRenderOnly } from '../Page';

const dotSvg = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDQwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxjaXJjbGUgY3g9IjIwMCIgY3k9IjIwMCIgcj0iMzAiIGZpbGw9IiNDQ0NDQ0MiLz4KPC9zdmc+Cg=='

interface IPageContainerMap {
    [key: string | number]: TgsCanvasPage | TgsCanvasPageRenderOnly;
}

/** 整个画布类, 基类只处理画布自身信息及基础功能 */
export class TgsCanvasBase {
    static util = Util;

    actions = new Actions();
    renderZIndex;
    auxiliaryLine: AuxiliaryLine;
    intervalLine: IntervalLine;
    // hover展示链接线
    hoverLinkLine: HoverLinkLine;
    // 页面区域 所有页面里面通过fabric创建的元素都需要添加clipPath:page
    // page!: TgsCanvasPage;
    // 全屏画布
    canvas!: fabric.Canvas;
    pageContainerMap: IPageContainerMap = {};
    //string Map<string | number, TgsCanvasPage> = new Map();
    currentPage = 0;
    // 每个页面的Y方向的间距
    baseGap = 60;

    renderMode = '';
    readOnly = false;
    // 拉起状态 单独的展示页
    filmStripPageMap: IPageContainerMap = {};

    canvasInfo: Tgs.ICanvas = {
        width: 1000,
        height: 1000,
        x: 0,
        y: 0,
        scale: 1,
    };

    // events control
    moving = false;
    declare startMovingTime: number;
    rotating = false;
    scaling = false;
    selectClassNames = new Set<string>();
    isActivePage = true;
    hoveringAsset: fabric.Object | undefined = undefined;
    groupHelperRectTarget: fabric.Rect | undefined = undefined;
    helperRectTargetStatus = 'remove';
    defaultBackgroundColor = '#F1F3F7';
    defaultInterative = true;
    lastSubSelectTarget: fabric.Object | undefined = undefined;
    updateActiveSelection = false;
    protected viewportAnimateInfo: { x: number; y: number } | undefined = undefined;
    protected viewportAnimating = false;
    protected viewportAnimate: any = undefined;

    protected isSyncSelect = false;
    //
    protected _syncSelectTimer: number | undefined = undefined;
    protected _onWheelScaleTimer: number | undefined = undefined;
    protected _onWheelScrollYTimer: number | undefined = undefined;

    protected stashGroupMovingBoundaryInfo: {
        top: number | undefined;
        left: number | undefined;
        width?: number | undefined;
        height?: number | undefined;
        angle?: number | undefined;
    } = { top: undefined, left: undefined, width: undefined, height: undefined, angle: undefined };
    _onWheelChangePageTimer = throttle(
        (delta) => {
            const index = delta > 0 ? this.currentPage + 1 : this.currentPage - 1;
            if (index >= 0 && index < this.pageCount) {
                if (this.currentPage !== index) {
                    this.canvas.discardActiveObject();
                }
                this.currentPage = index;
                this.actions.onUpdateSelectedPage({ pageNumber: index });
            }
        },
        250,
        { trailing: false },
    );
    protected initTime: number;

    protected showWaterMark: boolean;

    protected dotPattern = new fabric.Pattern({
        source: dotSvg,
        repeat: 'repeat', // 重复填充
        patternTransform: [0.055, 0, 0, 0.055, 0, 0], // 缩放图案
        offsetX: -11,
        offsetY: -11,
    });

    constructor(param: { element: string | HTMLCanvasElement | null; options?: fabric.ICanvasOptions }) {
        this.initTime = new Date().getTime();
        // 限制缩放，禁止翻转缩放
        fabric.Object.prototype.lockScalingFlip = true;
        fabric.Object.prototype.minScaleLimit = 0.01;
        customControls();
        this.canvas = new fabric.Canvas(param.element, {
            // ...param.options,
            width: Math.ceil((param.options?.width ?? 1000) / 20) * 20,
            height: Math.ceil((param.options?.height ?? 1000) / 20) * 20,
            backgroundColor: param.options?.backgroundColor ?? this.defaultBackgroundColor,
            renderOnAddRemove: false,
            // 保存操作元素时 层级不变
            preserveObjectStacking: true,
            fireRightClick: true,
            stopContextMenu: true,
            selectionKey: ['shiftKey', 'ctrlKey', 'metaKey'],
            uniScaleKey: undefined, // 禁止按住 shift 解锁从等比缩放变成自由缩放
            interactive: param.options?.interactive ?? this.defaultInterative,
            skipOffscreen: true,
            enableRetinaScaling: param.options?.enableRetinaScaling !== undefined ? param.options?.enableRetinaScaling : true,
            targetFindTolerance: 10,
        });
        this.renderZIndex = new RenderZIndex(this.canvas, this.actions);
        this.auxiliaryLine = new AuxiliaryLine(this.canvas, this.renderZIndex);
        this.intervalLine = new IntervalLine(this.canvas, this.renderZIndex);
        this.hoverLinkLine = new HoverLinkLine(this.canvas, this.renderZIndex);
        this.showWaterMark = false;
        console.log(this)
    }
    get pageCount() {
        return Object.keys(this.pageContainerMap).length || 1;
    }
    // 设置页面居中及其初始化缩放标准
    protected setPagePosition(canvas: Tgs.ICanvas, withAnimate = false) {
        const transform = this.canvas.viewportTransform?.slice() as number[];
        transform[4] = canvas.x ?? transform[4];
        transform[5] = canvas.y ?? transform[5];
        if (this.viewportAnimating) {
            if (equal(this.viewportAnimateInfo, { x: canvas.x, y: canvas.y })) {
                return;
            } else {
                withAnimate = true;
            }
        }
        if (withAnimate) {
            if (this.viewportAnimate && this.viewportAnimating) {
                fabric.util.cancelAnimFrame(this.viewportAnimate);
            }
            this.viewportAnimating = true;
            this.viewportAnimateInfo = { x: canvas.x as number, y: canvas.y as number };
            const transform = this.canvas.viewportTransform?.slice() as number[];
            transform[4] = canvas.x ?? transform[4];
            transform[5] = canvas.y ?? transform[5];
            this.viewportAnimate = fabric.util.animate({
                startValue: this.canvas.viewportTransform?.slice() as unknown as number,
                endValue: transform as unknown as number,
                duration: 175,
                easing: Util.cssEase.linear,
                onChange: (value) => {
                    this.canvas.setViewportTransform(value as unknown as number[]);
                    this.canvas.requestRenderAll();
                },
                onComplete: () => {
                    this.viewportAnimating = false;
                    this.canvas.requestRenderAll();
                },
            });
        } else {
            this.canvas.setViewportTransform(transform);
        }
    }

    protected centerPage(page: fabric.Object) {
        if (!page) return;
        const canvas = this.canvas;
        const zoom = canvas.getZoom();
        const pageTop = page.top as number;
        const pageHeight = page.height as number;
        // 移动画布视口，以达到让元素居中的效果
        const viewportTransform = (canvas.viewportTransform as number[]).slice();
        const canvasHeight = this.renderMode === '' ? canvas.getHeight() : canvas.getHeight() - 60;
        viewportTransform[4] = (canvas.getWidth() - ((page.left as number) + (page.width as number)) * zoom) / 2;
        viewportTransform[5] = (canvasHeight - pageHeight * zoom) / 2 - pageTop * zoom;
        canvas.setViewportTransform(viewportTransform);
        this.syncCanvasInfo();
    }
    /* 判断页面是否超出视口 */
    protected updatePagePosition(page: fabric.Object, isForce?: boolean) {
        if (isForce) {
            this.centerPage(page);
        } else {
            // 判断页面是否处于视口当中如果是则不做处理
            const canvas = this.canvas;
            const zoom = canvas.getZoom();
            const pageTop = (page.top as number) * zoom;
            const viewportTransform = (canvas.viewportTransform as number[]).slice();
            const canvasHeight = this.renderMode === '' ? canvas.getHeight() : canvas.getHeight() - 200;
            const tranY = viewportTransform[5] < 0 ? Math.abs(viewportTransform[5]) : 0;

            if (tranY + canvasHeight < pageTop) {
                this.centerPage(page);
            }
        }
    }
    /* 添加页面元素动画 */
    protected centerAddPageAnim(page: fabric.Object) {
        if (!page || this.pageCount === 1) return;
        if (this.renderMode === 'pull' || this.renderMode === 'board') {
            return;
        }
        const canvas = this.canvas;
        const zoom = canvas.getZoom();

        // 移动画布视口，以达到让元素居中的效果
        const viewportTransform = (canvas.viewportTransform as number[]).slice();
        const startTransform = (canvas.viewportTransform as number[]).slice();
        // const canvasHeight = this.renderMode === '' ? canvas.getHeight() : canvas.getHeight() - 200;
        // viewportTransform[4] = (canvas.getWidth() - ((page.left as number) + (page.width as number)) * zoom) / 2;
        viewportTransform[5] -= (page.height as number) * zoom + this.baseGap;
        // canvas.setViewportTransform(viewportTransform);

        // 定义动画的持续时间和缓动函数
        const duration = 300; // 0.3秒
        // 执行动画
        fabric.util.animate({
            startValue: startTransform as unknown as number,
            endValue: viewportTransform as unknown as number,
            duration: duration,
            onChange: (value) => {
                canvas.setViewportTransform(value as unknown as number[]);
                canvas.requestRenderAll();
                this.syncCanvasInfo();
            },
            onComplete: () => {
                this.syncCanvasInfo();
            },
        });
    }
    /* 删除页面元素动画 */
    protected centerDelPageAnim(page: fabric.Object, pageLength: number, direction: string) {
        if (!page) return;
        if (this.renderMode === 'pull' || this.renderMode === 'board') {
            return;
        }
        this.actions.changeAddPageBtnStatus(false);
        const canvas = this.canvas;
        const zoom = canvas.getZoom();

        // 移动画布视口，以达到让元素居中的效果
        const viewportTransform = (canvas.viewportTransform as number[]).slice();
        const startTransform = (canvas.viewportTransform as number[]).slice();
        const pageHeight = (page.height as number) * zoom;
        const pageTop = (page.top as number) * zoom;
        if (pageLength === 1) {
            viewportTransform[5] = (canvas.getHeight() - pageHeight) / 2;
        } else {
            if (direction === 'up') {
                viewportTransform[5] += Math.abs(viewportTransform[5]) - pageTop + this.baseGap;
            }
        }

        // 定义动画的持续时间和缓动函数
        const duration = 300; // 0.3秒

        // 执行动画
        fabric.util.animate({
            startValue: startTransform as unknown as number,
            endValue: viewportTransform as unknown as number,
            duration: duration,
            onChange: (value) => {
                canvas.setViewportTransform(value as unknown as number[]);
                canvas.requestRenderAll();
                this.syncCanvasInfo();
            },
            onComplete: () => {
                this.syncCanvasInfo();
                this.actions.changeAddPageBtnStatus(true);
            },
        });
    }
    /** 更新 dom 变化后的 canvas 宽高 */
    resize(width: number, height: number, withAnimate: boolean, canvas: Tgs.ICanvas) {
        // console.log(width, height);
        this.canvas.setWidth(Math.ceil(width / 20) * 20);
        this.canvas.setHeight(Math.ceil(height / 20) * 20);
        // this.pageContainerMap[this.currentPage] && this.centerPage(this.pageContainerMap[this.currentPage].page);
        if (withAnimate) {
            this.setPagePosition(canvas, true);
        }
    }
    // 始终保持中心缩放
    protected centerZoom(zoom: number, pointer: fabric.Point, delta: number, page?: fabric.Object) {
        if (!zoom) return;
        // 如果画布页面不超出就中心缩放，否则按鼠标位置缩放
        const beforeZoom = this.canvas.getZoom();
        const width = this.canvas.getWidth();
        const pageWidth = this.canvasInfo.width * zoom;
        const firstPageTop =  this.pageContainerMap[0].page.top as number;
        const lastPageBottom = (this.pageContainerMap[this.pageCount - 1].page.top as number + this.canvasInfo.height) * beforeZoom;
        const transform = this.canvas.viewportTransform as number[];
        const transformX  = transform?.[4] as number;
        const transformY = transform?.[5] as number;
        const center = this.canvas.getCenter();
        const right = transformX + pageWidth;
        let allPageHeight = this.canvasInfo.height * beforeZoom;
        let height = this.canvas.getHeight() - 10;
        if(this.renderMode === '') { 
            height = this.canvas.getHeight(); 
            allPageHeight = (this.canvasInfo.height * beforeZoom ) * this.pageCount + this.baseGap * this.pageCount + this.baseGap;
        }
        // 第一页和最后一页是否超出画布
        if ((transformX > 0 && right < width && allPageHeight < height) && firstPageTop <= transformY && lastPageBottom < height) {
            // let top = center.top - (transformY - transformY* zoom) / 2;
            // if (center.top - allPageHeight / 2 + transformY < 10) {
            //     top = center.top
            // }
            const top = center.top
            const left = pageWidth < width ? center.left : center.left - pageWidth/2 + transformX;
            const point = new fabric.Point(left, top);
            this.canvas.zoomToPoint(point, zoom); // 缩放画布
        } else {
          // 未超出画布 x按中心缩放
            if ((transformX > 0 && right < width) && pageWidth < width) {
                pointer.x = center.left
            } else {
                // 超出画布 x按鼠标位置缩放
                if (delta > 0) {
                    if (transformX > 0) {
                        pointer.x = 0
                    } else {
                        pointer.x = width - 20
                    }
                }
            }
            if (height >= allPageHeight) {
                pointer.y = center.top
            } else {
                if (transformY > 60) {
                    pointer.y = 60
                } else if(transformY + allPageHeight < height && delta > 0) {
                    pointer.y = height - 20
                }
            }
            if (delta > 0) {
                // 向下滚动，缩小画布
                this.canvas.zoomToPoint(pointer, zoom);
            } else {
                // 向上滚动，放大画布
                this.canvas.zoomToPoint(pointer, zoom);
            }
        }
        this.syncCanvasInfo();
    }

    protected syncCanvasInfo() {
        const transform = this.canvas.viewportTransform;
        const scale = this.canvas.getZoom();
        this.canvasInfo.x = transform?.[4];
        this.canvasInfo.y = transform?.[5];
        this.canvasInfo.scale = scale;
        this.actions.onUpdateCanvasInfo({
            x: transform?.[4],
            y: transform?.[5],
            scale,
        });
    }

    protected isPointInPage(point: fabric.Point, targetClipPage?: fabric.Object) {
        let isInPage = false;
        let pageIndex: number | undefined = undefined;
        let page: TgsCanvasPage | TgsCanvasPageRenderOnly | undefined = undefined;
        if (this.renderMode === 'board') {
            // 在board模式下，页面是一个整体，不需要判断具体的页面
            return { isInPage: true, pageIndex: this.currentPage, page: this.pageContainerMap[this.currentPage] };
        } else {
            const pageMap = this.getModePage();
            for (const i in pageMap) {
                if (pageMap[i].page.containsPoint(point) && !targetClipPage) {
                    page = pageMap[i];
                    isInPage = true;
                    pageIndex = page.pageConfig.pageIndex;
                    break;
                } else if (pageMap[i].page === targetClipPage) {
                    page = pageMap[i];
                    isInPage = true;
                    pageIndex = page.pageConfig.pageIndex;
                    break;
                }
            }
        }
        return { isInPage, pageIndex, page };
    }

    getModePage<T extends 'pageContainerMap' | 'filmStripPageMap'>(): this[T] {
        return this.renderMode === '' ? this.pageContainerMap : this.filmStripPageMap;
    }

    /* 判断元素和所有的页面是否相交 */
    protected isIntersectingWithPages(element: fabric.Object) {
        const pageMap = this.getModePage();
        let isIntersecting = false;
        for (const key in pageMap) {
            const page = pageMap[key].page;
            if (element.group) {
                return true;
            }
            isIntersecting = page.intersectsWithObject(element, true);
            if (isIntersecting) {
                break;
            }
        }
        return isIntersecting;
    }
    convertPageToImage(options:Tgs.IConvertPageToImageOptions) {
        const page = this.pageContainerMap[options.index] as TgsCanvasPage;
        if (!page) return '';
        return page.convertPageToImage(options);
    }
    getImageData(index:number) {
        const page = this.pageContainerMap[index] as TgsCanvasPage;
        if (!page) return '';
        return page.getImageData();
    }
    blurSelect() {
        const select = this.canvas.getActiveObject();
        if (select) {
            if (select.get('type') !== 'activeSelection' && this.selectClassNames.size === 1) {
                const className = this.selectClassNames.values().next().value;
                const pageMap = this.getModePage();
                const page = pageMap[this.currentPage] || pageMap[0];
                const a = page?.assetsMap[className];
                if (a && a.type === 'image' && a.asset?.attribute?.container?.isEdit && 'setClipEnd' in a) {
                    a.setClipEnd();
                }
            }
            if (select.type == 'group') {
                select.fire('blurSelect');
            }
            this.canvas.discardActiveObject();
        }
    }
       /**
     * @description: 更新水印状态
     * @param {boolean} showWaterMark
     * @return {*}
     */
       updateWaterMark(showWaterMark: boolean) {
        if (showWaterMark != this.showWaterMark) {
            this.showWaterMark = showWaterMark;
            for (const index in this.pageContainerMap) {
                const pageContainer = this.pageContainerMap[index];
                if (showWaterMark) {
                    pageContainer.addWaterMark();
                } else {
                    pageContainer.removeWaterMark();
                }
            }
        }
    }
}
