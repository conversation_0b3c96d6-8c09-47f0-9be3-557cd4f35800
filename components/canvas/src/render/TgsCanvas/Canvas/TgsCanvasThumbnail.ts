import { fabric } from 'fabric';
import { Actions } from '../../../actions';
import { RenderZIndex } from '../../RenderZIndex';
import { TgsCanvasPageRenderOnly } from '../Page';
import { applyToPoint, rotateDEG } from 'transformation-matrix';

const calcRotateRect = (size: { x: number; y: number; w: number; h: number }, rotate = 0) => {
    const { x, y, w, h } = size;
    const tl = { x, y };
    const tr = { x: x + w, y };
    const bl = { x, y: y + h };
    const br = { x: x + w, y: y + h };
    const matrix = rotateDEG(rotate, x + w / 2, y + h / 2);
    const newTl = applyToPoint(matrix, tl);
    const newTr = applyToPoint(matrix, tr);
    const newBl = applyToPoint(matrix, bl);
    const newBr = applyToPoint(matrix, br);
    const t: number = Math.min(newTl.y, newTr.y, newBl.y, newBr.y);
    const b: number = Math.max(newTl.y, newTr.y, newBl.y, newBr.y);
    const l: number = Math.min(newTl.x, newTr.x, newBl.x, newBr.x);
    const r: number = Math.max(newTl.x, newTr.x, newBl.x, newBr.x);
    return {
        x: l,
        y: t,
        w: r - l,
        h: b - t,
    };
};

/** 整个画布类, 基类只处理画布自身信息及基础功能 */
export class TgsCanvasThumbnail {
    renderZIndex;
    actions = new Actions();

    canvas!: fabric.Canvas;
    readOnly = true;

    canvasInfo: Tgs.ICanvas = {
        width: 1000,
        height: 1000,
        x: 0,
        y: 0,
        scale: 1,
    };

    currentPage: TgsCanvasPageRenderOnly | null = null;

    constructor(param: { element: string | HTMLCanvasElement | null; options?: fabric.ICanvasOptions }) {
        this.canvas = new fabric.Canvas(param.element, {
            width: param.options?.width || 200,
            height: param.options?.width || 200,
            renderOnAddRemove: false,
            interactive: false,
            skipOffscreen: true,
            enableRetinaScaling: true,
            backgroundColor: param.options?.backgroundColor || '#F0F1F5',
        });
        // this.canvas.requestRenderAll = () => {
        //     return this.canvas as fabric.Canvas;
        // };
        this.renderZIndex = new RenderZIndex(this.canvas, this.actions);
        // console.log('TgsCanvasThumbnail: ', this);
        // const dom = this.canvas.getElement();
        // dom.style.position = 'absolute';
        // dom.style.left = '0px';
        // dom.style.top = '0px';
        // dom.style.zIndex = '99999999999999999';
        // dom.style.background = '#f00';
        // document.body.appendChild(this.canvas.getElement() as HTMLCanvasElement);
    }

    async renderPage(options: {
        canvas: Tgs.ICanvas;
        page: Tgs.IPage;
        pageAttr: Tgs.IPageAttr;
        pageIndex: number;
        callback: (canvasDom: HTMLCanvasElement) => void;
    }) {
        const { page, pageAttr, canvas, pageIndex } = options;
        const { backgroundColor = [{ r: 255, g: 255, b: 255, a: 1 }] } = pageAttr;
        const color = backgroundColor[pageIndex];
        // const now = Date.now();
        const pageType = pageAttr.pageInfo?.[pageIndex]?.type || '';
        this.canvas.setWidth(Math.ceil(canvas.width * canvas.scale));
        this.canvas.setHeight(Math.ceil(canvas.height * canvas.scale));
        if (pageType === 'board') {
            let left = 0;
            let top = 0;
            let right = 0;
            let bottom = 0;
            page.assets.forEach((asset) => {
                const [ox, oy, ow, oh] = [
                    asset.transform.posX,
                    asset.transform.posY,
                    'container' in asset.attribute
                        ? (asset.attribute.container?.width ?? asset.attribute.width)
                        : asset.attribute.width,
                    'container' in asset.attribute
                        ? (asset.attribute.container?.height ?? asset.attribute.height)
                        : asset.attribute.height,
                ];
                const { x, y, w, h } = calcRotateRect({ x: ox, y: oy, w: ow, h: oh }, asset.transform.rotate);
                left = Math.min(left, x);
                top = Math.min(top, y);
                right = Math.max(right, x + w);
                bottom = Math.max(bottom, y + h);
            });
            const width = right - left;
            const height = bottom - top;
            const scale = Math.min(canvas.width / width, canvas.height / height) * canvas.scale;
            // 将内容调整到可视区域并缩放居中
            this.canvas.setViewportTransform([
                scale,
                0,
                0,
                scale,
                (0 - left + 20) * scale,
                (0 - top + 20) * scale,
            ]);
            // this.canvas.setZoom(scale);
        } else {
            this.canvas.setZoom(canvas.scale);
        }

        if (this.currentPage?.pageConfig.pageIndex === pageIndex) {
            this.currentPage?.updateBackground({
                backgroundColor: pageAttr.backgroundColor?.[pageIndex] || { r: 255, g: 255, b: 255, a: 1 },
                backgroundOpacity: pageAttr.backgroundOpacity?.[pageIndex] || false,
                backgroundImage: pageAttr.backgroundImage?.[pageIndex] || { resId: undefined },
            });
            await this.currentPage?.updateAssets(page.assets, pageIndex);
        } else {
            this.clearPage();
            this.canvas.on('after:render', () => {
                // console.log(11111111, 'after:render', new Date().getTime() - now);
                options.callback(this.canvas.getElement() as HTMLCanvasElement);
            });
            this.currentPage = new TgsCanvasPageRenderOnly(
                this as any,
                this.canvas as fabric.Canvas,
                {
                    left: 0,
                    top: 0,
                    width: canvas.width,
                    height: canvas.height,
                    backgroundColor: color,
                    pageIndex: pageIndex,
                    pageHash: '',
                },
                this.actions,
                this.renderZIndex,
                this.readOnly,
            );
            if (pageType === 'board') {
                this.currentPage.page.setOptions({
                    opacity: 0,
                    pageType: 'board',
                });
            }
            this.currentPage.addToCanvas();
            this.renderZIndex.setPageZIndex(pageIndex, this.currentPage.page);

            this.currentPage?.updateBackground({
                backgroundColor: pageAttr.backgroundColor?.[pageIndex] || { r: 255, g: 255, b: 255, a: 1 },
                backgroundOpacity: pageAttr.backgroundOpacity?.[pageIndex] || false,
                backgroundImage: pageAttr.backgroundImage?.[pageIndex] || { resId: undefined },
            });
            await this.currentPage.updateAssets(page.assets, pageIndex);
            // this.canvas.renderAll();
        }
    }

    async updatePage(options: {
        canvas: Tgs.ICanvas;
        page: Tgs.IPage;
        pageAttr: Tgs.IPageAttr;
        pageIndex: number;
        callback: (canvasDom: HTMLCanvasElement) => void;
    }) {
        const { page, pageAttr, canvas, pageIndex } = options;

        this.currentPage?.updateBackground({
            backgroundColor: pageAttr.backgroundColor?.[pageIndex] || { r: 255, g: 255, b: 255, a: 1 },
            backgroundOpacity: pageAttr.backgroundOpacity?.[pageIndex] || false,
            backgroundImage: pageAttr.backgroundImage?.[pageIndex] || { resId: undefined },
        });
        await this.currentPage?.updateAssets(page.assets, pageIndex);
    }

    clearPage() {
        if (this.currentPage) {
            this.renderZIndex.removePageZIndex(this.currentPage.pageConfig.pageIndex);
            this.currentPage.removeFromCanvas();
            this.currentPage.destroy();
            this.currentPage = null;
        }
        this.canvas.off('after:render');
    }
}
