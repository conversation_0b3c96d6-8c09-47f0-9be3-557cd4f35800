import { fabric } from 'fabric';
import equal from 'fast-deep-equal';
import { DataFormat } from '../../../dataFormat';
import { TgsCanvasPage } from '../Page';
import { TgsCanvasEvents } from './TgsCanvasEvents';
import { IAssetTextBrushStatusEnum } from '@tgs/types';

/** 整个画布类，只管理模板数据处理 */
export class TgsCanvas extends TgsCanvasEvents {
    static SHOW_TEXT_RECT_ON_AI_DESIGN_FIRST_RENDER = false;
    protected pageAttr!: Tgs.IPageAttr;
    declare initLoaded: boolean;
    protected asyncRenderNumLimit = 5;
    protected currentBackgroundColor?: fabric.StaticCanvas['backgroundColor'];

    async init(param: {
        canvas: Tgs.ICanvas;
        pages: Tgs.IPage[];
        pageAttr: Tgs.IPageAttr;
        pageIndex: number;
        currentPage: number;
        mode: string;
        readOnly?: boolean;
        showWaterMark?: boolean;
    }) {
        this.canvas.on('after:render', () => {
            for (const i in this.pageContainerMap) {
                this.pageContainerMap[i].initAssetsOnNeed();
            }
        });

        this.pageAttr = DataFormat.formatPageAttr(param.pageAttr);
        if (param.mode === 'board') {
            this.currentBackgroundColor = this.canvas.backgroundColor
            this.canvas.setBackgroundColor(this.dotPattern, this.canvas.requestRenderAll.bind(this.canvas));
        }
        this.renderMode = param.mode;
        this.readOnly = param.readOnly || false;
        this.currentPage = param.currentPage;
        this.initLoaded = false;
        // 初始页面及其缩放，根据浏览器视口大小
        this.createAllPages(param.pages, param.pageAttr, param.canvas, param.mode, param.pageIndex);
        this.updateCanvas(param.canvas);
        this.showWaterMark = param.showWaterMark || false;
        // 控制画布的缩放移动等
        if (!this.readOnly) {
            this.bindEvent();
        }
    }

    /** 更新页元素 添加多页*/
    // @CommonDecorator.Debounce(500,{leading:true,trailing:true})
    updatePages(options: {
        canvas: Tgs.ICanvas;
        pages: Tgs.IPage[];
        pageAttr: Tgs.IPageAttr;
        pageIndex: number;
        mode: string;
    }) {
        const { pages, pageAttr, canvas, mode } = options;
        const len = Object.keys(this.pageContainerMap).length;
        const isForce = this.renderMode !== mode;
        const pageIndexChange = this.currentPage !== options.pageIndex;
        this.currentPage = options.pageIndex;
        if (mode === 'board' && this.renderMode !== 'board') {
            this.currentBackgroundColor = this.canvas.backgroundColor
            this.canvas.setBackgroundColor(this.dotPattern, this.canvas.renderAll.bind(this.canvas));
        } else if (mode !== 'board' && this.renderMode === 'board') {
            if (this.currentBackgroundColor) {
                this.canvas.setBackgroundColor(this.currentBackgroundColor, this.canvas.renderAll.bind(this.canvas));
            }
        }
        this.renderMode = mode;
        if (options.pages.length <= this.asyncRenderNumLimit) {
            this.initLoaded = true;
        }
        const isSamePageHash = equal(this.pageAttr.pageHash, pageAttr.pageHash);
        if (!isSamePageHash) {
            this.pageAttr = DataFormat.formatPageAttr(pageAttr);
        }
        // if (isSamePageHash && !isForce) {
        //     for (const i in this.pageContainerMap) {
        //         this.pageContainerMap[i].updateAssets(pages[Number(i)].assets, Number(i));
        //     }
        //     return;
        // }
        // 数量相等,增删改pageContainerMap都需要更新
        if (len === pages.length) {
            if (!this.initLoaded && !pageIndexChange) return;
            if (pageAttr.pageHash && !isSamePageHash) {
                const newPageMap: Record<string, { newIndex: number; oldIndex: number; pageContainer: TgsCanvasPage }> =
                    {};
                for (const key in this.pageContainerMap) {
                    const hash = this.pageContainerMap[key].pageConfig.pageHash;
                    if (pageAttr.pageHash && pageAttr.pageHash[hash] >= 0) {
                        const oldIndex = this.pageContainerMap[key].pageConfig.pageIndex;
                        newPageMap[hash] = {
                            newIndex: pageAttr.pageHash[hash],
                            oldIndex,
                            pageContainer: this.pageContainerMap[key],
                        };
                        if (pageAttr.pageHash[hash] !== oldIndex) {
                            delete this.pageContainerMap[oldIndex];
                            this.renderZIndex.removePageZIndex(oldIndex);
                        }
                    }
                }
                for (const key in newPageMap) {
                    const pageContainerInfo = newPageMap[key];
                    const newIndex = pageContainerInfo.newIndex;
                    const pageContainer = pageContainerInfo.pageContainer;
                    pageContainer.pageConfig.pageIndex = newIndex;
                    pageContainer.page.name = 'page_' + newIndex;
                    pageContainer.pageConfig.pageHash = key;
                    pageContainer.setPageType(pageAttr.pageInfo?.[newIndex]?.type);
                    this.pageContainerMap[newIndex] = pageContainer;
                    this.renderZIndex.setPageZIndex(newIndex, pageContainer.page);
                }
            }
            if (mode === 'pull' || mode === 'board') {
                this.updateRenderMode({ ...options, isSamePageHash });
            } else {
                let centerPage!: TgsCanvasPage;

                for (const index in this.pageContainerMap) {
                    const item = this.pageContainerMap[index];
                    const { pageIndex: pageNumber } = item.pageConfig;
                    const page = pages[pageNumber];
                    if (options.pageIndex === pageNumber) {
                        centerPage = item;
                        this.currentPage = pageNumber;
                    }
                    item.setPageAndAssetsVisible(true);
                    item.updateAssets(page.assets, pageNumber);
                    this.updatePageBackground(item, pageNumber, pageAttr, page);
                }
                // 模式切换，需要重新计算每个页面的位置
                this.updateAllPageOffsetY();
                !isSamePageHash && centerPage && this.updatePagePosition(centerPage?.page, isForce);
                isForce && this.blurSelect(); // 切换模式元素失去焦点
            }
        } else if (len > pages.length) {
            // 根据hash查找要删除的页面
            let delPage: TgsCanvasPage | undefined;
            if (pageAttr.pageHash) {
                for (const key in this.pageContainerMap) {
                    const pageContainer = this.pageContainerMap[key];
                    const hash = this.pageContainerMap[key].pageConfig.pageHash;
                    if (pageAttr.pageHash[hash] === undefined) {
                        delPage = pageContainer;
                        delPage.page.name = `page_delete`;
                        if (delPage) {
                            delPage.destroy();
                            delPage.removeFromCanvas();
                            this.renderZIndex.removePageZIndex(delPage.pageConfig.pageIndex);
                        }
                        delete this.pageContainerMap[key];
                    } else {
                        const index = pageAttr.pageHash[hash];
                        const oldPageIndex = pageContainer.pageConfig.pageIndex;
                        if (index !== oldPageIndex) {
                            pageContainer.pageConfig.pageIndex = index;
                            pageContainer.page.name = `page_${index}`;
                            this.pageContainerMap[index] = pageContainer;
                            delete this.pageContainerMap[oldPageIndex];
                        }
                    }
                }
            }
            if (mode === 'pull' || mode === 'board') {
                if (delPage) {
                    delPage.removeFromCanvas();
                    this.renderZIndex.removePageZIndex(delPage.pageConfig.pageIndex);
                }
                this.updateRenderMode(options);
            } else {
                if (delPage) {
                    let page: TgsCanvasPage[];
                    const { pageIndex } = delPage.pageConfig;
                    let direction = 'down';
                    if (pageIndex === 0) {
                        // 删除的是第1页 则跳转到下一页
                        page = this.getPageByPageIndex(0);
                        direction = 'down';
                    } else if (pageIndex === pages.length) {
                        // 删除的是最后一页，则跳转到上一页
                        page = this.getPageByPageIndex(pages.length - 1);
                        direction = 'up';
                    } else {
                        // 删除的是中间页，则跳转到下一页
                        page = this.getPageByPageIndex(pageIndex);
                    }
                    delPage.removeFromCanvas();
                    this.renderZIndex.removePageZIndex(pageIndex);
                    if (direction === 'up' || pages.length === 1) {
                        // 移动视口
                        this.updateAllPageOffsetY();
                        this.centerDelPageAnim(page[0]?.page, pages.length, direction);
                    } else {
                        // 下一页移动到上方
                        this.updatePageTopByAnim();
                    }
                }
            }
            if (pageAttr.pageHash) {
                for (const key in this.pageContainerMap) {
                    const pageContainer = this.pageContainerMap[key];
                    const hash = pageContainer.pageConfig.pageHash;
                    const index = pageAttr.pageHash[hash];
                    pageContainer.pageConfig.pageIndex = index;
                    pageContainer.setPageType(pageAttr.pageInfo?.[index]?.type);
                    this.pageContainerMap[index] = pageContainer;
                    this.renderZIndex.setPageZIndex(index, pageContainer.page);
                }
            }
        } else if (len < pages.length) {
            const zoom = this.canvas.getZoom();
            const gapY = this.baseGap / zoom;
            // 添加页面
            const { pageHash } = pageAttr;
            const newPageMap: Record<string, { newIndex: number; oldIndex: number; pageContainer: TgsCanvasPage }> = {};
            for (const key in this.pageContainerMap) {
                const hash = this.pageContainerMap[key].pageConfig.pageHash;
                if (pageHash && pageHash[hash] >= 0) {
                    const oldIndex = this.pageContainerMap[key].pageConfig.pageIndex;
                    newPageMap[hash] = {
                        newIndex: pageHash[hash],
                        oldIndex,
                        pageContainer: this.pageContainerMap[key],
                    };
                    if (pageHash[hash] !== oldIndex) {
                        delete this.pageContainerMap[oldIndex];
                        this.renderZIndex.removePageZIndex(oldIndex);
                    }
                } else if (pageHash && !pageHash[hash]) {
                    const delPage = this.pageContainerMap[key];
                    delPage.destroy();
                    this.renderZIndex.removePageZIndex(delPage.pageConfig.pageIndex);
                    delPage.removeFromCanvas();
                    delete this.pageContainerMap[key];
                }
            }
            const newPageHashs = [];
            for (const key in pageHash) {
                const index = pageHash[key];
                const pageContainerInfo = newPageMap[key];
                if (pageContainerInfo && pageContainerInfo.newIndex !== pageContainerInfo.oldIndex) {
                    const newIndex = pageContainerInfo.newIndex;
                    const pageContainer = pageContainerInfo.pageContainer;
                    pageContainer.pageConfig.pageIndex = newIndex;
                    pageContainer.page.name = 'page_' + newIndex;
                    // 如果是0就不用更新
                    if (index !== 0) {
                        this.updatePageTop(pageContainer);
                    }
                    this.pageContainerMap[index] = pageContainer;
                    pageContainer.setPageType(pageAttr.pageInfo?.[index]?.type);
                    pageContainer.updateAssets(pages[index].assets, index);

                    this.updatePageBackground(pageContainer, index, pageAttr, pages[index]);
                    this.renderZIndex.setPageZIndex(index, pageContainer.page);
                } else if (!pageContainerInfo) {
                    newPageHashs.push({ key, index });
                }
            }
            const taskQueue: Promise<unknown>[] = [];
            newPageHashs.forEach(({ key, index }) => {
                const pageContainer = this.createPages({
                    hash: key,
                    index: index,
                    color: pageAttr.backgroundColor?.[index] || { r: 255, g: 255, b: 255, a: 1 },
                    width: canvas.width,
                    height: canvas.height,
                    top: index ? (canvas.height + gapY) * index : 0,
                });
                if (index !== 0) {
                    this.updatePageTop(pageContainer);
                }
                pageContainer.setPageType(pageAttr.pageInfo?.[index]?.type);
                // 对于超过10页的模板 优先加载第一页，其他按页面顺序降级加载
                if (!this.initLoaded && pages.length >= this.asyncRenderNumLimit) {
                    // 优先加载前5页
                    if (Number(index) > this.asyncRenderNumLimit - 1) {
                        const task = new Promise((resolve) => {
                            requestIdleCallback(async () => {
                                await pageContainer.updateAssets(pages[index]?.assets || [], index);
                                this.updatePageBackground(pageContainer, index, pageAttr, pages[index]);
                                resolve('');
                            });
                        });
                        taskQueue.push(task);
                    } else {
                        const task = new Promise(async (resolve) => {
                            await pageContainer.updateAssets(pages[index]?.assets || [], index);
                            this.updatePageBackground(pageContainer, index, pageAttr, pages[index]);
                            this.actions.updateCanvasLoadingStatus(false);
                            resolve('');
                        });
                        taskQueue.push(task);
                    }
                } else {
                    pageContainer.updateAssets(pages[index].assets, index);
                    this.updatePageBackground(pageContainer, index, pageAttr, pages[index]);
                }
                this.pageContainerMap[index] = pageContainer;
                this.pageContainerMap[index].addToCanvas();
                if (this.showWaterMark) {
                    this.pageContainerMap[index].addWaterMark();
                }
                this.renderZIndex.setPageZIndex(index, pageContainer.page);
                if (mode === 'pull' || mode === 'board') {
                    this.updateRenderMode(options);
                } else if (index === options.pageIndex) {
                    this.centerAddPageAnim(pageContainer.page);
                }
            });
            if (taskQueue.length) {
                Promise.all(taskQueue).finally(() => {
                    this.initLoaded = true;
                });
            }
            // const hashKeys = Object.keys(newPageHashs)
            // let curIndex = 0
            // const renderFramePage = ()=>{
            //     requestAnimationFrame(async ()=>{
            //     const key = hashKeys[curIndex]
            //     if(!key || curIndex > hashKeys.length - 1) return
            //     const pageContainer = this.createPages({
            //         hash: key,
            //         index: curIndex,
            //         color: pageAttr.backgroundColor?.[curIndex] || { r: 255, g: 255, b: 255, a: 1 },
            //         width: canvas.width,
            //         height: canvas.height,
            //         top: curIndex ? (canvas.height + gapY) * curIndex : 0,
            //     });
            //     await pageContainer.updateAssets(pages[curIndex].assets, curIndex);
            //     this.updatePageBackground(pageContainer, curIndex, pageAttr, pages[curIndex]);
            //     this.pageContainerMap[curIndex] = pageContainer;
            //     this.pageContainerMap[curIndex].addToCanvas();
            //     this.renderZIndex.setPageZIndex(curIndex, pageContainer.page);
            //     if (mode === 'pull') {
            //         this.updateRenderMode(options);
            //     } else if (curIndex === options.pageIndex) {
            //         this.centerAddPageAnim(pageContainer.page);
            //     }
            //     })
            //     curIndex++
            //     if(curIndex <= hashKeys.length - 1){
            //         renderFramePage()
            //     }
            // }
            // renderFramePage()
        }
    }

    getPageByPageIndex(pageIndex: number) {
        return Object.values(this.pageContainerMap).filter((item) => item.pageConfig.pageIndex === pageIndex);
    }

    /** 更新渲染模式*/
    updateRenderMode(options: {
        canvas: Tgs.ICanvas;
        pages: Tgs.IPage[];
        pageAttr: Tgs.IPageAttr;
        pageIndex: number;
        mode: string;
        isSamePageHash?: boolean;
    }) {
        if (options.mode === 'pull' || options.mode === 'board') {
            // this.currentPage = 0
            const { pageIndex, pages } = options;
            for (const index in this.pageContainerMap) {
                const item = this.pageContainerMap[index];
                const { pageIndex: pageNumber } = item.pageConfig;
                if (pageNumber === pageIndex) {
                    item.setPageAndAssetsVisible(true);
                    item.updateAssets(pages[pageIndex].assets, pageIndex);
                    item.setPageType(options.pageAttr.pageInfo?.[pageIndex]?.type);
                    this.currentPage = pageIndex;
                    this.updatePageBackground(item, pageIndex, options.pageAttr, pages[pageIndex]);
                    this.updatePageTop(item, 0);
                    if (!this.readOnly) {
                        !options.isSamePageHash && this.updatePagePosition(item.page, true);
                    }
                    this.filmStripPageMap[0] = item;
                } else {
                    // 将不需要显示的页面隐藏
                    item.setPageAndAssetsVisible(false);
                }
            }
            this.canvas.requestRenderAll();
        }
    }

    /* 创建单个页面 */
    protected createPages(params: {
        hash: string;
        index: number;
        color: Tgs.IColor;
        width: number;
        height: number;
        top: number;
    }) {
        const { hash, index, color, width, height, top } = params;
        const pageContainer = new TgsCanvasPage(
            this,
            this.canvas,
            {
                left: 0,
                top: top,
                width: width,
                height: height,
                backgroundColor: color,
                pageIndex: index,
                pageHash: hash,
            },
            this.actions,
            this.renderZIndex,
            false,
            this.renderMode === 'pull' || this.renderMode === 'board' ? index === this.currentPage : true,
        );
        return pageContainer;
    }

    protected async createAllPages(
        pages: Tgs.IPage[],
        pageAttr: Tgs.IPageAttr,
        canvas: Tgs.ICanvas,
        mode: string,
        pageIndex: number,
    ) {
        const { backgroundColor = [{ r: 255, g: 255, b: 255, a: 1 }], pageHash } = pageAttr;
        const { height, width } = canvas;
        const zoom = this.canvas.getZoom();
        const gapY = this.baseGap / zoom;
        for (const key in pageHash) {
            const index = Number(pageHash[key]);
            const page = pages[index];
            const color = backgroundColor[index];
            const top = index ? (height + gapY) * index : 0;
            const pageContainer = this.createPages({ hash: key, index, color, width, height, top });
            this.pageContainerMap[index] = pageContainer;
            this.pageContainerMap[index].addToCanvas();
            this.renderZIndex.setPageZIndex(index, pageContainer.page);
            await pageContainer.updateAssets(page.assets, index);
            if (this.showWaterMark) {
                pageContainer.addWaterMark();
            }
            pageContainer.updateBackground({
                backgroundColor: pageAttr.backgroundColor?.[index] || { r: 255, g: 255, b: 255, a: 1 },
                backgroundOpacity: pageAttr.backgroundOpacity?.[index] || false,
                backgroundImage: pageAttr.backgroundImage?.[index] || { resId: undefined },
            });
        }
        if (this.renderMode === 'pull' || this.renderMode === 'board') {
            this.updateRenderMode({ canvas, pages, pageAttr, pageIndex, mode: this.renderMode });
        } else {
            this.updateAllPageOffsetY();
        }
    }

    /**
     * @description: 更新文本格式刷状态
     * @param {boolean} status
     * @return {*}
     */
    updateTextStyleBrushStatus(status: IAssetTextBrushStatusEnum) {
        this.canvas.selectionKey = status ? [] : ['ctrlKey', 'shiftKey'];
        this.canvas?.fire('updateTextStyleBrushStatus', { status });
    }
    /** 更新页面间距 */
    protected updateAllPageOffsetY() {
        // 根据模式来
        if (this.renderMode === '') {
            for (const index in this.pageContainerMap) {
                const item = this.pageContainerMap[index];
                this.updatePageTop(item, Number(index));
            }
        }
    }

    /** 动画更新页面间距 */
    protected updatePageTopByAnim() {
        const zoom = this.canvas.getZoom();
        const gapY = this.baseGap / zoom;
        fabric.util.animate({
            startValue: 0, // 起始值
            endValue: 1, // 结束值
            duration: 300,
            onChange: (value) => {
                for (const index in this.pageContainerMap) {
                    const item = this.pageContainerMap[index];
                    const { height = 1000 } = item.page;
                    let top = index ? (height + gapY) * Number(index) : 0;
                    if (item.page.top !== top) {
                        top = top + ((item.page.top as number) - top) * (1 - value);
                        item.updatePageTop(top);
                        item.updateAssetScaleCoordinate(Number(index));
                    }
                    this.canvas.requestRenderAll();
                }
            },
            onComplete: () => {
                console.log('finish');
            },
        });
    }
    /** 更新单页页面间距 没有动画版*/
    protected updatePageTop(item: TgsCanvasPage, pageIndex?: number) {
        if (pageIndex === undefined) {
            pageIndex = item.pageConfig.pageIndex;
        }
        const zoom = this.canvas.getZoom();
        const gapY = this.baseGap / zoom;
        const { height = 1000 } = item.page;
        const top = pageIndex ? (height + gapY) * Number(pageIndex) : 0;
        item.updatePageTop(top);
        item.updateAssetScaleCoordinate(pageIndex);
    }

    /* 更新页面背景等以及元素 */
    protected updatePageBackground(item: TgsCanvasPage, index: number, pageAttr: Tgs.IPageAttr, page?: Tgs.IPage) {
        let backgroundColor = pageAttr.backgroundColor?.[index] || { r: 255, g: 255, b: 255, a: 1 };
        let isOpacityBg = pageAttr.backgroundOpacity?.[index] || false;
        if (page) {
            backgroundColor = page.backgroundColor as Tgs.IColor;
            isOpacityBg = page.isOpacityBg as boolean;
        }
        item.updateBackground({
            backgroundColor: backgroundColor,
            backgroundOpacity: isOpacityBg,
            backgroundImage: pageAttr.backgroundImage?.[index] || { resId: undefined },
        });
    }

    updateBackground(
        param: {
            backgroundColor: Tgs.IColor[];
            backgroundOpacity: boolean[];
            backgroundImage: {
                resId?: string;
                rt_imageUrl?: string;
                backgroundSize?: {
                    width: string | number;
                    height: string | number;
                };
            }[];
        },
        pageIndex: number,
        pages?: Tgs.IPage[],
    ) {
        // TODO 支持多页
        const updatePage = this.pageContainerMap[pageIndex];
        let backgroundColor = param.backgroundColor?.[pageIndex];
        let isOpacityBg = param.backgroundOpacity?.[pageIndex] || false;
        if (pages && pages[pageIndex]) {
            const page = pages[pageIndex];
            backgroundColor = page.backgroundColor as Tgs.IColor;
            isOpacityBg = page.isOpacityBg as boolean;
        }
        if (updatePage) {
            updatePage.updateBackground({
                backgroundColor: backgroundColor,
                backgroundOpacity: isOpacityBg || false,
                backgroundImage: param.backgroundImage?.[pageIndex] || { resId: undefined },
            });
        }
    }


    updateCanvas(canvas: Tgs.ICanvas) {
        canvas = DataFormat.formatCanvas(canvas);
        if (this.canvasInfo.scale !== canvas.scale) {
            this.canvas.setZoom(canvas.scale);
            this.setCornerScaleViability();
            this.updateAsssetScale();
        }
        if (this.canvasInfo.width !== canvas.width || this.canvasInfo.height !== canvas.height) {
            for (const i in this.pageContainerMap) {
                let top = 0;
                if (this.renderMode === '') {
                    top = i ? (canvas.height + this.baseGap / canvas.scale) * Number(i) : 0;
                }
                this.pageContainerMap[i].updatePageSize(canvas.width, canvas.height, top);
            }
            // this.page.updatePageSize(canvas.width, canvas.height);
            // if (this.pageContainerMap?.[this.currentPage]?.page) {
            //     this.centerPage(this.pageContainerMap[this.currentPage].page);
            // }
        }
        // if (this.canvasInfo.x !== canvas.x || this.canvasInfo.y !== canvas.y) {
            this.setPagePosition(canvas);
        // }
        this.canvasInfo = canvas;
        this.updateAllPageOffsetY();
        this.canvas.requestRenderAll();
    }

    /**
     * 绘制内容区域
     */
    // private drawPageContent(param: {
    //     canvas: Tgs.ICanvas;
    //     pages: Tgs.IPage[];
    //     pageAttr: Tgs.IPageAttr;
    //     pageIndex: number;
    // }) {
    //     const pageContainer = new TgsCanvasPage(
    //         this,
    //         this.canvas,
    //         {
    //             left: 0,
    //             top: 0,
    //             width: param.canvas.width,
    //             height: param.canvas.height,
    //             backgroundColor: param.pages[param.pageIndex].backgroundColor,
    //             pageIndex: param.pageIndex,
    //         },
    //         this.actions,
    //         this.renderZIndex,
    //     );
    //     this.pageContainerMap[param.pageIndex] = pageContainer;
    //     this.page = pageContainer;
    //     this.canvas.add(pageContainer.page);
    //     this.renderZIndex.setPageZIndex(param.pageIndex, pageContainer.page);
    //     this.centerPage(pageContainer.page);
    // }

    protected removeAllPage() {
        Object.values(this.pageContainerMap).forEach((page: TgsCanvasPage) => {
            // 删除页面
            page.removeFromCanvas();
            this.renderZIndex.removePageZIndex(page.pageConfig.pageIndex);
            // 删除当前页面的元素
            page.destroy();
        });
        this.pageContainerMap = {};
    }

    destroy() {
        this.removeAllPage();
        this.auxiliaryLine.removeFromCanvas();
    }

    /** 同步选中，目前只支持单选 */
    syncSelectAsset(param: { index: number[]; pageIndex: number }) {
        this.isSyncSelect = true;
        if (this._syncSelectTimer) {
            clearTimeout(this._syncSelectTimer);
        }
        this._syncSelectTimer = window.setTimeout(() => {
            const pageContainer = this.pageContainerMap[param.pageIndex];
            if (pageContainer) {
                const activeObjects: fabric.Object[] = [];
                const len = param.index.length;
                let repeat = false;
                param.index.forEach((index) => {
                    const className = pageContainer.assets?.[index]?.meta.className;
                    const t = pageContainer.assetsMap[className];
                    const select = this.canvas.getActiveObject();
                    if (select?.data?.className !== className && (!t?.groupName || t?.type === 'group')) {
                        if (t?.target && t?.hasAdd) {
                            if (len === 1 || !t.asset.meta.locked) {
                                activeObjects.push(t?.target);
                            }
                        } else {
                            repeat = true;
                        }
                    }
                });
                if (activeObjects.length > 1) {
                    this.setCanvasActiveObject(activeObjects);
                } else if (activeObjects.length === 1) {
                    this.canvas.setActiveObject(activeObjects[0]);
                    this.canvas.requestRenderAll();
                } else {
                    if (repeat) {
                        this.syncSelectAsset(param);
                        return;
                    }
                }
            }
            this.isSyncSelect = false;
            clearTimeout(this._syncSelectTimer);
        }, 200);
    }

    selectPageAllAssets() {
        const page = this.pageContainerMap[this.currentPage];
        const targets: fabric.Object[] = [];
        for (const className in page.assetsMap) {
            if (
                page.assetsMap[className].target &&
                page.assetsMap[className].target?.data?.type !== 'background' &&
                (!page.assetsMap[className].groupName || page.assetsMap[className].type === 'group')
            ) {
                targets.push(page.assetsMap[className].target as fabric.Object);
            }
        }
        const selection = new fabric.ActiveSelection(targets, {
            canvas: this.canvas,
        });
        this.canvas.discardActiveObject();
        this.canvas.setActiveObject(selection);
        this.canvas.requestRenderAll();
    }

    // 是否显示文本框，ai生成海报设计师编辑器默认显示，其他情况默认关闭
    switchAiDesignerTextRect(show: boolean) {
        for (const pageKey in this.pageContainerMap) {
            const page = this.pageContainerMap[pageKey];
            for (const className in page.assetsMap) {
                if (
                    page.assetsMap[className].target &&
                    page.assetsMap[className].target.data?.type === 'text'
                ) {
                    if (show) {
                        page.assetsMap[className]?.helpAiDesignRect?.setOptions({visible: true});
                    } else {                    
                        page.assetsMap[className]?.hideHelpAiDesignRect?.();
                    }
                }
            }
        }
        this.canvas.requestRenderAll();
    }
}
