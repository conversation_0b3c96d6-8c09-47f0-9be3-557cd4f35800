import { TgsCanvasPageRenderOnly } from '../Page';
import { TgsCanvasBase } from './TgsCanvasBase';
import { DataFormat } from '../../../dataFormat';
import equal from 'fast-deep-equal';
import { fabric } from 'fabric';
import { IText } from '@tgs/ips_text';
import { Debounce } from '../../../decorator/common';
import { TCanvasAssetRenderOnly } from '../../../render/assets';
import { ITextAsset } from '@tgs/types';

interface IPageContainerMap {
    [key: string | number]: TgsCanvasPageRenderOnly;
}

export class TgsCanvasRenderOnly extends TgsCanvasBase {
    override pageContainerMap: IPageContainerMap = {};
    override filmStripPageMap: IPageContainerMap = {};

    protected pageAttr!: Tgs.IPageAttr;
    protected helpSelectControlRect: fabric.Rect | undefined;
    override readOnly = true;
    protected currentBackgroundColor?: fabric.StaticCanvas['backgroundColor'];

    async init(param: {
        canvas: Tgs.ICanvas;
        pages: Tgs.IPage[];
        pageAttr: Tgs.IPageAttr;
        pageIndex: number;
        currentPage: number;
        mode: string;
        readOnly?: boolean;
    }) {
        this.pageAttr = DataFormat.formatPageAttr(param.pageAttr);
        if (param.mode === 'board') {
            this.currentBackgroundColor = this.canvas.backgroundColor
            this.canvas.setBackgroundColor(this.dotPattern, this.canvas.requestRenderAll.bind(this.canvas));
        }
        this.renderMode = param.mode;
        this.readOnly = true;
        this.currentPage = param.currentPage;

        // 初始页面及其缩放，根据浏览器视口大小
        this.createAllPages(param.pages, param.pageAttr, param.canvas, param.mode, param.pageIndex);

        this.updateCanvas(param.canvas);
    }

    updateCanvas(canvas: Tgs.ICanvas) {
        canvas = DataFormat.formatCanvas(canvas);
        if (this.canvasInfo.scale !== canvas.scale) {
            this.canvas.setZoom(canvas.scale);
        }
        if (this.canvasInfo.width !== canvas.width || this.canvasInfo.height !== canvas.height) {
            for (const i in this.pageContainerMap) {
                const top = i ? (canvas.height + this.baseGap / canvas.scale) * Number(i) : 0;
                this.pageContainerMap[i].updatePageSize(canvas.width, canvas.height, top);
            }
            // this.page.updatePageSize(canvas.width, canvas.height);
            if (this.pageContainerMap?.[this.currentPage]?.page) {
                this.centerPage(this.pageContainerMap[this.currentPage].page);
            }
        }
        if (this.canvasInfo.x !== canvas.x || this.canvasInfo.y !== canvas.y) {
            this.setPagePosition(canvas);
        }
        this.canvasInfo = canvas;
        this.updateAllPageOffsetY();
        this.canvas.requestRenderAll();
    }

    /* 更新页面背景等以及元素 */
    protected updatePageBackground(
        item: TgsCanvasPageRenderOnly,
        index: number,
        pageAttr: Tgs.IPageAttr,
        page?: Tgs.IPage,
    ) {
        let backgroundColor = pageAttr.backgroundColor?.[index] || { r: 255, g: 255, b: 255, a: 1 };
        let isOpacityBg = pageAttr.backgroundOpacity?.[index] || false;
        if (page) {
            backgroundColor = page.backgroundColor as Tgs.IColor;
            isOpacityBg = page.isOpacityBg as boolean;
        }
        item.updateBackground({
            backgroundColor: backgroundColor,
            backgroundOpacity: isOpacityBg,
            backgroundImage: pageAttr.backgroundImage?.[index] || { resId: undefined },
        });
    }

    /** 更新页面间距 */
    protected updateAllPageOffsetY() {
        // 根据模式来
        if (this.renderMode === '') {
            for (const index in this.pageContainerMap) {
                const item = this.pageContainerMap[index];
                this.updatePageTop(item, Number(index));
            }
        }
    }

    /** 更新单页页面间距 没有动画版*/
    protected updatePageTop(item: TgsCanvasPageRenderOnly, pageIndex?: number) {
        if (pageIndex === undefined) {
            pageIndex = item.pageConfig.pageIndex;
        }
        const zoom = this.canvas.getZoom();
        const gapY = this.baseGap / zoom;
        const { height = 1000 } = item.page;
        const top = pageIndex ? (height + gapY) * Number(pageIndex) : 0;
        item.updatePageTop(top);
        item.updateAssetScaleCoordinate(pageIndex);
    }

    /** 更新渲染模式*/
    updateRenderMode(options: {
        canvas: Tgs.ICanvas;
        pages: Tgs.IPage[];
        pageAttr: Tgs.IPageAttr;
        pageIndex: number;
        mode: string;
        isSamePageHash?: boolean;
    }) {
        if (options.mode === 'pull' || options.mode === 'board') {
            // this.currentPage = 0
            const { pageIndex, pages } = options;
            for (const index in this.pageContainerMap) {
                const item = this.pageContainerMap[index];
                const { pageIndex: pageNumber } = item.pageConfig;
                if (pageNumber === pageIndex) {
                    item.setPageAndAssetsVisible(true);
                    item.updateAssets(pages[pageIndex].assets, pageIndex);
                    this.currentPage = pageIndex;
                    this.updatePageBackground(item, pageIndex, options.pageAttr, pages[pageIndex]);
                    this.updatePageTop(item, 0);
                    if (!this.readOnly) {
                        !options.isSamePageHash && this.updatePagePosition(item.page, true);
                    }
                    this.filmStripPageMap[0] = item;
                } else {
                    // 将不需要显示的页面隐藏
                    item.setPageAndAssetsVisible(false);
                }
            }
            this.canvas.requestRenderAll();
        }
    }

    protected async createAllPages(
        pages: Tgs.IPage[],
        pageAttr: Tgs.IPageAttr,
        canvas: Tgs.ICanvas,
        mode: string,
        pageIndex: number,
    ) {
        const { backgroundColor = [{ r: 255, g: 255, b: 255, a: 1 }], pageHash } = pageAttr;
        const { height, width } = canvas;
        const zoom = this.canvas.getZoom();
        const gapY = this.baseGap / zoom;
        for (const key in pageHash) {
            const index = Number(pageHash[key]);
            const page = pages[index];
            const color = backgroundColor[index];
            const top = index ? (height + gapY) * index : 0;
            const pageContainer = this.createPages({ hash: key, index, color, width, height, top });
            this.pageContainerMap[index] = pageContainer;
            this.pageContainerMap[index].addToCanvas();
            this.renderZIndex.setPageZIndex(index, pageContainer.page);
            await pageContainer.updateAssets(page.assets, index);
            pageContainer.updateBackground({
                backgroundColor: pageAttr.backgroundColor?.[index] || { r: 255, g: 255, b: 255, a: 1 },
                backgroundOpacity: pageAttr.backgroundOpacity?.[index] || false,
                backgroundImage: pageAttr.backgroundImage?.[index] || { resId: undefined },
            });
        }
        if (this.renderMode === 'pull' || this.renderMode === 'board') {
            this.updateRenderMode({ canvas, pages, pageAttr, pageIndex, mode: this.renderMode });
        } else {
            this.updateAllPageOffsetY();
        }
    }

    /* 创建单个页面 */
    protected createPages(params: {
        hash: string;
        index: number;
        color: Tgs.IColor;
        width: number;
        height: number;
        top: number;
    }) {
        const { hash, index, color, width, height, top } = params;
        const pageContainer = new TgsCanvasPageRenderOnly(
            this,
            this.canvas,
            {
                left: 0,
                top: top,
                width: width,
                height: height,
                backgroundColor: color,
                pageIndex: index,
                pageHash: hash,
            },
            this.actions,
            this.renderZIndex,
            this.readOnly,
        );
        return pageContainer;
    }
    /** 更新页元素 添加多页*/
    // @CommonDecorator.Debounce(500,{leading:true,trailing:true})
    updatePages(options: {
        canvas: Tgs.ICanvas;
        pages: Tgs.IPage[];
        pageAttr: Tgs.IPageAttr;
        pageIndex: number;
        mode: string;
    }) {
        const { pages, pageAttr, canvas, mode } = options;
        const len = Object.keys(this.pageContainerMap).length;
        const isForce = this.renderMode !== mode;
        const pageIndexChange = this.currentPage !== options.pageIndex;
        this.currentPage = options.pageIndex;
        if (mode === 'board' && this.renderMode !== 'board') {
            this.currentBackgroundColor = this.canvas.backgroundColor
            this.canvas.setBackgroundColor(this.dotPattern, this.canvas.requestRenderAll.bind(this.canvas));
        } else if (mode !== 'board' && this.renderMode === 'board') {
            if (this.currentBackgroundColor) {
                this.canvas.setBackgroundColor(this.currentBackgroundColor, this.canvas.requestRenderAll.bind(this.canvas));
            }
        }
        this.renderMode = mode;
        const isSamePageHash = equal(this.pageAttr.pageHash, pageAttr.pageHash);
        if (!isSamePageHash) {
            this.pageAttr = DataFormat.formatPageAttr(pageAttr);
        }
        // if (isSamePageHash && !isForce) {
        //     for (const i in this.pageContainerMap) {
        //         this.pageContainerMap[i].updateAssets(pages[Number(i)].assets, Number(i));
        //     }
        //     return;
        // }
        // 数量相等,增删改pageContainerMap都需要更新
        if (len === pages.length) {
            if (pageAttr.pageHash && !isSamePageHash) {
                const newPageMap: Record<
                    string,
                    { newIndex: number; oldIndex: number; pageContainer: TgsCanvasPageRenderOnly }
                > = {};
                for (const key in this.pageContainerMap) {
                    const hash = this.pageContainerMap[key].pageConfig.pageHash;
                    if (pageAttr.pageHash && pageAttr.pageHash[hash] >= 0) {
                        const oldIndex = this.pageContainerMap[key].pageConfig.pageIndex;
                        newPageMap[hash] = {
                            newIndex: pageAttr.pageHash[hash],
                            oldIndex,
                            pageContainer: this.pageContainerMap[key],
                        };
                        if (pageAttr.pageHash[hash] !== oldIndex) {
                            delete this.pageContainerMap[oldIndex];
                            this.renderZIndex.removePageZIndex(oldIndex);
                        }
                    }
                }
                for (const key in newPageMap) {
                    const pageContainerInfo = newPageMap[key];
                    const newIndex = pageContainerInfo.newIndex;
                    const pageContainer = pageContainerInfo.pageContainer;
                    pageContainer.pageConfig.pageIndex = newIndex;
                    pageContainer.page.name = 'page_' + newIndex;
                    pageContainer.pageConfig.pageHash = key;
                    this.pageContainerMap[newIndex] = pageContainer;
                    this.renderZIndex.setPageZIndex(newIndex, pageContainer.page);

                }
            }
            if (mode === 'pull' || mode === 'board') {
                this.updateRenderMode({ ...options, isSamePageHash });
            } else {
                let centerPage!: TgsCanvasPageRenderOnly;

                for (const index in this.pageContainerMap) {
                    const item = this.pageContainerMap[index];
                    const { pageIndex: pageNumber } = item.pageConfig;
                    const page = pages[pageNumber];
                    if (options.pageIndex === pageNumber) {
                        centerPage = item;
                        this.currentPage = pageNumber;
                    }
                    item.setPageAndAssetsVisible(true);
                    item.updateAssets(page.assets, pageNumber);
                    this.updatePageBackground(item, pageNumber, pageAttr, page);
                    item.sortWaterMark()
                }
                // 模式切换，需要重新计算每个页面的位置
                this.updateAllPageOffsetY();
                !isSamePageHash && centerPage && this.updatePagePosition(centerPage?.page, isForce);
                isForce && this.blurSelect(); // 切换模式元素失去焦点
            }
        } else if (len > pages.length) {
            // 根据hash查找要删除的页面
            let delPage: TgsCanvasPageRenderOnly | undefined;
            if (pageAttr.pageHash) {
                for (const key in this.pageContainerMap) {
                    const pageContainer = this.pageContainerMap[key];
                    const hash = this.pageContainerMap[key].pageConfig.pageHash;
                    if (pageAttr.pageHash[hash] === undefined) {
                        delPage = pageContainer;
                        delPage.page.name = `page_delete`;
                        if (delPage) {
                            delPage.destroy();
                            delPage.removeFromCanvas();
                            this.renderZIndex.removePageZIndex(delPage.pageConfig.pageIndex);
                        }
                        delete this.pageContainerMap[key];
                    } else {
                        const index = pageAttr.pageHash[hash];
                        const oldPageIndex = pageContainer.pageConfig.pageIndex;
                        if (index !== oldPageIndex) {
                            pageContainer.pageConfig.pageIndex = index;
                            pageContainer.page.name = `page_${index}`;
                            this.pageContainerMap[index] = pageContainer;
                            delete this.pageContainerMap[oldPageIndex];
                        }
                    }
                }
            }
            if (pageAttr.pageHash) {
                for (const key in this.pageContainerMap) {
                    const pageContainer = this.pageContainerMap[key];
                    const hash = pageContainer.pageConfig.pageHash;
                    const index = pageAttr.pageHash[hash];
                    pageContainer.pageConfig.pageIndex = index;
                    this.pageContainerMap[index] = pageContainer;
                    this.renderZIndex.setPageZIndex(index, pageContainer.page);
                }
            }
        } else if (len < pages.length) {
            const zoom = this.canvas.getZoom();
            const gapY = this.baseGap / zoom;
            // 添加页面
            const { pageHash } = pageAttr;
            const newPageMap: Record<
                string,
                { newIndex: number; oldIndex: number; pageContainer: TgsCanvasPageRenderOnly }
            > = {};
            for (const key in this.pageContainerMap) {
                const hash = this.pageContainerMap[key].pageConfig.pageHash;
                if (pageHash && pageHash[hash] >= 0) {
                    const oldIndex = this.pageContainerMap[key].pageConfig.pageIndex;
                    newPageMap[hash] = {
                        newIndex: pageHash[hash],
                        oldIndex,
                        pageContainer: this.pageContainerMap[key],
                    };
                    if (pageHash[hash] !== oldIndex) {
                        delete this.pageContainerMap[oldIndex];
                        this.renderZIndex.removePageZIndex(oldIndex);
                    }
                } else if (pageHash && !pageHash[hash]) {
                    const delPage = this.pageContainerMap[key];
                    delPage.destroy();
                    this.renderZIndex.removePageZIndex(delPage.pageConfig.pageIndex);
                    delPage.removeFromCanvas();
                    delete this.pageContainerMap[key];
                }
            }
            const newPageHashs = [];
            for (const key in pageHash) {
                const index = pageHash[key];
                const pageContainerInfo = newPageMap[key];
                if (pageContainerInfo && pageContainerInfo.newIndex !== pageContainerInfo.oldIndex) {
                    const newIndex = pageContainerInfo.newIndex;
                    const pageContainer = pageContainerInfo.pageContainer;
                    pageContainer.pageConfig.pageIndex = newIndex;
                    pageContainer.page.name = 'page_' + newIndex;
                    // 如果是0就不用更新
                    if (index !== 0) {
                        this.updatePageTop(pageContainer);
                    }
                    this.pageContainerMap[index] = pageContainer;

                    pageContainer.updateAssets(pages[index].assets, index);

                    this.updatePageBackground(pageContainer, index, pageAttr, pages[index]);
                    this.renderZIndex.setPageZIndex(index, pageContainer.page);
                } else if (!pageContainerInfo) {
                    newPageHashs.push({ key, index });
                }
            }
            newPageHashs.forEach(({ key, index }) => {
                const pageContainer = this.createPages({
                    hash: key,
                    index: index,
                    color: pageAttr.backgroundColor?.[index] || { r: 255, g: 255, b: 255, a: 1 },
                    width: canvas.width,
                    height: canvas.height,
                    top: index ? (canvas.height + gapY) * index : 0,
                });
                if (index !== 0) {
                    this.updatePageTop(pageContainer);
                }

                pageContainer.updateAssets(pages[index].assets, index);
                this.updatePageBackground(pageContainer, index, pageAttr, pages[index]);

                this.pageContainerMap[index] = pageContainer;
                this.pageContainerMap[index].addToCanvas();
                if (this.showWaterMark) {
                    this.pageContainerMap[index].addWaterMark();
                }
                this.renderZIndex.setPageZIndex(index, pageContainer.page);
                if (mode === 'pull' || mode === 'board') {
                    this.updateRenderMode(options);
                } else if (index === options.pageIndex) {
                    this.centerAddPageAnim(pageContainer.page);
                }
            });
        }
    }
    getPageByPageIndex(pageIndex: number) {
        return Object.values(this.pageContainerMap).filter((item) => item.pageConfig.pageIndex === pageIndex);
    }
    /** 同步选中，目前只支持单选 */
    syncSelectAsset(param: { index: number[]; pageIndex: number }) {
        if (this._syncSelectTimer) {
            clearTimeout(this._syncSelectTimer);
        }
        this._syncSelectTimer = setTimeout(() => {
            const pageContainer = this.pageContainerMap[param.pageIndex];
            const asset = pageContainer?.assets[param.index[0]];
            const t = pageContainer.assetsMap[asset.meta.className];
            if (!this.helpSelectControlRect) {
                this.helpSelectControlRect = new fabric.Rect();
                this.canvas.add(this.helpSelectControlRect);
            }
            if (t && t.target) {
                if (t.asset.meta.index == 0) {
                    return this.clearSelectStatus();
                }
                const { top, left } = t.target.getBoundingRect();
                const scale = this.canvas.getZoom();
                const { attribute } = t.asset;
                const width = attribute.width;
                const height = attribute.height;
                this.helpSelectControlRect.setOptions({
                    top: top - 1,
                    left: left - 1,
                    width: width + 1,
                    height: height + 1,
                    fill: 'transparent',
                    stroke: 'red',
                    strokeWidth: Math.round(2 / scale),
                });
                this.helpSelectControlRect.setCoords();
                this.helpSelectControlRect.setPositionByOrigin(
                    { x: left - 1 + (width + 1) / 2, y: top - 1 + (height + 1) / 2 } as fabric.Point,
                    'center',
                    'center',
                );
                this.helpSelectControlRect.rotate(t.target.angle ?? 0);
                this.helpSelectControlRect.bringToFront();
                const assetTarget = t as TCanvasAssetRenderOnly & { renderTarget: fabric.Object };
                if (assetTarget.renderTarget && 'renderCursorForAnimate' in assetTarget.renderTarget) {
                    (assetTarget.renderTarget as unknown as IText).renderCursorForAnimate();
                }
                this.canvas.requestRenderAll();
            }
            this.clearSelectStatus();
        }, 10) as unknown as number;
    }
    @Debounce(150, { leading: false, trailing: true })
    clearSelectStatus(remove?: boolean) {
        if (this.helpSelectControlRect) {
            this.helpSelectControlRect.setOptions({
                top: 0,
                left: 0,
                width: 0,
                height: 0,
            });
            if (remove) {
                this.canvas.remove(this.helpSelectControlRect);
                this.helpSelectControlRect = undefined;
            }
            this.canvas.requestRenderAll();
        }
    }
}
