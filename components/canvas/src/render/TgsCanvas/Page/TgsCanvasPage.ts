import {
    ChartAsset,
    FrameAsset,
    GroupAsset,
    ImageAsset,
    BackgroundAsset,
    QRCodeAsset,
    SvgAsset,
    TCanvasAsset,
    TableAsset,
    TextAsset,
    VideoEAsset,
    FlowAsset,
    LineAsset,
} from '../../assets';
import { TgsCanvasPageAssets } from './TgsCanvasPageAssets';

/** 画布容器分页类, 只管理元素的更新 */
export class TgsCanvasPage extends TgsCanvasPageAssets {
    hasInitAssets = false;
    pageIndex = 0;

    /** 渲染元素对象缓存管理 */
    override assetsMap = {} as Record<string, TCanvasAsset>;

    override addAsset(asset: Tgs.IAsset<Tgs.TAssetType>, index: number, pageIndex: number) {
        if (this.assetsMap[asset.meta.className]) {
            return;
        } else {
            const options = {
                page: this,
                clipPage: this.page,
                mainCanvas: this.mainCanvas,
                actions: this.actions,
                renderZIndex: this.renderZIndex,
                index,
                pageIndex,
            };
            switch (asset.meta.type) {
                case 'chart': {
                    const target = new ChartAsset(options, asset as Tgs.IAsset<Tgs.TChartAssetType>);
                    this.assetsMap[asset.meta.className] = target;
                    break;
                }
                case 'image':
                case 'pic': {
                    const target = new ImageAsset(options, asset as Tgs.IAsset<Tgs.TImageAssetType>);
                    this.assetsMap[asset.meta.className] = target;
                    break;
                }
                case 'background': {
                    if (this.page.pageType === 'board') {
                        const target = new ImageAsset(options, asset as Tgs.IAsset<Tgs.TImageAssetType>);
                        this.assetsMap[asset.meta.className] = target;
                    } else {
                        const target = new BackgroundAsset(options, asset as Tgs.IAsset<Tgs.TImageAssetType>);
                        this.assetsMap[asset.meta.className] = target;
                    }
                    break;
                }
                case 'group':
                    const target = new GroupAsset(options, asset as Tgs.IAsset<Tgs.TGroupAssetType>);
                    this.assetsMap[asset.meta.className] = target;
                    break;
                case 'qrcode': {
                    const target = new QRCodeAsset(options, asset as Tgs.IAsset<Tgs.TQRCodeAssetType>);
                    this.assetsMap[asset.meta.className] = target;
                    break;
                }
                case 'SVG': {
                    const target = new SvgAsset(options, asset as Tgs.IAsset<Tgs.TSvgAssetType>);
                    this.assetsMap[asset.meta.className] = target;
                    break;
                }
                case 'table': {
                    const target = new TableAsset(options, asset as Tgs.IAsset<Tgs.TTableAssetType>);
                    this.assetsMap[asset.meta.className] = target;
                    break;
                }
                case 'text': {
                    const target = new TextAsset(options, asset as Tgs.IAsset<Tgs.TTextAssetType>);
                    this.assetsMap[asset.meta.className] = target;
                    break;
                }
                case 'videoE': {
                    const target = new VideoEAsset(options, asset as Tgs.IAsset<Tgs.TVideoEAssetType>);
                    this.assetsMap[asset.meta.className] = target;
                    break;
                }
                case 'frame': {
                    const target = new FrameAsset(options, asset as Tgs.IAsset<Tgs.TFrameAssetType>);
                    this.assetsMap[asset.meta.className] = target;
                    break;
                }
                case 'flow': {
                    const target = new FlowAsset(options, asset as Tgs.IAsset<Tgs.TFlowAssetType>);
                    this.assetsMap[asset.meta.className] = target;
                    break;
                }
                case 'line': {
                    const target = new LineAsset(options, asset as Tgs.IAsset<Tgs.TLineAssetType>);
                    this.assetsMap[asset.meta.className] = target;
                    break;
                }
                default: {
                    return;
                }
            }
        }
    }

    initAssetsOnNeed() {
        try {
            if (!this.hasInitAssets) {
                if (this.tgsCanvas.renderMode === 'board') {
                    if (this.pageIndex === this.tgsCanvas.currentPage) {
                        if (this.page.visible && this.page.isOnScreen()) {
                            this.updatePageAssetsMap(this.assets, this.pageIndex);
                        }
                    }
                } else if (this.page.isOnScreen()) {
                    this.updatePageAssetsMap(this.assets, this.pageIndex);
                }
            }
        } catch (error) {
            console.error('updatePageAssetsMap error:', error);
        }
    }

    override async updateAssets(assets: Tgs.IAsset<Tgs.TAssetType>[], pageIndex: number) {
        if (this.assets.length === assets.length && this.assets === assets) {
            return true;
        }
        this.assets = assets;
        this.pageIndex = pageIndex;

        try {
            if (!this.hasInitAssets) {
                if (this.tgsCanvas.renderMode === 'board') {
                    if (this.pageIndex === this.tgsCanvas.currentPage) {
                        if (this.page.visible && this.page.isOnScreen()) {
                            await this.updatePageAssetsMap(assets, pageIndex);
                        }
                    }
                } else {
                    if (this.page.isOnScreen()) {
                        await this.updatePageAssetsMap(assets, pageIndex);
                    }
                }
            } else {
                await this.updatePageAssetsMap(assets, pageIndex);
            }
        } catch (error) {
            console.error('updatePageAssetsMap error:', error);
        }

        return true;
    }

    async updatePageAssetsMap(assets: Tgs.IAsset<Tgs.TAssetType>[], pageIndex: number) {
        this.hasInitAssets = true;
        let needUpdateCount = 0;
        let hasUpdateCount = 0;
        const newPageAssetsMap: typeof this.pageAssetsMap = {};
        assets.forEach(async (a, index) => {
            newPageAssetsMap[a.meta.className] = a;
            if (this.pageAssetsMap[a.meta.className] === a) {
                const target = this.assetsMap[a.meta.className];
                target?.setIndexAndGroup(index, pageIndex, a);
                // return true;
            } else {
                needUpdateCount++;
                await this.updateAsset(a, index, pageIndex);
                hasUpdateCount++;
                if (needUpdateCount === hasUpdateCount) {
                    // TODO 统计更新完成
                }
            }
        });
        const removeAssets: string[] = [];
        for (const className in this.pageAssetsMap) {
            if (!newPageAssetsMap[className]) {
                removeAssets.push(className);
            }
        }
        if (removeAssets.length > 0) {
            this.removeAssets(removeAssets);
        }
        this.pageAssetsMap = newPageAssetsMap;
    }
}
