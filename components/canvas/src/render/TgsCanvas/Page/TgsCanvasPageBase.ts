import { fabric } from 'fabric';
import { klona } from 'klona';
import { TCanvasAsset, TCanvasAssetRenderOnly } from '../../assets';
import type { Actions } from '../../../actions/Actions';
import { Util } from '../../../util';
import type { RenderZIndex } from '../../RenderZIndex';
import type { TgsCanvasBase } from '../Canvas';
import { waterMarkBase64 } from './waterMark';
import { circleGrayIconData, nearObjectIconData} from '../../IconData';

type TTgsCanvasPageConfig = {
    left: number;
    top: number;
    width: number;
    height: number;
    backgroundColor?: Tgs.IColor;
    pageIndex: number;
    pageHash: string;
    backgroundImage?: {
        resId: string;
    };
};

type TBackgroundImage = {
    resId?: string;
    rt_imageUrl?: string;
    backgroundSize?: {
        width: string | number;
        height: string | number;
    };
};

/** 画布容器分页类, 只管理页自身的渲染 */
export class TgsCanvasPageBase {
    mainCanvas: fabric.Canvas;

    readonly type = 'pageContainer';

    actions: Actions;
    renderZIndex: RenderZIndex;

    backgroundImage?: TBackgroundImage = undefined;

    key: string | undefined = undefined;

    // 裁切区域
    page: fabric.Rect;
    pageConfig: TTgsCanvasPageConfig;
    hasAdd = false;
    // 画布总控
    tgsCanvas: TgsCanvasBase;

    assetsMap: Record<string, TCanvasAsset | TCanvasAssetRenderOnly> = {};
    // 画布水印
    declare waterMarkTarget: fabric.Rect | null;
    declare waterMarkPattern: fabric.Pattern | null;
    waterMarkScaleFactor = 1.2;
    readOnly = false;

    startClosestObject: fabric.Object | null = null;
    startLinkPointerGroup: fabric.Group | null = null;
    startLinkCorner: 'ml' | 'mr' | 'mt' | 'mb' | undefined;

    endClosestObject: fabric.Object | null = null;
    endLinkCorner: 'ml' | 'mr' | 'mt' | 'mb' | undefined;
    endLinkPointerGroup: fabric.Group | null = null;

    constructor(
        tgsCanvas: TgsCanvasBase,
        mainCanvas: fabric.Canvas,
        pageConfig: TTgsCanvasPageConfig,
        actions: Actions,
        renderZIndex: RenderZIndex,
        readOnly?: boolean,
        visible: boolean = true,
    ) {
        this.tgsCanvas = tgsCanvas;
        this.mainCanvas = mainCanvas;
        this.actions = actions;
        this.renderZIndex = renderZIndex;
        this.pageConfig = pageConfig;
        this.readOnly = readOnly || false;
        const options: fabric.IRectOptions = {
            top: pageConfig.top || 0,
            left: pageConfig.left || 0,
            width: pageConfig.width || 10,
            height: pageConfig.height || 10,
            fill: pageConfig.backgroundColor ? Util.rgbaToString(pageConfig.backgroundColor) : '#fff',
        };
        const shadow = new fabric.Shadow({
            blur: 30,
            offsetX: 0,
            offsetY: 2,
            color: 'rgba(0,0,0,0.3)',
            nonScaling: true,
        });
        this.page = new fabric.Rect({
            data: {
                pageIndex: pageConfig.pageIndex,
                // type: 'page',
            },
            name: 'page_' + pageConfig.pageIndex,
            lockMovementX: true,
            lockMovementY: true,
            lockSkewingX: true,
            lockSkewingY: true,
            lockScalingX: true,
            lockScalingY: true,
            lockScalingFlip: true,
            lockUniScaling: true,
            lockRotation: true,
            selectable: false,
            hasControls: false,
            hasRotatingPoint: false,
            absolutePositioned: true,
            hoverCursor: 'initial',
            hasBorders: true,
            objectCaching: false,
            shadow: this.readOnly ? undefined : shadow,
            visible,
            ...options,
        });
        this.bindEvents();
    }

    addToCanvas() {
        if (!this.hasAdd) {
            this.hasAdd = true;
            this.mainCanvas.add(this.page);
        }
    }

    removeFromCanvas() {
        if (this.hasAdd) {
            this.hasAdd = false;
            this.mainCanvas.remove(this.page);
        }
    }

    updateBackground(param: {
        backgroundColor: Tgs.IColor;
        backgroundOpacity: boolean;
        backgroundImage: TBackgroundImage;
    }) {
        if (param.backgroundImage?.resId && param.backgroundImage?.resId !== this.backgroundImage?.resId) {
            this.backgroundImageToPattern(param.backgroundImage);
            this.backgroundImage = klona(param.backgroundImage);
        } else if (param.backgroundOpacity && param.backgroundColor.a === 0) {
            this.backgroundImage = undefined;
            this.backgroundImageToPattern({
                resId: 'opacity',
                rt_imageUrl: 'https://s.tuguaishou.com/image/picEditor/opacity.png',
                backgroundSize: {
                    width: 40,
                    height: 40,
                },
            });
            this.mainCanvas.requestRenderAll();
        } else if (param.backgroundColor && !param.backgroundImage?.resId) {
            this.backgroundImage = undefined;
            this.page.setOptions({
                fill: Util.rgbaToString(param.backgroundColor),
            });
            this.mainCanvas.requestRenderAll();
        }
    }

    updatePageTop(top: number) {
        this.page.setOptions({
            top,
        });
        this.page.setCoords();
        this.waterMarkTarget?.setOptions({
            top,
        });
        this.waterMarkTarget?.setCoords();
    }

    updatePageSize(width: number, height: number, top: number) {
        this.page.setOptions({
            width,
            height,
            top,
        });
        this.pageConfig = {
            ...this.pageConfig,
            width,
            height,
            top,
        };
        this.page.setCoords();
        this.updateWaterMarkSize(width, height, top);
    }

    setPageType(pageType?: Tgs.TPageType) {
        this.page.setOptions({
            pageType,
            opacity: pageType === 'board' ? 0 : this.page.opacity,
        });
    }

    bindEvents() {
        this.bindRightClick();
    }
    addWaterMark() {
        if (this.waterMarkTarget) return;
        const img = new Image();
        img.crossOrigin = 'anonymous';
        this.waterMarkTarget = fabric.util.object.clone(this.page);
        img.onload = () => {
            if (!this.waterMarkTarget) return;
            const scale = this.mainCanvas.getZoom();
            const patterScale = this.waterMarkScaleFactor / scale;
            this.waterMarkPattern = new fabric.Pattern({
                crossOrigin: 'anonymous',
                source: img,
                repeat: 'repeat',
                patternTransform: [patterScale, 0, 0, patterScale, 0, 0],
            });
            this.waterMarkTarget.setOptions({
                evented: false,
                fill: this.waterMarkPattern,
                top: this.page.top,
            });
            this.mainCanvas.add(this.waterMarkTarget);
            this.mainCanvas.bringToFront(this.waterMarkTarget);
            this.mainCanvas.requestRenderAll();
        };
        img.src = waterMarkBase64;
    }
    sortWaterMark() {
        if (!this.waterMarkTarget) return;
        this.waterMarkTarget.setCoords();
        this.mainCanvas.bringToFront(this.waterMarkTarget);
        this.mainCanvas.requestRenderAll();
    }
    removeWaterMark() {
        if (this.waterMarkTarget) {
            this.mainCanvas.remove(this.waterMarkTarget);
            this.waterMarkTarget = null;
            this.waterMarkPattern = null;
        }
    }
    updateWaterMarkSize(width: number, height: number, top: number) {
        if (!this.waterMarkTarget || !this.waterMarkPattern) return;
        const scale = this.mainCanvas.getZoom();
        const img = this.waterMarkPattern.source as HTMLImageElement;
        const patterScale = this.waterMarkScaleFactor / scale;
        this.waterMarkPattern.patternTransform = [patterScale, 0, 0, patterScale, 0, 0];
        this.waterMarkPattern.setOptions({
            source: img,
        });
        this.waterMarkTarget.setOptions({
            width,
            height,
            top,
            fill: this.waterMarkPattern,
        });
      
    }
    protected bindRightClick() {
        this.page.on('rightclick', (e) => {
            const viewportTransform = this.mainCanvas.viewportTransform;
            const scale = this.mainCanvas.getZoom();
            this.actions.displayRightClickMenu(e.e as MouseEvent, {
                x: viewportTransform?.[4] as number,
                y: viewportTransform?.[5] as number,
                scale,
            });
        });
        this.page.on('line:mouse:up',()=>{
            this.startLinkPointerGroup?.setOptions({
                visible: false,
            });
            this.endLinkPointerGroup?.setOptions({
                visible: false,
            });
            this.endClosestObject = null;
            this.endLinkCorner = undefined;
            this.startClosestObject = null;
            this.startLinkCorner = undefined;
            this.mainCanvas.requestRenderAll();
        })
    }

    protected backgroundImageToPattern(backgroundImage: TBackgroundImage) {
        if (backgroundImage?.resId && backgroundImage?.rt_imageUrl) {
            const img = new Image();
            img.crossOrigin = 'anonymous';
            img.width = Number(backgroundImage.backgroundSize?.width);
            img.height = Number(backgroundImage.backgroundSize?.height);
            img.onload = () => {
                const pattern = new fabric.Pattern({
                    crossOrigin: 'anonymous',
                    source: img,
                    repeat: 'repeat',
                });
                this.page.setOptions({
                    fill: pattern,
                });
                this.mainCanvas.requestRenderAll();
            };
            img.src = backgroundImage.rt_imageUrl;
        }
    }
    convertPageToImage(options: Tgs.IConvertPageToImageOptions) {
        if (!this.page) return '';
        const { format, quality, enableRetinaScaling = false } = options;
        let left, top, right, bottom, width, height;
        if (this.page.pageType === 'board') {
            for (const key in this.assetsMap) {
                const size = this.assetsMap[key].target?.getBoundingRect();
                if (size) {
                    left = Math.min(left ?? size.left, size.left);
                    top = Math.min(top ?? size.top, size.top);
                    right = Math.max(right ?? size.left + size.width, size.left + size.width);
                    bottom = Math.max(bottom ?? size.top + size.height, size.top + size.height);
                }
            }
            if (left !== undefined && top !== undefined && right !== undefined && bottom !== undefined) {
                left -= 40;
                top -= 40;
                right += 40;
                bottom += 40;
                width = right - left;
                height = bottom - top;
            } else {
                ({ left, top } = this.page.getBoundingRect());
                width = Math.floor(this.pageConfig.width * this.mainCanvas.getZoom());
                height = Math.floor(this.pageConfig.height * this.mainCanvas.getZoom());
            }
        } else {
            ({ left, top } = this.page.getBoundingRect());
            width = Math.floor(this.pageConfig.width * this.mainCanvas.getZoom());
            height = Math.floor(this.pageConfig.height * this.mainCanvas.getZoom());
        }
        const currentBackgroundColor = this.mainCanvas.backgroundColor
        this.mainCanvas.setBackgroundColor('#fff', () => {});
        const dataUrl = this.mainCanvas.toDataURL({
            format: format,
            quality: quality,
            left: left,
            top: top,
            width: width,
            height: height,
            enableRetinaScaling,
            multiplier: 1,
        });
        if (currentBackgroundColor) {
            this.mainCanvas.setBackgroundColor(currentBackgroundColor, () => {});
        }
        this.mainCanvas.renderAll();
        return dataUrl;
    }
    getImageData() {
        if (!this.page) return '';
        const { left, top } = this.page.getBoundingRect();
        const width = Math.floor(this.pageConfig.width * this.mainCanvas.getZoom());
        const height = Math.floor(this.pageConfig.height * this.mainCanvas.getZoom());
        const imageData = this.mainCanvas.getContext().getImageData(left, top, width, height);
        return imageData
    }


    /** 获取当前pointer附近的object */
    getPointerNearObject(point: fabric.Point, key: 'start'|'end' = 'start', target?: fabric.Object) {
        const objects = this.assetsMap;
        const { x, y } = point;
        const zoom = this.mainCanvas.getZoom();
        // 检测吸附
        const snapDistance = Math.floor(15 / zoom); // 吸附距离
        let closestPoint = null,
            closestObject = null;
        let minDistance = snapDistance;
        let selectAnchor;
        const nearObject = key === 'start' ? this.startClosestObject : this.endClosestObject;
        for (const object of Object.values(objects)) {
            const otherObject = object.target as fabric.Object;
            if (otherObject === target || ['group', 'background', 'line'].includes(otherObject.data.type)) {
                continue;
            }
            const coords = object.getControlPositions();
            for (const i in coords) {
                const anchor = i as 'ml' | 'mr' | 'mt' | 'mb';
                const coord = coords[anchor];
                const dx = Math.abs(x - coord.x);
                const dy = Math.abs(y - coord.y);
                const distance = Math.sqrt(dx * dx + dy * dy);
                if (otherObject?.containsPoint(point)) {
                    closestObject = otherObject;
                }
                if (distance < minDistance) {
                    minDistance = distance;
                    closestPoint = coord;
                    closestObject = otherObject;
                    selectAnchor = anchor;
                }
            }
            if (closestPoint || closestObject) {
                if (nearObject && nearObject !== closestObject) {
                    this.removeLinkArrowHoverEffect(key);
                }
                this.setCloseObjectByKey(key, closestObject, selectAnchor);
                this.addLinkArrowHoverEffect(object, key);
                return closestPoint;
            } else if (nearObject) {
                this.removeLinkArrowHoverEffect(key);
            }
        }
        return null;
    }
    protected setCloseObjectByKey(key: 'start' | 'end', object: fabric.Object | null, corner: 'ml' | 'mr' | 'mt' | 'mb' | undefined) {
        if (key === 'start') {
            this.startClosestObject = object;
            this.startLinkCorner = corner;
        } else {
            this.endClosestObject = object;
            this.endLinkCorner = corner;
        }
    }

    /** 连接线hover展示ml mr mt mb*/
    protected addLinkArrowHoverEffect(object: any, key: 'start' | 'end' = 'start') {
        // 根据ml mr mt mb的位置分别创建圆形，形成一个组合
        const linkPointerGroup = key === 'start' ? this.startLinkPointerGroup : this.endLinkPointerGroup;
        const linkCorner = key === 'start' ? this.startLinkCorner : this.endLinkCorner;
        const offset = 4;
        const coords = object.getControlPositions();

        if (!linkPointerGroup) {
            const zoom = this.mainCanvas.getZoom();
            const circles: fabric.Image[] = [];
            for (const key in coords) {
                const point = coords[key];
                let style = { visible: true };
                if (linkCorner === key) {
                    style = { visible: false };
                }
                const image = Util.loadImage(nearObjectIconData)
                circles.push(
                    new fabric.Image(image, {
                        ...style,
                        name: key,
                        height: 8,
                        width: 8,
                        left: point.x - offset,
                        top: point.y - offset,
                        scaleX: 1 / zoom,
                        scaleY: 1 / zoom,
                        selectable: false,
                    }),
                );
            }
    
            if (key === 'start') {
                this.startLinkPointerGroup = new fabric.Group(circles, {
                    selectable: false,
                    visible: true,
                    name: 'linkPointerGroup_' + key,
                });;
                this.mainCanvas.add(this.startLinkPointerGroup);
            } else {
                this.endLinkPointerGroup = new fabric.Group(circles, {
                    selectable: false,
                    visible: true,
                    name: 'linkPointerGroup_' + key,
                });;
                this.mainCanvas.add(this.endLinkPointerGroup);
            }

        } else {
            // 根据coords的位置更新圆形的位置
            const {width, height, left, top} = object?.target;
            const circles = linkPointerGroup?.getObjects();

            for (const key in coords) {
                const circle = circles?.find(circle => circle.name === key);
                if (circle) {
                    let  left = 0, top = 0;
                    const visible = linkCorner !== key;
                    if (key === 'ml') {
                        left = -width / 2;
                        top = 0;
                    } else if (key === 'mr') {
                        left = width / 2;
                        top = 0;
                    } else if (key === 'mt') {
                        left = 0;
                        top = -height / 2;
                    } else if (key === 'mb') {
                        left = 0;
                        top = height / 2;
                    }
                    circle.setOptions({
                        left,
                        top,
                        visible
                    });
                }
            }
            linkPointerGroup?.setOptions({
                visible: true,
                left: left - offset*2,
                top: top - offset*2,
                width,
                height,
            });
        }
        this.mainCanvas.requestRenderAll();
    }

    protected removeLinkArrowHoverEffect(key: 'start' | 'end' = 'start') {
        if (key === 'start') {
            this.startClosestObject = null;
            this.startLinkCorner = undefined;
            this.startLinkPointerGroup?.setOptions({
                visible: false,
            });
        }
        if (key === 'end') {
            this.endClosestObject = null;
            this.endLinkCorner = undefined;
            this.endLinkPointerGroup?.setOptions({
                visible: false,
            });
        }
        this.mainCanvas.requestRenderAll();
    }
}
