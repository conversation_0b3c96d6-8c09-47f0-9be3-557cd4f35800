import {
    ChartRenderOnly,
    ImageRenderOnly,
    BackgroundAsset,
    QRCodeRenderOnly,
    SvgRenderOnly,
    TableRenderOnly,
    TextRenderOnly,
    FrameRenderOnly,
    VideoERenderOnly,
    TCanvasAssetRenderOnly,
    LineRenderOnly,
} from '../../assets';
import { TgsCanvasPageAssets } from './TgsCanvasPageAssets';

/** 画布容器分页类, 只管理元素的更新 */
export class TgsCanvasPageRenderOnly extends TgsCanvasPageAssets {
    override assetsMap = {} as Record<string, TCanvasAssetRenderOnly>;
    override bindEvents(): void {
        // do nothing
    }
    override addAsset(asset: Tgs.IAsset<Tgs.TAssetType>, index: number, pageIndex: number) {
        if (this.assetsMap[asset.meta.className]) {
            return;
        } else {
            const options = {
                page: this,
                clipPage: this.page,
                mainCanvas: this.mainCanvas,
                actions: this.actions,
                renderZIndex: this.renderZIndex,
                index,
                pageIndex,
                readOnly: true,
            };
            switch (asset.meta.type) {
                case 'chart': {
                    const target = new ChartRenderOnly(options, asset as Tgs.IAsset<Tgs.TChartAssetType>);
                    this.assetsMap[asset.meta.className] = target;
                    break;
                }
                case 'image':
                case 'pic': {
                    const target = new ImageRenderOnly(options, asset as Tgs.IAsset<Tgs.TImageAssetType>);
                    this.assetsMap[asset.meta.className] = target;
                    break;
                }
                case 'background': {
                    if (this.page.pageType === 'board') {
                        const target = new ImageRenderOnly(options, asset as Tgs.IAsset<Tgs.TImageAssetType>);
                        this.assetsMap[asset.meta.className] = target;
                    } else {
                        const target = new BackgroundAsset(options, asset as Tgs.IAsset<Tgs.TImageAssetType>);
                        this.assetsMap[asset.meta.className] = target;
                    }
                    break;
                }
                // case 'group':
                //     const target = new GroupAsset(options, asset as Tgs.IAsset<Tgs.TGroupAssetType>);
                //     this.assetsMap[asset.meta.className] = target;
                //     break;
                case 'qrcode': {
                    const target = new QRCodeRenderOnly(options, asset as Tgs.IAsset<Tgs.TQRCodeAssetType>);
                    this.assetsMap[asset.meta.className] = target;
                    break;
                }
                case 'SVG': {
                    const target = new SvgRenderOnly(options, asset as Tgs.IAsset<Tgs.TSvgAssetType>);
                    this.assetsMap[asset.meta.className] = target;
                    break;
                }
                case 'table': {
                    const target = new TableRenderOnly(options, asset as Tgs.IAsset<Tgs.TTableAssetType>);
                    this.assetsMap[asset.meta.className] = target;
                    break;
                }
                case 'text': {
                    const target = new TextRenderOnly(options, asset as Tgs.IAsset<Tgs.TTextAssetType>);
                    this.assetsMap[asset.meta.className] = target;
                    break;
                }
                case 'videoE': {
                    const target = new VideoERenderOnly(options, asset as Tgs.IAsset<Tgs.TVideoEAssetType>);
                    this.assetsMap[asset.meta.className] = target;
                    break;
                }
                case 'frame': {
                    const target = new FrameRenderOnly(options, asset as Tgs.IAsset<Tgs.TFrameAssetType>);
                    this.assetsMap[asset.meta.className] = target;
                    break;
                }
                case 'line': {
                    const target = new LineRenderOnly(options, asset as Tgs.IAsset<Tgs.TLineAssetType>);
                    this.assetsMap[asset.meta.className] = target;
                    break;
                }
                default: {
                    return;
                }
            }
        }
    }
}
