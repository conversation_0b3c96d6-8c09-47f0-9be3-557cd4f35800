declare const THREE: any;
declare const libtess: any;
declare const opentype: any;

// 避免引入 node type 的情况下使用 node 的全局变量
declare const process: {
    env: {
        NODE_ENV: 'development' | 'production';
    };
};

declare namespace fabric {
    interface IObjectOptions {
        /** 通过 object.container.inScreen 来简化渲染判断 */
        inScreen?: boolean;
        /** 增加用于 ctx.clip 的自定义属性 */
        container?: fabric.Object;
        id?: string;
        pageType?: Tgs.TPageType; 
    }
    interface Object {
        isNotVisible: () => boolean;
    }
    interface ICanvasOptions {
        pageIndex?: number; // 用于标记当前 画布所在的页码
        setPageIndex?: (index: number | undefined) => void; // 用于设置当前 画布所在的页码
        searchNotTransparentTargets?: (e: fabric.IEvent, pointer: fabric.Point) => fabric.Object | undefined; // 用于搜索不透明的对象
    }
}
