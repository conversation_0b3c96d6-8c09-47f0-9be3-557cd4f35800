{"name": "@tgs/general_components", "version": "0.2.16-beta", "description": "@tgs/general_components", "main": "dist/general_components.js", "module": "src/index.ts", "type": "module", "types": "lib/index.d.ts", "files": ["dist", "lib", "README.md"], "scripts": {"dev": "vite", "build": "tsc -p tsconfig.build.json && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "release": "pnpm publish --registry=http://*************:4873/ --no-git-checks"}, "dependencies": {"ahooks": "^3.7.2", "classnames": "^2.3.1", "js-cookie": "^3.0.5", "js-md5": "^0.7.3"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.2.0", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "react": "^17.0.0", "react-dom": "^17.0.0", "sass": "^1.32.8", "sass-loader": "^13.0.2", "typescript": "^5.2.2", "vite": "^5.0.0", "vite-plugin-css-injected-by-js": "^3.3.0"}}