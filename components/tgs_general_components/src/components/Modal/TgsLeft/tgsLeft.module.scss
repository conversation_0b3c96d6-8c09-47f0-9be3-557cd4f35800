.tgsLeftModal {
  position: relative;
  border-radius: 24px;
  // background: #FF3C7F;
  background: linear-gradient(169deg, #EF3964 3.9%, #E85C73 91.73%);
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  min-height: 500px;

  .tgsModalLeft {
    position: relative;
    display: flex;
    flex-direction: column;
    color: #FFF;
    font-family: "Source Han Sans CN";
    font-size: 35.667px;
    font-style: normal;
    font-weight: 500;
    width: calc(340px - 48px);
    padding: 37px 24px;

    .corner {
      position: absolute;
      bottom: 0;
      right: -30px;
      background-color: #fff;
      width: 60px;
      height: 100%;
      overflow: hidden;
      background: url("https://js.tuguaishou.com/ips_work_platform/images/corner.png") no-repeat;
      background-size: cover;
    }

    p {
      margin: 0;
      padding: 0;
    }

    .logo {
      width: 90px;

      img {
        display: inline-block;
        width: 100%;
      }
    }

    .slogan {
      padding-left: 12px;
      color: #FFF;
      font-family: "Source Han Sans CN";
      font-size: 35.667px;
      font-style: normal;
      font-weight: 500;
      line-height: 53.5px;
      /* 150% */
    }
    .shieldIcon {
        width:48px;
        height: 48px;
        margin: 74px auto 18px auto;
        position: relative;
        span {
            width: 48px;
            height: 48px;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
    .sloganWrap {
        p {
            color: #FFF;
            text-align: center;
            margin-bottom: 0;
            &:first-child {
                font-size: 30.314px;
                line-height: 45.47px;
                font-weight: 500;
            }
            &:last-child {
                font-size: 14px;
                line-height: 21px;
                margin-top: 8px;
                font-weight: 400;
            }
        }
    }
    .introWrap {
        margin-top: 68px;
        padding-left: 24px;
        p {
            color: #FFF;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            display: flex;
            align-items: center;
            margin-bottom: 18px;
            .icon {
                width: 14px;
                height: 14px;
                margin-right: 8px;
                svg {
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }

    .intro:nth-of-type(3) {
      margin-top: 90px;
    }

    .slogan:nth-of-type(1) {
      margin-top: 67px;
    }

    .intro {
      padding-left: 12px;
      color: #FFF;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 300;
      line-height: 21px;
      display: flex;
      align-items: center;
      margin-bottom: 18px;

      span {
        display: inline-block;
        margin-left: 5px;
      }


    }

  }

  .tgsModalRight {
    position: relative;
    background: #fff;
    flex-grow: 1;
    border-radius: 30px 24px 24px 0;
    overflow: hidden;
  }

  .tgsModalRightWrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    background: #ffffff;
    border-radius: 6px;
    height: 100%;


    input {
      outline: none;
      border: none;
    }

    .tgsModalTitle {
      margin-top: 48px;
      font-size: 16px;
      font-weight: 700;
      color: #1F1A1B;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 24px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      display: flex;
      align-items: center;

      svg {
        width: 24px;
        margin-right: 10px;
      }


      img {
        width: 100%;
        height: 100%;
      }
    }

    .tgsModalSubTitle {
      margin-top: 4px;
      color: var(---, #797676);
      /* 14/14-CN-Medium */
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 22px;
      /* 157.143% */
    }

    :global {
      .top {
        width: 100%;
        display: flex;
        align-items: center;
        flex-direction: column;
      }
    }
  }

  .tgsModalClose {
    position: absolute;
    right: 0;
    top: 0;
    width: 44px;
    height: 44px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background: rgb(232, 233, 232);
    border-radius: 4px;
    z-index: 1;

    svg {

      transition: all 0.3s linear;
      transform: rotate(0);
    }

    &:hover {
      background-color: rgb(232, 233, 232, .3);;
      svg {
        transform: rotate(180deg);
      }
    }
  }
}