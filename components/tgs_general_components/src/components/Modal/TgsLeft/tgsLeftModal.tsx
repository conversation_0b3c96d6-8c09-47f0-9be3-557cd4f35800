/***
 * 2024.10.31
 * tgs左右分栏风格的弹框，可复用：手机绑定、三次验证、四次验证
 */
import React, { ReactElement } from 'react';
import styles from './tgsLeft.module.scss';
import classNames from 'classnames';

export interface TgsLeftModalProps {
  className?: string
  title?: string
  subTitle?: string
  children: ReactElement
  closeable?: boolean
  close?: () => void
}

export const TgsLeftModal = (props: TgsModal.ITgsLeftProps) => {

  const Gou = <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <g clip-path="url(#clip0_1223_136)">
      <path d="M15.592 1.09595C10.728 4.07995 7.2 7.84795 5.608 9.73595L2.34507 7.17797C1.97906 6.89103 1.46354 6.89431 1.1012 7.18586L0.860288 7.37971C0.399574 7.75043 0.359425 8.4378 0.773846 8.85963L5.62783 13.8004C6.13646 14.3181 7.02116 14.1448 7.34477 13.4952C8.9139 10.3452 12.1589 5.57984 16 2.04795L15.592 1.09595Z" fill="white" />
    </g>
    <defs>
      <clipPath id="clip0_1223_136">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>

  const Tip = <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path d="M12 22C14.7614 22 17.2614 20.8807 19.0711 19.0711C20.8807 17.2614 22 14.7614 22 12C22 9.2386 20.8807 6.7386 19.0711 4.92893C17.2614 3.11929 14.7614 2 12 2C9.2386 2 6.7386 3.11929 4.92893 4.92893C3.11929 6.7386 2 9.2386 2 12C2 14.7614 3.11929 17.2614 4.92893 19.0711C6.7386 20.8807 9.2386 22 12 22Z" fill="#0EB52D" stroke="#0EB52D" stroke-width="2" stroke-linejoin="round" />
    <path d="M8 12L11 15L17 9" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
  </svg>

  const Close = <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
    <path d="M8.85637 10L3.15387 4.2975C3.0022 4.14583 2.91699 3.94012 2.91699 3.72562C2.91699 3.61942 2.93791 3.51425 2.97855 3.41613C3.0192 3.318 3.07877 3.22885 3.15387 3.15375C3.22897 3.07865 3.31813 3.01908 3.41625 2.97843C3.51437 2.93779 3.61954 2.91687 3.72575 2.91687C3.94024 2.91687 4.14595 3.00208 4.29762 3.15375L10.0001 8.85625L15.7026 3.15375C15.7777 3.07865 15.8669 3.01908 15.965 2.97843C16.0631 2.93779 16.1683 2.91687 16.2745 2.91687C16.3807 2.91687 16.4859 2.93779 16.584 2.97843C16.6821 3.01908 16.7713 3.07865 16.8464 3.15375C16.9215 3.22885 16.981 3.318 17.0217 3.41613C17.0623 3.51425 17.0833 3.61942 17.0833 3.72562C17.0833 3.83183 17.0623 3.937 17.0217 4.03512C16.981 4.13324 16.9215 4.2224 16.8464 4.2975L11.1439 10L16.8464 15.7025C16.998 15.8542 17.0833 16.0599 17.0833 16.2744C17.0833 16.4889 16.998 16.6946 16.8464 16.8462C16.6947 16.9979 16.489 17.0831 16.2745 17.0831C16.06 17.0831 15.8543 16.9979 15.7026 16.8462L10.0001 11.1437L4.29762 16.8462C4.14595 16.9979 3.94024 17.0831 3.72575 17.0831C3.51125 17.0831 3.30554 16.9979 3.15387 16.8462C3.0022 16.6946 2.91699 16.4889 2.91699 16.2744C2.91699 16.0599 3.0022 15.8542 3.15387 15.7025L8.85637 10Z" fill="#BABCBB" />
  </svg>

  const protectedIcon = <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 48 48" fill="none">
  <path opacity="0.8" d="M23.6924 2.53449C23.9358 2.48197 24.1895 2.49071 24.4297 2.56086L42.4209 7.81574C43.0604 8.0027 43.5 8.58984 43.5 9.25617V20.0345C43.4996 31.8212 36.0744 42.3062 25.0068 46.2415L24.4766 46.4241C24.169 46.5265 23.8359 46.5266 23.5283 46.4241C12.1654 42.6391 4.50018 32.0055 4.5 20.0286V9.25617C4.5 8.58963 4.94025 8.00252 5.58008 7.81574L23.5879 2.56086L23.6924 2.53449Z" fill="#0EB52D"/>
  <path d="M32.9394 16.9393C33.5252 16.3536 34.4747 16.3536 35.0605 16.9393C35.6463 17.5251 35.6463 18.4746 35.0605 19.0604L23.0605 31.0604C22.5115 31.6095 21.6428 31.6441 21.0537 31.1639L20.9394 31.0604L13.9394 24.0604L13.8359 23.9462C13.3558 23.357 13.3904 22.4884 13.9394 21.9393C14.4884 21.3903 15.3571 21.3557 15.9463 21.8358L16.0605 21.9393L22 27.8788L32.9394 16.9393Z" fill="white"/>
  </svg>

  return (
    <div className={styles.tgsLeftModal}>

      <div className={styles.tgsModalLeft}>
        <div className={styles.logo}><img src='//js.tuguaishou.com/ips_work_platform/images/logo-white.png' alt='图怪兽Logo' /></div>
        {/* <p className={styles.slogan}>人人都在用的</p>
        <p className={styles.slogan}>作图神器</p>
        <p className={styles.intro}>{Gou}<span>只需一步，极速安全验证</span></p>
        <p className={styles.intro}>{Gou}<span>205W+海量原创正版模版</span></p>
        <p className={styles.intro}>{Gou}<span>支付安全加密防护</span></p> */}
        <div className={styles.shieldIcon}>
            <span>{protectedIcon}</span>
        </div>
        <div className={styles.sloganWrap}>
            <p>充值安全验证</p>
            <p>为了您的安全充值，请填写验证信息</p>
        </div>
        <div className={styles.introWrap}>
            <p><span className={styles.icon}>{Tip}</span><span>图怪兽严格保障账号信息</span></p>
            <p><span className={styles.icon}>{Tip}</span><span>只需一步，极速安全验证</span></p>
            <p><span className={styles.icon}>{Tip}</span><span>图怪兽支付安全加密防护中</span></p>
        </div>
    
        <div className={styles.corner}></div>
      </div>

      <div className={styles.tgsModalRight}>
        <div className={classNames(styles.tgsModalRightWrap, props.className)}>
          <div className='top'>
            <div className={styles.tgsModalTitle}>
              {protectedIcon}{props.title || '图怪兽充值安全验证'}
            </div>
            <div className={styles.tgsModalSubTitle}>{props.subTitle}</div>
          </div>
          {props.children}
        </div>
      </div>
      {props.closeable === true && <div className={styles.tgsModalClose} onClick={props.close}>{Close}</div>}
    </div >

  );
}
