import React, { useState } from 'react';
import TencentValidateStyles from './scss/tgsLeftIndex.module.scss';
import { classNames } from '../../../utils';
import { bankCardRegx, idCardRegx } from '../../../utils';
import { TgsLeftModal } from '../TgsLeft';
type IValidateData = Record<
  string,
  {
    value: string;
    validRule?: (value: string) => boolean;
    emptyMsg: string;
    errorMsg?: string;
  }
>;

const protectedIcon = <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 48 48" fill="none">
<path opacity="0.8" d="M23.6924 2.53449C23.9358 2.48197 24.1895 2.49071 24.4297 2.56086L42.4209 7.81574C43.0604 8.0027 43.5 8.58984 43.5 9.25617V20.0345C43.4996 31.8212 36.0744 42.3062 25.0068 46.2415L24.4766 46.4241C24.169 46.5265 23.8359 46.5266 23.5283 46.4241C12.1654 42.6391 4.50018 32.0055 4.5 20.0286V9.25617C4.5 8.58963 4.94025 8.00252 5.58008 7.81574L23.5879 2.56086L23.6924 2.53449Z" fill="#0EB52D"/>
<path d="M32.9394 16.9393C33.5252 16.3536 34.4747 16.3536 35.0605 16.9393C35.6463 17.5251 35.6463 18.4746 35.0605 19.0604L23.0605 31.0604C22.5115 31.6095 21.6428 31.6441 21.0537 31.1639L20.9394 31.0604L13.9394 24.0604L13.8359 23.9462C13.3558 23.357 13.3904 22.4884 13.9394 21.9393C14.4884 21.3903 15.3571 21.3557 15.9463 21.8358L16.0605 21.9393L22 27.8788L32.9394 16.9393Z" fill="white"/>
</svg>

export const TgsLeftTencentValidateBox = (props: TgsModal.IValidateProps) => {
  const { phoneNum, submitValidate, onSubmit, getUniqueId, validateElementNums = 3, closeable, onClose } = props;
  const [username, setUserName] = useState('');
  const [cardId, setCardId] = useState('');
  const [validateStatus, setValidateStatus] = useState<number>(-1);
  const [errMsg, setErrMsg] = useState('');
  const [bankCard, setBankCard] = useState('');
  const [validateMsg, setValidateMsg] = useState('');
  const [errorItem, setErrorItem] = useState('');
  const submitClick = async () => {
    let status = validateStatus;
    switch (validateStatus) {
      // 验证达到上限
      case -2:
        break;
      case -1:
        status = await fetchValidate();
        break;
      case 0:
        // 验证失败,重新验证
        setValidateStatus(-1);
        break;
      case 1:
        // 验证成功
        break;
    }
    onSubmit && onSubmit(status);
  };
  const fetchValidate = async () => {
    try {
      const validateObj: IValidateData = {
        username: {
          value: username,
          emptyMsg: '真实姓名不能为空',
        },
        cardId: {
          value: cardId,
          validRule: (value: string) => idCardRegx.test(value),
          emptyMsg: '身份证号码不能为空',
          errorMsg: '身份证号码格式错误',
        },
      };
      if (validateElementNums && validateElementNums >= 4) {
        validateObj['bankCard'] = {
          value: bankCard,
          validRule: (value: string) => bankCardRegx.test(value),
          emptyMsg: '银行卡号不能为空',
          errorMsg: '银行卡号格式错误',
        };
      }
      await validateInput(validateObj);
      clearValidateInfo();
    } catch (error) {
      const { key, msg } = error as { key: string; msg: string };
      setValidateMsg(msg);
      setErrorItem(key);
      return;
    }
    if (submitValidate) {
      const res = await getUniqueId({ mobile: phoneNum, bank_card: bankCard.trim() });
      if (res.code == 1) {
        const { code, msg } = await submitValidate({
          mobile: phoneNum,
          card_id: cardId.trim(),
          unique_id: res?.data?.unique_id,
          bank_card: bankCard.trim(),
          name: username.trim(),
        });
        if (code !== 1) {
          setErrMsg(msg);
        }
        setValidateStatus(code);
        return code;
      } else {
        setValidateStatus(-3);
        setErrMsg(res?.msg);
        return -3;
      }
    }
  };
  const clearValidateInfo = () => {
    setValidateMsg('');
    setErrorItem('');
  };
  const validateInput = (validateData: IValidateData) => {
    return new Promise((res, rej) => {
      Object.entries(validateData).forEach(([key, info]) => {
        if (!info) return;
        if (info.value.trim() == '') {
          rej({ key, msg: info.emptyMsg || '信息不能为空' });
        }
        if (info.validRule && !info.validRule(info.value)) {
          rej({ key, msg: info.errorMsg || '信息格式错误' });
        }
      });
      res(true);
    });
  };
  const renderValidateBox = () => {
    return (
      <>
        <div className={TencentValidateStyles['recharge_validate_box']}>
          <div className={TencentValidateStyles['validate_form']}>
            <form>
              <div
                className={classNames(
                  TencentValidateStyles['validate_item'],
                  TencentValidateStyles[errorItem == 'username' ? 'error_item' : ''],
                )}
              >
                <div className={TencentValidateStyles['input_wrap']}>
                  <input
                    type="text"
                    placeholder="输入真实姓名"
                    value={username}
                    onInput={(e) => {
                      setValidateMsg('')
                      setUserName((e.target as HTMLInputElement).value);
                    }}
                  />
                </div>
              </div>
              <div
                className={classNames(
                  TencentValidateStyles['validate_item'],
                  TencentValidateStyles[errorItem == 'cardId' ? 'error_item' : ''],
                )}
              >
                <div className={TencentValidateStyles['input_wrap']}>
                  <input
                    type="text"
                    placeholder="输入身份证号码"
                    value={cardId}
                    onInput={(e) => {
                      setValidateMsg('')
                      setCardId((e.target as HTMLInputElement).value);
                    }}
                  />
                </div>
              </div>
              {/* 4要素验证需要银行卡号 */}
              {validateElementNums && validateElementNums >= 4 && (
                <div
                  className={classNames(
                    TencentValidateStyles['validate_item'],
                    TencentValidateStyles[errorItem == 'bankCard' ? 'error_item' : ''],
                  )}
                >
                  <div className={TencentValidateStyles['input_wrap']}>
                    <input
                      type="text"
                      placeholder="输入当前手机号绑定的银行卡号"
                      value={bankCard}
                      onInput={(e) => {
                        setValidateMsg('')
                        setBankCard((e.target as HTMLInputElement).value);
                      }}
                    />
                  </div>
                  {/* <div className={TencentValidateStyles['error_msg']}>{validateMsg}</div> */}
                </div>
              )}
              <div className={TencentValidateStyles['error_msg']}>{validateMsg}</div>

            </form>
          </div>

        </div>
        <div className={TencentValidateStyles['submit_box']}>
          {phoneNum && <p className={TencentValidateStyles['bind']}>已绑定手机：<span>{phoneNum}</span></p> || null}
          <div className={classNames(TencentValidateStyles['submit_btn'],
            validateMsg !== '' ? TencentValidateStyles['disabled'] : ''
          )} onClick={submitClick}>
            <span className={TencentValidateStyles['icon']}></span>提交安全验证
          </div>
          <p className={TencentValidateStyles['promise']}><span className={TencentValidateStyles['icon']}>{protectedIcon}</span>图怪兽承诺对您的所有信息加密保护</p>
        </div>
      </>
    );
  };
  const renderValidateSuccess = () => {
    return (
      <>
        <div
          className={classNames(
            TencentValidateStyles['recharge_validate_box'],
            TencentValidateStyles['recharge_validate_success'],
          )}
        >
          <div className={TencentValidateStyles['validate_info']}>
            <div>信息验证成功</div>
          </div>
        </div>
        <div className={TencentValidateStyles['submit_box']} style={{ paddingBottom: 110 }}>
          <div className={TencentValidateStyles['submit_btn']} onClick={submitClick}>
            继续充值
          </div>
        </div>
      </>
    );
  };

  const renderValidateFailByMsg = () => {
    return (
      <>
        <div
          className={classNames(
            TencentValidateStyles['recharge_validate_box'],
            TencentValidateStyles['recharge_validate_fail'],
          )}
        >
          <div className={TencentValidateStyles['tip_title']}>信息验证失败</div>
          <div className={TencentValidateStyles['validate_tip']}>
            <span className={TencentValidateStyles['tip']}>失败原因：</span>
            <span className={TencentValidateStyles['tip']}>{errMsg}</span>
          </div>

        </div>
        <div className={TencentValidateStyles['submit_box']} style={{ paddingBottom: 110 }}>
          <div className={TencentValidateStyles['submit_btn']} onClick={() => setValidateStatus(-1)}>
            重新验证
          </div>
          <a href="https://818ps.com/kf-service" target='_blank' className={TencentValidateStyles.contactKeFu}>
            联系客服
          </a>
        </div>
      </>
    );
  };

  const renderValidate = () => {
    // validateStatus = 1
    switch (validateStatus) {
      case -10:
      case -9:
      case -8:
      case -7:
      case -6:
      case -3:
      case -2:
      case 0:
        return renderValidateFailByMsg();
      case -1:
        return renderValidateBox();
      case 1:
        return renderValidateSuccess();
      default:
        return renderValidateBox();
    }
  };
  return <TgsLeftModal 
    className={TencentValidateStyles.tecent} 
    subTitle={![-10, -9, -8, -7, -6, -3, -2, 0, 1].includes(validateStatus) ? '为保证您的账号安全，请填写验证信息' : ''}
    closeable={closeable}
    close={onClose}
  >
    {renderValidate()}
  </TgsLeftModal>
};
