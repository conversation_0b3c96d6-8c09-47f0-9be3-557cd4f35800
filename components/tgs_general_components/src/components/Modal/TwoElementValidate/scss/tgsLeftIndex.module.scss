.tecent {
    p {
        padding: 0;
        margin: 0;
    }

    input {
        outline: none;
        background: none;
    }

    input::placeholder {
        color: #BABCBB;
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
    }

    .recharge_validate_box {
        width: 500px;
        display: flex;
        flex-direction: column;
        align-items: center;
        flex-grow: 1;
        padding: 30px 0 0;
    }

    .validate_item {
        margin-top: 18px;
    }

    .error_msg {
        color: #EF2833;

        /* 12/12-CN-Regular */
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        /* 166.667% */
        padding: 10px 22px 0;
    }

    .input_wrap {
        width: 344px;
        height: 50px;
        flex-shrink: 0;
        border-radius: 26px;
        border: 1px solid var(---, #CFF0D5);
        background: var(---, #FFF);
        display: flex;
        align-items: center;
        padding: 0 22px;
        color: #4C4849;
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        display: flex;
        flex-direction: column;

        input {
            width: 100%;
            height: 100%;
        }


    }

    .submit_box {
        display: flex;
        flex-direction: column;
        align-items: center;

        .submit_btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 344px;
            height: 50px;
            flex-shrink: 0;
            cursor: pointer;
            border-radius: 109px;
            background: var(---, #EF3964);
            color: var(---, #FFF);
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
        }

        .submit_btn.disabled {
            color: #A3A5A4;
            background: #E8E9E8;
            cursor: none;
        }

        .bind {
            color: var(---, #797676);
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;

            span {
                display: inline-block;
                margin: 0 0 10px;
            }
        }

        .promise {
            margin: 10px 0 30px;
            color: var(---, #A5A3A4);
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            display: flex;
            align-items: center;
            .icon {
                display: block;
                width: 14px;
                height: 14px;
                margin-right: 6px;
                svg {
                    width: 100%;
                    height: 100%;
                }
            }
        }


    }

    .recharge_validate_success,
    .recharge_validate_fail {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        flex-grow: 1;

        &>div{
            width: 344px;
            text-align: center;
        }

        .tip_title {
            color: #000;
            font-family: "Source Han Sans CN";
            font-size: 16px;
            font-style: normal;
            font-weight: 700;
            line-height: normal;
            margin-bottom: 10px;
        }

        .tip {
            color: var(---, #4C4849);
            font-family: "Source Han Sans CN";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            margin-bottom: 14px;
        }
    }

    .contactKeFu {
        margin-top: 10px;
        color: var(---, #797676);
        text-align: center;
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        cursor: pointer;

        &:hover{
            color: #EF2833;
        }
    }


    :global {}
}