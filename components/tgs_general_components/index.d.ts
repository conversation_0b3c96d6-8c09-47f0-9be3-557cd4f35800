/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
namespace TgsModal {
    interface IValidateProps {
        phoneNum: string;
        submitValidate?: (data: {
            name: string;
            mobile: string;
            card_id: string;
            unique_id: string;
            bank_card?: string;
        }) => Promise<any>;
        onSubmit?: (validateStatus: number) => void;
        validateElementNums?: number; // 验证要素个数
        getUniqueId: (data: { mobile: string; bank_card?: string }) => Promise<any>;
        closeable?: boolean;
        onClose?: () => void;
    }
    interface IBindPhoneProps {
        submitBindPh: (param: { num: string; code: string }) => Promise<any>;
        getCode: (num: string) => Promise<any>;
        onSubmit?: (validateStatus: number) => void;
        title?: string;
        tip?: string;
        style?: React.CSSProperties;
    }
    interface IContactServiceAgentProps {
        onContact?: () => void;
        onClose?: () => void;
    }
    interface IPhoneCodeValidateProps {
        mobile: string;
        submitValidate: (data: { mobile: string; code: string }) => Promise<any>;
        sendPhoneCode: (data: { mobile: string; code: string }) => Promise<any>;
        onCommitSuccess: () => void;
    }
    interface IBindPhoneCommonProps {
        onClose?: () => void;
        templateNum?: string;
        getPhoneCode: (mobile: string) => Promise<any>;
        submitBindPh: (param: { num: string; code: string }) => Promise<any>;
        onBindSuccess?: () => void;
    }
    interface IOutLineProps {
        loginTime: string;
        ipLocation: string;
        onClose?: () => void;
        onReLogin?: () => void;
    }
    interface IDownloadLimitProps {
        isCompany: boolean; // 企业or个人
        origin: string;
        templateNum: number;
        newVipUrlSuffix?: string;
        onJump?: (isCompany: boolean) => void;
    }

    interface ITgsLeftProps {
        className?: string;
        title?: string;
        subTitle?: string;
        children: ReactElement;
        closeable?: boolean;
        close?: () => void;
    }
    interface ISatisfactionSurveyProps {
        visible: boolean;
        onClose: () => void;
        onSubmit: (data: {
            impression_score: number;
            satisfaction_score: number;
            phone: string;
        }) => void;
    }
}
namespace TgsTextArea {
    interface AiTextprops {
        value: string;
        setText?: (text: string) => void;
        customStyle?: React.CSSProperties;
        tipStyle?: React.CSSProperties;
        placeholder?: string;
        maxLength?: number;
        onFocus?: (e: React.FocusEvent<HTMLTextAreaElement>) => void;
        onPaste?: (e: React.ClipboardEvent<HTMLTextAreaElement>) => void;
        onKeyDown?: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
        onBlur?: (e: React.FocusEvent<HTMLTextAreaElement>) => void;
        onMeetLimit?: (meetLimit: boolean) => void;
        className?: string;
        showCounter?: boolean;
        ref?: React.Ref<{
            replaceAiText: (text: string) => string;
            manualSetText: (text: string) => void;
            textareaRef: React.RefObject<HTMLTextAreaElement>;
        }>;
    }
}
